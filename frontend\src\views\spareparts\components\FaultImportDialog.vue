<template>
  <el-dialog :model-value="props.visible" @update:model-value="emit('update:visible', $event)" title="从故障清单导入" width="900px" @close="handleClose">
    <!-- 搜索筛选 -->
    <div class="filter-section">
      <el-form :inline="true" :model="queryParams" class="filter-form">
        <el-form-item label="故障标题">
          <el-input v-model="queryParams.title" placeholder="故障标题" clearable @keyup.enter="loadFaults" />
        </el-form-item>
        <el-form-item label="故障状态">
          <el-select v-model="queryParams.status" placeholder="故障状态" clearable>
            <el-option label="待处理" value="OPEN" />
            <el-option label="处理中" value="IN_PROGRESS" />
            <el-option label="待验证" value="PENDING_VERIFICATION" />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级">
          <el-select v-model="queryParams.priority" placeholder="优先级" clearable>
            <el-option label="紧急" :value="1" />
            <el-option label="高" :value="2" />
            <el-option label="中" :value="3" />
            <el-option label="低" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadFaults">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 故障列表 -->
    <el-table
      :data="faults"
      border
      style="width: 100%"
      @row-click="handleRowClick"
      highlight-current-row
    >
      <el-table-column type="index" label="序号" width="60" />
      <el-table-column prop="title" label="故障标题" min-width="200" />
      <el-table-column prop="assetName" label="关联资产" width="150" />
      <el-table-column prop="priority" label="优先级" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="getPriorityTagType(row.priority)" size="small">
            {{ getPriorityText(row.priority) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusTagType(row.status)" size="small">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="reporterName" label="报告人" width="100" />
      <el-table-column prop="createdAt" label="创建时间" width="150">
        <template #default="{ row }">
          {{ formatDateTime(row.createdAt) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleImport(row)">导入</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="queryParams.pageIndex"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="loadFaults"
        @current-change="loadFaults"
      />
    </div>

    <!-- 故障详情预览 -->
    <div v-if="selectedFault" class="fault-preview">
      <el-divider content-position="left">故障详情预览</el-divider>
      <el-card class="preview-card" shadow="never">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="故障标题">
            <strong>{{ selectedFault.title }}</strong>
          </el-descriptions-item>
          <el-descriptions-item label="关联资产">
            {{ selectedFault.assetName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="故障描述" :span="2">
            {{ selectedFault.description || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :type="getPriorityTagType(selectedFault.priority)" size="small">
              {{ getPriorityText(selectedFault.priority) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(selectedFault.status)" size="small">
              {{ getStatusText(selectedFault.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="报告人">
            {{ selectedFault.reporterName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(selectedFault.createdAt) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 关联备件信息 -->
        <div v-if="selectedFault.relatedSpareParts && selectedFault.relatedSpareParts.length > 0" class="related-parts">
          <el-divider content-position="left">关联备件</el-divider>
          <div class="parts-list">
            <el-tag
              v-for="part in selectedFault.relatedSpareParts"
              :key="part.id"
              style="margin-right: 8px; margin-bottom: 8px;"
            >
              {{ part.name }} ({{ part.code }})
            </el-tag>
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleImportSelected" :disabled="!selectedFault">
          导入选中故障
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getFaults } from '@/api/faults'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'import'])

// 响应式数据
const faults = ref([])
const total = ref(0)
const selectedFault = ref(null)

const queryParams = reactive({
  pageIndex: 1,
  pageSize: 10,
  title: '',
  status: '',
  priority: null
})

// 计算属性

// 方法
const loadFaults = async () => {
  try {
    // 构建查询参数，过滤空值
    const params = {
      page: queryParams.pageIndex,
      pageSize: queryParams.pageSize
    }

    // 只添加非空参数
    if (queryParams.title) params.assetKeyword = queryParams.title
    if (queryParams.status !== '') params.status = queryParams.status
    if (queryParams.priority !== null) params.faultType = queryParams.priority

    const response = await getFaults(params)
    if (response.success) {
      faults.value = response.data.items || []
      total.value = response.data.total || 0
    }
  } catch (error) {
    console.error('加载故障清单失败:', error)
    ElMessage.error('加载故障清单失败')
  }
}

const handleRowClick = (row) => {
  selectedFault.value = row
}

const handleImport = (fault) => {
  selectedFault.value = fault
  handleImportSelected()
}

const handleImportSelected = () => {
  if (!selectedFault.value) {
    ElMessage.warning('请先选择要导入的故障')
    return
  }

  emit('import', selectedFault.value)
  handleClose()
}

const resetQuery = () => {
  Object.assign(queryParams, {
    pageIndex: 1,
    pageSize: 10,
    title: '',
    status: '',
    priority: null
  })
  loadFaults()
}

const handleClose = () => {
  emit('update:visible', false)
  selectedFault.value = null
}

const getPriorityTagType = (priority) => {
  const priorityMap = {
    1: 'danger',
    2: 'warning',
    3: '',
    4: 'info'
  }
  return priorityMap[priority] || ''
}

const getPriorityText = (priority) => {
  const priorityMap = {
    1: '紧急',
    2: '高',
    3: '中',
    4: '低'
  }
  return priorityMap[priority] || priority
}

const getStatusTagType = (status) => {
  const statusMap = {
    'OPEN': 'danger',
    'IN_PROGRESS': 'warning',
    'PENDING_VERIFICATION': 'primary',
    'RESOLVED': 'success',
    'CLOSED': 'info'
  }
  return statusMap[status] || ''
}

const getStatusText = (status) => {
  const statusMap = {
    'OPEN': '待处理',
    'IN_PROGRESS': '处理中',
    'PENDING_VERIFICATION': '待验证',
    'RESOLVED': '已解决',
    'CLOSED': '已关闭'
  }
  return statusMap[status] || status
}

const formatDateTime = (dateTime) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  if (props.visible) {
    loadFaults()
  }
})

// 监听visible变化
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    loadFaults()
  }
})
</script>

<style scoped>
.filter-section {
  margin-bottom: 16px;
}

.filter-form {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 16px;
  text-align: right;
}

.fault-preview {
  margin-top: 16px;
}

.preview-card {
  border: 1px solid #e4e7ed;
}

.related-parts {
  margin-top: 16px;
}

.parts-list {
  margin-top: 8px;
}

.dialog-footer {
  text-align: right;
}

/* 表格行点击样式 */
:deep(.el-table__row) {
  cursor: pointer;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}
</style>
