import{_ as e,aN as a,ab as l,aF as s,am as t,a as n,b as r,o as i,Y as u,a9 as d,af as o,w as c,e as p,A as m,t as v,i as g,aR as f,d as k,as as h,a7 as y,F as b,h as w,r as _,j as I,a5 as C,m as U,aP as D,a8 as V,c as S,aS as z,ag as x,f as T,aT as F,v as q,R as E,B as P,an as M,ao as L,aA as N,aQ as A,aU as j,a1 as Q}from"./index-C7OOw0MO.js";import{t as R}from"./task-BTGSy_AK.js";import{u as H,T as B,B as K,b as $,a as O,E as Y}from"./BatchStatusDialog-DWf3betI.js";import{U as G}from"./UserAvatarStack-CUKN1R7u.js";import"./format-DfhXadVZ.js";import"./UserSelect-C-1BVWPu.js";const J={class:"quick-task-creator"},W={class:"quick-form-container floating"},X={class:"form-header"},Z={class:"quick-settings"},ee={class:"quick-actions"},ae={class:"right-actions"};const le=e({name:"QuickTaskCreatorSimple",components:{Plus:t,Close:s,Check:l,More:a},props:{triggerText:{type:String,default:"快速创建任务"},triggerClass:{type:String,default:""}},emits:["created","expand","collapse","expandToFullForm"],setup(e,{emit:a}){const l=_(null),s=_(!1),t=_(!1),n=I(),r=_([]),i=_(!1),u=C({title:"",description:"",assigneeUserIds:[],priority:"Medium",dueDate:null}),d=()=>{s.value=!1,o(),a("collapse")},o=()=>{u.title="",u.description="",u.assigneeUserIds=[],u.priority="Medium",u.dueDate=null,l.value&&l.value.clearValidate()},c=e=>e?e.toISOString().split("T")[0]:null;return U((()=>{(async()=>{i.value=!0;try{const e=await D.getUserList();e&&e.data&&(r.value=e.data.map((e=>({id:e.id,name:e.name||e.userName,department:e.department||"未知部门"}))),n.userInfo&&n.userInfo.id&&(u.assigneeUserIds=[n.userInfo.id]))}catch(e){V.error("获取用户列表失败，使用测试数据"),r.value=[{id:1,name:"张三",department:"技术部"},{id:2,name:"李四",department:"运维部"},{id:3,name:"王五",department:"产品部"},{id:4,name:"赵六",department:"设计部"}],r.value.length>0&&(u.assigneeUserIds=[r.value[0].id])}finally{i.value=!1}})(),(()=>{const e=new Date,a=new Date;a.setMonth(e.getMonth()+1),u.dueDate=a})()})),{quickFormRef:l,isExpanded:s,submitting:t,users:r,quickForm:u,quickRules:{title:[{required:!0,message:"请输入任务标题",trigger:"blur"},{min:2,max:100,message:"长度在 2 到 100 个字符",trigger:"blur"}]},expandCreator:()=>{s.value=!0,a("expand")},collapseCreator:d,resetForm:o,handleQuickSubmit:async()=>{if(!l.value)return;if(await l.value.validate().catch((()=>!1))){t.value=!0;try{const e=u.assigneeUserIds.length>0?u.assigneeUserIds[0]:null,l=u.assigneeUserIds.slice(1),s=new Date,t={name:u.title,description:u.description||"",assigneeUserId:e,collaboratorUserIds:l,priority:u.priority,planStartDate:c(s),planEndDate:u.dueDate?c(u.dueDate):null,status:"Todo",taskType:"Normal"},n=await R.createTask(t);if(!n.success)throw new Error(n.message||"创建失败");V.success("🎉 任务创建成功！"),a("created",n.data),o(),d()}catch(e){V.error("创建任务失败: "+(e.message||"未知错误"))}finally{t.value=!1}}},expandToFullForm:()=>{a("expandToFullForm",{title:u.title,description:u.description,assigneeUserIds:u.assigneeUserIds,participantUserIds:u.participantUserIds,priority:u.priority,dueDate:u.dueDate}),d()},loading:i}}},[["render",function(e,a,l,s,t,_){const I=n("Plus"),C=n("el-icon"),U=n("el-button"),D=n("Close"),V=n("el-input"),S=n("el-form-item"),z=n("el-option"),x=n("el-select"),T=n("el-tag"),F=n("el-date-picker"),q=n("More"),E=n("Check"),P=n("el-form"),M=n("el-card");return i(),r("div",J,[s.isExpanded?d("",!0):(i(),u(U,{key:0,type:"primary",onClick:s.expandCreator,class:g(l.triggerClass)},{default:c((()=>[p(C,null,{default:c((()=>[p(I)])),_:1}),m(" "+v(l.triggerText),1)])),_:1},8,["onClick","class"])),o(k("div",W,[p(M,{shadow:"always",class:"quick-form-card"},{header:c((()=>[k("div",X,[a[5]||(a[5]=k("span",null,"⚡ 快速创建任务",-1)),p(U,{type:"text",onClick:s.collapseCreator,class:"close-btn"},{default:c((()=>[p(C,null,{default:c((()=>[p(D)])),_:1})])),_:1},8,["onClick"])])])),default:c((()=>[p(P,{ref:"quickFormRef",model:s.quickForm,rules:s.quickRules,size:"small",onSubmit:h(s.handleQuickSubmit,["prevent"])},{default:c((()=>[p(S,{prop:"title"},{default:c((()=>[p(V,{modelValue:s.quickForm.title,"onUpdate:modelValue":a[0]||(a[0]=e=>s.quickForm.title=e),placeholder:"任务标题（必填）",maxlength:"100",clearable:"",onKeyup:y(s.handleQuickSubmit,["enter"]),autofocus:""},null,8,["modelValue","onKeyup"])])),_:1}),p(S,{prop:"description"},{default:c((()=>[p(V,{type:"textarea",modelValue:s.quickForm.description,"onUpdate:modelValue":a[1]||(a[1]=e=>s.quickForm.description=e),placeholder:"任务内容/描述",rows:3,maxlength:"500","show-word-limit":""},null,8,["modelValue"])])),_:1}),k("div",Z,[p(x,{modelValue:s.quickForm.assigneeUserIds,"onUpdate:modelValue":a[2]||(a[2]=e=>s.quickForm.assigneeUserIds=e),multiple:"","collapse-tags":"",placeholder:"负责人",filterable:"",clearable:"",class:"quick-assignee"},{default:c((()=>[(i(!0),r(b,null,w(s.users,(e=>(i(),u(z,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),p(x,{modelValue:s.quickForm.priority,"onUpdate:modelValue":a[3]||(a[3]=e=>s.quickForm.priority=e),placeholder:"优先级",class:"quick-priority"},{default:c((()=>[p(z,{label:"低",value:"Low"},{default:c((()=>[p(T,{type:"info",size:"small"},{default:c((()=>a[6]||(a[6]=[m("低")]))),_:1})])),_:1}),p(z,{label:"中",value:"Medium"},{default:c((()=>[p(T,{type:"warning",size:"small"},{default:c((()=>a[7]||(a[7]=[m("中")]))),_:1})])),_:1}),p(z,{label:"高",value:"High"},{default:c((()=>[p(T,{type:"danger",size:"small"},{default:c((()=>a[8]||(a[8]=[m("高")]))),_:1})])),_:1})])),_:1},8,["modelValue"]),p(F,{modelValue:s.quickForm.dueDate,"onUpdate:modelValue":a[4]||(a[4]=e=>s.quickForm.dueDate=e),type:"date",placeholder:"截止日期",class:"quick-date",size:"small"},null,8,["modelValue"])]),k("div",ee,[a[11]||(a[11]=k("div",{class:"left-actions"},[k("span",{class:"tip-text"},"💡 填写标题即可快速创建")],-1)),k("div",ae,[p(U,{type:"text",onClick:s.expandToFullForm,class:"expand-btn"},{default:c((()=>[p(C,null,{default:c((()=>[p(q)])),_:1}),a[9]||(a[9]=m(" 详细设置 "))])),_:1},8,["onClick"]),p(U,{type:"primary",onClick:s.handleQuickSubmit,loading:s.submitting,size:"small"},{default:c((()=>[p(C,null,{default:c((()=>[p(E)])),_:1}),a[10]||(a[10]=m(" 创建 "))])),_:1},8,["onClick","loading"])])])])),_:1},8,["model","rules","onSubmit"])])),_:1})],512),[[f,s.isExpanded]])])}],["__scopeId","data-v-7ea0e0c4"]]),se={class:"enhanced-task-list"},te={class:"page-header"},ne={class:"header-right"},re={class:"card-header"},ie={class:"filter-grid"},ue={class:"user-dept"},de={class:"stats-grid"},oe={class:"stat-content"},ce={class:"stat-info"},pe={class:"stat-number"},me={class:"stat-content"},ve={class:"stat-info"},ge={class:"stat-number"},fe={class:"stat-content"},ke={class:"stat-info"},he={class:"stat-number"},ye={class:"stat-content"},be={class:"stat-info"},we={class:"stat-number"},_e={key:0,class:"batch-actions-bar"},Ie={class:"batch-info"},Ce={class:"batch-actions"},Ue={class:"table-header"},De={class:"table-actions"},Ve={key:0},Se=["onClick"],ze={class:"task-title"},xe={key:0,class:"task-desc"},Te={class:"assignee-cell"},Fe={key:0,class:"no-assignee"},qe={class:"date-cell"},Ee={key:0,class:"start-date"},Pe={key:1,class:"no-date"},Me={key:0,class:"date-cell"},Le={class:"due-date-container"},Ne={key:0,class:"overdue-warning"},Ae={key:1,class:"no-date"},je={class:"action-buttons"},Qe={key:1,class:"card-view"},Re={class:"task-cards-grid"},He={class:"pagination-wrapper"},Be=e({__name:"EnhancedTaskListView",setup(e){const l=H(),s=_(!1),t=_("table"),f=_([]),h=_(""),I=_([]),J=_([]),W=_(null),X=_(!1),Z=_(0),ee=C({status:"",priority:"",assigneeIds:[]}),ae=C({currentPage:1,pageSize:20}),Be=_(!1),Ke=_(!1),$e=_(!1),Oe=_(!1),Ye=_(null),Ge=S((()=>{let e=l.tasks||[];if(ee.status&&(e=e.filter((e=>e.status===ee.status))),ee.priority&&(e=e.filter((e=>e.priority===ee.priority))),ee.assigneeIds&&ee.assigneeIds.length>0&&(e=e.filter((e=>ee.assigneeIds.includes(e.assigneeUserId)))),h.value){const a=h.value.toLowerCase();e=e.filter((e=>{var l,s;return(null==(l=e.name)?void 0:l.toLowerCase().includes(a))||(null==(s=e.description)?void 0:s.toLowerCase().includes(a))}))}if(I.value&&2===I.value.length){const[a,l]=I.value;e=e.filter((e=>{if(!e.planEndDate)return!1;const s=new Date(e.planEndDate);return s>=a&&s<=l}))}return e})),Je=S((()=>{const e=(ae.currentPage-1)*ae.pageSize,a=e+ae.pageSize;return Ge.value.slice(e,a)})),We=S((()=>{const e=Ge.value;return{total:e.length,inProgress:e.filter((e=>"InProgress"===e.status)).length,completed:e.filter((e=>"Done"===e.status)).length,overdue:e.filter((e=>Da(e))).length}})),Xe=S((()=>J.value.map((e=>e.taskId)))),Ze=S((()=>Je.value.map((e=>{if(!e)return e;const a=f.value.find((a=>a&&a.id===e.assigneeUserId));return{...e,assigneeUserName:(null==a?void 0:a.name)||e.assigneeUserName||"未知用户",assigneeAvatarUrl:z(e.assigneeAvatarUrl||(null==a?void 0:a.avatarUrl)||(null==a?void 0:a.avatar)||"")}})).filter(Boolean))),ea=async()=>{s.value=!0;try{await l.fetchTasks({status:ee.status,priority:ee.priority,assigneeIds:ee.assigneeIds,search:h.value,pageNumber:1,pageSize:1e3})}catch(e){V.error("加载任务列表失败: "+e.message)}finally{s.value=!1}},aa=()=>{ae.currentPage=1,ea()},la=()=>{Object.assign(ee,{status:"",priority:"",assigneeIds:[]}),h.value="",I.value=[],ae.currentPage=1,ea()},sa=()=>{aa()},ta=e=>{J.value=e},na=()=>{J.value=[]},ra=(e,a)=>{if(a){const a=Ge.value.find((a=>a.taskId===e));a&&!J.value.find((a=>a.taskId===e))&&J.value.push(a)}else J.value=J.value.filter((a=>a.taskId!==e))},ia=e=>{ae.pageSize=e,ae.currentPage=1},ua=e=>{ae.currentPage=e},da=e=>{Ye.value=e,Be.value=!0,async function(e){await R.recordTaskView(e),await Ta()}(e.taskId)},oa=()=>{W.value=null,X.value=!1,Ke.value=!0},ca=()=>{Ke.value=!1,W.value=null,X.value=!1},pa=e=>{V.success("任务创建成功!"),ea()},ma=()=>{ea()},va=e=>{W.value=e,X.value=!1,Ke.value=!0},ga=async(e,a)=>{switch(e){case"clone":await ha(a);break;case"assign":J.value=[a],$e.value=!0;break;case"complete":await ya(a);break;case"delete":await ba(a)}},fa=e=>{ga(e.action,e.task)},ka=async e=>{try{await l.updateTaskStatus(e.taskId,e.newStatus),V.success("任务状态更新成功"),ea()}catch(a){V.error("状态更新失败: "+a.message)}},ha=async e=>{try{const a={...e,name:`${e.name} (副本)`,status:"Todo",progress:0};delete a.taskId,await l.createTask(a),V.success("任务克隆成功"),ea()}catch(a){V.error("任务克隆失败: "+a.message)}},ya=async e=>{try{await l.updateTaskStatus(e.taskId,"Done"),V.success("任务已标记为完成"),ea()}catch(a){V.error("操作失败: "+a.message)}},ba=async e=>{var a;try{await Q.confirm(`确定要删除任务"${e.name}"吗？`,"确认删除",{type:"warning"}),await l.deleteTask(e.taskId),V.success("任务删除成功"),ea()}catch(s){404===(null==(a=null==s?void 0:s.response)?void 0:a.status)?(V.error("任务已被删除或不存在"),ea()):"cancel"!==s&&V.error("删除失败: "+s.message)}},wa=async()=>{try{await Q.confirm(`确定要删除选中的 ${J.value.length} 个任务吗？`,"确认批量删除",{type:"warning"}),await l.batchDeleteTasks(Xe.value),V.success("批量删除成功"),na(),ea()}catch(e){"cancel"!==e&&V.error("批量删除失败: "+e.message)}},_a=()=>{V.success("批量分配成功"),na(),ea()},Ia=()=>{V.success("批量状态修改成功"),na(),ea()},Ca=e=>({High:"🔴",Medium:"🟡",Low:"🟢"}[e]||"⚪"),Ua=e=>({High:"高",Medium:"中",Low:"低"}[e]||e),Da=e=>!(!e.planEndDate||"Done"===e.status)&&new Date(e.planEndDate)<new Date,Va=e=>{if(!e.planEndDate||"Done"===e.status)return!1;const a=new Date(e.planEndDate),l=new Date,s=Math.ceil((a-l)/864e5);return s<=2&&s>=0},Sa=e=>e?new Date(e).toLocaleDateString("zh-CN"):"",za=e=>{if(!e.planEndDate)return 0;const a=new Date(e.planEndDate),l=new Date;return Math.floor((l-a)/864e5)};U((()=>{ea(),(async()=>{try{const e=await D.getUserList();e&&e.data?f.value=e.data.map((e=>e?{id:e.id||e.userId||e.ID||0,name:e.name||e.userName||e.username||e.displayName||"未知用户",department:e.department||e.departmentName||"",avatar:e.avatar||e.avatarUrl||""}:null)).filter((e=>e&&e.id>0)):f.value=[]}catch(e){f.value=[]}})(),Ta()}));const xa=async e=>{try{if(X.value){const a={...e,taskId:W.value.taskId};await l.updateTask(a),V.success("任务更新成功!")}else await l.createTask(e),V.success("任务创建成功!");ca(),await ea()}catch(a){V.error("操作失败: "+(a.message||"未知错误"))}};async function Ta(){var e;const a=await R.getTodayViewedCount();Z.value=(null==(e=a.data)?void 0:e.count)||0}const Fa=e=>{if(!e)return[];const a=[];return e.assigneeUserId&&a.push({id:e.assigneeUserId,name:e.assigneeUserName||"未知用户",avatarUrl:z(e.assigneeAvatarUrl||""),role:"Primary",isPrimary:!0}),e.assignees&&Array.isArray(e.assignees)&&e.assignees.length>0&&e.assignees.forEach((l=>{l.userId!==e.assigneeUserId&&a.push({id:l.userId,name:l.userName||"未知用户",avatarUrl:z(l.avatarUrl||""),role:"Collaborator",isPrimary:!1})})),a};return(e,_)=>{const C=n("el-icon"),U=n("el-button"),D=n("el-option"),S=n("el-select"),z=n("el-avatar"),Q=n("el-input"),H=n("el-date-picker"),Z=n("el-card"),ea=n("el-button-group"),ha=n("el-table-column"),ya=n("el-tag"),ba=n("el-text"),Ta=n("el-dropdown-item"),qa=n("el-dropdown-menu"),Ea=n("el-dropdown"),Pa=n("el-table"),Ma=n("el-pagination"),La=x("loading");return i(),r("div",se,[k("div",te,[_[19]||(_[19]=k("div",{class:"header-left"},[k("h1",null,"📋 任务管理中心"),k("p",{class:"subtitle"},"高效管理，轻松协作")],-1)),k("div",ne,[p(le,{"trigger-text":"⚡ 快速创建","trigger-class":"quick-create-btn",onCreated:pa,onExpandToFullForm:va}),p(U,{type:"primary",onClick:oa},{default:c((()=>[p(C,null,{default:c((()=>[p(T(F))])),_:1}),_[18]||(_[18]=m(" 详细创建 "))])),_:1})])]),p(Z,{class:"filter-card",shadow:"hover"},{header:c((()=>[k("div",re,[_[21]||(_[21]=k("span",null,"🔍 智能筛选",-1)),p(U,{link:"",onClick:la,size:"small"},{default:c((()=>[p(C,null,{default:c((()=>[p(T(E))])),_:1}),_[20]||(_[20]=m(" 重置 "))])),_:1})])])),default:c((()=>[k("div",ie,[p(S,{modelValue:ee.status,"onUpdate:modelValue":_[0]||(_[0]=e=>ee.status=e),placeholder:"任务状态",clearable:"",class:"filter-item"},{default:c((()=>[p(D,{label:"全部状态",value:""}),p(D,{label:"📋 待处理",value:"Todo"}),p(D,{label:"🔄 进行中",value:"InProgress"}),p(D,{label:"✅ 已完成",value:"Done"}),p(D,{label:"❌ 已取消",value:"Cancelled"})])),_:1},8,["modelValue"]),p(S,{modelValue:ee.priority,"onUpdate:modelValue":_[1]||(_[1]=e=>ee.priority=e),placeholder:"优先级",clearable:"",class:"filter-item"},{default:c((()=>[p(D,{label:"全部优先级",value:""}),p(D,{label:"🔴 高优先级",value:"High"}),p(D,{label:"🟡 中优先级",value:"Medium"}),p(D,{label:"🟢 低优先级",value:"Low"})])),_:1},8,["modelValue"]),p(S,{modelValue:ee.assigneeIds,"onUpdate:modelValue":_[2]||(_[2]=e=>ee.assigneeIds=e),multiple:"",placeholder:"负责人",clearable:"",filterable:"",class:"filter-item","collapse-tags":"","collapse-tags-tooltip":""},{default:c((()=>[(i(!0),r(b,null,w(f.value,(e=>(i(),u(D,{key:e.id,label:e.name,value:e.id},{default:c((()=>[p(z,{src:e.avatar,size:18,style:{"margin-right":"6px"}},null,8,["src"]),k("span",null,v(e.name),1),k("span",ue,v(e.department),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"]),p(Q,{modelValue:h.value,"onUpdate:modelValue":_[3]||(_[3]=e=>h.value=e),placeholder:"🔍 搜索任务标题、描述...",clearable:"",class:"search-input",onKeyup:y(aa,["enter"])},{prefix:c((()=>[p(C,null,{default:c((()=>[p(T(q))])),_:1})])),_:1},8,["modelValue"]),p(H,{modelValue:I.value,"onUpdate:modelValue":_[4]||(_[4]=e=>I.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",class:"date-picker",onChange:sa},null,8,["modelValue"]),p(U,{type:"primary",onClick:aa,loading:s.value,class:"search-btn"},{default:c((()=>[p(C,null,{default:c((()=>[p(T(q))])),_:1}),_[22]||(_[22]=m(" 搜索 "))])),_:1},8,["loading"])])])),_:1}),k("div",de,[p(Z,{class:"stat-card total",shadow:"hover"},{default:c((()=>[k("div",oe,[_[24]||(_[24]=k("div",{class:"stat-icon"},"📊",-1)),k("div",ce,[k("div",pe,v(We.value.total),1),_[23]||(_[23]=k("div",{class:"stat-label"},"总任务",-1))])])])),_:1}),p(Z,{class:"stat-card progress",shadow:"hover"},{default:c((()=>[k("div",me,[_[26]||(_[26]=k("div",{class:"stat-icon"},"🔄",-1)),k("div",ve,[k("div",ge,v(We.value.inProgress),1),_[25]||(_[25]=k("div",{class:"stat-label"},"进行中",-1))])])])),_:1}),p(Z,{class:"stat-card completed",shadow:"hover"},{default:c((()=>[k("div",fe,[_[28]||(_[28]=k("div",{class:"stat-icon"},"✅",-1)),k("div",ke,[k("div",he,v(We.value.completed),1),_[27]||(_[27]=k("div",{class:"stat-label"},"已完成",-1))])])])),_:1}),p(Z,{class:"stat-card overdue",shadow:"hover"},{default:c((()=>[k("div",ye,[_[30]||(_[30]=k("div",{class:"stat-icon"},"⏰",-1)),k("div",be,[k("div",we,v(We.value.overdue),1),_[29]||(_[29]=k("div",{class:"stat-label"},"已逾期",-1))])])])),_:1})]),J.value.length>0?(i(),r("div",_e,[k("div",Ie,[k("span",null,"已选择 "+v(J.value.length)+" 个任务",1),p(U,{link:"",onClick:na},{default:c((()=>_[31]||(_[31]=[m("取消选择")]))),_:1})]),k("div",Ce,[p(U,{size:"small",onClick:_[5]||(_[5]=e=>$e.value=!0)},{default:c((()=>[p(C,null,{default:c((()=>[p(T(P))])),_:1}),_[32]||(_[32]=m(" 批量分配 "))])),_:1}),p(U,{size:"small",onClick:_[6]||(_[6]=e=>Oe.value=!0)},{default:c((()=>[p(C,null,{default:c((()=>[p(T(M))])),_:1}),_[33]||(_[33]=m(" 批量修改状态 "))])),_:1}),p(U,{size:"small",type:"danger",onClick:wa},{default:c((()=>[p(C,null,{default:c((()=>[p(T(L))])),_:1}),_[34]||(_[34]=m(" 批量删除 "))])),_:1})])])):d("",!0),p(Z,{class:"table-card",shadow:"hover"},{header:c((()=>[k("div",Ue,[k("span",null,"📋 任务列表 ("+v(Ge.value.length)+")",1),k("div",De,[p(ea,{size:"small"},{default:c((()=>[p(U,{type:"table"===t.value?"primary":"",onClick:_[7]||(_[7]=e=>t.value="table")},{default:c((()=>[p(C,null,{default:c((()=>[p(T(A))])),_:1}),_[35]||(_[35]=m(" 表格 "))])),_:1},8,["type"]),p(U,{type:"card"===t.value?"primary":"",onClick:_[8]||(_[8]=e=>t.value="card")},{default:c((()=>[p(C,null,{default:c((()=>[p(T(j))])),_:1}),_[36]||(_[36]=m(" 卡片 "))])),_:1},8,["type"])])),_:1})])])])),default:c((()=>["table"===t.value?(i(),r("div",Ve,[o((i(),u(Pa,{data:Ze.value,onSelectionChange:ta,"row-key":"taskId",class:"enhanced-table"},{default:c((()=>[p(ha,{type:"selection",width:"50"}),p(ha,{label:"优先级",width:"100",align:"center"},{default:c((({row:e})=>{return[p(ya,{type:(a=e.priority,{High:"danger",Medium:"warning",Low:"info"}[a]||"info"),size:"small"},{default:c((()=>[m(v(Ca(e.priority))+" "+v(Ua(e.priority)),1)])),_:2},1032,["type"])];var a})),_:1}),p(ha,{label:"任务名称","min-width":"200"},{default:c((({row:e})=>{return[k("div",{class:"task-name-cell",onClick:a=>da(e)},[k("span",ze,v(e.name),1),e.description?(i(),r("div",xe,v((a=e.description,l=50,!a||a.length<=l?a:a.substring(0,l)+"...")),1)):d("",!0)],8,Se)];var a,l})),_:1}),p(ha,{label:"负责人",width:"200"},{default:c((({row:e})=>[k("div",Te,[p(G,{users:Fa(e),"is-main-user-primary":!0,"max-users":4,"avatar-size":"6",overlap:-10,class:"small"},null,8,["users"]),e.assigneeUserId?d("",!0):(i(),r("span",Fe,"未分配"))])])),_:1}),p(ha,{label:"状态",width:"120",align:"center"},{default:c((({row:e})=>[p(S,{"model-value":e.status,onChange:a=>(async(e,a)=>{try{s.value=!0,await l.updateTask({taskId:e.taskId,status:a}),e.status=a,V.success("状态更新成功")}catch(t){V.error("状态更新失败: "+t.message)}finally{s.value=!1}})(e,a),size:"small",class:"status-select"},{default:c((()=>[p(D,{label:"待处理",value:"Todo"}),p(D,{label:"进行中",value:"InProgress"}),p(D,{label:"已完成",value:"Done"}),p(D,{label:"已取消",value:"Canceled"})])),_:2},1032,["model-value","onChange"])])),_:1}),p(ha,{label:"开始日期",width:"120"},{default:c((({row:e})=>[k("div",qe,[e.planStartDate?(i(),r("span",Ee,v(Sa(e.planStartDate)),1)):(i(),r("span",Pe,"未设置"))])])),_:1}),p(ha,{label:"截止日期",width:"150"},{default:c((({row:e})=>[e.planEndDate?(i(),r("div",Me,[k("div",Le,[k("span",{class:g({overdue:Da(e),"due-soon":Va(e)})},v(Sa(e.planEndDate)),3),Da(e)?(i(),r("div",Ne,[p(ba,{type:"danger",size:"small"},{default:c((()=>[m(" 已逾期 "+v(za(e))+" 天 ",1)])),_:2},1024)])):d("",!0)])])):(i(),r("span",Ae,"未设置"))])),_:1}),p(ha,{label:"操作",width:"180",align:"center"},{default:c((({row:e})=>[k("div",je,[p(U,{link:"",size:"small",onClick:a=>da(e)},{default:c((()=>[p(C,null,{default:c((()=>[p(T(N))])),_:1})])),_:2},1032,["onClick"]),p(U,{link:"",size:"small",onClick:a=>(async e=>{try{const a=await R.getTaskDetail(e.taskId);a.success&&a.data?(W.value=a.data,X.value=!0,Ke.value=!0):V.error("获取任务详情失败: "+(a.message||"未知错误"))}catch(a){V.error("获取任务详情时发生错误")}})(e)},{default:c((()=>[p(C,null,{default:c((()=>[p(T(M))])),_:1})])),_:2},1032,["onClick"]),p(Ea,{onCommand:a=>ga(a,e)},{dropdown:c((()=>[p(qa,null,{default:c((()=>[p(Ta,{command:"clone"},{default:c((()=>_[37]||(_[37]=[m("克隆任务")]))),_:1}),p(Ta,{command:"assign"},{default:c((()=>_[38]||(_[38]=[m("重新分配")]))),_:1}),"Done"!==e.status?(i(),u(Ta,{key:0,command:"complete"},{default:c((()=>_[39]||(_[39]=[m("标记完成")]))),_:1})):d("",!0),p(Ta,{command:"delete",divided:""},{default:c((()=>_[40]||(_[40]=[m("删除任务")]))),_:1})])),_:2},1024)])),default:c((()=>[p(U,{link:"",size:"small"},{default:c((()=>[p(C,null,{default:c((()=>[p(T(a))])),_:1})])),_:1})])),_:2},1032,["onCommand"])])])),_:1})])),_:1},8,["data"])),[[La,s.value]])])):(i(),r("div",Qe,[k("div",Re,[(i(!0),r(b,null,w(Je.value,(e=>(i(),u(Y,{key:e.taskId,task:e,selected:Xe.value.includes(e.taskId),onSelect:ra,onClick:da,onQuickAction:fa,onStatusChange:ka},null,8,["task","selected"])))),128))])])),k("div",He,[p(Ma,{"current-page":ae.currentPage,"onUpdate:currentPage":_[9]||(_[9]=e=>ae.currentPage=e),"page-size":ae.pageSize,"onUpdate:pageSize":_[10]||(_[10]=e=>ae.pageSize=e),"page-sizes":[10,20,50,100],small:!1,disabled:s.value,background:!0,layout:"total, sizes, prev, pager, next, jumper",total:Ge.value.length,onSizeChange:ia,onCurrentChange:ua},null,8,["current-page","page-size","disabled","total"])])])),_:1}),p(B,{modelValue:Be.value,"onUpdate:modelValue":_[11]||(_[11]=e=>Be.value=e),task:Ye.value,onClose:_[12]||(_[12]=e=>Be.value=!1),onUpdated:ma},null,8,["modelValue","task"]),p(K,{modelValue:$e.value,"onUpdate:modelValue":_[13]||(_[13]=e=>$e.value=e),"task-ids":Xe.value,onClose:_[14]||(_[14]=e=>$e.value=!1),onAssigned:_a},null,8,["modelValue","task-ids"]),p($,{modelValue:Oe.value,"onUpdate:modelValue":_[15]||(_[15]=e=>Oe.value=e),"task-ids":Xe.value,onClose:_[16]||(_[16]=e=>Oe.value=!1),onUpdated:Ia},null,8,["modelValue","task-ids"]),p(O,{visible:Ke.value,"onUpdate:visible":_[17]||(_[17]=e=>Ke.value=e),isEdit:X.value,formData:W.value,onClose:ca,onSubmit:xa},null,8,["visible","isEdit","formData"])])}}},[["__scopeId","data-v-20f199e9"]]);export{Be as default};
