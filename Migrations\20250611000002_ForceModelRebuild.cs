using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ItAssetsSystem.Migrations
{
    /// <summary>
    /// 强制重建EF Core模型，解决PeriodicTaskSchedule映射问题
    /// </summary>
    public partial class ForceModelRebuild : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // 强制EF Core重新读取模型配置
            // 确保PeriodicTaskSchedule实体正确映射到periodictaskschedules表
            
            // 检查并确保表结构正确
            migrationBuilder.Sql(@"
                -- 验证 periodictaskschedules 表存在且结构正确
                SHOW CREATE TABLE periodictaskschedules;
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // 无需回滚
        }
    }
}