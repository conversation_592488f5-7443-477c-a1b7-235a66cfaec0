# 班次管理系统API测试脚本
# 使用VS Code REST Client扩展或类似工具执行

### 变量定义
@baseUrl = https://localhost:5001
@apiUrl = {{baseUrl}}/api/v2/work-shifts

### 1. 获取所有班次
GET {{apiUrl}}
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

### 2. 创建新班次
POST {{apiUrl}}
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

{
  "shiftName": "早班",
  "shiftCode": "EARLY",
  "shiftType": "Day",
  "startTime": "06:00:00",
  "endTime": "14:00:00",
  "taskClaimTime": "06:00:00",
  "isOvernight": false,
  "description": "早班 6:00-14:00"
}

### 3. 分配用户到班次
POST {{apiUrl}}/assignments
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

{
  "userId": 1,
  "shiftId": 1,
  "effectiveDate": "2025-06-19",
  "expiryDate": null,
  "assignmentType": "Permanent",
  "notes": "分配到白班"
}

### 4. 获取用户当前班次
GET {{apiUrl}}/current
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

### 5. 领取任务
POST {{apiUrl}}/claim-task
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

{
  "taskId": 1,
  "notes": "领取设备维护任务"
}

### 6. 更新任务领取状态
PUT {{apiUrl}}/claims/1/status
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

{
  "claimStatus": "Started",
  "notes": "开始执行任务"
}

### 7. 获取用户今日任务领取记录
GET {{apiUrl}}/claims/today
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

### 8. 获取今日班次任务统计
GET {{apiUrl}}/statistics/today
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

### 9. 获取指定日期的班次统计
GET {{apiUrl}}/statistics/today?statisticsDate=2025-06-19
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

### 10. 获取指定用户的当前班次
GET {{apiUrl}}/users/1/current
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

### 11. 获取指定用户今日任务领取记录
GET {{apiUrl}}/users/1/claims/today
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

### 12. 完成任务领取状态更新
PUT {{apiUrl}}/claims/1/status
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

{
  "claimStatus": "Completed",
  "notes": "任务已完成"
}

### 测试数据准备脚本
### 注意：以下SQL脚本需要在数据库中执行

/*
-- 插入测试班次数据
INSERT INTO work_shifts (shift_name, shift_code, shift_type, start_time, end_time, task_claim_time, is_overnight, description, created_by) VALUES
('白班', 'DAY', 'Day', '08:00:00', '20:00:00', '08:00:00', FALSE, '白班 8:00-20:00', 1),
('夜班', 'NIGHT', 'Night', '20:00:00', '08:00:00', '20:00:00', TRUE, '夜班 20:00-次日8:00', 1);

-- 插入测试用户班次分配
INSERT INTO user_shift_assignments (user_id, shift_id, effective_date, assignment_type, created_by) VALUES
(1, 1, CURDATE(), 'Permanent', 1),
(2, 2, CURDATE(), 'Permanent', 1);

-- 插入测试任务（如果tasks表存在）
INSERT INTO tasks (Name, Description, Status, Priority, TaskType, CreatorUserId, CreationTimestamp, LastUpdatedTimestamp) VALUES
('设备维护检查', '对生产线设备进行日常维护检查', 'Todo', 'High', 'Maintenance', 1, NOW(), NOW()),
('安全巡检', '车间安全巡检任务', 'Todo', 'Medium', 'Safety', 1, NOW(), NOW()),
('质量检测', '产品质量检测任务', 'Todo', 'High', 'Quality', 1, NOW(), NOW());
*/

### 错误处理测试

### 测试无效的班次ID
POST {{apiUrl}}/assignments
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

{
  "userId": 1,
  "shiftId": 999,
  "effectiveDate": "2025-06-19",
  "assignmentType": "Permanent"
}

### 测试重复领取任务
POST {{apiUrl}}/claim-task
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

{
  "taskId": 1,
  "notes": "重复领取测试"
}

### 测试无效的任务ID
POST {{apiUrl}}/claim-task
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

{
  "taskId": 999,
  "notes": "无效任务ID测试"
}

### 性能测试 - 批量获取统计数据
GET {{apiUrl}}/statistics/today?shiftId=1&userId=1
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

### 边界条件测试 - 跨天班次
POST {{apiUrl}}
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

{
  "shiftName": "跨天测试班次",
  "shiftCode": "OVERNIGHT_TEST",
  "shiftType": "Night",
  "startTime": "23:00:00",
  "endTime": "07:00:00",
  "taskClaimTime": "23:00:00",
  "isOvernight": true,
  "description": "跨天班次测试"
}
