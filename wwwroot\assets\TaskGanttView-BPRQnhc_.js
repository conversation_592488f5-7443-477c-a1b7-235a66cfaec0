import{_ as e,b as t,e as a,w as s,r as n,m as l,a as r,a8 as d,o,d as i,A as c,t as p}from"./index-CkwLz8y6.js";import{f as u}from"./date-DeQj3nH2.js";import{t as g}from"./task-Uzj9rZkj.js";const m={class:"gantt-view-container"},h={class:"card-header"},y={class:"header-actions"},w={key:0,class:"loading-container"},D={key:1,class:"empty-data"},v={key:2,class:"gantt-chart-container"},f={class:"gantt-placeholder"};const T=e({name:"TaskGanttView",setup(){const e=n([]),t=n(!0),a=n([]),s=[{text:"最近一周",value:()=>{const e=new Date,t=new Date;return t.setTime(t.getTime()-6048e5),[t,e]}},{text:"最近一个月",value:()=>{const e=new Date,t=new Date;return t.setTime(t.getTime()-2592e6),[t,e]}},{text:"最近三个月",value:()=>{const e=new Date,t=new Date;return t.setTime(t.getTime()-7776e6),[t,e]}}],r=async()=>{t.value=!0;try{const t={pageIndex:1,pageSize:100};a.value&&2===a.value.length&&(t.startDate=u(a.value[0],"yyyy-MM-dd"),t.endDate=u(a.value[1],"yyyy-MM-dd"));const s=await g.getTasks(t);s.success?e.value=s.data.items.map((e=>({...e,startDate:e.startDate||e.createdTime,dueDate:e.dueDate||o(e.startDate||e.createdTime),progress:e.progress||0}))):d.error(s.message||"加载任务数据失败")}catch(s){d.error("加载任务数据时发生错误")}finally{t.value=!1}},o=e=>{const t=new Date(e);return t.setDate(t.getDate()+7),u(t,"yyyy-MM-dd")};return l((()=>{const e=new Date,t=new Date;t.setTime(t.getTime()-2592e6),a.value=[t,e],r()})),{tasks:e,loading:t,dateRange:a,dateShortcuts:s,refreshData:()=>{r()},handleDateRangeChange:e=>{e&&r()},getStatusType:e=>({pending:"info",in_progress:"warning",completed:"success",cancelled:"danger"}[e]||"info"),getStatusText:e=>({pending:"待处理",in_progress:"进行中",completed:"已完成",cancelled:"已取消"}[e]||e)}}},[["render",function(e,n,l,d,u,g){const T=r("Refresh"),k=r("el-icon"),_=r("el-button"),b=r("el-date-picker"),x=r("el-skeleton"),S=r("el-empty"),C=r("el-table-column"),M=r("el-tag"),R=r("el-progress"),I=r("el-table"),V=r("el-card");return o(),t("div",m,[a(V,{class:"gantt-card"},{header:s((()=>[i("div",h,[n[2]||(n[2]=i("h2",null,"任务甘特图",-1)),i("div",y,[a(_,{type:"primary",onClick:d.refreshData},{default:s((()=>[a(k,null,{default:s((()=>[a(T)])),_:1}),n[1]||(n[1]=c(" 刷新 "))])),_:1},8,["onClick"]),a(b,{modelValue:d.dateRange,"onUpdate:modelValue":n[0]||(n[0]=e=>d.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:d.handleDateRangeChange,shortcuts:d.dateShortcuts},null,8,["modelValue","onChange","shortcuts"])])])])),default:s((()=>[d.loading?(o(),t("div",w,[a(x,{rows:10,animated:""})])):0===d.tasks.length?(o(),t("div",D,[a(S,{description:"暂无任务数据"})])):(o(),t("div",v,[i("div",f,[n[3]||(n[3]=i("p",{class:"development-note"},"甘特图组件正在开发中，敬请期待...",-1)),a(I,{data:d.tasks,style:{width:"100%"}},{default:s((()=>[a(C,{prop:"taskId",label:"ID",width:"70"}),a(C,{prop:"title",label:"任务名称","min-width":"200"}),a(C,{prop:"status",label:"状态",width:"120"},{default:s((e=>[a(M,{type:d.getStatusType(e.row.status)},{default:s((()=>[c(p(d.getStatusText(e.row.status)),1)])),_:2},1032,["type"])])),_:1}),a(C,{prop:"startDate",label:"开始日期",width:"120"}),a(C,{prop:"dueDate",label:"截止日期",width:"120"}),a(C,{prop:"progress",label:"进度",width:"180"},{default:s((e=>[a(R,{percentage:e.row.progress},null,8,["percentage"])])),_:1}),a(C,{prop:"assigneeName",label:"负责人",width:"120"})])),_:1},8,["data"])])]))])),_:1})])}],["__scopeId","data-v-52a13f05"]]);export{T as default};
