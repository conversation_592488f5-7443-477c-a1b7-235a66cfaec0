# IT资产管理系统 - 项目优化实施步骤指南

**制定时间**: 2025-06-16  
**实施周期**: 6个月  
**优化目标**: 减少耦合度、提升性能、改进架构

---

## 📋 **总体实施计划**

### **Phase 1: 基础解耦 (第1-2个月)**
- 🎯 **目标**: 建立接口抽象层，实现基础解耦
- ⏱️ **周期**: 8周
- 🔧 **风险**: 低
- 📊 **预期收益**: 提升代码可测试性30%

### **Phase 2: 架构统一 (第3-4个月)**
- 🎯 **目标**: 统一数据访问模式，实现事件驱动
- ⏱️ **周期**: 8周
- 🔧 **风险**: 中
- 📊 **预期收益**: 减少模块耦合50%

### **Phase 3: 性能优化 (第5-6个月)**
- 🎯 **目标**: 性能调优，完成Clean Architecture迁移
- ⏱️ **周期**: 8周
- 🔧 **风险**: 中高
- 📊 **预期收益**: 提升系统性能40%

---

## 🚀 **Phase 1: 基础解耦 (第1-2个月)**

### **Week 1-2: 服务接口定义**

#### **1.1 创建核心服务接口**

**步骤1**: 创建接口定义目录结构
```bash
mkdir -p Application/Interfaces/Services
mkdir -p Application/Interfaces/Repositories
```

**步骤2**: 定义核心服务接口
```csharp
// Application/Interfaces/Services/IAssetService.cs
public interface IAssetService
{
    Task<AssetDto> GetByIdAsync(int id);
    Task<PagedResult<AssetDto>> GetPagedAsync(AssetQueryDto query);
    Task<AssetDto> CreateAsync(CreateAssetDto dto);
    Task<AssetDto> UpdateAsync(int id, UpdateAssetDto dto);
    Task<bool> DeleteAsync(int id);
    Task<bool> ExistsAsync(int id);
}

// Application/Interfaces/Services/IUserService.cs
public interface IUserService
{
    Task<UserDto> GetByIdAsync(int id);
    Task<UserDto> GetByUsernameAsync(string username);
    Task<AuthResultDto> AuthenticateAsync(LoginDto dto);
    Task<UserDto> CreateAsync(CreateUserDto dto);
    Task<bool> ChangePasswordAsync(int userId, ChangePasswordDto dto);
}

// Application/Interfaces/Services/ITaskService.cs
public interface ITaskService
{
    Task<TaskDto> GetByIdAsync(long id);
    Task<PagedResult<TaskDto>> GetPagedAsync(TaskQueryDto query);
    Task<TaskDto> CreateAsync(CreateTaskDto dto);
    Task<TaskDto> UpdateAsync(long id, UpdateTaskDto dto);
    Task<bool> AssignAsync(long taskId, int assigneeId);
    Task<bool> CompleteAsync(long taskId);
}
```

**步骤3**: 修改现有Service实现接口
```csharp
// Application/Features/Assets/Services/AssetService.cs
public class AssetService : IAssetService
{
    private readonly IAssetRepository _repository;
    private readonly IMapper _mapper;
    private readonly ILogger<AssetService> _logger;

    public AssetService(
        IAssetRepository repository,
        IMapper mapper,
        ILogger<AssetService> logger)
    {
        _repository = repository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<AssetDto> GetByIdAsync(int id)
    {
        var asset = await _repository.GetByIdAsync(id);
        return _mapper.Map<AssetDto>(asset);
    }
    
    // 实现其他接口方法...
}
```

**步骤4**: 更新依赖注入配置
```csharp
// Startup.cs - ConfigureServices方法
// 替换具体类注册为接口注册
services.AddScoped<IAssetService, AssetService>();
services.AddScoped<IUserService, UserService>();
services.AddScoped<ITaskService, TaskService>();
```

#### **1.2 Repository接口标准化**

**步骤1**: 创建通用Repository接口
```csharp
// Core/Abstractions/IRepository.cs
public interface IRepository<TEntity, TKey> where TEntity : class
{
    Task<TEntity> GetByIdAsync(TKey id);
    Task<IEnumerable<TEntity>> GetAllAsync();
    Task<PagedResult<TEntity>> GetPagedAsync(int pageIndex, int pageSize);
    Task<TEntity> AddAsync(TEntity entity);
    Task UpdateAsync(TEntity entity);
    Task DeleteAsync(TKey id);
    Task<bool> ExistsAsync(TKey id);
}

// Core/Abstractions/IAssetRepository.cs
public interface IAssetRepository : IRepository<Asset, int>
{
    Task<IEnumerable<Asset>> GetByTypeAsync(int assetTypeId);
    Task<IEnumerable<Asset>> GetByLocationAsync(int locationId);
    Task<IEnumerable<Asset>> SearchAsync(string keyword);
    Task<Asset> GetByCodeAsync(string assetCode);
}
```

**步骤2**: 实现Repository基类
```csharp
// Infrastructure/Data/Repositories/RepositoryBase.cs
public abstract class RepositoryBase<TEntity, TKey> : IRepository<TEntity, TKey>
    where TEntity : class
{
    protected readonly AppDbContext _context;
    protected readonly DbSet<TEntity> _dbSet;
    protected readonly ILogger _logger;

    protected RepositoryBase(AppDbContext context, ILogger logger)
    {
        _context = context;
        _dbSet = context.Set<TEntity>();
        _logger = logger;
    }

    public virtual async Task<TEntity> GetByIdAsync(TKey id)
    {
        return await _dbSet.FindAsync(id);
    }

    public virtual async Task<IEnumerable<TEntity>> GetAllAsync()
    {
        return await _dbSet.ToListAsync();
    }

    // 实现其他通用方法...
}
```

### **Week 3-4: 全局异常处理机制**

#### **1.3 实现全局异常处理中间件**

**步骤1**: 创建自定义异常类
```csharp
// Domain/Exceptions/BusinessException.cs
public class BusinessException : Exception
{
    public string ErrorCode { get; }
    public object Details { get; }

    public BusinessException(string message, string errorCode = null, object details = null)
        : base(message)
    {
        ErrorCode = errorCode;
        Details = details;
    }
}

// Domain/Exceptions/ValidationException.cs
public class ValidationException : Exception
{
    public IDictionary<string, string[]> Errors { get; }

    public ValidationException(IDictionary<string, string[]> errors)
        : base("One or more validation failures have occurred.")
    {
        Errors = errors;
    }
}
```

**步骤2**: 创建全局异常处理中间件
```csharp
// Core/Middleware/GlobalExceptionHandlingMiddleware.cs
public class GlobalExceptionHandlingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<GlobalExceptionHandlingMiddleware> _logger;

    public GlobalExceptionHandlingMiddleware(
        RequestDelegate next,
        ILogger<GlobalExceptionHandlingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            await HandleExceptionAsync(context, ex);
        }
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        _logger.LogError(exception, "An unhandled exception occurred");

        var response = exception switch
        {
            BusinessException businessEx => new ApiResponse<object>
            {
                Success = false,
                Message = businessEx.Message,
                ErrorCode = businessEx.ErrorCode,
                Data = businessEx.Details
            },
            ValidationException validationEx => new ApiResponse<object>
            {
                Success = false,
                Message = "Validation failed",
                ErrorCode = "VALIDATION_ERROR",
                Data = validationEx.Errors
            },
            UnauthorizedAccessException => new ApiResponse<object>
            {
                Success = false,
                Message = "Unauthorized access",
                ErrorCode = "UNAUTHORIZED"
            },
            _ => new ApiResponse<object>
            {
                Success = false,
                Message = "An internal server error occurred",
                ErrorCode = "INTERNAL_ERROR"
            }
        };

        context.Response.StatusCode = GetStatusCode(exception);
        context.Response.ContentType = "application/json";

        var jsonResponse = JsonSerializer.Serialize(response, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        await context.Response.WriteAsync(jsonResponse);
    }

    private static int GetStatusCode(Exception exception) => exception switch
    {
        BusinessException => StatusCodes.Status400BadRequest,
        ValidationException => StatusCodes.Status400BadRequest,
        UnauthorizedAccessException => StatusCodes.Status401Unauthorized,
        _ => StatusCodes.Status500InternalServerError
    };
}
```

**步骤3**: 注册中间件
```csharp
// Startup.cs - Configure方法
public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
{
    // 在其他中间件之前注册
    app.UseMiddleware<GlobalExceptionHandlingMiddleware>();
    
    // 其他中间件...
}
```

### **Week 5-6: 前端防抖节流机制**

#### **1.4 实现请求防抖和节流**

**步骤1**: 创建防抖节流工具函数
```javascript
// frontend/src/utils/performance.js
/**
 * 防抖函数 - 延迟执行，重复调用会重置计时器
 * @param {Function} func 要防抖的函数
 * @param {number} delay 延迟时间(ms)
 * @param {boolean} immediate 是否立即执行
 */
export function debounce(func, delay = 300, immediate = false) {
  let timeoutId = null
  let isInvoked = false

  return function debounced(...args) {
    const context = this

    if (immediate && !isInvoked) {
      func.apply(context, args)
      isInvoked = true
    }

    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => {
      if (!immediate) {
        func.apply(context, args)
      }
      isInvoked = false
    }, delay)
  }
}

/**
 * 节流函数 - 限制执行频率
 * @param {Function} func 要节流的函数
 * @param {number} limit 时间间隔(ms)
 */
export function throttle(func, limit = 300) {
  let inThrottle = false

  return function throttled(...args) {
    const context = this

    if (!inThrottle) {
      func.apply(context, args)
      inThrottle = true
      setTimeout(() => {
        inThrottle = false
      }, limit)
    }
  }
}

/**
 * 请求防重复提交
 * @param {Function} requestFunc 请求函数
 * @param {number} delay 防重复时间(ms)
 */
export function preventDuplicateRequest(requestFunc, delay = 1000) {
  let isRequesting = false
  let lastRequestTime = 0

  return async function(...args) {
    const now = Date.now()
    
    if (isRequesting || (now - lastRequestTime) < delay) {
      console.warn('请求过于频繁，已忽略')
      return Promise.reject(new Error('请求过于频繁'))
    }

    isRequesting = true
    lastRequestTime = now

    try {
      const result = await requestFunc.apply(this, args)
      return result
    } finally {
      isRequesting = false
    }
  }
}
```

**步骤2**: 在API层应用防抖节流
```javascript
// frontend/src/api/asset.js
import request from '@/utils/request'
import { debounce, preventDuplicateRequest } from '@/utils/performance'

// 搜索API应用防抖
export const searchAssets = debounce(async (keyword) => {
  return request.get('/assets/search', { params: { keyword } })
}, 500)

// 创建资产API应用防重复
export const createAsset = preventDuplicateRequest(async (data) => {
  return request.post('/assets', data)
}, 2000)

// 批量操作API应用节流
export const batchUpdateAssets = throttle(async (ids, data) => {
  return request.put('/assets/batch', { ids, ...data })
}, 1000)
```

**步骤3**: 在组件中应用性能优化
```vue
<!-- frontend/src/views/asset/AssetList.vue -->
<template>
  <div class="asset-list">
    <!-- 搜索框应用防抖 -->
    <el-input
      v-model="searchKeyword"
      placeholder="搜索资产..."
      @input="handleSearch"
      clearable
    />
    
    <!-- 表格操作按钮应用防重复 -->
    <el-button
      type="primary"
      @click="handleCreate"
      :loading="isCreating"
    >
      新增资产
    </el-button>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { debounce, preventDuplicateRequest } from '@/utils/performance'
import { searchAssets, createAsset } from '@/api/asset'

const searchKeyword = ref('')
const isCreating = ref(false)

// 搜索防抖处理
const handleSearch = debounce(async (keyword) => {
  if (!keyword.trim()) return
  
  try {
    const result = await searchAssets(keyword)
    // 处理搜索结果
  } catch (error) {
    console.error('搜索失败:', error)
  }
}, 300)

// 创建操作防重复
const handleCreate = preventDuplicateRequest(async () => {
  isCreating.value = true
  try {
    await createAsset(formData.value)
    // 处理创建成功
  } catch (error) {
    console.error('创建失败:', error)
  } finally {
    isCreating.value = false
  }
}, 2000)
</script>
```

### **Week 7-8: 基础监控和日志增强**

#### **1.5 增强日志记录系统**

**步骤1**: 配置结构化日志
```csharp
// Program.cs
Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Information()
    .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
    .MinimumLevel.Override("System", LogEventLevel.Warning)
    .Enrich.FromLogContext()
    .Enrich.WithProperty("Application", "ItAssetsSystem")
    .Enrich.WithProperty("Environment", Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT"))
    .WriteTo.Console(outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}")
    .WriteTo.File(
        path: "Logs/app-.log",
        rollingInterval: RollingInterval.Day,
        retainedFileCountLimit: 30,
        outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}")
    .WriteTo.File(
        path: "Logs/errors-.log",
        restrictedToMinimumLevel: LogEventLevel.Error,
        rollingInterval: RollingInterval.Day,
        retainedFileCountLimit: 90)
    .CreateLogger();
```

**步骤2**: 创建性能监控中间件
```csharp
// Core/Middleware/PerformanceMonitoringMiddleware.cs
public class PerformanceMonitoringMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<PerformanceMonitoringMiddleware> _logger;

    public PerformanceMonitoringMiddleware(
        RequestDelegate next,
        ILogger<PerformanceMonitoringMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        var requestId = Guid.NewGuid().ToString();
        
        using (LogContext.PushProperty("RequestId", requestId))
        {
            _logger.LogInformation("Request started: {Method} {Path}",
                context.Request.Method, context.Request.Path);

            try
            {
                await _next(context);
            }
            finally
            {
                stopwatch.Stop();
                var elapsed = stopwatch.ElapsedMilliseconds;

                _logger.LogInformation(
                    "Request completed: {Method} {Path} {StatusCode} in {ElapsedMs}ms",
                    context.Request.Method,
                    context.Request.Path,
                    context.Response.StatusCode,
                    elapsed);

                // 记录慢请求
                if (elapsed > 1000)
                {
                    _logger.LogWarning(
                        "Slow request detected: {Method} {Path} took {ElapsedMs}ms",
                        context.Request.Method,
                        context.Request.Path,
                        elapsed);
                }
            }
        }
    }
}
```

---

**Phase 1 检查清单**:
- [ ] 所有核心Service实现接口定义
- [ ] Repository层标准化完成
- [ ] 全局异常处理机制就位
- [ ] 前端防抖节流机制实现
- [ ] 增强日志记录系统配置
- [ ] 性能监控中间件部署
- [ ] 单元测试覆盖率达到60%+

---

## 🔄 **Phase 2: 架构统一 (第3-4个月)**

### **Week 9-10: 统一V1/V2数据访问模式**

#### **2.1 实现统一的Repository模式**

**步骤1**: 创建V1模块Repository接口
```csharp
// Core/Abstractions/V1/IAssetRepositoryV1.cs
public interface IAssetRepositoryV1 : IRepository<Asset, int>
{
    Task<PagedResult<Asset>> GetPagedWithDetailsAsync(AssetQueryDto query);
    Task<IEnumerable<Asset>> GetByDepartmentAsync(int departmentId);
    Task<Asset> GetWithHistoryAsync(int id);
}

// Infrastructure/Data/Repositories/V1/AssetRepositoryV1.cs
public class AssetRepositoryV1 : RepositoryBase<Asset, int>, IAssetRepositoryV1
{
    public AssetRepositoryV1(AppDbContext context, ILogger<AssetRepositoryV1> logger)
        : base(context, logger) { }

    public async Task<PagedResult<Asset>> GetPagedWithDetailsAsync(AssetQueryDto query)
    {
        var queryable = _dbSet
            .Include(a => a.AssetType)
            .Include(a => a.Location)
            .Include(a => a.Department)
            .AsQueryable();

        // 应用查询条件
        if (!string.IsNullOrEmpty(query.Keyword))
        {
            queryable = queryable.Where(a =>
                a.Name.Contains(query.Keyword) ||
                a.AssetCode.Contains(query.Keyword));
        }

        if (query.AssetTypeId.HasValue)
        {
            queryable = queryable.Where(a => a.AssetTypeId == query.AssetTypeId);
        }

        var total = await queryable.CountAsync();
        var items = await queryable
            .Skip((query.PageIndex - 1) * query.PageSize)
            .Take(query.PageSize)
            .ToListAsync();

        return new PagedResult<Asset>
        {
            Items = items,
            TotalCount = total,
            PageIndex = query.PageIndex,
            PageSize = query.PageSize
        };
    }
}
```

**步骤2**: 重构V2模块使用相同模式
```csharp
// Infrastructure/Data/Repositories/V2/TaskRepositoryV2.cs
public class TaskRepositoryV2 : RepositoryBase<TaskEntity, long>, ITaskRepository
{
    public TaskRepositoryV2(AppDbContext context, ILogger<TaskRepositoryV2> logger)
        : base(context, logger) { }

    public async Task<PagedResult<TaskEntity>> GetPagedWithDetailsAsync(TaskQueryDto query)
    {
        var queryable = _dbSet
            .Include(t => t.Assignee)
            .Include(t => t.Creator)
            .Include(t => t.Comments)
            .AsQueryable();

        // 统一的查询逻辑
        if (!string.IsNullOrEmpty(query.Keyword))
        {
            queryable = queryable.Where(t =>
                t.Title.Contains(query.Keyword) ||
                t.Description.Contains(query.Keyword));
        }

        return await GetPagedResultAsync(queryable, query.PageIndex, query.PageSize);
    }
}
```

#### **2.2 实现Unit of Work模式**

**步骤1**: 创建UoW接口和实现
```csharp
// Core/Abstractions/IUnitOfWork.cs
public interface IUnitOfWork : IDisposable
{
    IAssetRepositoryV1 Assets { get; }
    IUserRepository Users { get; }
    ITaskRepository Tasks { get; }
    ILocationRepository Locations { get; }

    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    Task BeginTransactionAsync();
    Task CommitTransactionAsync();
    Task RollbackTransactionAsync();
}

// Infrastructure/Data/UnitOfWork.cs
public class UnitOfWork : IUnitOfWork
{
    private readonly AppDbContext _context;
    private IDbContextTransaction _transaction;

    public UnitOfWork(AppDbContext context)
    {
        _context = context;
        Assets = new AssetRepositoryV1(_context, logger);
        Users = new UserRepository(_context, logger);
        Tasks = new TaskRepositoryV2(_context, logger);
        Locations = new LocationRepository(_context, logger);
    }

    public IAssetRepositoryV1 Assets { get; }
    public IUserRepository Users { get; }
    public ITaskRepository Tasks { get; }
    public ILocationRepository Locations { get; }

    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task BeginTransactionAsync()
    {
        _transaction = await _context.Database.BeginTransactionAsync();
    }

    public async Task CommitTransactionAsync()
    {
        if (_transaction != null)
        {
            await _transaction.CommitAsync();
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async Task RollbackTransactionAsync()
    {
        if (_transaction != null)
        {
            await _transaction.RollbackAsync();
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public void Dispose()
    {
        _transaction?.Dispose();
        _context?.Dispose();
    }
}
```

### **Week 11-12: 实现事件总线机制**

#### **2.3 构建事件驱动架构**

**步骤1**: 定义领域事件接口
```csharp
// Domain/Events/IDomainEvent.cs
public interface IDomainEvent
{
    Guid EventId { get; }
    DateTime OccurredOn { get; }
    string EventType { get; }
}

// Domain/Events/DomainEventBase.cs
public abstract class DomainEventBase : IDomainEvent
{
    public Guid EventId { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public abstract string EventType { get; }
}

// Domain/Events/Assets/AssetCreatedEvent.cs
public class AssetCreatedEvent : DomainEventBase
{
    public override string EventType => "AssetCreated";
    public int AssetId { get; }
    public string AssetCode { get; }
    public string AssetName { get; }
    public int CreatedBy { get; }

    public AssetCreatedEvent(int assetId, string assetCode, string assetName, int createdBy)
    {
        AssetId = assetId;
        AssetCode = assetCode;
        AssetName = assetName;
        CreatedBy = createdBy;
    }
}
```

**步骤2**: 实现事件总线
```csharp
// Core/Events/IEventBus.cs
public interface IEventBus
{
    Task PublishAsync<T>(T @event) where T : IDomainEvent;
    void Subscribe<T>(Func<T, Task> handler) where T : IDomainEvent;
    void Unsubscribe<T>(Func<T, Task> handler) where T : IDomainEvent;
}

// Core/Events/InMemoryEventBus.cs
public class InMemoryEventBus : IEventBus
{
    private readonly ConcurrentDictionary<Type, List<Func<IDomainEvent, Task>>> _handlers;
    private readonly ILogger<InMemoryEventBus> _logger;
    private readonly IServiceProvider _serviceProvider;

    public InMemoryEventBus(ILogger<InMemoryEventBus> logger, IServiceProvider serviceProvider)
    {
        _handlers = new ConcurrentDictionary<Type, List<Func<IDomainEvent, Task>>>();
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    public async Task PublishAsync<T>(T @event) where T : IDomainEvent
    {
        var eventType = typeof(T);

        if (_handlers.TryGetValue(eventType, out var handlers))
        {
            var tasks = handlers.Select(handler =>
            {
                return Task.Run(async () =>
                {
                    try
                    {
                        await handler(@event);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error handling event {EventType} with ID {EventId}",
                            @event.EventType, @event.EventId);
                    }
                });
            });

            await Task.WhenAll(tasks);
        }

        _logger.LogInformation("Published event {EventType} with ID {EventId}",
            @event.EventType, @event.EventId);
    }

    public void Subscribe<T>(Func<T, Task> handler) where T : IDomainEvent
    {
        var eventType = typeof(T);
        var wrappedHandler = new Func<IDomainEvent, Task>(e => handler((T)e));

        _handlers.AddOrUpdate(eventType,
            new List<Func<IDomainEvent, Task>> { wrappedHandler },
            (key, existing) =>
            {
                existing.Add(wrappedHandler);
                return existing;
            });
    }

    public void Unsubscribe<T>(Func<T, Task> handler) where T : IDomainEvent
    {
        var eventType = typeof(T);
        if (_handlers.TryGetValue(eventType, out var handlers))
        {
            // 实现取消订阅逻辑
        }
    }
}
```

**步骤3**: 创建事件处理器
```csharp
// Application/EventHandlers/AssetEventHandlers.cs
public class AssetEventHandlers
{
    private readonly ILogger<AssetEventHandlers> _logger;
    private readonly INotificationService _notificationService;
    private readonly IAuditLogService _auditLogService;

    public AssetEventHandlers(
        ILogger<AssetEventHandlers> logger,
        INotificationService notificationService,
        IAuditLogService auditLogService)
    {
        _logger = logger;
        _notificationService = notificationService;
        _auditLogService = auditLogService;
    }

    public async Task HandleAssetCreatedAsync(AssetCreatedEvent @event)
    {
        // 记录审计日志
        await _auditLogService.LogAsync(new AuditLogEntry
        {
            Action = "AssetCreated",
            EntityType = "Asset",
            EntityId = @event.AssetId.ToString(),
            UserId = @event.CreatedBy,
            Details = $"Asset {@event.AssetCode} ({@event.AssetName}) was created",
            Timestamp = @event.OccurredOn
        });

        // 发送通知
        await _notificationService.NotifyAsync(new NotificationRequest
        {
            Type = NotificationType.AssetCreated,
            Title = "新资产已创建",
            Message = $"资产 {@event.AssetCode} 已成功创建",
            Recipients = await GetAssetManagersAsync()
        });

        _logger.LogInformation("Handled AssetCreatedEvent for asset {AssetId}", @event.AssetId);
    }
}
```

### **Week 13-14: 前端事件总线实现**

#### **2.4 前端组件解耦**

**步骤1**: 创建前端事件总线
```javascript
// frontend/src/utils/eventBus.js
import { ref, reactive } from 'vue'

class EventBus {
  constructor() {
    this.events = reactive({})
    this.onceEvents = new Set()
  }

  /**
   * 订阅事件
   * @param {string} eventName 事件名称
   * @param {Function} callback 回调函数
   * @param {boolean} once 是否只执行一次
   */
  on(eventName, callback, once = false) {
    if (!this.events[eventName]) {
      this.events[eventName] = []
    }

    this.events[eventName].push(callback)

    if (once) {
      this.onceEvents.add(callback)
    }

    // 返回取消订阅函数
    return () => this.off(eventName, callback)
  }

  /**
   * 订阅一次性事件
   * @param {string} eventName 事件名称
   * @param {Function} callback 回调函数
   */
  once(eventName, callback) {
    return this.on(eventName, callback, true)
  }

  /**
   * 发布事件
   * @param {string} eventName 事件名称
   * @param {any} data 事件数据
   */
  emit(eventName, data) {
    if (!this.events[eventName]) return

    // 复制回调数组，避免在执行过程中被修改
    const callbacks = [...this.events[eventName]]

    callbacks.forEach(callback => {
      try {
        callback(data)

        // 如果是一次性事件，执行后移除
        if (this.onceEvents.has(callback)) {
          this.off(eventName, callback)
          this.onceEvents.delete(callback)
        }
      } catch (error) {
        console.error(`Error in event handler for ${eventName}:`, error)
      }
    })
  }

  /**
   * 取消订阅
   * @param {string} eventName 事件名称
   * @param {Function} callback 回调函数
   */
  off(eventName, callback) {
    if (!this.events[eventName]) return

    const index = this.events[eventName].indexOf(callback)
    if (index > -1) {
      this.events[eventName].splice(index, 1)
      this.onceEvents.delete(callback)
    }

    // 如果没有监听器了，删除事件
    if (this.events[eventName].length === 0) {
      delete this.events[eventName]
    }
  }

  /**
   * 清除所有事件监听器
   */
  clear() {
    Object.keys(this.events).forEach(eventName => {
      delete this.events[eventName]
    })
    this.onceEvents.clear()
  }
}

// 创建全局事件总线实例
export const eventBus = new EventBus()

// 提供组合式API
export function useEventBus() {
  return {
    on: eventBus.on.bind(eventBus),
    once: eventBus.once.bind(eventBus),
    emit: eventBus.emit.bind(eventBus),
    off: eventBus.off.bind(eventBus)
  }
}
```

**步骤2**: 在组件中使用事件总线
```vue
<!-- frontend/src/components/AssetForm.vue -->
<template>
  <el-form @submit="handleSubmit">
    <!-- 表单内容 -->
    <el-button type="primary" @click="handleSubmit">保存</el-button>
  </el-form>
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue'
import { useEventBus } from '@/utils/eventBus'
import { createAsset, updateAsset } from '@/api/asset'

const { emit, on, off } = useEventBus()

const handleSubmit = async () => {
  try {
    const result = props.isEdit
      ? await updateAsset(props.assetId, formData.value)
      : await createAsset(formData.value)

    // 发布资产变更事件
    emit('asset:changed', {
      type: props.isEdit ? 'updated' : 'created',
      asset: result.data
    })

    // 发布成功通知事件
    emit('notification:success', {
      message: props.isEdit ? '资产更新成功' : '资产创建成功'
    })

  } catch (error) {
    emit('notification:error', {
      message: '操作失败: ' + error.message
    })
  }
}

// 监听外部刷新事件
onMounted(() => {
  on('asset:refresh', handleRefresh)
})

onUnmounted(() => {
  off('asset:refresh', handleRefresh)
})
</script>
```

### **Week 15-16: CQRS模式实现**

#### **2.5 命令查询职责分离**

**步骤1**: 实现CQRS基础结构
```csharp
// Application/Common/CQRS/ICommand.cs
public interface ICommand<TResponse> : IRequest<TResponse> { }
public interface ICommand : IRequest { }

// Application/Common/CQRS/IQuery.cs
public interface IQuery<TResponse> : IRequest<TResponse> { }

// Application/Common/CQRS/ICommandHandler.cs
public interface ICommandHandler<TCommand, TResponse> : IRequestHandler<TCommand, TResponse>
    where TCommand : ICommand<TResponse> { }

public interface ICommandHandler<TCommand> : IRequestHandler<TCommand>
    where TCommand : ICommand { }

// Application/Common/CQRS/IQueryHandler.cs
public interface IQueryHandler<TQuery, TResponse> : IRequestHandler<TQuery, TResponse>
    where TQuery : IQuery<TResponse> { }
```

**步骤2**: 实现具体的Commands和Queries
```csharp
// Application/Features/Assets/Commands/CreateAssetCommand.cs
public class CreateAssetCommand : ICommand<AssetDto>
{
    public string AssetCode { get; set; }
    public string Name { get; set; }
    public int AssetTypeId { get; set; }
    public int? LocationId { get; set; }
    public decimal? Price { get; set; }
    public string Notes { get; set; }
}

public class CreateAssetCommandHandler : ICommandHandler<CreateAssetCommand, AssetDto>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IEventBus _eventBus;
    private readonly ICurrentUserService _currentUserService;

    public CreateAssetCommandHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IEventBus eventBus,
        ICurrentUserService currentUserService)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _eventBus = eventBus;
        _currentUserService = currentUserService;
    }

    public async Task<AssetDto> Handle(CreateAssetCommand request, CancellationToken cancellationToken)
    {
        // 验证资产编码唯一性
        var existingAsset = await _unitOfWork.Assets.GetByCodeAsync(request.AssetCode);
        if (existingAsset != null)
        {
            throw new BusinessException($"资产编码 {request.AssetCode} 已存在", "DUPLICATE_ASSET_CODE");
        }

        // 创建资产实体
        var asset = new Asset
        {
            AssetCode = request.AssetCode,
            Name = request.Name,
            AssetTypeId = request.AssetTypeId,
            LocationId = request.LocationId,
            Price = request.Price,
            Notes = request.Notes,
            Status = AssetStatus.Available,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _currentUserService.GetCurrentUserId()
        };

        // 保存到数据库
        await _unitOfWork.Assets.AddAsync(asset);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // 发布领域事件
        await _eventBus.PublishAsync(new AssetCreatedEvent(
            asset.Id,
            asset.AssetCode,
            asset.Name,
            asset.CreatedBy.Value));

        return _mapper.Map<AssetDto>(asset);
    }
}
```

---

**Phase 2 检查清单**:
- [ ] V1/V2模块数据访问模式统一
- [ ] Unit of Work模式实现
- [ ] 事件总线机制建立
- [ ] 前端组件事件解耦完成
- [ ] CQRS模式基础架构就位
- [ ] 领域事件处理器实现
- [ ] 代码耦合度降低50%+

---

## ⚡ **Phase 3: 性能优化 (第5-6个月)**

### **Week 17-18: 递归调用优化**

#### **3.1 位置层级递归优化**

**步骤1**: 实现深度限制的递归查询
```csharp
// Application/Features/Locations/Services/LocationHierarchyService.cs
public class LocationHierarchyService : ILocationHierarchyService
{
    private readonly ILocationRepository _locationRepository;
    private readonly IMemoryCache _cache;
    private readonly ILogger<LocationHierarchyService> _logger;
    private const int MAX_DEPTH = 10; // 最大递归深度
    private const int CACHE_DURATION_MINUTES = 30;

    public LocationHierarchyService(
        ILocationRepository locationRepository,
        IMemoryCache cache,
        ILogger<LocationHierarchyService> logger)
    {
        _locationRepository = locationRepository;
        _cache = cache;
        _logger = logger;
    }

    public async Task<LocationTreeDto> GetLocationTreeAsync(int? rootId = null, int maxDepth = 5)
    {
        // 限制最大深度
        maxDepth = Math.Min(maxDepth, MAX_DEPTH);

        var cacheKey = $"location_tree_{rootId}_{maxDepth}";

        if (_cache.TryGetValue(cacheKey, out LocationTreeDto cachedResult))
        {
            return cachedResult;
        }

        var result = await BuildLocationTreeAsync(rootId, maxDepth, 0);

        // 缓存结果
        _cache.Set(cacheKey, result, TimeSpan.FromMinutes(CACHE_DURATION_MINUTES));

        return result;
    }

    private async Task<LocationTreeDto> BuildLocationTreeAsync(int? parentId, int maxDepth, int currentDepth)
    {
        // 深度检查
        if (currentDepth >= maxDepth)
        {
            _logger.LogWarning("Location tree depth limit reached: {CurrentDepth}/{MaxDepth}",
                currentDepth, maxDepth);
            return null;
        }

        var locations = await _locationRepository.GetByParentIdAsync(parentId);
        var locationTrees = new List<LocationTreeDto>();

        // 使用并行处理优化性能
        var tasks = locations.Select(async location =>
        {
            var children = await BuildLocationTreeAsync(location.Id, maxDepth, currentDepth + 1);
            return new LocationTreeDto
            {
                Id = location.Id,
                Name = location.Name,
                Type = location.Type,
                ParentId = location.ParentId,
                Children = children?.Children ?? new List<LocationTreeDto>(),
                Depth = currentDepth
            };
        });

        var results = await Task.WhenAll(tasks);

        return new LocationTreeDto
        {
            Children = results.ToList()
        };
    }

    // 迭代版本 - 避免深度递归
    public async Task<LocationTreeDto> GetLocationTreeIterativeAsync(int? rootId = null)
    {
        var allLocations = await _locationRepository.GetAllAsync();
        var locationDict = allLocations.ToDictionary(l => l.Id);
        var result = new LocationTreeDto { Children = new List<LocationTreeDto>() };

        // 使用队列进行广度优先遍历
        var queue = new Queue<(int? parentId, LocationTreeDto parent, int depth)>();
        queue.Enqueue((rootId, result, 0));

        while (queue.Count > 0 && queue.Peek().depth < MAX_DEPTH)
        {
            var (parentId, parent, depth) = queue.Dequeue();

            var children = allLocations.Where(l => l.ParentId == parentId);

            foreach (var location in children)
            {
                var locationDto = new LocationTreeDto
                {
                    Id = location.Id,
                    Name = location.Name,
                    Type = location.Type,
                    ParentId = location.ParentId,
                    Children = new List<LocationTreeDto>(),
                    Depth = depth
                };

                parent.Children.Add(locationDto);
                queue.Enqueue((location.Id, locationDto, depth + 1));
            }
        }

        return result;
    }
}
```

#### **3.2 权限递归优化**

**步骤1**: 实现权限缓存和深度限制
```csharp
// Application/Features/Auth/Services/PermissionService.cs
public class PermissionService : IPermissionService
{
    private readonly IUserRepository _userRepository;
    private readonly IRoleRepository _roleRepository;
    private readonly IMemoryCache _cache;
    private readonly ILogger<PermissionService> _logger;
    private const int MAX_ROLE_DEPTH = 5;

    public async Task<bool> HasPermissionAsync(int userId, string permission)
    {
        var cacheKey = $"user_permission_{userId}_{permission}";

        if (_cache.TryGetValue(cacheKey, out bool cachedResult))
        {
            return cachedResult;
        }

        var result = await CheckPermissionRecursiveAsync(userId, permission, 0, new HashSet<int>());

        // 缓存权限结果5分钟
        _cache.Set(cacheKey, result, TimeSpan.FromMinutes(5));

        return result;
    }

    private async Task<bool> CheckPermissionRecursiveAsync(
        int userId,
        string permission,
        int depth,
        HashSet<int> visitedRoles)
    {
        // 深度限制
        if (depth >= MAX_ROLE_DEPTH)
        {
            _logger.LogWarning("Permission check depth limit reached for user {UserId}", userId);
            return false;
        }

        // 检查直接权限
        var userPermissions = await _userRepository.GetUserPermissionsAsync(userId);
        if (userPermissions.Contains(permission))
        {
            return true;
        }

        // 检查角色权限
        var userRoles = await _userRepository.GetUserRolesAsync(userId);

        foreach (var role in userRoles)
        {
            // 避免循环引用
            if (visitedRoles.Contains(role.Id))
            {
                continue;
            }

            visitedRoles.Add(role.Id);

            // 检查角色直接权限
            var rolePermissions = await _roleRepository.GetRolePermissionsAsync(role.Id);
            if (rolePermissions.Contains(permission))
            {
                return true;
            }

            // 检查父角色权限
            if (role.ParentRoleId.HasValue)
            {
                var parentHasPermission = await CheckPermissionRecursiveAsync(
                    role.ParentRoleId.Value, permission, depth + 1, visitedRoles);
                if (parentHasPermission)
                {
                    return true;
                }
            }
        }

        return false;
    }
}
```

### **Week 19-20: 并行处理优化**

#### **3.3 异步并行处理实现**

**步骤1**: 批量操作并行化
```csharp
// Application/Features/Assets/Services/AssetBatchService.cs
public class AssetBatchService : IAssetBatchService
{
    private readonly IAssetRepository _assetRepository;
    private readonly IEventBus _eventBus;
    private readonly ILogger<AssetBatchService> _logger;
    private readonly SemaphoreSlim _semaphore;

    public AssetBatchService(
        IAssetRepository assetRepository,
        IEventBus eventBus,
        ILogger<AssetBatchService> logger)
    {
        _assetRepository = assetRepository;
        _eventBus = eventBus;
        _logger = logger;
        _semaphore = new SemaphoreSlim(Environment.ProcessorCount * 2); // 限制并发数
    }

    public async Task<BatchOperationResult> BatchUpdateAssetsAsync(
        IEnumerable<int> assetIds,
        UpdateAssetDto updateData)
    {
        var results = new ConcurrentBag<AssetOperationResult>();
        var assetIdList = assetIds.ToList();

        _logger.LogInformation("Starting batch update for {Count} assets", assetIdList.Count);

        // 分批处理，避免过多并发
        const int batchSize = 50;
        var batches = assetIdList.Chunk(batchSize);

        foreach (var batch in batches)
        {
            var tasks = batch.Select(async assetId =>
            {
                await _semaphore.WaitAsync();
                try
                {
                    return await UpdateSingleAssetAsync(assetId, updateData);
                }
                finally
                {
                    _semaphore.Release();
                }
            });

            var batchResults = await Task.WhenAll(tasks);
            foreach (var result in batchResults)
            {
                results.Add(result);
            }
        }

        var successCount = results.Count(r => r.Success);
        var failureCount = results.Count(r => !r.Success);

        _logger.LogInformation("Batch update completed: {Success} success, {Failure} failures",
            successCount, failureCount);

        return new BatchOperationResult
        {
            TotalCount = assetIdList.Count,
            SuccessCount = successCount,
            FailureCount = failureCount,
            Results = results.ToList()
        };
    }

    private async Task<AssetOperationResult> UpdateSingleAssetAsync(int assetId, UpdateAssetDto updateData)
    {
        try
        {
            var asset = await _assetRepository.GetByIdAsync(assetId);
            if (asset == null)
            {
                return new AssetOperationResult
                {
                    AssetId = assetId,
                    Success = false,
                    ErrorMessage = "Asset not found"
                };
            }

            // 更新资产属性
            asset.Name = updateData.Name ?? asset.Name;
            asset.LocationId = updateData.LocationId ?? asset.LocationId;
            asset.Status = updateData.Status ?? asset.Status;
            asset.UpdatedAt = DateTime.UtcNow;

            await _assetRepository.UpdateAsync(asset);

            // 发布事件
            await _eventBus.PublishAsync(new AssetUpdatedEvent(asset.Id, asset.AssetCode));

            return new AssetOperationResult
            {
                AssetId = assetId,
                Success = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update asset {AssetId}", assetId);
            return new AssetOperationResult
            {
                AssetId = assetId,
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }
}
```

#### **3.4 数据库查询并行优化**

**步骤1**: 实现并行数据加载
```csharp
// Application/Features/Dashboard/Services/DashboardService.cs
public class DashboardService : IDashboardService
{
    private readonly IAssetRepository _assetRepository;
    private readonly ITaskRepository _taskRepository;
    private readonly IFaultRepository _faultRepository;
    private readonly IUserRepository _userRepository;

    public async Task<DashboardDataDto> GetDashboardDataAsync()
    {
        // 并行执行多个查询
        var tasks = new[]
        {
            GetAssetStatisticsAsync(),
            GetTaskStatisticsAsync(),
            GetFaultStatisticsAsync(),
            GetUserStatisticsAsync(),
            GetRecentActivitiesAsync()
        };

        var results = await Task.WhenAll(tasks);

        return new DashboardDataDto
        {
            AssetStatistics = results[0] as AssetStatisticsDto,
            TaskStatistics = results[1] as TaskStatisticsDto,
            FaultStatistics = results[2] as FaultStatisticsDto,
            UserStatistics = results[3] as UserStatisticsDto,
            RecentActivities = results[4] as List<ActivityDto>
        };
    }

    private async Task<object> GetAssetStatisticsAsync()
    {
        // 并行执行多个统计查询
        var statisticsTasks = new[]
        {
            _assetRepository.GetTotalCountAsync(),
            _assetRepository.GetCountByStatusAsync(AssetStatus.Available),
            _assetRepository.GetCountByStatusAsync(AssetStatus.InUse),
            _assetRepository.GetCountByStatusAsync(AssetStatus.Maintenance)
        };

        var results = await Task.WhenAll(statisticsTasks);

        return new AssetStatisticsDto
        {
            TotalCount = results[0],
            AvailableCount = results[1],
            InUseCount = results[2],
            MaintenanceCount = results[3]
        };
    }
}
```

### **Week 21-22: Clean Architecture完整迁移**

#### **3.5 V1模块Clean Architecture迁移**

**步骤1**: 重构V1控制器
```csharp
// Api/V1/Controllers/AssetsController.cs
[ApiController]
[Route("api/v1/[controller]")]
[ApiVersion("1.0")]
public class AssetsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<AssetsController> _logger;

    public AssetsController(IMediator mediator, ILogger<AssetsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    [HttpGet]
    public async Task<ActionResult<ApiResponse<PagedResult<AssetDto>>>> GetAssets(
        [FromQuery] GetAssetsQuery query)
    {
        try
        {
            var result = await _mediator.Send(query);
            return Ok(ApiResponse<PagedResult<AssetDto>>.Success(result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting assets");
            return StatusCode(500, ApiResponse<PagedResult<AssetDto>>.Failure("Internal server error"));
        }
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<ApiResponse<AssetDto>>> GetAsset(int id)
    {
        var query = new GetAssetByIdQuery { Id = id };
        var result = await _mediator.Send(query);

        if (result == null)
        {
            return NotFound(ApiResponse<AssetDto>.Failure("Asset not found"));
        }

        return Ok(ApiResponse<AssetDto>.Success(result));
    }

    [HttpPost]
    public async Task<ActionResult<ApiResponse<AssetDto>>> CreateAsset(
        [FromBody] CreateAssetCommand command)
    {
        var result = await _mediator.Send(command);
        return CreatedAtAction(nameof(GetAsset), new { id = result.Id },
            ApiResponse<AssetDto>.Success(result));
    }
}
```

**步骤2**: 实现V1查询处理器
```csharp
// Application/Features/Assets/Queries/GetAssetsQuery.cs
public class GetAssetsQuery : IQuery<PagedResult<AssetDto>>
{
    public int PageIndex { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public string Keyword { get; set; }
    public int? AssetTypeId { get; set; }
    public int? LocationId { get; set; }
    public AssetStatus? Status { get; set; }
}

public class GetAssetsQueryHandler : IQueryHandler<GetAssetsQuery, PagedResult<AssetDto>>
{
    private readonly IAssetRepository _assetRepository;
    private readonly IMapper _mapper;
    private readonly IMemoryCache _cache;

    public GetAssetsQueryHandler(
        IAssetRepository assetRepository,
        IMapper mapper,
        IMemoryCache cache)
    {
        _assetRepository = assetRepository;
        _mapper = mapper;
        _cache = cache;
    }

    public async Task<PagedResult<AssetDto>> Handle(GetAssetsQuery request, CancellationToken cancellationToken)
    {
        // 构建缓存键
        var cacheKey = $"assets_query_{request.PageIndex}_{request.PageSize}_{request.Keyword}_{request.AssetTypeId}_{request.LocationId}_{request.Status}";

        if (_cache.TryGetValue(cacheKey, out PagedResult<AssetDto> cachedResult))
        {
            return cachedResult;
        }

        // 构建查询规范
        var specification = new AssetSpecification()
            .WithKeyword(request.Keyword)
            .WithAssetType(request.AssetTypeId)
            .WithLocation(request.LocationId)
            .WithStatus(request.Status);

        var assets = await _assetRepository.GetPagedAsync(specification, request.PageIndex, request.PageSize);
        var result = _mapper.Map<PagedResult<AssetDto>>(assets);

        // 缓存结果5分钟
        _cache.Set(cacheKey, result, TimeSpan.FromMinutes(5));

        return result;
    }
}
```

### **Week 23-24: 最终优化和测试**

#### **3.6 性能监控和调优**

**步骤1**: 实现性能监控
```csharp
// Core/Monitoring/PerformanceMonitor.cs
public class PerformanceMonitor : IPerformanceMonitor
{
    private readonly ILogger<PerformanceMonitor> _logger;
    private readonly IMetricsCollector _metricsCollector;

    public async Task<T> MonitorAsync<T>(string operationName, Func<Task<T>> operation)
    {
        var stopwatch = Stopwatch.StartNew();
        var startTime = DateTime.UtcNow;

        try
        {
            var result = await operation();
            stopwatch.Stop();

            var duration = stopwatch.ElapsedMilliseconds;

            // 记录性能指标
            _metricsCollector.RecordOperationDuration(operationName, duration);

            if (duration > 1000) // 超过1秒的慢操作
            {
                _logger.LogWarning("Slow operation detected: {Operation} took {Duration}ms",
                    operationName, duration);
            }

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _metricsCollector.RecordOperationError(operationName);
            _logger.LogError(ex, "Operation failed: {Operation} after {Duration}ms",
                operationName, stopwatch.ElapsedMilliseconds);
            throw;
        }
    }
}
```

**步骤2**: 前端性能优化
```javascript
// frontend/src/utils/performance.js
/**
 * 组件性能监控
 */
export function usePerformanceMonitor() {
  const performanceData = ref({})

  const measureRender = (componentName) => {
    const startTime = performance.now()

    return {
      end: () => {
        const endTime = performance.now()
        const duration = endTime - startTime

        performanceData.value[componentName] = {
          renderTime: duration,
          timestamp: Date.now()
        }

        if (duration > 100) {
          console.warn(`Slow render detected: ${componentName} took ${duration.toFixed(2)}ms`)
        }
      }
    }
  }

  const measureAsync = async (operationName, asyncOperation) => {
    const startTime = performance.now()

    try {
      const result = await asyncOperation()
      const duration = performance.now() - startTime

      console.log(`${operationName} completed in ${duration.toFixed(2)}ms`)
      return result
    } catch (error) {
      const duration = performance.now() - startTime
      console.error(`${operationName} failed after ${duration.toFixed(2)}ms:`, error)
      throw error
    }
  }

  return {
    performanceData: readonly(performanceData),
    measureRender,
    measureAsync
  }
}
```

---

## 📊 **实施进度跟踪**

### **关键里程碑**

| 阶段 | 里程碑 | 完成标准 | 验收指标 |
|------|--------|----------|----------|
| Phase 1 | 基础解耦完成 | 所有Service实现接口 | 代码耦合度降低30% |
| Phase 2 | 架构统一完成 | 事件驱动架构就位 | 模块间依赖减少50% |
| Phase 3 | 性能优化完成 | Clean Architecture迁移 | 系统性能提升40% |

### **风险控制措施**

1. **技术风险**:
   - 每周代码审查
   - 自动化测试覆盖
   - 渐进式重构策略

2. **进度风险**:
   - 每两周进度评估
   - 关键路径监控
   - 资源动态调配

3. **质量风险**:
   - 持续集成/部署
   - 性能基准测试
   - 用户验收测试

---

## 🎯 **最终验收标准**

### **技术指标**
- [ ] 代码耦合度降低60%+
- [ ] 系统响应时间提升40%+
- [ ] 单元测试覆盖率达到85%+
- [ ] 代码重复率降低到5%以下

### **架构指标**
- [ ] 100%的Service层实现接口抽象
- [ ] 事件驱动架构覆盖所有核心业务
- [ ] Clean Architecture模式全面应用
- [ ] 递归调用深度限制机制就位

### **性能指标**
- [ ] API平均响应时间<200ms
- [ ] 并发处理能力提升50%+
- [ ] 内存使用优化30%+
- [ ] 数据库查询性能提升40%+

---

**总结**: 这个6个月的优化计划将系统性地改善项目的架构质量、性能表现和可维护性，为未来的云原生演进奠定坚实基础。每个阶段都有明确的目标和验收标准，确保优化工作的有效性和可衡量性。
