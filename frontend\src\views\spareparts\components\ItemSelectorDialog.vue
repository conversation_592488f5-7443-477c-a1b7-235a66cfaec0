<template>
  <el-dialog
    v-model="visible"
    title="选择维修物品"
    width="70%"
    :before-close="handleClose"
  >
    <div class="item-selector">
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="备件名称">
            <el-input
              v-model="searchForm.name"
              placeholder="备件名称"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item label="备件编码">
            <el-input
              v-model="searchForm.code"
              placeholder="备件编码"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item label="类型">
            <el-select v-model="searchForm.typeId" placeholder="备件类型" clearable>
              <el-option
                v-for="type in sparePartTypes"
                :key="type.id"
                :label="type.name"
                :value="type.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格区域 -->
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="sparePartList"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="code" label="备件编码" width="120" />
        <el-table-column prop="name" label="备件名称" min-width="150" />
        <el-table-column prop="specification" label="规格型号" width="120" />
        <el-table-column prop="brand" label="品牌" width="100" />
        <el-table-column prop="typeName" label="类型" width="100" />
        <el-table-column prop="stockQuantity" label="库存数量" width="100" align="center">
          <template #default="{ row }">
            <span :class="getStockClass(row.stockQuantity, row.minStock)">
              {{ row.stockQuantity }} {{ row.unit }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="单价" width="100" align="right">
          <template #default="{ row }">
            {{ row.price ? `¥${formatAmount(row.price)}` : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="locationName" label="库位" width="120" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="searchForm.pageIndex"
          v-model:page-size="searchForm.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :disabled="selectedItems.length === 0">
          确定选择 ({{ selectedItems.length }})
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'confirm'])

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const sparePartList = ref([])
const sparePartTypes = ref([])
const selectedItems = ref([])
const total = ref(0)

const tableRef = ref()

// 搜索表单
const searchForm = reactive({
  name: '',
  code: '',
  typeId: null,
  pageIndex: 1,
  pageSize: 10
})

// 方法
const formatAmount = (amount) => {
  if (amount == null || amount === '') return '0.00'
  return Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const getStockClass = (stock, minStock) => {
  if (stock <= 0) return 'stock-empty'
  if (stock <= minStock) return 'stock-low'
  return 'stock-normal'
}

const handleClose = () => {
  visible.value = false
  emit('update:modelValue', false)
  resetSelection()
}

const resetSelection = () => {
  selectedItems.value = []
  if (tableRef.value) {
    tableRef.value.clearSelection()
  }
}

const handleSelectionChange = (selection) => {
  selectedItems.value = selection.map(item => ({
    id: item.id,
    partId: item.id,
    partName: item.name,
    partCode: item.code,
    quantity: 1, // 默认数量为1
    specification: item.specification,
    brand: item.brand,
    unit: item.unit
  }))
}

const handleConfirm = () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请选择至少一个备件')
    return
  }
  
  emit('confirm', selectedItems.value)
  handleClose()
}

const handleSearch = () => {
  searchForm.pageIndex = 1
  loadSparePartList()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    code: '',
    typeId: null,
    pageIndex: 1,
    pageSize: 10
  })
  loadSparePartList()
}

const handleSizeChange = (size) => {
  searchForm.pageSize = size
  loadSparePartList()
}

const handleCurrentChange = (page) => {
  searchForm.pageIndex = page
  loadSparePartList()
}

const loadSparePartList = async () => {
  loading.value = true
  try {
    // TODO: 从API获取备件列表
    // const response = await sparePartApi.getSpareParts(searchForm)
    // sparePartList.value = response.data.items
    // total.value = response.data.total
    
    // 模拟数据
    sparePartList.value = [
      {
        id: 1,
        code: 'SP001',
        name: '电机轴承',
        specification: '6205-2RS',
        brand: 'SKF',
        typeName: '机械件',
        stockQuantity: 15,
        minStock: 5,
        unit: '个',
        price: 150.00,
        locationName: '仓库A区-01'
      },
      {
        id: 2,
        code: 'SP002',
        name: '变频器模块',
        specification: 'IGBT-300A',
        brand: 'ABB',
        typeName: '电气件',
        stockQuantity: 4,
        minStock: 2,
        unit: '个',
        price: 2500.00,
        locationName: '仓库A区-01'
      },
      {
        id: 3,
        code: 'SP003',
        name: '传感器',
        specification: 'PT100',
        brand: 'Siemens',
        typeName: '仪表件',
        stockQuantity: 23,
        minStock: 10,
        unit: '个',
        price: 80.00,
        locationName: '仓库A区-02'
      }
    ]
    total.value = 3
  } catch (error) {
    console.error('获取备件列表失败:', error)
    ElMessage.error('获取备件列表失败')
  } finally {
    loading.value = false
  }
}

const loadSparePartTypes = async () => {
  try {
    // TODO: 从API获取备件类型
    // const response = await sparePartApi.getSparePartTypes()
    // sparePartTypes.value = response.data
    
    // 模拟数据
    sparePartTypes.value = [
      { id: 1, name: '机械件' },
      { id: 2, name: '电气件' },
      { id: 3, name: '仪表件' }
    ]
  } catch (error) {
    console.error('获取备件类型失败:', error)
  }
}

// 监听器
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    loadSparePartList()
    loadSparePartTypes()
  }
})

// 生命周期
onMounted(() => {
  loadSparePartTypes()
})
</script>

<style scoped>
.item-selector {
  max-height: 60vh;
  overflow-y: auto;
}

.search-area {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 16px;
  text-align: right;
}

.stock-empty {
  color: #f56c6c;
  font-weight: bold;
}

.stock-low {
  color: #e6a23c;
  font-weight: bold;
}

.stock-normal {
  color: #67c23a;
}
</style>
