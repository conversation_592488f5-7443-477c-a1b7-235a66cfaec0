using System;
using System.Collections.Generic;

namespace ItAssetsSystem.Core.Configuration
{
    /// <summary>
    /// API版本配置服务
    /// 支持动态切换API版本，实现渐进式升级
    /// </summary>
    public class ApiVersionConfiguration
    {
        /// <summary>
        /// 默认API版本
        /// </summary>
        public string DefaultVersion { get; set; } = "v1";

        /// <summary>
        /// 服务版本映射
        /// </summary>
        public Dictionary<string, ServiceVersionConfig> Services { get; set; } = new();

        /// <summary>
        /// A/B测试配置
        /// </summary>
        public ABTestConfig ABTest { get; set; } = new();

        /// <summary>
        /// 是否启用版本切换
        /// </summary>
        public bool EnableVersionSwitching { get; set; } = true;

        /// <summary>
        /// 获取服务的API版本
        /// </summary>
        /// <param name="serviceName">服务名称</param>
        /// <param name="userId">用户ID（用于A/B测试）</param>
        /// <returns>API版本</returns>
        public string GetServiceVersion(string serviceName, int? userId = null)
        {
            if (!EnableVersionSwitching)
            {
                return DefaultVersion;
            }

            if (Services.TryGetValue(serviceName, out var config))
            {
                // 检查A/B测试
                if (ABTest.Enabled && userId.HasValue)
                {
                    var testGroup = GetABTestGroup(userId.Value);
                    if (testGroup == "B" && !string.IsNullOrEmpty(config.TestVersion))
                    {
                        return config.TestVersion;
                    }
                }

                // 检查灰度发布
                if (config.GrayRelease.Enabled)
                {
                    var percentage = GetGrayReleasePercentage();
                    if (percentage <= config.GrayRelease.Percentage)
                    {
                        return config.GrayRelease.TargetVersion;
                    }
                }

                return config.CurrentVersion;
            }

            return DefaultVersion;
        }

        /// <summary>
        /// 获取A/B测试分组
        /// </summary>
        private string GetABTestGroup(int userId)
        {
            // 简单的哈希分组算法
            return (userId % 2 == 0) ? "A" : "B";
        }

        /// <summary>
        /// 获取灰度发布百分比（0-100）
        /// </summary>
        private int GetGrayReleasePercentage()
        {
            // 基于时间的随机数，确保一定的随机性
            var random = new Random(DateTime.Now.Millisecond);
            return random.Next(0, 101);
        }
    }

    /// <summary>
    /// 服务版本配置
    /// </summary>
    public class ServiceVersionConfig
    {
        /// <summary>
        /// 当前版本
        /// </summary>
        public string CurrentVersion { get; set; } = "v1";

        /// <summary>
        /// 测试版本（用于A/B测试）
        /// </summary>
        public string TestVersion { get; set; } = "v1.1";

        /// <summary>
        /// 灰度发布配置
        /// </summary>
        public GrayReleaseConfig GrayRelease { get; set; } = new();

        /// <summary>
        /// 是否启用此服务的版本切换
        /// </summary>
        public bool Enabled { get; set; } = true;
    }

    /// <summary>
    /// 灰度发布配置
    /// </summary>
    public class GrayReleaseConfig
    {
        /// <summary>
        /// 是否启用灰度发布
        /// </summary>
        public bool Enabled { get; set; } = false;

        /// <summary>
        /// 灰度发布百分比（0-100）
        /// </summary>
        public int Percentage { get; set; } = 10;

        /// <summary>
        /// 目标版本
        /// </summary>
        public string TargetVersion { get; set; } = "v1.1";
    }

    /// <summary>
    /// A/B测试配置
    /// </summary>
    public class ABTestConfig
    {
        /// <summary>
        /// 是否启用A/B测试
        /// </summary>
        public bool Enabled { get; set; } = false;

        /// <summary>
        /// 测试名称
        /// </summary>
        public string TestName { get; set; } = "API_Version_Test";

        /// <summary>
        /// 测试描述
        /// </summary>
        public string Description { get; set; } = "API版本切换A/B测试";

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
    }
}
