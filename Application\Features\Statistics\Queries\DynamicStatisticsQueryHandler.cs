// File: Application/Features/Statistics/Queries/DynamicStatisticsQueryHandler.cs
// Description: 处理动态统计查询的 MediatR Handler，依赖于仓储接口而非DbContext

using MediatR;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Core.Abstractions; // 依赖于Domain层的接口
using ItAssetsSystem.Application.Models;
using ItAssetsSystem.Application.Features.Statistics.Dtos;
using System.Threading;
using System.Threading.Tasks;

namespace ItAssetsSystem.Application.Features.Statistics.Queries;

/// <summary>
/// 动态统计查询处理器
/// </summary>
public class DynamicStatisticsQueryHandler : IRequestHandler<DynamicStatisticsQuery, Result<DynamicStatisticsResultDto>>
{
    private readonly IStatisticsRepository _statisticsRepository;
    private readonly ILogger<DynamicStatisticsQueryHandler> _logger;

    public DynamicStatisticsQueryHandler(IStatisticsRepository statisticsRepository, ILogger<DynamicStatisticsQueryHandler> logger)
    {
        _statisticsRepository = statisticsRepository;
        _logger = logger;
    }

    public async Task<Result<DynamicStatisticsResultDto>> Handle(DynamicStatisticsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("开始处理动态统计查询: {Query}", request);

        // 验证逻辑（更佳实践是使用 FluentValidation 和 MediatR Pipeline Behavior）
        if (string.IsNullOrEmpty(request.QueryRequest.Dimension) || string.IsNullOrEmpty(request.QueryRequest.Metric))
        {
             _logger.LogWarning("查询请求无效: 缺少维度或度量指标。");
             return Result.Failure<DynamicStatisticsResultDto>(new Error("Validation.Error", "维度和度量指标不能为空。"));
        }

        try
        {
            // 将所有复杂的数据查询逻辑委托给仓储层
            var data = await _statisticsRepository.GetDynamicStatisticsAsync(request, cancellationToken);

            _logger.LogInformation("动态统计查询成功，返回聚合记录。");
            
            // 即使查询结果为空，也应视为成功
            return Result.Success(data);
        }
        catch (System.Exception ex)
        {
            _logger.LogError(ex, "处理动态统计查询时发生未捕获的异常。");
            return Result.Failure<DynamicStatisticsResultDto>(new Error("Query.Exception", "查询过程中发生严重错误。"));
        }
    }
}