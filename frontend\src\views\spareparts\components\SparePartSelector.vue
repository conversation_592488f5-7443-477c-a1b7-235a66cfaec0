<template>
  <el-dialog :model-value="props.visible" @update:model-value="emit('update:visible', $event)" title="选择备件" width="800px" @close="handleClose">
    <!-- 搜索筛选 -->
    <div class="filter-section">
      <el-form :inline="true" :model="queryParams" class="filter-form">
        <el-form-item label="备件名称">
          <el-input v-model="queryParams.name" placeholder="备件名称" clearable @keyup.enter="loadSpareParts" />
        </el-form-item>
        <el-form-item label="备件编号">
          <el-input v-model="queryParams.code" placeholder="备件编号" clearable @keyup.enter="loadSpareParts" />
        </el-form-item>
        <el-form-item label="备件类型">
          <el-select v-model="queryParams.typeId" placeholder="备件类型" clearable>
            <el-option v-for="type in sparePartTypes" :key="type.id" :label="type.name" :value="type.id" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadSpareParts">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 备件列表 -->
    <el-table
      ref="tableRef"
      :data="spareParts"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="code" label="备件编号" width="120" />
      <el-table-column prop="name" label="备件名称" min-width="150" />
      <el-table-column prop="typeName" label="备件类型" width="120" />
      <el-table-column prop="specification" label="规格型号" width="120" />
      <el-table-column prop="stockQuantity" label="库存数量" width="100" align="center">
        <template #default="{ row }">
          <span :class="getStockClass(row)">{{ row.stockQuantity }} {{ row.unit }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="locationName" label="库位" width="120" />
      <el-table-column label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStockTagType(row)" size="small">
            {{ getStockStatusText(row) }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="queryParams.pageIndex"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="loadSpareParts"
        @current-change="loadSpareParts"
      />
    </div>

    <!-- 已选择的备件 -->
    <div v-if="selectedParts.length > 0" class="selected-section">
      <el-divider content-position="left">已选择的备件 ({{ selectedParts.length }})</el-divider>
      <div class="selected-items">
        <el-tag
          v-for="part in selectedParts"
          :key="part.id"
          closable
          @close="removeSelectedPart(part)"
          style="margin-right: 8px; margin-bottom: 8px;"
        >
          {{ part.name }} ({{ part.code }})
        </el-tag>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :disabled="selectedParts.length === 0">
          确认选择 ({{ selectedParts.length }})
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getSpareParts,
  getSparePartTypes
} from '@/api/spareparts'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  selectedParts: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:visible', 'confirm'])

// 响应式数据
const tableRef = ref()
const spareParts = ref([])
const sparePartTypes = ref([])
const total = ref(0)
const selectedParts = ref([])

const queryParams = reactive({
  pageIndex: 1,
  pageSize: 10,
  name: '',
  code: '',
  typeId: null,
  stockStatus: 'available' // 只显示有库存的备件
})

// 计算属性

// 方法
const loadSpareParts = async () => {
  try {
    const response = await getSpareParts(queryParams)
    if (response.success) {
      spareParts.value = response.data.items || []
      total.value = response.data.totalCount || 0
      
      // 恢复已选择的状态
      nextTick(() => {
        spareParts.value.forEach(part => {
          const isSelected = selectedParts.value.some(selected => selected.id === part.id)
          if (isSelected) {
            tableRef.value.toggleRowSelection(part, true)
          }
        })
      })
    }
  } catch (error) {
    console.error('加载备件列表失败:', error)
    ElMessage.error('加载备件列表失败')
  }
}

const loadSparePartTypes = async () => {
  try {
    const response = await getSparePartTypes()
    if (response.success) {
      sparePartTypes.value = response.data || []
    }
  } catch (error) {
    console.error('加载备件类型失败:', error)
  }
}

const handleSelectionChange = (selection) => {
  selectedParts.value = selection
}

const removeSelectedPart = (part) => {
  const index = selectedParts.value.findIndex(p => p.id === part.id)
  if (index > -1) {
    selectedParts.value.splice(index, 1)
    tableRef.value.toggleRowSelection(part, false)
  }
}

const resetQuery = () => {
  Object.assign(queryParams, {
    pageIndex: 1,
    pageSize: 10,
    name: '',
    code: '',
    typeId: null,
    stockStatus: 'available'
  })
  loadSpareParts()
}

const handleConfirm = () => {
  emit('confirm', selectedParts.value)
  handleClose()
}

const handleClose = () => {
  emit('update:visible', false)
  selectedParts.value = []
}

const getStockClass = (row) => {
  if (row.stockQuantity <= 0) return 'stock-empty'
  if (row.stockQuantity <= row.minStock) return 'stock-danger'
  if (row.stockQuantity <= row.warningThreshold) return 'stock-warning'
  return 'stock-normal'
}

const getStockTagType = (row) => {
  if (row.stockQuantity <= 0) return 'danger'
  if (row.stockQuantity <= row.minStock) return 'danger'
  if (row.stockQuantity <= row.warningThreshold) return 'warning'
  return 'success'
}

const getStockStatusText = (row) => {
  if (row.stockQuantity <= 0) return '无库存'
  if (row.stockQuantity <= row.minStock) return '库存不足'
  if (row.stockQuantity <= row.warningThreshold) return '库存预警'
  return '库存正常'
}

// 监听props变化
watch(() => props.selectedParts, (newSelected) => {
  selectedParts.value = [...newSelected]
}, { deep: true, immediate: true })

// 生命周期
onMounted(() => {
  loadSpareParts()
  loadSparePartTypes()
})
</script>

<style scoped>
.filter-section {
  margin-bottom: 16px;
}

.filter-form {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 16px;
  text-align: right;
}

.selected-section {
  margin-top: 16px;
}

.selected-items {
  max-height: 100px;
  overflow-y: auto;
}

.dialog-footer {
  text-align: right;
}

/* 库存状态样式 */
.stock-normal {
  color: #67c23a;
  font-weight: 600;
}

.stock-warning {
  color: #e6a23c;
  font-weight: 600;
}

.stock-danger {
  color: #f56c6c;
  font-weight: 600;
}

.stock-empty {
  color: #909399;
  font-weight: 600;
}
</style>
