// File: Application/Features/SpareParts/Dtos/StockAdjustmentRequest.cs
// Description: 库存调整请求数据传输对象

using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Application.Features.SpareParts.Dtos
{
    /// <summary>
    /// 库存调整请求DTO
    /// </summary>
    public class StockAdjustmentRequest
    {
        /// <summary>
        /// 备件ID
        /// </summary>
        [Required(ErrorMessage = "备件ID不能为空")]
        public long PartId { get; set; }
        
        /// <summary>
        /// 调整数量（正数为增加，负数为减少）
        /// </summary>
        [Required(ErrorMessage = "调整数量不能为空")]
        public int AdjustmentQuantity { get; set; }
        
        /// <summary>
        /// 库位ID
        /// </summary>
        [Required(ErrorMessage = "库位ID不能为空")]
        public long LocationId { get; set; }
        
        /// <summary>
        /// 调整原因
        /// </summary>
        [Required(ErrorMessage = "调整原因不能为空")]
        [StringLength(500, ErrorMessage = "调整原因长度不能超过500个字符")]
        public string Reason { get; set; }
        
        /// <summary>
        /// 参考单号
        /// </summary>
        [StringLength(100, ErrorMessage = "参考单号长度不能超过100个字符")]
        public string ReferenceNumber { get; set; }
    }
}
