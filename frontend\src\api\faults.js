import request from '@/utils/request'

// 获取故障清单
export function getFaults(params) {
  return request({
    url: '/v2/faults',
    method: 'get',
    params
  });
}

// 获取故障详情
export function getFaultDetails(id) {
  return request({
    url: `/v2/faults/${id}`,
    method: 'get'
  });
}

// 创建故障
export function createFault(data) {
  return request({
    url: '/v2/faults',
    method: 'post',
    data
  });
}

// 更新故障
export function updateFault(id, data) {
  return request({
    url: `/v2/faults/${id}`,
    method: 'put',
    data
  });
}

// 关闭故障
export function closeFault(id, data) {
  return request({
    url: `/v2/faults/${id}/close`,
    method: 'post',
    data
  });
}

// 获取故障统计
export function getFaultStatistics(params) {
  return request({
    url: '/v2/faults/statistics',
    method: 'get',
    params
  });
}
