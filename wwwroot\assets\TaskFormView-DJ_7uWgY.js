import{_ as e,b6 as a,aF as l,am as s,b as t,e as r,w as o,r as d,a5 as i,c as u,m as n,a as c,aP as m,a8 as p,u as g,s as k,o as b,Y as f,d as y,F as v,h,t as U,a9 as _,A as I}from"./index-C7OOw0MO.js";import{f as w}from"./date-DeQj3nH2.js";import{t as F}from"./task-BTGSy_AK.js";const V={name:"TaskFormView",components:{Plus:s,Close:l,Back:a},props:{taskId:{type:Number,default:null},isEdit:{type:Boolean,default:!1}},setup(e){const a=g();k();const l=d(null),s=d(!1),t=d(!1),r=d([]),o=d(["bug","功能","文档","优化","紧急","待讨论"]),c=i({title:"",description:"",priority:"medium",status:"pending",startDate:null,dueDate:null,progress:0,assigneeUserId:null,collaboratorUserIds:[],taskType:"Normal",tags:[]}),b=d(null),f={title:[{required:!0,message:"请输入任务标题",trigger:"blur"},{min:2,max:100,message:"长度在 2 到 100 个字符",trigger:"blur"}],description:[{required:!0,message:"请输入任务描述",trigger:"blur"}],priority:[{required:!0,message:"请选择优先级",trigger:"change"}],status:[{required:!0,message:"请选择状态",trigger:"change"}],dueDate:[{validator:(e,a,l)=>{c.startDate&&a&&new Date(a)<new Date(c.startDate)?l(new Error("截止日期不能早于开始日期")):l()},trigger:"change"}]},y=u((()=>r.value.filter((e=>e.id!==c.assigneeUserId)))),v=u((()=>!!b.value&&(c.assigneeUserId===b.value.id||c.collaboratorUserIds.includes(b.value.id)))),h=u((()=>c.assigneeUserId||c.collaboratorUserIds.length>0)),U=async()=>{if(e.taskId){s.value=!0;try{const a=await F.getTaskById(e.taskId);if(a.success){const e=a.data;c.title=e.name||e.title,c.description=e.description,c.priority=e.priority,c.status=e.status,c.startDate=e.planStartDate?new Date(e.planStartDate):null,c.dueDate=e.planEndDate?new Date(e.planEndDate):null,c.progress=e.progress||0,c.assigneeUserId=e.assigneeUserId,c.taskType=e.taskType||"Normal",e.assignees&&Array.isArray(e.assignees)?c.collaboratorUserIds=e.assignees.filter((e=>"Participant"===e.assignmentType)).map((e=>e.userId)):c.collaboratorUserIds=e.collaboratorUserIds||[],c.tags=e.tagList||e.tags||[]}else p.error(a.message||"加载任务详情失败"),_()}catch(a){p.error("加载任务详情时发生错误"),_()}finally{s.value=!1}}},_=()=>{a.back()};return n((()=>{(async()=>{try{const e=await m.getCurrentUser();e.success&&(b.value=e.data)}catch(e){}})(),(async()=>{try{const e=await m.getUsers();e.success?r.value=e.data:p.warning(e.message||"加载用户列表失败")}catch(e){p.warning("加载用户列表时发生错误")}})(),e.isEdit&&U()})),{taskFormRef:l,taskForm:c,rules:f,loading:s,submitting:t,users:r,availableTags:o,currentUser:b,availableCollaborators:y,isCurrentUserInvolved:v,hasAnyAssignee:h,formatProgressTooltip:e=>`${e}%`,submitForm:async()=>{l.value&&await l.value.validate((async(l,s)=>{if(l){t.value=!0;try{const l={name:c.title,description:c.description,priority:c.priority,status:c.status,planStartDate:c.startDate?w(c.startDate,"yyyy-MM-dd"):null,planEndDate:c.dueDate?w(c.dueDate,"yyyy-MM-dd"):null,progress:c.progress,assigneeUserId:c.assigneeUserId,collaboratorUserIds:c.collaboratorUserIds,taskType:c.taskType||"Normal",tags:c.tags};let s;if(s=e.isEdit?await F.updateTask(e.taskId,l):await F.createTask(l),s.success)if(p.success(e.isEdit?"任务更新成功":"任务创建成功"),e.isEdit)a.push({name:"TaskDetail",params:{id:e.taskId}});else{const e=s.data.taskId;a.push({name:"TaskDetail",params:{id:e}})}else p.error(s.message||(e.isEdit?"更新任务失败":"创建任务失败"))}catch(r){p.error(e.isEdit?"更新任务时发生错误":"创建任务时发生错误")}finally{t.value=!1}}}))},resetForm:()=>{var a;e.isEdit?U():null==(a=l.value)||a.resetFields()},goBack:_,addCurrentUserAsCollaborator:()=>{b.value&&!c.collaboratorUserIds.includes(b.value.id)&&(c.collaboratorUserIds.push(b.value.id),p.success("已将您加入协作人员"))},clearAllAssignees:()=>{c.assigneeUserId=null,c.collaboratorUserIds=[],p.success("已清空所有负责人")}}}},D={class:"task-form-container"},C={class:"card-header"},T={class:"header-actions"},E={key:0,class:"loading-container"},A={class:"assignee-section"},x={class:"main-assignee"},B={class:"user-option"},P={class:"user-dept"},q={class:"collaborators",style:{"margin-top":"10px"}},M={class:"user-option"},N={class:"user-dept"},j={class:"quick-actions",style:{"margin-top":"8px"}};const S=e(V,[["render",function(e,a,l,s,d,i){const u=c("Back"),n=c("el-icon"),m=c("el-button"),p=c("el-skeleton"),g=c("el-input"),k=c("el-form-item"),w=c("el-option"),F=c("el-select"),V=c("el-col"),S=c("el-row"),z=c("el-date-picker"),R=c("el-slider"),L=c("Plus"),Y=c("Close"),$=c("el-form"),G=c("el-card");return b(),t("div",D,[r(G,{class:"form-card"},{header:o((()=>[y("div",C,[y("h2",null,U(l.isEdit?"编辑任务":"创建任务"),1),y("div",T,[r(m,{onClick:s.goBack},{default:o((()=>[r(n,null,{default:o((()=>[r(u)])),_:1}),a[10]||(a[10]=I(" 返回 "))])),_:1},8,["onClick"])])])])),default:o((()=>[s.loading?(b(),t("div",E,[r(p,{rows:10,animated:""})])):(b(),f($,{key:1,ref:"taskFormRef",model:s.taskForm,rules:s.rules,"label-width":"100px",class:"task-form"},{default:o((()=>[r(k,{label:"任务标题",prop:"title"},{default:o((()=>[r(g,{modelValue:s.taskForm.title,"onUpdate:modelValue":a[0]||(a[0]=e=>s.taskForm.title=e),placeholder:"请输入任务标题",maxlength:"100","show-word-limit":""},null,8,["modelValue"])])),_:1}),r(k,{label:"任务描述",prop:"description"},{default:o((()=>[r(g,{modelValue:s.taskForm.description,"onUpdate:modelValue":a[1]||(a[1]=e=>s.taskForm.description=e),type:"textarea",rows:4,placeholder:"请输入任务描述",maxlength:"2000","show-word-limit":""},null,8,["modelValue"])])),_:1}),r(S,{gutter:20},{default:o((()=>[r(V,{span:12},{default:o((()=>[r(k,{label:"优先级",prop:"priority"},{default:o((()=>[r(F,{modelValue:s.taskForm.priority,"onUpdate:modelValue":a[2]||(a[2]=e=>s.taskForm.priority=e),placeholder:"请选择优先级",style:{width:"100%"}},{default:o((()=>[r(w,{label:"低",value:"low"}),r(w,{label:"中",value:"medium"}),r(w,{label:"高",value:"high"}),r(w,{label:"紧急",value:"urgent"})])),_:1},8,["modelValue"])])),_:1})])),_:1}),r(V,{span:12},{default:o((()=>[r(k,{label:"状态",prop:"status"},{default:o((()=>[r(F,{modelValue:s.taskForm.status,"onUpdate:modelValue":a[3]||(a[3]=e=>s.taskForm.status=e),placeholder:"请选择状态",style:{width:"100%"}},{default:o((()=>[r(w,{label:"待处理",value:"pending"}),r(w,{label:"进行中",value:"in_progress"}),r(w,{label:"已完成",value:"completed"}),r(w,{label:"已取消",value:"cancelled"})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),r(S,{gutter:20},{default:o((()=>[r(V,{span:12},{default:o((()=>[r(k,{label:"开始日期"},{default:o((()=>[r(z,{modelValue:s.taskForm.startDate,"onUpdate:modelValue":a[4]||(a[4]=e=>s.taskForm.startDate=e),type:"date",placeholder:"选择开始日期",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),r(V,{span:12},{default:o((()=>[r(k,{label:"截止日期",prop:"dueDate"},{default:o((()=>[r(z,{modelValue:s.taskForm.dueDate,"onUpdate:modelValue":a[5]||(a[5]=e=>s.taskForm.dueDate=e),type:"date",placeholder:"选择截止日期",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),r(k,{label:"进度"},{default:o((()=>[r(R,{modelValue:s.taskForm.progress,"onUpdate:modelValue":a[6]||(a[6]=e=>s.taskForm.progress=e),"format-tooltip":s.formatProgressTooltip},null,8,["modelValue","format-tooltip"])])),_:1}),r(k,{label:"负责人"},{default:o((()=>[y("div",A,[y("div",x,[a[11]||(a[11]=y("label",{class:"sub-label"},"主要负责人",-1)),r(F,{modelValue:s.taskForm.assigneeUserId,"onUpdate:modelValue":a[7]||(a[7]=e=>s.taskForm.assigneeUserId=e),placeholder:"选择主要负责人",filterable:"",clearable:"",style:{width:"100%"}},{default:o((()=>[(b(!0),t(v,null,h(s.users,(e=>(b(),f(w,{key:e.id,label:e.name,value:e.id},{default:o((()=>[y("div",B,[y("span",null,U(e.name),1),y("span",P,U(e.department),1)])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])]),y("div",q,[a[12]||(a[12]=y("label",{class:"sub-label"},"协作人员（可选）",-1)),r(F,{modelValue:s.taskForm.collaboratorUserIds,"onUpdate:modelValue":a[8]||(a[8]=e=>s.taskForm.collaboratorUserIds=e),placeholder:"选择协作人员",multiple:"",filterable:"","collapse-tags":"","collapse-tags-tooltip":"",style:{width:"100%"}},{default:o((()=>[(b(!0),t(v,null,h(s.availableCollaborators,(e=>(b(),f(w,{key:e.id,label:e.name,value:e.id},{default:o((()=>[y("div",M,[y("span",null,U(e.name),1),y("span",N,U(e.department),1)])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])]),y("div",j,[s.isCurrentUserInvolved?_("",!0):(b(),f(m,{key:0,size:"small",onClick:s.addCurrentUserAsCollaborator},{default:o((()=>[r(n,null,{default:o((()=>[r(L)])),_:1}),a[13]||(a[13]=I(" 加入协作 "))])),_:1},8,["onClick"])),s.hasAnyAssignee?(b(),f(m,{key:1,size:"small",onClick:s.clearAllAssignees},{default:o((()=>[r(n,null,{default:o((()=>[r(Y)])),_:1}),a[14]||(a[14]=I(" 清空所有 "))])),_:1},8,["onClick"])):_("",!0)])])])),_:1}),r(k,{label:"标签"},{default:o((()=>[r(F,{modelValue:s.taskForm.tags,"onUpdate:modelValue":a[9]||(a[9]=e=>s.taskForm.tags=e),multiple:"",filterable:"","allow-create":"","default-first-option":"",placeholder:"请选择或创建标签",style:{width:"100%"}},{default:o((()=>[(b(!0),t(v,null,h(s.availableTags,(e=>(b(),f(w,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),r(k,null,{default:o((()=>[r(m,{type:"primary",onClick:s.submitForm,loading:s.submitting},{default:o((()=>[I(U(l.isEdit?"更新":"创建"),1)])),_:1},8,["onClick","loading"]),r(m,{onClick:s.resetForm},{default:o((()=>a[15]||(a[15]=[I("重置")]))),_:1},8,["onClick"]),r(m,{onClick:s.goBack},{default:o((()=>a[16]||(a[16]=[I("取消")]))),_:1},8,["onClick"])])),_:1})])),_:1},8,["model","rules"]))])),_:1})])}],["__scopeId","data-v-2a914657"]]);export{S as default};
