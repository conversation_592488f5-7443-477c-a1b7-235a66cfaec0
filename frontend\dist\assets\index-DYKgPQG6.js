import{w as t,_ as e,a as n,m as i,b as o,c as r,e as a,B as s,d as l,f as u,g as d,h as c,j as h,T as p,n as g,k as f,l as y,o as v,p as m,q as x,s as _,r as b,t as S,u as w,v as M,x as I,y as A,z as T,A as C,C as D,D as L,E as P,F as k,G as N,Z as R,H as V,I as O,J as E,K as z,L as B,M as G,N as F,O as W,P as H,Q as Z,R as Y,S as X,U,V as j,W as q,X as K,Y as J,$,a0 as Q,a1 as tt,a2 as et,a3 as nt,a4 as it,a5 as ot,a6 as rt,a7 as at,a8 as st,a9 as lt,aa as ut,ab as dt,ac as ct,ad as ht,ae as pt,af as gt,ag as ft,ah as yt,ai as vt,aj as mt,ak as xt,al as _t,am as bt,an as St,ao as wt,ap as Mt,aq as It,ar as At,as as Tt,at as Ct,au as Dt,av as Lt,aw as Pt,ax as kt,ay as Nt,az as Rt,aA as Vt,aB as Ot,aC as Et,aD as zt,aE as Bt,aF as Gt,aG as Ft,aH as Wt,aI as Ht,aJ as Zt,aK as Yt,aL as Xt,aM as Ut,aN as jt,aO as qt,aP as Kt,aQ as Jt,aR as $t,aS as Qt,aT as te,aU as ee,aV as ne,aW as ie,aX as oe,aY as re,aZ as ae,a_ as se,a$ as le,b0 as ue,b1 as de,b2 as ce,b3 as he,b4 as pe,b5 as ge,b6 as fe,b7 as ye,b8 as ve,b9 as me,ba as xe,bb as _e,bc as be,bd as Se,be as we,bf as Me,bg as Ie,bh as Ae,bi as Te,bj as Ce,bk as De,bl as Le,bm as Pe,bn as ke,bo as Ne,bp as Re,bq as Ve,br as Oe,bs as Ee,bt as ze,bu as Be,bv as Ge,bw as Fe,bx as We,by as He,bz as Ze,bA as Ye,bB as Xe,bC as Ue,bD as je,bE as qe,bF as Ke,bG as Je,bH as $e,bI as Qe,bJ as tn,bK as en,bL as nn,bM as on,bN as rn,bO as an,bP as sn,bQ as ln,bR as un,bS as dn,bT as cn,bU as hn,bV as pn,bW as gn,bX as fn,bY as yn,bZ as vn,b_ as mn,b$ as xn,c0 as _n,c1 as bn,c2 as Sn,c3 as wn,c4 as Mn,c5 as In,c6 as An,c7 as Tn,c8 as Cn,c9 as Dn,ca as Ln,cb as Pn,cc as kn,cd as Nn,ce as Rn,cf as Vn,cg as On,ch as En,ci as zn,cj as Bn,ck as Gn,cl as Fn,cm as Wn,cn as Hn,co as Zn,cp as Yn,cq as Xn,cr as Un,cs as jn,ct as qn,cu as Kn,cv as Jn,cw as $n,cx as Qn,cy as ti,cz as ei,cA as ni,cB as ii,cC as oi,cD as ri,cE as ai,cF as si,cG as li,cH as ui,cI as di,cJ as ci,cK as hi,cL as pi,cM as gi,cN as fi,cO as yi,cP as vi,cQ as mi,cR as xi,cS as _i,cT as bi,cU as Si,cV as wi,cW as Mi,cX as Ii,cY as Ai,cZ as Ti,c_ as Ci,c$ as Di,d0 as Li,d1 as Pi,d2 as ki,d3 as Ni,d4 as Ri,d5 as Vi,d6 as Oi,d7 as Ei,d8 as zi,d9 as Bi,da as Gi,db as Fi,dc as Wi,dd as Hi,de as Zi,df as Yi,dg as Xi,dh as Ui,di as ji,dj as qi,dk as Ki,dl as Ji,dm as $i,dn as Qi,dp as to,dq as eo,dr as no,ds as io,dt as oo,du as ro,dv as ao,dw as so,dx as lo,dy as uo,dz as co,dA as ho,dB as po,dC as go,dD as fo,dE as yo,dF as vo,dG as mo,dH as xo,dI as _o,dJ as bo,dK as So,dL as wo,dM as Mo,dN as Io,dO as Ao,dP as To,dQ as Co,dR as Do,dS as Lo,dT as Po,dU as ko,dV as No,dW as Ro,dX as Vo,dY as Oo,dZ as Eo,d_ as zo,d$ as Bo,e0 as Go,e1 as Fo,e2 as Wo,e3 as Ho,e4 as Zo,e5 as Yo,e6 as Xo,e7 as Uo,e8 as jo,e9 as qo,ea as Ko,eb as Jo,ec as $o,ed as Qo,ee as tr,ef as er,eg as nr,eh as ir,ei as or,ej as rr,ek as ar,el as sr,em as lr,en as ur,eo as dr,ep as cr,eq as hr,er as pr,es as gr,et as fr,eu as yr}from"./install-BB2cVj0s.js";function vr(t,e){return Math.abs(t-e)<1e-8}function mr(e,n,i){var o=0,r=e[0];if(!r)return!1;for(var a=1;a<e.length;a++){var s=e[a];o+=t(r[0],r[1],s[0],s[1],n,i),r=s}var l=e[0];return vr(r[0],l[0])&&vr(r[1],l[1])||(o+=t(r[0],r[1],l[0],l[1],n,i)),0!==o}var xr=[];function _r(t,e){for(var n=0;n<t.length;n++)r(t[n],t[n],e)}function br(t,e,n,i){for(var o=0;o<t.length;o++){var r=t[o];i&&(r=i.project(r)),r&&isFinite(r[0])&&isFinite(r[1])&&(l(e,e,r),u(n,n,r))}}var Sr=function(){function t(t){this.name=t}return t.prototype.setCenter=function(t){this._center=t},t.prototype.getCenter=function(){var t=this._center;return t||(t=this._center=this.calcCenter()),t},t}(),wr=function(){return function(t,e){this.type="polygon",this.exterior=t,this.interiors=e}}(),Mr=function(){return function(t){this.type="linestring",this.points=t}}(),Ir=function(t){function n(e,n,i){var o=t.call(this,e)||this;return o.type="geoJSON",o.geometries=n,o._center=i&&[i[0],i[1]],o}return e(n,t),n.prototype.calcCenter=function(){for(var t,e=this.geometries,n=0,i=0;i<e.length;i++){var o=e[i],r=o.exterior,a=r&&r.length;a>n&&(t=o,n=a)}if(t)return function(t){for(var e=0,n=0,i=0,o=t.length,r=t[o-1][0],a=t[o-1][1],s=0;s<o;s++){var l=t[s][0],u=t[s][1],d=r*u-l*a;e+=d,n+=(r+l)*d,i+=(a+u)*d,r=l,a=u}return e?[n/e/3,i/e/3,e]:[t[0][0]||0,t[0][1]||0]}(t.exterior);var s=this.getBoundingRect();return[s.x+s.width/2,s.y+s.height/2]},n.prototype.getBoundingRect=function(t){var e=this._rect;if(e&&!t)return e;var n=[1/0,1/0],i=[-1/0,-1/0],o=this.geometries;return a(o,(function(e){"polygon"===e.type?br(e.exterior,n,i,t):a(e.points,(function(e){br(e,n,i,t)}))})),isFinite(n[0])&&isFinite(n[1])&&isFinite(i[0])&&isFinite(i[1])||(n[0]=n[1]=i[0]=i[1]=0),e=new s(n[0],n[1],i[0]-n[0],i[1]-n[1]),t||(this._rect=e),e},n.prototype.contain=function(t){var e=this.getBoundingRect(),n=this.geometries;if(!e.contain(t[0],t[1]))return!1;t:for(var i=0,o=n.length;i<o;i++){var r=n[i];if("polygon"===r.type){var a=r.exterior,s=r.interiors;if(mr(a,t[0],t[1])){for(var l=0;l<(s?s.length:0);l++)if(mr(s[l],t[0],t[1]))continue t;return!0}}}return!1},n.prototype.transformTo=function(t,e,n,i){var o=this.getBoundingRect(),r=o.width/o.height;n?i||(i=n/r):n=r*i;for(var l=new s(t,e,n,i),u=o.calculateTransform(l),d=this.geometries,c=0;c<d.length;c++){var h=d[c];"polygon"===h.type?(_r(h.exterior,u),a(h.interiors,(function(t){_r(t,u)}))):a(h.points,(function(t){_r(t,u)}))}(o=this._rect).copy(l),this._center=[o.x+o.width/2,o.y+o.height/2]},n.prototype.cloneShallow=function(t){null==t&&(t=this.name);var e=new n(t,this.geometries,this._center);return e._rect=this._rect,e.transformTo=null,e},n}(Sr),Ar=function(t){function a(e,n){var i=t.call(this,e)||this;return i.type="geoSVG",i._elOnlyForCalculate=n,i}return e(a,t),a.prototype.calcCenter=function(){for(var t=this._elOnlyForCalculate,e=t.getBoundingRect(),a=[e.x+e.width/2,e.y+e.height/2],s=n(xr),l=t;l&&!l.isGeoSVGGraphicRoot;)i(s,l.getLocalTransform(),s),l=l.parent;return o(s,s),r(a,a,s),a},a}(Sr);function Tr(t,e,n){for(var i=0;i<t.length;i++)t[i]=Cr(t[i],e[i],n)}function Cr(t,e,n){for(var i=[],o=e[0],r=e[1],a=0;a<t.length;a+=2){var s=t.charCodeAt(a)-64,l=t.charCodeAt(a+1)-64;s=s>>1^-(1&s),l=l>>1^-(1&l),o=s+=o,r=l+=r,i.push([s/n,l/n])}return i}function Dr(t,e){return t=function(t){if(!t.UTF8Encoding)return t;var e=t,n=e.UTF8Scale;null==n&&(n=1024);var i=e.features;return a(i,(function(t){var e=t.geometry,i=e.encodeOffsets,o=e.coordinates;if(i)switch(e.type){case"LineString":e.coordinates=Cr(o,i,n);break;case"Polygon":case"MultiLineString":Tr(o,i,n);break;case"MultiPolygon":a(o,(function(t,e){return Tr(t,i[e],n)}))}})),e.UTF8Encoding=!1,e}(t),d(c(t.features,(function(t){return t.geometry&&t.properties&&t.geometry.coordinates.length>0})),(function(t){var n=t.properties,i=t.geometry,o=[];switch(i.type){case"Polygon":var r=i.coordinates;o.push(new wr(r[0],r.slice(1)));break;case"MultiPolygon":a(i.coordinates,(function(t){t[0]&&o.push(new wr(t[0],t.slice(1)))}));break;case"LineString":o.push(new Mr([i.coordinates]));break;case"MultiLineString":o.push(new Mr(i.coordinates))}var s=new Ir(n[e||"name"],o,n.cp);return s.properties=n,s}))}function Lr(t){if(t){for(var e=[],n=0;n<t.length;n++)e.push(t[n].slice());return e}}function Pr(t,e){var n=t.label,i=e&&e.getTextGuideLine();return{dataIndex:t.dataIndex,dataType:t.dataType,seriesIndex:t.seriesModel.seriesIndex,text:t.label.style.text,rect:t.hostRect,labelRect:t.rect,align:n.style.align,verticalAlign:n.style.verticalAlign,labelLinePoints:Lr(i&&i.shape.points)}}var kr=["align","verticalAlign","width","height","fontSize"],Nr=new p,Rr=h(),Vr=h();function Or(t,e,n){for(var i=0;i<n.length;i++){var o=n[i];null!=e[o]&&(t[o]=e[o])}}var Er=["x","y","rotation"],zr=function(){function t(){this._labelList=[],this._chartViewList=[]}return t.prototype.clearLabels=function(){this._labelList=[],this._chartViewList=[]},t.prototype._addLabel=function(t,e,n,i,o){var r=i.style,a=i.__hostTarget.textConfig||{},l=i.getComputedTransform(),u=i.getBoundingRect().plain();s.applyTransform(u,u,l),l?Nr.setLocalTransform(l):(Nr.x=Nr.y=Nr.rotation=Nr.originX=Nr.originY=0,Nr.scaleX=Nr.scaleY=1),Nr.rotation=g(Nr.rotation);var d,c=i.__hostTarget;if(c){d=c.getBoundingRect().plain();var h=c.getComputedTransform();s.applyTransform(d,d,h)}var p=d&&c.getTextGuideLine();this._labelList.push({label:i,labelLine:p,seriesModel:n,dataIndex:t,dataType:e,layoutOption:o,computedLayoutOption:null,rect:u,hostRect:d,priority:d?d.width*d.height:0,defaultAttr:{ignore:i.ignore,labelGuideIgnore:p&&p.ignore,x:Nr.x,y:Nr.y,scaleX:Nr.scaleX,scaleY:Nr.scaleY,rotation:Nr.rotation,style:{x:r.x,y:r.y,align:r.align,verticalAlign:r.verticalAlign,width:r.width,height:r.height,fontSize:r.fontSize},cursor:i.cursor,attachedPos:a.position,attachedRot:a.rotation}})},t.prototype.addLabelsOfSeries=function(t){var e=this;this._chartViewList.push(t);var n=t.__model,i=n.get("labelLayout");(f(i)||y(i).length)&&t.group.traverse((function(t){if(t.ignore)return!0;var o=t.getTextContent(),r=v(t);o&&!o.disableLabelLayout&&e._addLabel(r.dataIndex,r.dataType,n,o,i)}))},t.prototype.updateLayoutConfig=function(t){var e=t.getWidth(),n=t.getHeight();function i(t,e){return function(){I(t,e)}}for(var o=0;o<this._labelList.length;o++){var r=this._labelList[o],a=r.label,s=a.__hostTarget,l=r.defaultAttr,u=void 0;u=(u=f(r.layoutOption)?r.layoutOption(Pr(r,s)):r.layoutOption)||{},r.computedLayoutOption=u;var d=Math.PI/180;s&&s.setTextConfig({local:!1,position:null!=u.x||null!=u.y?null:l.attachedPos,rotation:null!=u.rotate?u.rotate*d:l.attachedRot,offset:[u.dx||0,u.dy||0]});var c=!1;if(null!=u.x?(a.x=m(u.x,e),a.setStyle("x",0),c=!0):(a.x=l.x,a.setStyle("x",l.style.x)),null!=u.y?(a.y=m(u.y,n),a.setStyle("y",0),c=!0):(a.y=l.y,a.setStyle("y",l.style.y)),u.labelLinePoints){var h=s.getTextGuideLine();h&&(h.setShape({points:u.labelLinePoints}),c=!1)}Rr(a).needsUpdateLabelLine=c,a.rotation=null!=u.rotate?u.rotate*d:l.rotation,a.scaleX=l.scaleX,a.scaleY=l.scaleY;for(var p=0;p<kr.length;p++){var g=kr[p];a.setStyle(g,null!=u[g]?u[g]:l.style[g])}if(u.draggable){if(a.draggable=!0,a.cursor="move",s){var y=r.seriesModel;if(null!=r.dataIndex)y=r.seriesModel.getData(r.dataType).getItemModel(r.dataIndex);a.on("drag",i(s,y.getModel("labelLine")))}}else a.off("drag"),a.cursor=l.cursor}},t.prototype.layout=function(t){var e=t.getWidth(),n=t.getHeight(),i=x(this._labelList),o=c(i,(function(t){return"shiftX"===t.layoutOption.moveOverlap})),r=c(i,(function(t){return"shiftY"===t.layoutOption.moveOverlap}));_(o,0,e),b(r,0,n);var a=c(i,(function(t){return t.layoutOption.hideOverlap}));S(a)},t.prototype.processLabelsOverall=function(){var t=this;a(this._chartViewList,(function(e){var n=e.__model,i=e.ignoreLabelLineUpdate,o=n.isAnimationEnabled();e.group.traverse((function(e){if(e.ignore&&!e.forceLabelAnimation)return!0;var r=!i,a=e.getTextContent();!r&&a&&(r=Rr(a).needsUpdateLabelLine),r&&t._updateLabelLine(e,n),o&&t._animateLabels(e,n)}))}))},t.prototype._updateLabelLine=function(t,e){var n=t.getTextContent(),i=v(t),o=i.dataIndex;if(n&&null!=o){var r=e.getData(i.dataType),a=r.getItemModel(o),s={},l=r.getItemVisual(o,"style");if(l){var u=r.getVisual("drawType");s.stroke=l[u]}var d=a.getModel("labelLine");w(t,M(a),s),I(t,d)}},t.prototype._animateLabels=function(t,e){var n=t.getTextContent(),i=t.getTextGuideLine();if(n&&(t.forceLabelAnimation||!n.ignore&&!n.invisible&&!t.disableLabelAnimation&&!A(t))){var o=(p=Rr(n)).oldLayout,r=v(t),a=r.dataIndex,s={x:n.x,y:n.y,rotation:n.rotation},l=e.getData(r.dataType);if(o){n.attr(o);var u=t.prevStates;u&&(L(u,"select")>=0&&n.attr(p.oldLayoutSelect),L(u,"emphasis")>=0&&n.attr(p.oldLayoutEmphasis)),P(n,s,e,a)}else if(n.attr(s),!T(n).valueAnimation){var d=C(n.style.opacity,1);n.style.opacity=0,D(n,{style:{opacity:d}},e,a)}if(p.oldLayout=s,n.states.select){var c=p.oldLayoutSelect={};Or(c,s,Er),Or(c,n.states.select,Er)}if(n.states.emphasis){var h=p.oldLayoutEmphasis={};Or(h,s,Er),Or(h,n.states.emphasis,Er)}k(n,a,l,e,e)}if(i&&!i.ignore&&!i.invisible){o=(p=Vr(i)).oldLayout;var p,g={points:i.shape.points};o?(i.attr({shape:o}),P(i,{shape:g},e)):(i.setShape(g),i.style.strokePercent=0,D(i,{style:{strokePercent:1}},e)),p.oldLayout=g}},t}(),Br=h();var Gr=Math.sin,Fr=Math.cos,Wr=Math.PI,Hr=2*Math.PI,Zr=180/Wr,Yr=function(){function t(){}return t.prototype.reset=function(t){this._start=!0,this._d=[],this._str="",this._p=Math.pow(10,t||4)},t.prototype.moveTo=function(t,e){this._add("M",t,e)},t.prototype.lineTo=function(t,e){this._add("L",t,e)},t.prototype.bezierCurveTo=function(t,e,n,i,o,r){this._add("C",t,e,n,i,o,r)},t.prototype.quadraticCurveTo=function(t,e,n,i){this._add("Q",t,e,n,i)},t.prototype.arc=function(t,e,n,i,o,r){this.ellipse(t,e,n,n,0,i,o,r)},t.prototype.ellipse=function(t,e,n,i,o,r,a,s){var l=a-r,u=!s,d=Math.abs(l),c=N(d-Hr)||(u?l>=Hr:-l>=Hr),h=l>0?l%Hr:l%Hr+Hr,p=!1;p=!!c||!N(d)&&h>=Wr==!!u;var g=t+n*Fr(r),f=e+i*Gr(r);this._start&&this._add("M",g,f);var y=Math.round(o*Zr);if(c){var v=1/this._p,m=(u?1:-1)*(Hr-v);this._add("A",n,i,y,1,+u,t+n*Fr(r+m),e+i*Gr(r+m)),v>.01&&this._add("A",n,i,y,0,+u,g,f)}else{var x=t+n*Fr(a),_=e+i*Gr(a);this._add("A",n,i,y,+p,+u,x,_)}},t.prototype.rect=function(t,e,n,i){this._add("M",t,e),this._add("l",n,0),this._add("l",0,i),this._add("l",-n,0),this._add("Z")},t.prototype.closePath=function(){this._d.length>0&&this._add("Z")},t.prototype._add=function(t,e,n,i,o,r,a,s,l){for(var u=[],d=this._p,c=1;c<arguments.length;c++){var h=arguments[c];if(isNaN(h))return void(this._invalid=!0);u.push(Math.round(h*d)/d)}this._d.push(t+u.join(" ")),this._start="Z"===t},t.prototype.generateStr=function(){this._str=this._invalid?"":this._d.join(""),this._d=[]},t.prototype.getStr=function(){return this._str},t}(),Xr="none",Ur=Math.round;var jr=["lineCap","miterLimit","lineJoin"],qr=d(jr,(function(t){return"stroke-"+t.toLowerCase()}));function Kr(t,e,n,i){var o=null==e.opacity?1:e.opacity;if(n instanceof R)t("opacity",o);else{if(function(t){var e=t.fill;return null!=e&&e!==Xr}(e)){var r=V(e.fill);t("fill",r.color);var a=null!=e.fillOpacity?e.fillOpacity*r.opacity*o:r.opacity*o;a<1&&t("fill-opacity",a)}else t("fill",Xr);if(function(t){var e=t.stroke;return null!=e&&e!==Xr}(e)){var s=V(e.stroke);t("stroke",s.color);var l=e.strokeNoScale?n.getLineScale():1,u=l?(e.lineWidth||0)/l:0,d=null!=e.strokeOpacity?e.strokeOpacity*s.opacity*o:s.opacity*o,c=e.strokeFirst;if(1!==u&&t("stroke-width",u),c&&t("paint-order",c?"stroke":"fill"),d<1&&t("stroke-opacity",d),e.lineDash){var h=O(n),p=h[0],g=h[1];p&&(g=Ur(g||0),t("stroke-dasharray",p.join(",")),(g||i)&&t("stroke-dashoffset",g))}for(var f=0;f<jr.length;f++){var y=jr[f];if(e[y]!==E[y]){var v=e[y]||E[y];v&&t(qr[f],v)}}}}}var Jr="http://www.w3.org/2000/svg",$r="http://www.w3.org/1999/xlink",Qr="ecmeta_";function ta(t){return document.createElementNS(Jr,t)}function ea(t,e,n,i,o){return{tag:t,attrs:n||{},children:i,text:o,key:e}}function na(t,e){var n=(e=e||{}).newline?"\n":"";return function t(e){var i=e.children,o=e.tag,r=e.attrs,a=e.text;return function(t,e){var n=[];if(e)for(var i in e){var o=e[i],r=i;!1!==o&&(!0!==o&&null!=o&&(r+='="'+o+'"'),n.push(r))}return"<"+t+" "+n.join(" ")+">"}(o,r)+("style"!==o?z(a):a||"")+(i?""+n+d(i,(function(e){return t(e)})).join(n)+n:"")+("</"+o+">")}(t)}function ia(t){return{zrId:t,shadowCache:{},patternCache:{},gradientCache:{},clipPathCache:{},defs:{},cssNodes:{},cssAnims:{},cssStyleCache:{},cssAnimIdx:0,shadowIdx:0,gradientIdx:0,patternIdx:0,clipPathIdx:0}}function oa(t,e,n,i){return ea("svg","root",{width:t,height:e,xmlns:Jr,"xmlns:xlink":$r,version:"1.1",baseProfile:"full",viewBox:!!i&&"0 0 "+t+" "+e},n)}var ra=0;function aa(){return ra++}var sa={cubicIn:"0.32,0,0.67,0",cubicOut:"0.33,1,0.68,1",cubicInOut:"0.65,0,0.35,1",quadraticIn:"0.11,0,0.5,0",quadraticOut:"0.5,1,0.89,1",quadraticInOut:"0.45,0,0.55,1",quarticIn:"0.5,0,0.75,0",quarticOut:"0.25,1,0.5,1",quarticInOut:"0.76,0,0.24,1",quinticIn:"0.64,0,0.78,0",quinticOut:"0.22,1,0.36,1",quinticInOut:"0.83,0,0.17,1",sinusoidalIn:"0.12,0,0.39,0",sinusoidalOut:"0.61,1,0.88,1",sinusoidalInOut:"0.37,0,0.63,1",exponentialIn:"0.7,0,0.84,0",exponentialOut:"0.16,1,0.3,1",exponentialInOut:"0.87,0,0.13,1",circularIn:"0.55,0,1,0.45",circularOut:"0,0.55,0.45,1",circularInOut:"0.85,0,0.15,1"},la="transform-origin";function ua(t,e,n){var i=H({},t.shape);H(i,e),t.buildPath(n,i);var o=new Yr;return o.reset(X(t)),n.rebuildPath(o,1),o.generateStr(),o.getStr()}function da(t,e){var n=e.originX,i=e.originY;(n||i)&&(t[la]=n+"px "+i+"px")}var ca={fill:"fill",opacity:"opacity",lineWidth:"stroke-width",lineDashOffset:"stroke-dashoffset"};function ha(t,e){var n=e.zrId+"-ani-"+e.cssAnimIdx++;return e.cssAnims[n]=t,n}function pa(t){return G(t)?sa[t]?"cubic-bezier("+sa[t]+")":F(t)?t:"":""}function ga(t,e,n,i){var o=t.animators,r=o.length,s=[];if(t instanceof B){var l=function(t,e,n){var i,o,r=t.shape.paths,s={};if(a(r,(function(t){var e=ia(n.zrId);e.animation=!0,ga(t,{},e,!0);var r=e.cssAnims,a=e.cssNodes,l=y(r),u=l.length;if(u){var d=r[o=l[u-1]];for(var c in d){var h=d[c];s[c]=s[c]||{d:""},s[c].d+=h.d||""}for(var p in a){var g=a[p].animation;g.indexOf(o)>=0&&(i=g)}}})),i){e.d=!1;var l=ha(s,n);return i.replace(o,l)}}(t,e,n);if(l)s.push(l);else if(!r)return}else if(!r)return;for(var u={},d=0;d<r;d++){var h=o[d],p=[h.getMaxTime()/1e3+"s"],g=pa(h.getClip().easing),f=h.getDelay();g?p.push(g):p.push("linear"),f&&p.push(f/1e3+"s"),h.getLoop()&&p.push("infinite");var v=p.join(" ");u[v]=u[v]||[v,[]],u[v][1].push(h)}function m(o){var r,a=o[1],s=a.length,l={},u={},d={},h="animation-timing-function";function p(t,e,n){for(var i=t.getTracks(),o=t.getMaxTime(),r=0;r<i.length;r++){var a=i[r];if(a.needsAnimate()){var s=a.keyframes,l=a.propName;if(n&&(l=n(l)),l)for(var u=0;u<s.length;u++){var d=s[u],c=Math.round(d.time/o*100)+"%",p=pa(d.easing),g=d.rawValue;(G(g)||Y(g))&&(e[c]=e[c]||{},e[c][l]=d.rawValue,p&&(e[c][h]=p))}}}}for(var g=0;g<s;g++){(I=(M=a[g]).targetName)?"shape"===I&&p(M,u):!i&&p(M,l)}for(var f in l){var v={};W(v,t),H(v,l[f]);var m=Z(v),x=l[f][h];d[f]=m?{transform:m}:{},da(d[f],v),x&&(d[f][h]=x)}var _=!0;for(var f in u){d[f]=d[f]||{};var b=!r;x=u[f][h];b&&(r=new U);var S=r.len();r.reset(),d[f].d=ua(t,u[f],r);var w=r.len();if(!b&&S!==w){_=!1;break}x&&(d[f][h]=x)}if(!_)for(var f in d)delete d[f].d;if(!i)for(g=0;g<s;g++){var M,I;"style"===(I=(M=a[g]).targetName)&&p(M,d,(function(t){return ca[t]}))}var A,T=y(d),C=!0;for(g=1;g<T.length;g++){var D=T[g-1],L=T[g];if(d[D][la]!==d[L][la]){C=!1;break}A=d[D][la]}if(C&&A){for(var f in d)d[f][la]&&delete d[f][la];e[la]=A}if(c(T,(function(t){return y(d[t]).length>0})).length)return ha(d,n)+" "+o[0]+" both"}for(var x in u){(l=m(u[x]))&&s.push(l)}if(s.length){var _=n.zrId+"-cls-"+aa();n.cssNodes["."+_]={animation:s.join(",")},e.class=_}}function fa(t,e,n,i){var o=JSON.stringify(t),r=n.cssStyleCache[o];r||(r=n.zrId+"-cls-"+aa(),n.cssStyleCache[o]=r,n.cssNodes["."+r+":hover"]=t),e.class=e.class?e.class+" "+r:r}var ya=Math.round;function va(t){return t&&G(t.src)}function ma(t){return t&&f(t.toDataURL)}function xa(t,e,n,i){Kr((function(o,r){var a="fill"===o||"stroke"===o;a&&pt(r)?La(e,t,o,i):a&&gt(r)?Pa(n,t,o,i):t[o]=r,a&&i.ssr&&"none"===r&&(t["pointer-events"]="visible")}),e,n,!1),function(t,e,n){var i=t.style;if(yt(i)){var o=vt(t),r=n.shadowCache,a=r[o];if(!a){var s=t.getGlobalScale(),l=s[0],u=s[1];if(!l||!u)return;var d=i.shadowOffsetX||0,c=i.shadowOffsetY||0,h=i.shadowBlur,p=V(i.shadowColor),g=p.opacity,f=p.color,y=h/2/l+" "+h/2/u;a=n.zrId+"-s"+n.shadowIdx++,n.defs[a]=ea("filter",a,{id:a,x:"-100%",y:"-100%",width:"300%",height:"300%"},[ea("feDropShadow","",{dx:d/l,dy:c/u,stdDeviation:y,"flood-color":f,"flood-opacity":g})]),r[o]=a}e.filter=J(a)}}(n,t,i)}function _a(t,e){var n=ft(e);n&&(n.each((function(e,n){null!=e&&(t[(Qr+n).toLowerCase()]=e+"")})),e.isSilent()&&(t[Qr+"silent"]="true"))}function ba(t){return N(t[0]-1)&&N(t[1])&&N(t[2])&&N(t[3]-1)}function Sa(t,e,n){if(e&&(!function(t){return N(t[4])&&N(t[5])}(e)||!ba(e))){var i=1e4;t.transform=ba(e)?"translate("+ya(e[4]*i)/i+" "+ya(e[5]*i)/i+")":ht(e)}}function wa(t,e,n){for(var i=t.points,o=[],r=0;r<i.length;r++)o.push(ya(i[r][0]*n)/n),o.push(ya(i[r][1]*n)/n);e.points=o.join(" ")}function Ma(t){return!t.smooth}var Ia,Aa,Ta={circle:[(Ia=["cx","cy","r"],Aa=d(Ia,(function(t){return"string"==typeof t?[t,t]:t})),function(t,e,n){for(var i=0;i<Aa.length;i++){var o=Aa[i],r=t[o[0]];null!=r&&(e[o[1]]=ya(r*n)/n)}})],polyline:[wa,Ma],polygon:[wa,Ma]};function Ca(t,e){var n=t.style,i=t.shape,o=Ta[t.type],r={},a=e.animation,s="path",l=t.style.strokePercent,u=e.compress&&X(t)||4;if(!o||e.willUpdate||o[1]&&!o[1](i)||a&&function(t){for(var e=t.animators,n=0;n<e.length;n++)if("shape"===e[n].targetName)return!0;return!1}(t)||l<1){var d=!t.path||t.shapeChanged();t.path||t.createPathProxy();var c=t.path;d&&(c.beginPath(),t.buildPath(c,t.shape),t.pathUpdated());var h=c.getVersion(),p=t,g=p.__svgPathBuilder;p.__svgPathVersion===h&&g&&l===p.__svgPathStrokePercent||(g||(g=p.__svgPathBuilder=new Yr),g.reset(u),c.rebuildPath(g,l),g.generateStr(),p.__svgPathVersion=h,p.__svgPathStrokePercent=l),r.d=g.getStr()}else{s=t.type;var f=Math.pow(10,u);o[0](i,r,f)}return Sa(r,t.transform),xa(r,n,t,e),_a(r,t),e.animation&&ga(t,r,e),e.emphasis&&function(t,e,n){if(!t.ignore)if(t.isSilent())fa(u={"pointer-events":"none"},e,n);else{var i=t.states.emphasis&&t.states.emphasis.style?t.states.emphasis.style:{},o=i.fill;if(!o){var r=t.style&&t.style.fill,a=t.states.select&&t.states.select.style&&t.states.select.style.fill,s=t.currentStates.indexOf("select")>=0&&a||r;s&&(o=j(s))}var l=i.lineWidth;l&&(l/=!i.strokeNoScale&&t.transform?t.transform[0]:1);var u={cursor:"pointer"};o&&(u.fill=o),i.stroke&&(u.stroke=i.stroke),l&&(u["stroke-width"]=l),fa(u,e,n)}}(t,r,e),ea(s,t.id+"",r)}function Da(t,e){return t instanceof q?Ca(t,e):t instanceof R?function(t,e){var n=t.style,i=n.image;if(i&&!G(i)&&(va(i)?i=i.src:ma(i)&&(i=i.toDataURL())),i){var o=n.x||0,r=n.y||0,a={href:i,width:n.width,height:n.height};return o&&(a.x=o),r&&(a.y=r),Sa(a,t.transform),xa(a,n,t,e),_a(a,t),e.animation&&ga(t,a,e),ea("image",t.id+"",a)}}(t,e):t instanceof K?function(t,e){var n=t.style,i=n.text;if(null!=i&&(i+=""),i&&!isNaN(n.x)&&!isNaN(n.y)){var o=n.font||$,r=n.x||0,a=Q(n.y||0,tt(o),n.textBaseline),s={"dominant-baseline":"central","text-anchor":et[n.textAlign]||n.textAlign};if(nt(n)){var l="",u=n.fontStyle,d=it(n.fontSize);if(!parseFloat(d))return;var c=n.fontFamily||ot,h=n.fontWeight;l+="font-size:"+d+";font-family:"+c+";",u&&"normal"!==u&&(l+="font-style:"+u+";"),h&&"normal"!==h&&(l+="font-weight:"+h+";"),s.style=l}else s.style="font: "+o;return i.match(/\s/)&&(s["xml:space"]="preserve"),r&&(s.x=r),a&&(s.y=a),Sa(s,t.transform),xa(s,n,t,e),_a(s,t),e.animation&&ga(t,s,e),ea("text",t.id+"",s,void 0,i)}}(t,e):void 0}function La(t,e,n,i){var o,r=t[n],a={gradientUnits:r.global?"userSpaceOnUse":"objectBoundingBox"};if(rt(r))o="linearGradient",a.x1=r.x,a.y1=r.y,a.x2=r.x2,a.y2=r.y2;else{if(!at(r))return;o="radialGradient",a.cx=C(r.x,.5),a.cy=C(r.y,.5),a.r=C(r.r,.5)}for(var s=r.colorStops,l=[],u=0,d=s.length;u<d;++u){var c=100*st(s[u].offset)+"%",h=s[u].color,p=V(h),g=p.color,f=p.opacity,y={offset:c};y["stop-color"]=g,f<1&&(y["stop-opacity"]=f),l.push(ea("stop",u+"",y))}var v=na(ea(o,"",a,l)),m=i.gradientCache,x=m[v];x||(x=i.zrId+"-g"+i.gradientIdx++,m[v]=x,a.id=x,i.defs[x]=ea(o,x,a,l)),e[n]=J(x)}function Pa(t,e,n,i){var o,r=t.style[n],a=t.getBoundingRect(),s={},l=r.repeat,u="no-repeat"===l,d="repeat-x"===l,c="repeat-y"===l;if(lt(r)){var h=r.imageWidth,p=r.imageHeight,g=void 0,f=r.image;if(G(f)?g=f:va(f)?g=f.src:ma(f)&&(g=f.toDataURL()),"undefined"==typeof Image){var y="Image width/height must been given explictly in svg-ssr renderer.";ut(h,y),ut(p,y)}else if(null==h||null==p){var v=function(t,e){if(t){var n=t.elm,i=h||e.width,o=p||e.height;"pattern"===t.tag&&(d?(o=1,i/=a.width):c&&(i=1,o/=a.height)),t.attrs.width=i,t.attrs.height=o,n&&(n.setAttribute("width",i),n.setAttribute("height",o))}},m=dt(g,null,t,(function(t){u||v(S,t),v(o,t)}));m&&m.width&&m.height&&(h=h||m.width,p=p||m.height)}o=ea("image","img",{href:g,width:h,height:p}),s.width=h,s.height=p}else r.svgElement&&(o=ct(r.svgElement),s.width=r.svgWidth,s.height=r.svgHeight);if(o){var x,_;u?x=_=1:d?(_=1,x=s.width/a.width):c?(x=1,_=s.height/a.height):s.patternUnits="userSpaceOnUse",null==x||isNaN(x)||(s.width=x),null==_||isNaN(_)||(s.height=_);var b=Z(r);b&&(s.patternTransform=b);var S=ea("pattern","",s,[o]),w=na(S),M=i.patternCache,I=M[w];I||(I=i.zrId+"-p"+i.patternIdx++,M[w]=I,s.id=I,S=i.defs[I]=ea("pattern",I,s,[o])),e[n]=J(I)}}function ka(t,e,n){var i=n.clipPathCache,o=n.defs,r=i[t.id];if(!r){var a={id:r=n.zrId+"-c"+n.clipPathIdx++};i[t.id]=r,o[r]=ea("clipPath",r,a,[Ca(t,n)])}e["clip-path"]=J(r)}function Na(t){return document.createTextNode(t)}function Ra(t,e,n){t.insertBefore(e,n)}function Va(t,e){t.removeChild(e)}function Oa(t,e){t.appendChild(e)}function Ea(t){return t.parentNode}function za(t){return t.nextSibling}function Ba(t,e){t.textContent=e}var Ga=ea("","");function Fa(t){return void 0===t}function Wa(t){return void 0!==t}function Ha(t,e,n){for(var i={},o=e;o<=n;++o){var r=t[o].key;void 0!==r&&(i[r]=o)}return i}function Za(t,e){var n=t.key===e.key;return t.tag===e.tag&&n}function Ya(t){var e,n=t.children,i=t.tag;if(Wa(i)){var o=t.elm=ta(i);if(ja(Ga,t),mt(n))for(e=0;e<n.length;++e){var r=n[e];null!=r&&Oa(o,Ya(r))}else Wa(t.text)&&!xt(t.text)&&Oa(o,Na(t.text))}else t.elm=Na(t.text);return t.elm}function Xa(t,e,n,i,o){for(;i<=o;++i){var r=n[i];null!=r&&Ra(t,Ya(r),e)}}function Ua(t,e,n,i){for(;n<=i;++n){var o=e[n];if(null!=o)if(Wa(o.tag))Va(Ea(o.elm),o.elm);else Va(t,o.elm)}}function ja(t,e){var n,i=e.elm,o=t&&t.attrs||{},r=e.attrs||{};if(o!==r){for(n in r){var a=r[n];o[n]!==a&&(!0===a?i.setAttribute(n,""):!1===a?i.removeAttribute(n):"style"===n?i.style.cssText=a:120!==n.charCodeAt(0)?i.setAttribute(n,a):"xmlns:xlink"===n||"xmlns"===n?i.setAttributeNS("http://www.w3.org/2000/xmlns/",n,a):58===n.charCodeAt(3)?i.setAttributeNS("http://www.w3.org/XML/1998/namespace",n,a):58===n.charCodeAt(5)?i.setAttributeNS($r,n,a):i.setAttribute(n,a))}for(n in o)n in r||i.removeAttribute(n)}}function qa(t,e){var n=e.elm=t.elm,i=t.children,o=e.children;t!==e&&(ja(t,e),Fa(e.text)?Wa(i)&&Wa(o)?i!==o&&function(t,e,n){for(var i,o,r,a=0,s=0,l=e.length-1,u=e[0],d=e[l],c=n.length-1,h=n[0],p=n[c];a<=l&&s<=c;)null==u?u=e[++a]:null==d?d=e[--l]:null==h?h=n[++s]:null==p?p=n[--c]:Za(u,h)?(qa(u,h),u=e[++a],h=n[++s]):Za(d,p)?(qa(d,p),d=e[--l],p=n[--c]):Za(u,p)?(qa(u,p),Ra(t,u.elm,za(d.elm)),u=e[++a],p=n[--c]):Za(d,h)?(qa(d,h),Ra(t,d.elm,u.elm),d=e[--l],h=n[++s]):(Fa(i)&&(i=Ha(e,a,l)),Fa(o=i[h.key])||(r=e[o]).tag!==h.tag?Ra(t,Ya(h),u.elm):(qa(r,h),e[o]=void 0,Ra(t,r.elm,u.elm)),h=n[++s]);(a<=l||s<=c)&&(a>l?Xa(t,null==n[c+1]?null:n[c+1].elm,n,s,c):Ua(t,e,a,l))}(n,i,o):Wa(o)?(Wa(t.text)&&Ba(n,""),Xa(n,null,o,0,o.length-1)):Wa(i)?Ua(n,i,0,i.length-1):Wa(t.text)&&Ba(n,""):t.text!==e.text&&(Wa(i)&&Ua(n,i,0,i.length-1),Ba(n,e.text)))}var Ka=0,Ja=function(){function t(t,e,n){if(this.type="svg",this.refreshHover=function(){},this.configLayer=function(){},this.storage=e,this._opts=n=H({},n),this.root=t,this._id="zr"+Ka++,this._oldVNode=oa(n.width,n.height),t&&!n.ssr){var i=this._viewport=document.createElement("div");i.style.cssText="position:relative;overflow:hidden";var o=this._svgDom=this._oldVNode.elm=ta("svg");ja(null,this._oldVNode),i.appendChild(o),t.appendChild(i)}this.resize(n.width,n.height)}return t.prototype.getType=function(){return this.type},t.prototype.getViewportRoot=function(){return this._viewport},t.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},t.prototype.getSvgDom=function(){return this._svgDom},t.prototype.refresh=function(){if(this.root){var t=this.renderToVNode({willUpdate:!0});t.attrs.style="position:absolute;left:0;top:0;user-select:none",function(t,e){if(Za(t,e))qa(t,e);else{var n=t.elm,i=Ea(n);Ya(e),null!==i&&(Ra(i,e.elm,za(n)),Ua(i,[t],0,0))}}(this._oldVNode,t),this._oldVNode=t}},t.prototype.renderOneToVNode=function(t){return Da(t,ia(this._id))},t.prototype.renderToVNode=function(t){t=t||{};var e=this.storage.getDisplayList(!0),n=this._width,i=this._height,o=ia(this._id);o.animation=t.animation,o.willUpdate=t.willUpdate,o.compress=t.compress,o.emphasis=t.emphasis,o.ssr=this._opts.ssr;var r=[],a=this._bgVNode=function(t,e,n,i){var o;if(n&&"none"!==n)if(o=ea("rect","bg",{width:t,height:e,x:"0",y:"0"}),pt(n))La({fill:n},o.attrs,"fill",i);else if(gt(n))Pa({style:{fill:n},dirty:St,getBoundingRect:function(){return{width:t,height:e}}},o.attrs,"fill",i);else{var r=V(n),a=r.color,s=r.opacity;o.attrs.fill=a,s<1&&(o.attrs["fill-opacity"]=s)}return o}(n,i,this._backgroundColor,o);a&&r.push(a);var s=t.compress?null:this._mainVNode=ea("g","main",{},[]);this._paintList(e,o,s?s.children:r),s&&r.push(s);var l=d(y(o.defs),(function(t){return o.defs[t]}));if(l.length&&r.push(ea("defs","defs",{},l)),t.animation){var u=function(t,e,n){var i=(n=n||{}).newline?"\n":"",o=" {"+i,r=i+"}",a=d(y(t),(function(e){return e+o+d(y(t[e]),(function(n){return n+":"+t[e][n]+";"})).join(i)+r})).join(i),s=d(y(e),(function(t){return"@keyframes "+t+o+d(y(e[t]),(function(n){return n+o+d(y(e[t][n]),(function(i){var o=e[t][n][i];return"d"===i&&(o='path("'+o+'")'),i+":"+o+";"})).join(i)+r})).join(i)+r})).join(i);return a||s?["<![CDATA[",a,s,"]]>"].join(i):""}(o.cssNodes,o.cssAnims,{newline:!0});if(u){var c=ea("style","stl",{},[],u);r.push(c)}}return oa(n,i,r,t.useViewBox)},t.prototype.renderToString=function(t){return t=t||{},na(this.renderToVNode({animation:C(t.cssAnimation,!0),emphasis:C(t.cssEmphasis,!0),willUpdate:!1,compress:!0,useViewBox:C(t.useViewBox,!0)}),{newline:!0})},t.prototype.setBackgroundColor=function(t){this._backgroundColor=t},t.prototype.getSvgRoot=function(){return this._mainVNode&&this._mainVNode.elm},t.prototype._paintList=function(t,e,n){for(var i,o,r=t.length,a=[],s=0,l=0,u=0;u<r;u++){var d=t[u];if(!d.invisible){var c=d.__clipPaths,h=c&&c.length||0,p=o&&o.length||0,g=void 0;for(g=Math.max(h-1,p-1);g>=0&&(!c||!o||c[g]!==o[g]);g--);for(var f=p-1;f>g;f--)i=a[--s-1];for(var y=g+1;y<h;y++){var v={};ka(c[y],v,e);var m=ea("g","clip-g-"+l++,v,[]);(i?i.children:n).push(m),a[s++]=m,i=m}o=c;var x=Da(d,e);x&&(i?i.children:n).push(x)}}},t.prototype.resize=function(t,e){var n=this._opts,i=this.root,o=this._viewport;if(null!=t&&(n.width=t),null!=e&&(n.height=e),i&&o&&(o.style.display="none",t=_t(i,0,n),e=_t(i,1,n),o.style.display=""),this._width!==t||this._height!==e){if(this._width=t,this._height=e,o){var r=o.style;r.width=t+"px",r.height=e+"px"}if(gt(this._backgroundColor))this.refresh();else{var a=this._svgDom;a&&(a.setAttribute("width",t),a.setAttribute("height",e));var s=this._bgVNode&&this._bgVNode.elm;s&&(s.setAttribute("width",t),s.setAttribute("height",e))}}},t.prototype.getWidth=function(){return this._width},t.prototype.getHeight=function(){return this._height},t.prototype.dispose=function(){this.root&&(this.root.innerHTML=""),this._svgDom=this._viewport=this.storage=this._oldVNode=this._bgVNode=this._mainVNode=null},t.prototype.clear=function(){this._svgDom&&(this._svgDom.innerHTML=null),this._oldVNode=null},t.prototype.toDataURL=function(t){var e=this.renderToString(),n="data:image/svg+xml;";return t?(e=bt(e))&&n+"base64,"+e:n+"charset=UTF-8,"+encodeURIComponent(e)},t}();var $a=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.hasSymbolVisual=!0,e}return e(n,t),n.prototype.getInitialData=function(t,e){return wt(null,this,{useEncodeDefaulter:!0})},n.prototype.getProgressive=function(){var t=this.option.progressive;return null==t?this.option.large?5e3:this.get("progressive"):t},n.prototype.getProgressiveThreshold=function(){var t=this.option.progressiveThreshold;return null==t?this.option.large?1e4:this.get("progressiveThreshold"):t},n.prototype.brushSelector=function(t,e,n){return n.point(e.getItemLayout(t))},n.prototype.getZLevelKey=function(){return this.getData().count()>this.getProgressiveThreshold()?this.id:""},n.type="series.scatter",n.dependencies=["grid","polar","geo","singleAxis","calendar"],n.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,symbolSize:10,large:!1,largeThreshold:2e3,itemStyle:{opacity:.8},emphasis:{scale:!0},clip:!0,select:{itemStyle:{borderColor:"#212121"}},universalTransition:{divideShape:"clone"}},n}(Mt),Qa=function(){return function(){}}(),ts=function(t){function n(e){var n=t.call(this,e)||this;return n._off=0,n.hoverDataIdx=-1,n}return e(n,t),n.prototype.getDefaultShape=function(){return new Qa},n.prototype.reset=function(){this.notClear=!1,this._off=0},n.prototype.buildPath=function(t,e){var n,i=e.points,o=e.size,r=this.symbolProxy,a=r.shape,s=t.getContext?t.getContext():t,l=s&&o[0]<4,u=this.softClipShape;if(l)this._ctx=s;else{for(this._ctx=null,n=this._off;n<i.length;){var d=i[n++],c=i[n++];isNaN(d)||isNaN(c)||(u&&!u.contain(d,c)||(a.x=d-o[0]/2,a.y=c-o[1]/2,a.width=o[0],a.height=o[1],r.buildPath(t,a,!0)))}this.incremental&&(this._off=n,this.notClear=!0)}},n.prototype.afterBrush=function(){var t,e=this.shape,n=e.points,i=e.size,o=this._ctx,r=this.softClipShape;if(o){for(t=this._off;t<n.length;){var a=n[t++],s=n[t++];isNaN(a)||isNaN(s)||(r&&!r.contain(a,s)||o.fillRect(a-i[0]/2,s-i[1]/2,i[0],i[1]))}this.incremental&&(this._off=t,this.notClear=!0)}},n.prototype.findDataIndex=function(t,e){for(var n=this.shape,i=n.points,o=n.size,r=Math.max(o[0],4),a=Math.max(o[1],4),s=i.length/2-1;s>=0;s--){var l=2*s,u=i[l]-r/2,d=i[l+1]-a/2;if(t>=u&&e>=d&&t<=u+r&&e<=d+a)return s}return-1},n.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect();return t=n[0],e=n[1],i.contain(t,e)?(this.hoverDataIdx=this.findDataIndex(t,e))>=0:(this.hoverDataIdx=-1,!1)},n.prototype.getBoundingRect=function(){var t=this._rect;if(!t){for(var e=this.shape,n=e.points,i=e.size,o=i[0],r=i[1],a=1/0,l=1/0,u=-1/0,d=-1/0,c=0;c<n.length;){var h=n[c++],p=n[c++];a=Math.min(h,a),u=Math.max(h,u),l=Math.min(p,l),d=Math.max(p,d)}t=this._rect=new s(a-o/2,l-r/2,u-a+o,d-l+r)}return t},n}(q),es=function(){function t(){this.group=new It}return t.prototype.updateData=function(t,e){this._clear();var n=this._create();n.setShape({points:t.getLayout("points")}),this._setCommon(n,t,e)},t.prototype.updateLayout=function(t){var e=t.getLayout("points");this.group.eachChild((function(t){if(null!=t.startIndex){var n=2*(t.endIndex-t.startIndex),i=4*t.startIndex*2;e=new Float32Array(e.buffer,i,n)}t.setShape("points",e),t.reset()}))},t.prototype.incrementalPrepareUpdate=function(t){this._clear()},t.prototype.incrementalUpdate=function(t,e,n){var i=this._newAdded[0],o=e.getLayout("points"),r=i&&i.shape.points;if(r&&r.length<2e4){var a=r.length,s=new Float32Array(a+o.length);s.set(r),s.set(o,a),i.endIndex=t.end,i.setShape({points:s})}else{this._newAdded=[];var l=this._create();l.startIndex=t.start,l.endIndex=t.end,l.incremental=!0,l.setShape({points:o}),this._setCommon(l,e,n)}},t.prototype.eachRendered=function(t){this._newAdded[0]&&t(this._newAdded[0])},t.prototype._create=function(){var t=new ts({cursor:"default"});return t.ignoreCoarsePointer=!0,this.group.add(t),this._newAdded.push(t),t},t.prototype._setCommon=function(t,e,n){var i=e.hostModel;n=n||{};var o=e.getVisual("symbolSize");t.setShape("size",o instanceof Array?o:[o,o]),t.softClipShape=n.clipShape||null,t.symbolProxy=At(e.getVisual("symbol"),0,0,0,0),t.setColor=t.symbolProxy.setColor;var r=t.shape.size[0]<4;t.useStyle(i.getModel("itemStyle").getItemStyle(r?["color","shadowBlur","shadowColor"]:["color"]));var a=e.getVisual("style"),s=a&&a.fill;s&&t.setColor(s);var l=v(t);l.seriesIndex=i.seriesIndex,t.on("mousemove",(function(e){l.dataIndex=null;var n=t.hoverDataIdx;n>=0&&(l.dataIndex=n+(t.startIndex||0))}))},t.prototype.remove=function(){this._clear()},t.prototype._clear=function(){this._newAdded=[],this.group.removeAll()},t}(),ns=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.render=function(t,e,n){var i=t.getData();this._updateSymbolDraw(i,t).updateData(i,{clipShape:this._getClipShape(t)}),this._finished=!0},n.prototype.incrementalPrepareRender=function(t,e,n){var i=t.getData();this._updateSymbolDraw(i,t).incrementalPrepareUpdate(i),this._finished=!1},n.prototype.incrementalRender=function(t,e,n){this._symbolDraw.incrementalUpdate(t,e.getData(),{clipShape:this._getClipShape(e)}),this._finished=t.end===e.getData().count()},n.prototype.updateTransform=function(t,e,n){var i=t.getData();if(this.group.dirty(),!this._finished||i.count()>1e4)return{update:!0};var o=Tt("").reset(t,e,n);o.progress&&o.progress({start:0,end:i.count(),count:i.count()},i),this._symbolDraw.updateLayout(i)},n.prototype.eachRendered=function(t){this._symbolDraw&&this._symbolDraw.eachRendered(t)},n.prototype._getClipShape=function(t){if(t.get("clip",!0)){var e=t.coordinateSystem;return e&&e.getArea&&e.getArea(.1)}},n.prototype._updateSymbolDraw=function(t,e){var n=this._symbolDraw,i=e.pipelineContext.large;return n&&i===this._isLargeDraw||(n&&n.remove(),n=this._symbolDraw=i?new es:new Ct,this._isLargeDraw=i,this.group.removeAll()),this.group.add(n.group),n},n.prototype.remove=function(t,e){this._symbolDraw&&this._symbolDraw.remove(!0),this._symbolDraw=null},n.prototype.dispose=function(){},n.type="scatter",n}(Dt);function is(t){t.eachSeriesByType("radar",(function(t){var e=t.getData(),n=[],i=t.coordinateSystem;if(i){var o=i.getIndicatorAxes();a(o,(function(t,r){e.each(e.mapDimension(o[r].dim),(function(t,e){n[e]=n[e]||[];var o=i.dataToPoint(t,r);n[e][r]=os(o)?o:rs(i)}))})),e.each((function(t){var o=kt(n[t],(function(t){return os(t)}))||rs(i);n[t].push(o.slice()),e.setItemLayout(t,n[t])}))}}))}function os(t){return!isNaN(t[0])&&!isNaN(t[1])}function rs(t){return[t.cx,t.cy]}function as(t){var e=t.polar;if(e){mt(e)||(e=[e]);var n=[];a(e,(function(e,i){e.indicator?(e.type&&!e.shape&&(e.shape=e.type),t.radar=t.radar||[],mt(t.radar)||(t.radar=[t.radar]),t.radar.push(e)):n.push(e)})),t.polar=n}a(t.series,(function(t){t&&"radar"===t.type&&t.polarIndex&&(t.radarIndex=t.polarIndex)}))}var ss=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.render=function(t,e,n){var i=t.coordinateSystem,o=this.group,r=t.getData(),s=this._data;function l(t,e){var n=t.getItemVisual(e,"symbol")||"circle";if("none"!==n){var i=Wt(t.getItemVisual(e,"symbolSize")),o=At(n,-1,-1,2,2),r=t.getItemVisual(e,"symbolRotate")||0;return o.attr({style:{strokeNoScale:!0},z2:100,scaleX:i[0]/2,scaleY:i[1]/2,rotation:r*Math.PI/180||0}),o}}function u(e,n,i,o,r,a){i.removeAll();for(var s=0;s<n.length-1;s++){var u=l(o,r);u&&(u.__dimIdx=s,e[s]?(u.setPosition(e[s]),Ft[a?"initProps":"updateProps"](u,{x:n[s][0],y:n[s][1]},t,r)):u.setPosition(n[s]),i.add(u))}}function c(t){return d(t,(function(t){return[i.cx,i.cy]}))}r.diff(s).add((function(e){var n=r.getItemLayout(e);if(n){var i=new Nt,o=new Rt,a={shape:{points:n}};i.shape.points=c(n),o.shape.points=c(n),D(i,a,t,e),D(o,a,t,e);var s=new It,l=new It;s.add(o),s.add(i),s.add(l),u(o.shape.points,n,l,r,e,!0),r.setItemGraphicEl(e,s)}})).update((function(e,n){var i=s.getItemGraphicEl(n),o=i.childAt(0),a=i.childAt(1),l=i.childAt(2),d={shape:{points:r.getItemLayout(e)}};d.shape.points&&(u(o.shape.points,d.shape.points,l,r,e,!1),Vt(a),Vt(o),P(o,d,t),P(a,d,t),r.setItemGraphicEl(e,i))})).remove((function(t){o.remove(s.getItemGraphicEl(t))})).execute(),r.eachItemGraphicEl((function(t,e){var n=r.getItemModel(e),i=t.childAt(0),s=t.childAt(1),l=t.childAt(2),u=r.getItemVisual(e,"style"),d=u.fill;o.add(t),i.useStyle(Ot(n.getModel("lineStyle").getLineStyle(),{fill:"none",stroke:d})),Et(i,n,"lineStyle"),Et(s,n,"areaStyle");var c=n.getModel("areaStyle"),h=c.isEmpty()&&c.parentModel.isEmpty();s.ignore=h,a(["emphasis","select","blur"],(function(t){var e=n.getModel([t,"areaStyle"]),i=e.isEmpty()&&e.parentModel.isEmpty();s.ensureState(t).ignore=i&&h})),s.useStyle(Ot(c.getAreaStyle(),{fill:d,opacity:.7,decal:u.decal}));var p=n.getModel("emphasis"),g=p.getModel("itemStyle").getItemStyle();l.eachChild((function(t){if(t instanceof R){var i=t.style;t.useStyle(H({image:i.image,x:i.x,y:i.y,width:i.width,height:i.height},u))}else t.useStyle(u),t.setColor(d),t.style.strokeNoScale=!0;t.ensureState("emphasis").style=ct(g);var o=r.getStore().get(r.getDimensionIndex(t.__dimIdx),e);(null==o||isNaN(o))&&(o=""),zt(t,Bt(n),{labelFetcher:r.hostModel,labelDataIndex:e,labelDimIndex:t.__dimIdx,defaultText:o,inheritColor:d,defaultOpacity:u.opacity})})),Gt(t,p.get("focus"),p.get("blurScope"),p.get("disabled"))})),this._data=r},n.prototype.remove=function(){this.group.removeAll(),this._data=null},n.type="radar",n}(Dt),ls=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.hasSymbolVisual=!0,e}return e(n,t),n.prototype.init=function(e){t.prototype.init.apply(this,arguments),this.legendVisualProvider=new Ht(Zt(this.getData,this),Zt(this.getRawData,this))},n.prototype.getInitialData=function(t,e){return Yt(this,{generateCoord:"indicator_",generateCoordCount:1/0})},n.prototype.formatTooltip=function(t,e,n){var i=this.getData(),o=this.coordinateSystem.getIndicatorAxes(),r=this.getData().getName(t),a=""===r?this.name:r,s=Xt(this,t);return Ut("section",{header:a,sortBlocks:!0,blocks:d(o,(function(e){var n=i.get(i.mapDimension(e.dim),t);return Ut("nameValue",{markerType:"subItem",markerColor:s,name:e.name,value:n,sortParam:n})}))})},n.prototype.getTooltipPosition=function(t){if(null!=t)for(var e=this.getData(),n=this.coordinateSystem,i=e.getValues(d(n.dimensions,(function(t){return e.mapDimension(t)})),t),o=0,r=i.length;o<r;o++)if(!isNaN(i[o])){var a=n.getIndicatorAxes();return n.coordToPoint(a[o].dataToCoord(i[o]),o)}},n.type="series.radar",n.dependencies=["radar"],n.defaultOption={z:2,colorBy:"data",coordinateSystem:"radar",legendHoverLink:!0,radarIndex:0,lineStyle:{width:2,type:"solid",join:"round"},label:{position:"top"},symbolSize:8},n}(Mt),us=$t.value;function ds(t,e){return Ot({show:e},t)}var cs=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.optionUpdated=function(){var t=this.get("boundaryGap"),e=this.get("splitNumber"),n=this.get("scale"),i=this.get("axisLine"),o=this.get("axisTick"),r=this.get("axisLabel"),a=this.get("axisName"),s=this.get(["axisName","show"]),l=this.get(["axisName","formatter"]),u=this.get("axisNameGap"),c=this.get("triggerEvent"),h=d(this.get("indicator")||[],(function(d){null!=d.max&&d.max>0&&!d.min?d.min=0:null!=d.min&&d.min<0&&!d.max&&(d.max=0);var h=a;null!=d.color&&(h=Ot({color:d.color},a));var p=jt(ct(d),{boundaryGap:t,splitNumber:e,scale:n,axisLine:i,axisTick:o,axisLabel:r,name:d.text,showName:s,nameLocation:"end",nameGap:u,nameTextStyle:h,triggerEvent:c},!1);if(G(l)){var g=p.name;p.name=l.replace("{value}",null!=g?g:"")}else f(l)&&(p.name=l(p.name,p));var y=new qt(p,null,this.ecModel);return Kt(y,Jt.prototype),y.mainType="radar",y.componentIndex=this.componentIndex,y}),this);this._indicatorModels=h},n.prototype.getIndicatorModels=function(){return this._indicatorModels},n.type="radar",n.defaultOption={z:0,center:["50%","50%"],radius:"75%",startAngle:90,axisName:{show:!0},boundaryGap:[0,0],splitNumber:5,axisNameGap:15,scale:!1,shape:"polygon",axisLine:jt({lineStyle:{color:"#bbb"}},us.axisLine),axisLabel:ds(us.axisLabel,!1),axisTick:ds(us.axisTick,!1),splitLine:ds(us.splitLine,!0),splitArea:ds(us.splitArea,!0),indicator:[]},n}(Qt),hs=["axisLine","axisTickLabel","axisName"],ps=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.render=function(t,e,n){this.group.removeAll(),this._buildAxes(t),this._buildSplitLineAndArea(t)},n.prototype._buildAxes=function(t){var e=t.coordinateSystem,n=e.getIndicatorAxes(),i=d(n,(function(t){var n=t.model.get("showName")?t.name:"";return new te(t.model,{axisName:n,position:[e.cx,e.cy],rotation:t.angle,labelDirection:-1,tickDirection:-1,nameDirection:1})}));a(i,(function(t){a(hs,t.add,t),this.group.add(t.getGroup())}),this)},n.prototype._buildSplitLineAndArea=function(t){var e=t.coordinateSystem,n=e.getIndicatorAxes();if(n.length){var i=t.get("shape"),o=t.getModel("splitLine"),r=t.getModel("splitArea"),s=o.getModel("lineStyle"),l=r.getModel("areaStyle"),u=o.get("show"),c=r.get("show"),h=s.get("color"),p=l.get("color"),g=mt(h)?h:[h],f=mt(p)?p:[p],y=[],v=[];if("circle"===i)for(var m=n[0].getTicksCoords(),x=e.cx,_=e.cy,b=0;b<m.length;b++){if(u)y[D(y,g,b)].push(new ee({shape:{cx:x,cy:_,r:m[b].coord}}));if(c&&b<m.length-1)v[D(v,f,b)].push(new ne({shape:{cx:x,cy:_,r0:m[b].coord,r:m[b+1].coord}}))}else{var S,w=d(n,(function(t,n){var i=t.getTicksCoords();return S=null==S?i.length-1:Math.min(i.length-1,S),d(i,(function(t){return e.coordToPoint(t.coord,n)}))})),M=[];for(b=0;b<=S;b++){for(var I=[],A=0;A<n.length;A++)I.push(w[A][b]);if(I[0]&&I.push(I[0].slice()),u)y[D(y,g,b)].push(new Rt({shape:{points:I}}));if(c&&M)v[D(v,f,b-1)].push(new Nt({shape:{points:I.concat(M)}}));M=I.slice().reverse()}}var T=s.getLineStyle(),C=l.getAreaStyle();a(v,(function(t,e){this.group.add(ie(t,{style:Ot({stroke:"none",fill:f[e%f.length]},C),silent:!0}))}),this),a(y,(function(t,e){this.group.add(ie(t,{style:Ot({fill:"none",stroke:g[e%g.length]},T),silent:!0}))}),this)}function D(t,e,n){var i=n%e.length;return t[i]=t[i]||[],i}},n.type="radar",n}(oe),gs=function(t){function n(e,n,i){var o=t.call(this,e,n,i)||this;return o.type="value",o.angle=0,o.name="",o}return e(n,t),n}(re),fs=function(){function t(t,e,n){this.dimensions=[],this._model=t,this._indicatorAxes=d(t.getIndicatorModels(),(function(t,e){var n="indicator_"+e,i=new gs(n,new ae);return i.name=t.get("name"),i.model=t,t.axis=i,this.dimensions.push(n),i}),this),this.resize(t,n)}return t.prototype.getIndicatorAxes=function(){return this._indicatorAxes},t.prototype.dataToPoint=function(t,e){var n=this._indicatorAxes[e];return this.coordToPoint(n.dataToCoord(t),e)},t.prototype.coordToPoint=function(t,e){var n=this._indicatorAxes[e].angle;return[this.cx+t*Math.cos(n),this.cy-t*Math.sin(n)]},t.prototype.pointToData=function(t){var e=t[0]-this.cx,n=t[1]-this.cy,i=Math.sqrt(e*e+n*n);e/=i,n/=i;for(var o,r=Math.atan2(-n,e),a=1/0,s=-1,l=0;l<this._indicatorAxes.length;l++){var u=this._indicatorAxes[l],d=Math.abs(r-u.angle);d<a&&(o=u,s=l,a=d)}return[s,+(o&&o.coordToData(i))]},t.prototype.resize=function(t,e){var n=t.get("center"),i=e.getWidth(),o=e.getHeight(),r=Math.min(i,o)/2;this.cx=m(n[0],i),this.cy=m(n[1],o),this.startAngle=t.get("startAngle")*Math.PI/180;var s=t.get("radius");(G(s)||Y(s))&&(s=[0,s]),this.r0=m(s[0],r),this.r=m(s[1],r),a(this._indicatorAxes,(function(t,e){t.setExtent(this.r0,this.r);var n=this.startAngle+e*Math.PI*2/this._indicatorAxes.length;n=Math.atan2(Math.sin(n),Math.cos(n)),t.angle=n}),this)},t.prototype.update=function(t,e){var n=this._indicatorAxes,i=this._model;a(n,(function(t){t.scale.setExtent(1/0,-1/0)})),t.eachSeriesByType("radar",(function(e,o){if("radar"===e.get("coordinateSystem")&&t.getComponent("radar",e.get("radarIndex"))===i){var r=e.getData();a(n,(function(t){t.scale.unionExtentFromData(r,r.mapDimension(t.dim))}))}}),this);var o=i.get("splitNumber"),r=new ae;r.setExtent(0,o),r.setInterval(1),a(n,(function(t,e){se(t.scale,t.model,r)}))},t.prototype.convertToPixel=function(t,e,n){return null},t.prototype.convertFromPixel=function(t,e,n){return null},t.prototype.containPoint=function(t){return!1},t.create=function(e,n){var i=[];return e.eachComponent("radar",(function(o){var r=new t(o,e,n);i.push(r),o.coordinateSystem=r})),e.eachSeriesByType("radar",(function(t){"radar"===t.get("coordinateSystem")&&(t.coordinateSystem=i[t.get("radarIndex")||0])})),i},t.dimensions=[],t}();function ys(t){t.registerCoordinateSystem("radar",fs),t.registerComponentModel(cs),t.registerComponentView(ps),t.registerVisual({seriesType:"radar",reset:function(t){var e=t.getData();e.each((function(t){e.setItemVisual(t,"legendIcon","roundRect")})),e.setVisual("legendIcon","roundRect")}})}var vs="\0_ec_interaction_mutex";function ms(t,e){return!!xs(t)[e]}function xs(t){return t[vs]||(t[vs]={})}ue({type:"takeGlobalCursor",event:"globalCursorTaken",update:"update"},St);var _s=function(t){function n(e){var n=t.call(this)||this;n._zr=e;var i=Zt(n._mousedownHandler,n),o=Zt(n._mousemoveHandler,n),r=Zt(n._mouseupHandler,n),a=Zt(n._mousewheelHandler,n),s=Zt(n._pinchHandler,n);return n.enable=function(t,n){this.disable(),this._opt=Ot(ct(n)||{},{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}),null==t&&(t=!0),!0!==t&&"move"!==t&&"pan"!==t||(e.on("mousedown",i),e.on("mousemove",o),e.on("mouseup",r)),!0!==t&&"scale"!==t&&"zoom"!==t||(e.on("mousewheel",a),e.on("pinch",s))},n.disable=function(){e.off("mousedown",i),e.off("mousemove",o),e.off("mouseup",r),e.off("mousewheel",a),e.off("pinch",s)},n}return e(n,t),n.prototype.isDragging=function(){return this._dragging},n.prototype.isPinching=function(){return this._pinching},n.prototype.setPointerChecker=function(t){this.pointerChecker=t},n.prototype.dispose=function(){this.disable()},n.prototype._mousedownHandler=function(t){if(!de(t)){for(var e=t.target;e;){if(e.draggable)return;e=e.__hostTarget||e.parent}var n=t.offsetX,i=t.offsetY;this.pointerChecker&&this.pointerChecker(t,n,i)&&(this._x=n,this._y=i,this._dragging=!0)}},n.prototype._mousemoveHandler=function(t){if(this._dragging&&ws("moveOnMouseMove",t,this._opt)&&"pinch"!==t.gestureEvent&&!ms(this._zr,"globalPan")){var e=t.offsetX,n=t.offsetY,i=this._x,o=this._y,r=e-i,a=n-o;this._x=e,this._y=n,this._opt.preventDefaultMouseMove&&ce(t.event),Ss(this,"pan","moveOnMouseMove",t,{dx:r,dy:a,oldX:i,oldY:o,newX:e,newY:n,isAvailableBehavior:null})}},n.prototype._mouseupHandler=function(t){de(t)||(this._dragging=!1)},n.prototype._mousewheelHandler=function(t){var e=ws("zoomOnMouseWheel",t,this._opt),n=ws("moveOnMouseWheel",t,this._opt),i=t.wheelDelta,o=Math.abs(i),r=t.offsetX,a=t.offsetY;if(0!==i&&(e||n)){if(e){var s=o>3?1.4:o>1?1.2:1.1;bs(this,"zoom","zoomOnMouseWheel",t,{scale:i>0?s:1/s,originX:r,originY:a,isAvailableBehavior:null})}if(n){var l=Math.abs(i);bs(this,"scrollMove","moveOnMouseWheel",t,{scrollDelta:(i>0?1:-1)*(l>3?.4:l>1?.15:.05),originX:r,originY:a,isAvailableBehavior:null})}}},n.prototype._pinchHandler=function(t){ms(this._zr,"globalPan")||bs(this,"zoom",null,t,{scale:t.pinchScale>1?1.1:1/1.1,originX:t.pinchX,originY:t.pinchY,isAvailableBehavior:null})},n}(he);function bs(t,e,n,i,o){t.pointerChecker&&t.pointerChecker(i,o.originX,o.originY)&&(ce(i.event),Ss(t,e,n,i,o))}function Ss(t,e,n,i,o){o.isAvailableBehavior=Zt(ws,null,n,i),t.trigger(e,o)}function ws(t,e,n){var i=n[t];return!t||i&&(!G(i)||e.event[i+"Key"])}function Ms(t,e,n){var i=t.target;i.x+=e,i.y+=n,i.dirty()}function Is(t,e,n,i){var o=t.target,r=t.zoomLimit,a=t.zoom=t.zoom||1;if(a*=e,r){var s=r.min||0,l=r.max||1/0;a=Math.max(Math.min(l,a),s)}var u=a/t.zoom;t.zoom=a,o.x-=(n-o.x)*(u-1),o.y-=(i-o.y)*(u-1),o.scaleX*=u,o.scaleY*=u,o.dirty()}var As,Ts={axisPointer:1,tooltip:1,brush:1};function Cs(t,e,n){var i=e.getComponentByElement(t.topTarget),o=i&&i.coordinateSystem;return i&&i!==n&&!Ts.hasOwnProperty(i.mainType)&&o&&o.model!==n}function Ds(t){G(t)&&(t=(new DOMParser).parseFromString(t,"text/xml"));var e=t;for(9===e.nodeType&&(e=e.firstChild);"svg"!==e.nodeName.toLowerCase()||1!==e.nodeType;)e=e.nextSibling;return e}var Ls={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-anchor":"textAlign",visibility:"visibility",display:"display"},Ps=y(Ls),ks={"alignment-baseline":"textBaseline","stop-color":"stopColor"},Ns=y(ks),Rs=function(){function t(){this._defs={},this._root=null}return t.prototype.parse=function(t,e){e=e||{};var n=Ds(t);this._defsUsePending=[];var i=new It;this._root=i;var o=[],r=n.getAttribute("viewBox")||"",a=parseFloat(n.getAttribute("width")||e.width),s=parseFloat(n.getAttribute("height")||e.height);isNaN(a)&&(a=null),isNaN(s)&&(s=null),Gs(n,i,null,!0,!1);for(var l,u,d=n.firstChild;d;)this._parseNode(d,i,o,null,!1,!1),d=d.nextSibling;if(function(t,e){for(var n=0;n<e.length;n++){var i=e[n];i[0].style[i[1]]=t[i[2]]}}(this._defs,this._defsUsePending),this._defsUsePending=[],r){var c=Zs(r);c.length>=4&&(l={x:parseFloat(c[0]||0),y:parseFloat(c[1]||0),width:parseFloat(c[2]),height:parseFloat(c[3])})}if(l&&null!=a&&null!=s&&(u=qs(l,{x:0,y:0,width:a,height:s}),!e.ignoreViewBox)){var h=i;(i=new It).add(h),h.scaleX=h.scaleY=u.scale,h.x=u.x,h.y=u.y}return e.ignoreRootClip||null==a||null==s||i.setClipPath(new pe({shape:{x:0,y:0,width:a,height:s}})),{root:i,width:a,height:s,viewBoxRect:l,viewBoxTransform:u,named:o}},t.prototype._parseNode=function(t,e,n,i,o,r){var a,s=t.nodeName.toLowerCase(),l=i;if("defs"===s&&(o=!0),"text"===s&&(r=!0),"defs"===s||"switch"===s)a=e;else{if(!o){var u=As[s];if(u&&ge(As,s)){a=u.call(this,t,e);var d=t.getAttribute("name");if(d){var c={name:d,namedFrom:null,svgNodeTagLower:s,el:a};n.push(c),"g"===s&&(l=c)}else i&&n.push({name:i.name,namedFrom:i,svgNodeTagLower:s,el:a});e.add(a)}}var h=Vs[s];if(h&&ge(Vs,s)){var p=h.call(this,t),g=t.getAttribute("id");g&&(this._defs[g]=p)}}if(a&&a.isGroup)for(var f=t.firstChild;f;)1===f.nodeType?this._parseNode(f,a,n,l,o,r):3===f.nodeType&&r&&this._parseText(f,a),f=f.nextSibling},t.prototype._parseText=function(t,e){var n=new K({style:{text:t.textContent},silent:!0,x:this._textX||0,y:this._textY||0});zs(e,n),Gs(t,n,this._defsUsePending,!1,!1),function(t,e){var n=e.__selfStyle;if(n){var i=n.textBaseline,o=i;i&&"auto"!==i?"baseline"===i?o="alphabetic":"before-edge"===i||"text-before-edge"===i?o="top":"after-edge"===i||"text-after-edge"===i?o="bottom":"central"!==i&&"mathematical"!==i||(o="middle"):o="alphabetic",t.style.textBaseline=o}var r=e.__inheritedStyle;if(r){var a=r.textAlign,s=a;a&&("middle"===a&&(s="center"),t.style.textAlign=s)}}(n,e);var i=n.style,o=i.fontSize;o&&o<9&&(i.fontSize=9,n.scaleX*=o/9,n.scaleY*=o/9);var r=(i.fontSize||i.fontFamily)&&[i.fontStyle,i.fontWeight,(i.fontSize||12)+"px",i.fontFamily||"sans-serif"].join(" ");i.font=r;var a=n.getBoundingRect();return this._textX+=a.width,e.add(n),n},t.internalField=void(As={g:function(t,e){var n=new It;return zs(e,n),Gs(t,n,this._defsUsePending,!1,!1),n},rect:function(t,e){var n=new pe;return zs(e,n),Gs(t,n,this._defsUsePending,!1,!1),n.setShape({x:parseFloat(t.getAttribute("x")||"0"),y:parseFloat(t.getAttribute("y")||"0"),width:parseFloat(t.getAttribute("width")||"0"),height:parseFloat(t.getAttribute("height")||"0")}),n.silent=!0,n},circle:function(t,e){var n=new ee;return zs(e,n),Gs(t,n,this._defsUsePending,!1,!1),n.setShape({cx:parseFloat(t.getAttribute("cx")||"0"),cy:parseFloat(t.getAttribute("cy")||"0"),r:parseFloat(t.getAttribute("r")||"0")}),n.silent=!0,n},line:function(t,e){var n=new ve;return zs(e,n),Gs(t,n,this._defsUsePending,!1,!1),n.setShape({x1:parseFloat(t.getAttribute("x1")||"0"),y1:parseFloat(t.getAttribute("y1")||"0"),x2:parseFloat(t.getAttribute("x2")||"0"),y2:parseFloat(t.getAttribute("y2")||"0")}),n.silent=!0,n},ellipse:function(t,e){var n=new ye;return zs(e,n),Gs(t,n,this._defsUsePending,!1,!1),n.setShape({cx:parseFloat(t.getAttribute("cx")||"0"),cy:parseFloat(t.getAttribute("cy")||"0"),rx:parseFloat(t.getAttribute("rx")||"0"),ry:parseFloat(t.getAttribute("ry")||"0")}),n.silent=!0,n},polygon:function(t,e){var n,i=t.getAttribute("points");i&&(n=Bs(i));var o=new Nt({shape:{points:n||[]},silent:!0});return zs(e,o),Gs(t,o,this._defsUsePending,!1,!1),o},polyline:function(t,e){var n,i=t.getAttribute("points");i&&(n=Bs(i));var o=new Rt({shape:{points:n||[]},silent:!0});return zs(e,o),Gs(t,o,this._defsUsePending,!1,!1),o},image:function(t,e){var n=new R;return zs(e,n),Gs(t,n,this._defsUsePending,!1,!1),n.setStyle({image:t.getAttribute("xlink:href")||t.getAttribute("href"),x:+t.getAttribute("x"),y:+t.getAttribute("y"),width:+t.getAttribute("width"),height:+t.getAttribute("height")}),n.silent=!0,n},text:function(t,e){var n=t.getAttribute("x")||"0",i=t.getAttribute("y")||"0",o=t.getAttribute("dx")||"0",r=t.getAttribute("dy")||"0";this._textX=parseFloat(n)+parseFloat(o),this._textY=parseFloat(i)+parseFloat(r);var a=new It;return zs(e,a),Gs(t,a,this._defsUsePending,!1,!0),a},tspan:function(t,e){var n=t.getAttribute("x"),i=t.getAttribute("y");null!=n&&(this._textX=parseFloat(n)),null!=i&&(this._textY=parseFloat(i));var o=t.getAttribute("dx")||"0",r=t.getAttribute("dy")||"0",a=new It;return zs(e,a),Gs(t,a,this._defsUsePending,!1,!0),this._textX+=parseFloat(o),this._textY+=parseFloat(r),a},path:function(t,e){var n=t.getAttribute("d")||"",i=fe(n);return zs(e,i),Gs(t,i,this._defsUsePending,!1,!1),i.silent=!0,i}}),t}(),Vs={lineargradient:function(t){var e=parseInt(t.getAttribute("x1")||"0",10),n=parseInt(t.getAttribute("y1")||"0",10),i=parseInt(t.getAttribute("x2")||"10",10),o=parseInt(t.getAttribute("y2")||"0",10),r=new xe(e,n,i,o);return Os(t,r),Es(t,r),r},radialgradient:function(t){var e=parseInt(t.getAttribute("cx")||"0",10),n=parseInt(t.getAttribute("cy")||"0",10),i=parseInt(t.getAttribute("r")||"0",10),o=new me(e,n,i);return Os(t,o),Es(t,o),o}};function Os(t,e){"userSpaceOnUse"===t.getAttribute("gradientUnits")&&(e.global=!0)}function Es(t,e){for(var n=t.firstChild;n;){if(1===n.nodeType&&"stop"===n.nodeName.toLocaleLowerCase()){var i=n.getAttribute("offset"),o=void 0;o=i&&i.indexOf("%")>0?parseInt(i,10)/100:i?parseFloat(i):0;var r={};js(n,r,r);var a=r.stopColor||n.getAttribute("stop-color")||"#000000";e.colorStops.push({offset:o,color:a})}n=n.nextSibling}}function zs(t,e){t&&t.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),Ot(e.__inheritedStyle,t.__inheritedStyle))}function Bs(t){for(var e=Zs(t),n=[],i=0;i<e.length;i+=2){var o=parseFloat(e[i]),r=parseFloat(e[i+1]);n.push([o,r])}return n}function Gs(t,e,n,o,r){var s=e,l=s.__inheritedStyle=s.__inheritedStyle||{},u={};1===t.nodeType&&(function(t,e){var n=t.getAttribute("transform");if(n){n=n.replace(/,/g," ");var o=[],r=null;n.replace(Ys,(function(t,e,n){return o.push(e,n),""}));for(var a=o.length-1;a>0;a-=2){var s=o[a],l=o[a-1],u=Zs(s);switch(r=r||Me(),l){case"translate":we(r,r,[parseFloat(u[0]),parseFloat(u[1]||"0")]);break;case"scale":Se(r,r,[parseFloat(u[0]),parseFloat(u[1]||u[0])]);break;case"rotate":be(r,r,-parseFloat(u[0])*Xs,[parseFloat(u[1]||"0"),parseFloat(u[2]||"0")]);break;case"skewX":var d=Math.tan(parseFloat(u[0])*Xs);i(r,[1,0,d,1,0,0],r);break;case"skewY":var c=Math.tan(parseFloat(u[0])*Xs);i(r,[1,c,0,1,0,0],r);break;case"matrix":r[0]=parseFloat(u[0]),r[1]=parseFloat(u[1]),r[2]=parseFloat(u[2]),r[3]=parseFloat(u[3]),r[4]=parseFloat(u[4]),r[5]=parseFloat(u[5])}}e.setLocalTransform(r)}}(t,e),js(t,l,u),o||function(t,e,n){for(var i=0;i<Ps.length;i++){var o=Ps[i];null!=(r=t.getAttribute(o))&&(e[Ls[o]]=r)}for(i=0;i<Ns.length;i++){var r;o=Ns[i];null!=(r=t.getAttribute(o))&&(n[ks[o]]=r)}}(t,l,u)),s.style=s.style||{},null!=l.fill&&(s.style.fill=Ws(s,"fill",l.fill,n)),null!=l.stroke&&(s.style.stroke=Ws(s,"stroke",l.stroke,n)),a(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],(function(t){null!=l[t]&&(s.style[t]=parseFloat(l[t]))})),a(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign"],(function(t){null!=l[t]&&(s.style[t]=l[t])})),r&&(s.__selfStyle=u),l.lineDash&&(s.style.lineDash=d(Zs(l.lineDash),(function(t){return parseFloat(t)}))),"hidden"!==l.visibility&&"collapse"!==l.visibility||(s.invisible=!0),"none"===l.display&&(s.ignore=!0)}var Fs=/^url\(\s*#(.*?)\)/;function Ws(t,e,n,i){var o=n&&n.match(Fs);if(!o)return"none"===n&&(n=null),n;var r=_e(o[1]);i.push([t,e,r])}var Hs=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function Zs(t){return t.match(Hs)||[]}var Ys=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.eE,]*)\)/g,Xs=Math.PI/180;var Us=/([^\s:;]+)\s*:\s*([^:;]+)/g;function js(t,e,n){var i,o=t.getAttribute("style");if(o)for(Us.lastIndex=0;null!=(i=Us.exec(o));){var r=i[1],a=ge(Ls,r)?Ls[r]:null;a&&(e[a]=i[2]);var s=ge(ks,r)?ks[r]:null;s&&(n[s]=i[2])}}function qs(t,e){var n=e.width/t.width,i=e.height/t.height,o=Math.min(n,i);return{scale:o,x:-(t.x+t.width/2)*o+(e.x+e.width/2),y:-(t.y+t.height/2)*o+(e.y+e.height/2)}}var Ks=Ie(["rect","circle","line","ellipse","polygon","polyline","path","text","tspan","g"]),Js=function(){function t(t,e){this.type="geoSVG",this._usedGraphicMap=Ie(),this._freedGraphics=[],this._mapName=t,this._parsedXML=Ds(e)}return t.prototype.load=function(){var t=this._firstGraphic;if(!t){t=this._firstGraphic=this._buildGraphic(this._parsedXML),this._freedGraphics.push(t),this._boundingRect=this._firstGraphic.boundingRect.clone();var e=function(t){var e=[],n=Ie();return a(t,(function(t){if(null==t.namedFrom){var i=new Ar(t.name,t.el);e.push(i),n.set(t.name,i)}})),{regions:e,regionsMap:n}}(t.named),n=e.regions,i=e.regionsMap;this._regions=n,this._regionsMap=i}return{boundingRect:this._boundingRect,regions:this._regions,regionsMap:this._regionsMap}},t.prototype._buildGraphic=function(t){var e,n,i,o;try{n=(e=t&&(i=t,o={ignoreViewBox:!0,ignoreRootClip:!0},(new Rs).parse(i,o))||{}).root,ut(null!=n)}catch(x){throw new Error("Invalid svg format\n"+x.message)}var r=new It;r.add(n),r.isGeoSVGGraphicRoot=!0;var l=e.width,u=e.height,d=e.viewBoxRect,c=this._boundingRect;if(!c){var h=void 0,p=void 0,g=void 0,f=void 0;if(null!=l?(h=0,g=l):d&&(h=d.x,g=d.width),null!=u?(p=0,f=u):d&&(p=d.y,f=d.height),null==h||null==p){var y=n.getBoundingRect();null==h&&(h=y.x,g=y.width),null==p&&(p=y.y,f=y.height)}c=this._boundingRect=new s(h,p,g,f)}if(d){var v=qs(d,c);n.scaleX=n.scaleY=v.scale,n.x=v.x,n.y=v.y}r.setClipPath(new pe({shape:c.plain()}));var m=[];return a(e.named,(function(t){var e;null!=Ks.get(t.svgNodeTagLower)&&(m.push(t),(e=t.el).silent=!1,e.isGroup&&e.traverse((function(t){t.silent=!1})))})),{root:r,boundingRect:c,named:m}},t.prototype.useGraphic=function(t){var e=this._usedGraphicMap,n=e.get(t);return n||(n=this._freedGraphics.pop()||this._buildGraphic(this._parsedXML),e.set(t,n),n)},t.prototype.freeGraphic=function(t){var e=this._usedGraphicMap,n=e.get(t);n&&(e.removeKey(t),this._freedGraphics.push(n))},t}();for(var $s=[126,25],Qs="南海诸岛",tl=[[[0,3.5],[7,11.2],[15,11.9],[30,7],[42,.7],[52,.7],[56,7.7],[59,.7],[64,.7],[64,0],[5,0],[0,3.5]],[[13,16.1],[19,14.7],[16,21.7],[11,23.1],[13,16.1]],[[12,32.2],[14,38.5],[15,38.5],[13,32.2],[12,32.2]],[[16,47.6],[12,53.2],[13,53.2],[18,47.6],[16,47.6]],[[6,64.4],[8,70],[9,70],[8,64.4],[6,64.4]],[[23,82.6],[29,79.8],[30,79.8],[25,82.6],[23,82.6]],[[37,70.7],[43,62.3],[44,62.3],[39,70.7],[37,70.7]],[[48,51.1],[51,45.5],[53,45.5],[50,51.1],[48,51.1]],[[51,35],[51,28.7],[53,28.7],[53,35],[51,35]],[[52,22.4],[55,17.5],[56,17.5],[53,22.4],[52,22.4]],[[58,12.6],[62,7],[63,7],[60,12.6],[58,12.6]],[[0,3.5],[0,93.1],[64,93.1],[64,0],[63,0],[63,92.4],[1,92.4],[1,3.5],[0,3.5]]],el=0;el<tl.length;el++)for(var nl=0;nl<tl[el].length;nl++)tl[el][nl][0]/=10.5,tl[el][nl][1]/=-14,tl[el][nl][0]+=$s[0],tl[el][nl][1]+=$s[1];var il={"南海诸岛":[32,80],"广东":[0,-10],"香港":[10,5],"澳门":[-10,10],"天津":[5,5]};var ol=[[[123.45165252685547,25.73527164402261],[123.49731445312499,25.73527164402261],[123.49731445312499,25.750734064600884],[123.45165252685547,25.750734064600884],[123.45165252685547,25.73527164402261]]];var rl=function(){function t(t,e,n){var i;this.type="geoJSON",this._parsedMap=Ie(),this._mapName=t,this._specialAreas=n,this._geoJSON=G(i=e)?"undefined"!=typeof JSON&&JSON.parse?JSON.parse(i):new Function("return ("+i+");")():i}return t.prototype.load=function(t,e){e=e||"name";var n=this._parsedMap.get(e);if(!n){var i=this._parseToRegions(e);n=this._parsedMap.set(e,{regions:i,boundingRect:al(i)})}var o=Ie(),r=[];return a(n.regions,(function(e){var n=e.name;t&&ge(t,n)&&(e=e.cloneShallow(n=t[n])),r.push(e),o.set(n,e)})),{regions:r,boundingRect:n.boundingRect||new s(0,0,0,0),regionsMap:o}},t.prototype._parseToRegions=function(t){var e,n=this._mapName,i=this._geoJSON;try{e=i?Dr(i,t):[]}catch(o){throw new Error("Invalid geoJson format\n"+o.message)}return function(t,e){if("china"===t){for(var n=0;n<e.length;n++)if(e[n].name===Qs)return;e.push(new Ir(Qs,d(tl,(function(t){return{type:"polygon",exterior:t}})),$s))}}(n,e),a(e,(function(t){var e=t.name;!function(t,e){if("china"===t){var n=il[e.name];if(n){var i=e.getCenter();i[0]+=n[0]/10.5,i[1]+=-n[1]/14,e.setCenter(i)}}}(n,t),function(t,e){"china"===t&&"台湾"===e.name&&e.geometries.push({type:"polygon",exterior:ol[0]})}(n,t);var i=this._specialAreas&&this._specialAreas[e];i&&t.transformTo(i.left,i.top,i.width,i.height)}),this),e},t.prototype.getMapForUser=function(){return{geoJson:this._geoJSON,geoJSON:this._geoJSON,specialAreas:this._specialAreas}},t}();function al(t){for(var e,n=0;n<t.length;n++){var i=t[n].getBoundingRect();(e=e||i.clone()).union(i)}return e}var sl=Ie();const ll=function(t,e,n){if(e.svg){var i=new Js(t,e.svg);sl.set(t,i)}else{var o=e.geoJson||e.geoJSON;o&&!e.features?n=e.specialAreas:o=e;i=new rl(t,o,n);sl.set(t,i)}},ul=function(t){return sl.get(t)},dl=function(t){var e=sl.get(t);return e&&"geoJSON"===e.type&&e.getMapForUser()},cl=function(t,e,n){var i=sl.get(t);if(i)return i.load(e,n)};var hl=["rect","circle","line","ellipse","polygon","polyline","path"],pl=Ie(hl),gl=Ie(hl.concat(["g"])),fl=Ie(hl.concat(["g"])),yl=h();function vl(t){var e=t.getItemStyle(),n=t.get("areaColor");return null!=n&&(e.fill=n),e}function ml(t){var e=t.style;e&&(e.stroke=e.stroke||e.fill,e.fill=null)}var xl=function(){function t(t){var e=new It;this.uid=Ae("ec_map_draw"),this._controller=new _s(t.getZr()),this._controllerHost={target:e},this.group=e,e.add(this._regionsGroup=new It),e.add(this._svgGroup=new It)}return t.prototype.draw=function(t,e,n,i,o){var r="geo"===t.mainType,a=t.getData&&t.getData();r&&e.eachComponent({mainType:"series",subType:"map"},(function(e){a||e.getHostGeoModel()!==t||(a=e.getData())}));var s=t.coordinateSystem,l=this._regionsGroup,u=this.group,d=s.getTransformInfo(),c=d.raw,h=d.roam;!l.childAt(0)||o?(u.x=h.x,u.y=h.y,u.scaleX=h.scaleX,u.scaleY=h.scaleY,u.dirty()):P(u,h,t);var p=a&&a.getVisual("visualMeta")&&a.getVisual("visualMeta").length>0,g={api:n,geo:s,mapOrGeoModel:t,data:a,isVisualEncodedByVisualMap:p,isGeo:r,transformInfoRaw:c};"geoJSON"===s.resourceType?this._buildGeoJSON(g):"geoSVG"===s.resourceType&&this._buildSVG(g),this._updateController(t,e,n),this._updateMapSelectHandler(t,l,n,i)},t.prototype._buildGeoJSON=function(t){var e=this._regionsGroupByName=Ie(),n=Ie(),i=this._regionsGroup,o=t.transformInfoRaw,r=t.mapOrGeoModel,s=t.data,l=t.geo.projection,u=l&&l.stream;function d(t,e){return e&&(t=e(t)),t&&[t[0]*o.scaleX+o.x,t[1]*o.scaleY+o.y]}function c(t){for(var e=[],n=!u&&l&&l.project,i=0;i<t.length;++i){var o=d(t[i],n);o&&e.push(o)}return e}function h(t){return{shape:{points:c(t)}}}i.removeAll(),a(t.geo.regions,(function(o){var c=o.name,p=e.get(c),g=n.get(c)||{},f=g.dataIdx,y=g.regionModel;if(!p){p=e.set(c,new It),i.add(p),f=s?s.indexOfName(c):null;var v=(y=t.isGeo?r.getRegionModel(c):s?s.getItemModel(f):null).get("silent",!0);null!=v&&(p.silent=v),n.set(c,{dataIdx:f,regionModel:y})}var m=[],x=[];a(o.geometries,(function(t){if("polygon"===t.type){var e=[t.exterior].concat(t.interiors||[]);u&&(e=Il(e,u)),a(e,(function(t){m.push(new Nt(h(t)))}))}else{var n=t.points;u&&(n=Il(n,u,!0)),a(n,(function(t){x.push(new Rt(h(t)))}))}}));var _=d(o.getCenter(),l&&l.project);function b(e,n){if(e.length){var i=new B({culling:!0,segmentIgnoreThreshold:1,shape:{paths:e}});p.add(i),_l(t,i,f,y),bl(t,i,c,y,r,f,_),n&&(ml(i),a(i.states,ml))}}b(m),b(x,!0)})),e.each((function(e,i){var o=n.get(i),a=o.dataIdx,s=o.regionModel;Sl(t,e,i,s,r,a),wl(t,e,i,s,r),Ml(t,e,i,s,r)}),this)},t.prototype._buildSVG=function(t){var e=t.geo.map,n=t.transformInfoRaw;this._svgGroup.x=n.x,this._svgGroup.y=n.y,this._svgGroup.scaleX=n.scaleX,this._svgGroup.scaleY=n.scaleY,this._svgResourceChanged(e)&&(this._freeSVG(),this._useSVG(e));var i=this._svgDispatcherMap=Ie(),o=!1;a(this._svgGraphicRecord.named,(function(e){var n=e.name,r=t.mapOrGeoModel,a=t.data,s=e.svgNodeTagLower,l=e.el,u=a?a.indexOfName(n):null,d=r.getRegionModel(n);null!=pl.get(s)&&l instanceof Te&&_l(t,l,u,d),l instanceof Te&&(l.culling=!0);var c=d.get("silent",!0);(null!=c&&(l.silent=c),l.z2EmphasisLift=0,e.namedFrom)||(null!=fl.get(s)&&bl(t,l,n,d,r,u,null),Sl(t,l,n,d,r,u),wl(t,l,n,d,r),null!=gl.get(s)&&("self"===Ml(t,l,n,d,r)&&(o=!0),(i.get(n)||i.set(n,[])).push(l)))}),this),this._enableBlurEntireSVG(o,t)},t.prototype._enableBlurEntireSVG=function(t,e){if(t&&e.isGeo){var n=e.mapOrGeoModel.getModel(["blur","itemStyle"]).getItemStyle().opacity;this._svgGraphicRecord.root.traverse((function(t){if(!t.isGroup){Ce(t);var e=t.ensureState("blur").style||{};null==e.opacity&&null!=n&&(e.opacity=n),t.ensureState("emphasis")}}))}},t.prototype.remove=function(){this._regionsGroup.removeAll(),this._regionsGroupByName=null,this._svgGroup.removeAll(),this._freeSVG(),this._controller.dispose(),this._controllerHost=null},t.prototype.findHighDownDispatchers=function(t,e){if(null==t)return[];var n=e.coordinateSystem;if("geoJSON"===n.resourceType){var i=this._regionsGroupByName;if(i){var o=i.get(t);return o?[o]:[]}}else if("geoSVG"===n.resourceType)return this._svgDispatcherMap&&this._svgDispatcherMap.get(t)||[]},t.prototype._svgResourceChanged=function(t){return this._svgMapName!==t},t.prototype._useSVG=function(t){var e=ul(t);if(e&&"geoSVG"===e.type){var n=e.useGraphic(this.uid);this._svgGroup.add(n.root),this._svgGraphicRecord=n,this._svgMapName=t}},t.prototype._freeSVG=function(){var t=this._svgMapName;if(null!=t){var e=ul(t);e&&"geoSVG"===e.type&&e.freeGraphic(this.uid),this._svgGraphicRecord=null,this._svgDispatcherMap=null,this._svgGroup.removeAll(),this._svgMapName=null}},t.prototype._updateController=function(t,e,n){var i=t.coordinateSystem,o=this._controller,r=this._controllerHost;r.zoomLimit=t.get("scaleLimit"),r.zoom=i.getZoom(),o.enable(t.get("roam")||!1);var a=t.mainType;function s(){var e={type:"geoRoam",componentType:a};return e[a+"Id"]=t.id,e}o.off("pan").on("pan",(function(t){this._mouseDownFlag=!1,Ms(r,t.dx,t.dy),n.dispatchAction(H(s(),{dx:t.dx,dy:t.dy,animation:{duration:0}}))}),this),o.off("zoom").on("zoom",(function(t){this._mouseDownFlag=!1,Is(r,t.scale,t.originX,t.originY),n.dispatchAction(H(s(),{totalZoom:r.zoom,zoom:t.scale,originX:t.originX,originY:t.originY,animation:{duration:0}}))}),this),o.setPointerChecker((function(e,o,r){return i.containPoint([o,r])&&!Cs(e,n,t)}))},t.prototype.resetForLabelLayout=function(){this.group.traverse((function(t){var e=t.getTextContent();e&&(e.ignore=yl(e).ignore)}))},t.prototype._updateMapSelectHandler=function(t,e,n,i){var o=this;e.off("mousedown"),e.off("click"),t.get("selectedMode")&&(e.on("mousedown",(function(){o._mouseDownFlag=!0})),e.on("click",(function(t){o._mouseDownFlag&&(o._mouseDownFlag=!1)})))},t}();function _l(t,e,n,i){var o=i.getModel("itemStyle"),r=i.getModel(["emphasis","itemStyle"]),a=i.getModel(["blur","itemStyle"]),s=i.getModel(["select","itemStyle"]),l=vl(o),u=vl(r),d=vl(s),c=vl(a),h=t.data;if(h){var p=h.getItemVisual(n,"style"),g=h.getItemVisual(n,"decal");t.isVisualEncodedByVisualMap&&p.fill&&(l.fill=p.fill),g&&(l.decal=De(g,t.api))}e.setStyle(l),e.style.strokeNoScale=!0,e.ensureState("emphasis").style=u,e.ensureState("select").style=d,e.ensureState("blur").style=c,Ce(e)}function bl(t,e,n,i,o,r,a){var s=t.data,l=t.isGeo,u=s&&isNaN(s.get(s.mapDimension("value"),r)),d=s&&s.getItemLayout(r);if(l||u||d&&d.showLabel){var c=l?n:r,h=void 0;(!s||r>=0)&&(h=o);var p=a?{normal:{align:"center",verticalAlign:"middle"}}:null;zt(e,Bt(i),{labelFetcher:h,labelDataIndex:c,defaultText:n},p);var g=e.getTextContent();if(g&&(yl(g).ignore=g.ignore,e.textConfig&&a)){var f=e.getBoundingRect().clone();e.textConfig.layoutRect=f,e.textConfig.position=[(a[0]-f.x)/f.width*100+"%",(a[1]-f.y)/f.height*100+"%"]}e.disableLabelAnimation=!0}else e.removeTextContent(),e.removeTextConfig(),e.disableLabelAnimation=null}function Sl(t,e,n,i,o,r){t.data?t.data.setItemGraphicEl(r,e):v(e).eventData={componentType:"geo",componentIndex:o.componentIndex,geoIndex:o.componentIndex,name:n,region:i&&i.option||{}}}function wl(t,e,n,i,o){t.data||Le({el:e,componentModel:o,itemName:n,itemTooltipOption:i.get("tooltip")})}function Ml(t,e,n,i,o){e.highDownSilentOnTouch=!!o.get("selectedMode");var r=i.getModel("emphasis"),a=r.get("focus");return Gt(e,a,r.get("blurScope"),r.get("disabled")),t.isGeo&&Pe(e,o,n),a}function Il(t,e,n){var i,o=[];function r(){i=[]}function s(){i.length&&(o.push(i),i=[])}var l=e({polygonStart:r,polygonEnd:s,lineStart:r,lineEnd:s,point:function(t,e){isFinite(t)&&isFinite(e)&&i.push([t,e])},sphere:function(){}});return!n&&l.polygonStart(),a(t,(function(t){l.lineStart();for(var e=0;e<t.length;e++)l.point(t[e][0],t[e][1]);l.lineEnd()})),!n&&l.polygonEnd(),o}var Al=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.render=function(t,e,n,i){if(!i||"mapToggleSelect"!==i.type||i.from!==this.uid){var o=this.group;if(o.removeAll(),!t.getHostGeoModel()){if(this._mapDraw&&i&&"geoRoam"===i.type&&this._mapDraw.resetForLabelLayout(),i&&"geoRoam"===i.type&&"series"===i.componentType&&i.seriesId===t.id)(r=this._mapDraw)&&o.add(r.group);else if(t.needsDrawMap){var r=this._mapDraw||new xl(n);o.add(r.group),r.draw(t,e,n,this,i),this._mapDraw=r}else this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null;t.get("showLegendSymbol")&&e.getComponent("legend")&&this._renderSymbols(t,e,n)}}},n.prototype.remove=function(){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null,this.group.removeAll()},n.prototype.dispose=function(){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null},n.prototype._renderSymbols=function(t,e,n){var i=t.originalData,o=this.group;i.each(i.mapDimension("value"),(function(e,n){if(!isNaN(e)){var r=i.getItemLayout(n);if(r&&r.point){var a=r.point,s=r.offset,l=new ee({style:{fill:t.getData().getVisual("style").fill},shape:{cx:a[0]+9*s,cy:a[1],r:3},silent:!0,z2:8+(s?0:ke+1)});if(!s){var u=t.mainSeries.getData(),d=i.getName(n),c=u.indexOfName(d),h=i.getItemModel(n),p=h.getModel("label"),g=u.getItemGraphicEl(c);zt(l,Bt(h),{labelFetcher:{getFormattedLabel:function(e,n){return t.getFormattedLabel(c,n)}},defaultText:d}),l.disableLabelAnimation=!0,p.get("position")||l.setTextConfig({position:"bottom"}),g.onHoverStateChange=function(t){Ne(l,t)}}o.add(l)}}}))},n.type="map",n}(Dt),Tl=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.needsDrawMap=!1,e.seriesGroup=[],e.getTooltipPosition=function(t){if(null!=t){var e=this.getData().getName(t),n=this.coordinateSystem,i=n.getRegion(e);return i&&n.dataToPoint(i.getCenter())}},e}return e(n,t),n.prototype.getInitialData=function(t){for(var e=Yt(this,{coordDimensions:["value"],encodeDefaulter:Re(Ve,this)}),n=Ie(),i=[],o=0,r=e.count();o<r;o++){var s=e.getName(o);n.set(s,o)}var l=cl(this.getMapType(),this.option.nameMap,this.option.nameProperty);return a(l.regions,(function(t){var o,r=t.name,a=n.get(r),s=t.properties&&t.properties.echartsStyle;null==a?(o={name:r},i.push(o)):o=e.getRawDataItem(a),s&&jt(o,s)})),e.appendData(i),e},n.prototype.getHostGeoModel=function(){var t=this.option.geoIndex;return null!=t?this.ecModel.getComponent("geo",t):null},n.prototype.getMapType=function(){return(this.getHostGeoModel()||this).option.map},n.prototype.getRawValue=function(t){var e=this.getData();return e.get(e.mapDimension("value"),t)},n.prototype.getRegionModel=function(t){var e=this.getData();return e.getItemModel(e.indexOfName(t))},n.prototype.formatTooltip=function(t,e,n){for(var i=this.getData(),o=this.getRawValue(t),r=i.getName(t),a=this.seriesGroup,s=[],l=0;l<a.length;l++){var u=a[l].originalData.indexOfName(r),d=i.mapDimension("value");isNaN(a[l].originalData.get(d,u))||s.push(a[l].name)}return Ut("section",{header:s.join(", "),noHeader:!s.length,blocks:[Ut("nameValue",{name:r,value:o})]})},n.prototype.setZoom=function(t){this.option.zoom=t},n.prototype.setCenter=function(t){this.option.center=t},n.prototype.getLegendIcon=function(t){var e=t.icon||"roundRect",n=At(e,0,0,t.itemWidth,t.itemHeight,t.itemStyle.fill);return n.setStyle(t.itemStyle),n.style.stroke="none",e.indexOf("empty")>-1&&(n.style.stroke=n.style.fill,n.style.fill="#fff",n.style.lineWidth=2),n},n.type="series.map",n.dependencies=["geo"],n.layoutMode="box",n.defaultOption={z:2,coordinateSystem:"geo",map:"",left:"center",top:"center",aspectScale:null,showLegendSymbol:!0,boundingCoords:null,center:null,zoom:1,scaleLimit:null,selectedMode:!0,label:{show:!1,color:"#000"},itemStyle:{borderWidth:.5,borderColor:"#444",areaColor:"#eee"},emphasis:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{areaColor:"rgba(255,215,0,0.8)"}},select:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},nameProperty:"name"},n}(Mt);function Cl(t){var e={};t.eachSeriesByType("map",(function(t){var n=t.getHostGeoModel(),i=n?"o"+n.id:"i"+t.getMapType();(e[i]=e[i]||[]).push(t)})),a(e,(function(t,e){for(var n,i,o,r=(n=d(t,(function(t){return t.getData()})),i=t[0].get("mapValueCalculation"),o={},a(n,(function(t){t.each(t.mapDimension("value"),(function(e,n){var i="ec-"+t.getName(n);o[i]=o[i]||[],isNaN(e)||o[i].push(e)}))})),n[0].map(n[0].mapDimension("value"),(function(t,e){for(var r="ec-"+n[0].getName(e),a=0,s=1/0,l=-1/0,u=o[r].length,d=0;d<u;d++)s=Math.min(s,o[r][d]),l=Math.max(l,o[r][d]),a+=o[r][d];return 0===u?NaN:"min"===i?s:"max"===i?l:"average"===i?a/u:a}))),s=0;s<t.length;s++)t[s].originalData=t[s].getData();for(s=0;s<t.length;s++)t[s].seriesGroup=t,t[s].needsDrawMap=0===s&&!t[s].getHostGeoModel(),t[s].setData(r.cloneShallow()),t[s].mainSeries=t[0]}))}function Dl(t){var e={};t.eachSeriesByType("map",(function(n){var i=n.getMapType();if(!n.getHostGeoModel()&&!e[i]){var o={};a(n.seriesGroup,(function(e){var n=e.coordinateSystem,i=e.originalData;e.get("showLegendSymbol")&&t.getComponent("legend")&&i.each(i.mapDimension("value"),(function(t,e){var r=i.getName(e),a=n.getRegion(r);if(a&&!isNaN(t)){var s=o[r]||0,l=n.dataToPoint(a.getCenter());o[r]=s+1,i.setItemLayout(e,{point:l,offset:s})}}))}));var r=n.getData();r.each((function(t){var e=r.getName(t),n=r.getItemLayout(t)||{};n.showLabel=!o[e],r.setItemLayout(t,n)})),e[i]=!0}}))}var Ll=r,Pl=function(t){function n(e){var n=t.call(this)||this;return n.type="view",n.dimensions=["x","y"],n._roamTransformable=new p,n._rawTransformable=new p,n.name=e,n}return e(n,t),n.prototype.setBoundingRect=function(t,e,n,i){return this._rect=new s(t,e,n,i),this._rect},n.prototype.getBoundingRect=function(){return this._rect},n.prototype.setViewRect=function(t,e,n,i){this._transformTo(t,e,n,i),this._viewRect=new s(t,e,n,i)},n.prototype._transformTo=function(t,e,n,i){var o=this.getBoundingRect(),r=this._rawTransformable;r.transform=o.calculateTransform(new s(t,e,n,i));var a=r.parent;r.parent=null,r.decomposeTransform(),r.parent=a,this._updateTransform()},n.prototype.setCenter=function(t,e){t&&(this._center=[m(t[0],e.getWidth()),m(t[1],e.getHeight())],this._updateCenterAndZoom())},n.prototype.setZoom=function(t){t=t||1;var e=this.zoomLimit;e&&(null!=e.max&&(t=Math.min(e.max,t)),null!=e.min&&(t=Math.max(e.min,t))),this._zoom=t,this._updateCenterAndZoom()},n.prototype.getDefaultCenter=function(){var t=this.getBoundingRect();return[t.x+t.width/2,t.y+t.height/2]},n.prototype.getCenter=function(){return this._center||this.getDefaultCenter()},n.prototype.getZoom=function(){return this._zoom||1},n.prototype.getRoamTransform=function(){return this._roamTransformable.getLocalTransform()},n.prototype._updateCenterAndZoom=function(){var t=this._rawTransformable.getLocalTransform(),e=this._roamTransformable,n=this.getDefaultCenter(),i=this.getCenter(),o=this.getZoom();i=r([],i,t),n=r([],n,t),e.originX=i[0],e.originY=i[1],e.x=n[0]-i[0],e.y=n[1]-i[1],e.scaleX=e.scaleY=o,this._updateTransform()},n.prototype._updateTransform=function(){var t=this._roamTransformable,e=this._rawTransformable;e.parent=t,t.updateTransform(),e.updateTransform(),Oe(this.transform||(this.transform=[]),e.transform||Me()),this._rawTransform=e.getLocalTransform(),this.invTransform=this.invTransform||[],o(this.invTransform,this.transform),this.decomposeTransform()},n.prototype.getTransformInfo=function(){var t=this._rawTransformable,e=this._roamTransformable,n=new p;return n.transform=e.transform,n.decomposeTransform(),{roam:{x:n.x,y:n.y,scaleX:n.scaleX,scaleY:n.scaleY},raw:{x:t.x,y:t.y,scaleX:t.scaleX,scaleY:t.scaleY}}},n.prototype.getViewRect=function(){return this._viewRect},n.prototype.getViewRectAfterRoam=function(){var t=this.getBoundingRect().clone();return t.applyTransform(this.transform),t},n.prototype.dataToPoint=function(t,e,n){var i=e?this._rawTransform:this.transform;return n=n||[],i?Ll(n,t,i):Ee(n,t)},n.prototype.pointToData=function(t){var e=this.invTransform;return e?Ll([],t,e):[t[0],t[1]]},n.prototype.convertToPixel=function(t,e,n){var i=kl(e);return i===this?i.dataToPoint(n):null},n.prototype.convertFromPixel=function(t,e,n){var i=kl(e);return i===this?i.pointToData(n):null},n.prototype.containPoint=function(t){return this.getViewRectAfterRoam().contain(t[0],t[1])},n.dimensions=["x","y"],n}(p);function kl(t){var e=t.seriesModel;return e?e.coordinateSystem:null}var Nl={geoJSON:{aspectScale:.75,invertLongitute:!0},geoSVG:{aspectScale:1,invertLongitute:!1}},Rl=["lng","lat"],Vl=function(t){function n(e,n,i){var o=t.call(this,e)||this;o.dimensions=Rl,o.type="geo",o._nameCoordMap=Ie(),o.map=n;var r=i.projection,a=cl(n,i.nameMap,i.nameProperty),s=ul(n);o.resourceType=s?s.type:null;var l,u=o.regions=a.regions,d=Nl[s.type];if(o._regionsMap=a.regionsMap,o.regions=a.regions,o.projection=r,r)for(var c=0;c<u.length;c++){var h=u[c].getBoundingRect(r);(l=l||h.clone()).union(h)}else l=a.boundingRect;return o.setBoundingRect(l.x,l.y,l.width,l.height),o.aspectScale=r?1:C(i.aspectScale,d.aspectScale),o._invertLongitute=!r&&d.invertLongitute,o}return e(n,t),n.prototype._transformTo=function(t,e,n,i){var o=this.getBoundingRect(),r=this._invertLongitute;o=o.clone(),r&&(o.y=-o.y-o.height);var a=this._rawTransformable;a.transform=o.calculateTransform(new s(t,e,n,i));var l=a.parent;a.parent=null,a.decomposeTransform(),a.parent=l,r&&(a.scaleY=-a.scaleY),this._updateTransform()},n.prototype.getRegion=function(t){return this._regionsMap.get(t)},n.prototype.getRegionByCoord=function(t){for(var e=this.regions,n=0;n<e.length;n++){var i=e[n];if("geoJSON"===i.type&&i.contain(t))return e[n]}},n.prototype.addGeoCoord=function(t,e){this._nameCoordMap.set(t,e)},n.prototype.getGeoCoord=function(t){var e=this._regionsMap.get(t);return this._nameCoordMap.get(t)||e&&e.getCenter()},n.prototype.dataToPoint=function(t,e,n){if(G(t)&&(t=this.getGeoCoord(t)),t){var i=this.projection;return i&&(t=i.project(t)),t&&this.projectedToPoint(t,e,n)}},n.prototype.pointToData=function(t){var e=this.projection;return e&&(t=e.unproject(t)),t&&this.pointToProjected(t)},n.prototype.pointToProjected=function(e){return t.prototype.pointToData.call(this,e)},n.prototype.projectedToPoint=function(e,n,i){return t.prototype.dataToPoint.call(this,e,n,i)},n.prototype.convertToPixel=function(t,e,n){var i=Ol(e);return i===this?i.dataToPoint(n):null},n.prototype.convertFromPixel=function(t,e,n){var i=Ol(e);return i===this?i.pointToData(n):null},n}(Pl);function Ol(t){var e=t.geoModel,n=t.seriesModel;return e?e.coordinateSystem:n?n.coordinateSystem||(n.getReferringComponents("geo",ze).models[0]||{}).coordinateSystem:null}function El(t,e){var n=t.get("boundingCoords");if(null!=n){var i=n[0],o=n[1];if(isFinite(i[0])&&isFinite(i[1])&&isFinite(o[0])&&isFinite(o[1])){var r=this.projection;if(r){var a=i[0],s=i[1],d=o[0],c=o[1];i=[1/0,1/0],o=[-1/0,-1/0];var h=function(t,e,n,a){for(var s=n-t,d=a-e,c=0;c<=100;c++){var h=c/100,p=r.project([t+s*h,e+d*h]);l(i,i,p),u(o,o,p)}};h(a,s,d,s),h(d,s,d,c),h(d,c,a,c),h(a,c,d,s)}this.setBoundingRect(i[0],i[1],o[0]-i[0],o[1]-i[1])}else;}var p,g,f,y=this.getBoundingRect(),v=t.get("layoutCenter"),x=t.get("layoutSize"),_=e.getWidth(),b=e.getHeight(),S=y.width/y.height*this.aspectScale,w=!1;if(v&&x&&(p=[m(v[0],_),m(v[1],b)],g=m(x,Math.min(_,b)),isNaN(p[0])||isNaN(p[1])||isNaN(g)||(w=!0)),w)f={},S>1?(f.width=g,f.height=g/S):(f.height=g,f.width=g*S),f.y=p[1]-f.height/2,f.x=p[0]-f.width/2;else{var M=t.getBoxLayoutParams();M.aspect=S,f=Fe(M,{width:_,height:b})}this.setViewRect(f.x,f.y,f.width,f.height),this.setCenter(t.get("center"),e),this.setZoom(t.get("zoom"))}Kt(Vl,Pl);var zl=function(){function t(){this.dimensions=Rl}return t.prototype.create=function(t,e){var n=[];function i(t){return{nameProperty:t.get("nameProperty"),aspectScale:t.get("aspectScale"),projection:t.get("projection")}}t.eachComponent("geo",(function(t,o){var r=t.get("map"),a=new Vl(r+o,r,H({nameMap:t.get("nameMap")},i(t)));a.zoomLimit=t.get("scaleLimit"),n.push(a),t.coordinateSystem=a,a.model=t,a.resize=El,a.resize(t,e)})),t.eachSeries((function(t){if("geo"===t.get("coordinateSystem")){var e=t.get("geoIndex")||0;t.coordinateSystem=n[e]}}));var o={};return t.eachSeriesByType("map",(function(t){if(!t.getHostGeoModel()){var e=t.getMapType();o[e]=o[e]||[],o[e].push(t)}})),a(o,(function(t,o){var r=d(t,(function(t){return t.get("nameMap")})),s=new Vl(o,o,H({nameMap:Be(r)},i(t[0])));s.zoomLimit=Ge.apply(null,d(t,(function(t){return t.get("scaleLimit")}))),n.push(s),s.resize=El,s.resize(t[0],e),a(t,(function(t){t.coordinateSystem=s,function(t,e){a(e.get("geoCoord"),(function(e,n){t.addGeoCoord(n,e)}))}(s,t)}))})),n},t.prototype.getFilledRegions=function(t,e,n,i){for(var o=(t||[]).slice(),r=Ie(),s=0;s<o.length;s++)r.set(o[s].name,o[s]);var l=cl(e,n,i);return a(l.regions,(function(t){var e=t.name,n=r.get(e),i=t.properties&&t.properties.echartsStyle;n||(n={name:e},o.push(n)),i&&jt(n,i)})),o},t}(),Bl=new zl,Gl=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.init=function(t,e,n){var i=ul(t.map);if(i&&"geoJSON"===i.type){var o=t.itemStyle=t.itemStyle||{};"color"in o||(o.color="#eee")}this.mergeDefaultAndTheme(t,n),We(t,"label",["show"])},n.prototype.optionUpdated=function(){var t=this,e=this.option;e.regions=Bl.getFilledRegions(e.regions,e.map,e.nameMap,e.nameProperty);var n={};this._optionModelMap=He(e.regions||[],(function(e,i){var o=i.name;return o&&(e.set(o,new qt(i,t,t.ecModel)),i.selected&&(n[o]=!0)),e}),Ie()),e.selectedMap||(e.selectedMap=n)},n.prototype.getRegionModel=function(t){return this._optionModelMap.get(t)||new qt(null,this,this.ecModel)},n.prototype.getFormattedLabel=function(t,e){var n=this.getRegionModel(t),i="normal"===e?n.get(["label","formatter"]):n.get(["emphasis","label","formatter"]),o={name:t};return f(i)?(o.status=e,i(o)):G(i)?i.replace("{a}",null!=t?t:""):void 0},n.prototype.setZoom=function(t){this.option.zoom=t},n.prototype.setCenter=function(t){this.option.center=t},n.prototype.select=function(t){var e=this.option,n=e.selectedMode;n&&("multiple"!==n&&(e.selectedMap=null),(e.selectedMap||(e.selectedMap={}))[t]=!0)},n.prototype.unSelect=function(t){var e=this.option.selectedMap;e&&(e[t]=!1)},n.prototype.toggleSelected=function(t){this[this.isSelected(t)?"unSelect":"select"](t)},n.prototype.isSelected=function(t){var e=this.option.selectedMap;return!(!e||!e[t])},n.type="geo",n.layoutMode="box",n.defaultOption={z:0,show:!0,left:"center",top:"center",aspectScale:null,silent:!1,map:"",boundingCoords:null,center:null,zoom:1,scaleLimit:null,label:{show:!1,color:"#000"},itemStyle:{borderWidth:.5,borderColor:"#444"},emphasis:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},select:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},regions:[]},n}(Qt);function Fl(t,e){return t.pointToProjected?t.pointToProjected(e):t.pointToData(e)}function Wl(t,e,n,i){var o=t.getZoom(),r=t.getCenter(),a=e.zoom,s=t.projectedToPoint?t.projectedToPoint(r):t.dataToPoint(r);if(null!=e.dx&&null!=e.dy&&(s[0]-=e.dx,s[1]-=e.dy,t.setCenter(Fl(t,s),i)),null!=a){if(n){var l=n.min||0,u=n.max||1/0;a=Math.max(Math.min(o*a,u),l)/o}t.scaleX*=a,t.scaleY*=a;var d=(e.originX-t.x)*(a-1),c=(e.originY-t.y)*(a-1);t.x-=d,t.y-=c,t.updateTransform(),t.setCenter(Fl(t,s),i),t.setZoom(a*o)}return{center:t.getCenter(),zoom:t.getZoom()}}var Hl=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.focusBlurEnabled=!0,e}return e(n,t),n.prototype.init=function(t,e){this._api=e},n.prototype.render=function(t,e,n,i){if(this._model=t,!t.get("show"))return this._mapDraw&&this._mapDraw.remove(),void(this._mapDraw=null);this._mapDraw||(this._mapDraw=new xl(n));var o=this._mapDraw;o.draw(t,e,n,this,i),o.group.on("click",this._handleRegionClick,this),o.group.silent=t.get("silent"),this.group.add(o.group),this.updateSelectStatus(t,e,n)},n.prototype._handleRegionClick=function(t){var e;Ze(t.target,(function(t){return null!=(e=v(t).eventData)}),!0),e&&this._api.dispatchAction({type:"geoToggleSelect",geoId:this._model.id,name:e.name})},n.prototype.updateSelectStatus=function(t,e,n){var i=this;this._mapDraw.group.traverse((function(t){var e=v(t).eventData;if(e)return i._model.isSelected(e.name)?n.enterSelect(t):n.leaveSelect(t),!0}))},n.prototype.findHighDownDispatchers=function(t){return this._mapDraw&&this._mapDraw.findHighDownDispatchers(t,this._model)},n.prototype.dispose=function(){this._mapDraw&&this._mapDraw.remove()},n.type="geo",n}(oe);function Zl(t,e,n){ll(t,e,n)}function Yl(t){function e(e,n){n.update="geo:updateSelectStatus",t.registerAction(n,(function(t,n){var i={},o=[];return n.eachComponent({mainType:"geo",query:t},(function(n){n[e](t.name);var r=n.coordinateSystem;a(r.regions,(function(t){i[t.name]=n.isSelected(t.name)||!1}));var s=[];a(i,(function(t,e){i[e]&&s.push(e)})),o.push({geoIndex:n.componentIndex,name:s})})),{selected:i,allSelected:o,name:t.name}}))}t.registerCoordinateSystem("geo",Bl),t.registerComponentModel(Gl),t.registerComponentView(Hl),t.registerImpl("registerMap",Zl),t.registerImpl("getMap",(function(t){return dl(t)})),e("toggleSelected",{type:"geoToggleSelect",event:"geoselectchanged"}),e("select",{type:"geoSelect",event:"geoselected"}),e("unSelect",{type:"geoUnSelect",event:"geounselected"}),t.registerAction({type:"geoRoam",event:"geoRoam",update:"updateTransform"},(function(t,e,n){var i=t.componentType||"series";e.eachComponent({mainType:i,query:t},(function(e){var o=e.coordinateSystem;if("geo"===o.type){var r=Wl(o,t,e.get("scaleLimit"),n);e.setCenter&&e.setCenter(r.center),e.setZoom&&e.setZoom(r.zoom),"series"===i&&a(e.seriesGroup,(function(t){t.setCenter(r.center),t.setZoom(r.zoom)}))}}))}))}function Xl(t,e){var n=t.isExpand?t.children:[],i=t.parentNode.children,o=t.hierNode.i?i[t.hierNode.i-1]:null;if(n.length){!function(t){var e=t.children,n=e.length,i=0,o=0;for(;--n>=0;){var r=e[n];r.hierNode.prelim+=i,r.hierNode.modifier+=i,o+=r.hierNode.change,i+=r.hierNode.shift+o}}(t);var r=(n[0].hierNode.prelim+n[n.length-1].hierNode.prelim)/2;o?(t.hierNode.prelim=o.hierNode.prelim+e(t,o),t.hierNode.modifier=t.hierNode.prelim-r):t.hierNode.prelim=r}else o&&(t.hierNode.prelim=o.hierNode.prelim+e(t,o));t.parentNode.hierNode.defaultAncestor=function(t,e,n,i){if(e){for(var o=t,r=t,a=r.parentNode.children[0],s=e,l=o.hierNode.modifier,u=r.hierNode.modifier,d=a.hierNode.modifier,c=s.hierNode.modifier;s=Kl(s),r=Jl(r),s&&r;){o=Kl(o),a=Jl(a),o.hierNode.ancestor=t;var h=s.hierNode.prelim+c-r.hierNode.prelim-u+i(s,r);h>0&&(Ql($l(s,t,n),t,h),u+=h,l+=h),c+=s.hierNode.modifier,u+=r.hierNode.modifier,l+=o.hierNode.modifier,d+=a.hierNode.modifier}s&&!Kl(o)&&(o.hierNode.thread=s,o.hierNode.modifier+=c-l),r&&!Jl(a)&&(a.hierNode.thread=r,a.hierNode.modifier+=u-d,n=t)}return n}(t,o,t.parentNode.hierNode.defaultAncestor||i[0],e)}function Ul(t){var e=t.hierNode.prelim+t.parentNode.hierNode.modifier;t.setLayout({x:e},!0),t.hierNode.modifier+=t.parentNode.hierNode.modifier}function jl(t){return arguments.length?t:tu}function ql(t,e){return t-=Math.PI/2,{x:e*Math.cos(t),y:e*Math.sin(t)}}function Kl(t){var e=t.children;return e.length&&t.isExpand?e[e.length-1]:t.hierNode.thread}function Jl(t){var e=t.children;return e.length&&t.isExpand?e[0]:t.hierNode.thread}function $l(t,e,n){return t.hierNode.ancestor.parentNode===e.parentNode?t.hierNode.ancestor:n}function Ql(t,e,n){var i=n/(e.hierNode.i-t.hierNode.i);e.hierNode.change-=i,e.hierNode.shift+=n,e.hierNode.modifier+=n,e.hierNode.prelim+=n,t.hierNode.change+=i}function tu(t,e){return t.parentNode===e.parentNode?1:2}var eu=function(){return function(){this.parentPoint=[],this.childPoints=[]}}(),nu=function(t){function n(e){return t.call(this,e)||this}return e(n,t),n.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},n.prototype.getDefaultShape=function(){return new eu},n.prototype.buildPath=function(t,e){var n=e.childPoints,i=n.length,o=e.parentPoint,r=n[0],a=n[i-1];if(1===i)return t.moveTo(o[0],o[1]),void t.lineTo(r[0],r[1]);var s=e.orient,l="TB"===s||"BT"===s?0:1,u=1-l,d=m(e.forkPosition,1),c=[];c[l]=o[l],c[u]=o[u]+(a[u]-o[u])*d,t.moveTo(o[0],o[1]),t.lineTo(c[0],c[1]),t.moveTo(r[0],r[1]),c[l]=r[l],t.lineTo(c[0],c[1]),c[l]=a[l],t.lineTo(c[0],c[1]),t.lineTo(a[0],a[1]);for(var h=1;h<i-1;h++){var p=n[h];t.moveTo(p[0],p[1]),c[l]=p[l],t.lineTo(c[0],c[1])}},n}(q),iu=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e._mainGroup=new It,e}return e(n,t),n.prototype.init=function(t,e){this._controller=new _s(e.getZr()),this._controllerHost={target:this.group},this.group.add(this._mainGroup)},n.prototype.render=function(t,e,n){var i=t.getData(),o=t.layoutInfo,r=this._mainGroup;"radial"===t.get("layout")?(r.x=o.x+o.width/2,r.y=o.y+o.height/2):(r.x=o.x,r.y=o.y),this._updateViewCoordSys(t,n),this._updateController(t,e,n);var a=this._data;i.diff(a).add((function(e){ou(i,e)&&ru(i,e,null,r,t)})).update((function(e,n){var o=a.getItemGraphicEl(n);ou(i,e)?ru(i,e,o,r,t):o&&lu(a,n,o,r,t)})).remove((function(e){var n=a.getItemGraphicEl(e);n&&lu(a,e,n,r,t)})).execute(),this._nodeScaleRatio=t.get("nodeScaleRatio"),this._updateNodeAndLinkScale(t),!0===t.get("expandAndCollapse")&&i.eachItemGraphicEl((function(e,i){e.off("click").on("click",(function(){n.dispatchAction({type:"treeExpandAndCollapse",seriesId:t.id,dataIndex:i})}))})),this._data=i},n.prototype._updateViewCoordSys=function(t,e){var n=t.getData(),i=[];n.each((function(t){var e=n.getItemLayout(t);!e||isNaN(e.x)||isNaN(e.y)||i.push([+e.x,+e.y])}));var o=[],r=[];Xe(i,o,r);var a=this._min,s=this._max;r[0]-o[0]==0&&(o[0]=a?a[0]:o[0]-1,r[0]=s?s[0]:r[0]+1),r[1]-o[1]==0&&(o[1]=a?a[1]:o[1]-1,r[1]=s?s[1]:r[1]+1);var l=t.coordinateSystem=new Pl;l.zoomLimit=t.get("scaleLimit"),l.setBoundingRect(o[0],o[1],r[0]-o[0],r[1]-o[1]),l.setCenter(t.get("center"),e),l.setZoom(t.get("zoom")),this.group.attr({x:l.x,y:l.y,scaleX:l.scaleX,scaleY:l.scaleY}),this._min=o,this._max=r},n.prototype._updateController=function(t,e,n){var i=this,o=this._controller,r=this._controllerHost,a=this.group;o.setPointerChecker((function(e,i,o){var r=a.getBoundingRect();return r.applyTransform(a.transform),r.contain(i,o)&&!Cs(e,n,t)})),o.enable(t.get("roam")),r.zoomLimit=t.get("scaleLimit"),r.zoom=t.coordinateSystem.getZoom(),o.off("pan").off("zoom").on("pan",(function(e){Ms(r,e.dx,e.dy),n.dispatchAction({seriesId:t.id,type:"treeRoam",dx:e.dx,dy:e.dy})})).on("zoom",(function(e){Is(r,e.scale,e.originX,e.originY),n.dispatchAction({seriesId:t.id,type:"treeRoam",zoom:e.scale,originX:e.originX,originY:e.originY}),i._updateNodeAndLinkScale(t),n.updateLabelLayout()}))},n.prototype._updateNodeAndLinkScale=function(t){var e=t.getData(),n=this._getNodeGlobalScale(t);e.eachItemGraphicEl((function(t,e){t.setSymbolScale(n)}))},n.prototype._getNodeGlobalScale=function(t){var e=t.coordinateSystem;if("view"!==e.type)return 1;var n=this._nodeScaleRatio,i=e.scaleX||1;return((e.getZoom()-1)*n+1)/i},n.prototype.dispose=function(){this._controller&&this._controller.dispose(),this._controllerHost=null},n.prototype.remove=function(){this._mainGroup.removeAll(),this._data=null},n.type="tree",n}(Dt);function ou(t,e){var n=t.getItemLayout(e);return n&&!isNaN(n.x)&&!isNaN(n.y)}function ru(t,e,n,i,o){var r=!n,a=t.tree.getNodeByDataIndex(e),s=a.getModel(),l=a.getVisual("style").fill,u=!1===a.isExpand&&0!==a.children.length?l:"#fff",d=t.tree.root,c=a.parentNode===d?a:a.parentNode||a,h=t.getItemGraphicEl(c.dataIndex),p=c.getLayout(),g=h?{x:h.__oldX,y:h.__oldY,rawX:h.__radialOldRawX,rawY:h.__radialOldRawY}:p,f=a.getLayout();r?((n=new Ue(t,e,null,{symbolInnerColor:u,useNameLabel:!0})).x=g.x,n.y=g.y):n.updateData(t,e,null,{symbolInnerColor:u,useNameLabel:!0}),n.__radialOldRawX=n.__radialRawX,n.__radialOldRawY=n.__radialRawY,n.__radialRawX=f.rawX,n.__radialRawY=f.rawY,i.add(n),t.setItemGraphicEl(e,n),n.__oldX=n.x,n.__oldY=n.y,P(n,{x:f.x,y:f.y},o);var y=n.getSymbolPath();if("radial"===o.get("layout")){var m=d.children[0],x=m.getLayout(),_=m.children.length,b=void 0,S=void 0;if(f.x===x.x&&!0===a.isExpand&&m.children.length){var w={x:(m.children[0].getLayout().x+m.children[_-1].getLayout().x)/2,y:(m.children[0].getLayout().y+m.children[_-1].getLayout().y)/2};(b=Math.atan2(w.y-x.y,w.x-x.x))<0&&(b=2*Math.PI+b),(S=w.x<x.x)&&(b-=Math.PI)}else(b=Math.atan2(f.y-x.y,f.x-x.x))<0&&(b=2*Math.PI+b),0===a.children.length||0!==a.children.length&&!1===a.isExpand?(S=f.x<x.x)&&(b-=Math.PI):(S=f.x>x.x)||(b-=Math.PI);var M=S?"left":"right",I=s.getModel("label"),A=I.get("rotate"),T=A*(Math.PI/180),C=y.getTextContent();C&&(y.setTextConfig({position:I.get("position")||M,rotation:null==A?-b:T,origin:"center"}),C.setStyle("verticalAlign","middle"))}var D=s.get(["emphasis","focus"]),L="relative"===D?je(a.getAncestorsIndices(),a.getDescendantIndices()):"ancestor"===D?a.getAncestorsIndices():"descendant"===D?a.getDescendantIndices():null;L&&(v(n).focus=L),function(t,e,n,i,o,r,a,s){var l=e.getModel(),u=t.get("edgeShape"),d=t.get("layout"),c=t.getOrient(),h=t.get(["lineStyle","curveness"]),p=t.get("edgeForkPosition"),g=l.getModel("lineStyle").getLineStyle(),f=i.__edge;if("curve"===u)e.parentNode&&e.parentNode!==n&&(f||(f=i.__edge=new Je({shape:uu(d,c,h,o,o)})),P(f,{shape:uu(d,c,h,r,a)},t));else if("polyline"===u&&"orthogonal"===d&&e!==n&&e.children&&0!==e.children.length&&!0===e.isExpand){for(var y=e.children,v=[],m=0;m<y.length;m++){var x=y[m].getLayout();v.push([x.x,x.y])}f||(f=i.__edge=new nu({shape:{parentPoint:[a.x,a.y],childPoints:[[a.x,a.y]],orient:c,forkPosition:p}})),P(f,{shape:{parentPoint:[a.x,a.y],childPoints:v}},t)}f&&("polyline"!==u||e.isExpand)&&(f.useStyle(Ot({strokeNoScale:!0,fill:null},g)),Et(f,l,"lineStyle"),Ce(f),s.add(f))}(o,a,d,n,g,p,f,i),n.__edge&&(n.onHoverStateChange=function(e){if("blur"!==e){var i=a.parentNode&&t.getItemGraphicEl(a.parentNode.dataIndex);i&&i.hoverState===qe||Ne(n.__edge,e)}})}function au(t,e,n,i,o){var r=su(e.tree.root,t),a=r.source,s=r.sourceLayout,l=e.getItemGraphicEl(t.dataIndex);if(l){var u=e.getItemGraphicEl(a.dataIndex).__edge,d=l.__edge||(!1===a.isExpand||1===a.children.length?u:void 0),c=i.get("edgeShape"),h=i.get("layout"),p=i.get("orient"),g=i.get(["lineStyle","curveness"]);d&&("curve"===c?Ke(d,{shape:uu(h,p,g,s,s),style:{opacity:0}},i,{cb:function(){n.remove(d)},removeOpt:o}):"polyline"===c&&"orthogonal"===i.get("layout")&&Ke(d,{shape:{parentPoint:[s.x,s.y],childPoints:[[s.x,s.y]]},style:{opacity:0}},i,{cb:function(){n.remove(d)},removeOpt:o}))}}function su(t,e){for(var n,i=e.parentNode===t?e:e.parentNode||e;null==(n=i.getLayout());)i=i.parentNode===t?i:i.parentNode||i;return{source:i,sourceLayout:n}}function lu(t,e,n,i,o){var r=t.tree.getNodeByDataIndex(e),a=su(t.tree.root,r).sourceLayout,s={duration:o.get("animationDurationUpdate"),easing:o.get("animationEasingUpdate")};Ke(n,{x:a.x+1,y:a.y+1},o,{cb:function(){i.remove(n),t.setItemGraphicEl(e,null)},removeOpt:s}),n.fadeOut(null,t.hostModel,{fadeLabel:!0,animation:s}),r.children.forEach((function(e){au(e,t,i,o,s)})),au(r,t,i,o,s)}function uu(t,e,n,i,o){var r,a,s,l,u,d,c,h;if("radial"===t){u=i.rawX,c=i.rawY,d=o.rawX,h=o.rawY;var p=ql(u,c),g=ql(u,c+(h-c)*n),f=ql(d,h+(c-h)*n),y=ql(d,h);return{x1:p.x||0,y1:p.y||0,x2:y.x||0,y2:y.y||0,cpx1:g.x||0,cpy1:g.y||0,cpx2:f.x||0,cpy2:f.y||0}}return u=i.x,c=i.y,d=o.x,h=o.y,"LR"!==e&&"RL"!==e||(r=u+(d-u)*n,a=c,s=d+(u-d)*n,l=h),"TB"!==e&&"BT"!==e||(r=u,a=c+(h-c)*n,s=d,l=h+(c-h)*n),{x1:u,y1:c,x2:d,y2:h,cpx1:r,cpy1:a,cpx2:s,cpy2:l}}var du=h();function cu(t){var e=t.mainData,n=t.datas;n||(n={main:e},t.datasAttr={main:"data"}),t.datas=t.mainData=null,vu(e,n,t),a(n,(function(n){a(e.TRANSFERABLE_METHODS,(function(e){n.wrapMethod(e,Re(hu,t))}))})),e.wrapMethod("cloneShallow",Re(gu,t)),a(e.CHANGABLE_METHODS,(function(n){e.wrapMethod(n,Re(pu,t))})),ut(n[e.dataType]===e)}function hu(t,e){if(du(i=this).mainData===i){var n=H({},du(this).datas);n[this.dataType]=e,vu(e,n,t)}else mu(e,this.dataType,du(this).mainData,t);var i;return e}function pu(t,e){return t.struct&&t.struct.update(),e}function gu(t,e){return a(du(e).datas,(function(n,i){n!==e&&mu(n.cloneShallow(),i,e,t)})),e}function fu(t){var e=du(this).mainData;return null==t||null==e?e:du(e).datas[t]}function yu(){var t=du(this).mainData;return null==t?[{data:t}]:d(y(du(t).datas),(function(e){return{type:e,data:du(t).datas[e]}}))}function vu(t,e,n){du(t).datas={},a(e,(function(e,i){mu(e,i,t,n)}))}function mu(t,e,n,i){du(n).datas[e]=t,du(t).mainData=n,t.dataType=e,i.struct&&(t[i.structAttr]=i.struct,i.struct[i.datasAttr[e]]=t),t.getLinkedData=fu,t.getLinkedDataAll=yu}var xu=function(){function t(t,e){this.depth=0,this.height=0,this.dataIndex=-1,this.children=[],this.viewChildren=[],this.isExpand=!1,this.name=t||"",this.hostTree=e}return t.prototype.isRemoved=function(){return this.dataIndex<0},t.prototype.eachNode=function(t,e,n){f(t)&&(n=e,e=t,t=null),G(t=t||{})&&(t={order:t});var i,o=t.order||"preorder",r=this[t.attr||"children"];"preorder"===o&&(i=e.call(n,this));for(var a=0;!i&&a<r.length;a++)r[a].eachNode(t,e,n);"postorder"===o&&e.call(n,this)},t.prototype.updateDepthAndHeight=function(t){var e=0;this.depth=t;for(var n=0;n<this.children.length;n++){var i=this.children[n];i.updateDepthAndHeight(t+1),i.height>e&&(e=i.height)}this.height=e+1},t.prototype.getNodeById=function(t){if(this.getId()===t)return this;for(var e=0,n=this.children,i=n.length;e<i;e++){var o=n[e].getNodeById(t);if(o)return o}},t.prototype.contains=function(t){if(t===this)return!0;for(var e=0,n=this.children,i=n.length;e<i;e++){var o=n[e].contains(t);if(o)return o}},t.prototype.getAncestors=function(t){for(var e=[],n=t?this:this.parentNode;n;)e.push(n),n=n.parentNode;return e.reverse(),e},t.prototype.getAncestorsIndices=function(){for(var t=[],e=this;e;)t.push(e.dataIndex),e=e.parentNode;return t.reverse(),t},t.prototype.getDescendantIndices=function(){var t=[];return this.eachNode((function(e){t.push(e.dataIndex)})),t},t.prototype.getValue=function(t){var e=this.hostTree.data;return e.getStore().get(e.getDimensionIndex(t||"value"),this.dataIndex)},t.prototype.setLayout=function(t,e){this.dataIndex>=0&&this.hostTree.data.setItemLayout(this.dataIndex,t,e)},t.prototype.getLayout=function(){return this.hostTree.data.getItemLayout(this.dataIndex)},t.prototype.getModel=function(t){if(!(this.dataIndex<0))return this.hostTree.data.getItemModel(this.dataIndex).getModel(t)},t.prototype.getLevelModel=function(){return(this.hostTree.levelModels||[])[this.depth]},t.prototype.setVisual=function(t,e){this.dataIndex>=0&&this.hostTree.data.setItemVisual(this.dataIndex,t,e)},t.prototype.getVisual=function(t){return this.hostTree.data.getItemVisual(this.dataIndex,t)},t.prototype.getRawIndex=function(){return this.hostTree.data.getRawIndex(this.dataIndex)},t.prototype.getId=function(){return this.hostTree.data.getId(this.dataIndex)},t.prototype.getChildIndex=function(){if(this.parentNode){for(var t=this.parentNode.children,e=0;e<t.length;++e)if(t[e]===this)return e;return-1}return-1},t.prototype.isAncestorOf=function(t){for(var e=t.parentNode;e;){if(e===this)return!0;e=e.parentNode}return!1},t.prototype.isDescendantOf=function(t){return t!==this&&t.isAncestorOf(this)},t}(),_u=function(){function t(t){this.type="tree",this._nodes=[],this.hostModel=t}return t.prototype.eachNode=function(t,e,n){this.root.eachNode(t,e,n)},t.prototype.getNodeByDataIndex=function(t){var e=this.data.getRawIndex(t);return this._nodes[e]},t.prototype.getNodeById=function(t){return this.root.getNodeById(t)},t.prototype.update=function(){for(var t=this.data,e=this._nodes,n=0,i=e.length;n<i;n++)e[n].dataIndex=-1;for(n=0,i=t.count();n<i;n++)e[t.getRawIndex(n)].dataIndex=n},t.prototype.clearLayouts=function(){this.data.clearItemLayouts()},t.createTree=function(e,n,i){var o=new t(n),r=[],a=1;!function t(e,n){var i=e.value;a=Math.max(a,mt(i)?i.length:1),r.push(e);var s=new xu($e(e.name,""),o);n?function(t,e){var n=e.children;if(t.parentNode===e)return;n.push(t),t.parentNode=e}(s,n):o.root=s,o._nodes.push(s);var l=e.children;if(l)for(var u=0;u<l.length;u++)t(l[u],s)}(e),o.root.updateDepthAndHeight(0);var s=Qe(r,{coordDimensions:["value"],dimensionsCount:a}).dimensions,l=new tn(s,n);return l.initData(r),i&&i(l),cu({mainData:l,struct:o,structAttr:"tree"}),o.update(),o},t}();function bu(t,e,n){if(t&&L(e,t.type)>=0){var i=n.getData().tree.root,o=t.targetNode;if(G(o)&&(o=i.getNodeById(o)),o&&i.contains(o))return{node:o};var r=t.targetNodeId;if(null!=r&&(o=i.getNodeById(r)))return{node:o}}}function Su(t){for(var e=[];t;)(t=t.parentNode)&&e.push(t);return e.reverse()}function wu(t,e){var n=Su(t);return L(n,e)>=0}function Mu(t,e){for(var n=[];t;){var i=t.dataIndex;n.push({name:t.name,dataIndex:i,value:e.getRawValue(i)}),t=t.parentNode}return n.reverse(),n}var Iu=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.hasSymbolVisual=!0,e.ignoreStyleOnData=!0,e}return e(n,t),n.prototype.getInitialData=function(t){var e={name:t.name,children:t.data},n=t.leaves||{},i=new qt(n,this,this.ecModel),o=_u.createTree(e,this,(function(t){t.wrapMethod("getItemModel",(function(t,e){var n=o.getNodeByDataIndex(e);return n&&n.children.length&&n.isExpand||(t.parentModel=i),t}))}));var r=0;o.eachNode("preorder",(function(t){t.depth>r&&(r=t.depth)}));var a=t.expandAndCollapse&&t.initialTreeDepth>=0?t.initialTreeDepth:r;return o.root.eachNode("preorder",(function(t){var e=t.hostTree.data.getRawDataItem(t.dataIndex);t.isExpand=e&&null!=e.collapsed?!e.collapsed:t.depth<=a})),o.data},n.prototype.getOrient=function(){var t=this.get("orient");return"horizontal"===t?t="LR":"vertical"===t&&(t="TB"),t},n.prototype.setZoom=function(t){this.option.zoom=t},n.prototype.setCenter=function(t){this.option.center=t},n.prototype.formatTooltip=function(t,e,n){for(var i=this.getData().tree,o=i.root.children[0],r=i.getNodeByDataIndex(t),a=r.getValue(),s=r.name;r&&r!==o;)s=r.parentNode.name+"."+s,r=r.parentNode;return Ut("nameValue",{name:s,value:a,noValue:isNaN(a)||null==a})},n.prototype.getDataParams=function(e){var n=t.prototype.getDataParams.apply(this,arguments),i=this.getData().tree.getNodeByDataIndex(e);return n.treeAncestors=Mu(i,this),n.collapsed=!i.isExpand,n},n.type="series.tree",n.layoutMode="box",n.defaultOption={z:2,coordinateSystem:"view",left:"12%",top:"12%",right:"12%",bottom:"12%",layout:"orthogonal",edgeShape:"curve",edgeForkPosition:"50%",roam:!1,nodeScaleRatio:.4,center:null,zoom:1,orient:"LR",symbol:"emptyCircle",symbolSize:7,expandAndCollapse:!0,initialTreeDepth:2,lineStyle:{color:"#ccc",width:1.5,curveness:.5},itemStyle:{color:"lightsteelblue",borderWidth:1.5},label:{show:!0},animationEasing:"linear",animationDuration:700,animationDurationUpdate:500},n}(Mt);function Au(t,e){for(var n,i=[t];n=i.pop();)if(e(n),n.isExpand){var o=n.children;if(o.length)for(var r=o.length-1;r>=0;r--)i.push(o[r])}}function Tu(t,e){t.eachSeriesByType("tree",(function(t){!function(t,e){var n=function(t,e){return Fe(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}(t,e);t.layoutInfo=n;var i=t.get("layout"),o=0,r=0,a=null;"radial"===i?(o=2*Math.PI,r=Math.min(n.height,n.width)/2,a=jl((function(t,e){return(t.parentNode===e.parentNode?1:2)/t.depth}))):(o=n.width,r=n.height,a=jl());var s=t.getData().tree.root,l=s.children[0];if(l){!function(t){var e=t;e.hierNode={defaultAncestor:null,ancestor:e,prelim:0,modifier:0,change:0,shift:0,i:0,thread:null};for(var n,i,o=[e];n=o.pop();)if(i=n.children,n.isExpand&&i.length)for(var r=i.length-1;r>=0;r--){var a=i[r];a.hierNode={defaultAncestor:null,ancestor:a,prelim:0,modifier:0,change:0,shift:0,i:r,thread:null},o.push(a)}}(s),function(t,e,n){for(var i,o=[t],r=[];i=o.pop();)if(r.push(i),i.isExpand){var a=i.children;if(a.length)for(var s=0;s<a.length;s++)o.push(a[s])}for(;i=r.pop();)e(i,n)}(l,Xl,a),s.hierNode.modifier=-l.hierNode.prelim,Au(l,Ul);var u=l,d=l,c=l;Au(l,(function(t){var e=t.getLayout().x;e<u.getLayout().x&&(u=t),e>d.getLayout().x&&(d=t),t.depth>c.depth&&(c=t)}));var h=u===d?1:a(u,d)/2,p=h-u.getLayout().x,g=0,f=0,y=0,v=0;if("radial"===i)g=o/(d.getLayout().x+h+p),f=r/(c.depth-1||1),Au(l,(function(t){y=(t.getLayout().x+p)*g,v=(t.depth-1)*f;var e=ql(y,v);t.setLayout({x:e.x,y:e.y,rawX:y,rawY:v},!0)}));else{var m=t.getOrient();"RL"===m||"LR"===m?(f=r/(d.getLayout().x+h+p),g=o/(c.depth-1||1),Au(l,(function(t){v=(t.getLayout().x+p)*f,y="LR"===m?(t.depth-1)*g:o-(t.depth-1)*g,t.setLayout({x:y,y:v},!0)}))):"TB"!==m&&"BT"!==m||(g=o/(d.getLayout().x+h+p),f=r/(c.depth-1||1),Au(l,(function(t){y=(t.getLayout().x+p)*g,v="TB"===m?(t.depth-1)*f:r-(t.depth-1)*f,t.setLayout({x:y,y:v},!0)})))}}}(t,e)}))}function Cu(t){t.eachSeriesByType("tree",(function(t){var e=t.getData();e.tree.eachNode((function(t){var n=t.getModel().getModel("itemStyle").getItemStyle(),i=e.ensureUniqueItemVisual(t.dataIndex,"style");H(i,n)}))}))}var Du=["treemapZoomToNode","treemapRender","treemapMove"];function Lu(t){var e=t.getData().tree,n={};e.eachNode((function(e){for(var i=e;i&&i.depth>1;)i=i.parentNode;var o=en(t.ecModel,i.name||i.dataIndex+"",n);e.setVisual("decal",o)}))}var Pu=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.preventUsingHoverLayer=!0,e}return e(n,t),n.prototype.getInitialData=function(t,e){var n={name:t.name,children:t.data};ku(n);var i=t.levels||[],o=this.designatedVisualItemStyle={},r=new qt({itemStyle:o},this,e);i=t.levels=function(t,e){var n,i,o=nn(e.get("color")),r=nn(e.get(["aria","decal","decals"]));if(!o)return;t=t||[],a(t,(function(t){var e=new qt(t),o=e.get("color"),r=e.get("decal");(e.get(["itemStyle","color"])||o&&"none"!==o)&&(n=!0),(e.get(["itemStyle","decal"])||r&&"none"!==r)&&(i=!0)}));var s=t[0]||(t[0]={});n||(s.color=o.slice());!i&&r&&(s.decal=r.slice());return t}(i,e);var s=d(i||[],(function(t){return new qt(t,r,e)}),this),l=_u.createTree(n,this,(function(t){t.wrapMethod("getItemModel",(function(t,e){var n=l.getNodeByDataIndex(e),i=n?s[n.depth]:null;return t.parentModel=i||r,t}))}));return l.data},n.prototype.optionUpdated=function(){this.resetViewRoot()},n.prototype.formatTooltip=function(t,e,n){var i=this.getData(),o=this.getRawValue(t),r=i.getName(t);return Ut("nameValue",{name:r,value:o})},n.prototype.getDataParams=function(e){var n=t.prototype.getDataParams.apply(this,arguments),i=this.getData().tree.getNodeByDataIndex(e);return n.treeAncestors=Mu(i,this),n.treePathInfo=n.treeAncestors,n},n.prototype.setLayoutInfo=function(t){this.layoutInfo=this.layoutInfo||{},H(this.layoutInfo,t)},n.prototype.mapIdToIndex=function(t){var e=this._idIndexMap;e||(e=this._idIndexMap=Ie(),this._idIndexMapCount=0);var n=e.get(t);return null==n&&e.set(t,n=this._idIndexMapCount++),n},n.prototype.getViewRoot=function(){return this._viewRoot},n.prototype.resetViewRoot=function(t){t?this._viewRoot=t:t=this._viewRoot;var e=this.getRawData().tree.root;t&&(t===e||e.contains(t))||(this._viewRoot=e)},n.prototype.enableAriaDecal=function(){Lu(this)},n.type="series.treemap",n.layoutMode="box",n.defaultOption={progressive:0,left:"center",top:"middle",width:"80%",height:"80%",sort:!0,clipWindow:"origin",squareRatio:.5*(1+Math.sqrt(5)),leafDepth:null,drillDownIcon:"▶",zoomToNodeRatio:.1024,scaleLimit:null,roam:!0,nodeClick:"zoomToNode",animation:!0,animationDurationUpdate:900,animationEasing:"quinticInOut",breadcrumb:{show:!0,height:22,left:"center",top:"bottom",emptyItemWidth:25,itemStyle:{color:"rgba(0,0,0,0.7)",textStyle:{color:"#fff"}},emphasis:{itemStyle:{color:"rgba(0,0,0,0.9)"}}},label:{show:!0,distance:0,padding:5,position:"inside",color:"#fff",overflow:"truncate"},upperLabel:{show:!1,position:[0,"50%"],height:20,overflow:"truncate",verticalAlign:"middle"},itemStyle:{color:null,colorAlpha:null,colorSaturation:null,borderWidth:0,gapWidth:0,borderColor:"#fff",borderColorSaturation:null},emphasis:{upperLabel:{show:!0,position:[0,"50%"],overflow:"truncate",verticalAlign:"middle"}},visualDimension:0,visualMin:null,visualMax:null,color:[],colorAlpha:null,colorSaturation:null,colorMappingBy:"index",visibleMin:10,childrenVisibleMin:null,levels:[]},n}(Mt);function ku(t){var e=0;a(t.children,(function(t){ku(t);var n=t.value;mt(n)&&(n=n[0]),e+=n}));var n=t.value;mt(n)&&(n=n[0]),(null==n||isNaN(n))&&(n=e),n<0&&(n=0),mt(t.value)?t.value[0]=n:t.value=n}var Nu=function(){function t(t){this.group=new It,t.add(this.group)}return t.prototype.render=function(t,e,n,i){var o=t.getModel("breadcrumb"),r=this.group;if(r.removeAll(),o.get("show")&&n){var a=o.getModel("itemStyle"),s=o.getModel("emphasis"),l=a.getModel("textStyle"),u=s.getModel(["itemStyle","textStyle"]),d={pos:{left:o.get("left"),right:o.get("right"),top:o.get("top"),bottom:o.get("bottom")},box:{width:e.getWidth(),height:e.getHeight()},emptyItemWidth:o.get("emptyItemWidth"),totalWidth:0,renderList:[]};this._prepare(n,d,l),this._renderContent(t,d,a,s,l,u,i),on(r,d.pos,d.box)}},t.prototype._prepare=function(t,e,n){for(var i=t;i;i=i.parentNode){var o=$e(i.getModel().get("name"),""),r=n.getTextRect(o),a=Math.max(r.width+16,e.emptyItemWidth);e.totalWidth+=a+8,e.renderList.push({node:i,text:o,width:a})}},t.prototype._renderContent=function(t,e,n,i,o,r,a){for(var s=0,l=e.emptyItemWidth,u=t.get(["breadcrumb","height"]),d=rn(e.pos,e.box),c=e.totalWidth,h=e.renderList,p=i.getModel("itemStyle").getItemStyle(),g=h.length-1;g>=0;g--){var f=h[g],y=f.node,v=f.width,m=f.text;c>d.width&&(c-=v-l,v=l,m=null);var x=new Nt({shape:{points:Ru(s,0,v,u,g===h.length-1,0===g)},style:Ot(n.getItemStyle(),{lineJoin:"bevel"}),textContent:new an({style:sn(o,{text:m})}),textConfig:{position:"inside"},z2:1e4*ke,onclick:Re(a,y)});x.disableLabelAnimation=!0,x.getTextContent().ensureState("emphasis").style=sn(r,{text:m}),x.ensureState("emphasis").style=p,Gt(x,i.get("focus"),i.get("blurScope"),i.get("disabled")),this.group.add(x),Vu(x,t,y),s+=v+8}},t.prototype.remove=function(){this.group.removeAll()},t}();function Ru(t,e,n,i,o,r){var a=[[o?t:t-5,e],[t+n,e],[t+n,e+i],[o?t:t-5,e+i]];return!r&&a.splice(2,0,[t+n+5,e+i/2]),!o&&a.push([t,e+i/2]),a}function Vu(t,e,n){v(t).eventData={componentType:"series",componentSubType:"treemap",componentIndex:e.componentIndex,seriesIndex:e.seriesIndex,seriesName:e.name,seriesType:"treemap",selfType:"breadcrumb",nodeData:{dataIndex:n&&n.dataIndex,name:n&&n.name},treePathInfo:n&&Mu(n,e)}}var Ou=function(){function t(){this._storage=[],this._elExistsMap={}}return t.prototype.add=function(t,e,n,i,o){return!this._elExistsMap[t.id]&&(this._elExistsMap[t.id]=!0,this._storage.push({el:t,target:e,duration:n,delay:i,easing:o}),!0)},t.prototype.finished=function(t){return this._finishedCallback=t,this},t.prototype.start=function(){for(var t=this,e=this._storage.length,n=function(){--e<=0&&(t._storage.length=0,t._elExistsMap={},t._finishedCallback&&t._finishedCallback())},i=0,o=this._storage.length;i<o;i++){var r=this._storage[i];r.el.animateTo(r.target,{duration:r.duration,delay:r.delay,easing:r.easing,setToFinal:!0,done:n,aborted:n})}return this},t}();var Eu=It,zu=pe,Bu="label",Gu="upperLabel",Fu=10*ke,Wu=2*ke,Hu=3*ke,Zu=pn([["fill","color"],["stroke","strokeColor"],["lineWidth","strokeWidth"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),Yu=function(t){var e=Zu(t);return e.stroke=e.fill=e.lineWidth=null,e},Xu=h(),Uu=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e._state="ready",e._storage={nodeGroup:[],background:[],content:[]},e}return e(n,t),n.prototype.render=function(t,e,n,i){var o=e.findComponents({mainType:"series",subType:"treemap",query:i});if(!(L(o,t)<0)){this.seriesModel=t,this.api=n,this.ecModel=e;var r=bu(i,["treemapZoomToNode","treemapRootToNode"],t),a=i&&i.type,s=t.layoutInfo,l=!this._oldTree,u=this._storage,d="treemapRootToNode"===a&&r&&u?{rootNodeGroup:u.nodeGroup[r.node.getRawIndex()],direction:i.direction}:null,c=this._giveContainerGroup(s),h=t.get("animation"),p=this._doRender(c,t,d);!h||l||a&&"treemapZoomToNode"!==a&&"treemapRootToNode"!==a?p.renderFinally():this._doAnimation(c,p,t,d),this._resetController(n),this._renderBreadcrumb(t,n,r)}},n.prototype._giveContainerGroup=function(t){var e=this._containerGroup;return e||(e=this._containerGroup=new Eu,this._initEvents(e),this.group.add(e)),e.x=t.x,e.y=t.y,e},n.prototype._doRender=function(t,e,n){var i=e.getData().tree,o=this._oldTree,r={nodeGroup:[],background:[],content:[]},s={nodeGroup:[],background:[],content:[]},l=this._storage,u=[];function d(t,i,o,a){return function(t,e,n,i,o,r,a,s,l,u){if(!a)return;var d=a.getLayout(),c=t.getData(),h=a.getModel();if(c.setItemGraphicEl(a.dataIndex,null),!d||!d.isInView)return;var p=d.width,g=d.height,f=d.borderWidth,y=d.invisible,m=a.getRawIndex(),x=s&&s.getRawIndex(),_=a.viewChildren,b=d.upperHeight,S=_&&_.length,w=h.getModel("itemStyle"),M=h.getModel(["emphasis","itemStyle"]),I=h.getModel(["blur","itemStyle"]),A=h.getModel(["select","itemStyle"]),T=w.get("borderRadius")||0,C=W("nodeGroup",Eu);if(!C)return;if(l.add(C),C.x=d.x||0,C.y=d.y||0,C.markRedraw(),Xu(C).nodeWidth=p,Xu(C).nodeHeight=g,d.isAboveViewRoot)return C;var D=W("background",zu,u,Wu);D&&E(C,D,S&&d.upperLabelHeight);var L=h.getModel("emphasis"),P=L.get("focus"),k=L.get("blurScope"),N=L.get("disabled"),R="ancestor"===P?a.getAncestorsIndices():"descendant"===P?a.getDescendantIndices():P;if(S)dn(C)&&cn(C,!1),D&&(cn(D,!N),c.setItemGraphicEl(a.dataIndex,D),hn(D,R,k));else{var V=W("content",zu,u,Hu);V&&z(C,V),D.disableMorphing=!0,D&&dn(D)&&cn(D,!1),cn(C,!N),c.setItemGraphicEl(a.dataIndex,C);var O=h.getShallow("cursor");O&&V.attr("cursor",O),hn(C,R,k)}return C;function E(e,n,i){var o=v(n);if(o.dataIndex=a.dataIndex,o.seriesIndex=t.seriesIndex,n.setShape({x:0,y:0,width:p,height:g,r:T}),y)B(n);else{n.invisible=!1;var r=a.getVisual("style"),s=r.stroke,l=Yu(w);l.fill=s;var u=Zu(M);u.fill=M.get("borderColor");var d=Zu(I);d.fill=I.get("borderColor");var c=Zu(A);if(c.fill=A.get("borderColor"),i){var h=p-2*f;G(n,s,r.opacity,{x:f,y:0,width:h,height:b})}else n.removeTextContent();n.setStyle(l),n.ensureState("emphasis").style=u,n.ensureState("blur").style=d,n.ensureState("select").style=c,Ce(n)}e.add(n)}function z(e,n){var i=v(n);i.dataIndex=a.dataIndex,i.seriesIndex=t.seriesIndex;var o=Math.max(p-2*f,0),r=Math.max(g-2*f,0);if(n.culling=!0,n.setShape({x:f,y:f,width:o,height:r,r:T}),y)B(n);else{n.invisible=!1;var s=a.getVisual("style"),l=s.fill,u=Yu(w);u.fill=l,u.decal=s.decal;var d=Zu(M),c=Zu(I),h=Zu(A);G(n,l,s.opacity,null),n.setStyle(u),n.ensureState("emphasis").style=d,n.ensureState("blur").style=c,n.ensureState("select").style=h,Ce(n)}e.add(n)}function B(t){!t.invisible&&r.push(t)}function G(e,n,i,o){var r=h.getModel(o?Gu:Bu),s=$e(h.get("name"),null),l=r.getShallow("show");zt(e,Bt(h,o?Gu:Bu),{defaultText:l?s:null,inheritColor:n,defaultOpacity:i,labelFetcher:t,labelDataIndex:a.dataIndex});var u=e.getTextContent();if(u){var c=u.style,p=gn(c.padding||0);o&&(e.setTextConfig({layoutRect:o}),u.disableLabelLayout=!0),u.beforeUpdate=function(){var t=Math.max((o?o.width:e.shape.width)-p[1]-p[3],0),n=Math.max((o?o.height:e.shape.height)-p[0]-p[2],0);c.width===t&&c.height===n||u.setStyle({width:t,height:n})},c.truncateMinChar=2,c.lineOverflow="truncate",F(c,o,d);var g=u.getState("emphasis");F(g?g.style:null,o,d)}}function F(e,n,i){var o=e?e.text:null;if(!n&&i.isLeafRoot&&null!=o){var r=t.get("drillDownIcon",!0);e.text=r?r+" "+o:o}}function W(t,i,r,a){var s=null!=x&&n[t][x],l=o[t];return s?(n[t][x]=null,Z(l,s)):y||((s=new i)instanceof Te&&(s.z2=function(t,e){return t*Fu+e}(r,a)),Y(l,s)),e[t][m]=s}function Z(t,e){var n=t[m]={};e instanceof Eu?(n.oldX=e.x,n.oldY=e.y):n.oldShape=H({},e.shape)}function Y(t,e){var n=t[m]={},r=a.parentNode,s=e instanceof It;if(r&&(!i||"drillDown"===i.direction)){var l=0,u=0,d=o.background[r.getRawIndex()];!i&&d&&d.oldShape&&(l=d.oldShape.width,u=d.oldShape.height),s?(n.oldX=0,n.oldY=u):n.oldShape={x:l,y:u,width:0,height:0}}n.fadein=!s}}(e,s,l,n,r,u,t,i,o,a)}!function t(e,n,i,o,r){o?(n=e,a(e,(function(t,e){!t.isRemoved()&&l(e,e)}))):new ln(n,e,s,s).add(l).update(l).remove(Re(l,null)).execute();function s(t){return t.getId()}function l(a,s){var l=null!=a?e[a]:null,u=null!=s?n[s]:null,c=d(l,u,i,r);c&&t(l&&l.viewChildren||[],u&&u.viewChildren||[],c,o,r+1)}}(i.root?[i.root]:[],o&&o.root?[o.root]:[],t,i===o||!o,0);var c,h,p=(h={nodeGroup:[],background:[],content:[]},(c=l)&&a(c,(function(t,e){var n=h[e];a(t,(function(t){t&&(n.push(t),Xu(t).willDelete=!0)}))})),h);if(this._oldTree=i,this._storage=s,this._controllerHost){var g=this.seriesModel.layoutInfo,f=i.root.getLayout();f.width===g.width&&f.height===g.height&&(this._controllerHost.zoom=1)}return{lastsForAnimation:r,willDeleteEls:p,renderFinally:function(){a(p,(function(t){a(t,(function(t){t.parent&&t.parent.remove(t)}))})),a(u,(function(t){t.invisible=!0,t.dirty()}))}}},n.prototype._doAnimation=function(t,e,n,i){var o=n.get("animationDurationUpdate"),r=n.get("animationEasing"),s=(f(o)?0:o)||0,l=(f(r)?null:r)||"cubicOut",u=new Ou;a(e.willDeleteEls,(function(t,e){a(t,(function(t,n){if(!t.invisible){var o,r=t.parent,a=Xu(r);if(i&&"drillDown"===i.direction)o=r===i.rootNodeGroup?{shape:{x:0,y:0,width:a.nodeWidth,height:a.nodeHeight},style:{opacity:0}}:{style:{opacity:0}};else{var d=0,c=0;a.willDelete||(d=a.nodeWidth/2,c=a.nodeHeight/2),o="nodeGroup"===e?{x:d,y:c,style:{opacity:0}}:{shape:{x:d,y:c,width:0,height:0},style:{opacity:0}}}o&&u.add(t,o,s,0,l)}}))})),a(this._storage,(function(t,n){a(t,(function(t,i){var o=e.lastsForAnimation[n][i],r={};o&&(t instanceof It?null!=o.oldX&&(r.x=t.x,r.y=t.y,t.x=o.oldX,t.y=o.oldY):(o.oldShape&&(r.shape=H({},t.shape),t.setShape(o.oldShape)),o.fadein?(t.setStyle("opacity",0),r.style={opacity:1}):1!==t.style.opacity&&(r.style={opacity:1})),u.add(t,r,s,0,l))}))}),this),this._state="animating",u.finished(Zt((function(){this._state="ready",e.renderFinally()}),this)).start()},n.prototype._resetController=function(t){var e=this._controller,n=this._controllerHost;n||(this._controllerHost={target:this.group},n=this._controllerHost),e||((e=this._controller=new _s(t.getZr())).enable(this.seriesModel.get("roam")),n.zoomLimit=this.seriesModel.get("scaleLimit"),n.zoom=this.seriesModel.get("zoom"),e.on("pan",Zt(this._onPan,this)),e.on("zoom",Zt(this._onZoom,this)));var i=new s(0,0,t.getWidth(),t.getHeight());e.setPointerChecker((function(t,e,n){return i.contain(e,n)}))},n.prototype._clearController=function(){var t=this._controller;this._controllerHost=null,t&&(t.dispose(),t=null)},n.prototype._onPan=function(t){if("animating"!==this._state&&(Math.abs(t.dx)>3||Math.abs(t.dy)>3)){var e=this.seriesModel.getData().tree.root;if(!e)return;var n=e.getLayout();if(!n)return;this.api.dispatchAction({type:"treemapMove",from:this.uid,seriesId:this.seriesModel.id,rootRect:{x:n.x+t.dx,y:n.y+t.dy,width:n.width,height:n.height}})}},n.prototype._onZoom=function(t){var e=t.originX,n=t.originY,i=t.scale;if("animating"!==this._state){var o=this.seriesModel.getData().tree.root;if(!o)return;var r=o.getLayout();if(!r)return;var a,l=new s(r.x,r.y,r.width,r.height),u=this._controllerHost;a=u.zoomLimit;var d=u.zoom=u.zoom||1;if(d*=i,a){var c=a.min||0,h=a.max||1/0;d=Math.max(Math.min(h,d),c)}var p=d/u.zoom;u.zoom=d;var g=this.seriesModel.layoutInfo;e-=g.x,n-=g.y;var f=Me();we(f,f,[-e,-n]),Se(f,f,[p,p]),we(f,f,[e,n]),l.applyTransform(f),this.api.dispatchAction({type:"treemapRender",from:this.uid,seriesId:this.seriesModel.id,rootRect:{x:l.x,y:l.y,width:l.width,height:l.height}})}},n.prototype._initEvents=function(t){var e=this;t.on("click",(function(t){if("ready"===e._state){var n=e.seriesModel.get("nodeClick",!0);if(n){var i=e.findTarget(t.offsetX,t.offsetY);if(i){var o=i.node;if(o.getLayout().isLeafRoot)e._rootToNode(i);else if("zoomToNode"===n)e._zoomToNode(i);else if("link"===n){var r=o.hostTree.data.getItemModel(o.dataIndex),a=r.get("link",!0),s=r.get("target",!0)||"blank";a&&un(a,s)}}}}}),this)},n.prototype._renderBreadcrumb=function(t,e,n){var i=this;n||(n=null!=t.get("leafDepth",!0)?{node:t.getViewRoot()}:this.findTarget(e.getWidth()/2,e.getHeight()/2))||(n={node:t.getData().tree.root}),(this._breadcrumb||(this._breadcrumb=new Nu(this.group))).render(t,e,n.node,(function(e){"animating"!==i._state&&(wu(t.getViewRoot(),e)?i._rootToNode({node:e}):i._zoomToNode({node:e}))}))},n.prototype.remove=function(){this._clearController(),this._containerGroup&&this._containerGroup.removeAll(),this._storage={nodeGroup:[],background:[],content:[]},this._state="ready",this._breadcrumb&&this._breadcrumb.remove()},n.prototype.dispose=function(){this._clearController()},n.prototype._zoomToNode=function(t){this.api.dispatchAction({type:"treemapZoomToNode",from:this.uid,seriesId:this.seriesModel.id,targetNode:t.node})},n.prototype._rootToNode=function(t){this.api.dispatchAction({type:"treemapRootToNode",from:this.uid,seriesId:this.seriesModel.id,targetNode:t.node})},n.prototype.findTarget=function(t,e){var n;return this.seriesModel.getViewRoot().eachNode({attr:"viewChildren",order:"preorder"},(function(i){var o=this._storage.background[i.getRawIndex()];if(o){var r=o.transformCoordToLocal(t,e),a=o.shape;if(!(a.x<=r[0]&&r[0]<=a.x+a.width&&a.y<=r[1]&&r[1]<=a.y+a.height))return!1;n={node:i,offsetX:r[0],offsetY:r[1]}}}),this),n},n.type="treemap",n}(Dt);var ju=a,qu=xt,Ku=-1,Ju=function(){function t(e){var n=e.mappingMethod,i=e.type,o=this.option=ct(e);this.type=i,this.mappingMethod=n,this._normalizeData=sd[n];var r=t.visualHandlers[i];this.applyVisual=r.applyVisual,this.getColorMapper=r.getColorMapper,this._normalizedToVisual=r._normalizedToVisual[n],"piecewise"===n?($u(o),function(t){var e=t.pieceList;t.hasSpecialVisual=!1,a(e,(function(e,n){e.originIndex=n,null!=e.visual&&(t.hasSpecialVisual=!0)}))}(o)):"category"===n?o.categories?function(t){var e=t.categories,n=t.categoryMap={},i=t.visual;if(ju(e,(function(t,e){n[t]=e})),!mt(i)){var o=[];xt(i)?ju(i,(function(t,e){var i=n[e];o[null!=i?i:Ku]=t})):o[-1]=i,i=ad(t,o)}for(var r=e.length-1;r>=0;r--)null==i[r]&&(delete n[e[r]],e.pop())}(o):$u(o,!0):(ut("linear"!==n||o.dataExtent),$u(o))}return t.prototype.mapValueToVisual=function(t){var e=this._normalizeData(t);return this._normalizedToVisual(e,t)},t.prototype.getNormalizer=function(){return Zt(this._normalizeData,this)},t.listVisualTypes=function(){return y(t.visualHandlers)},t.isValidType=function(e){return t.visualHandlers.hasOwnProperty(e)},t.eachVisual=function(t,e,n){xt(t)?a(t,e,n):e.call(n,t)},t.mapVisual=function(e,n,i){var o,r=mt(e)?[]:xt(e)?{}:(o=!0,null);return t.eachVisual(e,(function(t,e){var a=n.call(i,t,e);o?r=a:r[e]=a})),r},t.retrieveVisuals=function(e){var n,i={};return e&&ju(t.visualHandlers,(function(t,o){e.hasOwnProperty(o)&&(i[o]=e[o],n=!0)})),n?i:null},t.prepareVisualTypes=function(t){if(mt(t))t=t.slice();else{if(!qu(t))return[];var e=[];ju(t,(function(t,n){e.push(n)})),t=e}return t.sort((function(t,e){return"color"===e&&"color"!==t&&0===t.indexOf("color")?1:-1})),t},t.dependsOn=function(t,e){return"color"===e?!(!t||0!==t.indexOf(e)):t===e},t.findPieceIndex=function(t,e,n){for(var i,o=1/0,r=0,a=e.length;r<a;r++){var s=e[r].value;if(null!=s){if(s===t||G(s)&&s===t+"")return r;n&&c(s,r)}}for(r=0,a=e.length;r<a;r++){var l=e[r],u=l.interval,d=l.close;if(u){if(u[0]===-1/0){if(ld(d[1],t,u[1]))return r}else if(u[1]===1/0){if(ld(d[0],u[0],t))return r}else if(ld(d[0],u[0],t)&&ld(d[1],t,u[1]))return r;n&&c(u[0],r),n&&c(u[1],r)}}if(n)return t===1/0?e.length-1:t===-1/0?0:i;function c(e,n){var r=Math.abs(e-t);r<o&&(o=r,i=n)}},t.visualHandlers={color:{applyVisual:ed("color"),getColorMapper:function(){var t=this.option;return Zt("category"===t.mappingMethod?function(t,e){return!e&&(t=this._normalizeData(t)),nd.call(this,t)}:function(e,n,i){var o=!!i;return!n&&(e=this._normalizeData(e)),i=mn(e,t.parsedVisual,i),o?i:vn(i,"rgba")},this)},_normalizedToVisual:{linear:function(t){return vn(mn(t,this.option.parsedVisual),"rgba")},category:nd,piecewise:function(t,e){var n=rd.call(this,e);return null==n&&(n=vn(mn(t,this.option.parsedVisual),"rgba")),n},fixed:id}},colorHue:Qu((function(t,e){return _n(t,e)})),colorSaturation:Qu((function(t,e){return _n(t,null,e)})),colorLightness:Qu((function(t,e){return _n(t,null,null,e)})),colorAlpha:Qu((function(t,e){return xn(t,e)})),decal:{applyVisual:ed("decal"),_normalizedToVisual:{linear:null,category:nd,piecewise:null,fixed:null}},opacity:{applyVisual:ed("opacity"),_normalizedToVisual:od([0,1])},liftZ:{applyVisual:ed("liftZ"),_normalizedToVisual:{linear:id,category:id,piecewise:id,fixed:id}},symbol:{applyVisual:function(t,e,n){n("symbol",this.mapValueToVisual(t))},_normalizedToVisual:{linear:td,category:nd,piecewise:function(t,e){var n=rd.call(this,e);return null==n&&(n=td.call(this,t)),n},fixed:id}},symbolSize:{applyVisual:ed("symbolSize"),_normalizedToVisual:od([0,1])}},t}();function $u(t,e){var n=t.visual,i=[];xt(n)?ju(n,(function(t){i.push(t)})):null!=n&&i.push(n);e||1!==i.length||{color:1,symbol:1}.hasOwnProperty(t.type)||(i[1]=i[0]),ad(t,i)}function Qu(t){return{applyVisual:function(e,n,i){var o=this.mapValueToVisual(e);i("color",t(n("color"),o))},_normalizedToVisual:od([0,1])}}function td(t){var e=this.option.visual;return e[Math.round(fn(t,[0,1],[0,e.length-1],!0))]||{}}function ed(t){return function(e,n,i){i(t,this.mapValueToVisual(e))}}function nd(t){var e=this.option.visual;return e[this.option.loop&&t!==Ku?t%e.length:t]}function id(){return this.option.visual[0]}function od(t){return{linear:function(e){return fn(e,t,this.option.visual,!0)},category:nd,piecewise:function(e,n){var i=rd.call(this,n);return null==i&&(i=fn(e,t,this.option.visual,!0)),i},fixed:id}}function rd(t){var e=this.option,n=e.pieceList;if(e.hasSpecialVisual){var i=n[Ju.findPieceIndex(t,n)];if(i&&i.visual)return i.visual[this.type]}}function ad(t,e){return t.visual=e,"color"===t.type&&(t.parsedVisual=d(e,(function(t){return yn(t)||[0,0,0,1]}))),e}var sd={linear:function(t){return fn(t,this.option.dataExtent,[0,1],!0)},piecewise:function(t){var e=this.option.pieceList,n=Ju.findPieceIndex(t,e,!0);if(null!=n)return fn(n,[0,e.length-1],[0,1],!0)},category:function(t){var e=this.option.categories?this.option.categoryMap[t]:t;return null==e?Ku:e},fixed:St};function ld(t,e,n){return t?e<=n:e<n}var ud=h();const dd={seriesType:"treemap",reset:function(t){var e=t.getData().tree.root;e.isRemoved()||cd(e,{},t.getViewRoot().getAncestors(),t)}};function cd(t,e,n,i){var o=t.getModel(),r=t.getLayout(),s=t.hostTree.data;if(r&&!r.invisible&&r.isInView){var l,u=o.getModel("itemStyle"),d=function(t,e,n){var i=H({},e),o=n.designatedVisualItemStyle;return a(["color","colorAlpha","colorSaturation"],(function(n){o[n]=e[n];var r=t.get(n);o[n]=null,null!=r&&(i[n]=r)})),i}(u,e,i),c=s.ensureUniqueItemVisual(t.dataIndex,"style"),h=u.get("borderColor"),p=u.get("borderColorSaturation");null!=p&&(h=function(t,e){return null!=e?_n(e,null,null,t):null}(p,l=hd(d))),c.stroke=h;var g=t.viewChildren;if(g&&g.length){var f=function(t,e,n,i,o,r){if(!r||!r.length)return;var a=gd(e,"color")||null!=o.color&&"none"!==o.color&&(gd(e,"colorAlpha")||gd(e,"colorSaturation"));if(!a)return;var s=e.get("visualMin"),l=e.get("visualMax"),u=n.dataExtent.slice();null!=s&&s<u[0]&&(u[0]=s),null!=l&&l>u[1]&&(u[1]=l);var d=e.get("colorMappingBy"),c={type:a.name,dataExtent:u,visual:a.range};"color"!==c.type||"index"!==d&&"id"!==d?c.mappingMethod="linear":(c.mappingMethod="category",c.loop=!0);var h=new Ju(c);return ud(h).drColorMappingBy=d,h}(0,o,r,0,d,g);a(g,(function(t,e){if(t.depth>=n.length||t===n[t.depth]){var r=function(t,e,n,i,o,r){var a=H({},e);if(o){var s=o.type,l="color"===s&&ud(o).drColorMappingBy,u="index"===l?i:"id"===l?r.mapIdToIndex(n.getId()):n.getValue(t.get("visualDimension"));a[s]=o.mapValueToVisual(u)}return a}(o,d,t,e,f,i);cd(t,r,n,i)}}))}else l=hd(d),c.fill=l}}function hd(t){var e=pd(t,"color");if(e){var n=pd(t,"colorAlpha"),i=pd(t,"colorSaturation");return i&&(e=_n(e,null,null,i)),n&&(e=xn(e,n)),e}}function pd(t,e){var n=t[e];if(null!=n&&"none"!==n)return n}function gd(t,e){var n=t.get(e);return mt(n)&&n.length?{name:e,range:n}:null}var fd=Math.max,yd=Math.min,vd=Ge,md=a,xd=["itemStyle","borderWidth"],_d=["itemStyle","gapWidth"],bd=["upperLabel","show"],Sd=["upperLabel","height"];const wd={seriesType:"treemap",reset:function(t,e,n,i){var o=n.getWidth(),r=n.getHeight(),a=t.option,l=Fe(t.getBoxLayoutParams(),{width:n.getWidth(),height:n.getHeight()}),u=a.size||[],d=m(vd(l.width,u[0]),o),c=m(vd(l.height,u[1]),r),h=i&&i.type,p=bu(i,["treemapZoomToNode","treemapRootToNode"],t),g="treemapRender"===h||"treemapMove"===h?i.rootRect:null,f=t.getViewRoot(),y=Su(f);if("treemapMove"!==h){var v="treemapZoomToNode"===h?function(t,e,n,i,o){var r,a=(e||{}).node,s=[i,o];if(!a||a===n)return s;var l=i*o,u=l*t.option.zoomToNodeRatio;for(;r=a.parentNode;){for(var d=0,c=r.children,h=0,p=c.length;h<p;h++)d+=c[h].getValue();var g=a.getValue();if(0===g)return s;u*=d/g;var f=r.getModel(),y=f.get(xd);(u+=4*y*y+(3*y+Math.max(y,Cd(f)))*Math.pow(u,.5))>bn&&(u=bn),a=r}u<l&&(u=l);var v=Math.pow(u/l,.5);return[i*v,o*v]}(t,p,f,d,c):g?[g.width,g.height]:[d,c],x=a.sort;x&&"asc"!==x&&"desc"!==x&&(x="desc");var _={squareRatio:a.squareRatio,sort:x,leafDepth:a.leafDepth};f.hostTree.clearLayouts();var b={x:0,y:0,width:v[0],height:v[1],area:v[0]*v[1]};f.setLayout(b),Md(f,_,!1,0),b=f.getLayout(),md(y,(function(t,e){var n=(y[e+1]||f).getValue();t.setLayout(H({dataExtent:[n,n],borderWidth:0,upperHeight:0},b))}))}var S=t.getData().tree.root;S.setLayout(function(t,e,n){if(e)return{x:e.x,y:e.y};var i={x:0,y:0};if(!n)return i;var o=n.node,r=o.getLayout();if(!r)return i;var a=[r.width/2,r.height/2],s=o;for(;s;){var l=s.getLayout();a[0]+=l.x,a[1]+=l.y,s=s.parentNode}return{x:t.width/2-a[0],y:t.height/2-a[1]}}(l,g,p),!0),t.setLayoutInfo(l),Td(S,new s(-l.x,-l.y,o,r),y,f,0)}};function Md(t,e,n,i){var o,r;if(!t.isRemoved()){var a=t.getLayout();o=a.width,r=a.height;var s=t.getModel(),l=s.get(xd),u=s.get(_d)/2,d=Cd(s),h=Math.max(l,d),p=l-u,g=h-u;t.setLayout({borderWidth:l,upperHeight:h,upperLabelHeight:d},!0);var f=(o=fd(o-2*p,0))*(r=fd(r-p-g,0)),y=function(t,e,n,i,o,r){var a=t.children||[],s=i.sort;"asc"!==s&&"desc"!==s&&(s=null);var l=null!=i.leafDepth&&i.leafDepth<=r;if(o&&!l)return t.viewChildren=[];a=c(a,(function(t){return!t.isRemoved()})),function(t,e){e&&t.sort((function(t,n){var i="asc"===e?t.getValue()-n.getValue():n.getValue()-t.getValue();return 0===i?"asc"===e?t.dataIndex-n.dataIndex:n.dataIndex-t.dataIndex:i}))}(a,s);var u=function(t,e,n){for(var i=0,o=0,r=e.length;o<r;o++)i+=e[o].getValue();var a,s=t.get("visualDimension");e&&e.length?"value"===s&&n?(a=[e[e.length-1].getValue(),e[0].getValue()],"asc"===n&&a.reverse()):(a=[1/0,-1/0],md(e,(function(t){var e=t.getValue(s);e<a[0]&&(a[0]=e),e>a[1]&&(a[1]=e)}))):a=[NaN,NaN];return{sum:i,dataExtent:a}}(e,a,s);if(0===u.sum)return t.viewChildren=[];if(u.sum=function(t,e,n,i,o){if(!i)return n;for(var r=t.get("visibleMin"),a=o.length,s=a,l=a-1;l>=0;l--){var u=o["asc"===i?a-l-1:l].getValue();u/n*e<r&&(s=l,n-=u)}return"asc"===i?o.splice(0,a-s):o.splice(s,a-s),n}(e,n,u.sum,s,a),0===u.sum)return t.viewChildren=[];for(var d=0,h=a.length;d<h;d++){var p=a[d].getValue()/u.sum*n;a[d].setLayout({area:p})}l&&(a.length&&t.setLayout({isLeafRoot:!0},!0),a.length=0);return t.viewChildren=a,t.setLayout({dataExtent:u.dataExtent},!0),a}(t,s,f,e,n,i);if(y.length){var v={x:p,y:g,width:o,height:r},m=yd(o,r),x=1/0,_=[];_.area=0;for(var b=0,S=y.length;b<S;){var w=y[b];_.push(w),_.area+=w.getLayout().area;var M=Id(_,m,e.squareRatio);M<=x?(b++,x=M):(_.area-=_.pop().getLayout().area,Ad(_,m,v,u,!1),m=yd(v.width,v.height),_.length=_.area=0,x=1/0)}if(_.length&&Ad(_,m,v,u,!0),!n){var I=s.get("childrenVisibleMin");null!=I&&f<I&&(n=!0)}for(b=0,S=y.length;b<S;b++)Md(y[b],e,n,i+1)}}}function Id(t,e,n){for(var i=0,o=1/0,r=0,a=void 0,s=t.length;r<s;r++)(a=t[r].getLayout().area)&&(a<o&&(o=a),a>i&&(i=a));var l=t.area*t.area,u=e*e*n;return l?fd(u*i/l,l/(u*o)):1/0}function Ad(t,e,n,i,o){var r=e===n.width?0:1,a=1-r,s=["x","y"],l=["width","height"],u=n[s[r]],d=e?t.area/e:0;(o||d>n[l[a]])&&(d=n[l[a]]);for(var c=0,h=t.length;c<h;c++){var p=t[c],g={},f=d?p.getLayout().area/d:0,y=g[l[a]]=fd(d-2*i,0),v=n[s[r]]+n[l[r]]-u,m=c===h-1||v<f?v:f,x=g[l[r]]=fd(m-2*i,0);g[s[a]]=n[s[a]]+yd(i,y/2),g[s[r]]=u+yd(i,x/2),u+=m,p.setLayout(g,!0)}n[s[a]]+=d,n[l[a]]-=d}function Td(t,e,n,i,o){var r=t.getLayout(),a=n[o],l=a&&a===t;if(!(a&&!l||o===n.length&&t!==i)){t.setLayout({isInView:!0,invisible:!l&&!e.intersect(r),isAboveViewRoot:l},!0);var u=new s(e.x-r.x,e.y-r.y,e.width,e.height);md(t.viewChildren||[],(function(t){Td(t,u,n,i,o+1)}))}}function Cd(t){return t.get(bd)?t.get(Sd):0}function Dd(t){var e=t.findComponents({mainType:"legend"});e&&e.length&&t.eachSeriesByType("graph",(function(t){var n=t.getCategoriesData(),i=t.getGraph().data,o=n.mapArray(n.getName);i.filterSelf((function(t){var n=i.getItemModel(t).getShallow("category");if(null!=n){Y(n)&&(n=o[n]);for(var r=0;r<e.length;r++)if(!e[r].isSelected(n))return!1}return!0}))}))}function Ld(t){var e={};t.eachSeriesByType("graph",(function(t){var n=t.getCategoriesData(),i=t.getData(),o={};n.each((function(i){var r=n.getName(i);o["ec-"+r]=i;var a=n.getItemModel(i),s=a.getModel("itemStyle").getItemStyle();s.fill||(s.fill=t.getColorFromPalette(r,e)),n.setItemVisual(i,"style",s);for(var l=["symbol","symbolSize","symbolKeepAspect"],u=0;u<l.length;u++){var d=a.getShallow(l[u],!0);null!=d&&n.setItemVisual(i,l[u],d)}})),n.count()&&i.each((function(t){var e=i.getItemModel(t).getShallow("category");if(null!=e){G(e)&&(e=o["ec-"+e]);var r=n.getItemVisual(e,"style"),a=i.ensureUniqueItemVisual(t,"style");H(a,r);for(var s=["symbol","symbolSize","symbolKeepAspect"],l=0;l<s.length;l++)i.setItemVisual(t,s[l],n.getItemVisual(e,s[l]))}}))}))}function Pd(t){return t instanceof Array||(t=[t,t]),t}function kd(t){t.eachSeriesByType("graph",(function(t){var e=t.getGraph(),n=t.getEdgeData(),i=Pd(t.get("edgeSymbol")),o=Pd(t.get("edgeSymbolSize"));n.setVisual("fromSymbol",i&&i[0]),n.setVisual("toSymbol",i&&i[1]),n.setVisual("fromSymbolSize",o&&o[0]),n.setVisual("toSymbolSize",o&&o[1]),n.setVisual("style",t.getModel("lineStyle").getLineStyle()),n.each((function(t){var i=n.getItemModel(t),o=e.getEdgeByIndex(t),r=Pd(i.getShallow("symbol",!0)),a=Pd(i.getShallow("symbolSize",!0)),s=i.getModel("lineStyle").getLineStyle(),l=n.ensureUniqueItemVisual(t,"style");switch(H(l,s),l.stroke){case"source":var u=o.node1.getVisual("style");l.stroke=u&&u.fill;break;case"target":u=o.node2.getVisual("style");l.stroke=u&&u.fill}r[0]&&o.setVisual("fromSymbol",r[0]),r[1]&&o.setVisual("toSymbol",r[1]),a[0]&&o.setVisual("fromSymbolSize",a[0]),a[1]&&o.setVisual("toSymbolSize",a[1])}))}))}var Nd="--\x3e",Rd=function(t){return t.get("autoCurveness")||null},Vd=function(t,e){var n=Rd(t),i=20,o=[];if(Y(n))i=n;else if(mt(n))return void(t.__curvenessList=n);e>i&&(i=e);var r=i%2?i+2:i+3;o=[];for(var a=0;a<r;a++)o.push((a%2?a+1:a)/10*(a%2?-1:1));t.__curvenessList=o},Od=function(t,e,n){var i=[t.id,t.dataIndex].join("."),o=[e.id,e.dataIndex].join(".");return[n.uid,i,o].join(Nd)},Ed=function(t){var e=t.split(Nd);return[e[0],e[2],e[1]].join(Nd)},zd=function(t,e){var n=e.__edgeMap;return n[t]?n[t].length:0};function Bd(t,e,n,i){var o=Rd(e),r=mt(o);if(!o)return null;var a=function(t,e){var n=Od(t.node1,t.node2,e);return e.__edgeMap[n]}(t,e);if(!a)return null;for(var s=-1,l=0;l<a.length;l++)if(a[l]===n){s=l;break}var u=function(t,e){return zd(Od(t.node1,t.node2,e),e)+zd(Od(t.node2,t.node1,e),e)}(t,e);Vd(e,u),t.lineStyle=t.lineStyle||{};var d=Od(t.node1,t.node2,e),c=e.__curvenessList,h=r||u%2?0:1;if(a.isForward)return c[h+s];var p=Ed(d),g=zd(p,e),f=c[s+g+h];return i?r?o&&0===o[0]?(g+h)%2?f:-f:((g%2?0:1)+h)%2?f:-f:(g+h)%2?f:-f:c[s+g+h]}function Gd(t){var e=t.coordinateSystem;if(!e||"view"===e.type){var n=t.getGraph();n.eachNode((function(t){var e=t.getModel();t.setLayout([+e.get("x"),+e.get("y")])})),Fd(n,t)}}function Fd(t,e){t.eachEdge((function(t,n){var i=Sn(t.getModel().get(["lineStyle","curveness"]),-Bd(t,e,n,!0),0),o=wn(t.node1.getLayout()),r=wn(t.node2.getLayout()),a=[o,r];+i&&a.push([(o[0]+r[0])/2-(o[1]-r[1])*i,(o[1]+r[1])/2-(r[0]-o[0])*i]),t.setLayout(a)}))}function Wd(t,e){t.eachSeriesByType("graph",(function(t){var e=t.get("layout"),n=t.coordinateSystem;if(n&&"view"!==n.type){var i=t.getData(),o=[];a(n.dimensions,(function(t){o=o.concat(i.mapDimensionsAll(t))}));for(var r=0;r<i.count();r++){for(var s=[],l=!1,u=0;u<o.length;u++){var d=i.get(o[u],r);isNaN(d)||(l=!0),s.push(d)}l?i.setItemLayout(r,n.dataToPoint(s)):i.setItemLayout(r,[NaN,NaN])}Fd(i.graph,t)}else e&&"none"!==e||Gd(t)}))}function Hd(t){var e=t.coordinateSystem;if("view"!==e.type)return 1;var n=t.option.nodeScaleRatio,i=e.scaleX;return((e.getZoom()-1)*n+1)/i}function Zd(t){var e=t.getVisual("symbolSize");return e instanceof Array&&(e=(e[0]+e[1])/2),+e}var Yd=Math.PI,Xd=[];function Ud(t,e,n,i){var o=t.coordinateSystem;if(!o||"view"===o.type){var r=o.getBoundingRect(),a=t.getData(),s=a.graph,l=r.width/2+r.x,u=r.height/2+r.y,d=Math.min(r.width,r.height)/2,c=a.count();if(a.setLayout({cx:l,cy:u}),c){if(n){var h=o.pointToData(i),p=h[0],g=h[1],f=[p-l,g-u];Mn(f,f),In(f,f,d),n.setLayout([l+f[0],u+f[1]],!0),qd(n,t.get(["circular","rotateLabel"]),l,u)}jd[e](t,s,a,d,l,u,c),s.eachEdge((function(e,n){var i,o=Sn(e.getModel().get(["lineStyle","curveness"]),Bd(e,t,n),0),r=wn(e.node1.getLayout()),a=wn(e.node2.getLayout()),s=(r[0]+a[0])/2,d=(r[1]+a[1])/2;+o&&(i=[l*(o*=3)+s*(1-o),u*o+d*(1-o)]),e.setLayout([r,a,i])}))}}}var jd={value:function(t,e,n,i,o,r,a){var s=0,l=n.getSum("value"),u=2*Math.PI/(l||a);e.eachNode((function(t){var e=t.getValue("value"),n=u*(l?e:1)/2;s+=n,t.setLayout([i*Math.cos(s)+o,i*Math.sin(s)+r]),s+=n}))},symbolSize:function(t,e,n,i,o,r,a){var s=0;Xd.length=a;var l=Hd(t);e.eachNode((function(t){var e=Zd(t);isNaN(e)&&(e=2),e<0&&(e=0),e*=l;var n=Math.asin(e/2/i);isNaN(n)&&(n=Yd/2),Xd[t.dataIndex]=n,s+=2*n}));var u=(2*Yd-s)/a/2,d=0;e.eachNode((function(t){var e=u+Xd[t.dataIndex];d+=e,(!t.getLayout()||!t.getLayout().fixed)&&t.setLayout([i*Math.cos(d)+o,i*Math.sin(d)+r]),d+=e}))}};function qd(t,e,n,i){var o=t.getGraphicEl();if(o){var r=t.getModel().get(["label","rotate"])||0,a=o.getSymbolPath();if(e){var s=t.getLayout(),l=Math.atan2(s[1]-i,s[0]-n);l<0&&(l=2*Math.PI+l);var u=s[0]<n;u&&(l-=Math.PI);var d=u?"left":"right";a.setTextConfig({rotation:-l,position:d,origin:"center"});var c=a.ensureState("emphasis");H(c.textConfig||(c.textConfig={}),{position:d})}else a.setTextConfig({rotation:r*=Math.PI/180})}}function Kd(t){t.eachSeriesByType("graph",(function(t){"circular"===t.get("layout")&&Ud(t,"symbolSize")}))}var Jd=Dn;function $d(t){t.eachSeriesByType("graph",(function(t){var e=t.coordinateSystem;if(!e||"view"===e.type)if("force"===t.get("layout")){var n=t.preservedPoints||{},i=t.getGraph(),o=i.data,r=i.edgeData,a=t.getModel("force"),s=a.get("initLayout");t.preservedPoints?o.each((function(t){var e=o.getId(t);o.setItemLayout(t,n[e]||[NaN,NaN])})):s&&"none"!==s?"circular"===s&&Ud(t,"value"):Gd(t);var l=o.getDataExtent("value"),u=r.getDataExtent("value"),d=a.get("repulsion"),c=a.get("edgeLength"),h=mt(d)?d:[d,d],p=mt(c)?c:[c,c];p=[p[1],p[0]];var g=o.mapArray("value",(function(t,e){var n=o.getItemLayout(e),i=fn(t,l,h);return isNaN(i)&&(i=(h[0]+h[1])/2),{w:i,rep:i,fixed:o.getItemModel(e).get("fixed"),p:!n||isNaN(n[0])||isNaN(n[1])?null:n}})),f=r.mapArray("value",(function(e,n){var o=i.getEdgeByIndex(n),r=fn(e,u,p);isNaN(r)&&(r=(p[0]+p[1])/2);var a=o.getModel(),s=Sn(o.getModel().get(["lineStyle","curveness"]),-Bd(o,t,n,!0),0);return{n1:g[o.node1.dataIndex],n2:g[o.node2.dataIndex],d:r,curveness:s,ignoreForceLayout:a.get("ignoreForceLayout")}})),y=e.getBoundingRect(),v=function(t,e,n){for(var i=t,o=e,r=n.rect,a=r.width,s=r.height,l=[r.x+a/2,r.y+s/2],u=null==n.gravity?.1:n.gravity,d=0;d<i.length;d++){var c=i[d];c.p||(c.p=An(a*(Math.random()-.5)+l[0],s*(Math.random()-.5)+l[1])),c.pp=wn(c.p),c.edges=null}var h,p,g=null==n.friction?.6:n.friction,f=g;return{warmUp:function(){f=.8*g},setFixed:function(t){i[t].fixed=!0},setUnfixed:function(t){i[t].fixed=!1},beforeStep:function(t){h=t},afterStep:function(t){p=t},step:function(t){h&&h(i,o);for(var e=[],n=i.length,r=0;r<o.length;r++){var a=o[r];if(!a.ignoreForceLayout){var s=a.n1,d=a.n2;Tn(e,d.p,s.p);var c=Cn(e)-a.d,g=d.w/(s.w+d.w);isNaN(g)&&(g=0),Mn(e,e),!s.fixed&&Jd(s.p,s.p,e,g*c*f),!d.fixed&&Jd(d.p,d.p,e,-(1-g)*c*f)}}for(r=0;r<n;r++)(x=i[r]).fixed||(Tn(e,l,x.p),Jd(x.p,x.p,e,u*f));for(r=0;r<n;r++){s=i[r];for(var y=r+1;y<n;y++){d=i[y],Tn(e,d.p,s.p),0===(c=Cn(e))&&(Ln(e,Math.random()-.5,Math.random()-.5),c=1);var v=(s.rep+d.rep)/c/c;!s.fixed&&Jd(s.pp,s.pp,e,v),!d.fixed&&Jd(d.pp,d.pp,e,-v)}}var m=[];for(r=0;r<n;r++){var x;(x=i[r]).fixed||(Tn(m,x.p,x.pp),Jd(x.p,x.p,m,f),Ee(x.pp,x.p))}var _=(f*=.992)<.01;p&&p(i,o,_),t&&t(_)}}}(g,f,{rect:y,gravity:a.get("gravity"),friction:a.get("friction")});v.beforeStep((function(t,e){for(var n=0,o=t.length;n<o;n++)t[n].fixed&&Ee(t[n].p,i.getNodeByIndex(n).getLayout())})),v.afterStep((function(t,e,r){for(var a=0,s=t.length;a<s;a++)t[a].fixed||i.getNodeByIndex(a).setLayout(t[a].p),n[o.getId(a)]=t[a].p;for(a=0,s=e.length;a<s;a++){var l=e[a],u=i.getEdgeByIndex(a),d=l.n1.p,c=l.n2.p,h=u.getLayout();(h=h?h.slice():[])[0]=h[0]||[],h[1]=h[1]||[],Ee(h[0],d),Ee(h[1],c),+l.curveness&&(h[2]=[(d[0]+c[0])/2-(d[1]-c[1])*l.curveness,(d[1]+c[1])/2-(c[0]-d[0])*l.curveness]),u.setLayout(h)}})),t.forceLayout=v,t.preservedPoints=n,v.step()}else t.forceLayout=null}))}function Qd(t,e){var n=[];return t.eachSeriesByType("graph",(function(t){var i=t.get("coordinateSystem");if(!i||"view"===i){var o=t.getData(),r=o.mapArray((function(t){var e=o.getItemModel(t);return[+e.get("x"),+e.get("y")]})),a=[],s=[];Xe(r,a,s),s[0]-a[0]==0&&(s[0]+=1,a[0]-=1),s[1]-a[1]==0&&(s[1]+=1,a[1]-=1);var l=(s[0]-a[0])/(s[1]-a[1]),u=function(t,e,n){var i=H(t.getBoxLayoutParams(),{aspect:n});return Fe(i,{width:e.getWidth(),height:e.getHeight()})}(t,e,l);isNaN(l)&&(a=[u.x,u.y],s=[u.x+u.width,u.y+u.height]);var d=s[0]-a[0],c=s[1]-a[1],h=u.width,p=u.height,g=t.coordinateSystem=new Pl;g.zoomLimit=t.get("scaleLimit"),g.setBoundingRect(a[0],a[1],d,c),g.setViewRect(u.x,u.y,h,p),g.setCenter(t.get("center"),e),g.setZoom(t.get("zoom")),n.push(g)}})),n}var tc=ve.prototype,ec=Je.prototype,nc=function(){return function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}}();function ic(t){return isNaN(+t.cpx1)||isNaN(+t.cpy1)}!function(t){function n(){return null!==t&&t.apply(this,arguments)||this}e(n,t)}(nc);var oc=function(t){function n(e){var n=t.call(this,e)||this;return n.type="ec-line",n}return e(n,t),n.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},n.prototype.getDefaultShape=function(){return new nc},n.prototype.buildPath=function(t,e){ic(e)?tc.buildPath.call(this,t,e):ec.buildPath.call(this,t,e)},n.prototype.pointAt=function(t){return ic(this.shape)?tc.pointAt.call(this,t):ec.pointAt.call(this,t)},n.prototype.tangentAt=function(t){var e=this.shape,n=ic(e)?[e.x2-e.x1,e.y2-e.y1]:ec.tangentAt.call(this,t);return Mn(n,n)},n}(q),rc=["fromSymbol","toSymbol"];function ac(t){return"_"+t+"Type"}function sc(t,e,n){var i=e.getItemVisual(n,t);if(!i||"none"===i)return i;var o=e.getItemVisual(n,t+"Size"),r=e.getItemVisual(n,t+"Rotate"),a=e.getItemVisual(n,t+"Offset"),s=e.getItemVisual(n,t+"KeepAspect"),l=Wt(o);return i+l+Vn(a||0,l)+(r||"")+(s||"")}function lc(t,e,n){var i=e.getItemVisual(n,t);if(i&&"none"!==i){var o=e.getItemVisual(n,t+"Size"),r=e.getItemVisual(n,t+"Rotate"),a=e.getItemVisual(n,t+"Offset"),s=e.getItemVisual(n,t+"KeepAspect"),l=Wt(o),u=Vn(a||0,l),d=At(i,-l[0]/2+u[0],-l[1]/2+u[1],l[0],l[1],null,s);return d.__specifiedRotation=null==r||isNaN(r)?void 0:+r*Math.PI/180||0,d.name=t,d}}function uc(t,e){t.x1=e[0][0],t.y1=e[0][1],t.x2=e[1][0],t.y2=e[1][1],t.percent=1;var n=e[2];n?(t.cpx1=n[0],t.cpy1=n[1]):(t.cpx1=NaN,t.cpy1=NaN)}var dc=function(t){function n(e,n,i){var o=t.call(this)||this;return o._createLine(e,n,i),o}return e(n,t),n.prototype._createLine=function(t,e,n){var i=t.hostModel,o=function(t){var e=new oc({name:"line",subPixelOptimize:!0});return uc(e.shape,t),e}(t.getItemLayout(e));o.shape.percent=0,D(o,{shape:{percent:1}},i,e),this.add(o),a(rc,(function(n){var i=lc(n,t,e);this.add(i),this[ac(n)]=sc(n,t,e)}),this),this._updateCommonStl(t,e,n)},n.prototype.updateData=function(t,e,n){var i=t.hostModel,o=this.childOfName("line"),r=t.getItemLayout(e),s={shape:{}};uc(s.shape,r),P(o,s,i,e),a(rc,(function(n){var i=sc(n,t,e),o=ac(n);if(this[o]!==i){this.remove(this.childOfName(n));var r=lc(n,t,e);this.add(r)}this[o]=i}),this),this._updateCommonStl(t,e,n)},n.prototype.getLinePath=function(){return this.childAt(0)},n.prototype._updateCommonStl=function(t,e,n){var i=t.hostModel,o=this.childOfName("line"),r=n&&n.emphasisLineStyle,s=n&&n.blurLineStyle,l=n&&n.selectLineStyle,u=n&&n.labelStatesModels,d=n&&n.emphasisDisabled,c=n&&n.focus,h=n&&n.blurScope;if(!n||t.hasItemOption){var p=t.getItemModel(e),g=p.getModel("emphasis");r=g.getModel("lineStyle").getLineStyle(),s=p.getModel(["blur","lineStyle"]).getLineStyle(),l=p.getModel(["select","lineStyle"]).getLineStyle(),d=g.get("disabled"),c=g.get("focus"),h=g.get("blurScope"),u=Bt(p)}var f=t.getItemVisual(e,"style"),y=f.stroke;o.useStyle(f),o.style.fill=null,o.style.strokeNoScale=!0,o.ensureState("emphasis").style=r,o.ensureState("blur").style=s,o.ensureState("select").style=l,a(rc,(function(t){var e=this.childOfName(t);if(e){e.setColor(y),e.style.opacity=f.opacity;for(var n=0;n<Pn.length;n++){var i=Pn[n],r=o.getState(i);if(r){var a=r.style||{},s=e.ensureState(i),l=s.style||(s.style={});null!=a.stroke&&(l[e.__isEmptyBrush?"stroke":"fill"]=a.stroke),null!=a.opacity&&(l.opacity=a.opacity)}}e.markRedraw()}}),this);var v=i.getRawValue(e);zt(this,u,{labelDataIndex:e,labelFetcher:{getFormattedLabel:function(e,n){return i.getFormattedLabel(e,n,t.dataType)}},inheritColor:y||"#000",defaultOpacity:f.opacity,defaultText:(null==v?t.getName(e):isFinite(v)?kn(v):v)+""});var m=this.getTextContent();if(m){var x=u.normal;m.__align=m.style.align,m.__verticalAlign=m.style.verticalAlign,m.__position=x.get("position")||"middle";var _=x.get("distance");mt(_)||(_=[_,_]),m.__labelDistance=_}this.setTextConfig({position:null,local:!0,inside:!1}),Gt(this,c,h,d)},n.prototype.highlight=function(){Nn(this)},n.prototype.downplay=function(){Rn(this)},n.prototype.updateLayout=function(t,e){this.setLinePoints(t.getItemLayout(e))},n.prototype.setLinePoints=function(t){var e=this.childOfName("line");uc(e.shape,t),e.dirty()},n.prototype.beforeUpdate=function(){var t=this,e=t.childOfName("fromSymbol"),n=t.childOfName("toSymbol"),i=t.getTextContent();if(e||n||i&&!i.ignore){for(var o=1,r=this.parent;r;)r.scaleX&&(o/=r.scaleX),r=r.parent;var a=t.childOfName("line");if(this.__dirty||a.__dirty){var s=a.shape.percent,l=a.pointAt(0),u=a.pointAt(s),d=Tn([],u,l);if(Mn(d,d),e&&(e.setPosition(l),w(e,0),e.scaleX=e.scaleY=o*s,e.markRedraw()),n&&(n.setPosition(u),w(n,1),n.scaleX=n.scaleY=o*s,n.markRedraw()),i&&!i.ignore){i.x=i.y=0,i.originX=i.originY=0;var c=void 0,h=void 0,p=i.__labelDistance,g=p[0]*o,f=p[1]*o,y=s/2,v=a.tangentAt(y),m=[v[1],-v[0]],x=a.pointAt(y);m[1]>0&&(m[0]=-m[0],m[1]=-m[1]);var _=v[0]<0?-1:1;if("start"!==i.__position&&"end"!==i.__position){var b=-Math.atan2(v[1],v[0]);u[0]<l[0]&&(b=Math.PI+b),i.rotation=b}var S=void 0;switch(i.__position){case"insideStartTop":case"insideMiddleTop":case"insideEndTop":case"middle":S=-f,h="bottom";break;case"insideStartBottom":case"insideMiddleBottom":case"insideEndBottom":S=f,h="top";break;default:S=0,h="middle"}switch(i.__position){case"end":i.x=d[0]*g+u[0],i.y=d[1]*f+u[1],c=d[0]>.8?"left":d[0]<-.8?"right":"center",h=d[1]>.8?"top":d[1]<-.8?"bottom":"middle";break;case"start":i.x=-d[0]*g+l[0],i.y=-d[1]*f+l[1],c=d[0]>.8?"right":d[0]<-.8?"left":"center",h=d[1]>.8?"bottom":d[1]<-.8?"top":"middle";break;case"insideStartTop":case"insideStart":case"insideStartBottom":i.x=g*_+l[0],i.y=l[1]+S,c=v[0]<0?"right":"left",i.originX=-g*_,i.originY=-S;break;case"insideMiddleTop":case"insideMiddle":case"insideMiddleBottom":case"middle":i.x=x[0],i.y=x[1]+S,c="center",i.originY=-S;break;case"insideEndTop":case"insideEnd":case"insideEndBottom":i.x=-g*_+u[0],i.y=u[1]+S,c=v[0]>=0?"right":"left",i.originX=g*_,i.originY=-S}i.scaleX=i.scaleY=o,i.setStyle({verticalAlign:i.__verticalAlign||h,align:i.__align||c})}}}function w(t,e){var n=t.__specifiedRotation;if(null==n){var i=a.tangentAt(e);t.attr("rotation",(1===e?-1:1)*Math.PI/2-Math.atan2(i[1],i[0]))}else t.attr("rotation",n)}},n}(It),cc=function(){function t(t){this.group=new It,this._LineCtor=t||dc}return t.prototype.updateData=function(t){var e=this;this._progressiveEls=null;var n=this,i=n.group,o=n._lineData;n._lineData=t,o||i.removeAll();var r=hc(t);t.diff(o).add((function(n){e._doAdd(t,n,r)})).update((function(n,i){e._doUpdate(o,t,i,n,r)})).remove((function(t){i.remove(o.getItemGraphicEl(t))})).execute()},t.prototype.updateLayout=function(){var t=this._lineData;t&&t.eachItemGraphicEl((function(e,n){e.updateLayout(t,n)}),this)},t.prototype.incrementalPrepareUpdate=function(t){this._seriesScope=hc(t),this._lineData=null,this.group.removeAll()},t.prototype.incrementalUpdate=function(t,e){function n(t){t.isGroup||function(t){return t.animators&&t.animators.length>0}(t)||(t.incremental=!0,t.ensureState("emphasis").hoverLayer=!0)}this._progressiveEls=[];for(var i=t.start;i<t.end;i++){if(gc(e.getItemLayout(i))){var o=new this._LineCtor(e,i,this._seriesScope);o.traverse(n),this.group.add(o),e.setItemGraphicEl(i,o),this._progressiveEls.push(o)}}},t.prototype.remove=function(){this.group.removeAll()},t.prototype.eachRendered=function(t){On(this._progressiveEls||this.group,t)},t.prototype._doAdd=function(t,e,n){if(gc(t.getItemLayout(e))){var i=new this._LineCtor(t,e,n);t.setItemGraphicEl(e,i),this.group.add(i)}},t.prototype._doUpdate=function(t,e,n,i,o){var r=t.getItemGraphicEl(n);gc(e.getItemLayout(i))?(r?r.updateData(e,i,o):r=new this._LineCtor(e,i,o),e.setItemGraphicEl(i,r),this.group.add(r)):this.group.remove(r)},t}();function hc(t){var e=t.hostModel,n=e.getModel("emphasis");return{lineStyle:e.getModel("lineStyle").getLineStyle(),emphasisLineStyle:n.getModel(["lineStyle"]).getLineStyle(),blurLineStyle:e.getModel(["blur","lineStyle"]).getLineStyle(),selectLineStyle:e.getModel(["select","lineStyle"]).getLineStyle(),emphasisDisabled:n.get("disabled"),blurScope:n.get("blurScope"),focus:n.get("focus"),labelStatesModels:Bt(e)}}function pc(t){return isNaN(t[0])||isNaN(t[1])}function gc(t){return t&&!pc(t[0])&&!pc(t[1])}var fc=[],yc=[],vc=[],mc=zn,xc=Bn,_c=Math.abs;function bc(t,e,n){for(var i,o=t[0],r=t[1],a=t[2],s=1/0,l=n*n,u=.1,d=.1;d<=.9;d+=.1){fc[0]=mc(o[0],r[0],a[0],d),fc[1]=mc(o[1],r[1],a[1],d),(p=_c(xc(fc,e)-l))<s&&(s=p,i=d)}for(var c=0;c<32;c++){var h=i+u;yc[0]=mc(o[0],r[0],a[0],i),yc[1]=mc(o[1],r[1],a[1],i),vc[0]=mc(o[0],r[0],a[0],h),vc[1]=mc(o[1],r[1],a[1],h);var p=xc(yc,e)-l;if(_c(p)<.01)break;var g=xc(vc,e)-l;u/=2,p<0?g>=0?i+=u:i-=u:g>=0?i-=u:i+=u}return i}function Sc(t,e){var n=[],i=En,o=[[],[],[]],r=[[],[]],a=[];e/=2,t.eachEdge((function(t,s){var l=t.getLayout(),u=t.getVisual("fromSymbol"),d=t.getVisual("toSymbol");l.__original||(l.__original=[wn(l[0]),wn(l[1])],l[2]&&l.__original.push(wn(l[2])));var c=l.__original;if(null!=l[2]){if(Ee(o[0],c[0]),Ee(o[1],c[2]),Ee(o[2],c[1]),u&&"none"!==u){var h=Zd(t.node1),p=bc(o,c[0],h*e);i(o[0][0],o[1][0],o[2][0],p,n),o[0][0]=n[3],o[1][0]=n[4],i(o[0][1],o[1][1],o[2][1],p,n),o[0][1]=n[3],o[1][1]=n[4]}if(d&&"none"!==d){h=Zd(t.node2),p=bc(o,c[1],h*e);i(o[0][0],o[1][0],o[2][0],p,n),o[1][0]=n[1],o[2][0]=n[2],i(o[0][1],o[1][1],o[2][1],p,n),o[1][1]=n[1],o[2][1]=n[2]}Ee(l[0],o[0]),Ee(l[1],o[2]),Ee(l[2],o[1])}else{if(Ee(r[0],c[0]),Ee(r[1],c[1]),Tn(a,r[1],r[0]),Mn(a,a),u&&"none"!==u){h=Zd(t.node1);Dn(r[0],r[0],a,h*e)}if(d&&"none"!==d){h=Zd(t.node2);Dn(r[1],r[1],a,-h*e)}Ee(l[0],r[0]),Ee(l[1],r[1])}}))}function wc(t){return"view"===t.type}var Mc=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.init=function(t,e){var n=new Ct,i=new cc,o=this.group;this._controller=new _s(e.getZr()),this._controllerHost={target:o},o.add(n.group),o.add(i.group),this._symbolDraw=n,this._lineDraw=i,this._firstRender=!0},n.prototype.render=function(t,e,n){var i=this,o=t.coordinateSystem;this._model=t;var r=this._symbolDraw,a=this._lineDraw,s=this.group;if(wc(o)){var l={x:o.x,y:o.y,scaleX:o.scaleX,scaleY:o.scaleY};this._firstRender?s.attr(l):P(s,l,t)}Sc(t.getGraph(),Hd(t));var u=t.getData();r.updateData(u);var d=t.getEdgeData();a.updateData(d),this._updateNodeAndLinkScale(),this._updateController(t,e,n),clearTimeout(this._layoutTimeout);var c=t.forceLayout,h=t.get(["force","layoutAnimation"]);c&&this._startForceLayoutIteration(c,h);var p=t.get("layout");u.graph.eachNode((function(e){var n=e.dataIndex,o=e.getGraphicEl(),r=e.getModel();if(o){o.off("drag").off("dragend");var a=r.get("draggable");a&&o.on("drag",(function(r){switch(p){case"force":c.warmUp(),!i._layouting&&i._startForceLayoutIteration(c,h),c.setFixed(n),u.setItemLayout(n,[o.x,o.y]);break;case"circular":u.setItemLayout(n,[o.x,o.y]),e.setLayout({fixed:!0},!0),Ud(t,"symbolSize",e,[r.offsetX,r.offsetY]),i.updateLayout(t);break;default:u.setItemLayout(n,[o.x,o.y]),Fd(t.getGraph(),t),i.updateLayout(t)}})).on("dragend",(function(){c&&c.setUnfixed(n)})),o.setDraggable(a,!!r.get("cursor")),"adjacency"===r.get(["emphasis","focus"])&&(v(o).focus=e.getAdjacentDataIndices())}})),u.graph.eachEdge((function(t){var e=t.getGraphicEl(),n=t.getModel().get(["emphasis","focus"]);e&&"adjacency"===n&&(v(e).focus={edge:[t.dataIndex],node:[t.node1.dataIndex,t.node2.dataIndex]})}));var g="circular"===t.get("layout")&&t.get(["circular","rotateLabel"]),f=u.getLayout("cx"),y=u.getLayout("cy");u.graph.eachNode((function(t){qd(t,g,f,y)})),this._firstRender=!1},n.prototype.dispose=function(){this.remove(),this._controller&&this._controller.dispose(),this._controllerHost=null},n.prototype._startForceLayoutIteration=function(t,e){var n=this;!function i(){t.step((function(t){n.updateLayout(n._model),(n._layouting=!t)&&(e?n._layoutTimeout=setTimeout(i,16):i())}))}()},n.prototype._updateController=function(t,e,n){var i=this,o=this._controller,r=this._controllerHost,a=this.group;o.setPointerChecker((function(e,i,o){var r=a.getBoundingRect();return r.applyTransform(a.transform),r.contain(i,o)&&!Cs(e,n,t)})),wc(t.coordinateSystem)?(o.enable(t.get("roam")),r.zoomLimit=t.get("scaleLimit"),r.zoom=t.coordinateSystem.getZoom(),o.off("pan").off("zoom").on("pan",(function(e){Ms(r,e.dx,e.dy),n.dispatchAction({seriesId:t.id,type:"graphRoam",dx:e.dx,dy:e.dy})})).on("zoom",(function(e){Is(r,e.scale,e.originX,e.originY),n.dispatchAction({seriesId:t.id,type:"graphRoam",zoom:e.scale,originX:e.originX,originY:e.originY}),i._updateNodeAndLinkScale(),Sc(t.getGraph(),Hd(t)),i._lineDraw.updateLayout(),n.updateLabelLayout()}))):o.disable()},n.prototype._updateNodeAndLinkScale=function(){var t=this._model,e=t.getData(),n=Hd(t);e.eachItemGraphicEl((function(t,e){t&&t.setSymbolScale(n)}))},n.prototype.updateLayout=function(t){Sc(t.getGraph(),Hd(t)),this._symbolDraw.updateLayout(),this._lineDraw.updateLayout()},n.prototype.remove=function(){clearTimeout(this._layoutTimeout),this._layouting=!1,this._layoutTimeout=null,this._symbolDraw&&this._symbolDraw.remove(),this._lineDraw&&this._lineDraw.remove()},n.type="graph",n}(Dt);function Ic(t){return"_EC_"+t}var Ac=function(){function t(t){this.type="graph",this.nodes=[],this.edges=[],this._nodesMap={},this._edgesMap={},this._directed=t||!1}return t.prototype.isDirected=function(){return this._directed},t.prototype.addNode=function(t,e){t=null==t?""+e:""+t;var n=this._nodesMap;if(!n[Ic(t)]){var i=new Tc(t,e);return i.hostGraph=this,this.nodes.push(i),n[Ic(t)]=i,i}},t.prototype.getNodeByIndex=function(t){var e=this.data.getRawIndex(t);return this.nodes[e]},t.prototype.getNodeById=function(t){return this._nodesMap[Ic(t)]},t.prototype.addEdge=function(t,e,n){var i=this._nodesMap,o=this._edgesMap;if(Y(t)&&(t=this.nodes[t]),Y(e)&&(e=this.nodes[e]),t instanceof Tc||(t=i[Ic(t)]),e instanceof Tc||(e=i[Ic(e)]),t&&e){var r=t.id+"-"+e.id,a=new Cc(t,e,n);return a.hostGraph=this,this._directed&&(t.outEdges.push(a),e.inEdges.push(a)),t.edges.push(a),t!==e&&e.edges.push(a),this.edges.push(a),o[r]=a,a}},t.prototype.getEdgeByIndex=function(t){var e=this.edgeData.getRawIndex(t);return this.edges[e]},t.prototype.getEdge=function(t,e){t instanceof Tc&&(t=t.id),e instanceof Tc&&(e=e.id);var n=this._edgesMap;return this._directed?n[t+"-"+e]:n[t+"-"+e]||n[e+"-"+t]},t.prototype.eachNode=function(t,e){for(var n=this.nodes,i=n.length,o=0;o<i;o++)n[o].dataIndex>=0&&t.call(e,n[o],o)},t.prototype.eachEdge=function(t,e){for(var n=this.edges,i=n.length,o=0;o<i;o++)n[o].dataIndex>=0&&n[o].node1.dataIndex>=0&&n[o].node2.dataIndex>=0&&t.call(e,n[o],o)},t.prototype.breadthFirstTraverse=function(t,e,n,i){if(e instanceof Tc||(e=this._nodesMap[Ic(e)]),e){for(var o="out"===n?"outEdges":"in"===n?"inEdges":"edges",r=0;r<this.nodes.length;r++)this.nodes[r].__visited=!1;if(!t.call(i,e,null))for(var a=[e];a.length;){var s=a.shift(),l=s[o];for(r=0;r<l.length;r++){var u=l[r],d=u.node1===s?u.node2:u.node1;if(!d.__visited){if(t.call(i,d,s))return;a.push(d),d.__visited=!0}}}}},t.prototype.update=function(){for(var t=this.data,e=this.edgeData,n=this.nodes,i=this.edges,o=0,r=n.length;o<r;o++)n[o].dataIndex=-1;for(o=0,r=t.count();o<r;o++)n[t.getRawIndex(o)].dataIndex=o;e.filterSelf((function(t){var n=i[e.getRawIndex(t)];return n.node1.dataIndex>=0&&n.node2.dataIndex>=0}));for(o=0,r=i.length;o<r;o++)i[o].dataIndex=-1;for(o=0,r=e.count();o<r;o++)i[e.getRawIndex(o)].dataIndex=o},t.prototype.clone=function(){for(var e=new t(this._directed),n=this.nodes,i=this.edges,o=0;o<n.length;o++)e.addNode(n[o].id,n[o].dataIndex);for(o=0;o<i.length;o++){var r=i[o];e.addEdge(r.node1.id,r.node2.id,r.dataIndex)}return e},t}(),Tc=function(){function t(t,e){this.inEdges=[],this.outEdges=[],this.edges=[],this.dataIndex=-1,this.id=null==t?"":t,this.dataIndex=null==e?-1:e}return t.prototype.degree=function(){return this.edges.length},t.prototype.inDegree=function(){return this.inEdges.length},t.prototype.outDegree=function(){return this.outEdges.length},t.prototype.getModel=function(t){if(!(this.dataIndex<0))return this.hostGraph.data.getItemModel(this.dataIndex).getModel(t)},t.prototype.getAdjacentDataIndices=function(){for(var t={edge:[],node:[]},e=0;e<this.edges.length;e++){var n=this.edges[e];n.dataIndex<0||(t.edge.push(n.dataIndex),t.node.push(n.node1.dataIndex,n.node2.dataIndex))}return t},t.prototype.getTrajectoryDataIndices=function(){for(var t=Ie(),e=Ie(),n=0;n<this.edges.length;n++){var i=this.edges[n];if(!(i.dataIndex<0)){t.set(i.dataIndex,!0);for(var o=[i.node1],r=[i.node2],a=0;a<o.length;){var s=o[a];a++,e.set(s.dataIndex,!0);for(var l=0;l<s.inEdges.length;l++)t.set(s.inEdges[l].dataIndex,!0),o.push(s.inEdges[l].node1)}for(a=0;a<r.length;){var u=r[a];a++,e.set(u.dataIndex,!0);for(l=0;l<u.outEdges.length;l++)t.set(u.outEdges[l].dataIndex,!0),r.push(u.outEdges[l].node2)}}}return{edge:t.keys(),node:e.keys()}},t}(),Cc=function(){function t(t,e,n){this.dataIndex=-1,this.node1=t,this.node2=e,this.dataIndex=null==n?-1:n}return t.prototype.getModel=function(t){if(!(this.dataIndex<0))return this.hostGraph.edgeData.getItemModel(this.dataIndex).getModel(t)},t.prototype.getAdjacentDataIndices=function(){return{edge:[this.dataIndex],node:[this.node1.dataIndex,this.node2.dataIndex]}},t.prototype.getTrajectoryDataIndices=function(){var t=Ie(),e=Ie();t.set(this.dataIndex,!0);for(var n=[this.node1],i=[this.node2],o=0;o<n.length;){var r=n[o];o++,e.set(r.dataIndex,!0);for(var a=0;a<r.inEdges.length;a++)t.set(r.inEdges[a].dataIndex,!0),n.push(r.inEdges[a].node1)}for(o=0;o<i.length;){var s=i[o];o++,e.set(s.dataIndex,!0);for(a=0;a<s.outEdges.length;a++)t.set(s.outEdges[a].dataIndex,!0),i.push(s.outEdges[a].node2)}return{edge:t.keys(),node:e.keys()}},t}();function Dc(t,e){return{getValue:function(n){var i=this[t][e];return i.getStore().get(i.getDimensionIndex(n||"value"),this.dataIndex)},setVisual:function(n,i){this.dataIndex>=0&&this[t][e].setItemVisual(this.dataIndex,n,i)},getVisual:function(n){return this[t][e].getItemVisual(this.dataIndex,n)},setLayout:function(n,i){this.dataIndex>=0&&this[t][e].setItemLayout(this.dataIndex,n,i)},getLayout:function(){return this[t][e].getItemLayout(this.dataIndex)},getGraphicEl:function(){return this[t][e].getItemGraphicEl(this.dataIndex)},getRawIndex:function(){return this[t][e].getRawIndex(this.dataIndex)}}}function Lc(t,e,n,i,o){for(var r=new Ac(i),a=0;a<t.length;a++)r.addNode(Ge(t[a].id,t[a].name,a),a);var s=[],l=[],u=0;for(a=0;a<e.length;a++){var d=e[a],c=d.source,h=d.target;r.addEdge(c,h,u)&&(l.push(d),s.push(Ge($e(d.id,null),c+" > "+h)),u++)}var p,g=n.get("coordinateSystem");if("cartesian2d"===g||"polar"===g)p=wt(t,n);else{var f=Gn.get(g),y=f&&f.dimensions||[];L(y,"value")<0&&y.concat(["value"]);var v=Qe(t,{coordDimensions:y,encodeDefine:n.getEncode()}).dimensions;(p=new tn(v,n)).initData(t)}var m=new tn(["value"],n);return m.initData(l,s),o&&o(p,m),cu({mainData:p,struct:r,structAttr:"graph",datas:{node:p,edge:m},datasAttr:{node:"data",edge:"edgeData"}}),r.update(),r}Kt(Tc,Dc("hostGraph","data")),Kt(Cc,Dc("hostGraph","edgeData"));var Pc=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.hasSymbolVisual=!0,e}return e(n,t),n.prototype.init=function(e){t.prototype.init.apply(this,arguments);var n=this;function i(){return n._categoriesData}this.legendVisualProvider=new Ht(i,i),this.fillDataTextStyle(e.edges||e.links),this._updateCategoriesData()},n.prototype.mergeOption=function(e){t.prototype.mergeOption.apply(this,arguments),this.fillDataTextStyle(e.edges||e.links),this._updateCategoriesData()},n.prototype.mergeDefaultAndTheme=function(e){t.prototype.mergeDefaultAndTheme.apply(this,arguments),We(e,"edgeLabel",["show"])},n.prototype.getInitialData=function(t,e){var n,i=t.edges||t.links||[],o=t.data||t.nodes||[],r=this;if(o&&i){Rd(n=this)&&(n.__curvenessList=[],n.__edgeMap={},Vd(n));var s=Lc(o,i,this,!0,(function(t,e){t.wrapMethod("getItemModel",(function(t){var e=r._categoriesModels[t.getShallow("category")];return e&&(e.parentModel=t.parentModel,t.parentModel=e),t}));var n=qt.prototype.getModel;function i(t,e){var i=n.call(this,t,e);return i.resolveParentPath=o,i}function o(t){if(t&&("label"===t[0]||"label"===t[1])){var e=t.slice();return"label"===t[0]?e[0]="edgeLabel":"label"===t[1]&&(e[1]="edgeLabel"),e}return t}e.wrapMethod("getItemModel",(function(t){return t.resolveParentPath=o,t.getModel=i,t}))}));return a(s.edges,(function(t){!function(t,e,n,i){if(Rd(n)){var o=Od(t,e,n),r=n.__edgeMap,a=r[Ed(o)];r[o]&&!a?r[o].isForward=!0:a&&r[o]&&(a.isForward=!0,r[o].isForward=!1),r[o]=r[o]||[],r[o].push(i)}}(t.node1,t.node2,this,t.dataIndex)}),this),s.data}},n.prototype.getGraph=function(){return this.getData().graph},n.prototype.getEdgeData=function(){return this.getGraph().edgeData},n.prototype.getCategoriesData=function(){return this._categoriesData},n.prototype.formatTooltip=function(t,e,n){if("edge"===n){var i=this.getData(),o=this.getDataParams(t,n),r=i.graph.getEdgeByIndex(t),a=i.getName(r.node1.dataIndex),s=i.getName(r.node2.dataIndex),l=[];return null!=a&&l.push(a),null!=s&&l.push(s),Ut("nameValue",{name:l.join(" > "),value:o.value,noValue:null==o.value})}return Fn({series:this,dataIndex:t,multipleSeries:e})},n.prototype._updateCategoriesData=function(){var t=d(this.option.categories||[],(function(t){return null!=t.value?t:H({value:0},t)})),e=new tn(["value"],this);e.initData(t),this._categoriesData=e,this._categoriesModels=e.mapArray((function(t){return e.getItemModel(t)}))},n.prototype.setZoom=function(t){this.option.zoom=t},n.prototype.setCenter=function(t){this.option.center=t},n.prototype.isAnimationEnabled=function(){return t.prototype.isAnimationEnabled.call(this)&&!("force"===this.get("layout")&&this.get(["force","layoutAnimation"]))},n.type="series.graph",n.dependencies=["grid","polar","geo","singleAxis","calendar"],n.defaultOption={z:2,coordinateSystem:"view",legendHoverLink:!0,layout:null,circular:{rotateLabel:!1},force:{initLayout:null,repulsion:[0,50],gravity:.1,friction:.6,edgeLength:30,layoutAnimation:!0},left:"center",top:"center",symbol:"circle",symbolSize:10,edgeSymbol:["none","none"],edgeSymbolSize:10,edgeLabel:{position:"middle",distance:5},draggable:!1,roam:!1,center:null,zoom:1,nodeScaleRatio:.6,label:{show:!1,formatter:"{b}"},itemStyle:{},lineStyle:{color:"#aaa",width:1,opacity:.5},emphasis:{scale:!0,label:{show:!0}},select:{itemStyle:{borderColor:"#212121"}}},n}(Mt),kc={type:"graphRoam",event:"graphRoam",update:"none"};var Nc=function(){return function(){this.angle=0,this.width=10,this.r=10,this.x=0,this.y=0}}(),Rc=function(t){function n(e){var n=t.call(this,e)||this;return n.type="pointer",n}return e(n,t),n.prototype.getDefaultShape=function(){return new Nc},n.prototype.buildPath=function(t,e){var n=Math.cos,i=Math.sin,o=e.r,r=e.width,a=e.angle,s=e.x-n(a)*r*(r>=o/3?1:2),l=e.y-i(a)*r*(r>=o/3?1:2);a=e.angle-Math.PI/2,t.moveTo(s,l),t.lineTo(e.x+n(a)*r,e.y+i(a)*r),t.lineTo(e.x+n(e.angle)*o,e.y+i(e.angle)*o),t.lineTo(e.x-n(a)*r,e.y-i(a)*r),t.lineTo(s,l)},n}(q);function Vc(t,e){var n=null==t?"":t+"";return e&&(G(e)?n=e.replace("{value}",n):f(e)&&(n=e(t))),n}var Oc=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.render=function(t,e,n){this.group.removeAll();var i=t.get(["axisLine","lineStyle","color"]),o=function(t,e){var n=t.get("center"),i=e.getWidth(),o=e.getHeight(),r=Math.min(i,o);return{cx:m(n[0],e.getWidth()),cy:m(n[1],e.getHeight()),r:m(t.get("radius"),r/2)}}(t,n);this._renderMain(t,e,n,i,o),this._data=t.getData()},n.prototype.dispose=function(){},n.prototype._renderMain=function(t,e,n,i,o){var r=this.group,s=t.get("clockwise"),l=-t.get("startAngle")/180*Math.PI,u=-t.get("endAngle")/180*Math.PI,d=t.getModel("axisLine"),c=d.get("roundCap")?Hn:Zn,h=d.get("show"),p=d.getModel("lineStyle"),g=p.get("width"),f=[l,u];Wn(f,!s);for(var y=(u=f[1])-(l=f[0]),v=l,m=[],x=0;h&&x<i.length;x++){var _=new c({shape:{startAngle:v,endAngle:u=l+y*Math.min(Math.max(i[x][0],0),1),cx:o.cx,cy:o.cy,clockwise:s,r0:o.r-g,r:o.r},silent:!0});_.setStyle({fill:i[x][1]}),_.setStyle(p.getLineStyle(["color","width"])),m.push(_),v=u}m.reverse(),a(m,(function(t){return r.add(t)}));var b=function(t){if(t<=0)return i[0][1];var e;for(e=0;e<i.length;e++)if(i[e][0]>=t&&(0===e?0:i[e-1][0])<t)return i[e][1];return i[e-1][1]};this._renderTicks(t,e,n,b,o,l,u,s,g),this._renderTitleAndDetail(t,e,n,b,o),this._renderAnchor(t,o),this._renderPointer(t,e,n,b,o,l,u,s,g)},n.prototype._renderTicks=function(t,e,n,i,o,r,a,s,l){for(var u,d,c=this.group,h=o.cx,p=o.cy,g=o.r,f=+t.get("min"),y=+t.get("max"),v=t.getModel("splitLine"),x=t.getModel("axisTick"),_=t.getModel("axisLabel"),b=t.get("splitNumber"),S=x.get("splitNumber"),w=m(v.get("length"),g),M=m(x.get("length"),g),I=r,A=(a-r)/b,T=A/S,C=v.getModel("lineStyle").getLineStyle(),D=x.getModel("lineStyle").getLineStyle(),L=v.get("distance"),P=0;P<=b;P++){if(u=Math.cos(I),d=Math.sin(I),v.get("show")){var k=new ve({shape:{x1:u*(g-(N=L?L+l:l))+h,y1:d*(g-N)+p,x2:u*(g-w-N)+h,y2:d*(g-w-N)+p},style:C,silent:!0});"auto"===C.stroke&&k.setStyle({stroke:i(P/b)}),c.add(k)}if(_.get("show")){var N=_.get("distance")+L,R=Vc(kn(P/b*(y-f)+f),_.get("formatter")),V=i(P/b),O=u*(g-w-N)+h,E=d*(g-w-N)+p,z=_.get("rotate"),B=0;"radial"===z?(B=-I+2*Math.PI)>Math.PI/2&&(B+=Math.PI):"tangential"===z?B=-I-Math.PI/2:Y(z)&&(B=z*Math.PI/180),0===B?c.add(new an({style:sn(_,{text:R,x:O,y:E,verticalAlign:d<-.8?"top":d>.8?"bottom":"middle",align:u<-.4?"left":u>.4?"right":"center"},{inheritColor:V}),silent:!0})):c.add(new an({style:sn(_,{text:R,x:O,y:E,verticalAlign:"middle",align:"center"},{inheritColor:V}),silent:!0,originX:O,originY:E,rotation:B}))}if(x.get("show")&&P!==b){N=(N=x.get("distance"))?N+l:l;for(var G=0;G<=S;G++){u=Math.cos(I),d=Math.sin(I);var F=new ve({shape:{x1:u*(g-N)+h,y1:d*(g-N)+p,x2:u*(g-M-N)+h,y2:d*(g-M-N)+p},silent:!0,style:D});"auto"===D.stroke&&F.setStyle({stroke:i((P+G/S)/b)}),c.add(F),I+=T}I-=T}else I+=A}},n.prototype._renderPointer=function(t,e,n,i,o,r,a,s,l){var u=this.group,d=this._data,c=this._progressEls,h=[],p=t.get(["pointer","show"]),g=t.getModel("progress"),f=g.get("show"),y=t.getData(),v=y.mapDimension("value"),x=+t.get("min"),_=+t.get("max"),b=[x,_],S=[r,a];function w(e,n){var i,r=y.getItemModel(e).getModel("pointer"),a=m(r.get("width"),o.r),s=m(r.get("length"),o.r),l=t.get(["pointer","icon"]),u=r.get("offsetCenter"),d=m(u[0],o.r),c=m(u[1],o.r),h=r.get("keepAspect");return(i=l?At(l,d-a/2,c-s,a,s,null,h):new Rc({shape:{angle:-Math.PI/2,width:a,r:s,x:d,y:c}})).rotation=-(n+Math.PI/2),i.x=o.cx,i.y=o.cy,i}function M(t,e){var n=g.get("roundCap")?Hn:Zn,i=g.get("overlap"),a=i?g.get("width"):l/y.count(),u=i?o.r-a:o.r-(t+1)*a,d=i?o.r:o.r-t*a,c=new n({shape:{startAngle:r,endAngle:e,cx:o.cx,cy:o.cy,clockwise:s,r0:u,r:d}});return i&&(c.z2=fn(y.get(v,t),[x,_],[100,0],!0)),c}(f||p)&&(y.diff(d).add((function(e){var n=y.get(v,e);if(p){var i=w(e,r);D(i,{rotation:-((isNaN(+n)?S[0]:fn(n,b,S,!0))+Math.PI/2)},t),u.add(i),y.setItemGraphicEl(e,i)}if(f){var o=M(e,r),a=g.get("clip");D(o,{shape:{endAngle:fn(n,b,S,a)}},t),u.add(o),Yn(t.seriesIndex,y.dataType,e,o),h[e]=o}})).update((function(e,n){var i=y.get(v,e);if(p){var o=d.getItemGraphicEl(n),a=o?o.rotation:r,s=w(e,a);s.rotation=a,P(s,{rotation:-((isNaN(+i)?S[0]:fn(i,b,S,!0))+Math.PI/2)},t),u.add(s),y.setItemGraphicEl(e,s)}if(f){var l=c[n],m=M(e,l?l.shape.endAngle:r),x=g.get("clip");P(m,{shape:{endAngle:fn(i,b,S,x)}},t),u.add(m),Yn(t.seriesIndex,y.dataType,e,m),h[e]=m}})).execute(),y.each((function(t){var e=y.getItemModel(t),n=e.getModel("emphasis"),o=n.get("focus"),r=n.get("blurScope"),a=n.get("disabled");if(p){var s=y.getItemGraphicEl(t),l=y.getItemVisual(t,"style"),u=l.fill;if(s instanceof R){var d=s.style;s.useStyle(H({image:d.image,x:d.x,y:d.y,width:d.width,height:d.height},l))}else s.useStyle(l),"pointer"!==s.type&&s.setColor(u);s.setStyle(e.getModel(["pointer","itemStyle"]).getItemStyle()),"auto"===s.style.fill&&s.setStyle("fill",i(fn(y.get(v,t),b,[0,1],!0))),s.z2EmphasisLift=0,Et(s,e),Gt(s,o,r,a)}if(f){var c=h[t];c.useStyle(y.getItemVisual(t,"style")),c.setStyle(e.getModel(["progress","itemStyle"]).getItemStyle()),c.z2EmphasisLift=0,Et(c,e),Gt(c,o,r,a)}})),this._progressEls=h)},n.prototype._renderAnchor=function(t,e){var n=t.getModel("anchor");if(n.get("show")){var i=n.get("size"),o=n.get("icon"),r=n.get("offsetCenter"),a=n.get("keepAspect"),s=At(o,e.cx-i/2+m(r[0],e.r),e.cy-i/2+m(r[1],e.r),i,i,null,a);s.z2=n.get("showAbove")?1:0,s.setStyle(n.getModel("itemStyle").getItemStyle()),this.group.add(s)}},n.prototype._renderTitleAndDetail=function(t,e,n,i,o){var r=this,a=t.getData(),s=a.mapDimension("value"),l=+t.get("min"),u=+t.get("max"),d=new It,c=[],h=[],p=t.isAnimationEnabled(),g=t.get(["pointer","showAbove"]);a.diff(this._data).add((function(t){c[t]=new an({silent:!0}),h[t]=new an({silent:!0})})).update((function(t,e){c[t]=r._titleEls[e],h[t]=r._detailEls[e]})).execute(),a.each((function(e){var n=a.getItemModel(e),r=a.get(s,e),f=new It,y=i(fn(r,[l,u],[0,1],!0)),v=n.getModel("title");if(v.get("show")){var x=v.get("offsetCenter"),_=o.cx+m(x[0],o.r),b=o.cy+m(x[1],o.r);(D=c[e]).attr({z2:g?0:2,style:sn(v,{x:_,y:b,text:a.getName(e),align:"center",verticalAlign:"middle"},{inheritColor:y})}),f.add(D)}var S=n.getModel("detail");if(S.get("show")){var w=S.get("offsetCenter"),M=o.cx+m(w[0],o.r),I=o.cy+m(w[1],o.r),A=m(S.get("width"),o.r),T=m(S.get("height"),o.r),C=t.get(["progress","show"])?a.getItemVisual(e,"style").fill:y,D=h[e],L=S.get("formatter");D.attr({z2:g?0:2,style:sn(S,{x:M,y:I,text:Vc(r,L),width:isNaN(A)?null:A,height:isNaN(T)?null:T,align:"center",verticalAlign:"middle"},{inheritColor:C})}),Xn(D,{normal:S},r,(function(t){return Vc(t,L)})),p&&k(D,e,a,t,{getFormattedLabel:function(t,e,n,i,o,a){return Vc(a?a.interpolatedValue:r,L)}}),f.add(D)}d.add(f)})),this.group.add(d),this._titleEls=c,this._detailEls=h},n.type="gauge",n}(Dt),Ec=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.visualStyleAccessPath="itemStyle",e}return e(n,t),n.prototype.getInitialData=function(t,e){return Yt(this,["value"])},n.type="series.gauge",n.defaultOption={z:2,colorBy:"data",center:["50%","50%"],legendHoverLink:!0,radius:"75%",startAngle:225,endAngle:-45,clockwise:!0,min:0,max:100,splitNumber:10,axisLine:{show:!0,roundCap:!1,lineStyle:{color:[[1,"#E6EBF8"]],width:10}},progress:{show:!1,overlap:!0,width:10,roundCap:!1,clip:!0},splitLine:{show:!0,length:10,distance:10,lineStyle:{color:"#63677A",width:3,type:"solid"}},axisTick:{show:!0,splitNumber:5,length:6,distance:10,lineStyle:{color:"#63677A",width:1,type:"solid"}},axisLabel:{show:!0,distance:15,color:"#464646",fontSize:12,rotate:0},pointer:{icon:null,offsetCenter:[0,0],show:!0,showAbove:!0,length:"60%",width:6,keepAspect:!1},anchor:{show:!1,showAbove:!1,size:6,icon:"circle",offsetCenter:[0,0],keepAspect:!1,itemStyle:{color:"#fff",borderWidth:0,borderColor:"#5470c6"}},title:{show:!0,offsetCenter:[0,"20%"],color:"#464646",fontSize:16,valueAnimation:!1},detail:{show:!0,backgroundColor:"rgba(0,0,0,0)",borderWidth:0,borderColor:"#ccc",width:100,height:null,padding:[5,10],offsetCenter:[0,"40%"],color:"#464646",fontSize:30,fontWeight:"bold",lineHeight:30,valueAnimation:!1}},n}(Mt);var zc=["itemStyle","opacity"],Bc=function(t){function n(e,n){var i=t.call(this)||this,o=i,r=new Rt,a=new an;return o.setTextContent(a),i.setTextGuideLine(r),i.updateData(e,n,!0),i}return e(n,t),n.prototype.updateData=function(t,e,n){var i=this,o=t.hostModel,r=t.getItemModel(e),a=t.getItemLayout(e),s=r.getModel("emphasis"),l=r.get(zc);l=null==l?1:l,n||Vt(i),i.useStyle(t.getItemVisual(e,"style")),i.style.lineJoin="round",n?(i.setShape({points:a.points}),i.style.opacity=0,D(i,{style:{opacity:l}},o,e)):P(i,{style:{opacity:l},shape:{points:a.points}},o,e),Et(i,r),this._updateLabel(t,e),Gt(this,s.get("focus"),s.get("blurScope"),s.get("disabled"))},n.prototype._updateLabel=function(t,e){var n=this,i=this.getTextGuideLine(),o=n.getTextContent(),r=t.hostModel,a=t.getItemModel(e),s=t.getItemLayout(e).label,l=t.getItemVisual(e,"style"),u=l.fill;zt(o,Bt(a),{labelFetcher:t.hostModel,labelDataIndex:e,defaultOpacity:l.opacity,defaultText:t.getName(e)},{normal:{align:s.textAlign,verticalAlign:s.verticalAlign}}),n.setTextConfig({local:!0,inside:!!s.inside,insideStroke:u,outsideFill:u});var d=s.linePoints;i.setShape({points:d}),n.textGuideLineConfig={anchor:d?new jn(d[0][0],d[0][1]):null},P(o,{style:{x:s.x,y:s.y}},r,e),o.attr({rotation:s.rotation,originX:s.x,originY:s.y,z2:10}),w(n,M(a),{stroke:u})},n}(Nt),Gc=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.ignoreLabelLineUpdate=!0,e}return e(n,t),n.prototype.render=function(t,e,n){var i=t.getData(),o=this._data,r=this.group;i.diff(o).add((function(t){var e=new Bc(i,t);i.setItemGraphicEl(t,e),r.add(e)})).update((function(t,e){var n=o.getItemGraphicEl(e);n.updateData(i,t),r.add(n),i.setItemGraphicEl(t,n)})).remove((function(e){var n=o.getItemGraphicEl(e);Un(n,t,e)})).execute(),this._data=i},n.prototype.remove=function(){this.group.removeAll(),this._data=null},n.prototype.dispose=function(){},n.type="funnel",n}(Dt),Fc=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.init=function(e){t.prototype.init.apply(this,arguments),this.legendVisualProvider=new Ht(Zt(this.getData,this),Zt(this.getRawData,this)),this._defaultLabelLine(e)},n.prototype.getInitialData=function(t,e){return Yt(this,{coordDimensions:["value"],encodeDefaulter:Re(Ve,this)})},n.prototype._defaultLabelLine=function(t){We(t,"labelLine",["show"]);var e=t.labelLine,n=t.emphasis.labelLine;e.show=e.show&&t.label.show,n.show=n.show&&t.emphasis.label.show},n.prototype.getDataParams=function(e){var n=this.getData(),i=t.prototype.getDataParams.call(this,e),o=n.mapDimension("value"),r=n.getSum(o);return i.percent=r?+(n.get(o,e)/r*100).toFixed(2):0,i.$vars.push("percent"),i},n.type="series.funnel",n.defaultOption={z:2,legendHoverLink:!0,colorBy:"data",left:80,top:60,right:80,bottom:60,minSize:"0%",maxSize:"100%",sort:"descending",orient:"vertical",gap:0,funnelAlign:"center",label:{show:!0,position:"outer"},labelLine:{show:!0,length:20,lineStyle:{width:1}},itemStyle:{borderColor:"#fff",borderWidth:1},emphasis:{label:{show:!0}},select:{itemStyle:{borderColor:"#212121"}}},n}(Mt);function Wc(t,e){t.eachSeriesByType("funnel",(function(t){var n=t.getData(),i=n.mapDimension("value"),o=t.get("sort"),r=function(t,e){return Fe(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}(t,e),a=t.get("orient"),s=r.width,l=r.height,u=function(t,e){for(var n=t.mapDimension("value"),i=t.mapArray(n,(function(t){return t})),o=[],r="ascending"===e,a=0,s=t.count();a<s;a++)o[a]=a;return f(e)?o.sort(e):"none"!==e&&o.sort((function(t,e){return r?i[t]-i[e]:i[e]-i[t]})),o}(n,o),d=r.x,c=r.y,h="horizontal"===a?[m(t.get("minSize"),l),m(t.get("maxSize"),l)]:[m(t.get("minSize"),s),m(t.get("maxSize"),s)],p=n.getDataExtent(i),g=t.get("min"),y=t.get("max");null==g&&(g=Math.min(p[0],0)),null==y&&(y=p[1]);var v=t.get("funnelAlign"),x=t.get("gap"),_=(("horizontal"===a?s:l)-x*(n.count()-1))/n.count(),b=function(t,e){if("horizontal"===a){var o=n.get(i,t)||0,r=fn(o,[g,y],h,!0),u=void 0;switch(v){case"top":u=c;break;case"center":u=c+(l-r)/2;break;case"bottom":u=c+(l-r)}return[[e,u],[e,u+r]]}var p,f=n.get(i,t)||0,m=fn(f,[g,y],h,!0);switch(v){case"left":p=d;break;case"center":p=d+(s-m)/2;break;case"right":p=d+s-m}return[[p,e],[p+m,e]]};"ascending"===o&&(_=-_,x=-x,"horizontal"===a?d+=s:c+=l,u=u.reverse());for(var S=0;S<u.length;S++){var w=u[S],M=u[S+1],I=n.getItemModel(w);if("horizontal"===a){var A=I.get(["itemStyle","width"]);null==A?A=_:(A=m(A,s),"ascending"===o&&(A=-A));var T=b(w,d),C=b(M,d+A);d+=A+x,n.setItemLayout(w,{points:T.concat(C.slice().reverse())})}else{var D=I.get(["itemStyle","height"]);null==D?D=_:(D=m(D,l),"ascending"===o&&(D=-D));T=b(w,c),C=b(M,c+D);c+=D+x,n.setItemLayout(w,{points:T.concat(C.slice().reverse())})}}!function(t){var e=t.hostModel.get("orient");t.each((function(n){var i,o,r,a,s=t.getItemModel(n),l=s.getModel("label").get("position"),u=s.getModel("labelLine"),d=t.getItemLayout(n),c=d.points,h="inner"===l||"inside"===l||"center"===l||"insideLeft"===l||"insideRight"===l;if(h)"insideLeft"===l?(o=(c[0][0]+c[3][0])/2+5,r=(c[0][1]+c[3][1])/2,i="left"):"insideRight"===l?(o=(c[1][0]+c[2][0])/2-5,r=(c[1][1]+c[2][1])/2,i="right"):(o=(c[0][0]+c[1][0]+c[2][0]+c[3][0])/4,r=(c[0][1]+c[1][1]+c[2][1]+c[3][1])/4,i="center"),a=[[o,r],[o,r]];else{var p=void 0,g=void 0,f=void 0,y=void 0,v=u.get("length");"left"===l?(p=(c[3][0]+c[0][0])/2,g=(c[3][1]+c[0][1])/2,o=(f=p-v)-5,i="right"):"right"===l?(p=(c[1][0]+c[2][0])/2,g=(c[1][1]+c[2][1])/2,o=(f=p+v)+5,i="left"):"top"===l?(p=(c[3][0]+c[0][0])/2,r=(y=(g=(c[3][1]+c[0][1])/2)-v)-5,i="center"):"bottom"===l?(p=(c[1][0]+c[2][0])/2,r=(y=(g=(c[1][1]+c[2][1])/2)+v)+5,i="center"):"rightTop"===l?(p="horizontal"===e?c[3][0]:c[1][0],g="horizontal"===e?c[3][1]:c[1][1],"horizontal"===e?(r=(y=g-v)-5,i="center"):(o=(f=p+v)+5,i="top")):"rightBottom"===l?(p=c[2][0],g=c[2][1],"horizontal"===e?(r=(y=g+v)+5,i="center"):(o=(f=p+v)+5,i="bottom")):"leftTop"===l?(p=c[0][0],g="horizontal"===e?c[0][1]:c[1][1],"horizontal"===e?(r=(y=g-v)-5,i="center"):(o=(f=p-v)-5,i="right")):"leftBottom"===l?(p="horizontal"===e?c[1][0]:c[3][0],g="horizontal"===e?c[1][1]:c[2][1],"horizontal"===e?(r=(y=g+v)+5,i="center"):(o=(f=p-v)-5,i="right")):(p=(c[1][0]+c[2][0])/2,g=(c[1][1]+c[2][1])/2,"horizontal"===e?(r=(y=g+v)+5,i="center"):(o=(f=p+v)+5,i="left")),"horizontal"===e?o=f=p:r=y=g,a=[[p,g],[f,y]]}d.label={linePoints:a,x:o,y:r,verticalAlign:"middle",textAlign:i,inside:h}}))}(n)}))}var Hc=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e._dataGroup=new It,e._initialized=!1,e}return e(n,t),n.prototype.init=function(){this.group.add(this._dataGroup)},n.prototype.render=function(t,e,n,i){this._progressiveEls=null;var o=this._dataGroup,r=t.getData(),a=this._data,s=t.coordinateSystem,l=s.dimensions,u=Xc(t);if(r.diff(a).add((function(t){Uc(Yc(r,o,t,l,s),r,t,u)})).update((function(e,n){var i=a.getItemGraphicEl(n),o=Zc(r,e,l,s);r.setItemGraphicEl(e,i),P(i,{shape:{points:o}},t,e),Vt(i),Uc(i,r,e,u)})).remove((function(t){var e=a.getItemGraphicEl(t);o.remove(e)})).execute(),!this._initialized){this._initialized=!0;var d=function(t,e,n){var i=t.model,o=t.getRect(),r=new pe({shape:{x:o.x,y:o.y,width:o.width,height:o.height}}),a="horizontal"===i.get("layout")?"width":"height";return r.setShape(a,0),D(r,{shape:{width:o.width,height:o.height}},e,n),r}(s,t,(function(){setTimeout((function(){o.removeClipPath()}))}));o.setClipPath(d)}this._data=r},n.prototype.incrementalPrepareRender=function(t,e,n){this._initialized=!0,this._data=null,this._dataGroup.removeAll()},n.prototype.incrementalRender=function(t,e,n){for(var i=e.getData(),o=e.coordinateSystem,r=o.dimensions,a=Xc(e),s=this._progressiveEls=[],l=t.start;l<t.end;l++){var u=Yc(i,this._dataGroup,l,r,o);u.incremental=!0,Uc(u,i,l,a),s.push(u)}},n.prototype.remove=function(){this._dataGroup&&this._dataGroup.removeAll(),this._data=null},n.type="parallel",n}(Dt);function Zc(t,e,n,i){for(var o,r=[],a=0;a<n.length;a++){var s=n[a],l=t.get(t.mapDimension(s),e);o=l,("category"===i.getAxis(s).type?null==o:null==o||isNaN(o))||r.push(i.dataToPoint(l,s))}return r}function Yc(t,e,n,i,o){var r=Zc(t,n,i,o),a=new Rt({shape:{points:r},z2:10});return e.add(a),t.setItemGraphicEl(n,a),a}function Xc(t){var e=t.get("smooth",!0);return!0===e&&(e=.3),e=qn(e),Kn(e)&&(e=0),{smooth:e}}function Uc(t,e,n,i){t.useStyle(e.getItemVisual(n,"style")),t.style.fill=null,t.setShape("smooth",i.smooth);var o=e.getItemModel(n),r=o.getModel("emphasis");Et(t,o,"lineStyle"),Gt(t,r.get("focus"),r.get("blurScope"),r.get("disabled"))}var jc=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.visualStyleAccessPath="lineStyle",e.visualDrawType="stroke",e}return e(n,t),n.prototype.getInitialData=function(t,e){return wt(null,this,{useEncodeDefaulter:Zt(qc,null,this)})},n.prototype.getRawIndicesByActiveState=function(t){var e=this.coordinateSystem,n=this.getData(),i=[];return e.eachActiveState(n,(function(e,o){t===e&&i.push(n.getRawIndex(o))})),i},n.type="series.parallel",n.dependencies=["parallel"],n.defaultOption={z:2,coordinateSystem:"parallel",parallelIndex:0,label:{show:!1},inactiveOpacity:.05,activeOpacity:1,lineStyle:{width:1,opacity:.45,type:"solid"},emphasis:{label:{show:!1}},progressive:500,smooth:!1,animationEasing:"linear"},n}(Mt);function qc(t){var e=t.ecModel.getComponent("parallel",t.get("parallelIndex"));if(e){var n={};return a(e.dimensions,(function(t){var e=+t.replace("dim","");n[t]=e})),n}}var Kc=["lineStyle","opacity"],Jc={seriesType:"parallel",reset:function(t,e){var n=t.coordinateSystem,i={normal:t.get(["lineStyle","opacity"]),active:t.get("activeOpacity"),inactive:t.get("inactiveOpacity")};return{progress:function(t,e){n.eachActiveState(e,(function(t,n){var o=i[t];if("normal"===t&&e.hasItemOption){var r=e.getItemModel(n).get(Kc,!0);null!=r&&(o=r)}e.ensureUniqueItemVisual(n,"style").opacity=o}),t.start,t.end)}}}};function $c(t){!function(t){if(t.parallel)return;var e=!1;a(t.series,(function(t){t&&"parallel"===t.type&&(e=!0)})),e&&(t.parallel=[{}])}(t),function(t){var e=nn(t.parallelAxis);a(e,(function(e){if(xt(e)){var n=e.parallelIndex||0,i=nn(t.parallel)[n];i&&i.parallelAxisDefault&&jt(e,i.parallelAxisDefault,!1)}}))}(t)}var Qc=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.render=function(t,e,n){this._model=t,this._api=n,this._handlers||(this._handlers={},a(th,(function(t,e){n.getZr().on(e,this._handlers[e]=Zt(t,this))}),this)),Jn(this,"_throttledDispatchExpand",t.get("axisExpandRate"),"fixRate")},n.prototype.dispose=function(t,e){$n(this,"_throttledDispatchExpand"),a(this._handlers,(function(t,n){e.getZr().off(n,t)})),this._handlers=null},n.prototype._throttledDispatchExpand=function(t){this._dispatchExpand(t)},n.prototype._dispatchExpand=function(t){t&&this._api.dispatchAction(H({type:"parallelAxisExpand"},t))},n.type="parallel",n}(oe),th={mousedown:function(t){eh(this,"click")&&(this._mouseDownPoint=[t.offsetX,t.offsetY])},mouseup:function(t){var e=this._mouseDownPoint;if(eh(this,"click")&&e){var n=[t.offsetX,t.offsetY];if(Math.pow(e[0]-n[0],2)+Math.pow(e[1]-n[1],2)>5)return;var i=this._model.coordinateSystem.getSlidedAxisExpandWindow([t.offsetX,t.offsetY]);"none"!==i.behavior&&this._dispatchExpand({axisExpandWindow:i.axisExpandWindow})}this._mouseDownPoint=null},mousemove:function(t){if(!this._mouseDownPoint&&eh(this,"mousemove")){var e=this._model,n=e.coordinateSystem.getSlidedAxisExpandWindow([t.offsetX,t.offsetY]),i=n.behavior;"jump"===i&&this._throttledDispatchExpand.debounceNextCall(e.get("axisExpandDebounce")),this._throttledDispatchExpand("none"===i?null:{axisExpandWindow:n.axisExpandWindow,animation:"jump"===i?null:{duration:0}})}}};function eh(t,e){var n=t._model;return n.get("axisExpandable")&&n.get("axisExpandTriggerOn")===e}var nh=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.init=function(){t.prototype.init.apply(this,arguments),this.mergeOption({})},n.prototype.mergeOption=function(t){var e=this.option;t&&jt(e,t,!0),this._initDimensions()},n.prototype.contains=function(t,e){var n=t.get("parallelIndex");return null!=n&&e.getComponent("parallel",n)===this},n.prototype.setAxisExpand=function(t){a(["axisExpandable","axisExpandCenter","axisExpandCount","axisExpandWidth","axisExpandWindow"],(function(e){t.hasOwnProperty(e)&&(this.option[e]=t[e])}),this)},n.prototype._initDimensions=function(){var t=this.dimensions=[],e=this.parallelAxisIndex=[],n=c(this.ecModel.queryComponents({mainType:"parallelAxis"}),(function(t){return(t.get("parallelIndex")||0)===this.componentIndex}),this);a(n,(function(n){t.push("dim"+n.get("dim")),e.push(n.componentIndex)}))},n.type="parallel",n.dependencies=["parallelAxis"],n.layoutMode="box",n.defaultOption={z:0,left:80,top:60,right:80,bottom:60,layout:"horizontal",axisExpandable:!1,axisExpandCenter:null,axisExpandCount:0,axisExpandWidth:50,axisExpandRate:17,axisExpandDebounce:50,axisExpandSlideTriggerArea:[-.15,.05,.4],axisExpandTriggerOn:"click",parallelAxisDefault:null},n}(Qt),ih=function(t){function n(e,n,i,o,r){var a=t.call(this,e,n,i)||this;return a.type=o||"value",a.axisIndex=r,a}return e(n,t),n.prototype.isHorizontal=function(){return"horizontal"!==this.coordinateSystem.getModel().get("layout")},n}(re);function oh(t,e,n,i,o,r){t=t||0;var a=n[1]-n[0];if(null!=o&&(o=ah(o,[0,a])),null!=r&&(r=Math.max(r,null!=o?o:0)),"all"===i){var s=Math.abs(e[1]-e[0]);s=ah(s,[0,a]),o=r=ah(s,[o,r]),i=0}e[0]=ah(e[0],n),e[1]=ah(e[1],n);var l=rh(e,i);e[i]+=t;var u,d=o||0,c=n.slice();return l.sign<0?c[0]+=d:c[1]-=d,e[i]=ah(e[i],c),u=rh(e,i),null!=o&&(u.sign!==l.sign||u.span<o)&&(e[1-i]=e[i]+l.sign*o),u=rh(e,i),null!=r&&u.span>r&&(e[1-i]=e[i]+u.sign*r),e}function rh(t,e){var n=t[e]-t[1-e];return{span:Math.abs(n),sign:n>0?-1:n<0?1:e?-1:1}}function ah(t,e){return Math.min(null!=e[1]?e[1]:1/0,Math.max(null!=e[0]?e[0]:-1/0,t))}var sh=a,lh=Math.min,uh=Math.max,dh=Math.floor,ch=Math.ceil,hh=kn,ph=Math.PI,gh=function(){function t(t,e,n){this.type="parallel",this._axesMap=Ie(),this._axesLayout={},this.dimensions=t.dimensions,this._model=t,this._init(t,e,n)}return t.prototype._init=function(t,e,n){var i=t.dimensions,o=t.parallelAxisIndex;sh(i,(function(t,n){var i=o[n],r=e.getComponent("parallelAxis",i),a=this._axesMap.set(t,new ih(t,Qn(r),[0,0],r.get("type"),i)),s="category"===a.type;a.onBand=s&&r.get("boundaryGap"),a.inverse=r.get("inverse"),r.axis=a,a.model=r,a.coordinateSystem=r.coordinateSystem=this}),this)},t.prototype.update=function(t,e){this._updateAxesFromSeries(this._model,t)},t.prototype.containPoint=function(t){var e=this._makeLayoutInfo(),n=e.axisBase,i=e.layoutBase,o=e.pixelDimIndex,r=t[1-o],a=t[o];return r>=n&&r<=n+e.axisLength&&a>=i&&a<=i+e.layoutLength},t.prototype.getModel=function(){return this._model},t.prototype._updateAxesFromSeries=function(t,e){e.eachSeries((function(n){if(t.contains(n,e)){var i=n.getData();sh(this.dimensions,(function(t){var e=this._axesMap.get(t);e.scale.unionExtentFromData(i,i.mapDimension(t)),ti(e.scale,e.model)}),this)}}),this)},t.prototype.resize=function(t,e){this._rect=Fe(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()}),this._layoutAxes()},t.prototype.getRect=function(){return this._rect},t.prototype._makeLayoutInfo=function(){var t,e=this._model,n=this._rect,i=["x","y"],o=["width","height"],r=e.get("layout"),a="horizontal"===r?0:1,s=n[o[a]],l=[0,s],u=this.dimensions.length,d=fh(e.get("axisExpandWidth"),l),c=fh(e.get("axisExpandCount")||0,[0,u]),h=e.get("axisExpandable")&&u>3&&u>c&&c>1&&d>0&&s>0,p=e.get("axisExpandWindow");p?(t=fh(p[1]-p[0],l),p[1]=p[0]+t):(t=fh(d*(c-1),l),(p=[d*(e.get("axisExpandCenter")||dh(u/2))-t/2])[1]=p[0]+t);var g=(s-t)/(u-c);g<3&&(g=0);var f=[dh(hh(p[0]/d,1))+1,ch(hh(p[1]/d,1))-1],y=g/d*p[0];return{layout:r,pixelDimIndex:a,layoutBase:n[i[a]],layoutLength:s,axisBase:n[i[1-a]],axisLength:n[o[1-a]],axisExpandable:h,axisExpandWidth:d,axisCollapseWidth:g,axisExpandWindow:p,axisCount:u,winInnerIndices:f,axisExpandWindow0Pos:y}},t.prototype._layoutAxes=function(){var t=this._rect,e=this._axesMap,n=this.dimensions,i=this._makeLayoutInfo(),o=i.layout;e.each((function(t){var e=[0,i.axisLength],n=t.inverse?1:0;t.setExtent(e[n],e[1-n])})),sh(n,(function(e,n){var r=(i.axisExpandable?vh:yh)(n,i),a={horizontal:{x:r.position,y:i.axisLength},vertical:{x:0,y:r.position}},s={horizontal:ph/2,vertical:0},l=[a[o].x+t.x,a[o].y+t.y],u=s[o],d=Me();be(d,d,u),we(d,d,l),this._axesLayout[e]={position:l,rotation:u,transform:d,axisNameAvailableWidth:r.axisNameAvailableWidth,axisLabelShow:r.axisLabelShow,nameTruncateMaxWidth:r.nameTruncateMaxWidth,tickDirection:1,labelDirection:1}}),this)},t.prototype.getAxis=function(t){return this._axesMap.get(t)},t.prototype.dataToPoint=function(t,e){return this.axisCoordToPoint(this._axesMap.get(e).dataToCoord(t),e)},t.prototype.eachActiveState=function(t,e,n,i){null==n&&(n=0),null==i&&(i=t.count());var o=this._axesMap,r=this.dimensions,s=[],l=[];a(r,(function(e){s.push(t.mapDimension(e)),l.push(o.get(e).model)}));for(var u=this.hasAxisBrushed(),d=n;d<i;d++){var c=void 0;if(u){c="active";for(var h=t.getValues(s,d),p=0,g=r.length;p<g;p++){if("inactive"===l[p].getActiveState(h[p])){c="inactive";break}}}else c="normal";e(c,d)}},t.prototype.hasAxisBrushed=function(){for(var t=this.dimensions,e=this._axesMap,n=!1,i=0,o=t.length;i<o;i++)"normal"!==e.get(t[i]).model.getActiveState()&&(n=!0);return n},t.prototype.axisCoordToPoint=function(t,e){var n=this._axesLayout[e];return ei([t,0],n.transform)},t.prototype.getAxisLayout=function(t){return ct(this._axesLayout[t])},t.prototype.getSlidedAxisExpandWindow=function(t){var e=this._makeLayoutInfo(),n=e.pixelDimIndex,i=e.axisExpandWindow.slice(),o=i[1]-i[0],r=[0,e.axisExpandWidth*(e.axisCount-1)];if(!this.containPoint(t))return{behavior:"none",axisExpandWindow:i};var a,s=t[n]-e.layoutBase-e.axisExpandWindow0Pos,l="slide",u=e.axisCollapseWidth,d=this._model.get("axisExpandSlideTriggerArea"),c=null!=d[0];if(u)c&&u&&s<o*d[0]?(l="jump",a=s-o*d[2]):c&&u&&s>o*(1-d[0])?(l="jump",a=s-o*(1-d[2])):(a=s-o*d[1])>=0&&(a=s-o*(1-d[1]))<=0&&(a=0),(a*=e.axisExpandWidth/u)?oh(a,i,r,"all"):l="none";else{var h=i[1]-i[0];(i=[uh(0,r[1]*s/h-h/2)])[1]=lh(r[1],i[0]+h),i[0]=i[1]-h}return{axisExpandWindow:i,behavior:l}},t}();function fh(t,e){return lh(uh(t,e[0]),e[1])}function yh(t,e){var n=e.layoutLength/(e.axisCount-1);return{position:n*t,axisNameAvailableWidth:n,axisLabelShow:!0}}function vh(t,e){var n,i,o=e.layoutLength,r=e.axisExpandWidth,a=e.axisCount,s=e.axisCollapseWidth,l=e.winInnerIndices,u=s,d=!1;return t<l[0]?(n=t*s,i=s):t<=l[1]?(n=e.axisExpandWindow0Pos+t*r-e.axisExpandWindow[0],u=r,d=!0):(n=o-(a-1-t)*s,i=s),{position:n,axisNameAvailableWidth:u,axisLabelShow:d,nameTruncateMaxWidth:i}}var mh={create:function(t,e){var n=[];return t.eachComponent("parallel",(function(i,o){var r=new gh(i,t,e);r.name="parallel_"+o,r.resize(i,e),i.coordinateSystem=r,r.model=i,n.push(r)})),t.eachSeries((function(t){if("parallel"===t.get("coordinateSystem")){var e=t.getReferringComponents("parallel",ze).models[0];t.coordinateSystem=e.coordinateSystem}})),n}},xh=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.activeIntervals=[],e}return e(n,t),n.prototype.getAreaSelectStyle=function(){return pn([["fill","color"],["lineWidth","borderWidth"],["stroke","borderColor"],["width","width"],["opacity","opacity"]])(this.getModel("areaSelectStyle"))},n.prototype.setActiveIntervals=function(t){var e=this.activeIntervals=ct(t);if(e)for(var n=e.length-1;n>=0;n--)ni(e[n])},n.prototype.getActiveState=function(t){var e=this.activeIntervals;if(!e.length)return"normal";if(null==t||isNaN(+t))return"inactive";if(1===e.length){var n=e[0];if(n[0]<=t&&t<=n[1])return"active"}else for(var i=0,o=e.length;i<o;i++)if(e[i][0]<=t&&t<=e[i][1])return"active";return"inactive"},n}(Qt);Kt(xh,Jt);var _h=!0,bh=Math.min,Sh=Math.max,wh=Math.pow,Mh="globalPan",Ih={w:[0,0],e:[0,1],n:[1,0],s:[1,1]},Ah={w:"ew",e:"ew",n:"ns",s:"ns",ne:"nesw",sw:"nesw",nw:"nwse",se:"nwse"},Th={brushStyle:{lineWidth:2,stroke:"rgba(210,219,238,0.3)",fill:"#D2DBEE"},transformable:!0,brushMode:"single",removeOnClick:!1},Ch=0,Dh=function(t){function n(e){var n=t.call(this)||this;return n._track=[],n._covers=[],n._handlers={},n._zr=e,n.group=new It,n._uid="brushController_"+Ch++,a(np,(function(t,e){this._handlers[e]=Zt(t,this)}),n),n}return e(n,t),n.prototype.enableBrush=function(t){return this._brushType&&this._doDisableBrush(),t.brushType&&this._doEnableBrush(t),this},n.prototype._doEnableBrush=function(t){var e=this._zr;this._enableGlobalPan||function(t,e,n){xs(t)[e]=n}(e,Mh,this._uid),a(this._handlers,(function(t,n){e.on(n,t)})),this._brushType=t.brushType,this._brushOption=jt(ct(Th),t,!0)},n.prototype._doDisableBrush=function(){var t=this._zr;!function(t,e,n){var i=xs(t);i[e]===n&&(i[e]=null)}(t,Mh,this._uid),a(this._handlers,(function(e,n){t.off(n,e)})),this._brushType=this._brushOption=null},n.prototype.setPanels=function(t){if(t&&t.length){var e=this._panels={};a(t,(function(t){e[t.panelId]=ct(t)}))}else this._panels=null;return this},n.prototype.mount=function(t){t=t||{},this._enableGlobalPan=t.enableGlobalPan;var e=this.group;return this._zr.add(e),e.attr({x:t.x||0,y:t.y||0,rotation:t.rotation||0,scaleX:t.scaleX||1,scaleY:t.scaleY||1}),this._transform=e.getLocalTransform(),this},n.prototype.updateCovers=function(t){t=d(t,(function(t){return jt(ct(Th),t,!0)}));var e=this._covers,n=this._covers=[],i=this,o=this._creatingCover;return new ln(e,t,(function(t,e){return r(t.__brushOption,e)}),r).add(a).update(a).remove((function(t){e[t]!==o&&i.group.remove(e[t])})).execute(),this;function r(t,e){return(null!=t.id?t.id:"\0-brush-index-"+e)+"-"+t.brushType}function a(r,a){var s=t[r];if(null!=a&&e[a]===o)n[r]=e[a];else{var l=n[r]=null!=a?(e[a].__brushOption=s,e[a]):Ph(i,Lh(i,s));Rh(i,l)}}},n.prototype.unmount=function(){return this.enableBrush(!1),zh(this),this._zr.remove(this.group),this},n.prototype.dispose=function(){this.unmount(),this.off()},n}(he);function Lh(t,e){var n=op[e.brushType].createCover(t,e);return n.__brushOption=e,Nh(n,e),t.group.add(n),n}function Ph(t,e){var n=Vh(e);return n.endCreating&&(n.endCreating(t,e),Nh(e,e.__brushOption)),e}function kh(t,e){var n=e.__brushOption;Vh(e).updateCoverShape(t,e,n.range,n)}function Nh(t,e){var n=e.z;null==n&&(n=1e4),t.traverse((function(t){t.z=n,t.z2=n}))}function Rh(t,e){Vh(e).updateCommon(t,e),kh(t,e)}function Vh(t){return op[t.__brushOption.brushType]}function Oh(t,e,n){var i,o=t._panels;if(!o)return _h;var r=t._transform;return a(o,(function(t){t.isTargetByCursor(e,n,r)&&(i=t)})),i}function Eh(t,e){var n=t._panels;if(!n)return _h;var i=e.__brushOption.panelId;return null!=i?n[i]:_h}function zh(t){var e=t._covers,n=e.length;return a(e,(function(e){t.group.remove(e)}),t),e.length=0,!!n}function Bh(t,e){var n=d(t._covers,(function(t){var e=t.__brushOption,n=ct(e.range);return{brushType:e.brushType,panelId:e.panelId,range:n}}));t.trigger("brush",{areas:n,isEnd:!!e.isEnd,removeOnClick:!!e.removeOnClick})}function Gh(t){var e=t.length-1;return e<0&&(e=0),[t[0],t[e]]}function Fh(t,e,n,i){var o=new It;return o.add(new pe({name:"main",style:Yh(n),silent:!0,draggable:!0,cursor:"move",drift:Re(jh,t,e,o,["n","s","w","e"]),ondragend:Re(Bh,e,{isEnd:!0})})),a(i,(function(n){o.add(new pe({name:n.join(""),style:{opacity:0},draggable:!0,silent:!0,invisible:!0,drift:Re(jh,t,e,o,n),ondragend:Re(Bh,e,{isEnd:!0})}))})),o}function Wh(t,e,n,i){var o=i.brushStyle.lineWidth||0,r=Sh(o,6),a=n[0][0],s=n[1][0],l=a-o/2,u=s-o/2,d=n[0][1],c=n[1][1],h=d-r+o/2,p=c-r+o/2,g=d-a,f=c-s,y=g+o,v=f+o;Zh(t,e,"main",a,s,g,f),i.transformable&&(Zh(t,e,"w",l,u,r,v),Zh(t,e,"e",h,u,r,v),Zh(t,e,"n",l,u,y,r),Zh(t,e,"s",l,p,y,r),Zh(t,e,"nw",l,u,r,r),Zh(t,e,"ne",h,u,r,r),Zh(t,e,"sw",l,p,r,r),Zh(t,e,"se",h,p,r,r))}function Hh(t,e){var n=e.__brushOption,i=n.transformable,o=e.childAt(0);o.useStyle(Yh(n)),o.attr({silent:!i,cursor:i?"move":"default"}),a([["w"],["e"],["n"],["s"],["s","e"],["s","w"],["n","e"],["n","w"]],(function(n){var o=e.childOfName(n.join("")),r=1===n.length?Uh(t,n[0]):function(t,e){var n=[Uh(t,e[0]),Uh(t,e[1])];return("e"===n[0]||"w"===n[0])&&n.reverse(),n.join("")}(t,n);o&&o.attr({silent:!i,invisible:!i,cursor:i?Ah[r]+"-resize":null})}))}function Zh(t,e,n,i,o,r,a){var s,l,u,d,c,h=e.childOfName(n);h&&h.setShape((s=Jh(t,e,[[i,o],[i+r,o+a]]),l=bh(s[0][0],s[1][0]),u=bh(s[0][1],s[1][1]),d=Sh(s[0][0],s[1][0]),c=Sh(s[0][1],s[1][1]),{x:l,y:u,width:d-l,height:c-u}))}function Yh(t){return Ot({strokeNoScale:!0},t.brushStyle)}function Xh(t,e,n,i){var o=[bh(t,n),bh(e,i)],r=[Sh(t,n),Sh(e,i)];return[[o[0],r[0]],[o[1],r[1]]]}function Uh(t,e){var n=ii({w:"left",e:"right",n:"top",s:"bottom"}[e],function(t){return oi(t.group)}(t));return{left:"w",right:"e",top:"n",bottom:"s"}[n]}function jh(t,e,n,i,o,r){var s=n.__brushOption,l=t.toRectRange(s.range),u=Kh(e,o,r);a(i,(function(t){var e=Ih[t];l[e[0]][e[1]]+=u[e[0]]})),s.range=t.fromRectRange(Xh(l[0][0],l[1][0],l[0][1],l[1][1])),Rh(e,n),Bh(e,{isEnd:!1})}function qh(t,e,n,i){var o=e.__brushOption.range,r=Kh(t,n,i);a(o,(function(t){t[0]+=r[0],t[1]+=r[1]})),Rh(t,e),Bh(t,{isEnd:!1})}function Kh(t,e,n){var i=t.group,o=i.transformCoordToLocal(e,n),r=i.transformCoordToLocal(0,0);return[o[0]-r[0],o[1]-r[1]]}function Jh(t,e,n){var i=Eh(t,e);return i&&i!==_h?i.clipPath(n,t._transform):ct(n)}function $h(t){var e=t.event;e.preventDefault&&e.preventDefault()}function Qh(t,e,n){return t.childOfName("main").contain(e,n)}function tp(t,e,n,i){var o,r=t._creatingCover,a=t._creatingPanel,s=t._brushOption;if(t._track.push(n.slice()),function(t){var e=t._track;if(!e.length)return!1;var n=e[e.length-1],i=e[0],o=n[0]-i[0],r=n[1]-i[1];return wh(o*o+r*r,.5)>6}(t)||r){if(a&&!r){"single"===s.brushMode&&zh(t);var l=ct(s);l.brushType=ep(l.brushType,a),l.panelId=a===_h?null:a.panelId,r=t._creatingCover=Lh(t,l),t._covers.push(r)}if(r){var u=op[ep(t._brushType,a)];r.__brushOption.range=u.getCreatingRange(Jh(t,r,t._track)),i&&(Ph(t,r),u.updateCommon(t,r)),kh(t,r),o={isEnd:i}}}else i&&"single"===s.brushMode&&s.removeOnClick&&Oh(t,e,n)&&zh(t)&&(o={isEnd:i,removeOnClick:!0});return o}function ep(t,e){return"auto"===t?e.defaultBrushType:t}var np={mousedown:function(t){if(this._dragging)ip(this,t);else if(!t.target||!t.target.draggable){$h(t);var e=this.group.transformCoordToLocal(t.offsetX,t.offsetY);this._creatingCover=null,(this._creatingPanel=Oh(this,t,e))&&(this._dragging=!0,this._track=[e.slice()])}},mousemove:function(t){var e=t.offsetX,n=t.offsetY,i=this.group.transformCoordToLocal(e,n);if(function(t,e,n){if(t._brushType&&!function(t,e,n){var i=t._zr;return e<0||e>i.getWidth()||n<0||n>i.getHeight()}(t,e.offsetX,e.offsetY)){var i=t._zr,o=t._covers,r=Oh(t,e,n);if(!t._dragging)for(var a=0;a<o.length;a++){var s=o[a].__brushOption;if(r&&(r===_h||s.panelId===r.panelId)&&op[s.brushType].contain(o[a],n[0],n[1]))return}r&&i.setCursorStyle("crosshair")}}(this,t,i),this._dragging){$h(t);var o=tp(this,t,i,!1);o&&Bh(this,o)}},mouseup:function(t){ip(this,t)}};function ip(t,e){if(t._dragging){$h(e);var n=e.offsetX,i=e.offsetY,o=t.group.transformCoordToLocal(n,i),r=tp(t,e,o,!0);t._dragging=!1,t._track=[],t._creatingCover=null,r&&Bh(t,r)}}var op={lineX:rp(0),lineY:rp(1),rect:{createCover:function(t,e){function n(t){return t}return Fh({toRectRange:n,fromRectRange:n},t,e,[["w"],["e"],["n"],["s"],["s","e"],["s","w"],["n","e"],["n","w"]])},getCreatingRange:function(t){var e=Gh(t);return Xh(e[1][0],e[1][1],e[0][0],e[0][1])},updateCoverShape:function(t,e,n,i){Wh(t,e,n,i)},updateCommon:Hh,contain:Qh},polygon:{createCover:function(t,e){var n=new It;return n.add(new Rt({name:"main",style:Yh(e),silent:!0})),n},getCreatingRange:function(t){return t},endCreating:function(t,e){e.remove(e.childAt(0)),e.add(new Nt({name:"main",draggable:!0,drift:Re(qh,t,e),ondragend:Re(Bh,t,{isEnd:!0})}))},updateCoverShape:function(t,e,n,i){e.childAt(0).setShape({points:Jh(t,e,n)})},updateCommon:Hh,contain:Qh}};function rp(t){return{createCover:function(e,n){return Fh({toRectRange:function(e){var n=[e,[0,100]];return t&&n.reverse(),n},fromRectRange:function(e){return e[t]}},e,n,[[["w"],["e"]],[["n"],["s"]]][t])},getCreatingRange:function(e){var n=Gh(e);return[bh(n[0][t],n[1][t]),Sh(n[0][t],n[1][t])]},updateCoverShape:function(e,n,i,o){var r,a=Eh(e,n);if(a!==_h&&a.getLinearBrushOtherExtent)r=a.getLinearBrushOtherExtent(t);else{var s=e._zr;r=[0,[s.getWidth(),s.getHeight()][1-t]]}var l=[i,r];t&&l.reverse(),Wh(e,n,l,o)},updateCommon:Hh,contain:Qh}}function ap(t){return t=up(t),function(e){return ri(e,t)}}function sp(t,e){return t=up(t),function(n){var i=null!=e?e:n,o=i?t.width:t.height,r=i?t.x:t.y;return[r,r+(o||0)]}}function lp(t,e,n){var i=up(t);return function(t,o){return i.contain(o[0],o[1])&&!Cs(t,e,n)}}function up(t){return s.create(t)}var dp=["axisLine","axisTickLabel","axisName"],cp=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.init=function(e,n){t.prototype.init.apply(this,arguments),(this._brushController=new Dh(n.getZr())).on("brush",Zt(this._onBrush,this))},n.prototype.render=function(t,e,n,i){if(!function(t,e,n){return n&&"axisAreaSelect"===n.type&&e.findComponents({mainType:"parallelAxis",query:n})[0]===t}(t,e,i)){this.axisModel=t,this.api=n,this.group.removeAll();var o=this._axisGroup;if(this._axisGroup=new It,this.group.add(this._axisGroup),t.get("show")){var r=function(t,e){return e.getComponent("parallel",t.get("parallelIndex"))}(t,e),s=r.coordinateSystem,l=t.getAreaSelectStyle(),u=l.width,d=t.axis.dim,c=s.getAxisLayout(d),h=H({strokeContainThreshold:u},c),p=new te(t,h);a(dp,p.add,p),this._axisGroup.add(p.getGroup()),this._refreshBrushController(h,l,t,r,u,n),ai(o,this._axisGroup,t)}}},n.prototype._refreshBrushController=function(t,e,n,i,o,r){var a=n.axis.getExtent(),l=a[1]-a[0],u=Math.min(30,.1*Math.abs(l)),c=s.create({x:a[0],y:-o/2,width:l,height:o});c.x-=u,c.width+=2*u,this._brushController.mount({enableGlobalPan:!0,rotation:t.rotation,x:t.position[0],y:t.position[1]}).setPanels([{panelId:"pl",clipPath:ap(c),isTargetByCursor:lp(c,r,i),getLinearBrushOtherExtent:sp(c,0)}]).enableBrush({brushType:"lineX",brushStyle:e,removeOnClick:!0}).updateCovers(function(t){var e=t.axis;return d(t.activeIntervals,(function(t){return{brushType:"lineX",panelId:"pl",range:[e.dataToCoord(t[0],!0),e.dataToCoord(t[1],!0)]}}))}(n))},n.prototype._onBrush=function(t){var e=t.areas,n=this.axisModel,i=n.axis,o=d(e,(function(t){return[i.coordToData(t.range[0],!0),i.coordToData(t.range[1],!0)]}));(!n.option.realtime===t.isEnd||t.removeOnClick)&&this.api.dispatchAction({type:"axisAreaSelect",parallelAxisId:n.id,intervals:o})},n.prototype.dispose=function(){this._brushController.dispose()},n.type="parallelAxis",n}(oe);var hp={type:"axisAreaSelect",event:"axisAreaSelected"};var pp={type:"value",areaSelectStyle:{width:20,borderWidth:1,borderColor:"rgba(160,197,232)",color:"rgba(160,197,232)",opacity:.3},realtime:!0,z:10};function gp(t){t.registerComponentView(Qc),t.registerComponentModel(nh),t.registerCoordinateSystem("parallel",mh),t.registerPreprocessor($c),t.registerComponentModel(xh),t.registerComponentView(cp),si(t,"parallel",xh,pp),function(t){t.registerAction(hp,(function(t,e){e.eachComponent({mainType:"parallelAxis",query:t},(function(e){e.axis.model.setActiveIntervals(t.intervals)}))})),t.registerAction("parallelAxisExpand",(function(t,e){e.eachComponent({mainType:"parallel",query:t},(function(e){e.setAxisExpand(t)}))}))}(t)}var fp=function(){return function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.cpx2=0,this.cpy2=0,this.extent=0}}(),yp=function(t){function n(e){return t.call(this,e)||this}return e(n,t),n.prototype.getDefaultShape=function(){return new fp},n.prototype.buildPath=function(t,e){var n=e.extent;t.moveTo(e.x1,e.y1),t.bezierCurveTo(e.cpx1,e.cpy1,e.cpx2,e.cpy2,e.x2,e.y2),"vertical"===e.orient?(t.lineTo(e.x2+n,e.y2),t.bezierCurveTo(e.cpx2+n,e.cpy2,e.cpx1+n,e.cpy1,e.x1+n,e.y1)):(t.lineTo(e.x2,e.y2+n),t.bezierCurveTo(e.cpx2,e.cpy2+n,e.cpx1,e.cpy1+n,e.x1,e.y1+n)),t.closePath()},n.prototype.highlight=function(){Nn(this)},n.prototype.downplay=function(){Rn(this)},n}(q),vp=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e._focusAdjacencyDisabled=!1,e}return e(n,t),n.prototype.render=function(t,e,n){var i=this,o=t.getGraph(),r=this.group,a=t.layoutInfo,s=a.width,l=a.height,u=t.getData(),d=t.getData("edge"),c=t.get("orient");this._model=t,r.removeAll(),r.x=a.x,r.y=a.y,o.eachEdge((function(e){var n=new yp,i=v(n);i.dataIndex=e.dataIndex,i.seriesIndex=t.seriesIndex,i.dataType="edge";var o,a,u,h,p,g,f,y,m=e.getModel(),x=m.getModel("lineStyle"),_=x.get("curveness"),b=e.node1.getLayout(),S=e.node1.getModel(),w=S.get("localX"),M=S.get("localY"),I=e.node2.getLayout(),A=e.node2.getModel(),T=A.get("localX"),C=A.get("localY"),D=e.getLayout();n.shape.extent=Math.max(1,D.dy),n.shape.orient=c,"vertical"===c?(o=(null!=w?w*s:b.x)+D.sy,a=(null!=M?M*l:b.y)+b.dy,u=(null!=T?T*s:I.x)+D.ty,p=o,g=a*(1-_)+(h=null!=C?C*l:I.y)*_,f=u,y=a*_+h*(1-_)):(o=(null!=w?w*s:b.x)+b.dx,a=(null!=M?M*l:b.y)+D.sy,p=o*(1-_)+(u=null!=T?T*s:I.x)*_,g=a,f=o*_+u*(1-_),y=h=(null!=C?C*l:I.y)+D.ty),n.setShape({x1:o,y1:a,x2:u,y2:h,cpx1:p,cpy1:g,cpx2:f,cpy2:y}),n.useStyle(x.getItemStyle()),mp(n.style,c,e);var L=""+m.get("value"),P=Bt(m,"edgeLabel");zt(n,P,{labelFetcher:{getFormattedLabel:function(e,n,i,o,r,a){return t.getFormattedLabel(e,n,"edge",o,Sn(r,P.normal&&P.normal.get("formatter"),L),a)}},labelDataIndex:e.dataIndex,defaultText:L}),n.setTextConfig({position:"inside"});var k=m.getModel("emphasis");Et(n,m,"lineStyle",(function(t){var n=t.getItemStyle();return mp(n,c,e),n})),r.add(n),d.setItemGraphicEl(e.dataIndex,n);var N=k.get("focus");Gt(n,"adjacency"===N?e.getAdjacentDataIndices():"trajectory"===N?e.getTrajectoryDataIndices():N,k.get("blurScope"),k.get("disabled"))})),o.eachNode((function(e){var n=e.getLayout(),i=e.getModel(),o=i.get("localX"),a=i.get("localY"),d=i.getModel("emphasis"),c=i.get(["itemStyle","borderRadius"])||0,h=new pe({shape:{x:null!=o?o*s:n.x,y:null!=a?a*l:n.y,width:n.dx,height:n.dy,r:c},style:i.getModel("itemStyle").getItemStyle(),z2:10});zt(h,Bt(i),{labelFetcher:{getFormattedLabel:function(e,n){return t.getFormattedLabel(e,n,"node")}},labelDataIndex:e.dataIndex,defaultText:e.id}),h.disableLabelAnimation=!0,h.setStyle("fill",e.getVisual("color")),h.setStyle("decal",e.getVisual("style").decal),Et(h,i),r.add(h),u.setItemGraphicEl(e.dataIndex,h),v(h).dataType="node";var p=d.get("focus");Gt(h,"adjacency"===p?e.getAdjacentDataIndices():"trajectory"===p?e.getTrajectoryDataIndices():p,d.get("blurScope"),d.get("disabled"))})),u.eachItemGraphicEl((function(e,o){u.getItemModel(o).get("draggable")&&(e.drift=function(e,r){i._focusAdjacencyDisabled=!0,this.shape.x+=e,this.shape.y+=r,this.dirty(),n.dispatchAction({type:"dragNode",seriesId:t.id,dataIndex:u.getRawIndex(o),localX:this.shape.x/s,localY:this.shape.y/l})},e.ondragend=function(){i._focusAdjacencyDisabled=!1},e.draggable=!0,e.cursor="move")})),!this._data&&t.isAnimationEnabled()&&r.setClipPath(function(t,e,n){var i=new pe({shape:{x:t.x-10,y:t.y-10,width:0,height:t.height+20}});return D(i,{shape:{width:t.width+20}},e,n),i}(r.getBoundingRect(),t,(function(){r.removeClipPath()}))),this._data=t.getData()},n.prototype.dispose=function(){},n.type="sankey",n}(Dt);function mp(t,e,n){switch(t.fill){case"source":t.fill=n.node1.getVisual("color"),t.decal=n.node1.getVisual("style").decal;break;case"target":t.fill=n.node2.getVisual("color"),t.decal=n.node2.getVisual("style").decal;break;case"gradient":var i=n.node1.getVisual("color"),o=n.node2.getVisual("color");G(i)&&G(o)&&(t.fill=new xe(0,0,+("horizontal"===e),+("vertical"===e),[{color:i,offset:0},{color:o,offset:1}]))}}var xp=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.getInitialData=function(t,e){var n=t.edges||t.links||[],i=t.data||t.nodes||[],o=t.levels||[];this.levelModels=[];for(var r=this.levelModels,a=0;a<o.length;a++)null!=o[a].depth&&o[a].depth>=0&&(r[o[a].depth]=new qt(o[a],this,e));return Lc(i,n,this,!0,(function(t,e){t.wrapMethod("getItemModel",(function(t,e){var n=t.parentModel,i=n.getData().getItemLayout(e);if(i){var o=i.depth,r=n.levelModels[o];r&&(t.parentModel=r)}return t})),e.wrapMethod("getItemModel",(function(t,e){var n=t.parentModel,i=n.getGraph().getEdgeByIndex(e).node1.getLayout();if(i){var o=i.depth,r=n.levelModels[o];r&&(t.parentModel=r)}return t}))})).data},n.prototype.setNodePosition=function(t,e){var n=(this.option.data||this.option.nodes)[t];n.localX=e[0],n.localY=e[1]},n.prototype.getGraph=function(){return this.getData().graph},n.prototype.getEdgeData=function(){return this.getGraph().edgeData},n.prototype.formatTooltip=function(t,e,n){function i(t){return isNaN(t)||null==t}if("edge"===n){var o=this.getDataParams(t,n),r=o.data,a=o.value,s=r.source+" -- "+r.target;return Ut("nameValue",{name:s,value:a,noValue:i(a)})}var l=this.getGraph().getNodeByIndex(t).getLayout().value,u=this.getDataParams(t,n).data.name;return Ut("nameValue",{name:null!=u?u+"":null,value:l,noValue:i(l)})},n.prototype.optionUpdated=function(){},n.prototype.getDataParams=function(e,n){var i=t.prototype.getDataParams.call(this,e,n);if(null==i.value&&"node"===n){var o=this.getGraph().getNodeByIndex(e).getLayout().value;i.value=o}return i},n.type="series.sankey",n.defaultOption={z:2,coordinateSystem:"view",left:"5%",top:"5%",right:"20%",bottom:"5%",orient:"horizontal",nodeWidth:20,nodeGap:8,draggable:!0,layoutIterations:32,label:{show:!0,position:"right",fontSize:12},edgeLabel:{show:!1,fontSize:12},levels:[],nodeAlign:"justify",lineStyle:{color:"#314656",opacity:.2,curveness:.5},emphasis:{label:{show:!0},lineStyle:{opacity:.5}},select:{itemStyle:{borderColor:"#212121"}},animationEasing:"linear",animationDuration:1e3},n}(Mt);function _p(t,e){t.eachSeriesByType("sankey",(function(t){var n=t.get("nodeWidth"),i=t.get("nodeGap"),o=function(t,e){return Fe(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}(t,e);t.layoutInfo=o;var r=o.width,s=o.height,l=t.getGraph(),u=l.nodes,d=l.edges;!function(t){a(t,(function(t){var e=Lp(t.outEdges,Dp),n=Lp(t.inEdges,Dp),i=t.getValue()||0,o=Math.max(e,n,i);t.setLayout({value:o},!0)}))}(u),function(t,e,n,i,o,r,s,l,u){(function(t,e,n,i,o,r,s){for(var l=[],u=[],d=[],c=[],h=0,p=0;p<e.length;p++)l[p]=1;for(p=0;p<t.length;p++)u[p]=t[p].inEdges.length,0===u[p]&&d.push(t[p]);var g=-1;for(;d.length;){for(var f=0;f<d.length;f++){var y=d[f],v=y.hostGraph.data.getRawDataItem(y.dataIndex),m=null!=v.depth&&v.depth>=0;m&&v.depth>g&&(g=v.depth),y.setLayout({depth:m?v.depth:h},!0),"vertical"===r?y.setLayout({dy:n},!0):y.setLayout({dx:n},!0);for(var x=0;x<y.outEdges.length;x++){var _=y.outEdges[x];l[e.indexOf(_)]=0;var b=_.node2;0==--u[t.indexOf(b)]&&c.indexOf(b)<0&&c.push(b)}}++h,d=c,c=[]}for(p=0;p<l.length;p++)if(1===l[p])throw new Error("Sankey is a DAG, the original data has cycle!");var S=g>h-1?g:h-1;s&&"left"!==s&&function(t,e,n,i){if("right"===e){for(var o=[],r=t,s=0;r.length;){for(var l=0;l<r.length;l++){var u=r[l];u.setLayout({skNodeHeight:s},!0);for(var d=0;d<u.inEdges.length;d++){var c=u.inEdges[d];o.indexOf(c.node1)<0&&o.push(c.node1)}}r=o,o=[],++s}a(t,(function(t){bp(t)||t.setLayout({depth:Math.max(0,i-t.getLayout().skNodeHeight)},!0)}))}else"justify"===e&&function(t,e){a(t,(function(t){bp(t)||t.outEdges.length||t.setLayout({depth:e},!0)}))}(t,i)}(t,s,0,S);var w="vertical"===r?(o-n)/S:(i-n)/S;!function(t,e,n){a(t,(function(t){var i=t.getLayout().depth*e;"vertical"===n?t.setLayout({y:i},!0):t.setLayout({x:i},!0)}))}(t,w,r)})(t,e,n,o,r,l,u),function(t,e,n,i,o,r,s){var l=function(t,e){var n=[],i="vertical"===e?"y":"x",o=li(t,(function(t){return t.getLayout()[i]}));return o.keys.sort((function(t,e){return t-e})),a(o.keys,(function(t){n.push(o.buckets.get(t))})),n}(t,s);(function(t,e,n,i,o,r){var s=1/0;a(t,(function(t){var e=t.length,l=0;a(t,(function(t){l+=t.getLayout().value}));var u="vertical"===r?(i-(e-1)*o)/l:(n-(e-1)*o)/l;u<s&&(s=u)})),a(t,(function(t){a(t,(function(t,e){var n=t.getLayout().value*s;"vertical"===r?(t.setLayout({x:e},!0),t.setLayout({dx:n},!0)):(t.setLayout({y:e},!0),t.setLayout({dy:n},!0))}))})),a(e,(function(t){var e=+t.getValue()*s;t.setLayout({dy:e},!0)}))})(l,e,n,i,o,s),Sp(l,o,n,i,s);for(var u=1;r>0;r--)wp(l,u*=.99,s),Sp(l,o,n,i,s),Pp(l,u,s),Sp(l,o,n,i,s)}(t,e,r,o,i,s,l),function(t,e){var n="vertical"===e?"x":"y";a(t,(function(t){t.outEdges.sort((function(t,e){return t.node2.getLayout()[n]-e.node2.getLayout()[n]})),t.inEdges.sort((function(t,e){return t.node1.getLayout()[n]-e.node1.getLayout()[n]}))})),a(t,(function(t){var e=0,n=0;a(t.outEdges,(function(t){t.setLayout({sy:e},!0),e+=t.getLayout().dy})),a(t.inEdges,(function(t){t.setLayout({ty:n},!0),n+=t.getLayout().dy}))}))}(t,l)}(u,d,n,i,r,s,0!==c(u,(function(t){return 0===t.getLayout().value})).length?0:t.get("layoutIterations"),t.get("orient"),t.get("nodeAlign"))}))}function bp(t){var e=t.hostGraph.data.getRawDataItem(t.dataIndex);return null!=e.depth&&e.depth>=0}function Sp(t,e,n,i,o){var r="vertical"===o?"x":"y";a(t,(function(t){var a,s,l;t.sort((function(t,e){return t.getLayout()[r]-e.getLayout()[r]}));for(var u=0,d=t.length,c="vertical"===o?"dx":"dy",h=0;h<d;h++)(l=u-(s=t[h]).getLayout()[r])>0&&(a=s.getLayout()[r]+l,"vertical"===o?s.setLayout({x:a},!0):s.setLayout({y:a},!0)),u=s.getLayout()[r]+s.getLayout()[c]+e;if((l=u-e-("vertical"===o?i:n))>0){a=s.getLayout()[r]-l,"vertical"===o?s.setLayout({x:a},!0):s.setLayout({y:a},!0),u=a;for(h=d-2;h>=0;--h)(l=(s=t[h]).getLayout()[r]+s.getLayout()[c]+e-u)>0&&(a=s.getLayout()[r]-l,"vertical"===o?s.setLayout({x:a},!0):s.setLayout({y:a},!0)),u=s.getLayout()[r]}}))}function wp(t,e,n){a(t.slice().reverse(),(function(t){a(t,(function(t){if(t.outEdges.length){var i=Lp(t.outEdges,Mp,n)/Lp(t.outEdges,Dp);if(isNaN(i)){var o=t.outEdges.length;i=o?Lp(t.outEdges,Ip,n)/o:0}if("vertical"===n){var r=t.getLayout().x+(i-Cp(t,n))*e;t.setLayout({x:r},!0)}else{var a=t.getLayout().y+(i-Cp(t,n))*e;t.setLayout({y:a},!0)}}}))}))}function Mp(t,e){return Cp(t.node2,e)*t.getValue()}function Ip(t,e){return Cp(t.node2,e)}function Ap(t,e){return Cp(t.node1,e)*t.getValue()}function Tp(t,e){return Cp(t.node1,e)}function Cp(t,e){return"vertical"===e?t.getLayout().x+t.getLayout().dx/2:t.getLayout().y+t.getLayout().dy/2}function Dp(t){return t.getValue()}function Lp(t,e,n){for(var i=0,o=t.length,r=-1;++r<o;){var a=+e(t[r],n);isNaN(a)||(i+=a)}return i}function Pp(t,e,n){a(t,(function(t){a(t,(function(t){if(t.inEdges.length){var i=Lp(t.inEdges,Ap,n)/Lp(t.inEdges,Dp);if(isNaN(i)){var o=t.inEdges.length;i=o?Lp(t.inEdges,Tp,n)/o:0}if("vertical"===n){var r=t.getLayout().x+(i-Cp(t,n))*e;t.setLayout({x:r},!0)}else{var a=t.getLayout().y+(i-Cp(t,n))*e;t.setLayout({y:a},!0)}}}))}))}function kp(t){t.eachSeriesByType("sankey",(function(t){var e=t.getGraph(),n=e.nodes,i=e.edges;if(n.length){var o=1/0,r=-1/0;a(n,(function(t){var e=t.getLayout().value;e<o&&(o=e),e>r&&(r=e)})),a(n,(function(e){var n=new Ju({type:"color",mappingMethod:"linear",dataExtent:[o,r],visual:t.get("color")}).mapValueToVisual(e.getLayout().value),i=e.getModel().get(["itemStyle","color"]);null!=i?(e.setVisual("color",i),e.setVisual("style",{fill:i})):(e.setVisual("color",n),e.setVisual("style",{fill:n}))}))}i.length&&a(i,(function(t){var e=t.getModel().get("lineStyle");t.setVisual("style",e)}))}))}var Np=function(){function t(){}return t.prototype._hasEncodeRule=function(t){var e=this.getEncode();return e&&null!=e.get(t)},t.prototype.getInitialData=function(t,e){var n,i,o=e.getComponent("xAxis",this.get("xAxisIndex")),r=e.getComponent("yAxis",this.get("yAxisIndex")),s=o.get("type"),l=r.get("type");"category"===s?(t.layout="horizontal",n=o.getOrdinalMeta(),i=!this._hasEncodeRule("x")):"category"===l?(t.layout="vertical",n=r.getOrdinalMeta(),i=!this._hasEncodeRule("y")):t.layout=t.layout||"horizontal";var u=["x","y"],d="horizontal"===t.layout?0:1,c=this._baseAxisDim=u[d],h=u[1-d],p=[o,r],g=p[d].get("type"),f=p[1-d].get("type"),y=t.data;if(y&&i){var v=[];a(y,(function(t,e){var n;mt(t)?(n=t.slice(),t.unshift(e)):mt(t.value)?((n=H({},t)).value=n.value.slice(),t.value.unshift(e)):n=t,v.push(n)})),t.data=v}var m=this.defaultValueDimensions,x=[{name:c,type:ui(g),ordinalMeta:n,otherDims:{tooltip:!1,itemName:0},dimsDef:["base"]},{name:h,type:ui(f),dimsDef:m.slice()}];return Yt(this,{coordDimensions:x,dimensionsCount:m.length+1,encodeDefaulter:Re(di,x,this)})},t.prototype.getBaseAxis=function(){var t=this._baseAxisDim;return this.ecModel.getComponent(t+"Axis",this.get(t+"AxisIndex")).axis},t}(),Rp=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.defaultValueDimensions=[{name:"min",defaultTooltip:!0},{name:"Q1",defaultTooltip:!0},{name:"median",defaultTooltip:!0},{name:"Q3",defaultTooltip:!0},{name:"max",defaultTooltip:!0}],e.visualDrawType="stroke",e}return e(n,t),n.type="series.boxplot",n.dependencies=["xAxis","yAxis","grid"],n.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,layout:null,boxWidth:[7,50],itemStyle:{color:"#fff",borderWidth:1},emphasis:{scale:!0,itemStyle:{borderWidth:2,shadowBlur:5,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0,0,0,0.2)"}},animationDuration:800},n}(Mt);Kt(Rp,Np,!0);var Vp=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.render=function(t,e,n){var i=t.getData(),o=this.group,r=this._data;this._data||o.removeAll();var a="horizontal"===t.get("layout")?1:0;i.diff(r).add((function(t){if(i.hasValue(t)){var e=zp(i.getItemLayout(t),i,t,a,!0);i.setItemGraphicEl(t,e),o.add(e)}})).update((function(t,e){var n=r.getItemGraphicEl(e);if(i.hasValue(t)){var s=i.getItemLayout(t);n?(Vt(n),Bp(s,n,i,t)):n=zp(s,i,t,a),o.add(n),i.setItemGraphicEl(t,n)}else o.remove(n)})).remove((function(t){var e=r.getItemGraphicEl(t);e&&o.remove(e)})).execute(),this._data=i},n.prototype.remove=function(t){var e=this.group,n=this._data;this._data=null,n&&n.eachItemGraphicEl((function(t){t&&e.remove(t)}))},n.type="boxplot",n}(Dt),Op=function(){return function(){}}(),Ep=function(t){function n(e){var n=t.call(this,e)||this;return n.type="boxplotBoxPath",n}return e(n,t),n.prototype.getDefaultShape=function(){return new Op},n.prototype.buildPath=function(t,e){var n=e.points,i=0;for(t.moveTo(n[i][0],n[i][1]),i++;i<4;i++)t.lineTo(n[i][0],n[i][1]);for(t.closePath();i<n.length;i++)t.moveTo(n[i][0],n[i][1]),i++,t.lineTo(n[i][0],n[i][1])},n}(q);function zp(t,e,n,i,o){var r=t.ends,a=new Ep({shape:{points:o?Gp(r,i,t):r}});return Bp(t,a,e,n,o),a}function Bp(t,e,n,i,o){var r=n.hostModel;(0,Ft[o?"initProps":"updateProps"])(e,{shape:{points:t.ends}},r,i),e.useStyle(n.getItemVisual(i,"style")),e.style.strokeNoScale=!0,e.z2=100;var a=n.getItemModel(i),s=a.getModel("emphasis");Et(e,a),Gt(e,s.get("focus"),s.get("blurScope"),s.get("disabled"))}function Gp(t,e,n){return d(t,(function(t){return(t=t.slice())[e]=n.initBaseline,t}))}var Fp=a;function Wp(t){var e=function(t){var e=[],n=[];return t.eachSeriesByType("boxplot",(function(t){var i=t.getBaseAxis(),o=L(n,i);o<0&&(o=n.length,n[o]=i,e[o]={axis:i,seriesModels:[]}),e[o].seriesModels.push(t)})),e}(t);Fp(e,(function(t){var e=t.seriesModels;e.length&&(!function(t){var e,n=t.axis,i=t.seriesModels,o=i.length,r=t.boxWidthList=[],a=t.boxOffsetList=[],s=[];if("category"===n.type)e=n.getBandWidth();else{var l=0;Fp(i,(function(t){l=Math.max(l,t.getData().count())}));var u=n.getExtent();e=Math.abs(u[1]-u[0])/l}Fp(i,(function(t){var n=t.get("boxWidth");mt(n)||(n=[n,n]),s.push([m(n[0],e)||0,m(n[1],e)||0])}));var d=.8*e-2,c=d/o*.3,h=(d-c*(o-1))/o,p=h/2-d/2;Fp(i,(function(t,e){a.push(p),p+=c+h,r.push(Math.min(Math.max(h,s[e][0]),s[e][1]))}))}(t),Fp(e,(function(e,n){!function(t,e,n){var i=t.coordinateSystem,o=t.getData(),r=n/2,a="horizontal"===t.get("layout")?0:1,s=1-a,l=["x","y"],u=o.mapDimension(l[a]),d=o.mapDimensionsAll(l[s]);if(null==u||d.length<5)return;for(var c=0;c<o.count();c++){var h=o.get(u,c),p=x(h,d[2],c),g=x(h,d[0],c),f=x(h,d[1],c),y=x(h,d[3],c),v=x(h,d[4],c),m=[];_(m,f,!1),_(m,y,!0),m.push(g,f,v,y),b(m,g),b(m,v),b(m,p),o.setItemLayout(c,{initBaseline:p[s],ends:m})}function x(t,n,r){var l,u=o.get(n,r),d=[];return d[a]=t,d[s]=u,isNaN(t)||isNaN(u)?l=[NaN,NaN]:(l=i.dataToPoint(d))[a]+=e,l}function _(t,e,n){var i=e.slice(),o=e.slice();i[a]+=r,o[a]-=r,n?t.push(i,o):t.push(o,i)}function b(t,e){var n=e.slice(),i=e.slice();n[a]-=r,i[a]+=r,t.push(n,i)}}(e,t.boxOffsetList[n],t.boxWidthList[n])})))}))}var Hp={type:"echarts:boxplot",transform:function(t){var e=t.upstream;if(e.sourceFormat!==hi){pi("")}var n=function(t,e){for(var n=[],i=[],o=(e=e||{}).boundIQR,r="none"===o||0===o,a=0;a<t.length;a++){var s=ni(t[a].slice()),l=ci(s,.25),u=ci(s,.5),d=ci(s,.75),c=s[0],h=s[s.length-1],p=(null==o?1.5:o)*(d-l),g=r?c:Math.max(c,l-p),y=r?h:Math.min(h,d+p),v=e.itemNameFormatter,m=f(v)?v({value:a}):G(v)?v.replace("{value}",a+""):a+"";n.push([m,g,l,u,d,y]);for(var x=0;x<s.length;x++){var _=s[x];if(_<g||_>y){var b=[m,_];i.push(b)}}}return{boxData:n,outliers:i}}(e.getRawData(),t.config);return[{dimensions:["ItemName","Low","Q1","Q2","Q3","High"],data:n.boxData},{data:n.outliers}]}};var Zp=["itemStyle","borderColor"],Yp=["itemStyle","borderColor0"],Xp=["itemStyle","borderColorDoji"],Up=["itemStyle","color"],jp=["itemStyle","color0"];function qp(t,e){return e.get(t>0?Up:jp)}function Kp(t,e){return e.get(0===t?Xp:t>0?Zp:Yp)}var Jp={seriesType:"candlestick",plan:gi(),performRawSeries:!0,reset:function(t,e){if(!e.isSeriesFiltered(t))return!t.pipelineContext.large&&{progress:function(t,e){for(var n;null!=(n=t.next());){var i=e.getItemModel(n),o=e.getItemLayout(n).sign,r=i.getItemStyle();r.fill=qp(o,i),r.stroke=Kp(o,i)||r.fill;var a=e.ensureUniqueItemVisual(n,"style");H(a,r)}}}}},$p=["color","borderColor"],Qp=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.render=function(t,e,n){this.group.removeClipPath(),this._progressiveEls=null,this._updateDrawMode(t),this._isLargeDraw?this._renderLarge(t):this._renderNormal(t)},n.prototype.incrementalPrepareRender=function(t,e,n){this._clear(),this._updateDrawMode(t)},n.prototype.incrementalRender=function(t,e,n,i){this._progressiveEls=[],this._isLargeDraw?this._incrementalRenderLarge(t,e):this._incrementalRenderNormal(t,e)},n.prototype.eachRendered=function(t){On(this._progressiveEls||this.group,t)},n.prototype._updateDrawMode=function(t){var e=t.pipelineContext.large;null!=this._isLargeDraw&&e===this._isLargeDraw||(this._isLargeDraw=e,this._clear())},n.prototype._renderNormal=function(t){var e=t.getData(),n=this._data,i=this.group,o=e.getLayout("isSimpleBox"),r=t.get("clip",!0),a=t.coordinateSystem,s=a.getArea&&a.getArea();this._data||i.removeAll(),e.diff(n).add((function(n){if(e.hasValue(n)){var a=e.getItemLayout(n);if(r&&ig(s,a))return;var l=ng(a,n,!0);D(l,{shape:{points:a.ends}},t,n),og(l,e,n,o),i.add(l),e.setItemGraphicEl(n,l)}})).update((function(a,l){var u=n.getItemGraphicEl(l);if(e.hasValue(a)){var d=e.getItemLayout(a);r&&ig(s,d)?i.remove(u):(u?(P(u,{shape:{points:d.ends}},t,a),Vt(u)):u=ng(d),og(u,e,a,o),i.add(u),e.setItemGraphicEl(a,u))}else i.remove(u)})).remove((function(t){var e=n.getItemGraphicEl(t);e&&i.remove(e)})).execute(),this._data=e},n.prototype._renderLarge=function(t){this._clear(),lg(t,this.group);var e=t.get("clip",!0)?fi(t.coordinateSystem,!1,t):null;e?this.group.setClipPath(e):this.group.removeClipPath()},n.prototype._incrementalRenderNormal=function(t,e){for(var n,i=e.getData(),o=i.getLayout("isSimpleBox");null!=(n=t.next());){var r=ng(i.getItemLayout(n));og(r,i,n,o),r.incremental=!0,this.group.add(r),this._progressiveEls.push(r)}},n.prototype._incrementalRenderLarge=function(t,e){lg(e,this.group,this._progressiveEls,!0)},n.prototype.remove=function(t){this._clear()},n.prototype._clear=function(){this.group.removeAll(),this._data=null},n.type="candlestick",n}(Dt),tg=function(){return function(){}}(),eg=function(t){function n(e){var n=t.call(this,e)||this;return n.type="normalCandlestickBox",n}return e(n,t),n.prototype.getDefaultShape=function(){return new tg},n.prototype.buildPath=function(t,e){var n=e.points;this.__simpleBox?(t.moveTo(n[4][0],n[4][1]),t.lineTo(n[6][0],n[6][1])):(t.moveTo(n[0][0],n[0][1]),t.lineTo(n[1][0],n[1][1]),t.lineTo(n[2][0],n[2][1]),t.lineTo(n[3][0],n[3][1]),t.closePath(),t.moveTo(n[4][0],n[4][1]),t.lineTo(n[5][0],n[5][1]),t.moveTo(n[6][0],n[6][1]),t.lineTo(n[7][0],n[7][1]))},n}(q);function ng(t,e,n){var i=t.ends;return new eg({shape:{points:n?rg(i,t):i},z2:100})}function ig(t,e){for(var n=!0,i=0;i<e.ends.length;i++)if(t.contain(e.ends[i][0],e.ends[i][1])){n=!1;break}return n}function og(t,e,n,i){var o=e.getItemModel(n);t.useStyle(e.getItemVisual(n,"style")),t.style.strokeNoScale=!0,t.__simpleBox=i,Et(t,o);var r=e.getItemLayout(n).sign;a(t.states,(function(t,e){var n=o.getModel(e),i=qp(r,n),a=Kp(r,n)||i,s=t.style||(t.style={});i&&(s.fill=i),a&&(s.stroke=a)}));var s=o.getModel("emphasis");Gt(t,s.get("focus"),s.get("blurScope"),s.get("disabled"))}function rg(t,e){return d(t,(function(t){return(t=t.slice())[1]=e.initBaseline,t}))}var ag=function(){return function(){}}(),sg=function(t){function n(e){var n=t.call(this,e)||this;return n.type="largeCandlestickBox",n}return e(n,t),n.prototype.getDefaultShape=function(){return new ag},n.prototype.buildPath=function(t,e){for(var n=e.points,i=0;i<n.length;)if(this.__sign===n[i++]){var o=n[i++];t.moveTo(o,n[i++]),t.lineTo(o,n[i++])}else i+=3},n}(q);function lg(t,e,n,i){var o=t.getData().getLayout("largePoints"),r=new sg({shape:{points:o},__sign:1,ignoreCoarsePointer:!0});e.add(r);var a=new sg({shape:{points:o},__sign:-1,ignoreCoarsePointer:!0});e.add(a);var s=new sg({shape:{points:o},__sign:0,ignoreCoarsePointer:!0});e.add(s),ug(1,r,t),ug(-1,a,t),ug(0,s,t),i&&(r.incremental=!0,a.incremental=!0),n&&n.push(r,a)}function ug(t,e,n,i){var o=Kp(t,n)||qp(t,n),r=n.getModel("itemStyle").getItemStyle($p);e.useStyle(r),e.style.fill=null,e.style.stroke=o}var dg=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.defaultValueDimensions=[{name:"open",defaultTooltip:!0},{name:"close",defaultTooltip:!0},{name:"lowest",defaultTooltip:!0},{name:"highest",defaultTooltip:!0}],e}return e(n,t),n.prototype.getShadowDim=function(){return"open"},n.prototype.brushSelector=function(t,e,n){var i=e.getItemLayout(t);return i&&n.rect(i.brushRect)},n.type="series.candlestick",n.dependencies=["xAxis","yAxis","grid"],n.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,layout:null,clip:!0,itemStyle:{color:"#eb5454",color0:"#47b262",borderColor:"#eb5454",borderColor0:"#47b262",borderColorDoji:null,borderWidth:1},emphasis:{itemStyle:{borderWidth:2}},barMaxWidth:null,barMinWidth:null,barWidth:null,large:!0,largeThreshold:600,progressive:3e3,progressiveThreshold:1e4,progressiveChunkMode:"mod",animationEasing:"linear",animationDuration:300},n}(Mt);function cg(t){t&&mt(t.series)&&a(t.series,(function(t){xt(t)&&"k"===t.type&&(t.type="candlestick")}))}Kt(dg,Np,!0);var hg={seriesType:"candlestick",plan:gi(),reset:function(t){var e=t.coordinateSystem,n=t.getData(),i=function(t,e){var n,i=t.getBaseAxis(),o="category"===i.type?i.getBandWidth():(n=i.getExtent(),Math.abs(n[1]-n[0])/e.count()),r=m(C(t.get("barMaxWidth"),o),o),a=m(C(t.get("barMinWidth"),1),o),s=t.get("barWidth");return null!=s?m(s,o):Math.max(Math.min(o/2,r),a)}(t,n),o=["x","y"],r=n.getDimensionIndex(n.mapDimension(o[0])),a=d(n.mapDimensionsAll(o[1]),n.getDimensionIndex,n),s=a[0],l=a[1],u=a[2],c=a[3];if(n.setLayout({candleWidth:i,isSimpleBox:i<=1.3}),!(r<0||a.length<4))return{progress:t.pipelineContext.large?function(n,i){var o,a,d=vi(4*n.count),h=0,p=[],g=[],f=i.getStore(),y=!!t.get(["itemStyle","borderColorDoji"]);for(;null!=(a=n.next());){var v=f.get(r,a),m=f.get(s,a),x=f.get(l,a),_=f.get(u,a),b=f.get(c,a);isNaN(v)||isNaN(_)||isNaN(b)?(d[h++]=NaN,h+=3):(d[h++]=pg(f,a,m,x,l,y),p[0]=v,p[1]=_,o=e.dataToPoint(p,null,g),d[h++]=o?o[0]:NaN,d[h++]=o?o[1]:NaN,p[1]=b,o=e.dataToPoint(p,null,g),d[h++]=o?o[1]:NaN)}i.setLayout("largePoints",d)}:function(t,n){var o,a=n.getStore();for(;null!=(o=t.next());){var d=a.get(r,o),h=a.get(s,o),p=a.get(l,o),g=a.get(u,o),f=a.get(c,o),y=Math.min(h,p),v=Math.max(h,p),m=M(y,d),x=M(v,d),_=M(g,d),b=M(f,d),S=[];I(S,x,0),I(S,m,1),S.push(T(b),T(x),T(_),T(m));var w=!!n.getItemModel(o).get(["itemStyle","borderColorDoji"]);n.setItemLayout(o,{sign:pg(a,o,h,p,l,w),initBaseline:h>p?x[1]:m[1],ends:S,brushRect:A(g,f,d)})}function M(t,n){var i=[];return i[0]=n,i[1]=t,isNaN(n)||isNaN(t)?[NaN,NaN]:e.dataToPoint(i)}function I(t,e,n){var o=e.slice(),r=e.slice();o[0]=yi(o[0]+i/2,1,!1),r[0]=yi(r[0]-i/2,1,!0),n?t.push(o,r):t.push(r,o)}function A(t,e,n){var o=M(t,n),r=M(e,n);return o[0]-=i/2,r[0]-=i/2,{x:o[0],y:o[1],width:i,height:r[1]-o[1]}}function T(t){return t[0]=yi(t[0],1),t}}}}};function pg(t,e,n,i,o,r){return n>i?-1:n<i?1:r?0:e>0?t.get(o,e-1)<=i?1:-1:1}function gg(t,e){var n=e.rippleEffectColor||e.color;t.eachChild((function(t){t.attr({z:e.z,zlevel:e.zlevel,style:{stroke:"stroke"===e.brushType?n:null,fill:"fill"===e.brushType?n:null}})}))}var fg=function(t){function n(e,n){var i=t.call(this)||this,o=new Ue(e,n),r=new It;return i.add(o),i.add(r),i.updateData(e,n),i}return e(n,t),n.prototype.stopEffectAnimation=function(){this.childAt(1).removeAll()},n.prototype.startEffectAnimation=function(t){for(var e=t.symbolType,n=t.color,i=t.rippleNumber,o=this.childAt(1),r=0;r<i;r++){var a=At(e,-1,-1,2,2,n);a.attr({style:{strokeNoScale:!0},z2:99,silent:!0,scaleX:.5,scaleY:.5});var s=-r/i*t.period+t.effectOffset;a.animate("",!0).when(t.period,{scaleX:t.rippleScale/2,scaleY:t.rippleScale/2}).delay(s).start(),a.animateStyle(!0).when(t.period,{opacity:0}).delay(s).start(),o.add(a)}gg(o,t)},n.prototype.updateEffectAnimation=function(t){for(var e=this._effectCfg,n=this.childAt(1),i=["symbolType","period","rippleScale","rippleNumber"],o=0;o<i.length;o++){var r=i[o];if(e[r]!==t[r])return this.stopEffectAnimation(),void this.startEffectAnimation(t)}gg(n,t)},n.prototype.highlight=function(){Nn(this)},n.prototype.downplay=function(){Rn(this)},n.prototype.getSymbolType=function(){var t=this.childAt(0);return t&&t.getSymbolType()},n.prototype.updateData=function(t,e){var n=this,i=t.hostModel;this.childAt(0).updateData(t,e);var o=this.childAt(1),r=t.getItemModel(e),a=t.getItemVisual(e,"symbol"),s=Wt(t.getItemVisual(e,"symbolSize")),l=t.getItemVisual(e,"style"),u=l&&l.fill,d=r.getModel("emphasis");o.setScale(s),o.traverse((function(t){t.setStyle("fill",u)}));var c=Vn(t.getItemVisual(e,"symbolOffset"),s);c&&(o.x=c[0],o.y=c[1]);var h=t.getItemVisual(e,"symbolRotate");o.rotation=(h||0)*Math.PI/180||0;var p={};p.showEffectOn=i.get("showEffectOn"),p.rippleScale=r.get(["rippleEffect","scale"]),p.brushType=r.get(["rippleEffect","brushType"]),p.period=1e3*r.get(["rippleEffect","period"]),p.effectOffset=e/t.count(),p.z=i.getShallow("z")||0,p.zlevel=i.getShallow("zlevel")||0,p.symbolType=a,p.color=u,p.rippleEffectColor=r.get(["rippleEffect","color"]),p.rippleNumber=r.get(["rippleEffect","number"]),"render"===p.showEffectOn?(this._effectCfg?this.updateEffectAnimation(p):this.startEffectAnimation(p),this._effectCfg=p):(this._effectCfg=null,this.stopEffectAnimation(),this.onHoverStateChange=function(t){"emphasis"===t?"render"!==p.showEffectOn&&n.startEffectAnimation(p):"normal"===t&&"render"!==p.showEffectOn&&n.stopEffectAnimation()}),this._effectCfg=p,Gt(this,d.get("focus"),d.get("blurScope"),d.get("disabled"))},n.prototype.fadeOut=function(t){t&&t()},n}(It),yg=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.init=function(){this._symbolDraw=new Ct(fg)},n.prototype.render=function(t,e,n){var i=t.getData(),o=this._symbolDraw;o.updateData(i,{clipShape:this._getClipShape(t)}),this.group.add(o.group)},n.prototype._getClipShape=function(t){var e=t.coordinateSystem,n=e&&e.getArea&&e.getArea();return t.get("clip",!0)?n:null},n.prototype.updateTransform=function(t,e,n){var i=t.getData();this.group.dirty();var o=Tt("").reset(t,e,n);o.progress&&o.progress({start:0,end:i.count(),count:i.count()},i),this._symbolDraw.updateLayout()},n.prototype._updateGroupTransform=function(t){var e=t.coordinateSystem;e&&e.getRoamTransform&&(this.group.transform=mi(e.getRoamTransform()),this.group.decomposeTransform())},n.prototype.remove=function(t,e){this._symbolDraw&&this._symbolDraw.remove(!0)},n.type="effectScatter",n}(Dt),vg=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.hasSymbolVisual=!0,e}return e(n,t),n.prototype.getInitialData=function(t,e){return wt(null,this,{useEncodeDefaulter:!0})},n.prototype.brushSelector=function(t,e,n){return n.point(e.getItemLayout(t))},n.type="series.effectScatter",n.dependencies=["grid","polar"],n.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,effectType:"ripple",progressive:0,showEffectOn:"render",clip:!0,rippleEffect:{period:4,scale:2.5,brushType:"fill",number:3},universalTransition:{divideShape:"clone"},symbolSize:10},n}(Mt);var mg=function(t){function n(e,n,i){var o=t.call(this)||this;return o.add(o.createLine(e,n,i)),o._updateEffectSymbol(e,n),o}return e(n,t),n.prototype.createLine=function(t,e,n){return new dc(t,e,n)},n.prototype._updateEffectSymbol=function(t,e){var n=t.getItemModel(e).getModel("effect"),i=n.get("symbolSize"),o=n.get("symbol");mt(i)||(i=[i,i]);var r=t.getItemVisual(e,"style"),a=n.get("color")||r&&r.stroke,s=this.childAt(1);this._symbolType!==o&&(this.remove(s),(s=At(o,-.5,-.5,1,1,a)).z2=100,s.culling=!0,this.add(s)),s&&(s.setStyle("shadowColor",a),s.setStyle(n.getItemStyle(["color"])),s.scaleX=i[0],s.scaleY=i[1],s.setColor(a),this._symbolType=o,this._symbolScale=i,this._updateEffectAnimation(t,n,e))},n.prototype._updateEffectAnimation=function(t,e,n){var i=this.childAt(1);if(i){var o=t.getItemLayout(n),r=1e3*e.get("period"),a=e.get("loop"),s=e.get("roundTrip"),l=e.get("constantSpeed"),u=Ge(e.get("delay"),(function(e){return e/t.count()*r/3}));if(i.ignore=!0,this._updateAnimationPoints(i,o),l>0&&(r=this._getLineLength(i)/l*1e3),r!==this._period||a!==this._loop||s!==this._roundTrip){i.stopAnimation();var d=void 0;d=f(u)?u(n):u,i.__t>0&&(d=-r*i.__t),this._animateSymbol(i,r,d,a,s)}this._period=r,this._loop=a,this._roundTrip=s}},n.prototype._animateSymbol=function(t,e,n,i,o){if(e>0){t.__t=0;var r=this,a=t.animate("",i).when(o?2*e:e,{__t:o?2:1}).delay(n).during((function(){r._updateSymbolPosition(t)}));i||a.done((function(){r.remove(t)})),a.start()}},n.prototype._getLineLength=function(t){return xi(t.__p1,t.__cp1)+xi(t.__cp1,t.__p2)},n.prototype._updateAnimationPoints=function(t,e){t.__p1=e[0],t.__p2=e[1],t.__cp1=e[2]||[(e[0][0]+e[1][0])/2,(e[0][1]+e[1][1])/2]},n.prototype.updateData=function(t,e,n){this.childAt(0).updateData(t,e,n),this._updateEffectSymbol(t,e)},n.prototype._updateSymbolPosition=function(t){var e=t.__p1,n=t.__p2,i=t.__cp1,o=t.__t<1?t.__t:2-t.__t,r=[t.x,t.y],a=r.slice(),s=zn,l=_i;r[0]=s(e[0],i[0],n[0],o),r[1]=s(e[1],i[1],n[1],o);var u=t.__t<1?l(e[0],i[0],n[0],o):l(n[0],i[0],e[0],1-o),d=t.__t<1?l(e[1],i[1],n[1],o):l(n[1],i[1],e[1],1-o);t.rotation=-Math.atan2(d,u)-Math.PI/2,"line"!==this._symbolType&&"rect"!==this._symbolType&&"roundRect"!==this._symbolType||(void 0!==t.__lastT&&t.__lastT<t.__t?(t.scaleY=1.05*xi(a,r),1===o&&(r[0]=a[0]+(r[0]-a[0])/2,r[1]=a[1]+(r[1]-a[1])/2)):1===t.__lastT?t.scaleY=2*xi(e,r):t.scaleY=this._symbolScale[1]),t.__lastT=t.__t,t.ignore=!1,t.x=r[0],t.y=r[1]},n.prototype.updateLayout=function(t,e){this.childAt(0).updateLayout(t,e);var n=t.getItemModel(e).getModel("effect");this._updateEffectAnimation(t,n,e)},n}(It),xg=function(t){function n(e,n,i){var o=t.call(this)||this;return o._createPolyline(e,n,i),o}return e(n,t),n.prototype._createPolyline=function(t,e,n){var i=t.getItemLayout(e),o=new Rt({shape:{points:i}});this.add(o),this._updateCommonStl(t,e,n)},n.prototype.updateData=function(t,e,n){var i=t.hostModel,o=this.childAt(0),r={shape:{points:t.getItemLayout(e)}};P(o,r,i,e),this._updateCommonStl(t,e,n)},n.prototype._updateCommonStl=function(t,e,n){var i=this.childAt(0),o=t.getItemModel(e),r=n&&n.emphasisLineStyle,a=n&&n.focus,s=n&&n.blurScope,l=n&&n.emphasisDisabled;if(!n||t.hasItemOption){var u=o.getModel("emphasis");r=u.getModel("lineStyle").getLineStyle(),l=u.get("disabled"),a=u.get("focus"),s=u.get("blurScope")}i.useStyle(t.getItemVisual(e,"style")),i.style.fill=null,i.style.strokeNoScale=!0,i.ensureState("emphasis").style=r,Gt(this,a,s,l)},n.prototype.updateLayout=function(t,e){this.childAt(0).setShape("points",t.getItemLayout(e))},n}(It),_g=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e._lastFrame=0,e._lastFramePercent=0,e}return e(n,t),n.prototype.createLine=function(t,e,n){return new xg(t,e,n)},n.prototype._updateAnimationPoints=function(t,e){this._points=e;for(var n=[0],i=0,o=1;o<e.length;o++){var r=e[o-1],a=e[o];i+=xi(r,a),n.push(i)}if(0!==i){for(o=0;o<n.length;o++)n[o]/=i;this._offsets=n,this._length=i}else this._length=0},n.prototype._getLineLength=function(){return this._length},n.prototype._updateSymbolPosition=function(t){var e=t.__t<1?t.__t:2-t.__t,n=this._points,i=this._offsets,o=n.length;if(i){var r,a=this._lastFrame;if(e<this._lastFramePercent){for(r=Math.min(a+1,o-1);r>=0&&!(i[r]<=e);r--);r=Math.min(r,o-2)}else{for(r=a;r<o&&!(i[r]>e);r++);r=Math.min(r-1,o-2)}var s=(e-i[r])/(i[r+1]-i[r]),l=n[r],u=n[r+1];t.x=l[0]*(1-s)+s*u[0],t.y=l[1]*(1-s)+s*u[1];var d=t.__t<1?u[0]-l[0]:l[0]-u[0],c=t.__t<1?u[1]-l[1]:l[1]-u[1];t.rotation=-Math.atan2(c,d)-Math.PI/2,this._lastFrame=r,this._lastFramePercent=e,t.ignore=!1}},n}(mg),bg=function(){return function(){this.polyline=!1,this.curveness=0,this.segs=[]}}(),Sg=function(t){function n(e){var n=t.call(this,e)||this;return n._off=0,n.hoverDataIdx=-1,n}return e(n,t),n.prototype.reset=function(){this.notClear=!1,this._off=0},n.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},n.prototype.getDefaultShape=function(){return new bg},n.prototype.buildPath=function(t,e){var n,i=e.segs,o=e.curveness;if(e.polyline)for(n=this._off;n<i.length;){var r=i[n++];if(r>0){t.moveTo(i[n++],i[n++]);for(var a=1;a<r;a++)t.lineTo(i[n++],i[n++])}}else for(n=this._off;n<i.length;){var s=i[n++],l=i[n++],u=i[n++],d=i[n++];if(t.moveTo(s,l),o>0){var c=(s+u)/2-(l-d)*o,h=(l+d)/2-(u-s)*o;t.quadraticCurveTo(c,h,u,d)}else t.lineTo(u,d)}this.incremental&&(this._off=n,this.notClear=!0)},n.prototype.findDataIndex=function(t,e){var n=this.shape,i=n.segs,o=n.curveness,r=this.style.lineWidth;if(n.polyline)for(var a=0,s=0;s<i.length;){var l=i[s++];if(l>0)for(var u=i[s++],d=i[s++],c=1;c<l;c++){var h=i[s++],p=i[s++];if(bi(u,d,h,p,r,t,e))return a}a++}else for(a=0,s=0;s<i.length;){u=i[s++],d=i[s++],h=i[s++],p=i[s++];if(o>0){if(Si(u,d,(u+h)/2-(d-p)*o,(d+p)/2-(h-u)*o,h,p,r,t,e))return a}else if(bi(u,d,h,p,r,t,e))return a;a++}return-1},n.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect();return t=n[0],e=n[1],i.contain(t,e)?(this.hoverDataIdx=this.findDataIndex(t,e))>=0:(this.hoverDataIdx=-1,!1)},n.prototype.getBoundingRect=function(){var t=this._rect;if(!t){for(var e=this.shape.segs,n=1/0,i=1/0,o=-1/0,r=-1/0,a=0;a<e.length;){var l=e[a++],u=e[a++];n=Math.min(l,n),o=Math.max(l,o),i=Math.min(u,i),r=Math.max(u,r)}t=this._rect=new s(n,i,o,r)}return t},n}(q),wg=function(){function t(){this.group=new It}return t.prototype.updateData=function(t){this._clear();var e=this._create();e.setShape({segs:t.getLayout("linesPoints")}),this._setCommon(e,t)},t.prototype.incrementalPrepareUpdate=function(t){this.group.removeAll(),this._clear()},t.prototype.incrementalUpdate=function(t,e){var n=this._newAdded[0],i=e.getLayout("linesPoints"),o=n&&n.shape.segs;if(o&&o.length<2e4){var r=o.length,a=new Float32Array(r+i.length);a.set(o),a.set(i,r),n.setShape({segs:a})}else{this._newAdded=[];var s=this._create();s.incremental=!0,s.setShape({segs:i}),this._setCommon(s,e),s.__startIndex=t.start}},t.prototype.remove=function(){this._clear()},t.prototype.eachRendered=function(t){this._newAdded[0]&&t(this._newAdded[0])},t.prototype._create=function(){var t=new Sg({cursor:"default",ignoreCoarsePointer:!0});return this._newAdded.push(t),this.group.add(t),t},t.prototype._setCommon=function(t,e,n){var i=e.hostModel;t.setShape({polyline:i.get("polyline"),curveness:i.get(["lineStyle","curveness"])}),t.useStyle(i.getModel("lineStyle").getLineStyle()),t.style.strokeNoScale=!0;var o=e.getVisual("style");o&&o.stroke&&t.setStyle("stroke",o.stroke),t.setStyle("fill",null);var r=v(t);r.seriesIndex=i.seriesIndex,t.on("mousemove",(function(e){r.dataIndex=null;var n=t.hoverDataIdx;n>0&&(r.dataIndex=n+t.__startIndex)}))},t.prototype._clear=function(){this._newAdded=[],this.group.removeAll()},t}(),Mg={seriesType:"lines",plan:gi(),reset:function(t){var e=t.coordinateSystem;if(e){var n=t.get("polyline"),i=t.pipelineContext.large;return{progress:function(o,r){var a=[];if(i){var s=void 0,l=o.end-o.start;if(n){for(var u=0,d=o.start;d<o.end;d++)u+=t.getLineCoordsCount(d);s=new Float32Array(l+2*u)}else s=new Float32Array(4*l);var c=0,h=[];for(d=o.start;d<o.end;d++){var p=t.getLineCoords(d,a);n&&(s[c++]=p);for(var g=0;g<p;g++)h=e.dataToPoint(a[g],!1,h),s[c++]=h[0],s[c++]=h[1]}r.setLayout("linesPoints",s)}else for(d=o.start;d<o.end;d++){var f=r.getItemModel(d),y=(p=t.getLineCoords(d,a),[]);if(n)for(var v=0;v<p;v++)y.push(e.dataToPoint(a[v]));else{y[0]=e.dataToPoint(a[0]),y[1]=e.dataToPoint(a[1]);var m=f.get(["lineStyle","curveness"]);+m&&(y[2]=[(y[0][0]+y[1][0])/2-(y[0][1]-y[1][1])*m,(y[0][1]+y[1][1])/2-(y[1][0]-y[0][0])*m])}r.setItemLayout(d,y)}}}}}},Ig=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.render=function(t,e,n){var i=t.getData(),o=this._updateLineDraw(i,t),r=t.get("zlevel"),a=t.get(["effect","trailLength"]),s=n.getZr(),l="svg"===s.painter.getType();l||s.painter.getLayer(r).clear(!0),null==this._lastZlevel||l||s.configLayer(this._lastZlevel,{motionBlur:!1}),this._showEffect(t)&&a>0&&(l||s.configLayer(r,{motionBlur:!0,lastFrameAlpha:Math.max(Math.min(a/10+.9,1),0)})),o.updateData(i);var u=t.get("clip",!0)&&fi(t.coordinateSystem,!1,t);u?this.group.setClipPath(u):this.group.removeClipPath(),this._lastZlevel=r,this._finished=!0},n.prototype.incrementalPrepareRender=function(t,e,n){var i=t.getData();this._updateLineDraw(i,t).incrementalPrepareUpdate(i),this._clearLayer(n),this._finished=!1},n.prototype.incrementalRender=function(t,e,n){this._lineDraw.incrementalUpdate(t,e.getData()),this._finished=t.end===e.getData().count()},n.prototype.eachRendered=function(t){this._lineDraw&&this._lineDraw.eachRendered(t)},n.prototype.updateTransform=function(t,e,n){var i=t.getData(),o=t.pipelineContext;if(!this._finished||o.large||o.progressiveRender)return{update:!0};var r=Mg.reset(t,e,n);r.progress&&r.progress({start:0,end:i.count(),count:i.count()},i),this._lineDraw.updateLayout(),this._clearLayer(n)},n.prototype._updateLineDraw=function(t,e){var n=this._lineDraw,i=this._showEffect(e),o=!!e.get("polyline"),r=e.pipelineContext.large;return n&&i===this._hasEffet&&o===this._isPolyline&&r===this._isLargeDraw||(n&&n.remove(),n=this._lineDraw=r?new wg:new cc(o?i?_g:xg:i?mg:dc),this._hasEffet=i,this._isPolyline=o,this._isLargeDraw=r),this.group.add(n.group),n},n.prototype._showEffect=function(t){return!!t.get(["effect","show"])},n.prototype._clearLayer=function(t){var e=t.getZr();"svg"===e.painter.getType()||null==this._lastZlevel||e.painter.getLayer(this._lastZlevel).clear(!0)},n.prototype.remove=function(t,e){this._lineDraw&&this._lineDraw.remove(),this._lineDraw=null,this._clearLayer(e)},n.prototype.dispose=function(t,e){this.remove(t,e)},n.type="lines",n}(Dt),Ag="undefined"==typeof Uint32Array?Array:Uint32Array,Tg="undefined"==typeof Float64Array?Array:Float64Array;function Cg(t){var e=t.data;e&&e[0]&&e[0][0]&&e[0][0].coord&&(t.data=d(e,(function(t){var e={coords:[t[0].coord,t[1].coord]};return t[0].name&&(e.fromName=t[0].name),t[1].name&&(e.toName=t[1].name),Be([e,t[0],t[1]])})))}var Dg=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.visualStyleAccessPath="lineStyle",e.visualDrawType="stroke",e}return e(n,t),n.prototype.init=function(e){e.data=e.data||[],Cg(e);var n=this._processFlatCoordsArray(e.data);this._flatCoords=n.flatCoords,this._flatCoordsOffset=n.flatCoordsOffset,n.flatCoords&&(e.data=new Float32Array(n.count)),t.prototype.init.apply(this,arguments)},n.prototype.mergeOption=function(e){if(Cg(e),e.data){var n=this._processFlatCoordsArray(e.data);this._flatCoords=n.flatCoords,this._flatCoordsOffset=n.flatCoordsOffset,n.flatCoords&&(e.data=new Float32Array(n.count))}t.prototype.mergeOption.apply(this,arguments)},n.prototype.appendData=function(t){var e=this._processFlatCoordsArray(t.data);e.flatCoords&&(this._flatCoords?(this._flatCoords=je(this._flatCoords,e.flatCoords),this._flatCoordsOffset=je(this._flatCoordsOffset,e.flatCoordsOffset)):(this._flatCoords=e.flatCoords,this._flatCoordsOffset=e.flatCoordsOffset),t.data=new Float32Array(e.count)),this.getRawData().appendData(t.data)},n.prototype._getCoordsFromItemModel=function(t){var e=this.getData().getItemModel(t);return e.option instanceof Array?e.option:e.getShallow("coords")},n.prototype.getLineCoordsCount=function(t){return this._flatCoordsOffset?this._flatCoordsOffset[2*t+1]:this._getCoordsFromItemModel(t).length},n.prototype.getLineCoords=function(t,e){if(this._flatCoordsOffset){for(var n=this._flatCoordsOffset[2*t],i=this._flatCoordsOffset[2*t+1],o=0;o<i;o++)e[o]=e[o]||[],e[o][0]=this._flatCoords[n+2*o],e[o][1]=this._flatCoords[n+2*o+1];return i}var r=this._getCoordsFromItemModel(t);for(o=0;o<r.length;o++)e[o]=e[o]||[],e[o][0]=r[o][0],e[o][1]=r[o][1];return r.length},n.prototype._processFlatCoordsArray=function(t){var e=0;if(this._flatCoords&&(e=this._flatCoords.length),Y(t[0])){for(var n=t.length,i=new Ag(n),o=new Tg(n),r=0,a=0,s=0,l=0;l<n;){s++;var u=t[l++];i[a++]=r+e,i[a++]=u;for(var d=0;d<u;d++){var c=t[l++],h=t[l++];o[r++]=c,o[r++]=h}}return{flatCoordsOffset:new Uint32Array(i.buffer,0,a),flatCoords:o,count:s}}return{flatCoordsOffset:null,flatCoords:null,count:t.length}},n.prototype.getInitialData=function(t,e){var n=new tn(["value"],this);return n.hasItemOption=!1,n.initData(t.data,[],(function(t,e,i,o){if(t instanceof Array)return NaN;n.hasItemOption=!0;var r=t.value;return null!=r?r instanceof Array?r[o]:r:void 0})),n},n.prototype.formatTooltip=function(t,e,n){var i=this.getData().getItemModel(t),o=i.get("name");if(o)return o;var r=i.get("fromName"),a=i.get("toName"),s=[];return null!=r&&s.push(r),null!=a&&s.push(a),Ut("nameValue",{name:s.join(" > ")})},n.prototype.preventIncremental=function(){return!!this.get(["effect","show"])},n.prototype.getProgressive=function(){var t=this.option.progressive;return null==t?this.option.large?1e4:this.get("progressive"):t},n.prototype.getProgressiveThreshold=function(){var t=this.option.progressiveThreshold;return null==t?this.option.large?2e4:this.get("progressiveThreshold"):t},n.prototype.getZLevelKey=function(){var t=this.getModel("effect"),e=t.get("trailLength");return this.getData().count()>this.getProgressiveThreshold()?this.id:t.get("show")&&e>0?e+"":""},n.type="series.lines",n.dependencies=["grid","polar","geo","calendar"],n.defaultOption={coordinateSystem:"geo",z:2,legendHoverLink:!0,xAxisIndex:0,yAxisIndex:0,symbol:["none","none"],symbolSize:[10,10],geoIndex:0,effect:{show:!1,period:4,constantSpeed:0,symbol:"circle",symbolSize:3,loop:!0,trailLength:.2},large:!1,largeThreshold:2e3,polyline:!1,clip:!0,label:{show:!1,position:"end"},lineStyle:{opacity:.5}},n}(Mt);function Lg(t){return t instanceof Array||(t=[t,t]),t}var Pg={seriesType:"lines",reset:function(t){var e=Lg(t.get("symbol")),n=Lg(t.get("symbolSize")),i=t.getData();return i.setVisual("fromSymbol",e&&e[0]),i.setVisual("toSymbol",e&&e[1]),i.setVisual("fromSymbolSize",n&&n[0]),i.setVisual("toSymbolSize",n&&n[1]),{dataEach:i.hasItemOption?function(t,e){var n=t.getItemModel(e),i=Lg(n.getShallow("symbol",!0)),o=Lg(n.getShallow("symbolSize",!0));i[0]&&t.setItemVisual(e,"fromSymbol",i[0]),i[1]&&t.setItemVisual(e,"toSymbol",i[1]),o[0]&&t.setItemVisual(e,"fromSymbolSize",o[0]),o[1]&&t.setItemVisual(e,"toSymbolSize",o[1])}:null}}};var kg=function(){function t(){this.blurSize=30,this.pointSize=20,this.maxOpacity=1,this.minOpacity=0,this._gradientPixels={inRange:null,outOfRange:null};var t=wi.createCanvas();this.canvas=t}return t.prototype.update=function(t,e,n,i,o,r){var a=this._getBrush(),s=this._getGradient(o,"inRange"),l=this._getGradient(o,"outOfRange"),u=this.pointSize+this.blurSize,d=this.canvas,c=d.getContext("2d"),h=t.length;d.width=e,d.height=n;for(var p=0;p<h;++p){var g=t[p],f=g[0],y=g[1],v=i(g[2]);c.globalAlpha=v,c.drawImage(a,f-u,y-u)}if(!d.width||!d.height)return d;for(var m=c.getImageData(0,0,d.width,d.height),x=m.data,_=0,b=x.length,S=this.minOpacity,w=this.maxOpacity-S;_<b;){v=x[_+3]/256;var M=4*Math.floor(255*v);if(v>0){var I=r(v)?s:l;v>0&&(v=v*w+S),x[_++]=I[M],x[_++]=I[M+1],x[_++]=I[M+2],x[_++]=I[M+3]*v*256}else _+=4}return c.putImageData(m,0,0),d},t.prototype._getBrush=function(){var t=this._brushCanvas||(this._brushCanvas=wi.createCanvas()),e=this.pointSize+this.blurSize,n=2*e;t.width=n,t.height=n;var i=t.getContext("2d");return i.clearRect(0,0,n,n),i.shadowOffsetX=n,i.shadowBlur=this.blurSize,i.shadowColor="#000",i.beginPath(),i.arc(-e,e,this.pointSize,0,2*Math.PI,!0),i.closePath(),i.fill(),t},t.prototype._getGradient=function(t,e){for(var n=this._gradientPixels,i=n[e]||(n[e]=new Uint8ClampedArray(1024)),o=[0,0,0,0],r=0,a=0;a<256;a++)t[e](a/255,!0,o),i[r++]=o[0],i[r++]=o[1],i[r++]=o[2],i[r++]=o[3];return i},t}();function Ng(t){var e=t.dimensions;return"lng"===e[0]&&"lat"===e[1]}var Rg=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.render=function(t,e,n){var i;e.eachComponent("visualMap",(function(e){e.eachTargetSeries((function(n){n===t&&(i=e)}))})),this._progressiveEls=null,this.group.removeAll();var o=t.coordinateSystem;"cartesian2d"===o.type||"calendar"===o.type?this._renderOnCartesianAndCalendar(t,n,0,t.getData().count()):Ng(o)&&this._renderOnGeo(o,t,i,n)},n.prototype.incrementalPrepareRender=function(t,e,n){this.group.removeAll()},n.prototype.incrementalRender=function(t,e,n,i){var o=e.coordinateSystem;o&&(Ng(o)?this.render(e,n,i):(this._progressiveEls=[],this._renderOnCartesianAndCalendar(e,i,t.start,t.end,!0)))},n.prototype.eachRendered=function(t){On(this._progressiveEls||this.group,t)},n.prototype._renderOnCartesianAndCalendar=function(t,e,n,i,o){var r,a,s,l,u=t.coordinateSystem,d=Mi(u,"cartesian2d");if(d){var c=u.getAxis("x"),h=u.getAxis("y");r=c.getBandWidth()+.5,a=h.getBandWidth()+.5,s=c.scale.getExtent(),l=h.scale.getExtent()}for(var p=this.group,g=t.getData(),f=t.getModel(["emphasis","itemStyle"]).getItemStyle(),y=t.getModel(["blur","itemStyle"]).getItemStyle(),v=t.getModel(["select","itemStyle"]).getItemStyle(),m=t.get(["itemStyle","borderRadius"]),x=Bt(t),_=t.getModel("emphasis"),b=_.get("focus"),S=_.get("blurScope"),w=_.get("disabled"),M=d?[g.mapDimension("x"),g.mapDimension("y"),g.mapDimension("value")]:[g.mapDimension("time"),g.mapDimension("value")],I=n;I<i;I++){var A=void 0,T=g.getItemVisual(I,"style");if(d){var C=g.get(M[0],I),D=g.get(M[1],I);if(isNaN(g.get(M[2],I))||isNaN(C)||isNaN(D)||C<s[0]||C>s[1]||D<l[0]||D>l[1])continue;var L=u.dataToPoint([C,D]);A=new pe({shape:{x:L[0]-r/2,y:L[1]-a/2,width:r,height:a},style:T})}else{if(isNaN(g.get(M[1],I)))continue;A=new pe({z2:1,shape:u.dataToRect([g.get(M[0],I)]).contentShape,style:T})}if(g.hasItemOption){var P=g.getItemModel(I),k=P.getModel("emphasis");f=k.getModel("itemStyle").getItemStyle(),y=P.getModel(["blur","itemStyle"]).getItemStyle(),v=P.getModel(["select","itemStyle"]).getItemStyle(),m=P.get(["itemStyle","borderRadius"]),b=k.get("focus"),S=k.get("blurScope"),w=k.get("disabled"),x=Bt(P)}A.shape.r=m;var N=t.getRawValue(I),R="-";N&&null!=N[2]&&(R=N[2]+""),zt(A,x,{labelFetcher:t,labelDataIndex:I,defaultOpacity:T.opacity,defaultText:R}),A.ensureState("emphasis").style=f,A.ensureState("blur").style=y,A.ensureState("select").style=v,Gt(A,b,S,w),A.incremental=o,o&&(A.states.emphasis.hoverLayer=!0),p.add(A),g.setItemGraphicEl(I,A),this._progressiveEls&&this._progressiveEls.push(A)}},n.prototype._renderOnGeo=function(t,e,n,i){var o=n.targetVisuals.inRange,r=n.targetVisuals.outOfRange,a=e.getData(),s=this._hmLayer||this._hmLayer||new kg;s.blurSize=e.get("blurSize"),s.pointSize=e.get("pointSize"),s.minOpacity=e.get("minOpacity"),s.maxOpacity=e.get("maxOpacity");var l=t.getViewRect().clone(),u=t.getRoamTransform();l.applyTransform(u);var c=Math.max(l.x,0),h=Math.max(l.y,0),p=Math.min(l.width+l.x,i.getWidth()),g=Math.min(l.height+l.y,i.getHeight()),f=p-c,y=g-h,v=[a.mapDimension("lng"),a.mapDimension("lat"),a.mapDimension("value")],m=a.mapArray(v,(function(e,n,i){var o=t.dataToPoint([e,n]);return o[0]-=c,o[1]-=h,o.push(i),o})),x=n.getExtent(),_="visualMap.continuous"===n.type?function(t,e){var n=t[1]-t[0];return e=[(e[0]-t[0])/n,(e[1]-t[0])/n],function(t){return t>=e[0]&&t<=e[1]}}(x,n.option.range):function(t,e,n){var i=t[1]-t[0],o=(e=d(e,(function(e){return{interval:[(e.interval[0]-t[0])/i,(e.interval[1]-t[0])/i]}}))).length,r=0;return function(t){var i;for(i=r;i<o;i++)if((a=e[i].interval)[0]<=t&&t<=a[1]){r=i;break}if(i===o)for(i=r-1;i>=0;i--){var a;if((a=e[i].interval)[0]<=t&&t<=a[1]){r=i;break}}return i>=0&&i<o&&n[i]}}(x,n.getPieceList(),n.option.selected);s.update(m,f,y,o.color.getNormalizer(),{inRange:o.color.getColorMapper(),outOfRange:r.color.getColorMapper()},_);var b=new R({style:{width:f,height:y,x:c,y:h,image:s.canvas},silent:!0});this.group.add(b)},n.type="heatmap",n}(Dt),Vg=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.getInitialData=function(t,e){return wt(null,this,{generateCoord:"value"})},n.prototype.preventIncremental=function(){var t=Gn.get(this.get("coordinateSystem"));if(t&&t.dimensions)return"lng"===t.dimensions[0]&&"lat"===t.dimensions[1]},n.type="series.heatmap",n.dependencies=["grid","geo","calendar"],n.defaultOption={coordinateSystem:"cartesian2d",z:2,geoIndex:0,blurSize:30,pointSize:20,maxOpacity:1,minOpacity:0,select:{itemStyle:{borderColor:"#212121"}}},n}(Mt);var Og=["itemStyle","borderWidth"],Eg=[{xy:"x",wh:"width",index:0,posDesc:["left","right"]},{xy:"y",wh:"height",index:1,posDesc:["top","bottom"]}],zg=new ee,Bg=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.render=function(t,e,n){var i=this.group,o=t.getData(),r=this._data,a=t.coordinateSystem,s=a.getBaseAxis().isHorizontal(),l=a.master.getRect(),u={ecSize:{width:n.getWidth(),height:n.getHeight()},seriesModel:t,coordSys:a,coordSysExtent:[[l.x,l.x+l.width],[l.y,l.y+l.height]],isHorizontal:s,valueDim:Eg[+s],categoryDim:Eg[1-+s]};o.diff(r).add((function(t){if(o.hasValue(t)){var e=Ug(o,t),n=Gg(o,t,e,u),r=Kg(o,u,n);o.setItemGraphicEl(t,r),i.add(r),ef(r,u,n)}})).update((function(t,e){var n=r.getItemGraphicEl(e);if(o.hasValue(t)){var a=Ug(o,t),s=Gg(o,t,a,u),l=$g(o,s);n&&l!==n.__pictorialShapeStr&&(i.remove(n),o.setItemGraphicEl(t,null),n=null),n?function(t,e,n){var i=n.animationModel,o=n.dataIndex,r=t.__pictorialBundle;P(r,{x:n.bundlePosition[0],y:n.bundlePosition[1]},i,o),n.symbolRepeat?Hg(t,e,n,!0):Zg(t,e,n,!0);Yg(t,n,!0),Xg(t,e,n,!0)}(n,u,s):n=Kg(o,u,s,!0),o.setItemGraphicEl(t,n),n.__pictorialSymbolMeta=s,i.add(n),ef(n,u,s)}else i.remove(n)})).remove((function(t){var e=r.getItemGraphicEl(t);e&&Jg(r,t,e.__pictorialSymbolMeta.animationModel,e)})).execute();var d=t.get("clip",!0)?fi(t.coordinateSystem,!1,t):null;return d?i.setClipPath(d):i.removeClipPath(),this._data=o,this.group},n.prototype.remove=function(t,e){var n=this.group,i=this._data;t.get("animation")?i&&i.eachItemGraphicEl((function(e){Jg(i,v(e).dataIndex,t,e)})):n.removeAll()},n.type="pictorialBar",n}(Dt);function Gg(t,e,n,i){var o=t.getItemLayout(e),r=n.get("symbolRepeat"),a=n.get("symbolClip"),s=n.get("symbolPosition")||"start",l=(n.get("symbolRotate")||0)*Math.PI/180||0,u=n.get("symbolPatternSize")||2,d=n.isAnimationEnabled(),c={dataIndex:e,layout:o,itemModel:n,symbolType:t.getItemVisual(e,"symbol")||"circle",style:t.getItemVisual(e,"style"),symbolClip:a,symbolRepeat:r,symbolRepeatDirection:n.get("symbolRepeatDirection"),symbolPatternSize:u,rotation:l,animationModel:d?n:null,hoverScale:d&&n.get(["emphasis","scale"]),z2:n.getShallow("z",!0)||0};!function(t,e,n,i,o){var r,a=i.valueDim,s=t.get("symbolBoundingData"),l=i.coordSys.getOtherAxis(i.coordSys.getBaseAxis()),u=l.toGlobalCoord(l.dataToCoord(0)),d=1-+(n[a.wh]<=0);if(mt(s)){var c=[Fg(l,s[0])-u,Fg(l,s[1])-u];c[1]<c[0]&&c.reverse(),r=c[d]}else r=null!=s?Fg(l,s)-u:e?i.coordSysExtent[a.index][d]-u:n[a.wh];o.boundingLength=r,e&&(o.repeatCutLength=n[a.wh]);var h="x"===a.xy,p=l.inverse;o.pxSign=h&&!p||!h&&p?r>=0?1:-1:r>0?1:-1}(n,r,o,i,c),function(t,e,n,i,o,r,a,s,l,u){var d,c=l.valueDim,h=l.categoryDim,p=Math.abs(n[h.wh]),g=t.getItemVisual(e,"symbolSize");d=mt(g)?g.slice():null==g?["100%","100%"]:[g,g];d[h.index]=m(d[h.index],p),d[c.index]=m(d[c.index],i?p:Math.abs(r)),u.symbolSize=d;var f=u.symbolScale=[d[0]/s,d[1]/s];f[c.index]*=(l.isHorizontal?-1:1)*a}(t,e,o,r,0,c.boundingLength,c.pxSign,u,i,c),function(t,e,n,i,o){var r=t.get(Og)||0;r&&(zg.attr({scaleX:e[0],scaleY:e[1],rotation:n}),zg.updateTransform(),r/=zg.getLineScale(),r*=e[i.valueDim.index]);o.valueLineWidth=r||0}(n,c.symbolScale,l,i,c);var h=c.symbolSize,p=Vn(n.get("symbolOffset"),h);return function(t,e,n,i,o,r,a,s,l,u,d,c){var h=d.categoryDim,p=d.valueDim,g=c.pxSign,f=Math.max(e[p.index]+s,0),y=f;if(i){var v=Math.abs(l),x=Ge(t.get("symbolMargin"),"15%")+"",_=!1;x.lastIndexOf("!")===x.length-1&&(_=!0,x=x.slice(0,x.length-1));var b=m(x,e[p.index]),S=Math.max(f+2*b,0),w=_?0:2*b,M=Ai(i),I=M?i:nf((v+w)/S);S=f+2*(b=(v-I*f)/2/(_?I:Math.max(I-1,1))),w=_?0:2*b,M||"fixed"===i||(I=u?nf((Math.abs(u)+w)/S):0),y=I*S-w,c.repeatTimes=I,c.symbolMargin=b}var A=g*(y/2),T=c.pathPosition=[];T[h.index]=n[h.wh]/2,T[p.index]="start"===a?A:"end"===a?l-A:l/2,r&&(T[0]+=r[0],T[1]+=r[1]);var C=c.bundlePosition=[];C[h.index]=n[h.xy],C[p.index]=n[p.xy];var D=c.barRectShape=H({},n);D[p.wh]=g*Math.max(Math.abs(n[p.wh]),Math.abs(T[p.index]+A)),D[h.wh]=n[h.wh];var L=c.clipShape={};L[h.xy]=-n[h.xy],L[h.wh]=d.ecSize[h.wh],L[p.xy]=0,L[p.wh]=n[p.wh]}(n,h,o,r,0,p,s,c.valueLineWidth,c.boundingLength,c.repeatCutLength,i,c),c}function Fg(t,e){return t.toGlobalCoord(t.dataToCoord(t.scale.parse(e)))}function Wg(t){var e=t.symbolPatternSize,n=At(t.symbolType,-e/2,-e/2,e,e);return n.attr({culling:!0}),"image"!==n.type&&n.setStyle({strokeNoScale:!0}),n}function Hg(t,e,n,i){var o=t.__pictorialBundle,r=n.symbolSize,a=n.valueLineWidth,s=n.pathPosition,l=e.valueDim,u=n.repeatTimes||0,d=0,c=r[e.valueDim.index]+a+2*n.symbolMargin;for(Qg(t,(function(t){t.__pictorialAnimationIndex=d,t.__pictorialRepeatTimes=u,d<u?tf(t,null,g(d),n,i):tf(t,null,{scaleX:0,scaleY:0},n,i,(function(){o.remove(t)})),d++}));d<u;d++){var h=Wg(n);h.__pictorialAnimationIndex=d,h.__pictorialRepeatTimes=u,o.add(h);var p=g(d);tf(h,{x:p.x,y:p.y,scaleX:0,scaleY:0},{scaleX:p.scaleX,scaleY:p.scaleY,rotation:p.rotation},n,i)}function g(t){var e=s.slice(),i=n.pxSign,o=t;return("start"===n.symbolRepeatDirection?i>0:i<0)&&(o=u-1-t),e[l.index]=c*(o-u/2+.5)+s[l.index],{x:e[0],y:e[1],scaleX:n.symbolScale[0],scaleY:n.symbolScale[1],rotation:n.rotation}}}function Zg(t,e,n,i){var o=t.__pictorialBundle,r=t.__pictorialMainPath;r?tf(r,null,{x:n.pathPosition[0],y:n.pathPosition[1],scaleX:n.symbolScale[0],scaleY:n.symbolScale[1],rotation:n.rotation},n,i):(r=t.__pictorialMainPath=Wg(n),o.add(r),tf(r,{x:n.pathPosition[0],y:n.pathPosition[1],scaleX:0,scaleY:0,rotation:n.rotation},{scaleX:n.symbolScale[0],scaleY:n.symbolScale[1]},n,i))}function Yg(t,e,n){var i=H({},e.barRectShape),o=t.__pictorialBarRect;o?tf(o,null,{shape:i},e,n):((o=t.__pictorialBarRect=new pe({z2:2,shape:i,silent:!0,style:{stroke:"transparent",fill:"transparent",lineWidth:0}})).disableMorphing=!0,t.add(o))}function Xg(t,e,n,i){if(n.symbolClip){var o=t.__pictorialClipPath,r=H({},n.clipShape),a=e.valueDim,s=n.animationModel,l=n.dataIndex;if(o)P(o,{shape:r},s,l);else{r[a.wh]=0,o=new pe({shape:r}),t.__pictorialBundle.setClipPath(o),t.__pictorialClipPath=o;var u={};u[a.wh]=n.clipShape[a.wh],Ft[i?"updateProps":"initProps"](o,{shape:u},s,l)}}}function Ug(t,e){var n=t.getItemModel(e);return n.getAnimationDelayParams=jg,n.isAnimationEnabled=qg,n}function jg(t){return{index:t.__pictorialAnimationIndex,count:t.__pictorialRepeatTimes}}function qg(){return this.parentModel.isAnimationEnabled()&&!!this.getShallow("animation")}function Kg(t,e,n,i){var o=new It,r=new It;return o.add(r),o.__pictorialBundle=r,r.x=n.bundlePosition[0],r.y=n.bundlePosition[1],n.symbolRepeat?Hg(o,e,n):Zg(o,0,n),Yg(o,n,i),Xg(o,e,n,i),o.__pictorialShapeStr=$g(t,n),o.__pictorialSymbolMeta=n,o}function Jg(t,e,n,i){var o=i.__pictorialBarRect;o&&o.removeTextContent();var r=[];Qg(i,(function(t){r.push(t)})),i.__pictorialMainPath&&r.push(i.__pictorialMainPath),i.__pictorialClipPath&&(n=null),a(r,(function(t){Ke(t,{scaleX:0,scaleY:0},n,e,(function(){i.parent&&i.parent.remove(i)}))})),t.setItemGraphicEl(e,null)}function $g(t,e){return[t.getItemVisual(e.dataIndex,"symbol")||"none",!!e.symbolRepeat,!!e.symbolClip].join(":")}function Qg(t,e,n){a(t.__pictorialBundle.children(),(function(i){i!==t.__pictorialBarRect&&e.call(n,i)}))}function tf(t,e,n,i,o,r){e&&t.attr(e),i.symbolClip&&!o?n&&t.attr(n):n&&Ft[o?"updateProps":"initProps"](t,n,i.animationModel,i.dataIndex,r)}function ef(t,e,n){var i=n.dataIndex,o=n.itemModel,r=o.getModel("emphasis"),a=r.getModel("itemStyle").getItemStyle(),s=o.getModel(["blur","itemStyle"]).getItemStyle(),l=o.getModel(["select","itemStyle"]).getItemStyle(),u=o.getShallow("cursor"),d=r.get("focus"),c=r.get("blurScope"),h=r.get("scale");Qg(t,(function(t){if(t instanceof R){var e=t.style;t.useStyle(H({image:e.image,x:e.x,y:e.y,width:e.width,height:e.height},n.style))}else t.useStyle(n.style);var i=t.ensureState("emphasis");i.style=a,h&&(i.scaleX=1.1*t.scaleX,i.scaleY=1.1*t.scaleY),t.ensureState("blur").style=s,t.ensureState("select").style=l,u&&(t.cursor=u),t.z2=n.z2}));var p=e.valueDim.posDesc[+(n.boundingLength>0)],g=t.__pictorialBarRect;g.ignoreClip=!0,zt(g,Bt(o),{labelFetcher:e.seriesModel,labelDataIndex:i,defaultText:Ii(e.seriesModel.getData(),i),inheritColor:n.style.fill,defaultOpacity:n.style.opacity,defaultOutsidePosition:p}),Gt(t,d,c,r.get("disabled"))}function nf(t){var e=Math.round(t);return Math.abs(t-e)<1e-4?e:Math.ceil(t)}var of=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.hasSymbolVisual=!0,e.defaultSymbol="roundRect",e}return e(n,t),n.prototype.getInitialData=function(e){return e.stack=null,t.prototype.getInitialData.apply(this,arguments)},n.type="series.pictorialBar",n.dependencies=["grid"],n.defaultOption=Ti(Ci.defaultOption,{symbol:"circle",symbolSize:null,symbolRotate:null,symbolPosition:null,symbolOffset:null,symbolMargin:null,symbolRepeat:!1,symbolRepeatDirection:"end",symbolClip:!1,symbolBoundingData:null,symbolPatternSize:400,barGap:"-100%",clip:!1,progressive:0,emphasis:{scale:!1},select:{itemStyle:{borderColor:"#212121"}}}),n}(Ci);var rf=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e._layers=[],e}return e(n,t),n.prototype.render=function(t,e,n){var i=t.getData(),o=this,r=this.group,a=t.getLayerSeries(),s=i.getLayout("layoutInfo"),l=s.rect,u=s.boundaryGap;function d(t){return t.name}r.x=0,r.y=l.y+u[0];var c=new ln(this._layersSeries||[],a,d,d),h=[];function p(e,n,s){var l=o._layers;if("remove"!==e){for(var u,d,c=[],p=[],g=a[n].indices,f=0;f<g.length;f++){var y=i.getItemLayout(g[f]),v=y.x,m=y.y0,x=y.y;c.push(v,m),p.push(v,m+x),u=i.getItemVisual(g[f],"style")}var _=i.getItemLayout(g[0]),b=t.getModel("label").get("margin"),S=t.getModel("emphasis");if("add"===e){var w=h[n]=new It;d=new Pi({shape:{points:c,stackedOnPoints:p,smooth:.4,stackedOnSmooth:.4,smoothConstraint:!1},z2:0}),w.add(d),r.add(w),t.isAnimationEnabled()&&d.setClipPath(function(t,e,n){var i=new pe({shape:{x:t.x-10,y:t.y-10,width:0,height:t.height+20}});return D(i,{shape:{x:t.x-50,width:t.width+100,height:t.height+20}},e,n),i}(d.getBoundingRect(),t,(function(){d.removeClipPath()})))}else{w=l[s];d=w.childAt(0),r.add(w),h[n]=w,P(d,{shape:{points:c,stackedOnPoints:p}},t),Vt(d)}zt(d,Bt(t),{labelDataIndex:g[f-1],defaultText:i.getName(g[f-1]),inheritColor:u.fill},{normal:{verticalAlign:"middle"}}),d.setTextConfig({position:null,local:!0});var M=d.getTextContent();M&&(M.x=_.x-b,M.y=_.y0+_.y/2),d.useStyle(u),i.setItemGraphicEl(n,d),Et(d,t),Gt(d,S.get("focus"),S.get("blurScope"),S.get("disabled"))}else r.remove(l[n])}c.add(Zt(p,this,"add")).update(Zt(p,this,"update")).remove(Zt(p,this,"remove")).execute(),this._layersSeries=a,this._layers=h},n.type="themeRiver",n}(Dt);var af=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.init=function(e){t.prototype.init.apply(this,arguments),this.legendVisualProvider=new Ht(Zt(this.getData,this),Zt(this.getRawData,this))},n.prototype.fixData=function(t){var e=t.length,n={},i=li(t,(function(t){return n.hasOwnProperty(t[0]+"")||(n[t[0]+""]=-1),t[2]})),o=[];i.buckets.each((function(t,e){o.push({name:e,dataList:t})}));for(var r=o.length,a=0;a<r;++a){for(var s=o[a].name,l=0;l<o[a].dataList.length;++l){var u=o[a].dataList[l][0]+"";n[u]=a}for(var u in n)n.hasOwnProperty(u)&&n[u]!==a&&(n[u]=a,t[e]=[u,0,s],e++)}return t},n.prototype.getInitialData=function(t,e){for(var n=this.getReferringComponents("singleAxis",ze).models[0].get("type"),i=c(t.data,(function(t){return void 0!==t[2]})),o=this.fixData(i||[]),r=[],a=this.nameMap=Ie(),s=0,l=0;l<o.length;++l)r.push(o[l][2]),a.get(o[l][2])||(a.set(o[l][2],s),s++);var u=Qe(o,{coordDimensions:["single"],dimensionsDefine:[{name:"time",type:ui(n)},{name:"value",type:"float"},{name:"name",type:"ordinal"}],encodeDefine:{single:0,value:1,itemName:2}}).dimensions,d=new tn(u,this);return d.initData(o),d},n.prototype.getLayerSeries=function(){for(var t=this.getData(),e=t.count(),n=[],i=0;i<e;++i)n[i]=i;var o=t.mapDimension("single"),r=li(n,(function(e){return t.get("name",e)})),a=[];return r.buckets.each((function(e,n){e.sort((function(e,n){return t.get(o,e)-t.get(o,n)})),a.push({name:n,indices:e})})),a},n.prototype.getAxisTooltipData=function(t,e,n){mt(t)||(t=t?[t]:[]);for(var i,o=this.getData(),r=this.getLayerSeries(),a=[],s=r.length,l=0;l<s;++l){for(var u=Number.MAX_VALUE,d=-1,c=r[l].indices.length,h=0;h<c;++h){var p=o.get(t[0],r[l].indices[h]),g=Math.abs(p-e);g<=u&&(i=p,u=g,d=r[l].indices[h])}a.push(d)}return{dataIndices:a,nestestValue:i}},n.prototype.formatTooltip=function(t,e,n){var i=this.getData(),o=i.getName(t),r=i.get(i.mapDimension("value"),t);return Ut("nameValue",{name:o,value:r})},n.type="series.themeRiver",n.dependencies=["singleAxis"],n.defaultOption={z:2,colorBy:"data",coordinateSystem:"singleAxis",boundaryGap:["10%","10%"],singleAxisIndex:0,animationEasing:"linear",label:{margin:4,show:!0,position:"left",fontSize:11},emphasis:{label:{show:!0}}},n}(Mt);function sf(t,e){t.eachSeriesByType("themeRiver",(function(t){var e=t.getData(),n=t.coordinateSystem,i={},o=n.getRect();i.rect=o;var r=t.get("boundaryGap"),a=n.getAxis();(i.boundaryGap=r,"horizontal"===a.orient)?(r[0]=m(r[0],o.height),r[1]=m(r[1],o.height),lf(e,t,o.height-r[0]-r[1])):(r[0]=m(r[0],o.width),r[1]=m(r[1],o.width),lf(e,t,o.width-r[0]-r[1]));e.setLayout("layoutInfo",i)}))}function lf(t,e,n){if(t.count())for(var i,o=e.coordinateSystem,r=e.getLayerSeries(),a=t.mapDimension("single"),s=t.mapDimension("value"),l=d(r,(function(e){return d(e.indices,(function(e){var n=o.dataToPoint(t.get(a,e));return n[1]=t.get(s,e),n}))})),u=function(t){for(var e=t.length,n=t[0].length,i=[],o=[],r=0,a=0;a<n;++a){for(var s=0,l=0;l<e;++l)s+=t[l][a][1];s>r&&(r=s),i.push(s)}for(var u=0;u<n;++u)o[u]=(r-i[u])/2;r=0;for(var d=0;d<n;++d){var c=i[d]+o[d];c>r&&(r=c)}return{y0:o,max:r}}(l),c=u.y0,h=n/u.max,p=r.length,g=r[0].indices.length,f=0;f<g;++f){i=c[f]*h,t.setItemLayout(r[0].indices[f],{layerIndex:0,x:l[0][f][0],y0:i,y:l[0][f][1]*h});for(var y=1;y<p;++y)i+=l[y-1][f][1]*h,t.setItemLayout(r[y].indices[f],{layerIndex:y,x:l[y][f][0],y0:i,y:l[y][f][1]*h})}}var uf=function(t){function n(e,n,i,o){var r=t.call(this)||this;r.z2=2,r.textConfig={inside:!0},v(r).seriesIndex=n.seriesIndex;var a=new an({z2:4,silent:e.getModel().get(["label","silent"])});return r.setTextContent(a),r.updateData(!0,e,n,i,o),r}return e(n,t),n.prototype.updateData=function(t,e,n,i,o){this.node=e,e.piece=this,n=n||this._seriesModel,i=i||this._ecModel;var r=this;v(r).dataIndex=e.dataIndex;var s=e.getModel(),l=s.getModel("emphasis"),u=e.getLayout(),d=H({},u);d.label=null;var c=e.getVisual("style");c.lineJoin="bevel";var h=e.getVisual("decal");h&&(c.decal=De(h,o));var p=ki(s.getModel("itemStyle"),d,!0);H(d,p),a(Pn,(function(t){var e=r.ensureState(t),n=s.getModel([t,"itemStyle"]);e.style=n.getItemStyle();var i=ki(n,d);i&&(e.shape=i)})),t?(r.setShape(d),r.shape.r=u.r0,D(r,{shape:{r:u.r}},n,e.dataIndex)):(P(r,{shape:d},n),Vt(r)),r.useStyle(c),this._updateLabel(n);var g=s.getShallow("cursor");g&&r.attr("cursor",g),this._seriesModel=n||this._seriesModel,this._ecModel=i||this._ecModel;var f=l.get("focus"),y="relative"===f?je(e.getAncestorsIndices(),e.getDescendantIndices()):"ancestor"===f?e.getAncestorsIndices():"descendant"===f?e.getDescendantIndices():f;Gt(this,y,l.get("blurScope"),l.get("disabled"))},n.prototype._updateLabel=function(t){var e=this,n=this.node.getModel(),i=n.getModel("label"),o=this.node.getLayout(),r=o.endAngle-o.startAngle,s=(o.startAngle+o.endAngle)/2,l=Math.cos(s),u=Math.sin(s),d=this,c=d.getTextContent(),h=this.node.dataIndex,p=i.get("minAngle")/180*Math.PI,f=i.get("show")&&!(null!=p&&Math.abs(r)<p);function y(t,e){var n=t.get(e);return null==n?i.get(e):n}c.ignore=!f,a(Ri,(function(i){var a="normal"===i?n.getModel("label"):n.getModel([i,"label"]),p="normal"===i,f=p?c:c.ensureState(i),v=t.getFormattedLabel(h,i);p&&(v=v||e.node.name),f.style=sn(a,{},null,"normal"!==i,!0),v&&(f.style.text=v);var m=a.get("show");null==m||p||(f.ignore=!m);var x,_=y(a,"position"),b=p?d:d.states[i],S=b.style.fill;b.textConfig={outsideFill:"inherit"===a.get("color")?S:null,inside:"outside"!==_};var w=y(a,"distance")||0,M=y(a,"align"),I=y(a,"rotate"),A=.5*Math.PI,T=1.5*Math.PI,C=g("tangential"===I?Math.PI/2-s:s),D=C>A&&!Ni(C-A)&&C<T;"outside"===_?(x=o.r+w,M=D?"right":"left"):M&&"center"!==M?"left"===M?(x=o.r0+w,M=D?"right":"left"):"right"===M&&(x=o.r-w,M=D?"left":"right"):(x=r===2*Math.PI&&0===o.r0?0:(o.r+o.r0)/2,M="center"),f.style.align=M,f.style.verticalAlign=y(a,"verticalAlign")||"middle",f.x=x*l+o.cx,f.y=x*u+o.cy;var L=0;"radial"===I?L=g(-s)+(D?Math.PI:0):"tangential"===I?L=g(Math.PI/2-s)+(D?Math.PI:0):Y(I)&&(L=I*Math.PI/180),f.rotation=g(L)})),c.dirtyStyle()},n}(Zn),df="sunburstRootToNode",cf="sunburstHighlight";var hf=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.render=function(t,e,n,i){var o=this;this.seriesModel=t,this.api=n,this.ecModel=e;var r=t.getData(),a=r.tree.root,s=t.getViewRoot(),l=this.group,u=t.get("renderLabelForZeroData"),d=[];s.eachNode((function(t){d.push(t)}));var c,h,p=this._oldChildren||[];!function(i,o){if(0===i.length&&0===o.length)return;function s(t){return t.getId()}function d(s,d){!function(i,o){u||!i||i.getValue()||(i=null);if(i!==a&&o!==a)if(o&&o.piece)i?(o.piece.updateData(!1,i,t,e,n),r.setItemGraphicEl(i.dataIndex,o.piece)):function(t){if(!t)return;t.piece&&(l.remove(t.piece),t.piece=null)}(o);else if(i){var s=new uf(i,t,e,n);l.add(s),r.setItemGraphicEl(i.dataIndex,s)}}(null==s?null:i[s],null==d?null:o[d])}new ln(o,i,s,s).add(d).update(d).remove(Re(d,null)).execute()}(d,p),c=a,(h=s).depth>0?(o.virtualPiece?o.virtualPiece.updateData(!1,c,t,e,n):(o.virtualPiece=new uf(c,t,e,n),l.add(o.virtualPiece)),h.piece.off("click"),o.virtualPiece.on("click",(function(t){o._rootToNode(h.parentNode)}))):o.virtualPiece&&(l.remove(o.virtualPiece),o.virtualPiece=null),this._initEvents(),this._oldChildren=d},n.prototype._initEvents=function(){var t=this;this.group.off("click"),this.group.on("click",(function(e){var n=!1;t.seriesModel.getViewRoot().eachNode((function(i){if(!n&&i.piece&&i.piece===e.target){var o=i.getModel().get("nodeClick");if("rootToNode"===o)t._rootToNode(i);else if("link"===o){var r=i.getModel(),a=r.get("link");if(a){var s=r.get("target",!0)||"_blank";un(a,s)}}n=!0}}))}))},n.prototype._rootToNode=function(t){t!==this.seriesModel.getViewRoot()&&this.api.dispatchAction({type:df,from:this.uid,seriesId:this.seriesModel.id,targetNode:t})},n.prototype.containPoint=function(t,e){var n=e.getData().getItemLayout(0);if(n){var i=t[0]-n.cx,o=t[1]-n.cy,r=Math.sqrt(i*i+o*o);return r<=n.r&&r>=n.r0}},n.type="sunburst",n}(Dt),pf=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.ignoreStyleOnData=!0,e}return e(n,t),n.prototype.getInitialData=function(t,e){var n={name:t.name,children:t.data};gf(n);var i=this._levelModels=d(t.levels||[],(function(t){return new qt(t,this,e)}),this),o=_u.createTree(n,this,(function(t){t.wrapMethod("getItemModel",(function(t,e){var n=o.getNodeByDataIndex(e),r=i[n.depth];return r&&(t.parentModel=r),t}))}));return o.data},n.prototype.optionUpdated=function(){this.resetViewRoot()},n.prototype.getDataParams=function(e){var n=t.prototype.getDataParams.apply(this,arguments),i=this.getData().tree.getNodeByDataIndex(e);return n.treePathInfo=Mu(i,this),n},n.prototype.getLevelModel=function(t){return this._levelModels&&this._levelModels[t.depth]},n.prototype.getViewRoot=function(){return this._viewRoot},n.prototype.resetViewRoot=function(t){t?this._viewRoot=t:t=this._viewRoot;var e=this.getRawData().tree.root;t&&(t===e||e.contains(t))||(this._viewRoot=e)},n.prototype.enableAriaDecal=function(){Lu(this)},n.type="series.sunburst",n.defaultOption={z:2,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,stillShowZeroSum:!0,nodeClick:"rootToNode",renderLabelForZeroData:!1,label:{rotate:"radial",show:!0,opacity:1,align:"center",position:"inside",distance:5,silent:!0},itemStyle:{borderWidth:1,borderColor:"white",borderType:"solid",shadowBlur:0,shadowColor:"rgba(0, 0, 0, 0.2)",shadowOffsetX:0,shadowOffsetY:0,opacity:1},emphasis:{focus:"descendant"},blur:{itemStyle:{opacity:.2},label:{opacity:.1}},animationType:"expansion",animationDuration:1e3,animationDurationUpdate:500,data:[],sort:"desc"},n}(Mt);function gf(t){var e=0;a(t.children,(function(t){gf(t);var n=t.value;mt(n)&&(n=n[0]),e+=n}));var n=t.value;mt(n)&&(n=n[0]),(null==n||isNaN(n))&&(n=e),n<0&&(n=0),mt(t.value)?t.value[0]=n:t.value=n}var ff=Math.PI/180;function yf(t,e,n){e.eachSeriesByType(t,(function(t){var e=t.get("center"),i=t.get("radius");mt(i)||(i=[0,i]),mt(e)||(e=[e,e]);var o=n.getWidth(),r=n.getHeight(),s=Math.min(o,r),l=m(e[0],o),u=m(e[1],r),d=m(i[0],s/2),c=m(i[1],s/2),h=-t.get("startAngle")*ff,p=t.get("minAngle")*ff,g=t.getData().tree.root,f=t.getViewRoot(),y=f.depth,v=t.get("sort");null!=v&&vf(f,v);var x=0;a(f.children,(function(t){!isNaN(t.getValue())&&x++}));var _=f.getValue(),b=Math.PI/(_||x)*2,S=f.depth>0,w=f.height-(S?-1:1),M=(c-d)/(w||1),I=t.get("clockwise"),A=t.get("stillShowZeroSum"),T=I?1:-1,C=function(e,n){if(e){var i=n;if(e!==g){var o=e.getValue(),r=0===_&&A?b:o*b;r<p&&(r=p),i=n+T*r;var c=e.depth-y-(S?-1:1),h=d+M*c,f=d+M*(c+1),v=t.getLevelModel(e);if(v){var x=v.get("r0",!0),w=v.get("r",!0),D=v.get("radius",!0);null!=D&&(x=D[0],w=D[1]),null!=x&&(h=m(x,s/2)),null!=w&&(f=m(w,s/2))}e.setLayout({angle:r,startAngle:n,endAngle:i,clockwise:I,cx:l,cy:u,r0:h,r:f})}if(e.children&&e.children.length){var L=0;a(e.children,(function(t){L+=C(t,n+L)}))}return i-n}};if(S){var D=d,L=d+M,P=2*Math.PI;g.setLayout({angle:P,startAngle:h,endAngle:h+P,clockwise:I,cx:l,cy:u,r0:D,r:L})}C(f,h)}))}function vf(t,e){var n=t.children||[];t.children=function(t,e){if(f(e)){var n=d(t,(function(t,e){var n=t.getValue();return{params:{depth:t.depth,height:t.height,dataIndex:t.dataIndex,getValue:function(){return n}},index:e}}));return n.sort((function(t,n){return e(t.params,n.params)})),d(n,(function(e){return t[e.index]}))}var i="asc"===e;return t.sort((function(t,e){var n=(t.getValue()-e.getValue())*(i?1:-1);return 0===n?(t.dataIndex-e.dataIndex)*(i?-1:1):n}))}(n,e),n.length&&a(t.children,(function(t){vf(t,e)}))}function mf(t){var e={};t.eachSeriesByType("sunburst",(function(t){var n=t.getData(),i=n.tree;i.eachNode((function(o){var r=o.getModel().getModel("itemStyle").getItemStyle();r.fill||(r.fill=function(t,n,i){for(var o=t;o&&o.depth>1;)o=o.parentNode;var r=n.getColorFromPalette(o.name||o.dataIndex+"",e);return t.depth>1&&G(r)&&(r=Vi(r,(t.depth-1)/(i-1)*.5)),r}(o,t,i.root.height));var a=n.ensureUniqueItemVisual(o.dataIndex,"style");H(a,r)}))}))}var xf={color:"fill",borderColor:"stroke"},_f={symbol:1,symbolSize:1,symbolKeepAspect:1,legendIcon:1,visualMeta:1,liftZ:1,decal:1},bf=h(),Sf=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.optionUpdated=function(){this.currentZLevel=this.get("zlevel",!0),this.currentZ=this.get("z",!0)},n.prototype.getInitialData=function(t,e){return wt(null,this)},n.prototype.getDataParams=function(e,n,i){var o=t.prototype.getDataParams.call(this,e,n);return i&&(o.info=bf(i).info),o},n.type="series.custom",n.dependencies=["grid","polar","geo","singleAxis","calendar"],n.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,clip:!1},n}(Mt);function wf(t,e){return e=e||[0,0],d(["x","y"],(function(n,i){var o=this.getAxis(n),r=e[i],a=t[i]/2;return"category"===o.type?o.getBandWidth():Math.abs(o.dataToCoord(r-a)-o.dataToCoord(r+a))}),this)}function Mf(t,e){return e=e||[0,0],d([0,1],(function(n){var i=e[n],o=t[n]/2,r=[],a=[];return r[n]=i-o,a[n]=i+o,r[1-n]=a[1-n]=e[1-n],Math.abs(this.dataToPoint(r)[n]-this.dataToPoint(a)[n])}),this)}function If(t,e){var n=this.getAxis(),i=e instanceof Array?e[0]:e,o=(t instanceof Array?t[0]:t)/2;return"category"===n.type?n.getBandWidth():Math.abs(n.dataToCoord(i-o)-n.dataToCoord(i+o))}function Af(t,e){return e=e||[0,0],d(["Radius","Angle"],(function(n,i){var o=this["get"+n+"Axis"](),r=e[i],a=t[i]/2,s="category"===o.type?o.getBandWidth():Math.abs(o.dataToCoord(r-a)-o.dataToCoord(r+a));return"Angle"===n&&(s=s*Math.PI/180),s}),this)}function Tf(t,e,n,i){return t&&(t.legacy||!1!==t.legacy&&!n&&!i&&"tspan"!==e&&("text"===e||ge(t,"text")))}function Cf(t,e,n){var i,o,r,s=t;if("text"===e)r=s;else{r={},ge(s,"text")&&(r.text=s.text),ge(s,"rich")&&(r.rich=s.rich),ge(s,"textFill")&&(r.fill=s.textFill),ge(s,"textStroke")&&(r.stroke=s.textStroke),ge(s,"fontFamily")&&(r.fontFamily=s.fontFamily),ge(s,"fontSize")&&(r.fontSize=s.fontSize),ge(s,"fontStyle")&&(r.fontStyle=s.fontStyle),ge(s,"fontWeight")&&(r.fontWeight=s.fontWeight),o={type:"text",style:r,silent:!0},i={};var l=ge(s,"textPosition");n?i.position=l?s.textPosition:"inside":l&&(i.position=s.textPosition),ge(s,"textPosition")&&(i.position=s.textPosition),ge(s,"textOffset")&&(i.offset=s.textOffset),ge(s,"textRotation")&&(i.rotation=s.textRotation),ge(s,"textDistance")&&(i.distance=s.textDistance)}return Df(r,t),a(r.rich,(function(t){Df(t,t)})),{textConfig:i,textContent:o}}function Df(t,e){e&&(e.font=e.textFont||e.font,ge(e,"textStrokeWidth")&&(t.lineWidth=e.textStrokeWidth),ge(e,"textAlign")&&(t.align=e.textAlign),ge(e,"textVerticalAlign")&&(t.verticalAlign=e.textVerticalAlign),ge(e,"textLineHeight")&&(t.lineHeight=e.textLineHeight),ge(e,"textWidth")&&(t.width=e.textWidth),ge(e,"textHeight")&&(t.height=e.textHeight),ge(e,"textBackgroundColor")&&(t.backgroundColor=e.textBackgroundColor),ge(e,"textPadding")&&(t.padding=e.textPadding),ge(e,"textBorderColor")&&(t.borderColor=e.textBorderColor),ge(e,"textBorderWidth")&&(t.borderWidth=e.textBorderWidth),ge(e,"textBorderRadius")&&(t.borderRadius=e.textBorderRadius),ge(e,"textBoxShadowColor")&&(t.shadowColor=e.textBoxShadowColor),ge(e,"textBoxShadowBlur")&&(t.shadowBlur=e.textBoxShadowBlur),ge(e,"textBoxShadowOffsetX")&&(t.shadowOffsetX=e.textBoxShadowOffsetX),ge(e,"textBoxShadowOffsetY")&&(t.shadowOffsetY=e.textBoxShadowOffsetY))}function Lf(t,e,n){var i=t;i.textPosition=i.textPosition||n.position||"inside",null!=n.offset&&(i.textOffset=n.offset),null!=n.rotation&&(i.textRotation=n.rotation),null!=n.distance&&(i.textDistance=n.distance);var o=i.textPosition.indexOf("inside")>=0,r=t.fill||"#000";Pf(i,e);var s=null==i.textFill;return o?s&&(i.textFill=n.insideFill||"#fff",!i.textStroke&&n.insideStroke&&(i.textStroke=n.insideStroke),!i.textStroke&&(i.textStroke=r),null==i.textStrokeWidth&&(i.textStrokeWidth=2)):(s&&(i.textFill=t.fill||n.outsideFill||"#000"),!i.textStroke&&n.outsideStroke&&(i.textStroke=n.outsideStroke)),i.text=e.text,i.rich=e.rich,a(e.rich,(function(t){Pf(t,t)})),i}function Pf(t,e){e&&(ge(e,"fill")&&(t.textFill=e.fill),ge(e,"stroke")&&(t.textStroke=e.fill),ge(e,"lineWidth")&&(t.textStrokeWidth=e.lineWidth),ge(e,"font")&&(t.font=e.font),ge(e,"fontStyle")&&(t.fontStyle=e.fontStyle),ge(e,"fontWeight")&&(t.fontWeight=e.fontWeight),ge(e,"fontSize")&&(t.fontSize=e.fontSize),ge(e,"fontFamily")&&(t.fontFamily=e.fontFamily),ge(e,"align")&&(t.textAlign=e.align),ge(e,"verticalAlign")&&(t.textVerticalAlign=e.verticalAlign),ge(e,"lineHeight")&&(t.textLineHeight=e.lineHeight),ge(e,"width")&&(t.textWidth=e.width),ge(e,"height")&&(t.textHeight=e.height),ge(e,"backgroundColor")&&(t.textBackgroundColor=e.backgroundColor),ge(e,"padding")&&(t.textPadding=e.padding),ge(e,"borderColor")&&(t.textBorderColor=e.borderColor),ge(e,"borderWidth")&&(t.textBorderWidth=e.borderWidth),ge(e,"borderRadius")&&(t.textBorderRadius=e.borderRadius),ge(e,"shadowColor")&&(t.textBoxShadowColor=e.shadowColor),ge(e,"shadowBlur")&&(t.textBoxShadowBlur=e.shadowBlur),ge(e,"shadowOffsetX")&&(t.textBoxShadowOffsetX=e.shadowOffsetX),ge(e,"shadowOffsetY")&&(t.textBoxShadowOffsetY=e.shadowOffsetY),ge(e,"textShadowColor")&&(t.textShadowColor=e.textShadowColor),ge(e,"textShadowBlur")&&(t.textShadowBlur=e.textShadowBlur),ge(e,"textShadowOffsetX")&&(t.textShadowOffsetX=e.textShadowOffsetX),ge(e,"textShadowOffsetY")&&(t.textShadowOffsetY=e.textShadowOffsetY))}var kf={position:["x","y"],scale:["scaleX","scaleY"],origin:["originX","originY"]},Nf=y(kf);He(Ei,(function(t,e){return t[e]=1,t}),{}),Ei.join(", ");var Rf=["","style","shape","extra"],Vf=h();function Of(t,e,n,i,o){var r=t+"Animation",a=Oi(t,i,o)||{},s=Vf(e).userDuring;return a.duration>0&&(a.during=s?Zt(Hf,{el:e,userDuring:s}):null,a.setToFinal=!0,a.scope=t),H(a,n[r]),a}function Ef(t,e,n,i){var o=(i=i||{}).dataIndex,r=i.isInit,s=i.clearStyle,l=n.isAnimationEnabled(),u=Vf(t),d=e.style;u.userDuring=e.during;var c={},h={};if(function(t,e,n){for(var i=0;i<Nf.length;i++){var o=Nf[i],r=kf[o],a=e[o];a&&(n[r[0]]=a[0],n[r[1]]=a[1])}for(i=0;i<Ei.length;i++){var s=Ei[i];null!=e[s]&&(n[s]=e[s])}}(0,e,h),Yf("shape",e,h),Yf("extra",e,h),!r&&l&&(function(t,e,n){for(var i=e.transition,o=Gf(i)?Ei:nn(i||[]),r=0;r<o.length;r++){var a=o[r];if("style"!==a&&"shape"!==a&&"extra"!==a){var s=t[a];n[a]=s}}}(t,e,c),Zf("shape",t,e,c),Zf("extra",t,e,c),function(t,e,n,i){if(!n)return;var o,r=t.style;if(r){var a=n.transition,s=e.transition;if(a&&!Gf(a)){var l=nn(a);!o&&(o=i.style={});for(var u=0;u<l.length;u++){var d=r[g=l[u]];o[g]=d}}else if(t.getAnimationStyleProps&&(Gf(s)||Gf(a)||L(s,"style")>=0)){var c=t.getAnimationStyleProps(),h=c?c.style:null;if(h){!o&&(o=i.style={});var p=y(n);for(u=0;u<p.length;u++){var g;if(h[g=p[u]]){d=r[g];o[g]=d}}}}}}(t,e,d,c)),h.style=d,function(t,e,n){var i=e.style;if(!t.isGroup&&i){if(n){t.useStyle({});for(var o=t.animators,r=0;r<o.length;r++){var a=o[r];"style"===a.targetName&&a.changeTarget(t.style)}}t.setStyle(i)}e&&(e.style=null,e&&t.attr(e),e.style=i)}(t,h,s),function(t,e){ge(e,"silent")&&(t.silent=e.silent),ge(e,"ignore")&&(t.ignore=e.ignore),t instanceof Te&&ge(e,"invisible")&&(t.invisible=e.invisible);t instanceof q&&ge(e,"autoBatch")&&(t.autoBatch=e.autoBatch)}(t,e),l)if(r){var p={};a(Rf,(function(t){var n=t?e[t]:e;n&&n.enterFrom&&(t&&(p[t]=p[t]||{}),H(t?p[t]:p,n.enterFrom))}));var g=Of("enter",t,e,n,o);g.duration>0&&t.animateFrom(p,g)}else!function(t,e,n,i,o){if(o){var r=Of("update",t,e,i,n);r.duration>0&&t.animateFrom(o,r)}}(t,e,o||0,n,c);zf(t,e),d?t.dirty():t.markRedraw()}function zf(t,e){for(var n=Vf(t).leaveToProps,i=0;i<Rf.length;i++){var o=Rf[i],r=o?e[o]:e;r&&r.leaveTo&&(n||(n=Vf(t).leaveToProps={}),o&&(n[o]=n[o]||{}),H(o?n[o]:n,r.leaveTo))}}function Bf(t,e,n,i){if(t){var o=t.parent,r=Vf(t).leaveToProps;if(r){var a=Of("update",t,e,n,0);a.done=function(){o.remove(t)},t.animateTo(r,a)}else o.remove(t)}}function Gf(t){return"all"===t}var Ff={},Wf={setTransform:function(t,e){return Ff.el[t]=e,this},getTransform:function(t){return Ff.el[t]},setShape:function(t,e){var n=Ff.el;return(n.shape||(n.shape={}))[t]=e,n.dirtyShape&&n.dirtyShape(),this},getShape:function(t){var e=Ff.el.shape;if(e)return e[t]},setStyle:function(t,e){var n=Ff.el,i=n.style;return i&&(i[t]=e,n.dirtyStyle&&n.dirtyStyle()),this},getStyle:function(t){var e=Ff.el.style;if(e)return e[t]},setExtra:function(t,e){return(Ff.el.extra||(Ff.el.extra={}))[t]=e,this},getExtra:function(t){var e=Ff.el.extra;if(e)return e[t]}};function Hf(){var t=this,e=t.el;if(e){var n=Vf(e).userDuring,i=t.userDuring;n===i?(Ff.el=e,i(Wf)):t.el=t.userDuring=null}}function Zf(t,e,n,i){var o=n[t];if(o){var r,a=e[t];if(a){var s=n.transition,l=o.transition;if(l)if(!r&&(r=i[t]={}),Gf(l))H(r,a);else for(var u=nn(l),d=0;d<u.length;d++){var c=a[p=u[d]];r[p]=c}else if(Gf(s)||L(s,t)>=0){!r&&(r=i[t]={});var h=y(a);for(d=0;d<h.length;d++){var p;c=a[p=h[d]];Xf(o[p],c)&&(r[p]=c)}}}}}function Yf(t,e,n){var i=e[t];if(i)for(var o=n[t]={},r=y(i),a=0;a<r.length;a++){var s=r[a];o[s]=zi(i[s])}}function Xf(t,e){return Bi(t)?t!==e:null!=t&&isFinite(t)}var Uf=h(),jf=["percent","easing","shape","style","extra"];function qf(t){t.stopAnimation("keyframe"),t.attr(Uf(t))}function Kf(t,e,n){if(n.isAnimationEnabled()&&e)if(mt(e))a(e,(function(e){Kf(t,e,n)}));else{var i=e.keyframes,o=e.duration;if(n&&null==o){var r=Oi("enter",n,0);o=r&&r.duration}if(i&&o){var s=Uf(t);a(Rf,(function(n){var r;n&&!t[n]||(i.sort((function(t,e){return t.percent-e.percent})),a(i,(function(i){var l=t.animators,u=n?i[n]:i;if(u){var d=y(u);if(n||(d=c(d,(function(t){return L(jf,t)<0}))),d.length){r||((r=t.animate(n,e.loop,!0)).scope="keyframe");for(var h=0;h<l.length;h++)l[h]!==r&&l[h].targetName===r.targetName&&l[h].stopTracks(d);n&&(s[n]=s[n]||{});var p=n?s[n]:s;a(d,(function(e){p[e]=((n?t[n]:t)||{})[e]})),r.whenWithKeys(o*i.percent,u,d,i.easing)}}})),r&&r.delay(e.delay||0).duration(o).start(e.easing))}))}}}var Jf="emphasis",$f="normal",Qf="blur",ty="select",ey=[$f,Jf,Qf,ty],ny={normal:["itemStyle"],emphasis:[Jf,"itemStyle"],blur:[Qf,"itemStyle"],select:[ty,"itemStyle"]},iy={normal:["label"],emphasis:[Jf,"label"],blur:[Qf,"label"],select:[ty,"label"]},oy=["x","y"],ry={normal:{},emphasis:{},blur:{},select:{}},ay={cartesian2d:function(t){var e=t.master.getRect();return{coordSys:{type:"cartesian2d",x:e.x,y:e.y,width:e.width,height:e.height},api:{coord:function(e){return t.dataToPoint(e)},size:Zt(wf,t)}}},geo:function(t){var e=t.getBoundingRect();return{coordSys:{type:"geo",x:e.x,y:e.y,width:e.width,height:e.height,zoom:t.getZoom()},api:{coord:function(e){return t.dataToPoint(e)},size:Zt(Mf,t)}}},single:function(t){var e=t.getRect();return{coordSys:{type:"singleAxis",x:e.x,y:e.y,width:e.width,height:e.height},api:{coord:function(e){return t.dataToPoint(e)},size:Zt(If,t)}}},polar:function(t){var e=t.getRadiusAxis(),n=t.getAngleAxis(),i=e.getExtent();return i[0]>i[1]&&i.reverse(),{coordSys:{type:"polar",cx:t.cx,cy:t.cy,r:i[1],r0:i[0]},api:{coord:function(i){var o=e.dataToRadius(i[0]),r=n.dataToAngle(i[1]),a=t.coordToPoint([o,r]);return a.push(o,r*Math.PI/180),a},size:Zt(Af,t)}}},calendar:function(t){var e=t.getRect(),n=t.getRangeInfo();return{coordSys:{type:"calendar",x:e.x,y:e.y,width:e.width,height:e.height,cellWidth:t.getCellWidth(),cellHeight:t.getCellHeight(),rangeInfo:{start:n.start,end:n.end,weeks:n.weeks,dayCount:n.allDay}},api:{coord:function(e,n){return t.dataToPoint(e,n)}}}}};function sy(t){return t instanceof q}function ly(t){return t instanceof Te}var uy=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.render=function(t,e,n,i){this._progressiveEls=null;var o=this._data,r=t.getData(),a=this.group,s=gy(t,r,e,n);o||a.removeAll(),r.diff(o).add((function(e){yy(n,null,e,s(e,i),t,a,r)})).remove((function(e){var n=o.getItemGraphicEl(e);n&&Bf(n,bf(n).option,t)})).update((function(e,l){var u=o.getItemGraphicEl(l);yy(n,u,e,s(e,i),t,a,r)})).execute();var l=t.get("clip",!0)?fi(t.coordinateSystem,!1,t):null;l?a.setClipPath(l):a.removeClipPath(),this._data=r},n.prototype.incrementalPrepareRender=function(t,e,n){this.group.removeAll(),this._data=null},n.prototype.incrementalRender=function(t,e,n,i,o){var r=e.getData(),a=gy(e,r,n,i),s=this._progressiveEls=[];function l(t){t.isGroup||(t.incremental=!0,t.ensureState("emphasis").hoverLayer=!0)}for(var u=t.start;u<t.end;u++){var d=yy(null,null,u,a(u,o),e,this.group,r);d&&(d.traverse(l),s.push(d))}},n.prototype.eachRendered=function(t){On(this._progressiveEls||this.group,t)},n.prototype.filterForExposedEvent=function(t,e,n,i){var o=e.element;if(null==o||n.name===o)return!0;for(;(n=n.__hostTarget||n.parent)&&n!==this.group;)if(n.name===o)return!0;return!1},n.type="custom",n}(Dt);function dy(t){var e,n=t.type;if("path"===n){var i=t.shape,o=null!=i.width&&null!=i.height?{x:i.x||0,y:i.y||0,width:i.width,height:i.height}:null,r=Ay(i);e=Hi(r,null,o,i.layout||"center"),bf(e).customPathData=r}else if("image"===n)e=new R({}),bf(e).customImagePath=t.style.image;else if("text"===n)e=new an({});else if("group"===n)e=new It;else{if("compoundPath"===n)throw new Error('"compoundPath" is not supported yet.');var a=Zi(n);if(!a){pi("")}e=new a}return bf(e).customGraphicType=n,e.name=t.name,e.z2EmphasisLift=1,e.z2SelectLift=1,e}function cy(t,e,n,i,o,r,a){qf(e);var s=o&&o.normal.cfg;s&&e.setTextConfig(s),i&&null==i.transition&&(i.transition=oy);var l=i&&i.style;if(l){if("text"===e.type){var u=l;ge(u,"textFill")&&(u.fill=u.textFill),ge(u,"textStroke")&&(u.stroke=u.textStroke)}var d=void 0,c=sy(e)?l.decal:null;t&&c&&(c.dirty=!0,d=De(c,t)),l.__decalPattern=d}ly(e)&&(l&&(d=l.__decalPattern)&&(l.decal=d));Ef(e,i,r,{dataIndex:n,isInit:a,clearStyle:!0}),Kf(e,i.keyframeAnimation,r)}function hy(t,e,n,i,o){var r=e.isGroup?null:e,a=o&&o[t].cfg;if(r){var s=r.ensureState(t);if(!1===i){var l=r.getState(t);l&&(l.style=null)}else s.style=i||null;a&&(s.textConfig=a),Ce(r)}}function py(t,e,n){var i=n===$f,o=i?e:_y(e,n),r=o?o.z2:null;null!=r&&((i?t:t.ensureState(n)).z2=r||0)}function gy(t,e,n,i){var o=t.get("renderItem"),r=t.coordinateSystem,a={};r&&(a=r.prepareCustoms?r.prepareCustoms(r):ay[r.type](r));for(var s,l,u=Ot({getWidth:i.getWidth,getHeight:i.getHeight,getZr:i.getZr,getDevicePixelRatio:i.getDevicePixelRatio,value:function(t,n){return null==n&&(n=s),e.getStore().get(e.getDimensionIndex(t||0),n)},style:function(n,i){null==i&&(i=s);var o=e.getItemVisual(i,"style"),r=o&&o.fill,a=o&&o.opacity,l=m(i,$f).getItemStyle();null!=r&&(l.fill=r),null!=a&&(l.opacity=a);var u={inheritColor:G(r)?r:"#000"},d=x(i,$f),c=sn(d,null,u,!1,!0);c.text=d.getShallow("show")?C(t.getFormattedLabel(i,$f),Ii(e,i)):null;var h=Gi(d,u,!1);return b(n,l),l=Lf(l,c,h),n&&_(l,n),l.legacy=!0,l},ordinalRawValue:function(t,n){null==n&&(n=s),t=t||0;var i=e.getDimensionInfo(t);if(!i){var o=e.getDimensionIndex(t);return o>=0?e.getStore().get(o,n):void 0}var r=e.get(i.name,n),a=i&&i.ordinalMeta;return a?a.categories[r]:r},styleEmphasis:function(n,i){null==i&&(i=s);var o=m(i,Jf).getItemStyle(),r=x(i,Jf),a=sn(r,null,null,!0,!0);a.text=r.getShallow("show")?Sn(t.getFormattedLabel(i,Jf),t.getFormattedLabel(i,$f),Ii(e,i)):null;var l=Gi(r,null,!0);return b(n,o),o=Lf(o,a,l),n&&_(o,n),o.legacy=!0,o},visual:function(t,n){if(null==n&&(n=s),ge(xf,t)){var i=e.getItemVisual(n,"style");return i?i[xf[t]]:null}if(ge(_f,t))return e.getItemVisual(n,t)},barLayout:function(t){if("cartesian2d"===r.type){var e=r.getBaseAxis();return Fi(Ot({axis:e},t))}},currentSeriesIndices:function(){return n.getCurrentSeriesIndices()},font:function(t){return Wi(t,n)}},a.api||{}),d={context:{},seriesId:t.id,seriesName:t.name,seriesIndex:t.seriesIndex,coordSys:a.coordSys,dataInsideLength:e.count(),encode:fy(t.getData())},c={},h={},p={},g={},f=0;f<ey.length;f++){var y=ey[f];p[y]=t.getModel(ny[y]),g[y]=t.getModel(iy[y])}function v(t){return t===s?l||(l=e.getItemModel(t)):e.getItemModel(t)}function m(t,n){return e.hasItemOption?t===s?c[n]||(c[n]=v(t).getModel(ny[n])):v(t).getModel(ny[n]):p[n]}function x(t,n){return e.hasItemOption?t===s?h[n]||(h[n]=v(t).getModel(iy[n])):v(t).getModel(iy[n]):g[n]}return function(t,n){return s=t,l=null,c={},h={},o&&o(Ot({dataIndexInside:t,dataIndex:e.getRawIndex(t),actionType:n?n.type:null},d),u)};function _(t,e){for(var n in e)ge(e,n)&&(t[n]=e[n])}function b(t,e){t&&(t.textFill&&(e.textFill=t.textFill),t.textPosition&&(e.textPosition=t.textPosition))}}function fy(t){var e={};return a(t.dimensions,(function(n){var i=t.getDimensionInfo(n);if(!i.isExtraCoord){var o=i.coordDim;(e[o]=e[o]||[])[i.coordDimIndex]=t.getDimensionIndex(n)}})),e}function yy(t,e,n,i,o,r,a){if(i){var s=vy(t,e,n,i,o,r);return s&&a.setItemGraphicEl(n,s),s&&Gt(s,i.focus,i.blurScope,i.emphasisDisabled),s}r.remove(e)}function vy(t,e,n,i,o,r){var a=-1,s=e;e&&my(e,i,o)&&(a=L(r.childrenRef(),e),e=null);var l,u,d=!e,c=e;c?c.clearStates():(c=dy(i),s&&(l=s,(u=c).copyTransform(l),ly(u)&&ly(l)&&(u.setStyle(l.style),u.z=l.z,u.z2=l.z2,u.zlevel=l.zlevel,u.invisible=l.invisible,u.ignore=l.ignore,sy(u)&&sy(l)&&u.setShape(l.shape)))),!1===i.morph?c.disableMorphing=!0:c.disableMorphing&&(c.disableMorphing=!1),ry.normal.cfg=ry.normal.conOpt=ry.emphasis.cfg=ry.emphasis.conOpt=ry.blur.cfg=ry.blur.conOpt=ry.select.cfg=ry.select.conOpt=null,ry.isLegacy=!1,function(t,e,n,i,o,r){if(t.isGroup)return;xy(n,null,r),xy(n,Jf,r);var a=r.normal.conOpt,s=r.emphasis.conOpt,l=r.blur.conOpt,u=r.select.conOpt;if(null!=a||null!=s||null!=u||null!=l){var d=t.getTextContent();if(!1===a)d&&t.removeTextContent();else{a=r.normal.conOpt=a||{type:"text"},d?d.clearStates():(d=dy(a),t.setTextContent(d)),cy(null,d,e,a,null,i,o);for(var c=a&&a.style,h=0;h<ey.length;h++){var p=ey[h];if(p!==$f){var g=r[p].conOpt;hy(p,d,0,by(a,g,p),null)}}c?d.dirty():d.markRedraw()}}}(c,n,i,o,d,ry),function(t,e,n,i,o){var r=n.clipPath;if(!1===r)t&&t.getClipPath()&&t.removeClipPath();else if(r){var a=t.getClipPath();a&&my(a,r,i)&&(a=null),a||(a=dy(r),t.setClipPath(a)),cy(null,a,e,r,null,i,o)}}(c,n,i,o,d),cy(t,c,n,i,ry,o,d),ge(i,"info")&&(bf(c).info=i.info);for(var h=0;h<ey.length;h++){var p=ey[h];if(p!==$f){var g=_y(i,p);hy(p,c,0,by(i,g,p),ry)}}return function(t,e,n){if(!t.isGroup){var i=t,o=n.currentZ,r=n.currentZLevel;i.z=o,i.zlevel=r;var a=e.z2;null!=a&&(i.z2=a||0);for(var s=0;s<ey.length;s++)py(i,e,ey[s])}}(c,i,o),"group"===i.type&&function(t,e,n,i,o){var r=i.children,a=r?r.length:0,s=i.$mergeChildren,l="byName"===s||i.diffChildrenByName,u=!1===s;if(!a&&!l&&!u)return;if(l)return d={api:t,oldChildren:e.children()||[],newChildren:r||[],dataIndex:n,seriesModel:o,group:e},void new ln(d.oldChildren,d.newChildren,wy,wy,d).add(My).update(My).remove(Iy).execute();var d;u&&e.removeAll();for(var c=0;c<a;c++){var h=r[c],p=e.childAt(c);h?(null==h.ignore&&(h.ignore=!1),vy(t,p,n,h,o,e)):p.ignore=!0}for(var g=e.childCount()-1;g>=c;g--){var f=e.childAt(g);Sy(e,f,o)}}(t,c,n,i,o),a>=0?r.replaceAt(c,a):r.add(c),c}function my(t,e,n){var i,o=bf(t),r=e.type,a=e.shape,s=e.style;return n.isUniversalTransitionEnabled()||null!=r&&r!==o.customGraphicType||"path"===r&&((i=a)&&(ge(i,"pathData")||ge(i,"d")))&&Ay(a)!==o.customPathData||"image"===r&&ge(s,"image")&&s.image!==o.customImagePath}function xy(t,e,n){var i=e?_y(t,e):t,o=e?by(t,i,Jf):t.style,r=t.type,a=i?i.textConfig:null,s=t.textContent,l=s?e?_y(s,e):s:null;if(o&&(n.isLegacy||Tf(o,r,!!a,!!l))){n.isLegacy=!0;var u=Cf(o,r,!e);!a&&u.textConfig&&(a=u.textConfig),!l&&u.textContent&&(l=u.textContent)}if(!e&&l){var d=l;!d.type&&(d.type="text")}var c=e?n[e]:n.normal;c.cfg=a,c.conOpt=l}function _y(t,e){return e?t?t[e]:null:t}function by(t,e,n){var i=e&&e.style;return null==i&&n===Jf&&t&&(i=t.styleEmphasis),i}function Sy(t,e,n){e&&Bf(e,bf(t).option,n)}function wy(t,e){var n=t&&t.name;return null!=n?n:"e\0\0"+e}function My(t,e){var n=this.context,i=null!=t?n.newChildren[t]:null,o=null!=e?n.oldChildren[e]:null;vy(n.api,o,n.dataIndex,i,n.seriesModel,n.group)}function Iy(t){var e=this.context,n=e.oldChildren[t];n&&Bf(n,bf(n).option,e.seriesModel)}function Ay(t){return t&&(t.pathData||t.d)}var Ty=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return e(n,t),n.prototype.makeElOption=function(t,e,n,i,o){var r=n.axis;"angle"===r.dim&&(this.animationThreshold=Math.PI/18);var a=r.polar,s=a.getOtherAxis(r).getExtent(),l=r.dataToCoord(e),u=i.get("type");if(u&&"none"!==u){var d=Yi(i),c=Cy[u](r,a,l,s);c.style=d,t.graphicKey=c.type,t.pointer=c}var h=function(t,e,n,i,o){var r=e.axis,a=r.dataToCoord(t),s=i.getAngleAxis().getExtent()[0];s=s/180*Math.PI;var l,u,d,c=i.getRadiusAxis().getExtent();if("radius"===r.dim){var h=Me();be(h,h,s),we(h,h,[i.cx,i.cy]),l=ei([a,-o],h);var p=e.getModel("axisLabel").get("rotate")||0,g=te.innerTextLayout(s,p*Math.PI/180,-1);u=g.textAlign,d=g.textVerticalAlign}else{var f=c[1];l=i.coordToPoint([f+o,a]);var y=i.cx,v=i.cy;u=Math.abs(l[0]-y)/f<.3?"center":l[0]>y?"left":"right",d=Math.abs(l[1]-v)/f<.3?"middle":l[1]>v?"top":"bottom"}return{position:l,align:u,verticalAlign:d}}(e,n,0,a,i.get(["label","margin"]));Xi(t,n,i,o,h)},n}(Ui);var Cy={line:function(t,e,n,i){return"angle"===t.dim?{type:"Line",shape:qi(e.coordToPoint([i[0],n]),e.coordToPoint([i[1],n]))}:{type:"Circle",shape:{cx:e.cx,cy:e.cy,r:n}}},shadow:function(t,e,n,i){var o=Math.max(1,t.getBandWidth()),r=Math.PI/180;return"angle"===t.dim?{type:"Sector",shape:ji(e.cx,e.cy,i[0],i[1],(-n-o/2)*r,(o/2-n)*r)}:{type:"Sector",shape:ji(e.cx,e.cy,n-o/2,n+o/2,0,2*Math.PI)}}},Dy=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.findAxisModel=function(t){var e;return this.ecModel.eachComponent(t,(function(t){t.getCoordSysModel()===this&&(e=t)}),this),e},n.type="polar",n.dependencies=["radiusAxis","angleAxis"],n.defaultOption={z:0,center:["50%","50%"],radius:"80%"},n}(Qt),Ly=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return e(n,t),n.prototype.getCoordSysModel=function(){return this.getReferringComponents("polar",ze).models[0]},n.type="polarAxis",n}(Qt);Kt(Ly,Jt);var Py=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.type="angleAxis",n}(Ly),ky=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.type="radiusAxis",n}(Ly),Ny=function(t){function n(e,n){return t.call(this,"radius",e,n)||this}return e(n,t),n.prototype.pointToData=function(t,e){return this.polar.pointToData(t,e)["radius"===this.dim?0:1]},n}(re);Ny.prototype.dataToRadius=re.prototype.dataToCoord,Ny.prototype.radiusToData=re.prototype.coordToData;var Ry=h(),Vy=function(t){function n(e,n){return t.call(this,"angle",e,n||[0,360])||this}return e(n,t),n.prototype.pointToData=function(t,e){return this.polar.pointToData(t,e)["radius"===this.dim?0:1]},n.prototype.calculateCategoryInterval=function(){var t=this,e=t.getLabelModel(),n=t.scale,i=n.getExtent(),o=n.count();if(i[1]-i[0]<1)return 0;var r=i[0],a=t.dataToCoord(r+1)-t.dataToCoord(r),s=Math.abs(a),l=Ki(null==r?"":r+"",e.getFont(),"center","top"),u=Math.max(l.height,7)/s;isNaN(u)&&(u=1/0);var d=Math.max(0,Math.floor(u)),c=Ry(t.model),h=c.lastAutoInterval,p=c.lastTickCount;return null!=h&&null!=p&&Math.abs(h-d)<=1&&Math.abs(p-o)<=1&&h>d?d=h:(c.lastTickCount=o,c.lastAutoInterval=d),d},n}(re);Vy.prototype.dataToAngle=re.prototype.dataToCoord,Vy.prototype.angleToData=re.prototype.coordToData;var Oy=["radius","angle"],Ey=function(){function t(t){this.dimensions=Oy,this.type="polar",this.cx=0,this.cy=0,this._radiusAxis=new Ny,this._angleAxis=new Vy,this.axisPointerEnabled=!0,this.name=t||"",this._radiusAxis.polar=this._angleAxis.polar=this}return t.prototype.containPoint=function(t){var e=this.pointToCoord(t);return this._radiusAxis.contain(e[0])&&this._angleAxis.contain(e[1])},t.prototype.containData=function(t){return this._radiusAxis.containData(t[0])&&this._angleAxis.containData(t[1])},t.prototype.getAxis=function(t){return this["_"+t+"Axis"]},t.prototype.getAxes=function(){return[this._radiusAxis,this._angleAxis]},t.prototype.getAxesByScale=function(t){var e=[],n=this._angleAxis,i=this._radiusAxis;return n.scale.type===t&&e.push(n),i.scale.type===t&&e.push(i),e},t.prototype.getAngleAxis=function(){return this._angleAxis},t.prototype.getRadiusAxis=function(){return this._radiusAxis},t.prototype.getOtherAxis=function(t){var e=this._angleAxis;return t===e?this._radiusAxis:e},t.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAngleAxis()},t.prototype.getTooltipAxes=function(t){var e=null!=t&&"auto"!==t?this.getAxis(t):this.getBaseAxis();return{baseAxes:[e],otherAxes:[this.getOtherAxis(e)]}},t.prototype.dataToPoint=function(t,e){return this.coordToPoint([this._radiusAxis.dataToRadius(t[0],e),this._angleAxis.dataToAngle(t[1],e)])},t.prototype.pointToData=function(t,e){var n=this.pointToCoord(t);return[this._radiusAxis.radiusToData(n[0],e),this._angleAxis.angleToData(n[1],e)]},t.prototype.pointToCoord=function(t){var e=t[0]-this.cx,n=t[1]-this.cy,i=this.getAngleAxis(),o=i.getExtent(),r=Math.min(o[0],o[1]),a=Math.max(o[0],o[1]);i.inverse?r=a-360:a=r+360;var s=Math.sqrt(e*e+n*n);e/=s,n/=s;for(var l=Math.atan2(-n,e)/Math.PI*180,u=l<r?1:-1;l<r||l>a;)l+=360*u;return[s,l]},t.prototype.coordToPoint=function(t){var e=t[0],n=t[1]/180*Math.PI;return[Math.cos(n)*e+this.cx,-Math.sin(n)*e+this.cy]},t.prototype.getArea=function(){var t=this.getAngleAxis(),e=this.getRadiusAxis().getExtent().slice();e[0]>e[1]&&e.reverse();var n=t.getExtent(),i=Math.PI/180,o=1e-4;return{cx:this.cx,cy:this.cy,r0:e[0],r:e[1],startAngle:-n[0]*i,endAngle:-n[1]*i,clockwise:t.inverse,contain:function(t,e){var n=t-this.cx,i=e-this.cy,r=n*n+i*i,a=this.r,s=this.r0;return a!==s&&r-o<=a*a&&r+o>=s*s}}},t.prototype.convertToPixel=function(t,e,n){return zy(e)===this?this.dataToPoint(n):null},t.prototype.convertFromPixel=function(t,e,n){return zy(e)===this?this.pointToData(n):null},t}();function zy(t){var e=t.seriesModel,n=t.polarModel;return n&&n.coordinateSystem||e&&e.coordinateSystem}function By(t,e){var n=this,i=n.getAngleAxis(),o=n.getRadiusAxis();if(i.scale.setExtent(1/0,-1/0),o.scale.setExtent(1/0,-1/0),t.eachSeries((function(t){if(t.coordinateSystem===n){var e=t.getData();a(Ji(e,"radius"),(function(t){o.scale.unionExtentFromData(e,t)})),a(Ji(e,"angle"),(function(t){i.scale.unionExtentFromData(e,t)}))}})),ti(i.scale,i.model),ti(o.scale,o.model),"category"===i.type&&!i.onBand){var r=i.getExtent(),s=360/i.scale.count();i.inverse?r[1]+=s:r[1]-=s,i.setExtent(r[0],r[1])}}function Gy(t,e){var n;if(t.type=e.get("type"),t.scale=Qn(e),t.onBand=e.get("boundaryGap")&&"category"===t.type,t.inverse=e.get("inverse"),function(t){return"angleAxis"===t.mainType}(e)){t.inverse=t.inverse!==e.get("clockwise");var i=e.get("startAngle"),o=null!==(n=e.get("endAngle"))&&void 0!==n?n:i+(t.inverse?-360:360);t.setExtent(i,o)}e.axis=t,t.model=e}var Fy={dimensions:Oy,create:function(t,e){var n=[];return t.eachComponent("polar",(function(t,i){var o=new Ey(i+"");o.update=By;var r=o.getRadiusAxis(),a=o.getAngleAxis(),s=t.findAxisModel("radiusAxis"),l=t.findAxisModel("angleAxis");Gy(r,s),Gy(a,l),function(t,e,n){var i=e.get("center"),o=n.getWidth(),r=n.getHeight();t.cx=m(i[0],o),t.cy=m(i[1],r);var a=t.getRadiusAxis(),s=Math.min(o,r)/2,l=e.get("radius");null==l?l=[0,"100%"]:mt(l)||(l=[0,l]);var u=[m(l[0],s),m(l[1],s)];a.inverse?a.setExtent(u[1],u[0]):a.setExtent(u[0],u[1])}(o,t,e),n.push(o),t.coordinateSystem=o,o.model=t})),t.eachSeries((function(t){if("polar"===t.get("coordinateSystem")){var e=t.getReferringComponents("polar",ze).models[0];t.coordinateSystem=e.coordinateSystem}})),n}},Wy=["axisLine","axisLabel","axisTick","minorTick","splitLine","minorSplitLine","splitArea"];function Hy(t,e,n){e[1]>e[0]&&(e=e.slice().reverse());var i=t.coordToPoint([e[0],n]),o=t.coordToPoint([e[1],n]);return{x1:i[0],y1:i[1],x2:o[0],y2:o[1]}}function Zy(t){return t.getRadiusAxis().inverse?0:1}function Yy(t){var e=t[0],n=t[t.length-1];e&&n&&Math.abs(Math.abs(e.coord-n.coord)-360)<1e-4&&t.pop()}var Xy=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.axisPointerClass="PolarAxisPointer",e}return e(n,t),n.prototype.render=function(t,e){if(this.group.removeAll(),t.get("show")){var n=t.axis,i=n.polar,o=i.getRadiusAxis().getExtent(),r=n.getTicksCoords(),s=n.getMinorTicksCoords(),l=d(n.getViewLabels(),(function(t){t=ct(t);var e=n.scale,i="ordinal"===e.type?e.getRawOrdinalNumber(t.tickValue):t.tickValue;return t.coord=n.dataToCoord(i),t}));Yy(l),Yy(r),a(Wy,(function(e){!t.get([e,"show"])||n.scale.isBlank()&&"axisLine"!==e||Uy[e](this.group,t,i,r,s,o,l)}),this)}},n.type="angleAxis",n}($i),Uy={axisLine:function(t,e,n,i,o,r){var a,s=e.getModel(["axisLine","lineStyle"]),l=n.getAngleAxis(),u=Math.PI/180,d=l.getExtent(),c=Zy(n),h=c?0:1,p=360===Math.abs(d[1]-d[0])?"Circle":"Arc";(a=0===r[h]?new Ft[p]({shape:{cx:n.cx,cy:n.cy,r:r[c],startAngle:-d[0]*u,endAngle:-d[1]*u,clockwise:l.inverse},style:s.getLineStyle(),z2:1,silent:!0}):new ne({shape:{cx:n.cx,cy:n.cy,r:r[c],r0:r[h]},style:s.getLineStyle(),z2:1,silent:!0})).style.fill=null,t.add(a)},axisTick:function(t,e,n,i,o,r){var a=e.getModel("axisTick"),s=(a.get("inside")?-1:1)*a.get("length"),l=r[Zy(n)],u=d(i,(function(t){return new ve({shape:Hy(n,[l,l+s],t.coord)})}));t.add(ie(u,{style:Ot(a.getModel("lineStyle").getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])})}))},minorTick:function(t,e,n,i,o,r){if(o.length){for(var a=e.getModel("axisTick"),s=e.getModel("minorTick"),l=(a.get("inside")?-1:1)*s.get("length"),u=r[Zy(n)],d=[],c=0;c<o.length;c++)for(var h=0;h<o[c].length;h++)d.push(new ve({shape:Hy(n,[u,u+l],o[c][h].coord)}));t.add(ie(d,{style:Ot(s.getModel("lineStyle").getLineStyle(),Ot(a.getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])}))}))}},axisLabel:function(t,e,n,i,o,r,s){var l=e.getCategories(!0),u=e.getModel("axisLabel"),d=u.get("margin"),c=e.get("triggerEvent");a(s,(function(i,o){var a=u,s=i.tickValue,h=r[Zy(n)],p=n.coordToPoint([h+d,i.coord]),g=n.cx,f=n.cy,y=Math.abs(p[0]-g)/h<.3?"center":p[0]>g?"left":"right",m=Math.abs(p[1]-f)/h<.3?"middle":p[1]>f?"top":"bottom";if(l&&l[s]){var x=l[s];xt(x)&&x.textStyle&&(a=new qt(x.textStyle,u,u.ecModel))}var _=new an({silent:te.isLabelSilent(e),style:sn(a,{x:p[0],y:p[1],fill:a.getTextColor()||e.get(["axisLine","lineStyle","color"]),text:i.formattedLabel,align:y,verticalAlign:m})});if(t.add(_),c){var b=te.makeAxisEventDataBase(e);b.targetType="axisLabel",b.value=i.rawLabel,v(_).eventData=b}}),this)},splitLine:function(t,e,n,i,o,r){var a=e.getModel("splitLine").getModel("lineStyle"),s=a.get("color"),l=0;s=s instanceof Array?s:[s];for(var u=[],d=0;d<i.length;d++){var c=l++%s.length;u[c]=u[c]||[],u[c].push(new ve({shape:Hy(n,r,i[d].coord)}))}for(d=0;d<u.length;d++)t.add(ie(u[d],{style:Ot({stroke:s[d%s.length]},a.getLineStyle()),silent:!0,z:e.get("z")}))},minorSplitLine:function(t,e,n,i,o,r){if(o.length){for(var a=e.getModel("minorSplitLine").getModel("lineStyle"),s=[],l=0;l<o.length;l++)for(var u=0;u<o[l].length;u++)s.push(new ve({shape:Hy(n,r,o[l][u].coord)}));t.add(ie(s,{style:a.getLineStyle(),silent:!0,z:e.get("z")}))}},splitArea:function(t,e,n,i,o,r){if(i.length){var a=e.getModel("splitArea").getModel("areaStyle"),s=a.get("color"),l=0;s=s instanceof Array?s:[s];for(var u=[],d=Math.PI/180,c=-i[0].coord*d,h=Math.min(r[0],r[1]),p=Math.max(r[0],r[1]),g=e.get("clockwise"),f=1,y=i.length;f<=y;f++){var v=f===y?i[0].coord:i[f].coord,m=l++%s.length;u[m]=u[m]||[],u[m].push(new Zn({shape:{cx:n.cx,cy:n.cy,r0:h,r:p,startAngle:c,endAngle:-v*d,clockwise:g},silent:!0})),c=-v*d}for(f=0;f<u.length;f++)t.add(ie(u[f],{style:Ot({fill:s[f%s.length]},a.getAreaStyle()),silent:!0}))}}},jy=["axisLine","axisTickLabel","axisName"],qy=["splitLine","splitArea","minorSplitLine"],Ky=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.axisPointerClass="PolarAxisPointer",e}return e(n,t),n.prototype.render=function(t,e){if(this.group.removeAll(),t.get("show")){var n=this._axisGroup,i=this._axisGroup=new It;this.group.add(i);var o=t.axis,r=o.polar,s=r.getAngleAxis(),l=o.getTicksCoords(),u=o.getMinorTicksCoords(),d=s.getExtent()[0],c=o.getExtent(),h=function(t,e,n){return{position:[t.cx,t.cy],rotation:n/180*Math.PI,labelDirection:-1,tickDirection:-1,nameDirection:1,labelRotate:e.getModel("axisLabel").get("rotate"),z2:1}}(r,t,d),p=new te(t,h);a(jy,p.add,p),i.add(p.getGroup()),ai(n,i,t),a(qy,(function(e){t.get([e,"show"])&&!o.scale.isBlank()&&Jy[e](this.group,t,r,d,c,l,u)}),this)}},n.type="radiusAxis",n}($i),Jy={splitLine:function(t,e,n,i,o,r){var a=e.getModel("splitLine").getModel("lineStyle"),s=a.get("color"),l=0,u=n.getAngleAxis(),d=Math.PI/180,c=u.getExtent(),h=360===Math.abs(c[1]-c[0])?"Circle":"Arc";s=s instanceof Array?s:[s];for(var p=[],g=0;g<r.length;g++){var f=l++%s.length;p[f]=p[f]||[],p[f].push(new Ft[h]({shape:{cx:n.cx,cy:n.cy,r:Math.max(r[g].coord,0),startAngle:-c[0]*d,endAngle:-c[1]*d,clockwise:u.inverse}}))}for(g=0;g<p.length;g++)t.add(ie(p[g],{style:Ot({stroke:s[g%s.length],fill:null},a.getLineStyle()),silent:!0}))},minorSplitLine:function(t,e,n,i,o,r,a){if(a.length){for(var s=e.getModel("minorSplitLine").getModel("lineStyle"),l=[],u=0;u<a.length;u++)for(var d=0;d<a[u].length;d++)l.push(new ee({shape:{cx:n.cx,cy:n.cy,r:a[u][d].coord}}));t.add(ie(l,{style:Ot({fill:null},s.getLineStyle()),silent:!0}))}},splitArea:function(t,e,n,i,o,r){if(r.length){var a=e.getModel("splitArea").getModel("areaStyle"),s=a.get("color"),l=0;s=s instanceof Array?s:[s];for(var u=[],d=r[0].coord,c=1;c<r.length;c++){var h=l++%s.length;u[h]=u[h]||[],u[h].push(new Zn({shape:{cx:n.cx,cy:n.cy,r0:d,r:r[c].coord,startAngle:0,endAngle:2*Math.PI},silent:!0})),d=r[c].coord}for(c=0;c<u.length;c++)t.add(ie(u[c],{style:Ot({fill:s[c%s.length]},a.getAreaStyle()),silent:!0}))}}};function $y(t){return t.get("stack")||"__ec_stack_"+t.seriesIndex}function Qy(t,e){return e.dim+t.model.componentIndex}function tv(t,e,n){var i={},o=function(t){var e={};a(t,(function(t,n){var i=t.getData(),o=t.coordinateSystem,r=o.getBaseAxis(),a=Qy(o,r),s=r.getExtent(),l="category"===r.type?r.getBandWidth():Math.abs(s[1]-s[0])/i.count(),u=e[a]||{bandWidth:l,remainedWidth:l,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},d=u.stacks;e[a]=u;var c=$y(t);d[c]||u.autoWidthCount++,d[c]=d[c]||{width:0,maxWidth:0};var h=m(t.get("barWidth"),l),p=m(t.get("barMaxWidth"),l),g=t.get("barGap"),f=t.get("barCategoryGap");h&&!d[c].width&&(h=Math.min(u.remainedWidth,h),d[c].width=h,u.remainedWidth-=h),p&&(d[c].maxWidth=p),null!=g&&(u.gap=g),null!=f&&(u.categoryGap=f)}));var n={};return a(e,(function(t,e){n[e]={};var i=t.stacks,o=t.bandWidth,r=m(t.categoryGap,o),s=m(t.gap,1),l=t.remainedWidth,u=t.autoWidthCount,d=(l-r)/(u+(u-1)*s);d=Math.max(d,0),a(i,(function(t,e){var n=t.maxWidth;n&&n<d&&(n=Math.min(n,l),t.width&&(n=Math.min(n,t.width)),l-=n,t.width=n,u--)})),d=(l-r)/(u+(u-1)*s),d=Math.max(d,0);var c,h=0;a(i,(function(t,e){t.width||(t.width=d),c=t,h+=t.width*(1+s)})),c&&(h-=c.width*s);var p=-h/2;a(i,(function(t,i){n[e][i]=n[e][i]||{offset:p,width:t.width},p+=t.width*(1+s)}))})),n}(c(e.getSeriesByType(t),(function(t){return!e.isSeriesFiltered(t)&&t.coordinateSystem&&"polar"===t.coordinateSystem.type})));e.eachSeriesByType(t,(function(t){if("polar"===t.coordinateSystem.type){var e=t.getData(),n=t.coordinateSystem,r=n.getBaseAxis(),a=Qy(n,r),s=$y(t),l=o[a][s],u=l.offset,d=l.width,c=n.getOtherAxis(r),h=t.coordinateSystem.cx,p=t.coordinateSystem.cy,g=t.get("barMinHeight")||0,f=t.get("barMinAngle")||0;i[s]=i[s]||[];for(var y=e.mapDimension(c.dim),v=e.mapDimension(r.dim),m=Qi(e,y),x="radius"!==r.dim||!t.get("roundCap",!0),_=c.model.get("startValue"),b=c.dataToCoord(_||0),S=0,w=e.count();S<w;S++){var M=e.get(y,S),I=e.get(v,S),A=M>=0?"p":"n",T=b;m&&(i[s][I]||(i[s][I]={p:b,n:b}),T=i[s][I][A]);var C=void 0,D=void 0,L=void 0,P=void 0;if("radius"===c.dim){var k=c.dataToCoord(M)-b,N=r.dataToCoord(I);Math.abs(k)<g&&(k=(k<0?-1:1)*g),C=T,D=T+k,P=(L=N-u)-d,m&&(i[s][I][A]=D)}else{var R=c.dataToCoord(M,x)-b,V=r.dataToCoord(I);Math.abs(R)<f&&(R=(R<0?-1:1)*f),D=(C=V+u)+d,L=T,P=T+R,m&&(i[s][I][A]=P)}e.setItemLayout(S,{cx:h,cy:p,r0:C,r:D,startAngle:-L*Math.PI/180,endAngle:-P*Math.PI/180,clockwise:L>=P})}}}))}var ev={startAngle:90,clockwise:!0,splitNumber:12,axisLabel:{rotate:0}},nv={splitNumber:5},iv=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.type="polar",n}(oe);function ov(t,e){e=e||{};var n=t.coordinateSystem,i=t.axis,o={},r=i.position,a=i.orient,s=n.getRect(),l=[s.x,s.x+s.width,s.y,s.y+s.height],u={horizontal:{top:l[2],bottom:l[3]},vertical:{left:l[0],right:l[1]}};o.position=["vertical"===a?u.vertical[r]:l[0],"horizontal"===a?u.horizontal[r]:l[3]];o.rotation=Math.PI/2*{horizontal:0,vertical:1}[a];o.labelDirection=o.tickDirection=o.nameDirection={top:-1,bottom:1,right:1,left:-1}[r],t.get(["axisTick","inside"])&&(o.tickDirection=-o.tickDirection),Ge(e.labelInside,t.get(["axisLabel","inside"]))&&(o.labelDirection=-o.labelDirection);var d=e.rotate;return null==d&&(d=t.get(["axisLabel","rotate"])),o.labelRotation="top"===r?-d:d,o.z2=1,o}var rv=["axisLine","axisTickLabel","axisName"],av=["splitArea","splitLine"],sv=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.axisPointerClass="SingleAxisPointer",e}return e(n,t),n.prototype.render=function(e,n,i,o){var r=this.group;r.removeAll();var s=this._axisGroup;this._axisGroup=new It;var l=ov(e),u=new te(e,l);a(rv,u.add,u),r.add(this._axisGroup),r.add(u.getGroup()),a(av,(function(t){e.get([t,"show"])&&lv[t](this,this.group,this._axisGroup,e)}),this),ai(s,this._axisGroup,e),t.prototype.render.call(this,e,n,i,o)},n.prototype.remove=function(){eo(this)},n.type="singleAxis",n}($i),lv={splitLine:function(t,e,n,i){var o=i.axis;if(!o.scale.isBlank()){var r=i.getModel("splitLine"),a=r.getModel("lineStyle"),s=a.get("color");s=s instanceof Array?s:[s];for(var l=a.get("width"),u=i.coordinateSystem.getRect(),d=o.isHorizontal(),c=[],h=0,p=o.getTicksCoords({tickModel:r}),g=[],f=[],y=0;y<p.length;++y){var v=o.toGlobalCoord(p[y].coord);d?(g[0]=v,g[1]=u.y,f[0]=v,f[1]=u.y+u.height):(g[0]=u.x,g[1]=v,f[0]=u.x+u.width,f[1]=v);var m=new ve({shape:{x1:g[0],y1:g[1],x2:f[0],y2:f[1]},silent:!0});io(m.shape,l);var x=h++%s.length;c[x]=c[x]||[],c[x].push(m)}var _=a.getLineStyle(["color"]);for(y=0;y<c.length;++y)e.add(ie(c[y],{style:Ot({stroke:s[y%s.length]},_),silent:!0}))}},splitArea:function(t,e,n,i){no(t,n,i,i)}},uv=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.getCoordSysModel=function(){return this},n.type="singleAxis",n.layoutMode="box",n.defaultOption={left:"5%",top:"5%",right:"5%",bottom:"5%",type:"value",position:"bottom",orient:"horizontal",axisLine:{show:!0,lineStyle:{width:1,type:"solid"}},tooltip:{show:!0},axisTick:{show:!0,length:6,lineStyle:{width:1}},axisLabel:{show:!0,interval:"auto"},splitLine:{show:!0,lineStyle:{type:"dashed",opacity:.2}}},n}(Qt);Kt(uv,Jt.prototype);var dv=function(t){function n(e,n,i,o,r){var a=t.call(this,e,n,i)||this;return a.type=o||"value",a.position=r||"bottom",a}return e(n,t),n.prototype.isHorizontal=function(){var t=this.position;return"top"===t||"bottom"===t},n.prototype.pointToData=function(t,e){return this.coordinateSystem.pointToData(t)[0]},n}(re),cv=["single"],hv=function(){function t(t,e,n){this.type="single",this.dimension="single",this.dimensions=cv,this.axisPointerEnabled=!0,this.model=t,this._init(t,e,n)}return t.prototype._init=function(t,e,n){var i=this.dimension,o=new dv(i,Qn(t),[0,0],t.get("type"),t.get("position")),r="category"===o.type;o.onBand=r&&t.get("boundaryGap"),o.inverse=t.get("inverse"),o.orient=t.get("orient"),t.axis=o,o.model=t,o.coordinateSystem=this,this._axis=o},t.prototype.update=function(t,e){t.eachSeries((function(t){if(t.coordinateSystem===this){var e=t.getData();a(e.mapDimensionsAll(this.dimension),(function(t){this._axis.scale.unionExtentFromData(e,t)}),this),ti(this._axis.scale,this._axis.model)}}),this)},t.prototype.resize=function(t,e){this._rect=Fe({left:t.get("left"),top:t.get("top"),right:t.get("right"),bottom:t.get("bottom"),width:t.get("width"),height:t.get("height")},{width:e.getWidth(),height:e.getHeight()}),this._adjustAxis()},t.prototype.getRect=function(){return this._rect},t.prototype._adjustAxis=function(){var t=this._rect,e=this._axis,n=e.isHorizontal(),i=n?[0,t.width]:[0,t.height],o=e.inverse?1:0;e.setExtent(i[o],i[1-o]),this._updateAxisTransform(e,n?t.x:t.y)},t.prototype._updateAxisTransform=function(t,e){var n=t.getExtent(),i=n[0]+n[1],o=t.isHorizontal();t.toGlobalCoord=o?function(t){return t+e}:function(t){return i-t+e},t.toLocalCoord=o?function(t){return t-e}:function(t){return i-t+e}},t.prototype.getAxis=function(){return this._axis},t.prototype.getBaseAxis=function(){return this._axis},t.prototype.getAxes=function(){return[this._axis]},t.prototype.getTooltipAxes=function(){return{baseAxes:[this.getAxis()],otherAxes:[]}},t.prototype.containPoint=function(t){var e=this.getRect(),n=this.getAxis();return"horizontal"===n.orient?n.contain(n.toLocalCoord(t[0]))&&t[1]>=e.y&&t[1]<=e.y+e.height:n.contain(n.toLocalCoord(t[1]))&&t[0]>=e.y&&t[0]<=e.y+e.height},t.prototype.pointToData=function(t){var e=this.getAxis();return[e.coordToData(e.toLocalCoord(t["horizontal"===e.orient?0:1]))]},t.prototype.dataToPoint=function(t){var e=this.getAxis(),n=this.getRect(),i=[],o="horizontal"===e.orient?0:1;return t instanceof Array&&(t=t[0]),i[o]=e.toGlobalCoord(e.dataToCoord(+t)),i[1-o]=0===o?n.y+n.height/2:n.x+n.width/2,i},t.prototype.convertToPixel=function(t,e,n){return pv(e)===this?this.dataToPoint(n):null},t.prototype.convertFromPixel=function(t,e,n){return pv(e)===this?this.pointToData(n):null},t}();function pv(t){var e=t.seriesModel,n=t.singleAxisModel;return n&&n.coordinateSystem||e&&e.coordinateSystem}var gv={create:function(t,e){var n=[];return t.eachComponent("singleAxis",(function(i,o){var r=new hv(i,t,e);r.name="single_"+o,r.resize(i,e),i.coordinateSystem=r,n.push(r)})),t.eachSeries((function(t){if("singleAxis"===t.get("coordinateSystem")){var e=t.getReferringComponents("singleAxis",ze).models[0];t.coordinateSystem=e&&e.coordinateSystem}})),n},dimensions:cv},fv=["x","y"],yv=["width","height"],vv=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return e(n,t),n.prototype.makeElOption=function(t,e,n,i,o){var r=n.axis,a=r.coordinateSystem,s=_v(a,1-xv(r)),l=a.dataToPoint(e)[0],u=i.get("type");if(u&&"none"!==u){var d=Yi(i),c=mv[u](r,l,s);c.style=d,t.graphicKey=c.type,t.pointer=c}var h=ov(n);oo(e,t,h,n,i,o)},n.prototype.getHandleTransform=function(t,e,n){var i=ov(e,{labelInside:!1});i.labelMargin=n.get(["handle","margin"]);var o=ro(e.axis,t,i);return{x:o[0],y:o[1],rotation:i.rotation+(i.labelDirection<0?Math.PI:0)}},n.prototype.updateHandleTransform=function(t,e,n,i){var o=n.axis,r=o.coordinateSystem,a=xv(o),s=_v(r,a),l=[t.x,t.y];l[a]+=e[a],l[a]=Math.min(s[1],l[a]),l[a]=Math.max(s[0],l[a]);var u=_v(r,1-a),d=(u[1]+u[0])/2,c=[d,d];return c[a]=l[a],{x:l[0],y:l[1],rotation:t.rotation,cursorPoint:c,tooltipOption:{verticalAlign:"middle"}}},n}(Ui),mv={line:function(t,e,n){return{type:"Line",subPixelOptimize:!0,shape:qi([e,n[0]],[e,n[1]],xv(t))}},shadow:function(t,e,n){var i=t.getBandWidth(),o=n[1]-n[0];return{type:"Rect",shape:ao([e-i/2,n[0]],[i,o],xv(t))}}};function xv(t){return t.isHorizontal()?0:1}function _v(t,e){var n=t.getRect();return[n[fv[e]],n[fv[e]]+n[yv[e]]]}var bv=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.type="single",n}(oe);var Sv=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.init=function(e,n,i){var o=so(e);t.prototype.init.apply(this,arguments),wv(e,o)},n.prototype.mergeOption=function(e){t.prototype.mergeOption.apply(this,arguments),wv(this.option,e)},n.prototype.getCellSize=function(){return this.option.cellSize},n.type="calendar",n.defaultOption={z:2,left:80,top:60,cellSize:20,orient:"horizontal",splitLine:{show:!0,lineStyle:{color:"#000",width:1,type:"solid"}},itemStyle:{color:"#fff",borderWidth:1,borderColor:"#ccc"},dayLabel:{show:!0,firstDay:0,position:"start",margin:"50%",color:"#000"},monthLabel:{show:!0,position:"start",margin:5,align:"center",formatter:null,color:"#000"},yearLabel:{show:!0,position:null,margin:30,formatter:null,color:"#ccc",fontFamily:"sans-serif",fontWeight:"bolder",fontSize:20}},n}(Qt);function wv(t,e){var n,i=t.cellSize;1===(n=mt(i)?i:t.cellSize=[i,i]).length&&(n[1]=n[0]);var o=d([0,1],(function(t){return lo(e,t)&&(n[t]="auto"),null!=n[t]&&"auto"!==n[t]}));uo(t,e,{type:"box",ignoreSize:o})}var Mv=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.render=function(t,e,n){var i=this.group;i.removeAll();var o=t.coordinateSystem,r=o.getRangeInfo(),a=o.getOrient(),s=e.getLocaleModel();this._renderDayRect(t,r,i),this._renderLines(t,r,a,i),this._renderYearText(t,r,a,i),this._renderMonthText(t,s,a,i),this._renderWeekText(t,s,r,a,i)},n.prototype._renderDayRect=function(t,e,n){for(var i=t.coordinateSystem,o=t.getModel("itemStyle").getItemStyle(),r=i.getCellWidth(),a=i.getCellHeight(),s=e.start.time;s<=e.end.time;s=i.getNextNDay(s,1).time){var l=i.dataToRect([s],!1).tl,u=new pe({shape:{x:l[0],y:l[1],width:r,height:a},cursor:"default",style:o});n.add(u)}},n.prototype._renderLines=function(t,e,n,i){var o=this,r=t.coordinateSystem,a=t.getModel(["splitLine","lineStyle"]).getLineStyle(),s=t.get(["splitLine","show"]),l=a.lineWidth;this._tlpoints=[],this._blpoints=[],this._firstDayOfMonth=[],this._firstDayPoints=[];for(var u=e.start,d=0;u.time<=e.end.time;d++){h(u.formatedDate),0===d&&(u=r.getDateInfo(e.start.y+"-"+e.start.m));var c=u.date;c.setMonth(c.getMonth()+1),u=r.getDateInfo(c)}function h(e){o._firstDayOfMonth.push(r.getDateInfo(e)),o._firstDayPoints.push(r.dataToRect([e],!1).tl);var l=o._getLinePointsOfOneWeek(t,e,n);o._tlpoints.push(l[0]),o._blpoints.push(l[l.length-1]),s&&o._drawSplitline(l,a,i)}h(r.getNextNDay(e.end.time,1).formatedDate),s&&this._drawSplitline(o._getEdgesPoints(o._tlpoints,l,n),a,i),s&&this._drawSplitline(o._getEdgesPoints(o._blpoints,l,n),a,i)},n.prototype._getEdgesPoints=function(t,e,n){var i=[t[0].slice(),t[t.length-1].slice()],o="horizontal"===n?0:1;return i[0][o]=i[0][o]-e/2,i[1][o]=i[1][o]+e/2,i},n.prototype._drawSplitline=function(t,e,n){var i=new Rt({z2:20,shape:{points:t},style:e});n.add(i)},n.prototype._getLinePointsOfOneWeek=function(t,e,n){for(var i=t.coordinateSystem,o=i.getDateInfo(e),r=[],a=0;a<7;a++){var s=i.getNextNDay(o.time,a),l=i.dataToRect([s.time],!1);r[2*s.day]=l.tl,r[2*s.day+1]=l["horizontal"===n?"bl":"tr"]}return r},n.prototype._formatterLabel=function(t,e){return G(t)&&t?co(t,e):f(t)?t(e):e.nameMap},n.prototype._yearTextPositionControl=function(t,e,n,i,o){var r=e[0],a=e[1],s=["center","bottom"];"bottom"===i?(a+=o,s=["center","top"]):"left"===i?r-=o:"right"===i?(r+=o,s=["center","top"]):a-=o;var l=0;return"left"!==i&&"right"!==i||(l=Math.PI/2),{rotation:l,x:r,y:a,style:{align:s[0],verticalAlign:s[1]}}},n.prototype._renderYearText=function(t,e,n,i){var o=t.getModel("yearLabel");if(o.get("show")){var r=o.get("margin"),a=o.get("position");a||(a="horizontal"!==n?"top":"left");var s=[this._tlpoints[this._tlpoints.length-1],this._blpoints[0]],l=(s[0][0]+s[1][0])/2,u=(s[0][1]+s[1][1])/2,d="horizontal"===n?0:1,c={top:[l,s[d][1]],bottom:[l,s[1-d][1]],left:[s[1-d][0],u],right:[s[d][0],u]},h=e.start.y;+e.end.y>+e.start.y&&(h=h+"-"+e.end.y);var p=o.get("formatter"),g={start:e.start.y,end:e.end.y,nameMap:h},f=this._formatterLabel(p,g),y=new an({z2:30,style:sn(o,{text:f}),silent:o.get("silent")});y.attr(this._yearTextPositionControl(y,c[a],n,a,r)),i.add(y)}},n.prototype._monthTextPositionControl=function(t,e,n,i,o){var r="left",a="top",s=t[0],l=t[1];return"horizontal"===n?(l+=o,e&&(r="center"),"start"===i&&(a="bottom")):(s+=o,e&&(a="middle"),"start"===i&&(r="right")),{x:s,y:l,align:r,verticalAlign:a}},n.prototype._renderMonthText=function(t,e,n,i){var o=t.getModel("monthLabel");if(o.get("show")){var r=o.get("nameMap"),a=o.get("margin"),s=o.get("position"),l=o.get("align"),u=[this._tlpoints,this._blpoints];r&&!G(r)||(r&&(e=ho(r)||e),r=e.get(["time","monthAbbr"])||[]);var d="start"===s?0:1,c="horizontal"===n?0:1;a="start"===s?-a:a;for(var h="center"===l,p=o.get("silent"),g=0;g<u[d].length-1;g++){var f=u[d][g].slice(),y=this._firstDayOfMonth[g];if(h){var v=this._firstDayPoints[g];f[c]=(v[c]+u[0][g+1][c])/2}var m=o.get("formatter"),x=r[+y.m-1],_={yyyy:y.y,yy:(y.y+"").slice(2),MM:y.m,M:+y.m,nameMap:x},b=this._formatterLabel(m,_),S=new an({z2:30,style:H(sn(o,{text:b}),this._monthTextPositionControl(f,h,n,s,a)),silent:p});i.add(S)}}},n.prototype._weekTextPositionControl=function(t,e,n,i,o){var r="center",a="middle",s=t[0],l=t[1],u="start"===n;return"horizontal"===e?(s=s+i+(u?1:-1)*o[0]/2,r=u?"right":"left"):(l=l+i+(u?1:-1)*o[1]/2,a=u?"bottom":"top"),{x:s,y:l,align:r,verticalAlign:a}},n.prototype._renderWeekText=function(t,e,n,i,o){var r=t.getModel("dayLabel");if(r.get("show")){var a=t.coordinateSystem,s=r.get("position"),l=r.get("nameMap"),u=r.get("margin"),c=a.getFirstDayOfWeek();if(!l||G(l))l&&(e=ho(l)||e),l=e.get(["time","dayOfWeekShort"])||d(e.get(["time","dayOfWeekAbbr"]),(function(t){return t[0]}));var h=a.getNextNDay(n.end.time,7-n.lweek).time,p=[a.getCellWidth(),a.getCellHeight()];u=m(u,Math.min(p[1],p[0])),"start"===s&&(h=a.getNextNDay(n.start.time,-(7+n.fweek)).time,u=-u);for(var g=r.get("silent"),f=0;f<7;f++){var y,v=a.getNextNDay(h,f),x=a.dataToRect([v.time],!1).center;y=Math.abs((f+c)%7);var _=new an({z2:30,style:H(sn(r,{text:l[y]}),this._weekTextPositionControl(x,i,s,u,p)),silent:g});o.add(_)}}},n.type="calendar",n}(oe),Iv=864e5,Av=function(){function t(e,n,i){this.type="calendar",this.dimensions=t.dimensions,this.getDimensionsInfo=t.getDimensionsInfo,this._model=e}return t.getDimensionsInfo=function(){return[{name:"time",type:"time"},"value"]},t.prototype.getRangeInfo=function(){return this._rangeInfo},t.prototype.getModel=function(){return this._model},t.prototype.getRect=function(){return this._rect},t.prototype.getCellWidth=function(){return this._sw},t.prototype.getCellHeight=function(){return this._sh},t.prototype.getOrient=function(){return this._orient},t.prototype.getFirstDayOfWeek=function(){return this._firstDayOfWeek},t.prototype.getDateInfo=function(t){var e=(t=po(t)).getFullYear(),n=t.getMonth()+1,i=n<10?"0"+n:""+n,o=t.getDate(),r=o<10?"0"+o:""+o,a=t.getDay();return{y:e+"",m:i,d:r,day:a=Math.abs((a+7-this.getFirstDayOfWeek())%7),time:t.getTime(),formatedDate:e+"-"+i+"-"+r,date:t}},t.prototype.getNextNDay=function(t,e){return 0===(e=e||0)||(t=new Date(this.getDateInfo(t).time)).setDate(t.getDate()+e),this.getDateInfo(t)},t.prototype.update=function(t,e){this._firstDayOfWeek=+this._model.getModel("dayLabel").get("firstDay"),this._orient=this._model.get("orient"),this._lineWidth=this._model.getModel("itemStyle").getItemStyle().lineWidth||0,this._rangeInfo=this._getRangeInfo(this._initRangeOption());var n=this._rangeInfo.weeks||1,i=["width","height"],o=this._model.getCellSize().slice(),r=this._model.getBoxLayoutParams(),s="horizontal"===this._orient?[n,7]:[7,n];a([0,1],(function(t){d(o,t)&&(r[i[t]]=o[t]*s[t])}));var l={width:e.getWidth(),height:e.getHeight()},u=this._rect=Fe(r,l);function d(t,e){return null!=t[e]&&"auto"!==t[e]}a([0,1],(function(t){d(o,t)||(o[t]=u[i[t]]/s[t])})),this._sw=o[0],this._sh=o[1]},t.prototype.dataToPoint=function(t,e){mt(t)&&(t=t[0]),null==e&&(e=!0);var n=this.getDateInfo(t),i=this._rangeInfo,o=n.formatedDate;if(e&&!(n.time>=i.start.time&&n.time<i.end.time+Iv))return[NaN,NaN];var r=n.day,a=this._getRangeInfo([i.start.time,o]).nthWeek;return"vertical"===this._orient?[this._rect.x+r*this._sw+this._sw/2,this._rect.y+a*this._sh+this._sh/2]:[this._rect.x+a*this._sw+this._sw/2,this._rect.y+r*this._sh+this._sh/2]},t.prototype.pointToData=function(t){var e=this.pointToDate(t);return e&&e.time},t.prototype.dataToRect=function(t,e){var n=this.dataToPoint(t,e);return{contentShape:{x:n[0]-(this._sw-this._lineWidth)/2,y:n[1]-(this._sh-this._lineWidth)/2,width:this._sw-this._lineWidth,height:this._sh-this._lineWidth},center:n,tl:[n[0]-this._sw/2,n[1]-this._sh/2],tr:[n[0]+this._sw/2,n[1]-this._sh/2],br:[n[0]+this._sw/2,n[1]+this._sh/2],bl:[n[0]-this._sw/2,n[1]+this._sh/2]}},t.prototype.pointToDate=function(t){var e=Math.floor((t[0]-this._rect.x)/this._sw)+1,n=Math.floor((t[1]-this._rect.y)/this._sh)+1,i=this._rangeInfo.range;return"vertical"===this._orient?this._getDateByWeeksAndDay(n,e-1,i):this._getDateByWeeksAndDay(e,n-1,i)},t.prototype.convertToPixel=function(t,e,n){var i=Tv(e);return i===this?i.dataToPoint(n):null},t.prototype.convertFromPixel=function(t,e,n){var i=Tv(e);return i===this?i.pointToData(n):null},t.prototype.containPoint=function(t){return!1},t.prototype._initRangeOption=function(){var t,e=this._model.get("range");if(mt(e)&&1===e.length&&(e=e[0]),mt(e))t=e;else{var n=e.toString();if(/^\d{4}$/.test(n)&&(t=[n+"-01-01",n+"-12-31"]),/^\d{4}[\/|-]\d{1,2}$/.test(n)){var i=this.getDateInfo(n),o=i.date;o.setMonth(o.getMonth()+1);var r=this.getNextNDay(o,-1);t=[i.formatedDate,r.formatedDate]}/^\d{4}[\/|-]\d{1,2}[\/|-]\d{1,2}$/.test(n)&&(t=[n,n])}if(!t)return e;var a=this._getRangeInfo(t);return a.start.time>a.end.time&&t.reverse(),t},t.prototype._getRangeInfo=function(t){var e,n=[this.getDateInfo(t[0]),this.getDateInfo(t[1])];n[0].time>n[1].time&&(e=!0,n.reverse());var i=Math.floor(n[1].time/Iv)-Math.floor(n[0].time/Iv)+1,o=new Date(n[0].time),r=o.getDate(),a=n[1].date.getDate();o.setDate(r+i-1);var s=o.getDate();if(s!==a)for(var l=o.getTime()-n[1].time>0?1:-1;(s=o.getDate())!==a&&(o.getTime()-n[1].time)*l>0;)i-=l,o.setDate(s-l);var u=Math.floor((i+n[0].day+6)/7),d=e?1-u:u-1;return e&&n.reverse(),{range:[n[0].formatedDate,n[1].formatedDate],start:n[0],end:n[1],allDay:i,weeks:u,nthWeek:d,fweek:n[0].day,lweek:n[1].day}},t.prototype._getDateByWeeksAndDay=function(t,e,n){var i=this._getRangeInfo(n);if(t>i.weeks||0===t&&e<i.fweek||t===i.weeks&&e>i.lweek)return null;var o=7*(t-1)-i.fweek+e,r=new Date(i.start.time);return r.setDate(+i.start.d+o),this.getDateInfo(r)},t.create=function(e,n){var i=[];return e.eachComponent("calendar",(function(e){var n=new t(e);i.push(n),e.coordinateSystem=n})),e.eachSeries((function(t){"calendar"===t.get("coordinateSystem")&&(t.coordinateSystem=i[t.get("calendarIndex")||0])})),i},t.dimensions=["time","value"],t}();function Tv(t){var e=t.calendarModel,n=t.seriesModel;return e?e.coordinateSystem:n?n.coordinateSystem:null}function Cv(t,e){var n;return a(e,(function(e){null!=t[e]&&"auto"!==t[e]&&(n=!0)})),n}var Dv=["transition","enterFrom","leaveTo"],Lv=Dv.concat(["enterAnimation","updateAnimation","leaveAnimation"]);function Pv(t,e,n){if(n&&(!t[n]&&e[n]&&(t[n]={}),t=t[n],e=e[n]),t&&e)for(var i=n?Dv:Lv,o=0;o<i.length;o++){var r=i[o];null==t[r]&&null!=e[r]&&(t[r]=e[r])}}var kv=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.preventAutoZ=!0,e}return e(n,t),n.prototype.mergeOption=function(e,n){var i=this.option.elements;this.option.elements=null,t.prototype.mergeOption.call(this,e,n),this.option.elements=i},n.prototype.optionUpdated=function(t,e){var n=this.option,i=(e?n:t).elements,o=n.elements=e?[]:n.elements,r=[];this._flatten(i,r,null);var s=go(o,r,"normalMerge"),l=this._elOptionsToUpdate=[];a(s,(function(t,e){var n=t.newOption;n&&(l.push(n),function(t,e){var n=t.existing;if(e.id=t.keyInfo.id,!e.type&&n&&(e.type=n.type),null==e.parentId){var i=e.parentOption;i?e.parentId=i.id:n&&(e.parentId=n.parentId)}e.parentOption=null}(t,n),function(t,e,n){var i=H({},n),o=t[e],r=n.$action||"merge";"merge"===r?o?(jt(o,i,!0),uo(o,i,{ignoreSize:!0}),fo(n,o),Pv(n,o),Pv(n,o,"shape"),Pv(n,o,"style"),Pv(n,o,"extra"),n.clipPath=o.clipPath):t[e]=i:"replace"===r?t[e]=i:"remove"===r&&o&&(t[e]=null)}(o,e,n),function(t,e){if(t&&(t.hv=e.hv=[Cv(e,["left","right"]),Cv(e,["top","bottom"])],"group"===t.type)){var n=t,i=e;null==n.width&&(n.width=i.width=0),null==n.height&&(n.height=i.height=0)}}(o[e],n))}),this),n.elements=c(o,(function(t){return t&&delete t.$action,null!=t}))},n.prototype._flatten=function(t,e,n){a(t,(function(t){if(t){n&&(t.parentOption=n),e.push(t);var i=t.children;i&&i.length&&this._flatten(i,e,t),delete t.children}}),this)},n.prototype.useElOptionsToUpdate=function(){var t=this._elOptionsToUpdate;return this._elOptionsToUpdate=null,t},n.type="graphic",n.defaultOption={elements:[]},n}(Qt),Nv={path:null,compoundPath:null,group:It,image:R,text:an},Rv=h(),Vv=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.init=function(){this._elMap=Ie()},n.prototype.render=function(t,e,n){t!==this._lastGraphicModel&&this._clear(),this._lastGraphicModel=t,this._updateElements(t),this._relocate(t,n)},n.prototype._updateElements=function(t){var e=t.useElOptionsToUpdate();if(e){var n=this._elMap,i=this.group,o=t.get("z"),r=t.get("zlevel");a(e,(function(e){var s=$e(e.id,null),l=null!=s?n.get(s):null,u=$e(e.parentId,null),d=null!=u?n.get(u):i,c=e.type,h=e.style;"text"===c&&h&&e.hv&&e.hv[1]&&(h.textVerticalAlign=h.textBaseline=h.verticalAlign=h.align=null);var p=e.textContent,g=e.textConfig;if(h&&Tf(h,c,!!g,!!p)){var f=Cf(h,c,!0);!g&&f.textConfig&&(g=e.textConfig=f.textConfig),!p&&f.textContent&&(p=f.textContent)}var y=function(t){return t=H({},t),a(["id","parentId","$action","hv","bounding","textContent","clipPath"].concat(yo),(function(e){delete t[e]})),t}(e),m=e.$action||"merge",x="merge"===m,_="replace"===m;if(x){var b=l;(C=!l)?b=Ev(s,d,e.type,n):(b&&(Rv(b).isNew=!1),qf(b)),b&&(Ef(b,y,t,{isInit:C}),Bv(b,e,o,r))}else if(_){zv(l,e,n,t);var S=Ev(s,d,e.type,n);S&&(Ef(S,y,t,{isInit:!0}),Bv(S,e,o,r))}else"remove"===m&&(zf(l,e),zv(l,e,n,t));var w=n.get(s);if(w&&p)if(x){var M=w.getTextContent();M?M.attr(p):w.setTextContent(new an(p))}else _&&w.setTextContent(new an(p));if(w){var I=e.clipPath;if(I){var A=I.type,T=void 0,C=!1;if(x){var D=w.getClipPath();T=(C=!D||Rv(D).type!==A)?Ov(A):D}else _&&(C=!0,T=Ov(A));w.setClipPath(T),Ef(T,I,t,{isInit:C}),Kf(T,I.keyframeAnimation,t)}var L=Rv(w);w.setTextConfig(g),L.option=e,function(t,e,n){var i=v(t).eventData;t.silent||t.ignore||i||(i=v(t).eventData={componentType:"graphic",componentIndex:e.componentIndex,name:t.name});i&&(i.info=n.info)}(w,t,e),Le({el:w,componentModel:t,itemName:w.name,itemTooltipOption:e.tooltip}),Kf(w,e.keyframeAnimation,t)}}))}},n.prototype._relocate=function(t,e){for(var n=t.option.elements,i=this.group,o=this._elMap,r=e.getWidth(),a=e.getHeight(),s=["x","y"],l=0;l<n.length;l++){var u=n[l];if((g=null!=(p=$e(u.id,null))?o.get(p):null)&&g.isGroup){var d=(f=g.parent)===i,c=Rv(g),h=Rv(f);c.width=m(c.option.width,d?r:h.width)||0,c.height=m(c.option.height,d?a:h.height)||0}}for(l=n.length-1;l>=0;l--){var p,g;u=n[l];if(g=null!=(p=$e(u.id,null))?o.get(p):null){var f=g.parent,y=(h=Rv(f),f===i?{width:r,height:a}:{width:h.width,height:h.height}),v={},x=on(g,u,y,null,{hv:u.hv,boundingMode:u.bounding},v);if(!Rv(g).isNew&&x){for(var _=u.transition,b={},S=0;S<s.length;S++){var w=s[S],M=v[w];_&&(Gf(_)||L(_,w)>=0)?b[w]=M:g[w]=M}P(g,b,t,0)}else g.attr(v)}}},n.prototype._clear=function(){var t=this,e=this._elMap;e.each((function(n){zv(n,Rv(n).option,e,t._lastGraphicModel)})),this._elMap=Ie()},n.prototype.dispose=function(){this._clear()},n.type="graphic",n}(oe);function Ov(t){var e=new(ge(Nv,t)?Nv[t]:Zi(t))({});return Rv(e).type=t,e}function Ev(t,e,n,i){var o=Ov(n);return e.add(o),i.set(t,o),Rv(o).id=t,Rv(o).isNew=!0,o}function zv(t,e,n,i){t&&t.parent&&("group"===t.type&&t.traverse((function(t){zv(t,e,n,i)})),Bf(t,e,i),n.removeKey(Rv(t).id))}function Bv(t,e,n,i){t.isGroup||a([["cursor",Te.prototype.cursor],["zlevel",i||0],["z",n||0],["z2",0]],(function(n){var i=n[0];ge(e,i)?t[i]=C(e[i],n[1]):null==t[i]&&(t[i]=n[1])})),a(y(e),(function(n){if(0===n.indexOf("on")){var i=e[n];t[n]=f(i)?i:null}})),ge(e,"draggable")&&(t.draggable=e.draggable),null!=e.name&&(t.name=e.name),null!=e.id&&(t.id=e.id)}var Gv=["x","y","radius","angle","single"],Fv=["cartesian2d","polar","singleAxis"];function Wv(t){return t+"Axis"}function Hv(t,e){var n,i=Ie(),o=[],r=Ie();t.eachComponent({mainType:"dataZoom",query:e},(function(t){r.get(t.uid)||s(t)}));do{n=!1,t.eachComponent("dataZoom",a)}while(n);function a(t){!r.get(t.uid)&&function(t){var e=!1;return t.eachTargetAxis((function(t,n){var o=i.get(t);o&&o[n]&&(e=!0)})),e}(t)&&(s(t),n=!0)}function s(t){r.set(t.uid,!0),o.push(t),t.eachTargetAxis((function(t,e){(i.get(t)||i.set(t,[]))[e]=!0}))}return o}function Zv(t){var e=t.ecModel,n={infoList:[],infoMap:Ie()};return t.eachTargetAxis((function(t,i){var o=e.getComponent(Wv(t),i);if(o){var r=o.getCoordSysModel();if(r){var a=r.uid,s=n.infoMap.get(a);s||(s={model:r,axisModels:[]},n.infoList.push(s),n.infoMap.set(a,s)),s.axisModels.push(o)}}})),n}var Yv=function(){function t(){this.indexList=[],this.indexMap=[]}return t.prototype.add=function(t){this.indexMap[t]||(this.indexList.push(t),this.indexMap[t]=!0)},t}(),Xv=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e._autoThrottle=!0,e._noTarget=!0,e._rangePropMode=["percent","percent"],e}return e(n,t),n.prototype.init=function(t,e,n){var i=Uv(t);this.settledOption=i,this.mergeDefaultAndTheme(t,n),this._doInit(i)},n.prototype.mergeOption=function(t){var e=Uv(t);jt(this.option,t,!0),jt(this.settledOption,e,!0),this._doInit(e)},n.prototype._doInit=function(t){var e=this.option;this._setDefaultThrottle(t),this._updateRangeUse(t);var n=this.settledOption;a([["start","startValue"],["end","endValue"]],(function(t,i){"value"===this._rangePropMode[i]&&(e[t[0]]=n[t[0]]=null)}),this),this._resetTarget()},n.prototype._resetTarget=function(){var t=this.get("orient",!0),e=this._targetAxisInfoMap=Ie();this._fillSpecifiedTargetAxis(e)?this._orient=t||this._makeAutoOrientByTargetAxis():(this._orient=t||"horizontal",this._fillAutoTargetAxisByOrient(e,this._orient)),this._noTarget=!0,e.each((function(t){t.indexList.length&&(this._noTarget=!1)}),this)},n.prototype._fillSpecifiedTargetAxis=function(t){var e=!1;return a(Gv,(function(n){var i=this.getReferringComponents(Wv(n),vo);if(i.specified){e=!0;var o=new Yv;a(i.models,(function(t){o.add(t.componentIndex)})),t.set(n,o)}}),this),e},n.prototype._fillAutoTargetAxisByOrient=function(t,e){var n=this.ecModel,i=!0;if(i){var o="vertical"===e?"y":"x";r(n.findComponents({mainType:o+"Axis"}),o)}i&&r(n.findComponents({mainType:"singleAxis",filter:function(t){return t.get("orient",!0)===e}}),"single");function r(e,n){var o=e[0];if(o){var r=new Yv;if(r.add(o.componentIndex),t.set(n,r),i=!1,"x"===n||"y"===n){var s=o.getReferringComponents("grid",ze).models[0];s&&a(e,(function(t){o.componentIndex!==t.componentIndex&&s===t.getReferringComponents("grid",ze).models[0]&&r.add(t.componentIndex)}))}}}i&&a(Gv,(function(e){if(i){var o=n.findComponents({mainType:Wv(e),filter:function(t){return"category"===t.get("type",!0)}});if(o[0]){var r=new Yv;r.add(o[0].componentIndex),t.set(e,r),i=!1}}}),this)},n.prototype._makeAutoOrientByTargetAxis=function(){var t;return this.eachTargetAxis((function(e){!t&&(t=e)}),this),"y"===t?"vertical":"horizontal"},n.prototype._setDefaultThrottle=function(t){if(t.hasOwnProperty("throttle")&&(this._autoThrottle=!1),this._autoThrottle){var e=this.ecModel.option;this.option.throttle=e.animation&&e.animationDurationUpdate>0?100:20}},n.prototype._updateRangeUse=function(t){var e=this._rangePropMode,n=this.get("rangeMode");a([["start","startValue"],["end","endValue"]],(function(i,o){var r=null!=t[i[0]],a=null!=t[i[1]];r&&!a?e[o]="percent":!r&&a?e[o]="value":n?e[o]=n[o]:r&&(e[o]="percent")}))},n.prototype.noTarget=function(){return this._noTarget},n.prototype.getFirstTargetAxisModel=function(){var t;return this.eachTargetAxis((function(e,n){null==t&&(t=this.ecModel.getComponent(Wv(e),n))}),this),t},n.prototype.eachTargetAxis=function(t,e){this._targetAxisInfoMap.each((function(n,i){a(n.indexList,(function(n){t.call(e,i,n)}))}))},n.prototype.getAxisProxy=function(t,e){var n=this.getAxisModel(t,e);if(n)return n.__dzAxisProxy},n.prototype.getAxisModel=function(t,e){var n=this._targetAxisInfoMap.get(t);if(n&&n.indexMap[e])return this.ecModel.getComponent(Wv(t),e)},n.prototype.setRawRange=function(t){var e=this.option,n=this.settledOption;a([["start","startValue"],["end","endValue"]],(function(i){null==t[i[0]]&&null==t[i[1]]||(e[i[0]]=n[i[0]]=t[i[0]],e[i[1]]=n[i[1]]=t[i[1]])}),this),this._updateRangeUse(t)},n.prototype.setCalculatedRange=function(t){var e=this.option;a(["start","startValue","end","endValue"],(function(n){e[n]=t[n]}))},n.prototype.getPercentRange=function(){var t=this.findRepresentativeAxisProxy();if(t)return t.getDataPercentWindow()},n.prototype.getValueRange=function(t,e){if(null!=t||null!=e)return this.getAxisProxy(t,e).getDataValueWindow();var n=this.findRepresentativeAxisProxy();return n?n.getDataValueWindow():void 0},n.prototype.findRepresentativeAxisProxy=function(t){if(t)return t.__dzAxisProxy;for(var e,n=this._targetAxisInfoMap.keys(),i=0;i<n.length;i++)for(var o=n[i],r=this._targetAxisInfoMap.get(o),a=0;a<r.indexList.length;a++){var s=this.getAxisProxy(o,r.indexList[a]);if(s.hostedBy(this))return s;e||(e=s)}return e},n.prototype.getRangePropMode=function(){return this._rangePropMode.slice()},n.prototype.getOrient=function(){return this._orient},n.type="dataZoom",n.dependencies=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","series","toolbox"],n.defaultOption={z:4,filterMode:"filter",start:0,end:100},n}(Qt);function Uv(t){var e={};return a(["start","end","startValue","endValue","throttle"],(function(n){t.hasOwnProperty(n)&&(e[n]=t[n])})),e}var jv=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.type="dataZoom.select",n}(Xv),qv=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.render=function(t,e,n,i){this.dataZoomModel=t,this.ecModel=e,this.api=n},n.type="dataZoom",n}(oe),Kv=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.type="dataZoom.select",n}(qv),Jv=a,$v=ni,Qv=function(){function t(t,e,n,i){this._dimName=t,this._axisIndex=e,this.ecModel=i,this._dataZoomModel=n}return t.prototype.hostedBy=function(t){return this._dataZoomModel===t},t.prototype.getDataValueWindow=function(){return this._valueWindow.slice()},t.prototype.getDataPercentWindow=function(){return this._percentWindow.slice()},t.prototype.getTargetSeriesModels=function(){var t=[];return this.ecModel.eachSeries((function(e){if(function(t){var e=t.get("coordinateSystem");return L(Fv,e)>=0}(e)){var n=Wv(this._dimName),i=e.getReferringComponents(n,ze).models[0];i&&this._axisIndex===i.componentIndex&&t.push(e)}}),this),t},t.prototype.getAxisModel=function(){return this.ecModel.getComponent(this._dimName+"Axis",this._axisIndex)},t.prototype.getMinMaxSpan=function(){return ct(this._minMaxSpan)},t.prototype.calculateDataWindow=function(t){var e,n=this._dataExtent,i=this.getAxisModel().axis.scale,o=this._dataZoomModel.getRangePropMode(),r=[0,100],a=[],s=[];Jv(["start","end"],(function(l,u){var d=t[l],c=t[l+"Value"];"percent"===o[u]?(null==d&&(d=r[u]),c=i.parse(fn(d,r,n))):(e=!0,c=null==c?n[u]:i.parse(c),d=fn(c,n,r)),s[u]=null==c||isNaN(c)?n[u]:c,a[u]=null==d||isNaN(d)?r[u]:d})),$v(s),$v(a);var l=this._minMaxSpan;function u(t,e,n,o,r){var a=r?"Span":"ValueSpan";oh(0,t,n,"all",l["min"+a],l["max"+a]);for(var s=0;s<2;s++)e[s]=fn(t[s],n,o,!0),r&&(e[s]=i.parse(e[s]))}return e?u(s,a,n,r,!1):u(a,s,r,n,!0),{valueWindow:s,percentWindow:a}},t.prototype.reset=function(t){if(t===this._dataZoomModel){var e=this.getTargetSeriesModels();this._dataExtent=function(t,e,n){var i=[1/0,-1/0];Jv(n,(function(t){xo(i,t.getData(),e)}));var o=t.getAxisModel(),r=_o(o.axis.scale,o,i).calculate();return[r.min,r.max]}(this,this._dimName,e),this._updateMinMaxSpan();var n=this.calculateDataWindow(t.settledOption);this._valueWindow=n.valueWindow,this._percentWindow=n.percentWindow,this._setAxisModel()}},t.prototype.filterData=function(t,e){if(t===this._dataZoomModel){var n=this._dimName,i=this.getTargetSeriesModels(),o=t.get("filterMode"),r=this._valueWindow;"none"!==o&&Jv(i,(function(t){var e=t.getData(),i=e.mapDimensionsAll(n);if(i.length){if("weakFilter"===o){var a=e.getStore(),s=d(i,(function(t){return e.getDimensionIndex(t)}),e);e.filterSelf((function(t){for(var e,n,o,l=0;l<i.length;l++){var u=a.get(s[l],t),d=!isNaN(u),c=u<r[0],h=u>r[1];if(d&&!c&&!h)return!0;d&&(o=!0),c&&(e=!0),h&&(n=!0)}return o&&e&&n}))}else Jv(i,(function(n){if("empty"===o)t.setData(e=e.map(n,(function(t){return function(t){return t>=r[0]&&t<=r[1]}(t)?t:NaN})));else{var i={};i[n]=r,e.selectRange(i)}}));Jv(i,(function(t){e.setApproximateExtent(r,t)}))}}))}},t.prototype._updateMinMaxSpan=function(){var t=this._minMaxSpan={},e=this._dataZoomModel,n=this._dataExtent;Jv(["min","max"],(function(i){var o=e.get(i+"Span"),r=e.get(i+"ValueSpan");null!=r&&(r=this.getAxisModel().axis.scale.parse(r)),null!=r?o=fn(n[0]+r,n,[0,100],!0):null!=o&&(r=fn(o,[0,100],n,!0)-n[0]),t[i+"Span"]=o,t[i+"ValueSpan"]=r}),this)},t.prototype._setAxisModel=function(){var t=this.getAxisModel(),e=this._percentWindow,n=this._valueWindow;if(e){var i=mo(n,[0,500]);i=Math.min(i,20);var o=t.axis.scale.rawExtentInfo;0!==e[0]&&o.setDeterminedMinMax("min",+n[0].toFixed(i)),100!==e[1]&&o.setDeterminedMinMax("max",+n[1].toFixed(i)),o.freeze()}},t}();var tm={getTargetSeries:function(t){function e(e){t.eachComponent("dataZoom",(function(n){n.eachTargetAxis((function(i,o){var r=t.getComponent(Wv(i),o);e(i,o,r,n)}))}))}e((function(t,e,n,i){n.__dzAxisProxy=null}));var n=[];e((function(e,i,o,r){o.__dzAxisProxy||(o.__dzAxisProxy=new Qv(e,i,r,t),n.push(o.__dzAxisProxy))}));var i=Ie();return a(n,(function(t){a(t.getTargetSeriesModels(),(function(t){i.set(t.uid,t)}))})),i},overallReset:function(t,e){t.eachComponent("dataZoom",(function(t){t.eachTargetAxis((function(e,n){t.getAxisProxy(e,n).reset(t)})),t.eachTargetAxis((function(n,i){t.getAxisProxy(n,i).filterData(t,e)}))})),t.eachComponent("dataZoom",(function(t){var e=t.findRepresentativeAxisProxy();if(e){var n=e.getDataPercentWindow(),i=e.getDataValueWindow();t.setCalculatedRange({start:n[0],end:n[1],startValue:i[0],endValue:i[1]})}}))}};var em=!1;function nm(t){em||(em=!0,t.registerProcessor(t.PRIORITY.PROCESSOR.FILTER,tm),function(t){t.registerAction("dataZoom",(function(t,e){var n=Hv(e,t);a(n,(function(e){e.setRawRange({start:t.start,end:t.end,startValue:t.startValue,endValue:t.endValue})}))}))}(t),t.registerSubTypeDefaulter("dataZoom",(function(){return"slider"})))}function im(t){t.registerComponentModel(jv),t.registerComponentView(Kv),nm(t)}var om=function(){return function(){}}(),rm={};function am(t,e){rm[t]=e}function sm(t){return rm[t]}var lm=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.optionUpdated=function(){t.prototype.optionUpdated.apply(this,arguments);var e=this.ecModel;a(this.option.feature,(function(t,n){var i=sm(n);i&&(i.getDefaultOption&&(i.defaultOption=i.getDefaultOption(e)),jt(t,i.defaultOption))}))},n.type="toolbox",n.layoutMode={type:"box",ignoreSize:!0},n.defaultOption={show:!0,z:6,orient:"horizontal",left:"right",top:"top",backgroundColor:"transparent",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemSize:15,itemGap:8,showTitle:!0,iconStyle:{borderColor:"#666",color:"none"},emphasis:{iconStyle:{borderColor:"#3E98C5"}},tooltip:{show:!1,position:"bottom"}},n}(Qt),um=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return e(n,t),n.prototype.render=function(t,e,n,i){var o=this.group;if(o.removeAll(),t.get("show")){var r=+t.get("itemSize"),s="vertical"===t.get("orient"),l=t.get("feature")||{},u=this._features||(this._features={}),d=[];a(l,(function(t,e){d.push(e)})),new ln(this._featureNames||[],d).add(c).update(c).remove(Re(c,null)).execute(),this._featureNames=d,So(o,t,n),o.add(wo(o.getBoundingRect(),t)),s||o.eachChild((function(t){var e=t.__title,i=t.ensureState("emphasis"),a=i.textConfig||(i.textConfig={}),s=t.getTextContent(),l=s&&s.ensureState("emphasis");if(l&&!f(l)&&e){var u=l.style||(l.style={}),d=Ki(e,an.makeFont(u)),c=t.x+o.x,h=!1;t.y+o.y+r+d.height>n.getHeight()&&(a.position="top",h=!0);var p=h?-5-d.height:r+10;c+d.width/2>n.getWidth()?(a.position=["100%",p],u.align="right"):c-d.width/2<0&&(a.position=[0,p],u.align="left")}}))}function c(c,h){var p,g=d[c],f=d[h],y=l[g],v=new qt(y,t,t.ecModel);if(i&&null!=i.newTitle&&i.featureName===g&&(y.title=i.newTitle),g&&!f){if(function(t){return 0===t.indexOf("my")}(g))p={onclick:v.option.onclick,featureName:g};else{var m=sm(g);if(!m)return;p=new m}u[g]=p}else if(!(p=u[f]))return;p.uid=Ae("toolbox-feature"),p.model=v,p.ecModel=e,p.api=n;var x=p instanceof om;g||!f?!v.get("show")||x&&p.unusable?x&&p.remove&&p.remove(e,n):(!function(i,l,u){var d,c,h=i.getModel("iconStyle"),p=i.getModel(["emphasis","iconStyle"]),g=l instanceof om&&l.getIcons?l.getIcons():i.get("icon"),f=i.get("title")||{};G(g)?(d={})[u]=g:d=g;G(f)?(c={})[u]=f:c=f;var y=i.iconPaths={};a(d,(function(a,u){var d=bo(a,{},{x:-r/2,y:-r/2,width:r,height:r});d.setStyle(h.getItemStyle()),d.ensureState("emphasis").style=p.getItemStyle();var g=new an({style:{text:c[u],align:p.get("textAlign"),borderRadius:p.get("textBorderRadius"),padding:p.get("textPadding"),fill:null,font:Wi({fontStyle:p.get("textFontStyle"),fontFamily:p.get("textFontFamily"),fontSize:p.get("textFontSize"),fontWeight:p.get("textFontWeight")},e)},ignore:!0});d.setTextContent(g),Le({el:d,componentModel:t,itemName:u,formatterParamsExtra:{title:c[u]}}),d.__title=c[u],d.on("mouseover",(function(){var e=p.getItemStyle(),i=s?null==t.get("right")&&"right"!==t.get("left")?"right":"left":null==t.get("bottom")&&"bottom"!==t.get("top")?"bottom":"top";g.setStyle({fill:p.get("textFill")||e.fill||e.stroke||"#000",backgroundColor:p.get("textBackgroundColor")}),d.setTextConfig({position:p.get("textPosition")||i}),g.ignore=!t.get("showTitle"),n.enterEmphasis(this)})).on("mouseout",(function(){"emphasis"!==i.get(["iconStatus",u])&&n.leaveEmphasis(this),g.hide()})),("emphasis"===i.get(["iconStatus",u])?Nn:Rn)(d),o.add(d),d.on("click",Zt(l.onclick,l,e,n,u)),y[u]=d}))}(v,p,g),v.setIconStatus=function(t,e){var n=this.option,i=this.iconPaths;n.iconStatus=n.iconStatus||{},n.iconStatus[t]=e,i[t]&&("emphasis"===e?Nn:Rn)(i[t])},p instanceof om&&p.render&&p.render(v,e,n,i)):x&&p.dispose&&p.dispose(e,n)}},n.prototype.updateView=function(t,e,n,i){a(this._features,(function(t){t instanceof om&&t.updateView&&t.updateView(t.model,e,n,i)}))},n.prototype.remove=function(t,e){a(this._features,(function(n){n instanceof om&&n.remove&&n.remove(t,e)})),this.group.removeAll()},n.prototype.dispose=function(t,e){a(this._features,(function(n){n instanceof om&&n.dispose&&n.dispose(t,e)}))},n.type="toolbox",n}(oe);var dm=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return e(n,t),n.prototype.onclick=function(t,e){var n=this.model,i=n.get("name")||t.get("title.0.text")||"echarts",o="svg"===e.getZr().painter.getType(),r=o?"svg":n.get("type",!0)||"png",a=e.getConnectedDataURL({type:r,backgroundColor:n.get("backgroundColor",!0)||t.get("backgroundColor")||"#fff",connectedBackgroundColor:n.get("connectedBackgroundColor"),excludeComponents:n.get("excludeComponents"),pixelRatio:n.get("pixelRatio")}),s=Mo.browser;if("function"!=typeof MouseEvent||!s.newEdge&&(s.ie||s.edge))if(window.navigator.msSaveOrOpenBlob||o){var l=a.split(","),u=l[0].indexOf("base64")>-1,d=o?decodeURIComponent(l[1]):l[1];u&&(d=window.atob(d));var c=i+"."+r;if(window.navigator.msSaveOrOpenBlob){for(var h=d.length,p=new Uint8Array(h);h--;)p[h]=d.charCodeAt(h);var g=new Blob([p]);window.navigator.msSaveOrOpenBlob(g,c)}else{var f=document.createElement("iframe");document.body.appendChild(f);var y=f.contentWindow,v=y.document;v.open("image/svg+xml","replace"),v.write(d),v.close(),y.focus(),v.execCommand("SaveAs",!0,c),document.body.removeChild(f)}}else{var m=n.get("lang"),x='<body style="margin:0;"><img src="'+a+'" style="max-width:100%;" title="'+(m&&m[0]||"")+'" /></body>',_=window.open();_.document.write(x),_.document.title=i}else{var b=document.createElement("a");b.download=i+"."+r,b.target="_blank",b.href=a;var S=new MouseEvent("click",{view:document.defaultView,bubbles:!0,cancelable:!1});b.dispatchEvent(S)}},n.getDefaultOption=function(t){return{show:!0,icon:"M4.7,22.9L29.3,45.5L54.7,23.4M4.6,43.6L4.6,58L53.8,58L53.8,43.6M29.2,45.1L29.2,0",title:t.getLocaleModel().get(["toolbox","saveAsImage","title"]),type:"png",connectedBackgroundColor:"#fff",name:"",excludeComponents:["toolbox"],lang:t.getLocaleModel().get(["toolbox","saveAsImage","lang"])}},n}(om),cm="__ec_magicType_stack__",hm=[["line","bar"],["stack"]],pm=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return e(n,t),n.prototype.getIcons=function(){var t=this.model,e=t.get("icon"),n={};return a(t.get("type"),(function(t){e[t]&&(n[t]=e[t])})),n},n.getDefaultOption=function(t){return{show:!0,type:[],icon:{line:"M4.1,28.9h7.1l9.3-22l7.4,38l9.7-19.7l3,12.8h14.9M4.1,58h51.4",bar:"M6.7,22.9h10V48h-10V22.9zM24.9,13h10v35h-10V13zM43.2,2h10v46h-10V2zM3.1,58h53.7",stack:"M8.2,38.4l-8.4,4.1l30.6,15.3L60,42.5l-8.1-4.1l-21.5,11L8.2,38.4z M51.9,30l-8.1,4.2l-13.4,6.9l-13.9-6.9L8.2,30l-8.4,4.2l8.4,4.2l22.2,11l21.5-11l8.1-4.2L51.9,30z M51.9,21.7l-8.1,4.2L35.7,30l-5.3,2.8L24.9,30l-8.4-4.1l-8.3-4.2l-8.4,4.2L8.2,30l8.3,4.2l13.9,6.9l13.4-6.9l8.1-4.2l8.1-4.1L51.9,21.7zM30.4,2.2L-0.2,17.5l8.4,4.1l8.3,4.2l8.4,4.2l5.5,2.7l5.3-2.7l8.1-4.2l8.1-4.2l8.1-4.1L30.4,2.2z"},title:t.getLocaleModel().get(["toolbox","magicType","title"]),option:{},seriesIndex:{}}},n.prototype.onclick=function(t,e,n){var i=this.model,o=i.get(["seriesIndex",n]);if(gm[n]){var r,s={series:[]};a(hm,(function(t){L(t,n)>=0&&a(t,(function(t){i.setIconStatus(t,"normal")}))})),i.setIconStatus(n,"emphasis"),t.eachComponent({mainType:"series",query:null==o?null:{seriesIndex:o}},(function(t){var e=t.subType,o=t.id,r=gm[n](e,o,t,i);r&&(Ot(r,t.option),s.series.push(r));var a=t.coordinateSystem;if(a&&"cartesian2d"===a.type&&("line"===n||"bar"===n)){var l=a.getAxesByScale("ordinal")[0];if(l){var u=l.dim+"Axis",d=t.getReferringComponents(u,ze).models[0].componentIndex;s[u]=s[u]||[];for(var c=0;c<=d;c++)s[u][d]=s[u][d]||{};s[u][d].boundaryGap="bar"===n}}}));var l=n;"stack"===n&&(r=jt({stack:i.option.title.tiled,tiled:i.option.title.stack},i.option.title),"emphasis"!==i.get(["iconStatus",n])&&(l="tiled")),e.dispatchAction({type:"changeMagicType",currentType:l,newOption:s,newTitle:r,featureName:"magicType"})}},n}(om),gm={line:function(t,e,n,i){if("bar"===t)return jt({id:e,type:"line",data:n.get("data"),stack:n.get("stack"),markPoint:n.get("markPoint"),markLine:n.get("markLine")},i.get(["option","line"])||{},!0)},bar:function(t,e,n,i){if("line"===t)return jt({id:e,type:"bar",data:n.get("data"),stack:n.get("stack"),markPoint:n.get("markPoint"),markLine:n.get("markLine")},i.get(["option","bar"])||{},!0)},stack:function(t,e,n,i){var o=n.get("stack")===cm;if("line"===t||"bar"===t)return i.setIconStatus("stack",o?"normal":"emphasis"),jt({id:e,stack:o?"":cm},i.get(["option","stack"])||{},!0)}};ue({type:"changeMagicType",event:"magicTypeChanged",update:"prepareAndUpdate"},(function(t,e){e.mergeOption(t.newOption)}));var fm=new Array(60).join("-"),ym="\t";function vm(t){return t.replace(/^\s\s*/,"").replace(/\s\s*$/,"")}var mm=new RegExp("[\t]+","g");function xm(t,e){var n=t.split(new RegExp("\n*"+fm+"\n*","g")),i={series:[]};return a(n,(function(t,n){if(function(t){if(t.slice(0,t.indexOf("\n")).indexOf(ym)>=0)return!0}(t)){var o=function(t){for(var e=t.split(/\n+/g),n=vm(e.shift()).split(mm),i=[],o=d(n,(function(t){return{name:t,data:[]}})),r=0;r<e.length;r++){var a=vm(e[r]).split(mm);i.push(a.shift());for(var s=0;s<a.length;s++)o[s]&&(o[s].data[r]=a[s])}return{series:o,categories:i}}(t),r=e[n],a=r.axisDim+"Axis";r&&(i[a]=i[a]||[],i[a][r.axisIndex]={data:o.categories},i.series=i.series.concat(o.series))}else{o=function(t){for(var e=t.split(/\n+/g),n=vm(e.shift()),i=[],o=0;o<e.length;o++){var r=vm(e[o]);if(r){var a=r.split(mm),s="",l=void 0,u=!1;isNaN(a[0])?(u=!0,s=a[0],a=a.slice(1),i[o]={name:s,value:[]},l=i[o].value):l=i[o]=[];for(var d=0;d<a.length;d++)l.push(+a[d]);1===l.length&&(u?i[o].value=l[0]:i[o]=l[0])}}return{name:n,data:i}}(t);i.series.push(o)}})),i}var _m=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return e(n,t),n.prototype.onclick=function(t,e){setTimeout((function(){e.dispatchAction({type:"hideTip"})}));var n=e.getDom(),i=this.model;this._dom&&n.removeChild(this._dom);var o=document.createElement("div");o.style.cssText="position:absolute;top:0;bottom:0;left:0;right:0;padding:5px",o.style.backgroundColor=i.get("backgroundColor")||"#fff";var r=document.createElement("h4"),s=i.get("lang")||[];r.innerHTML=s[0]||i.get("title"),r.style.cssText="margin:10px 20px",r.style.color=i.get("textColor");var l=document.createElement("div"),u=document.createElement("textarea");l.style.cssText="overflow:auto";var h=i.get("optionToContent"),p=i.get("contentToOption"),g=function(t){var e,n,i,o=function(t){var e={},n=[],i=[];return t.eachRawSeries((function(t){var o=t.coordinateSystem;if(!o||"cartesian2d"!==o.type&&"polar"!==o.type)n.push(t);else{var r=o.getBaseAxis();if("category"===r.type){var a=r.dim+"_"+r.index;e[a]||(e[a]={categoryAxis:r,valueAxis:o.getOtherAxis(r),series:[]},i.push({axisDim:r.dim,axisIndex:r.index})),e[a].series.push(t)}else n.push(t)}})),{seriesGroupByCategoryAxis:e,other:n,meta:i}}(t);return{value:c([(n=o.seriesGroupByCategoryAxis,i=[],a(n,(function(t,e){var n=t.categoryAxis,o=t.valueAxis.dim,r=[" "].concat(d(t.series,(function(t){return t.name}))),s=[n.model.getCategories()];a(t.series,(function(t){var e=t.getRawData();s.push(t.getRawData().mapArray(e.mapDimension(o),(function(t){return t})))}));for(var l=[r.join(ym)],u=0;u<s[0].length;u++){for(var c=[],h=0;h<s.length;h++)c.push(s[h][u]);l.push(c.join(ym))}i.push(l.join("\n"))})),i.join("\n\n"+fm+"\n\n")),(e=o.other,d(e,(function(t){var e=t.getRawData(),n=[t.name],i=[];return e.each(e.dimensions,(function(){for(var t=arguments.length,o=arguments[t-1],r=e.getName(o),a=0;a<t-1;a++)i[a]=arguments[a];n.push((r?r+ym:"")+i.join(ym))})),n.join("\n")})).join("\n\n"+fm+"\n\n"))],(function(t){return!!t.replace(/[\n\t\s]/g,"")})).join("\n\n"+fm+"\n\n"),meta:o.meta}}(t);if(f(h)){var y=h(e.getOption());G(y)?l.innerHTML=y:Io(y)&&l.appendChild(y)}else{u.readOnly=i.get("readOnly");var v=u.style;v.cssText="display:block;width:100%;height:100%;font-family:monospace;font-size:14px;line-height:1.6rem;resize:none;box-sizing:border-box;outline:none",v.color=i.get("textColor"),v.borderColor=i.get("textareaBorderColor"),v.backgroundColor=i.get("textareaColor"),u.value=g.value,l.appendChild(u)}var m=g.meta,x=document.createElement("div");x.style.cssText="position:absolute;bottom:5px;left:0;right:0";var _="float:right;margin-right:20px;border:none;cursor:pointer;padding:2px 5px;font-size:12px;border-radius:3px",b=document.createElement("div"),S=document.createElement("div");_+=";background-color:"+i.get("buttonColor"),_+=";color:"+i.get("buttonTextColor");var w=this;function M(){n.removeChild(o),w._dom=null}Ao(b,"click",M),Ao(S,"click",(function(){if(null==p&&null!=h||null!=p&&null==h)M();else{var t;try{t=f(p)?p(l,e.getOption()):xm(u.value,m)}catch(n){throw M(),new Error("Data view format error "+n)}t&&e.dispatchAction({type:"changeDataView",newOption:t}),M()}})),b.innerHTML=s[1],S.innerHTML=s[2],S.style.cssText=b.style.cssText=_,!i.get("readOnly")&&x.appendChild(S),x.appendChild(b),o.appendChild(r),o.appendChild(l),o.appendChild(x),l.style.height=n.clientHeight-80+"px",n.appendChild(o),this._dom=o},n.prototype.remove=function(t,e){this._dom&&e.getDom().removeChild(this._dom)},n.prototype.dispose=function(t,e){this.remove(t,e)},n.getDefaultOption=function(t){return{show:!0,readOnly:!1,optionToContent:null,contentToOption:null,icon:"M17.5,17.3H33 M17.5,17.3H33 M45.4,29.5h-28 M11.5,2v56H51V14.8L38.4,2H11.5z M38.4,2.2v12.7H51 M45.4,41.7h-28",title:t.getLocaleModel().get(["toolbox","dataView","title"]),lang:t.getLocaleModel().get(["toolbox","dataView","lang"]),backgroundColor:"#fff",textColor:"#000",textareaColor:"#fff",textareaBorderColor:"#333",buttonColor:"#c23531",buttonTextColor:"#fff"}},n}(om);function bm(t,e){return d(t,(function(t,n){var i=e&&e[n];if(xt(i)&&!mt(i)){xt(t)&&!mt(t)||(t={value:t});var o=null!=i.name&&null==t.name;return t=Ot(t,i),o&&delete t.name,t}return t}))}ue({type:"changeDataView",event:"dataViewChanged",update:"prepareAndUpdate"},(function(t,e){var n=[];a(t.newOption.series,(function(t){var i=e.getSeriesByName(t.name)[0];if(i){var o=i.get("data");n.push({name:t.name,data:bm(t.data,o)})}else n.push(H({type:"scatter"},t))})),e.mergeOption(Ot({series:n},t.newOption))}));var Sm=a,wm=h();function Mm(t){var e=wm(t);return e.snapshots||(e.snapshots=[{}]),e.snapshots}var Im=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return e(n,t),n.prototype.onclick=function(t,e){!function(t){wm(t).snapshots=null}(t),e.dispatchAction({type:"restore",from:this.uid})},n.getDefaultOption=function(t){return{show:!0,icon:"M3.8,33.4 M47,18.9h9.8V8.7 M56.3,20.1 C52.1,9,40.5,0.6,26.8,2.1C12.6,3.7,1.6,16.2,2.1,30.6 M13,41.1H3.1v10.2 M3.7,39.9c4.2,11.1,15.8,19.5,29.5,18 c14.2-1.6,25.2-14.1,24.7-28.5",title:t.getLocaleModel().get(["toolbox","restore","title"])}},n}(om);ue({type:"restore",event:"restore",update:"prepareAndUpdate"},(function(t,e){e.resetOption("recreate")}));var Am=["grid","xAxis","yAxis","geo","graph","polar","radiusAxis","angleAxis","bmap"],Tm=function(){function t(t,e,n){var i=this;this._targetInfoList=[];var o=Dm(e,t);a(Lm,(function(t,e){(!n||!n.include||L(n.include,e)>=0)&&t(o,i._targetInfoList)}))}return t.prototype.setOutputRanges=function(t,e){return this.matchOutputRanges(t,e,(function(t,e,n){if((t.coordRanges||(t.coordRanges=[])).push(e),!t.coordRange){t.coordRange=e;var i=Nm[t.brushType](0,n,e);t.__rangeOffset={offset:Vm[t.brushType](i.values,t.range,[1,1]),xyMinMax:i.xyMinMax}}})),t},t.prototype.matchOutputRanges=function(t,e,n){a(t,(function(t){var i=this.findTargetInfo(t,e);i&&!0!==i&&a(i.coordSyses,(function(i){var o=Nm[t.brushType](1,i,t.range,!0);n(t,o.values,i,e)}))}),this)},t.prototype.setInputRanges=function(t,e){a(t,(function(t){var n,i,o,r,a,s=this.findTargetInfo(t,e);if(t.range=t.range||[],s&&!0!==s){t.panelId=s.panelId;var l=Nm[t.brushType](0,s.coordSys,t.coordRange),u=t.__rangeOffset;t.range=u?Vm[t.brushType](l.values,u.offset,(n=l.xyMinMax,i=u.xyMinMax,o=Em(n),r=Em(i),a=[o[0]/r[0],o[1]/r[1]],isNaN(a[0])&&(a[0]=1),isNaN(a[1])&&(a[1]=1),a)):l.values}}),this)},t.prototype.makePanelOpts=function(t,e){return d(this._targetInfoList,(function(n){var i=n.getPanelRect();return{panelId:n.panelId,defaultBrushType:e?e(n):null,clipPath:ap(i),isTargetByCursor:lp(i,t,n.coordSysModel),getLinearBrushOtherExtent:sp(i)}}))},t.prototype.controlSeries=function(t,e,n){var i=this.findTargetInfo(t,n);return!0===i||i&&L(i.coordSyses,e.coordinateSystem)>=0},t.prototype.findTargetInfo=function(t,e){for(var n=this._targetInfoList,i=Dm(e,t),o=0;o<n.length;o++){var r=n[o],a=t.panelId;if(a){if(r.panelId===a)return r}else for(var s=0;s<Pm.length;s++)if(Pm[s](i,r))return r}return!0},t}();function Cm(t){return t[0]>t[1]&&t.reverse(),t}function Dm(t,e){return To(t,e,{includeMainTypes:Am})}var Lm={grid:function(t,e){var n=t.xAxisModels,i=t.yAxisModels,o=t.gridModels,r=Ie(),s={},l={};(n||i||o)&&(a(n,(function(t){var e=t.axis.grid.model;r.set(e.id,e),s[e.id]=!0})),a(i,(function(t){var e=t.axis.grid.model;r.set(e.id,e),l[e.id]=!0})),a(o,(function(t){r.set(t.id,t),s[t.id]=!0,l[t.id]=!0})),r.each((function(t){var o=t.coordinateSystem,r=[];a(o.getCartesians(),(function(t,e){(L(n,t.getAxis("x").model)>=0||L(i,t.getAxis("y").model)>=0)&&r.push(t)})),e.push({panelId:"grid--"+t.id,gridModel:t,coordSysModel:t,coordSys:r[0],coordSyses:r,getPanelRect:km.grid,xAxisDeclared:s[t.id],yAxisDeclared:l[t.id]})})))},geo:function(t,e){a(t.geoModels,(function(t){var n=t.coordinateSystem;e.push({panelId:"geo--"+t.id,geoModel:t,coordSysModel:t,coordSys:n,coordSyses:[n],getPanelRect:km.geo})}))}},Pm=[function(t,e){var n=t.xAxisModel,i=t.yAxisModel,o=t.gridModel;return!o&&n&&(o=n.axis.grid.model),!o&&i&&(o=i.axis.grid.model),o&&o===e.gridModel},function(t,e){var n=t.geoModel;return n&&n===e.geoModel}],km={grid:function(){return this.coordSys.master.getRect().clone()},geo:function(){var t=this.coordSys,e=t.getBoundingRect().clone();return e.applyTransform(oi(t)),e}},Nm={lineX:Re(Rm,0),lineY:Re(Rm,1),rect:function(t,e,n,i){var o=t?e.pointToData([n[0][0],n[1][0]],i):e.dataToPoint([n[0][0],n[1][0]],i),r=t?e.pointToData([n[0][1],n[1][1]],i):e.dataToPoint([n[0][1],n[1][1]],i),a=[Cm([o[0],r[0]]),Cm([o[1],r[1]])];return{values:a,xyMinMax:a}},polygon:function(t,e,n,i){var o=[[1/0,-1/0],[1/0,-1/0]];return{values:d(n,(function(n){var r=t?e.pointToData(n,i):e.dataToPoint(n,i);return o[0][0]=Math.min(o[0][0],r[0]),o[1][0]=Math.min(o[1][0],r[1]),o[0][1]=Math.max(o[0][1],r[0]),o[1][1]=Math.max(o[1][1],r[1]),r})),xyMinMax:o}}};function Rm(t,e,n,i){var o=n.getAxis(["x","y"][t]),r=Cm(d([0,1],(function(t){return e?o.coordToData(o.toLocalCoord(i[t]),!0):o.toGlobalCoord(o.dataToCoord(i[t]))}))),a=[];return a[t]=r,a[1-t]=[NaN,NaN],{values:r,xyMinMax:a}}var Vm={lineX:Re(Om,0),lineY:Re(Om,1),rect:function(t,e,n){return[[t[0][0]-n[0]*e[0][0],t[0][1]-n[0]*e[0][1]],[t[1][0]-n[1]*e[1][0],t[1][1]-n[1]*e[1][1]]]},polygon:function(t,e,n){return d(t,(function(t,i){return[t[0]-n[0]*e[i][0],t[1]-n[1]*e[i][1]]}))}};function Om(t,e,n,i){return[e[0]-i[t]*n[0],e[1]-i[t]*n[1]]}function Em(t){return t?[t[0][1]-t[0][0],t[1][1]-t[1][0]]:[NaN,NaN]}var zm=a,Bm=Do("toolbox-dataZoom_"),Gm=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return e(n,t),n.prototype.render=function(t,e,n,i){this._brushController||(this._brushController=new Dh(n.getZr()),this._brushController.on("brush",Zt(this._onBrush,this)).mount()),function(t,e,n,i,o){var r=n._isZoomActive;i&&"takeGlobalCursor"===i.type&&(r="dataZoomSelect"===i.key&&i.dataZoomSelectActive);n._isZoomActive=r,t.setIconStatus("zoom",r?"emphasis":"normal");var a=new Tm(Wm(t),e,{include:["grid"]}),s=a.makePanelOpts(o,(function(t){return t.xAxisDeclared&&!t.yAxisDeclared?"lineX":!t.xAxisDeclared&&t.yAxisDeclared?"lineY":"rect"}));n._brushController.setPanels(s).enableBrush(!(!r||!s.length)&&{brushType:"auto",brushStyle:t.getModel("brushStyle").getItemStyle()})}(t,e,this,i,n),function(t,e){t.setIconStatus("back",function(t){return Mm(t).length}(e)>1?"emphasis":"normal")}(t,e)},n.prototype.onclick=function(t,e,n){Fm[n].call(this)},n.prototype.remove=function(t,e){this._brushController&&this._brushController.unmount()},n.prototype.dispose=function(t,e){this._brushController&&this._brushController.dispose()},n.prototype._onBrush=function(t){var e=t.areas;if(t.isEnd&&e.length){var n={},i=this.ecModel;this._brushController.updateCovers([]),new Tm(Wm(this.model),i,{include:["grid"]}).matchOutputRanges(e,i,(function(t,e,n){if("cartesian2d"===n.type){var i=t.brushType;"rect"===i?(o("x",n,e[0]),o("y",n,e[1])):o({lineX:"x",lineY:"y"}[i],n,e)}})),function(t,e){var n=Mm(t);Sm(e,(function(e,i){for(var o=n.length-1;o>=0&&!n[o][i];o--);if(o<0){var r=t.queryComponents({mainType:"dataZoom",subType:"select",id:i})[0];if(r){var a=r.getPercentRange();n[0][i]={dataZoomId:i,start:a[0],end:a[1]}}}})),n.push(e)}(i,n),this._dispatchZoomAction(n)}function o(t,e,o){var r=e.getAxis(t),a=r.model,s=function(t,e,n){var i;return n.eachComponent({mainType:"dataZoom",subType:"select"},(function(n){n.getAxisModel(t,e.componentIndex)&&(i=n)})),i}(t,a,i),l=s.findRepresentativeAxisProxy(a).getMinMaxSpan();null==l.minValueSpan&&null==l.maxValueSpan||(o=oh(0,o.slice(),r.scale.getExtent(),0,l.minValueSpan,l.maxValueSpan)),s&&(n[s.id]={dataZoomId:s.id,startValue:o[0],endValue:o[1]})}},n.prototype._dispatchZoomAction=function(t){var e=[];zm(t,(function(t,n){e.push(ct(t))})),e.length&&this.api.dispatchAction({type:"dataZoom",from:this.uid,batch:e})},n.getDefaultOption=function(t){return{show:!0,filterMode:"filter",icon:{zoom:"M0,13.5h26.9 M13.5,26.9V0 M32.1,13.5H58V58H13.5 V32.1",back:"M22,1.4L9.9,13.5l12.3,12.3 M10.3,13.5H54.9v44.6 H10.3v-26"},title:t.getLocaleModel().get(["toolbox","dataZoom","title"]),brushStyle:{borderWidth:0,color:"rgba(210,219,238,0.2)"}}},n}(om),Fm={zoom:function(){var t=!this._isZoomActive;this.api.dispatchAction({type:"takeGlobalCursor",key:"dataZoomSelect",dataZoomSelectActive:t})},back:function(){this._dispatchZoomAction(function(t){var e=Mm(t),n=e[e.length-1];e.length>1&&e.pop();var i={};return Sm(n,(function(t,n){for(var o=e.length-1;o>=0;o--)if(t=e[o][n]){i[n]=t;break}})),i}(this.ecModel))}};function Wm(t){var e={xAxisIndex:t.get("xAxisIndex",!0),yAxisIndex:t.get("yAxisIndex",!0),xAxisId:t.get("xAxisId",!0),yAxisId:t.get("yAxisId",!0)};return null==e.xAxisIndex&&null==e.xAxisId&&(e.xAxisIndex="all"),null==e.yAxisIndex&&null==e.yAxisId&&(e.yAxisIndex="all"),e}Co("dataZoom",(function(t){var e=t.getComponent("toolbox",0),n=["feature","dataZoom"];if(e&&null!=e.get(n)){var i=e.getModel(n),o=[],r=Wm(i),a=To(t,r);return zm(a.xAxisModels,(function(t){return s(t,"xAxis","xAxisIndex")})),zm(a.yAxisModels,(function(t){return s(t,"yAxis","yAxisIndex")})),o}function s(t,e,n){var r=t.componentIndex,a={type:"select",$fromToolbox:!0,filterMode:i.get("filterMode",!0)||"filter",id:Bm+e+r};a[n]=r,o.push(a)}}));var Hm=["rect","polygon","keep","clear"];function Zm(t,e){var n=nn(t?t.brush:[]);if(n.length){var i=[];a(n,(function(t){var e=t.hasOwnProperty("toolbox")?t.toolbox:[];e instanceof Array&&(i=i.concat(e))}));var o=t&&t.toolbox;mt(o)&&(o=o[0]),o||(o={feature:{}},t.toolbox=[o]);var r,s,l=o.feature||(o.feature={}),u=l.brush||(l.brush={}),d=u.type||(u.type=[]);d.push.apply(d,i),s={},a(r=d,(function(t){s[t]=1})),r.length=0,a(s,(function(t,e){r.push(e)})),e&&!d.length&&d.push.apply(d,Hm)}}var Ym=a;function Xm(t){if(t)for(var e in t)if(t.hasOwnProperty(e))return!0}function Um(t,e,n){var i={};return Ym(e,(function(e){var o,r=i[e]=((o=function(){}).prototype.__hidden=o.prototype,new o);Ym(t[e],(function(t,i){if(Ju.isValidType(i)){var o={type:i,visual:t};n&&n(o,e),r[i]=new Ju(o),"opacity"===i&&((o=ct(o)).type="colorAlpha",r.__hidden.__alphaForOpacity=new Ju(o))}}))})),i}function jm(t,e,n){var i;a(n,(function(t){e.hasOwnProperty(t)&&Xm(e[t])&&(i=!0)})),i&&a(n,(function(n){e.hasOwnProperty(n)&&Xm(e[n])?t[n]=ct(e[n]):delete t[n]}))}var qm={lineX:Km(0),lineY:Km(1),rect:{point:function(t,e,n){return t&&n.boundingRect.contain(t[0],t[1])},rect:function(t,e,n){return t&&n.boundingRect.intersect(t)}},polygon:{point:function(t,e,n){return t&&n.boundingRect.contain(t[0],t[1])&&mr(n.range,t[0],t[1])},rect:function(t,e,n){var i=n.range;if(!t||i.length<=1)return!1;var o=t.x,r=t.y,a=t.width,l=t.height,u=i[0];return!!(mr(i,o,r)||mr(i,o+a,r)||mr(i,o,r+l)||mr(i,o+a,r+l)||s.create(t).contain(u[0],u[1])||ko(o,r,o+a,r,i)||ko(o,r,o,r+l,i)||ko(o+a,r,o+a,r+l,i)||ko(o,r+l,o+a,r+l,i))||void 0}}};function Km(t){var e=["x","y"],n=["width","height"];return{point:function(e,n,i){if(e){var o=i.range;return Jm(e[t],o)}},rect:function(i,o,r){if(i){var a=r.range,s=[i[e[t]],i[e[t]]+i[n[t]]];return s[1]<s[0]&&s.reverse(),Jm(s[0],a)||Jm(s[1],a)||Jm(a[0],s)||Jm(a[1],s)}}}}function Jm(t,e){return e[0]<=t&&t<=e[1]}var $m=["inBrush","outOfBrush"],Qm="__ecBrushSelect",tx="__ecInBrushSelectEvent";function ex(t){t.eachComponent({mainType:"brush"},(function(e){(e.brushTargetManager=new Tm(e.option,t)).setInputRanges(e.areas,t)}))}function nx(t,e,n){var i,o,r=[];t.eachComponent({mainType:"brush"},(function(t){n&&"takeGlobalCursor"===n.type&&t.setBrushOption("brush"===n.key?n.brushOption:{brushType:!1})})),ex(t),t.eachComponent({mainType:"brush"},(function(e,n){var s={brushId:e.id,brushIndex:n,brushName:e.name,areas:ct(e.areas),selected:[]};r.push(s);var l=e.option,u=l.brushLink,c=[],h=[],p=[],g=!1;n||(i=l.throttleType,o=l.throttleDelay);var f=d(e.areas,(function(t){var e=rx[t.brushType],n=Ot({boundingRect:e?e(t):void 0},t);return n.selectors=function(t){var e=t.brushType,n={point:function(i){return qm[e].point(i,n,t)},rect:function(i){return qm[e].rect(i,n,t)}};return n}(n),n})),y=Um(e.option,$m,(function(t){t.mappingMethod="fixed"}));function v(t){return"all"===u||!!c[t]}function m(t){return!!t.length}mt(u)&&a(u,(function(t){c[t]=1})),t.eachSeries((function(n,i){var o=p[i]=[];"parallel"===n.subType?function(t,e){var n=t.coordinateSystem;g=g||n.hasAxisBrushed(),v(e)&&n.eachActiveState(t.getData(),(function(t,e){"active"===t&&(h[e]=1)}))}(n,i):function(n,i,o){if(!n.brushSelector||function(t,e){var n=t.option.seriesIndex;return null!=n&&"all"!==n&&(mt(n)?L(n,e)<0:e!==n)}(e,i))return;if(a(f,(function(i){e.brushTargetManager.controlSeries(i,n,t)&&o.push(i),g=g||m(o)})),v(i)&&m(o)){var r=n.getData();r.each((function(t){ox(n,o,r,t)&&(h[t]=1)}))}}(n,i,o)})),t.eachSeries((function(t,e){var n={seriesId:t.id,seriesIndex:e,seriesName:t.name,dataIndex:[]};s.selected.push(n);var i=p[e],o=t.getData(),r=v(e)?function(t){return h[t]?(n.dataIndex.push(o.getRawIndex(t)),"inBrush"):"outOfBrush"}:function(e){return ox(t,i,o,e)?(n.dataIndex.push(o.getRawIndex(e)),"inBrush"):"outOfBrush"};(v(e)?g:m(i))&&function(t,e,n,i,o){var r,s={};function l(t){return Lo(n,r,t)}function u(t,e){Po(n,r,t,e)}a(t,(function(t){var n=Ju.prepareVisualTypes(e[t]);s[t]=n})),n.each((function(t,a){r=t;var d=n.getRawDataItem(r);if(!d||!1!==d.visualMap)for(var c=i.call(o,t),h=e[c],p=s[c],g=0,f=p.length;g<f;g++){var y=p[g];h[y]&&h[y].applyVisual(t,l,u)}}))}($m,y,o,r)}))})),function(t,e,n,i,o){if(!o)return;var r=t.getZr();if(r[tx])return;r[Qm]||(r[Qm]=ix);var a=Jn(r,Qm,n,e);a(t,i)}(e,i,o,r,n)}function ix(t,e){if(!t.isDisposed()){var n=t.getZr();n[tx]=!0,t.dispatchAction({type:"brushSelect",batch:e}),n[tx]=!1}}function ox(t,e,n,i){for(var o=0,r=e.length;o<r;o++){var a=e[o];if(t.brushSelector(i,n,a.selectors,a))return!0}}var rx={rect:function(t){return ax(t.range)},polygon:function(t){for(var e,n=t.range,i=0,o=n.length;i<o;i++){e=e||[[1/0,-1/0],[1/0,-1/0]];var r=n[i];r[0]<e[0][0]&&(e[0][0]=r[0]),r[0]>e[0][1]&&(e[0][1]=r[0]),r[1]<e[1][0]&&(e[1][0]=r[1]),r[1]>e[1][1]&&(e[1][1]=r[1])}return e&&ax(e)}};function ax(t){return new s(t[0][0],t[1][0],t[0][1]-t[0][0],t[1][1]-t[1][0])}var sx=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.init=function(t,e){this.ecModel=t,this.api=e,this.model,(this._brushController=new Dh(e.getZr())).on("brush",Zt(this._onBrush,this)).mount()},n.prototype.render=function(t,e,n,i){this.model=t,this._updateController(t,e,n,i)},n.prototype.updateTransform=function(t,e,n,i){ex(e),this._updateController(t,e,n,i)},n.prototype.updateVisual=function(t,e,n,i){this.updateTransform(t,e,n,i)},n.prototype.updateView=function(t,e,n,i){this._updateController(t,e,n,i)},n.prototype._updateController=function(t,e,n,i){(!i||i.$from!==t.id)&&this._brushController.setPanels(t.brushTargetManager.makePanelOpts(n)).enableBrush(t.brushOption).updateCovers(t.areas.slice())},n.prototype.dispose=function(){this._brushController.dispose()},n.prototype._onBrush=function(t){var e=this.model.id,n=this.model.brushTargetManager.setOutputRanges(t.areas,this.ecModel);(!t.isEnd||t.removeOnClick)&&this.api.dispatchAction({type:"brush",brushId:e,areas:ct(n),$from:e}),t.isEnd&&this.api.dispatchAction({type:"brushEnd",brushId:e,areas:ct(n),$from:e})},n.type="brush",n}(oe),lx=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.areas=[],e.brushOption={},e}return e(n,t),n.prototype.optionUpdated=function(t,e){var n=this.option;!e&&jm(n,t,["inBrush","outOfBrush"]);var i=n.inBrush=n.inBrush||{};n.outOfBrush=n.outOfBrush||{color:"#ddd"},i.hasOwnProperty("liftZ")||(i.liftZ=5)},n.prototype.setAreas=function(t){t&&(this.areas=d(t,(function(t){return ux(this.option,t)}),this))},n.prototype.setBrushOption=function(t){this.brushOption=ux(this.option,t),this.brushType=this.brushOption.brushType},n.type="brush",n.dependencies=["geo","grid","xAxis","yAxis","parallel","series"],n.defaultOption={seriesIndex:"all",brushType:"rect",brushMode:"single",transformable:!0,brushStyle:{borderWidth:1,color:"rgba(210,219,238,0.3)",borderColor:"#D2DBEE"},throttleType:"fixRate",throttleDelay:0,removeOnClick:!0,z:1e4},n}(Qt);function ux(t,e){return jt({brushType:t.brushType,brushMode:t.brushMode,transformable:t.transformable,brushStyle:new qt(t.brushStyle).getItemStyle(),removeOnClick:t.removeOnClick,z:t.z},e,!0)}var dx=["rect","polygon","lineX","lineY","keep","clear"],cx=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return e(n,t),n.prototype.render=function(t,e,n){var i,o,r;e.eachComponent({mainType:"brush"},(function(t){i=t.brushType,o=t.brushOption.brushMode||"single",r=r||!!t.areas.length})),this._brushType=i,this._brushMode=o,a(t.get("type",!0),(function(e){t.setIconStatus(e,("keep"===e?"multiple"===o:"clear"===e?r:e===i)?"emphasis":"normal")}))},n.prototype.updateView=function(t,e,n){this.render(t,e,n)},n.prototype.getIcons=function(){var t=this.model,e=t.get("icon",!0),n={};return a(t.get("type",!0),(function(t){e[t]&&(n[t]=e[t])})),n},n.prototype.onclick=function(t,e,n){var i=this._brushType,o=this._brushMode;"clear"===n?(e.dispatchAction({type:"axisAreaSelect",intervals:[]}),e.dispatchAction({type:"brush",command:"clear",areas:[]})):e.dispatchAction({type:"takeGlobalCursor",key:"brush",brushOption:{brushType:"keep"===n?i:i!==n&&n,brushMode:"keep"===n?"multiple"===o?"single":"multiple":o}})},n.getDefaultOption=function(t){return{show:!0,type:dx.slice(),icon:{rect:"M7.3,34.7 M0.4,10V-0.2h9.8 M89.6,10V-0.2h-9.8 M0.4,60v10.2h9.8 M89.6,60v10.2h-9.8 M12.3,22.4V10.5h13.1 M33.6,10.5h7.8 M49.1,10.5h7.8 M77.5,22.4V10.5h-13 M12.3,31.1v8.2 M77.7,31.1v8.2 M12.3,47.6v11.9h13.1 M33.6,59.5h7.6 M49.1,59.5 h7.7 M77.5,47.6v11.9h-13",polygon:"M55.2,34.9c1.7,0,3.1,1.4,3.1,3.1s-1.4,3.1-3.1,3.1 s-3.1-1.4-3.1-3.1S53.5,34.9,55.2,34.9z M50.4,51c1.7,0,3.1,1.4,3.1,3.1c0,1.7-1.4,3.1-3.1,3.1c-1.7,0-3.1-1.4-3.1-3.1 C47.3,52.4,48.7,51,50.4,51z M55.6,37.1l1.5-7.8 M60.1,13.5l1.6-8.7l-7.8,4 M59,19l-1,5.3 M24,16.1l6.4,4.9l6.4-3.3 M48.5,11.6 l-5.9,3.1 M19.1,12.8L9.7,5.1l1.1,7.7 M13.4,29.8l1,7.3l6.6,1.6 M11.6,18.4l1,6.1 M32.8,41.9 M26.6,40.4 M27.3,40.2l6.1,1.6 M49.9,52.1l-5.6-7.6l-4.9-1.2",lineX:"M15.2,30 M19.7,15.6V1.9H29 M34.8,1.9H40.4 M55.3,15.6V1.9H45.9 M19.7,44.4V58.1H29 M34.8,58.1H40.4 M55.3,44.4 V58.1H45.9 M12.5,20.3l-9.4,9.6l9.6,9.8 M3.1,29.9h16.5 M62.5,20.3l9.4,9.6L62.3,39.7 M71.9,29.9H55.4",lineY:"M38.8,7.7 M52.7,12h13.2v9 M65.9,26.6V32 M52.7,46.3h13.2v-9 M24.9,12H11.8v9 M11.8,26.6V32 M24.9,46.3H11.8v-9 M48.2,5.1l-9.3-9l-9.4,9.2 M38.9-3.9V12 M48.2,53.3l-9.3,9l-9.4-9.2 M38.9,62.3V46.4",keep:"M4,10.5V1h10.3 M20.7,1h6.1 M33,1h6.1 M55.4,10.5V1H45.2 M4,17.3v6.6 M55.6,17.3v6.6 M4,30.5V40h10.3 M20.7,40 h6.1 M33,40h6.1 M55.4,30.5V40H45.2 M21,18.9h62.9v48.6H21V18.9z",clear:"M22,14.7l30.9,31 M52.9,14.7L22,45.7 M4.7,16.8V4.2h13.1 M26,4.2h7.8 M41.6,4.2h7.8 M70.3,16.8V4.2H57.2 M4.7,25.9v8.6 M70.3,25.9v8.6 M4.7,43.2v12.6h13.1 M26,55.8h7.8 M41.6,55.8h7.8 M70.3,43.2v12.6H57.2"},title:t.getLocaleModel().get(["toolbox","brush","title"])}},n}(om);var hx=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.layoutMode="box",e}return e(n,t),n.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n),this._initData()},n.prototype.mergeOption=function(e){t.prototype.mergeOption.apply(this,arguments),this._initData()},n.prototype.setCurrentIndex=function(t){null==t&&(t=this.option.currentIndex);var e=this._data.count();this.option.loop?t=(t%e+e)%e:(t>=e&&(t=e-1),t<0&&(t=0)),this.option.currentIndex=t},n.prototype.getCurrentIndex=function(){return this.option.currentIndex},n.prototype.isIndexMax=function(){return this.getCurrentIndex()>=this._data.count()-1},n.prototype.setPlayState=function(t){this.option.autoPlay=!!t},n.prototype.getPlayState=function(){return!!this.option.autoPlay},n.prototype._initData=function(){var t,e=this.option,n=e.data||[],i=e.axisType,o=this._names=[];"category"===i?(t=[],a(n,(function(e,n){var i,r=$e(No(e),"");xt(e)?(i=ct(e)).value=n:i=n,t.push(i),o.push(r)}))):t=n;var r={category:"ordinal",time:"time",value:"number"}[i]||"number";(this._data=new tn([{name:"value",type:r}],this)).initData(t,o)},n.prototype.getData=function(){return this._data},n.prototype.getCategories=function(){if("category"===this.get("axisType"))return this._names.slice()},n.type="timeline",n.defaultOption={z:4,show:!0,axisType:"time",realtime:!0,left:"20%",top:null,right:"20%",bottom:0,width:null,height:40,padding:5,controlPosition:"left",autoPlay:!1,rewind:!1,loop:!0,playInterval:2e3,currentIndex:0,itemStyle:{},label:{color:"#000"},data:[]},n}(Qt),px=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.type="timeline.slider",n.defaultOption=Ti(hx.defaultOption,{backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,orient:"horizontal",inverse:!1,tooltip:{trigger:"item"},symbol:"circle",symbolSize:12,lineStyle:{show:!0,width:2,color:"#DAE1F5"},label:{position:"auto",show:!0,interval:"auto",rotate:0,color:"#A4B1D7"},itemStyle:{color:"#A4B1D7",borderWidth:1},checkpointStyle:{symbol:"circle",symbolSize:15,color:"#316bf3",borderColor:"#fff",borderWidth:2,shadowBlur:2,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0, 0, 0, 0.3)",animation:!0,animationDuration:300,animationEasing:"quinticInOut"},controlStyle:{show:!0,showPlayBtn:!0,showPrevBtn:!0,showNextBtn:!0,itemSize:24,itemGap:12,position:"left",playIcon:"path://M31.6,53C17.5,53,6,41.5,6,27.4S17.5,1.8,31.6,1.8C45.7,1.8,57.2,13.3,57.2,27.4S45.7,53,31.6,53z M31.6,3.3 C18.4,3.3,7.5,14.1,7.5,27.4c0,13.3,10.8,24.1,24.1,24.1C44.9,51.5,55.7,40.7,55.7,27.4C55.7,14.1,44.9,3.3,31.6,3.3z M24.9,21.3 c0-2.2,1.6-3.1,3.5-2l10.5,6.1c1.899,1.1,1.899,2.9,0,4l-10.5,6.1c-1.9,1.1-3.5,0.2-3.5-2V21.3z",stopIcon:"path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5C17.6,3.5,6.8,14.4,6.8,27.6c0,13.3,10.8,24.1,24.101,24.1C44.2,51.7,55,40.9,55,27.6C54.9,14.4,44.1,3.5,30.9,3.5z M36.9,35.8c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H36c0.5,0,0.9,0.4,0.9,1V35.8z M27.8,35.8 c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H27c0.5,0,0.9,0.4,0.9,1L27.8,35.8L27.8,35.8z",nextIcon:"M2,18.5A1.52,1.52,0,0,1,.92,18a1.49,1.49,0,0,1,0-2.12L7.81,9.36,1,3.11A1.5,1.5,0,1,1,3,.89l8,7.34a1.48,1.48,0,0,1,.49,1.09,1.51,1.51,0,0,1-.46,1.1L3,18.08A1.5,1.5,0,0,1,2,18.5Z",prevIcon:"M10,.5A1.52,1.52,0,0,1,11.08,1a1.49,1.49,0,0,1,0,2.12L4.19,9.64,11,15.89a1.5,1.5,0,1,1-2,2.22L1,10.77A1.48,1.48,0,0,1,.5,9.68,1.51,1.51,0,0,1,1,8.58L9,.92A1.5,1.5,0,0,1,10,.5Z",prevBtnSize:18,nextBtnSize:18,color:"#A4B1D7",borderColor:"#A4B1D7",borderWidth:1},emphasis:{label:{show:!0,color:"#6f778d"},itemStyle:{color:"#316BF3"},controlStyle:{color:"#316BF3",borderColor:"#316BF3",borderWidth:2}},progress:{lineStyle:{color:"#316BF3"},itemStyle:{color:"#316BF3"},label:{color:"#6f778d"}},data:[]}),n}(hx);Kt(px,Ro.prototype);var gx=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.type="timeline",n}(oe),fx=function(t){function n(e,n,i,o){var r=t.call(this,e,n,i)||this;return r.type=o||"value",r}return e(n,t),n.prototype.getLabelModel=function(){return this.model.getModel("label")},n.prototype.isHorizontal=function(){return"horizontal"===this.model.get("orient")},n}(re),yx=Math.PI,vx=h(),mx=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.init=function(t,e){this.api=e},n.prototype.render=function(t,e,n){if(this.model=t,this.api=n,this.ecModel=e,this.group.removeAll(),t.get("show",!0)){var i=this._layout(t,n),o=this._createGroup("_mainGroup"),r=this._createGroup("_labelGroup"),s=this._axis=this._createAxis(i,t);t.formatTooltip=function(t){var e=s.scale.getLabel({value:t});return Ut("nameValue",{noName:!0,value:e})},a(["AxisLine","AxisTick","Control","CurrentPointer"],(function(e){this["_render"+e](i,o,s,t)}),this),this._renderAxisLabel(i,r,s,t),this._position(i,t)}this._doPlayStop(),this._updateTicksStatus()},n.prototype.remove=function(){this._clearTimer(),this.group.removeAll()},n.prototype.dispose=function(){this._clearTimer()},n.prototype._layout=function(t,e){var n,i,o,r,a=t.get(["label","position"]),s=t.get("orient"),l=function(t,e){return Fe(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()},t.get("padding"))}(t,e),u={horizontal:"center",vertical:(n=null==a||"auto"===a?"horizontal"===s?l.y+l.height/2<e.getHeight()/2?"-":"+":l.x+l.width/2<e.getWidth()/2?"+":"-":G(a)?{horizontal:{top:"-",bottom:"+"},vertical:{left:"-",right:"+"}}[s][a]:a)>=0||"+"===n?"left":"right"},d={horizontal:n>=0||"+"===n?"top":"bottom",vertical:"middle"},c={horizontal:0,vertical:yx/2},h="vertical"===s?l.height:l.width,p=t.getModel("controlStyle"),g=p.get("show",!0),f=g?p.get("itemSize"):0,y=g?p.get("itemGap"):0,v=f+y,m=t.get(["label","rotate"])||0;m=m*yx/180;var x=p.get("position",!0),_=g&&p.get("showPlayBtn",!0),b=g&&p.get("showPrevBtn",!0),S=g&&p.get("showNextBtn",!0),w=0,M=h;"left"===x||"bottom"===x?(_&&(i=[0,0],w+=v),b&&(o=[w,0],w+=v),S&&(r=[M-f,0],M-=v)):(_&&(i=[M-f,0],M-=v),b&&(o=[0,0],w+=v),S&&(r=[M-f,0],M-=v));var I=[w,M];return t.get("inverse")&&I.reverse(),{viewRect:l,mainLength:h,orient:s,rotation:c[s],labelRotation:m,labelPosOpt:n,labelAlign:t.get(["label","align"])||u[s],labelBaseline:t.get(["label","verticalAlign"])||t.get(["label","baseline"])||d[s],playPosition:i,prevBtnPosition:o,nextBtnPosition:r,axisExtent:I,controlSize:f,controlGap:y}},n.prototype._position=function(t,e){var n=this._mainGroup,i=this._labelGroup,o=t.viewRect;if("vertical"===t.orient){var r=Me(),a=o.x,s=o.y+o.height;we(r,r,[-a,-s]),be(r,r,-yx/2),we(r,r,[a,s]),(o=o.clone()).applyTransform(r)}var l=y(o),u=y(n.getBoundingRect()),d=y(i.getBoundingRect()),c=[n.x,n.y],h=[i.x,i.y];h[0]=c[0]=l[0][0];var p,g=t.labelPosOpt;null==g||G(g)?(v(c,u,l,1,p="+"===g?0:1),v(h,d,l,1,1-p)):(v(c,u,l,1,p=g>=0?0:1),h[1]=c[1]+g);function f(t){t.originX=l[0][0]-t.x,t.originY=l[1][0]-t.y}function y(t){return[[t.x,t.x+t.width],[t.y,t.y+t.height]]}function v(t,e,n,i,o){t[i]+=n[i][o]-e[i][o]}n.setPosition(c),i.setPosition(h),n.rotation=i.rotation=t.rotation,f(n),f(i)},n.prototype._createAxis=function(t,e){var n=e.getData(),i=e.get("axisType"),o=function(t,e){if(e=e||t.get("type"),e)switch(e){case"category":return new zo({ordinalMeta:t.getCategories(),extent:[1/0,-1/0]});case"time":return new Eo({locale:t.ecModel.getLocaleModel(),useUTC:t.ecModel.get("useUTC")});default:return new ae}}(e,i);o.getTicks=function(){return n.mapArray(["value"],(function(t){return{value:t}}))};var r=n.getDataExtent("value");o.setExtent(r[0],r[1]),o.calcNiceTicks();var a=new fx("value",o,t.axisExtent,i);return a.model=e,a},n.prototype._createGroup=function(t){var e=this[t]=new It;return this.group.add(e),e},n.prototype._renderAxisLine=function(t,e,n,i){var o=n.getExtent();if(i.get(["lineStyle","show"])){var r=new ve({shape:{x1:o[0],y1:0,x2:o[1],y2:0},style:H({lineCap:"round"},i.getModel("lineStyle").getLineStyle()),silent:!0,z2:1});e.add(r);var a=this._progressLine=new ve({shape:{x1:o[0],x2:this._currentPointer?this._currentPointer.x:o[0],y1:0,y2:0},style:Ot({lineCap:"round",lineWidth:r.style.lineWidth},i.getModel(["progress","lineStyle"]).getLineStyle()),silent:!0,z2:1});e.add(a)}},n.prototype._renderAxisTick=function(t,e,n,i){var o=this,r=i.getData(),s=n.scale.getTicks();this._tickSymbols=[],a(s,(function(t){var a=n.dataToCoord(t.value),s=r.getItemModel(t.value),l=s.getModel("itemStyle"),u=s.getModel(["emphasis","itemStyle"]),d=s.getModel(["progress","itemStyle"]),c={x:a,y:0,onclick:Zt(o._changeTimeline,o,t.value)},h=xx(s,l,e,c);h.ensureState("emphasis").style=u.getItemStyle(),h.ensureState("progress").style=d.getItemStyle(),Vo(h);var p=v(h);s.get("tooltip")?(p.dataIndex=t.value,p.dataModel=i):p.dataIndex=p.dataModel=null,o._tickSymbols.push(h)}))},n.prototype._renderAxisLabel=function(t,e,n,i){var o=this;if(n.getLabelModel().get("show")){var r=i.getData(),s=n.getViewLabels();this._tickLabels=[],a(s,(function(i){var a=i.tickValue,s=r.getItemModel(a),l=s.getModel("label"),u=s.getModel(["emphasis","label"]),d=s.getModel(["progress","label"]),c=n.dataToCoord(i.tickValue),h=new an({x:c,y:0,rotation:t.labelRotation-t.rotation,onclick:Zt(o._changeTimeline,o,a),silent:!1,style:sn(l,{text:i.formattedLabel,align:t.labelAlign,verticalAlign:t.labelBaseline})});h.ensureState("emphasis").style=sn(u),h.ensureState("progress").style=sn(d),e.add(h),Vo(h),vx(h).dataIndex=a,o._tickLabels.push(h)}))}},n.prototype._renderControl=function(t,e,n,i){var o=t.controlSize,r=t.rotation,a=i.getModel("controlStyle").getItemStyle(),l=i.getModel(["emphasis","controlStyle"]).getItemStyle(),u=i.getPlayState(),d=i.get("inverse",!0);function c(t,n,u,d){if(t){var c=Oo(C(i.get(["controlStyle",n+"BtnSize"]),o),o),h=function(t,e,n,i){var o=i.style,r=bo(t.get(["controlStyle",e]),i||{},new s(n[0],n[1],n[2],n[3]));o&&r.setStyle(o);return r}(i,n+"Icon",[0,-c/2,c,c],{x:t[0],y:t[1],originX:o/2,originY:0,rotation:d?-r:0,rectHover:!0,style:a,onclick:u});h.ensureState("emphasis").style=l,e.add(h),Vo(h)}}c(t.nextBtnPosition,"next",Zt(this._changeTimeline,this,d?"-":"+")),c(t.prevBtnPosition,"prev",Zt(this._changeTimeline,this,d?"+":"-")),c(t.playPosition,u?"stop":"play",Zt(this._handlePlayClick,this,!u),!0)},n.prototype._renderCurrentPointer=function(t,e,n,i){var o=i.getData(),r=i.getCurrentIndex(),a=o.getItemModel(r).getModel("checkpointStyle"),s=this,l={onCreate:function(t){t.draggable=!0,t.drift=Zt(s._handlePointerDrag,s),t.ondragend=Zt(s._handlePointerDragend,s),_x(t,s._progressLine,r,n,i,!0)},onUpdate:function(t){_x(t,s._progressLine,r,n,i)}};this._currentPointer=xx(a,a,this._mainGroup,{},this._currentPointer,l)},n.prototype._handlePlayClick=function(t){this._clearTimer(),this.api.dispatchAction({type:"timelinePlayChange",playState:t,from:this.uid})},n.prototype._handlePointerDrag=function(t,e,n){this._clearTimer(),this._pointerChangeTimeline([n.offsetX,n.offsetY])},n.prototype._handlePointerDragend=function(t){this._pointerChangeTimeline([t.offsetX,t.offsetY],!0)},n.prototype._pointerChangeTimeline=function(t,e){var n=this._toAxisCoord(t)[0],i=this._axis,o=ni(i.getExtent().slice());n>o[1]&&(n=o[1]),n<o[0]&&(n=o[0]),this._currentPointer.x=n,this._currentPointer.markRedraw();var r=this._progressLine;r&&(r.shape.x2=n,r.dirty());var a=this._findNearestTick(n),s=this.model;(e||a!==s.getCurrentIndex()&&s.get("realtime"))&&this._changeTimeline(a)},n.prototype._doPlayStop=function(){var t=this;this._clearTimer(),this.model.getPlayState()&&(this._timer=setTimeout((function(){var e=t.model;t._changeTimeline(e.getCurrentIndex()+(e.get("rewind",!0)?-1:1))}),this.model.get("playInterval")))},n.prototype._toAxisCoord=function(t){var e=this._mainGroup.getLocalTransform();return ei(t,e,!0)},n.prototype._findNearestTick=function(t){var e,n=this.model.getData(),i=1/0,o=this._axis;return n.each(["value"],(function(n,r){var a=o.dataToCoord(n),s=Math.abs(a-t);s<i&&(i=s,e=r)})),e},n.prototype._clearTimer=function(){this._timer&&(clearTimeout(this._timer),this._timer=null)},n.prototype._changeTimeline=function(t){var e=this.model.getCurrentIndex();"+"===t?t=e+1:"-"===t&&(t=e-1),this.api.dispatchAction({type:"timelineChange",currentIndex:t,from:this.uid})},n.prototype._updateTicksStatus=function(){var t=this.model.getCurrentIndex(),e=this._tickSymbols,n=this._tickLabels;if(e)for(var i=0;i<e.length;i++)e&&e[i]&&e[i].toggleState("progress",i<t);if(n)for(i=0;i<n.length;i++)n&&n[i]&&n[i].toggleState("progress",vx(n[i]).dataIndex<=t)},n.type="timeline.slider",n}(gx);function xx(t,e,n,i,o,r){var a=e.get("color");if(o)o.setColor(a),n.add(o),r&&r.onUpdate(o);else{var s=t.get("symbol");(o=At(s,-1,-1,2,2,a)).setStyle("strokeNoScale",!0),n.add(o),r&&r.onCreate(o)}var l=e.getItemStyle(["color"]);o.setStyle(l),i=jt({rectHover:!0,z2:100},i,!0);var u=Wt(t.get("symbolSize"));i.scaleX=u[0]/2,i.scaleY=u[1]/2;var d=Vn(t.get("symbolOffset"),u);d&&(i.x=(i.x||0)+d[0],i.y=(i.y||0)+d[1]);var c=t.get("symbolRotate");return i.rotation=(c||0)*Math.PI/180||0,o.attr(i),o.updateTransform(),o}function _x(t,e,n,i,o,r){if(!t.dragging){var a=o.getModel("checkpointStyle"),s=i.dataToCoord(o.getData().get("value",n));if(r||!a.get("animation",!0))t.attr({x:s,y:0}),e&&e.attr({shape:{x2:s}});else{var l={duration:a.get("animationDuration",!0),easing:a.get("animationEasing",!0)};t.stopAnimation(null,!0),t.animateTo({x:s,y:0},l),e&&e.animateTo({shape:{x2:s}},l)}}}function bx(t){var e=t&&t.timeline;mt(e)||(e=e?[e]:[]),a(e,(function(t){t&&function(t){var e=t.type,n={number:"value",time:"time"};n[e]&&(t.axisType=n[e],delete t.type);if(Sx(t),wx(t,"controlPosition")){var i=t.controlStyle||(t.controlStyle={});wx(i,"position")||(i.position=t.controlPosition),"none"!==i.position||wx(i,"show")||(i.show=!1,delete i.position),delete t.controlPosition}a(t.data||[],(function(t){xt(t)&&!mt(t)&&(!wx(t,"value")&&wx(t,"name")&&(t.value=t.name),Sx(t))}))}(t)}))}function Sx(t){var e=t.itemStyle||(t.itemStyle={}),n=e.emphasis||(e.emphasis={}),i=t.label||t.label||{},o=i.normal||(i.normal={}),r={normal:1,emphasis:1};a(i,(function(t,e){r[e]||wx(o,e)||(o[e]=t)})),n.label&&!wx(i,"emphasis")&&(i.emphasis=n.label,delete n.label)}function wx(t,e){return t.hasOwnProperty(e)}function Mx(t,e){if(!t)return!1;for(var n=mt(t)?t:[t],i=0;i<n.length;i++)if(n[i]&&n[i][e])return!0;return!1}function Ix(t){We(t,"label",["show"])}var Ax=h(),Tx=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.createdBySelf=!1,e}return e(n,t),n.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n),this._mergeOption(t,n,!1,!0)},n.prototype.isAnimationEnabled=function(){if(Mo.node)return!1;var t=this.__hostSeries;return this.getShallow("animation")&&t&&t.isAnimationEnabled()},n.prototype.mergeOption=function(t,e){this._mergeOption(t,e,!1,!1)},n.prototype._mergeOption=function(t,e,n,i){var o=this.mainType;n||e.eachSeries((function(t){var n=t.get(this.mainType,!0),r=Ax(t)[o];n&&n.data?(r?r._mergeOption(n,e,!0):(i&&Ix(n),a(n.data,(function(t){t instanceof Array?(Ix(t[0]),Ix(t[1])):Ix(t)})),r=this.createMarkerModelFromSeries(n,this,e),H(r,{mainType:this.mainType,seriesIndex:t.seriesIndex,name:t.name,createdBySelf:!0}),r.__hostSeries=t),Ax(t)[o]=r):Ax(t)[o]=null}),this)},n.prototype.formatTooltip=function(t,e,n){var i=this.getData(),o=this.getRawValue(t),r=i.getName(t);return Ut("section",{header:this.name,blocks:[Ut("nameValue",{name:r,value:o,noName:!r,noValue:null==o})]})},n.prototype.getData=function(){return this._data},n.prototype.setData=function(t){this._data=t},n.prototype.getDataParams=function(t,e){var n=Ro.prototype.getDataParams.call(this,t,e),i=this.__hostSeries;return i&&(n.seriesId=i.id,n.seriesName=i.name,n.seriesType=i.subType),n},n.getMarkerModelFromSeries=function(t,e){return Ax(t)[e]},n.type="marker",n.dependencies=["series","grid","polar","geo"],n}(Qt);Kt(Tx,Ro.prototype);var Cx=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.createMarkerModelFromSeries=function(t,e,i){return new n(t,e,i)},n.type="markPoint",n.defaultOption={z:5,symbol:"pin",symbolSize:50,tooltip:{trigger:"item"},label:{show:!0,position:"inside"},itemStyle:{borderWidth:2},emphasis:{label:{show:!0}}},n}(Tx);function Dx(t){return!(isNaN(parseFloat(t.x))&&isNaN(parseFloat(t.y)))}function Lx(t,e,n,i,o,r){var a=[],s=Qi(e,i)?e.getCalculationInfo("stackResultDimension"):i,l=Ox(e,s,t),u=e.indicesOfNearest(s,l)[0];a[o]=e.get(n,u),a[r]=e.get(s,u);var d=e.get(i,u),c=Go(e.get(i,u));return(c=Math.min(c,20))>=0&&(a[r]=+a[r].toFixed(c)),[a,d]}var Px={min:Re(Lx,"min"),max:Re(Lx,"max"),average:Re(Lx,"average"),median:Re(Lx,"median")};function kx(t,e){if(e){var n=t.getData(),i=t.coordinateSystem,o=i&&i.dimensions;if(!function(t){return!isNaN(parseFloat(t.x))&&!isNaN(parseFloat(t.y))}(e)&&!mt(e.coord)&&mt(o)){var r=Nx(e,n,i,t);if((e=ct(e)).type&&Px[e.type]&&r.baseAxis&&r.valueAxis){var a=L(o,r.baseAxis.dim),s=L(o,r.valueAxis.dim),l=Px[e.type](n,r.baseDataDim,r.valueDataDim,a,s);e.coord=l[0],e.value=l[1]}else e.coord=[null!=e.xAxis?e.xAxis:e.radiusAxis,null!=e.yAxis?e.yAxis:e.angleAxis]}if(null!=e.coord&&mt(o))for(var u=e.coord,d=0;d<2;d++)Px[u[d]]&&(u[d]=Ox(n,n.mapDimension(o[d]),u[d]));else e.coord=[];return e}}function Nx(t,e,n,i){var o={};return null!=t.valueIndex||null!=t.valueDim?(o.valueDataDim=null!=t.valueIndex?e.getDimension(t.valueIndex):t.valueDim,o.valueAxis=n.getAxis(function(t,e){var n=t.getData().getDimensionInfo(e);return n&&n.coordDim}(i,o.valueDataDim)),o.baseAxis=n.getOtherAxis(o.valueAxis),o.baseDataDim=e.mapDimension(o.baseAxis.dim)):(o.baseAxis=i.getBaseAxis(),o.valueAxis=n.getOtherAxis(o.baseAxis),o.baseDataDim=e.mapDimension(o.baseAxis.dim),o.valueDataDim=e.mapDimension(o.valueAxis.dim)),o}function Rx(t,e){return!(t&&t.containData&&e.coord&&!Dx(e))||t.containData(e.coord)}function Vx(t,e){return t?function(t,n,i,o){var r=o<2?t.coord&&t.coord[o]:t.value;return Bo(r,e[o])}:function(t,n,i,o){return Bo(t.value,e[o])}}function Ox(t,e,n){if("average"===n){var i=0,o=0;return t.each(e,(function(t,e){isNaN(t)||(i+=t,o++)})),i/o}return"median"===n?t.getMedian(e):t.getDataExtent(e)["max"===n?1:0]}var Ex=h(),zx=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.init=function(){this.markerGroupMap=Ie()},n.prototype.render=function(t,e,n){var i=this,o=this.markerGroupMap;o.each((function(t){Ex(t).keep=!1})),e.eachSeries((function(t){var o=Tx.getMarkerModelFromSeries(t,i.type);o&&i.renderSeries(t,o,e,n)})),o.each((function(t){!Ex(t).keep&&i.group.remove(t.group)}))},n.prototype.markKeep=function(t){Ex(t).keep=!0},n.prototype.toggleBlurSeries=function(t,e){var n=this;a(t,(function(t){var i=Tx.getMarkerModelFromSeries(t,n.type);i&&i.getData().eachItemGraphicEl((function(t){t&&(e?Fo(t):Wo(t))}))}))},n.type="marker",n}(oe);function Bx(t,e,n){var i=e.coordinateSystem;t.each((function(o){var r,a=t.getItemModel(o),s=m(a.get("x"),n.getWidth()),l=m(a.get("y"),n.getHeight());if(isNaN(s)||isNaN(l)){if(e.getMarkerPosition)r=e.getMarkerPosition(t.getValues(t.dimensions,o));else if(i){var u=t.get(i.dimensions[0],o),d=t.get(i.dimensions[1],o);r=i.dataToPoint([u,d])}}else r=[s,l];isNaN(s)||(r[0]=s),isNaN(l)||(r[1]=l),t.setItemLayout(o,r)}))}var Gx=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.updateTransform=function(t,e,n){e.eachSeries((function(t){var e=Tx.getMarkerModelFromSeries(t,"markPoint");e&&(Bx(e.getData(),t,n),this.markerGroupMap.get(t.id).updateLayout())}),this)},n.prototype.renderSeries=function(t,e,n,i){var o=t.coordinateSystem,r=t.id,a=t.getData(),s=this.markerGroupMap,l=s.get(r)||s.set(r,new Ct),u=function(t,e,n){var i;i=t?d(t&&t.dimensions,(function(t){var n=e.getData().getDimensionInfo(e.getData().mapDimension(t))||{};return H(H({},n),{name:t,ordinalMeta:null})})):[{name:"value",type:"float"}];var o=new tn(i,n),r=d(n.get("data"),Re(kx,e));t&&(r=c(r,Re(Rx,t)));var a=Vx(!!t,i);return o.initData(r,null,a),o}(o,t,e);e.setData(u),Bx(e.getData(),t,i),u.each((function(t){var n=u.getItemModel(t),i=n.getShallow("symbol"),o=n.getShallow("symbolSize"),r=n.getShallow("symbolRotate"),s=n.getShallow("symbolOffset"),l=n.getShallow("symbolKeepAspect");if(f(i)||f(o)||f(r)||f(s)){var d=e.getRawValue(t),c=e.getDataParams(t);f(i)&&(i=i(d,c)),f(o)&&(o=o(d,c)),f(r)&&(r=r(d,c)),f(s)&&(s=s(d,c))}var h=n.getModel("itemStyle").getItemStyle(),p=Ho(a,"color");h.fill||(h.fill=p),u.setItemVisual(t,{symbol:i,symbolSize:o,symbolRotate:r,symbolOffset:s,symbolKeepAspect:l,style:h})})),l.updateData(u),this.group.add(l.group),u.eachItemGraphicEl((function(t){t.traverse((function(t){v(t).dataModel=e}))})),this.markKeep(l),l.group.silent=e.get("silent")||t.get("silent")},n.type="markPoint",n}(zx);var Fx=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.createMarkerModelFromSeries=function(t,e,i){return new n(t,e,i)},n.type="markLine",n.defaultOption={z:5,symbol:["circle","arrow"],symbolSize:[8,16],symbolOffset:0,precision:2,tooltip:{trigger:"item"},label:{show:!0,position:"end",distance:5},lineStyle:{type:"dashed"},emphasis:{label:{show:!0},lineStyle:{width:3}},animationEasing:"linear"},n}(Tx),Wx=h(),Hx=function(t,e,n,i){var o,r=t.getData();if(mt(i))o=i;else{var a=i.type;if("min"===a||"max"===a||"average"===a||"median"===a||null!=i.xAxis||null!=i.yAxis){var s=void 0,l=void 0;if(null!=i.yAxis||null!=i.xAxis)s=e.getAxis(null!=i.yAxis?"y":"x"),l=Ge(i.yAxis,i.xAxis);else{var u=Nx(i,r,e,t);s=u.valueAxis,l=Ox(r,Zo(r,u.valueDataDim),a)}var d="x"===s.dim?0:1,c=1-d,h=ct(i),p={coord:[]};h.type=null,h.coord=[],h.coord[c]=-1/0,p.coord[c]=1/0;var g=n.get("precision");g>=0&&Y(l)&&(l=+l.toFixed(Math.min(g,20))),h.coord[d]=p.coord[d]=l,o=[h,p,{type:a,valueIndex:i.valueIndex,value:l}]}else o=[]}var f=[kx(t,o[0]),kx(t,o[1]),H({},o[2])];return f[2].type=f[2].type||null,jt(f[2],f[0]),jt(f[2],f[1]),f};function Zx(t){return!isNaN(t)&&!isFinite(t)}function Yx(t,e,n,i){var o=1-t,r=i.dimensions[t];return Zx(e[o])&&Zx(n[o])&&e[t]===n[t]&&i.getAxis(r).containData(e[t])}function Xx(t,e){if("cartesian2d"===t.type){var n=e[0].coord,i=e[1].coord;if(n&&i&&(Yx(1,n,i,t)||Yx(0,n,i,t)))return!0}return Rx(t,e[0])&&Rx(t,e[1])}function Ux(t,e,n,i,o){var r,a=i.coordinateSystem,s=t.getItemModel(e),l=m(s.get("x"),o.getWidth()),u=m(s.get("y"),o.getHeight());if(isNaN(l)||isNaN(u)){if(i.getMarkerPosition)r=i.getMarkerPosition(t.getValues(t.dimensions,e));else{var d=a.dimensions,c=t.get(d[0],e),h=t.get(d[1],e);r=a.dataToPoint([c,h])}if(Mi(a,"cartesian2d")){var p=a.getAxis("x"),g=a.getAxis("y");d=a.dimensions;Zx(t.get(d[0],e))?r[0]=p.toGlobalCoord(p.getExtent()[n?0:1]):Zx(t.get(d[1],e))&&(r[1]=g.toGlobalCoord(g.getExtent()[n?0:1]))}isNaN(l)||(r[0]=l),isNaN(u)||(r[1]=u)}else r=[l,u];t.setItemLayout(e,r)}var jx=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.updateTransform=function(t,e,n){e.eachSeries((function(t){var e=Tx.getMarkerModelFromSeries(t,"markLine");if(e){var i=e.getData(),o=Wx(e).from,r=Wx(e).to;o.each((function(e){Ux(o,e,!0,t,n),Ux(r,e,!1,t,n)})),i.each((function(t){i.setItemLayout(t,[o.getItemLayout(t),r.getItemLayout(t)])})),this.markerGroupMap.get(t.id).updateLayout()}}),this)},n.prototype.renderSeries=function(t,e,n,i){var o=t.coordinateSystem,r=t.id,a=t.getData(),s=this.markerGroupMap,l=s.get(r)||s.set(r,new cc);this.group.add(l.group);var u=function(t,e,n){var i;i=t?d(t&&t.dimensions,(function(t){var n=e.getData().getDimensionInfo(e.getData().mapDimension(t))||{};return H(H({},n),{name:t,ordinalMeta:null})})):[{name:"value",type:"float"}];var o=new tn(i,n),r=new tn(i,n),a=new tn([],n),s=d(n.get("data"),Re(Hx,e,t,n));t&&(s=c(s,Re(Xx,t)));var l=Vx(!!t,i);return o.initData(d(s,(function(t){return t[0]})),null,l),r.initData(d(s,(function(t){return t[1]})),null,l),a.initData(d(s,(function(t){return t[2]}))),a.hasItemOption=!0,{from:o,to:r,line:a}}(o,t,e),h=u.from,p=u.to,g=u.line;Wx(e).from=h,Wx(e).to=p,e.setData(g);var f=e.get("symbol"),y=e.get("symbolSize"),m=e.get("symbolRotate"),x=e.get("symbolOffset");function _(e,n,o){var r=e.getItemModel(n);Ux(e,n,o,t,i);var s=r.getModel("itemStyle").getItemStyle();null==s.fill&&(s.fill=Ho(a,"color")),e.setItemVisual(n,{symbolKeepAspect:r.get("symbolKeepAspect"),symbolOffset:C(r.get("symbolOffset",!0),x[o?0:1]),symbolRotate:C(r.get("symbolRotate",!0),m[o?0:1]),symbolSize:C(r.get("symbolSize"),y[o?0:1]),symbol:C(r.get("symbol",!0),f[o?0:1]),style:s})}mt(f)||(f=[f,f]),mt(y)||(y=[y,y]),mt(m)||(m=[m,m]),mt(x)||(x=[x,x]),u.from.each((function(t){_(h,t,!0),_(p,t,!1)})),g.each((function(t){var e=g.getItemModel(t).getModel("lineStyle").getLineStyle();g.setItemLayout(t,[h.getItemLayout(t),p.getItemLayout(t)]),null==e.stroke&&(e.stroke=h.getItemVisual(t,"style").fill),g.setItemVisual(t,{fromSymbolKeepAspect:h.getItemVisual(t,"symbolKeepAspect"),fromSymbolOffset:h.getItemVisual(t,"symbolOffset"),fromSymbolRotate:h.getItemVisual(t,"symbolRotate"),fromSymbolSize:h.getItemVisual(t,"symbolSize"),fromSymbol:h.getItemVisual(t,"symbol"),toSymbolKeepAspect:p.getItemVisual(t,"symbolKeepAspect"),toSymbolOffset:p.getItemVisual(t,"symbolOffset"),toSymbolRotate:p.getItemVisual(t,"symbolRotate"),toSymbolSize:p.getItemVisual(t,"symbolSize"),toSymbol:p.getItemVisual(t,"symbol"),style:e})})),l.updateData(g),u.line.eachItemGraphicEl((function(t){v(t).dataModel=e,t.traverse((function(t){v(t).dataModel=e}))})),this.markKeep(l),l.group.silent=e.get("silent")||t.get("silent")},n.type="markLine",n}(zx);var qx=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.createMarkerModelFromSeries=function(t,e,i){return new n(t,e,i)},n.type="markArea",n.defaultOption={z:1,tooltip:{trigger:"item"},animation:!1,label:{show:!0,position:"top"},itemStyle:{borderWidth:0},emphasis:{label:{show:!0,position:"top"}}},n}(Tx),Kx=h(),Jx=function(t,e,n,i){var o=i[0],r=i[1];if(o&&r){var a=kx(t,o),s=kx(t,r),l=a.coord,u=s.coord;l[0]=Ge(l[0],-1/0),l[1]=Ge(l[1],-1/0),u[0]=Ge(u[0],1/0),u[1]=Ge(u[1],1/0);var d=Be([{},a,s]);return d.coord=[a.coord,s.coord],d.x0=a.x,d.y0=a.y,d.x1=s.x,d.y1=s.y,d}};function $x(t){return!isNaN(t)&&!isFinite(t)}function Qx(t,e,n,i){var o=1-t;return $x(e[o])&&$x(n[o])}function t_(t,e){var n=e.coord[0],i=e.coord[1],o={coord:n,x:e.x0,y:e.y0},r={coord:i,x:e.x1,y:e.y1};return Mi(t,"cartesian2d")?!(!n||!i||!Qx(1,n,i)&&!Qx(0,n,i))||function(t,e,n){return!(t&&t.containZone&&e.coord&&n.coord&&!Dx(e)&&!Dx(n))||t.containZone(e.coord,n.coord)}(t,o,r):Rx(t,o)||Rx(t,r)}function e_(t,e,n,i,o){var r,a=i.coordinateSystem,s=t.getItemModel(e),l=m(s.get(n[0]),o.getWidth()),u=m(s.get(n[1]),o.getHeight());if(isNaN(l)||isNaN(u)){if(i.getMarkerPosition){var d=t.getValues(["x0","y0"],e),c=t.getValues(["x1","y1"],e),h=a.clampData(d),p=a.clampData(c),g=[];"x0"===n[0]?g[0]=h[0]>p[0]?c[0]:d[0]:g[0]=h[0]>p[0]?d[0]:c[0],"y0"===n[1]?g[1]=h[1]>p[1]?c[1]:d[1]:g[1]=h[1]>p[1]?d[1]:c[1],r=i.getMarkerPosition(g,n,!0)}else{var f=[x=t.get(n[0],e),_=t.get(n[1],e)];a.clampData&&a.clampData(f,f),r=a.dataToPoint(f,!0)}if(Mi(a,"cartesian2d")){var y=a.getAxis("x"),v=a.getAxis("y"),x=t.get(n[0],e),_=t.get(n[1],e);$x(x)?r[0]=y.toGlobalCoord(y.getExtent()["x0"===n[0]?0:1]):$x(_)&&(r[1]=v.toGlobalCoord(v.getExtent()["y0"===n[1]?0:1]))}isNaN(l)||(r[0]=l),isNaN(u)||(r[1]=u)}else r=[l,u];return r}var n_=[["x0","y0"],["x1","y0"],["x1","y1"],["x0","y1"]],i_=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.updateTransform=function(t,e,n){e.eachSeries((function(t){var e=Tx.getMarkerModelFromSeries(t,"markArea");if(e){var i=e.getData();i.each((function(e){var o=d(n_,(function(o){return e_(i,e,o,t,n)}));i.setItemLayout(e,o),i.getItemGraphicEl(e).setShape("points",o)}))}}),this)},n.prototype.renderSeries=function(t,e,n,i){var o=t.coordinateSystem,r=t.id,a=t.getData(),s=this.markerGroupMap,l=s.get(r)||s.set(r,{group:new It});this.group.add(l.group),this.markKeep(l);var u=function(t,e,n){var i,o,r=["x0","y0","x1","y1"];if(t){var a=d(t&&t.dimensions,(function(t){var n=e.getData(),i=n.getDimensionInfo(n.mapDimension(t))||{};return H(H({},i),{name:t,ordinalMeta:null})}));o=d(r,(function(t,e){return{name:t,type:a[e%2].type}})),i=new tn(o,n)}else i=new tn(o=[{name:"value",type:"float"}],n);var s=d(n.get("data"),Re(Jx,e,t,n));t&&(s=c(s,Re(t_,t)));var l=t?function(t,e,n,i){var r=t.coord[Math.floor(i/2)][i%2];return Bo(r,o[i])}:function(t,e,n,i){return Bo(t.value,o[i])};return i.initData(s,null,l),i.hasItemOption=!0,i}(o,t,e);e.setData(u),u.each((function(e){var n=d(n_,(function(n){return e_(u,e,n,t,i)})),r=o.getAxis("x").scale,s=o.getAxis("y").scale,l=r.getExtent(),c=s.getExtent(),h=[r.parse(u.get("x0",e)),r.parse(u.get("x1",e))],p=[s.parse(u.get("y0",e)),s.parse(u.get("y1",e))];ni(h),ni(p);var g=!!(l[0]>h[1]||l[1]<h[0]||c[0]>p[1]||c[1]<p[0]);u.setItemLayout(e,{points:n,allClipped:g});var f=u.getItemModel(e).getModel("itemStyle").getItemStyle(),y=Ho(a,"color");f.fill||(f.fill=y,G(f.fill)&&(f.fill=xn(f.fill,.4))),f.stroke||(f.stroke=y),u.setItemVisual(e,"style",f)})),u.diff(Kx(l).data).add((function(t){var e=u.getItemLayout(t);if(!e.allClipped){var n=new Nt({shape:{points:e.points}});u.setItemGraphicEl(t,n),l.group.add(n)}})).update((function(t,n){var i=Kx(l).data.getItemGraphicEl(n),o=u.getItemLayout(t);o.allClipped?i&&l.group.remove(i):(i?P(i,{shape:{points:o.points}},e,t):i=new Nt({shape:{points:o.points}}),u.setItemGraphicEl(t,i),l.group.add(i))})).remove((function(t){var e=Kx(l).data.getItemGraphicEl(t);l.group.remove(e)})).execute(),u.eachItemGraphicEl((function(t,n){var i=u.getItemModel(n),o=u.getItemVisual(n,"style");t.useStyle(u.getItemVisual(n,"style")),zt(t,Bt(i),{labelFetcher:e,labelDataIndex:n,defaultText:u.getName(n)||"",inheritColor:G(o.fill)?xn(o.fill,1):"#000"}),Et(t,i),Gt(t,null,null,i.get(["emphasis","disabled"])),v(t).dataModel=e})),Kx(l).data=u,l.group.silent=e.get("silent")||t.get("silent")},n.type="markArea",n}(zx);var o_=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.type="dataZoom.inside",n.defaultOption=Ti(Xv.defaultOption,{disabled:!1,zoomLock:!1,zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}),n}(Xv),r_=h();function a_(t,e){if(e){t.removeKey(e.model.uid);var n=e.controller;n&&n.dispose()}}function s_(t,e){t.isDisposed()||t.dispatchAction({type:"dataZoom",animation:{easing:"cubicOut",duration:100},batch:e})}function l_(t,e,n,i){return t.coordinateSystem.containPoint([n,i])}function u_(t){t.registerProcessor(t.PRIORITY.PROCESSOR.FILTER,(function(t,e){var n=r_(e),i=n.coordSysRecordMap||(n.coordSysRecordMap=Ie());i.each((function(t){t.dataZoomInfoMap=null})),t.eachComponent({mainType:"dataZoom",subType:"inside"},(function(t){var n=Zv(t);a(n.infoList,(function(n){var o=n.model.uid,r=i.get(o)||i.set(o,function(t,e){var n={model:e,containsPoint:Re(l_,e),dispatchAction:Re(s_,t),dataZoomInfoMap:null,controller:null},i=n.controller=new _s(t.getZr());return a(["pan","zoom","scrollMove"],(function(t){i.on(t,(function(e){var i=[];n.dataZoomInfoMap.each((function(o){if(e.isAvailableBehavior(o.model.option)){var r=(o.getRange||{})[t],a=r&&r(o.dzReferCoordSysInfo,n.model.mainType,n.controller,e);!o.model.get("disabled",!0)&&a&&i.push({dataZoomId:o.model.id,start:a[0],end:a[1]})}})),i.length&&n.dispatchAction(i)}))})),n}(e,n.model));(r.dataZoomInfoMap||(r.dataZoomInfoMap=Ie())).set(t.uid,{dzReferCoordSysInfo:n,model:t,getRange:null})}))})),i.each((function(t){var e,n=t.controller,o=t.dataZoomInfoMap;if(o){var r=o.keys()[0];null!=r&&(e=o.get(r))}if(e){var a=function(t){var e,n="type_",i={type_true:2,type_move:1,type_false:0,type_undefined:-1},o=!0;return t.each((function(t){var r=t.model,a=!r.get("disabled",!0)&&(!r.get("zoomLock",!0)||"move");i[n+a]>i[n+e]&&(e=a),o=o&&r.get("preventDefaultMouseMove",!0)})),{controlType:e,opt:{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!0,preventDefaultMouseMove:!!o}}}(o);n.enable(a.controlType,a.opt),n.setPointerChecker(t.containsPoint),Jn(t,"dispatchAction",e.model.get("throttle",!0),"fixRate")}else a_(i,t)}))}))}var d_=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="dataZoom.inside",e}return e(n,t),n.prototype.render=function(e,n,i){t.prototype.render.apply(this,arguments),e.noTarget()?this._clear():(this.range=e.getPercentRange(),function(t,e,n){r_(t).coordSysRecordMap.each((function(t){var i=t.dataZoomInfoMap.get(e.uid);i&&(i.getRange=n)}))}(i,e,{pan:Zt(c_.pan,this),zoom:Zt(c_.zoom,this),scrollMove:Zt(c_.scrollMove,this)}))},n.prototype.dispose=function(){this._clear(),t.prototype.dispose.apply(this,arguments)},n.prototype._clear=function(){!function(t,e){for(var n=r_(t).coordSysRecordMap,i=n.keys(),o=0;o<i.length;o++){var r=i[o],a=n.get(r),s=a.dataZoomInfoMap;if(s){var l=e.uid;s.get(l)&&(s.removeKey(l),s.keys().length||a_(n,a))}}}(this.api,this.dataZoomModel),this.range=null},n.type="dataZoom.inside",n}(qv),c_={zoom:function(t,e,n,i){var o=this.range,r=o.slice(),a=t.axisModels[0];if(a){var s=p_[e](null,[i.originX,i.originY],a,n,t),l=(s.signal>0?s.pixelStart+s.pixelLength-s.pixel:s.pixel-s.pixelStart)/s.pixelLength*(r[1]-r[0])+r[0],u=Math.max(1/i.scale,0);r[0]=(r[0]-l)*u+l,r[1]=(r[1]-l)*u+l;var d=this.dataZoomModel.findRepresentativeAxisProxy().getMinMaxSpan();return oh(0,r,[0,100],0,d.minSpan,d.maxSpan),this.range=r,o[0]!==r[0]||o[1]!==r[1]?r:void 0}},pan:h_((function(t,e,n,i,o,r){var a=p_[i]([r.oldX,r.oldY],[r.newX,r.newY],e,o,n);return a.signal*(t[1]-t[0])*a.pixel/a.pixelLength})),scrollMove:h_((function(t,e,n,i,o,r){return p_[i]([0,0],[r.scrollDelta,r.scrollDelta],e,o,n).signal*(t[1]-t[0])*r.scrollDelta}))};function h_(t){return function(e,n,i,o){var r=this.range,a=r.slice(),s=e.axisModels[0];if(s)return oh(t(a,s,e,n,i,o),a,[0,100],"all"),this.range=a,r[0]!==a[0]||r[1]!==a[1]?a:void 0}}var p_={grid:function(t,e,n,i,o){var r=n.axis,a={},s=o.model.coordinateSystem.getRect();return t=t||[0,0],"x"===r.dim?(a.pixel=e[0]-t[0],a.pixelLength=s.width,a.pixelStart=s.x,a.signal=r.inverse?1:-1):(a.pixel=e[1]-t[1],a.pixelLength=s.height,a.pixelStart=s.y,a.signal=r.inverse?-1:1),a},polar:function(t,e,n,i,o){var r=n.axis,a={},s=o.model.coordinateSystem,l=s.getRadiusAxis().getExtent(),u=s.getAngleAxis().getExtent();return t=t?s.pointToCoord(t):[0,0],e=s.pointToCoord(e),"radiusAxis"===n.mainType?(a.pixel=e[0]-t[0],a.pixelLength=l[1]-l[0],a.pixelStart=l[0],a.signal=r.inverse?1:-1):(a.pixel=e[1]-t[1],a.pixelLength=u[1]-u[0],a.pixelStart=u[0],a.signal=r.inverse?-1:1),a},singleAxis:function(t,e,n,i,o){var r=n.axis,a=o.model.coordinateSystem.getRect(),s={};return t=t||[0,0],"horizontal"===r.orient?(s.pixel=e[0]-t[0],s.pixelLength=a.width,s.pixelStart=a.x,s.signal=r.inverse?1:-1):(s.pixel=e[1]-t[1],s.pixelLength=a.height,s.pixelStart=a.y,s.signal=r.inverse?-1:1),s}};function g_(t){nm(t),t.registerComponentModel(o_),t.registerComponentView(d_),u_(t)}var f_=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.type="dataZoom.slider",n.layoutMode="box",n.defaultOption=Ti(Xv.defaultOption,{show:!0,right:"ph",top:"ph",width:"ph",height:"ph",left:null,bottom:null,borderColor:"#d2dbee",borderRadius:3,backgroundColor:"rgba(47,69,84,0)",dataBackground:{lineStyle:{color:"#d2dbee",width:.5},areaStyle:{color:"#d2dbee",opacity:.2}},selectedDataBackground:{lineStyle:{color:"#8fb0f7",width:.5},areaStyle:{color:"#8fb0f7",opacity:.2}},fillerColor:"rgba(135,175,274,0.2)",handleIcon:"path://M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z",handleSize:"100%",handleStyle:{color:"#fff",borderColor:"#ACB8D1"},moveHandleSize:7,moveHandleIcon:"path://M-320.9-50L-320.9-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-348-41-339-50-320.9-50z M-212.3-50L-212.3-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-239.4-41-230.4-50-212.3-50z M-103.7-50L-103.7-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-130.9-41-121.8-50-103.7-50z",moveHandleStyle:{color:"#D2DBEE",opacity:.7},showDetail:!0,showDataShadow:"auto",realtime:!0,zoomLock:!1,textStyle:{color:"#6E7079"},brushSelect:!0,brushStyle:{color:"rgba(135,175,274,0.15)"},emphasis:{handleLabel:{show:!0},handleStyle:{borderColor:"#8FB0F7"},moveHandleStyle:{color:"#8FB0F7"}}}),n}(Xv),y_=pe,v_="horizontal",m_="vertical",x_=["line","bar","candlestick","scatter"],__={easing:"cubicOut",duration:100,delay:0},b_=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e._displayables={},e}return e(n,t),n.prototype.init=function(t,e){this.api=e,this._onBrush=Zt(this._onBrush,this),this._onBrushEnd=Zt(this._onBrushEnd,this)},n.prototype.render=function(e,n,i,o){if(t.prototype.render.apply(this,arguments),Jn(this,"_dispatchZoomAction",e.get("throttle"),"fixRate"),this._orient=e.getOrient(),!1!==e.get("show")){if(e.noTarget())return this._clear(),void this.group.removeAll();o&&"dataZoom"===o.type&&o.from===this.uid||this._buildView(),this._updateView()}else this.group.removeAll()},n.prototype.dispose=function(){this._clear(),t.prototype.dispose.apply(this,arguments)},n.prototype._clear=function(){$n(this,"_dispatchZoomAction");var t=this.api.getZr();t.off("mousemove",this._onBrush),t.off("mouseup",this._onBrushEnd)},n.prototype._buildView=function(){var t=this.group;t.removeAll(),this._brushing=!1,this._displayables.brushRect=null,this._resetLocation(),this._resetInterval();var e=this._displayables.sliderGroup=new It;this._renderBackground(),this._renderHandle(),this._renderDataShadow(),t.add(e),this._positionGroup()},n.prototype._resetLocation=function(){var t=this.dataZoomModel,e=this.api,n=t.get("brushSelect")?7:0,i=this._findCoordRect(),o={width:e.getWidth(),height:e.getHeight()},r=this._orient===v_?{right:o.width-i.x-i.width,top:o.height-30-7-n,width:i.width,height:30}:{right:7,top:i.y,width:30,height:i.height},s=so(t.option);a(["right","top","width","height"],(function(t){"ph"===s[t]&&(s[t]=r[t])}));var l=Fe(s,o);this._location={x:l.x,y:l.y},this._size=[l.width,l.height],this._orient===m_&&this._size.reverse()},n.prototype._positionGroup=function(){var t=this.group,e=this._location,n=this._orient,i=this.dataZoomModel.getFirstTargetAxisModel(),o=i&&i.get("inverse"),r=this._displayables.sliderGroup,a=(this._dataShadowInfo||{}).otherAxisInverse;r.attr(n!==v_||o?n===v_&&o?{scaleY:a?1:-1,scaleX:-1}:n!==m_||o?{scaleY:a?-1:1,scaleX:-1,rotation:Math.PI/2}:{scaleY:a?-1:1,scaleX:1,rotation:Math.PI/2}:{scaleY:a?1:-1,scaleX:1});var s=t.getBoundingRect([r]);t.x=e.x-s.x,t.y=e.y-s.y,t.markRedraw()},n.prototype._getViewExtent=function(){return[0,this._size[0]]},n.prototype._renderBackground=function(){var t=this.dataZoomModel,e=this._size,n=this._displayables.sliderGroup,i=t.get("brushSelect");n.add(new y_({silent:!0,shape:{x:0,y:0,width:e[0],height:e[1]},style:{fill:t.get("backgroundColor")},z2:-40}));var o=new y_({shape:{x:0,y:0,width:e[0],height:e[1]},style:{fill:"transparent"},z2:0,onclick:Zt(this._onClickPanel,this)}),r=this.api.getZr();i?(o.on("mousedown",this._onBrushStart,this),o.cursor="crosshair",r.on("mousemove",this._onBrush),r.on("mouseup",this._onBrushEnd)):(r.off("mousemove",this._onBrush),r.off("mouseup",this._onBrushEnd)),n.add(o)},n.prototype._renderDataShadow=function(){var t=this._dataShadowInfo=this._prepareDataShadowInfo();if(this._displayables.dataShadowSegs=[],t){var e=this._size,n=this._shadowSize||[],i=t.series,o=i.getRawData(),r=i.getShadowDim&&i.getShadowDim(),a=r&&o.getDimensionInfo(r)?i.getShadowDim():t.otherDim;if(null!=a){var s=this._shadowPolygonPts,l=this._shadowPolylinePts;if(o!==this._shadowData||a!==this._shadowDim||e[0]!==n[0]||e[1]!==n[1]){var u=o.getDataExtent(a),d=.3*(u[1]-u[0]);u=[u[0]-d,u[1]+d];var c,h=[0,e[1]],p=[0,e[0]],g=[[e[0],0],[0,0]],f=[],y=p[1]/(o.count()-1),v=0,m=Math.round(o.count()/e[0]);o.each([a],(function(t,e){if(m>0&&e%m)v+=y;else{var n=null==t||isNaN(t)||""===t,i=n?0:fn(t,u,h,!0);n&&!c&&e?(g.push([g[g.length-1][0],0]),f.push([f[f.length-1][0],0])):!n&&c&&(g.push([v,0]),f.push([v,0])),g.push([v,i]),f.push([v,i]),v+=y,c=n}})),s=this._shadowPolygonPts=g,l=this._shadowPolylinePts=f}this._shadowData=o,this._shadowDim=a,this._shadowSize=[e[0],e[1]];for(var x,_,b,S,w,M=this.dataZoomModel,I=0;I<3;I++){var A=(x=1===I,_=void 0,b=void 0,S=void 0,w=void 0,_=M.getModel(x?"selectedDataBackground":"dataBackground"),b=new It,S=new Nt({shape:{points:s},segmentIgnoreThreshold:1,style:_.getModel("areaStyle").getAreaStyle(),silent:!0,z2:-20}),w=new Rt({shape:{points:l},segmentIgnoreThreshold:1,style:_.getModel("lineStyle").getLineStyle(),silent:!0,z2:-19}),b.add(S),b.add(w),b);this._displayables.sliderGroup.add(A),this._displayables.dataShadowSegs.push(A)}}}},n.prototype._prepareDataShadowInfo=function(){var t=this.dataZoomModel,e=t.get("showDataShadow");if(!1!==e){var n,i=this.ecModel;return t.eachTargetAxis((function(o,r){var s=t.getAxisProxy(o,r).getTargetSeriesModels();a(s,(function(t){if(!(n||!0!==e&&L(x_,t.get("type"))<0)){var a,s=i.getComponent(Wv(o),r).axis,l={x:"y",y:"x",radius:"angle",angle:"radius"}[o],u=t.coordinateSystem;null!=l&&u.getOtherAxis&&(a=u.getOtherAxis(s).inverse),l=t.getData().mapDimension(l),n={thisAxis:s,series:t,thisDim:o,otherDim:l,otherAxisInverse:a}}}),this)}),this),n}},n.prototype._renderHandle=function(){var t=this.group,e=this._displayables,n=e.handles=[null,null],i=e.handleLabels=[null,null],o=this._displayables.sliderGroup,r=this._size,s=this.dataZoomModel,l=this.api,u=s.get("borderRadius")||0,d=s.get("brushSelect"),c=e.filler=new y_({silent:d,style:{fill:s.get("fillerColor")},textConfig:{position:"inside"}});o.add(c),o.add(new y_({silent:!0,subPixelOptimize:!0,shape:{x:0,y:0,width:r[0],height:r[1],r:u},style:{stroke:s.get("dataBackgroundColor")||s.get("borderColor"),lineWidth:1,fill:"rgba(0,0,0,0)"}})),a([0,1],(function(e){var r=s.get("handleIcon");!Yo[r]&&r.indexOf("path://")<0&&r.indexOf("image://")<0&&(r="path://"+r);var a=At(r,-1,0,2,2,null,!0);a.attr({cursor:S_(this._orient),draggable:!0,drift:Zt(this._onDragMove,this,e),ondragend:Zt(this._onDragEnd,this),onmouseover:Zt(this._showDataInfo,this,!0),onmouseout:Zt(this._showDataInfo,this,!1),z2:5});var l=a.getBoundingRect(),u=s.get("handleSize");this._handleHeight=m(u,this._size[1]),this._handleWidth=l.width/l.height*this._handleHeight,a.setStyle(s.getModel("handleStyle").getItemStyle()),a.style.strokeNoScale=!0,a.rectHover=!0,a.ensureState("emphasis").style=s.getModel(["emphasis","handleStyle"]).getItemStyle(),Vo(a);var d=s.get("handleColor");null!=d&&(a.style.fill=d),o.add(n[e]=a);var c=s.getModel("textStyle"),h=(s.get("handleLabel")||{}).show||!1;t.add(i[e]=new an({silent:!0,invisible:!h,style:sn(c,{x:0,y:0,text:"",verticalAlign:"middle",align:"center",fill:c.getTextColor(),font:c.getFont()}),z2:10}))}),this);var h=c;if(d){var p=m(s.get("moveHandleSize"),r[1]),g=e.moveHandle=new pe({style:s.getModel("moveHandleStyle").getItemStyle(),silent:!0,shape:{r:[0,0,2,2],y:r[1]-.5,height:p}}),f=.8*p,y=e.moveHandleIcon=At(s.get("moveHandleIcon"),-f/2,-f/2,f,f,"#fff",!0);y.silent=!0,y.y=r[1]+p/2-.5,g.ensureState("emphasis").style=s.getModel(["emphasis","moveHandleStyle"]).getItemStyle();var v=Math.min(r[1]/2,Math.max(p,10));(h=e.moveZone=new pe({invisible:!0,shape:{y:r[1]-v,height:p+v}})).on("mouseover",(function(){l.enterEmphasis(g)})).on("mouseout",(function(){l.leaveEmphasis(g)})),o.add(g),o.add(y),o.add(h)}h.attr({draggable:!0,cursor:S_(this._orient),drift:Zt(this._onDragMove,this,"all"),ondragstart:Zt(this._showDataInfo,this,!0),ondragend:Zt(this._onDragEnd,this),onmouseover:Zt(this._showDataInfo,this,!0),onmouseout:Zt(this._showDataInfo,this,!1)})},n.prototype._resetInterval=function(){var t=this._range=this.dataZoomModel.getPercentRange(),e=this._getViewExtent();this._handleEnds=[fn(t[0],[0,100],e,!0),fn(t[1],[0,100],e,!0)]},n.prototype._updateInterval=function(t,e){var n=this.dataZoomModel,i=this._handleEnds,o=this._getViewExtent(),r=n.findRepresentativeAxisProxy().getMinMaxSpan(),a=[0,100];oh(e,i,o,n.get("zoomLock")?"all":t,null!=r.minSpan?fn(r.minSpan,a,o,!0):null,null!=r.maxSpan?fn(r.maxSpan,a,o,!0):null);var s=this._range,l=this._range=ni([fn(i[0],o,a,!0),fn(i[1],o,a,!0)]);return!s||s[0]!==l[0]||s[1]!==l[1]},n.prototype._updateView=function(t){var e=this._displayables,n=this._handleEnds,i=ni(n.slice()),o=this._size;a([0,1],(function(t){var i=e.handles[t],r=this._handleHeight;i.attr({scaleX:r/2,scaleY:r/2,x:n[t]+(t?-1:1),y:o[1]/2-r/2})}),this),e.filler.setShape({x:i[0],y:0,width:i[1]-i[0],height:o[1]});var r={x:i[0],width:i[1]-i[0]};e.moveHandle&&(e.moveHandle.setShape(r),e.moveZone.setShape(r),e.moveZone.getBoundingRect(),e.moveHandleIcon&&e.moveHandleIcon.attr("x",r.x+r.width/2));for(var s=e.dataShadowSegs,l=[0,i[0],i[1],o[0]],u=0;u<s.length;u++){var d=s[u],c=d.getClipPath();c||(c=new pe,d.setClipPath(c)),c.setShape({x:l[u],y:0,width:l[u+1]-l[u],height:o[1]})}this._updateDataInfo(t)},n.prototype._updateDataInfo=function(t){var e=this.dataZoomModel,n=this._displayables,i=n.handleLabels,o=this._orient,r=["",""];if(e.get("showDetail")){var a=e.findRepresentativeAxisProxy();if(a){var s=a.getAxisModel().axis,l=this._range,u=t?a.calculateDataWindow({start:l[0],end:l[1]}).valueWindow:a.getDataValueWindow();r=[this._formatLabel(u[0],s),this._formatLabel(u[1],s)]}}var d=ni(this._handleEnds.slice());function c(t){var e=oi(n.handles[t].parent,this.group),a=ii(0===t?"right":"left",e),s=this._handleWidth/2+5,l=ei([d[t]+(0===t?-s:s),this._size[1]/2],e);i[t].setStyle({x:l[0],y:l[1],verticalAlign:o===v_?"middle":a,align:o===v_?a:"center",text:r[t]})}c.call(this,0),c.call(this,1)},n.prototype._formatLabel=function(t,e){var n=this.dataZoomModel,i=n.get("labelFormatter"),o=n.get("labelPrecision");null!=o&&"auto"!==o||(o=e.getPixelPrecision());var r=null==t||isNaN(t)?"":"category"===e.type||"time"===e.type?e.scale.getLabel({value:Math.round(t)}):t.toFixed(Math.min(o,20));return f(i)?i(t,r):G(i)?i.replace("{value}",r):r},n.prototype._showDataInfo=function(t){var e=(this.dataZoomModel.get("handleLabel")||{}).show||!1,n=this.dataZoomModel.getModel(["emphasis","handleLabel"]).get("show")||!1,i=t||this._dragging?n:e,o=this._displayables,r=o.handleLabels;r[0].attr("invisible",!i),r[1].attr("invisible",!i),o.moveHandle&&this.api[i?"enterEmphasis":"leaveEmphasis"](o.moveHandle,1)},n.prototype._onDragMove=function(t,e,n,i){this._dragging=!0,ce(i.event);var o=this._displayables.sliderGroup.getLocalTransform(),r=ei([e,n],o,!0),a=this._updateInterval(t,r[0]),s=this.dataZoomModel.get("realtime");this._updateView(!s),a&&s&&this._dispatchZoomAction(!0)},n.prototype._onDragEnd=function(){this._dragging=!1,this._showDataInfo(!1),!this.dataZoomModel.get("realtime")&&this._dispatchZoomAction(!1)},n.prototype._onClickPanel=function(t){var e=this._size,n=this._displayables.sliderGroup.transformCoordToLocal(t.offsetX,t.offsetY);if(!(n[0]<0||n[0]>e[0]||n[1]<0||n[1]>e[1])){var i=this._handleEnds,o=(i[0]+i[1])/2,r=this._updateInterval("all",n[0]-o);this._updateView(),r&&this._dispatchZoomAction(!1)}},n.prototype._onBrushStart=function(t){var e=t.offsetX,n=t.offsetY;this._brushStart=new jn(e,n),this._brushing=!0,this._brushStartTime=+new Date},n.prototype._onBrushEnd=function(t){if(this._brushing){var e=this._displayables.brushRect;if(this._brushing=!1,e){e.attr("ignore",!0);var n=e.shape;if(!(+new Date-this._brushStartTime<200&&Math.abs(n.width)<5)){var i=this._getViewExtent(),o=[0,100];this._range=ni([fn(n.x,i,o,!0),fn(n.x+n.width,i,o,!0)]),this._handleEnds=[n.x,n.x+n.width],this._updateView(),this._dispatchZoomAction(!1)}}}},n.prototype._onBrush=function(t){this._brushing&&(ce(t.event),this._updateBrushRect(t.offsetX,t.offsetY))},n.prototype._updateBrushRect=function(t,e){var n=this._displayables,i=this.dataZoomModel,o=n.brushRect;o||(o=n.brushRect=new y_({silent:!0,style:i.getModel("brushStyle").getItemStyle()}),n.sliderGroup.add(o)),o.attr("ignore",!1);var r=this._brushStart,a=this._displayables.sliderGroup,s=a.transformCoordToLocal(t,e),l=a.transformCoordToLocal(r.x,r.y),u=this._size;s[0]=Math.max(Math.min(u[0],s[0]),0),o.setShape({x:l[0],y:0,width:s[0]-l[0],height:u[1]})},n.prototype._dispatchZoomAction=function(t){var e=this._range;this.api.dispatchAction({type:"dataZoom",from:this.uid,dataZoomId:this.dataZoomModel.id,animation:t?__:null,start:e[0],end:e[1]})},n.prototype._findCoordRect=function(){var t,e=Zv(this.dataZoomModel).infoList;if(!t&&e.length){var n=e[0].model.coordinateSystem;t=n.getRect&&n.getRect()}if(!t){var i=this.api.getWidth(),o=this.api.getHeight();t={x:.2*i,y:.2*o,width:.6*i,height:.6*o}}return t},n.type="dataZoom.slider",n}(qv);function S_(t){return"vertical"===t?"ns-resize":"ew-resize"}function w_(t){t.registerComponentModel(f_),t.registerComponentView(b_),nm(t)}var M_=function(t,e,n){var i=ct((I_[t]||{})[e]);return n&&mt(i)?i[i.length-1]:i},I_={color:{active:["#006edd","#e0ffff"],inactive:["rgba(0,0,0,0)"]},colorHue:{active:[0,360],inactive:[0,0]},colorSaturation:{active:[.3,1],inactive:[0,0]},colorLightness:{active:[.9,.5],inactive:[0,0]},colorAlpha:{active:[.3,1],inactive:[0,0]},opacity:{active:[.3,1],inactive:[0,0]},symbol:{active:["circle","roundRect","diamond"],inactive:["none"]},symbolSize:{active:[10,50],inactive:[0,0]}},A_=Ju.mapVisual,T_=Ju.eachVisual,C_=mt,D_=a,L_=ni,P_=fn,k_=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.stateList=["inRange","outOfRange"],e.replacableOptionKeys=["inRange","outOfRange","target","controller","color"],e.layoutMode={type:"box",ignoreSize:!0},e.dataBound=[-1/0,1/0],e.targetVisuals={},e.controllerVisuals={},e}return e(n,t),n.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n)},n.prototype.optionUpdated=function(t,e){var n=this.option;!e&&jm(n,t,this.replacableOptionKeys),this.textStyleModel=this.getModel("textStyle"),this.resetItemSize(),this.completeVisualOption()},n.prototype.resetVisual=function(t){var e=this.stateList;t=Zt(t,this),this.controllerVisuals=Um(this.option.controller,e,t),this.targetVisuals=Um(this.option.target,e,t)},n.prototype.getItemSymbol=function(){return null},n.prototype.getTargetSeriesIndices=function(){var t=this.option.seriesIndex,e=[];return null==t||"all"===t?this.ecModel.eachSeries((function(t,n){e.push(n)})):e=nn(t),e},n.prototype.eachTargetSeries=function(t,e){a(this.getTargetSeriesIndices(),(function(n){var i=this.ecModel.getSeriesByIndex(n);i&&t.call(e,i)}),this)},n.prototype.isTargetSeries=function(t){var e=!1;return this.eachTargetSeries((function(n){n===t&&(e=!0)})),e},n.prototype.formatValueText=function(t,e,n){var i,o=this.option,r=o.precision,a=this.dataBound,s=o.formatter;n=n||["<",">"],mt(t)&&(t=t.slice(),i=!0);var l=e?t:i?[u(t[0]),u(t[1])]:u(t);return G(s)?s.replace("{value}",i?l[0]:l).replace("{value2}",i?l[1]:l):f(s)?i?s(t[0],t[1]):s(t):i?t[0]===a[0]?n[0]+" "+l[1]:t[1]===a[1]?n[1]+" "+l[0]:l[0]+" - "+l[1]:l;function u(t){return t===a[0]?"min":t===a[1]?"max":(+t).toFixed(Math.min(r,20))}},n.prototype.resetExtent=function(){var t=this.option,e=L_([t.min,t.max]);this._dataExtent=e},n.prototype.getDataDimensionIndex=function(t){var e=this.option.dimension;if(null!=e)return t.getDimensionIndex(e);for(var n=t.dimensions,i=n.length-1;i>=0;i--){var o=n[i],r=t.getDimensionInfo(o);if(!r.isCalculationCoord)return r.storeDimIndex}},n.prototype.getExtent=function(){return this._dataExtent.slice()},n.prototype.completeVisualOption=function(){var t=this.ecModel,e=this.option,n={inRange:e.inRange,outOfRange:e.outOfRange},i=e.target||(e.target={}),o=e.controller||(e.controller={});jt(i,n),jt(o,n);var r=this.isCategory();function a(n){C_(e.color)&&!n.inRange&&(n.inRange={color:e.color.slice().reverse()}),n.inRange=n.inRange||{color:t.get("gradientColor")}}a.call(this,i),a.call(this,o),function(t,e,n){var i=t[e],o=t[n];i&&!o&&(o=t[n]={},D_(i,(function(t,e){if(Ju.isValidType(e)){var n=M_(e,"inactive",r);null!=n&&(o[e]=n,"color"!==e||o.hasOwnProperty("opacity")||o.hasOwnProperty("colorAlpha")||(o.opacity=[0,0]))}})))}.call(this,i,"inRange","outOfRange"),function(t){var e=(t.inRange||{}).symbol||(t.outOfRange||{}).symbol,n=(t.inRange||{}).symbolSize||(t.outOfRange||{}).symbolSize,i=this.get("inactiveColor"),o=this.getItemSymbol()||"roundRect";D_(this.stateList,(function(a){var s=this.itemSize,l=t[a];l||(l=t[a]={color:r?i:[i]}),null==l.symbol&&(l.symbol=e&&ct(e)||(r?o:[o])),null==l.symbolSize&&(l.symbolSize=n&&ct(n)||(r?s[0]:[s[0],s[0]])),l.symbol=A_(l.symbol,(function(t){return"none"===t?o:t}));var u=l.symbolSize;if(null!=u){var d=-1/0;T_(u,(function(t){t>d&&(d=t)})),l.symbolSize=A_(u,(function(t){return P_(t,[0,d],[0,s[0]],!0)}))}}),this)}.call(this,o)},n.prototype.resetItemSize=function(){this.itemSize=[parseFloat(this.get("itemWidth")),parseFloat(this.get("itemHeight"))]},n.prototype.isCategory=function(){return!!this.option.categories},n.prototype.setSelected=function(t){},n.prototype.getSelected=function(){return null},n.prototype.getValueState=function(t){return null},n.prototype.getVisualMeta=function(t){return null},n.type="visualMap",n.dependencies=["series"],n.defaultOption={show:!0,z:4,seriesIndex:"all",min:0,max:200,left:0,right:null,top:null,bottom:0,itemWidth:null,itemHeight:null,inverse:!1,orient:"vertical",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",contentColor:"#5793f3",inactiveColor:"#aaa",borderWidth:0,padding:5,textGap:10,precision:0,textStyle:{color:"#333"}},n}(Qt),N_=[20,140],R_=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.optionUpdated=function(e,n){t.prototype.optionUpdated.apply(this,arguments),this.resetExtent(),this.resetVisual((function(t){t.mappingMethod="linear",t.dataExtent=this.getExtent()})),this._resetRange()},n.prototype.resetItemSize=function(){t.prototype.resetItemSize.apply(this,arguments);var e=this.itemSize;(null==e[0]||isNaN(e[0]))&&(e[0]=N_[0]),(null==e[1]||isNaN(e[1]))&&(e[1]=N_[1])},n.prototype._resetRange=function(){var t=this.getExtent(),e=this.option.range;!e||e.auto?(t.auto=1,this.option.range=t):mt(e)&&(e[0]>e[1]&&e.reverse(),e[0]=Math.max(e[0],t[0]),e[1]=Math.min(e[1],t[1]))},n.prototype.completeVisualOption=function(){t.prototype.completeVisualOption.apply(this,arguments),a(this.stateList,(function(t){var e=this.option.controller[t].symbolSize;e&&e[0]!==e[1]&&(e[0]=e[1]/3)}),this)},n.prototype.setSelected=function(t){this.option.range=t.slice(),this._resetRange()},n.prototype.getSelected=function(){var t=this.getExtent(),e=ni((this.get("range")||[]).slice());return e[0]>t[1]&&(e[0]=t[1]),e[1]>t[1]&&(e[1]=t[1]),e[0]<t[0]&&(e[0]=t[0]),e[1]<t[0]&&(e[1]=t[0]),e},n.prototype.getValueState=function(t){var e=this.option.range,n=this.getExtent();return(e[0]<=n[0]||e[0]<=t)&&(e[1]>=n[1]||t<=e[1])?"inRange":"outOfRange"},n.prototype.findTargetDataIndices=function(t){var e=[];return this.eachTargetSeries((function(n){var i=[],o=n.getData();o.each(this.getDataDimensionIndex(o),(function(e,n){t[0]<=e&&e<=t[1]&&i.push(n)}),this),e.push({seriesId:n.id,dataIndex:i})}),this),e},n.prototype.getVisualMeta=function(t){var e=V_(this,"outOfRange",this.getExtent()),n=V_(this,"inRange",this.option.range.slice()),i=[];function o(e,n){i.push({value:e,color:t(e,n)})}for(var r=0,a=0,s=n.length,l=e.length;a<l&&(!n.length||e[a]<=n[0]);a++)e[a]<n[r]&&o(e[a],"outOfRange");for(var u=1;r<s;r++,u=0)u&&i.length&&o(n[r],"outOfRange"),o(n[r],"inRange");for(u=1;a<l;a++)(!n.length||n[n.length-1]<e[a])&&(u&&(i.length&&o(i[i.length-1].value,"outOfRange"),u=0),o(e[a],"outOfRange"));var d=i.length;return{stops:i,outerColors:[d?i[0].color:"transparent",d?i[d-1].color:"transparent"]}},n.type="visualMap.continuous",n.defaultOption=Ti(k_.defaultOption,{align:"auto",calculable:!1,hoverLink:!0,realtime:!0,handleIcon:"path://M-11.39,9.77h0a3.5,3.5,0,0,1-3.5,3.5h-22a3.5,3.5,0,0,1-3.5-3.5h0a3.5,3.5,0,0,1,3.5-3.5h22A3.5,3.5,0,0,1-11.39,9.77Z",handleSize:"120%",handleStyle:{borderColor:"#fff",borderWidth:1},indicatorIcon:"circle",indicatorSize:"50%",indicatorStyle:{borderColor:"#fff",borderWidth:2,shadowBlur:2,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0,0,0,0.2)"}}),n}(k_);function V_(t,e,n){if(n[0]===n[1])return n.slice();for(var i=(n[1]-n[0])/200,o=n[0],r=[],a=0;a<=200&&o<n[1];a++)r.push(o),o+=i;return r.push(n[1]),r}var O_=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.autoPositionValues={left:1,right:1,top:1,bottom:1},e}return e(n,t),n.prototype.init=function(t,e){this.ecModel=t,this.api=e},n.prototype.render=function(t,e,n,i){this.visualMapModel=t,!1!==t.get("show")?this.doRender(t,e,n,i):this.group.removeAll()},n.prototype.renderBackground=function(t){var e=this.visualMapModel,n=Xo(e.get("padding")||0),i=t.getBoundingRect();t.add(new pe({z2:-1,silent:!0,shape:{x:i.x-n[3],y:i.y-n[0],width:i.width+n[3]+n[1],height:i.height+n[0]+n[2]},style:{fill:e.get("backgroundColor"),stroke:e.get("borderColor"),lineWidth:e.get("borderWidth")}}))},n.prototype.getControllerVisual=function(t,e,n){var i=(n=n||{}).forceState,o=this.visualMapModel,r={};if("color"===e){var s=o.get("contentColor");r.color=s}function l(t){return r[t]}function u(t,e){r[t]=e}var d=o.controllerVisuals[i||o.getValueState(t)],c=Ju.prepareVisualTypes(d);return a(c,(function(i){var o=d[i];n.convertOpacityToAlpha&&"opacity"===i&&(i="colorAlpha",o=d.__alphaForOpacity),Ju.dependsOn(i,e)&&o&&o.applyVisual(t,l,u)})),r[e]},n.prototype.positionGroup=function(t){var e=this.visualMapModel,n=this.api;on(t,e.getBoxLayoutParams(),{width:n.getWidth(),height:n.getHeight()})},n.prototype.doRender=function(t,e,n,i){},n.type="visualMap",n}(oe),E_=[["left","right","width"],["top","bottom","height"]];function z_(t,e,n){var i=t.option,o=i.align;if(null!=o&&"auto"!==o)return o;for(var r={width:e.getWidth(),height:e.getHeight()},a="horizontal"===i.orient?1:0,s=E_[a],l=[0,null,10],u={},d=0;d<3;d++)u[E_[1-a][d]]=l[d],u[s[d]]=2===d?n[0]:i[s[d]];var c=[["x","width",3],["y","height",0]][a],h=Fe(u,r,i.padding);return s[(h.margin[c[2]]||0)+h[c[0]]+.5*h[c[1]]<.5*r[c[1]]?0:1]}function B_(t,e){return a(t||[],(function(t){null!=t.dataIndex&&(t.dataIndexInside=t.dataIndex,t.dataIndex=null),t.highlightKey="visualMap"+(e?e.componentIndex:"")})),t}var G_=fn,F_=a,W_=Math.min,H_=Math.max,Z_=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e._shapes={},e._dataInterval=[],e._handleEnds=[],e._hoverLinkDataIndices=[],e}return e(n,t),n.prototype.init=function(e,n){t.prototype.init.call(this,e,n),this._hoverLinkFromSeriesMouseOver=Zt(this._hoverLinkFromSeriesMouseOver,this),this._hideIndicator=Zt(this._hideIndicator,this)},n.prototype.doRender=function(t,e,n,i){i&&"selectDataRange"===i.type&&i.from===this.uid||this._buildView()},n.prototype._buildView=function(){this.group.removeAll();var t=this.visualMapModel,e=this.group;this._orient=t.get("orient"),this._useHandle=t.get("calculable"),this._resetInterval(),this._renderBar(e);var n=t.get("text");this._renderEndsText(e,n,0),this._renderEndsText(e,n,1),this._updateView(!0),this.renderBackground(e),this._updateView(),this._enableHoverLinkToSeries(),this._enableHoverLinkFromSeries(),this.positionGroup(e)},n.prototype._renderEndsText=function(t,e,n){if(e){var i=e[1-n];i=null!=i?i+"":"";var o=this.visualMapModel,r=o.get("textGap"),a=o.itemSize,s=this._shapes.mainGroup,l=this._applyTransform([a[0]/2,0===n?-r:a[1]+r],s),u=this._applyTransform(0===n?"bottom":"top",s),d=this._orient,c=this.visualMapModel.textStyleModel;this.group.add(new an({style:sn(c,{x:l[0],y:l[1],verticalAlign:"horizontal"===d?"middle":u,align:"horizontal"===d?u:"center",text:i})}))}},n.prototype._renderBar=function(t){var e=this.visualMapModel,n=this._shapes,i=e.itemSize,o=this._orient,r=this._useHandle,a=z_(e,this.api,i),s=n.mainGroup=this._createBarGroup(a),l=new It;s.add(l),l.add(n.outOfRange=Y_()),l.add(n.inRange=Y_(null,r?U_(this._orient):null,Zt(this._dragHandle,this,"all",!1),Zt(this._dragHandle,this,"all",!0))),l.setClipPath(new pe({shape:{x:0,y:0,width:i[0],height:i[1],r:3}}));var u=e.textStyleModel.getTextRect("国"),d=H_(u.width,u.height);r&&(n.handleThumbs=[],n.handleLabels=[],n.handleLabelPoints=[],this._createHandle(e,s,0,i,d,o),this._createHandle(e,s,1,i,d,o)),this._createIndicator(e,s,i,d,o),t.add(s)},n.prototype._createHandle=function(t,e,n,i,o,r){var a=Zt(this._dragHandle,this,n,!1),s=Zt(this._dragHandle,this,n,!0),l=Oo(t.get("handleSize"),i[0]),u=At(t.get("handleIcon"),-l/2,-l/2,l,l,null,!0),d=U_(this._orient);u.attr({cursor:d,draggable:!0,drift:a,ondragend:s,onmousemove:function(t){ce(t.event)}}),u.x=i[0]/2,u.useStyle(t.getModel("handleStyle").getItemStyle()),u.setStyle({strokeNoScale:!0,strokeFirst:!0}),u.style.lineWidth*=2,u.ensureState("emphasis").style=t.getModel(["emphasis","handleStyle"]).getItemStyle(),cn(u,!0),e.add(u);var c=this.visualMapModel.textStyleModel,h=new an({cursor:d,draggable:!0,drift:a,onmousemove:function(t){ce(t.event)},ondragend:s,style:sn(c,{x:0,y:0,text:""})});h.ensureState("blur").style={opacity:.1},h.stateTransition={duration:200},this.group.add(h);var p=[l,0],g=this._shapes;g.handleThumbs[n]=u,g.handleLabelPoints[n]=p,g.handleLabels[n]=h},n.prototype._createIndicator=function(t,e,n,i,o){var r=Oo(t.get("indicatorSize"),n[0]),a=At(t.get("indicatorIcon"),-r/2,-r/2,r,r,null,!0);a.attr({cursor:"move",invisible:!0,silent:!0,x:n[0]/2});var s=t.getModel("indicatorStyle").getItemStyle();if(a instanceof R){var l=a.style;a.useStyle(H({image:l.image,x:l.x,y:l.y,width:l.width,height:l.height},s))}else a.useStyle(s);e.add(a);var u=this.visualMapModel.textStyleModel,d=new an({silent:!0,invisible:!0,style:sn(u,{x:0,y:0,text:""})});this.group.add(d);var c=[("horizontal"===o?i/2:6)+n[0]/2,0],h=this._shapes;h.indicator=a,h.indicatorLabel=d,h.indicatorLabelPoint=c,this._firstShowIndicator=!0},n.prototype._dragHandle=function(t,e,n,i){if(this._useHandle){if(this._dragging=!e,!e){var o=this._applyTransform([n,i],this._shapes.mainGroup,!0);this._updateInterval(t,o[1]),this._hideIndicator(),this._updateView()}e===!this.visualMapModel.get("realtime")&&this.api.dispatchAction({type:"selectDataRange",from:this.uid,visualMapId:this.visualMapModel.id,selected:this._dataInterval.slice()}),e?!this._hovering&&this._clearHoverLinkToSeries():X_(this.visualMapModel)&&this._doHoverLinkToSeries(this._handleEnds[t],!1)}},n.prototype._resetInterval=function(){var t=this.visualMapModel,e=this._dataInterval=t.getSelected(),n=t.getExtent(),i=[0,t.itemSize[1]];this._handleEnds=[G_(e[0],n,i,!0),G_(e[1],n,i,!0)]},n.prototype._updateInterval=function(t,e){e=e||0;var n=this.visualMapModel,i=this._handleEnds,o=[0,n.itemSize[1]];oh(e,i,o,t,0);var r=n.getExtent();this._dataInterval=[G_(i[0],o,r,!0),G_(i[1],o,r,!0)]},n.prototype._updateView=function(t){var e=this.visualMapModel,n=e.getExtent(),i=this._shapes,o=[0,e.itemSize[1]],r=t?o:this._handleEnds,a=this._createBarVisual(this._dataInterval,n,r,"inRange"),s=this._createBarVisual(n,n,o,"outOfRange");i.inRange.setStyle({fill:a.barColor}).setShape("points",a.barPoints),i.outOfRange.setStyle({fill:s.barColor}).setShape("points",s.barPoints),this._updateHandle(r,a)},n.prototype._createBarVisual=function(t,e,n,i){var o={forceState:i,convertOpacityToAlpha:!0},r=this._makeColorGradient(t,o),a=[this.getControllerVisual(t[0],"symbolSize",o),this.getControllerVisual(t[1],"symbolSize",o)],s=this._createBarPoints(n,a);return{barColor:new xe(0,0,0,1,r),barPoints:s,handlesColor:[r[0].color,r[r.length-1].color]}},n.prototype._makeColorGradient=function(t,e){var n=[],i=(t[1]-t[0])/100;n.push({color:this.getControllerVisual(t[0],"color",e),offset:0});for(var o=1;o<100;o++){var r=t[0]+i*o;if(r>t[1])break;n.push({color:this.getControllerVisual(r,"color",e),offset:o/100})}return n.push({color:this.getControllerVisual(t[1],"color",e),offset:1}),n},n.prototype._createBarPoints=function(t,e){var n=this.visualMapModel.itemSize;return[[n[0]-e[0],t[0]],[n[0],t[0]],[n[0],t[1]],[n[0]-e[1],t[1]]]},n.prototype._createBarGroup=function(t){var e=this._orient,n=this.visualMapModel.get("inverse");return new It("horizontal"!==e||n?"horizontal"===e&&n?{scaleX:"bottom"===t?-1:1,rotation:-Math.PI/2}:"vertical"!==e||n?{scaleX:"left"===t?1:-1}:{scaleX:"left"===t?1:-1,scaleY:-1}:{scaleX:"bottom"===t?1:-1,rotation:Math.PI/2})},n.prototype._updateHandle=function(t,e){if(this._useHandle){var n=this._shapes,i=this.visualMapModel,o=n.handleThumbs,r=n.handleLabels,a=i.itemSize,s=i.getExtent(),l=this._applyTransform("left",n.mainGroup);F_([0,1],(function(u){var d=o[u];d.setStyle("fill",e.handlesColor[u]),d.y=t[u];var c=G_(t[u],[0,a[1]],s,!0),h=this.getControllerVisual(c,"symbolSize");d.scaleX=d.scaleY=h/a[0],d.x=a[0]-h/2;var p=ei(n.handleLabelPoints[u],oi(d,this.group));if("horizontal"===this._orient){var g="left"===l||"top"===l?(a[0]-h)/2:(a[0]-h)/-2;p[1]+=g}r[u].setStyle({x:p[0],y:p[1],text:i.formatValueText(this._dataInterval[u]),verticalAlign:"middle",align:"vertical"===this._orient?this._applyTransform("left",n.mainGroup):"center"})}),this)}},n.prototype._showIndicator=function(t,e,n,i){var o=this.visualMapModel,r=o.getExtent(),a=o.itemSize,s=[0,a[1]],l=this._shapes,u=l.indicator;if(u){u.attr("invisible",!1);var d=this.getControllerVisual(t,"color",{convertOpacityToAlpha:!0}),c=this.getControllerVisual(t,"symbolSize"),h=G_(t,r,s,!0),p=a[0]-c/2,g={x:u.x,y:u.y};u.y=h,u.x=p;var f=ei(l.indicatorLabelPoint,oi(u,this.group)),y=l.indicatorLabel;y.attr("invisible",!1);var v=this._applyTransform("left",l.mainGroup),m="horizontal"===this._orient;y.setStyle({text:(n||"")+o.formatValueText(e),verticalAlign:m?v:"middle",align:m?"center":v});var x={x:p,y:h,style:{fill:d}},_={style:{x:f[0],y:f[1]}};if(o.ecModel.isAnimationEnabled()&&!this._firstShowIndicator){var b={duration:100,easing:"cubicInOut",additive:!0};u.x=g.x,u.y=g.y,u.animateTo(x,b),y.animateTo(_,b)}else u.attr(x),y.attr(_);this._firstShowIndicator=!1;var S=this._shapes.handleLabels;if(S)for(var w=0;w<S.length;w++)this.api.enterBlur(S[w])}},n.prototype._enableHoverLinkToSeries=function(){var t=this;this._shapes.mainGroup.on("mousemove",(function(e){if(t._hovering=!0,!t._dragging){var n=t.visualMapModel.itemSize,i=t._applyTransform([e.offsetX,e.offsetY],t._shapes.mainGroup,!0,!0);i[1]=W_(H_(0,i[1]),n[1]),t._doHoverLinkToSeries(i[1],0<=i[0]&&i[0]<=n[0])}})).on("mouseout",(function(){t._hovering=!1,!t._dragging&&t._clearHoverLinkToSeries()}))},n.prototype._enableHoverLinkFromSeries=function(){var t=this.api.getZr();this.visualMapModel.option.hoverLink?(t.on("mouseover",this._hoverLinkFromSeriesMouseOver,this),t.on("mouseout",this._hideIndicator,this)):this._clearHoverLinkFromSeries()},n.prototype._doHoverLinkToSeries=function(t,e){var n=this.visualMapModel,i=n.itemSize;if(n.option.hoverLink){var o=[0,i[1]],r=n.getExtent();t=W_(H_(o[0],t),o[1]);var a=function(t,e,n){var i=6,o=t.get("hoverLinkDataSize");o&&(i=G_(o,e,n,!0)/2);return i}(n,r,o),s=[t-a,t+a],l=G_(t,o,r,!0),u=[G_(s[0],o,r,!0),G_(s[1],o,r,!0)];s[0]<o[0]&&(u[0]=-1/0),s[1]>o[1]&&(u[1]=1/0),e&&(u[0]===-1/0?this._showIndicator(l,u[1],"< ",a):u[1]===1/0?this._showIndicator(l,u[0],"> ",a):this._showIndicator(l,l,"≈ ",a));var d=this._hoverLinkDataIndices,c=[];(e||X_(n))&&(c=this._hoverLinkDataIndices=n.findTargetDataIndices(u));var h=Uo(d,c);this._dispatchHighDown("downplay",B_(h[0],n)),this._dispatchHighDown("highlight",B_(h[1],n))}},n.prototype._hoverLinkFromSeriesMouseOver=function(t){var e;if(Ze(t.target,(function(t){var n=v(t);if(null!=n.dataIndex)return e=n,!0}),!0),e){var n=this.ecModel.getSeriesByIndex(e.seriesIndex),i=this.visualMapModel;if(i.isTargetSeries(n)){var o=n.getData(e.dataType),r=o.getStore().get(i.getDataDimensionIndex(o),e.dataIndex);isNaN(r)||this._showIndicator(r,r)}}},n.prototype._hideIndicator=function(){var t=this._shapes;t.indicator&&t.indicator.attr("invisible",!0),t.indicatorLabel&&t.indicatorLabel.attr("invisible",!0);var e=this._shapes.handleLabels;if(e)for(var n=0;n<e.length;n++)this.api.leaveBlur(e[n])},n.prototype._clearHoverLinkToSeries=function(){this._hideIndicator();var t=this._hoverLinkDataIndices;this._dispatchHighDown("downplay",B_(t,this.visualMapModel)),t.length=0},n.prototype._clearHoverLinkFromSeries=function(){this._hideIndicator();var t=this.api.getZr();t.off("mouseover",this._hoverLinkFromSeriesMouseOver),t.off("mouseout",this._hideIndicator)},n.prototype._applyTransform=function(t,e,n,i){var o=oi(e,i?null:this.group);return mt(t)?ei(t,o,n):ii(t,o,n)},n.prototype._dispatchHighDown=function(t,e){e&&e.length&&this.api.dispatchAction({type:t,batch:e})},n.prototype.dispose=function(){this._clearHoverLinkFromSeries(),this._clearHoverLinkToSeries()},n.type="visualMap.continuous",n}(O_);function Y_(t,e,n,i){return new Nt({shape:{points:t},draggable:!!n,cursor:e,drift:n,onmousemove:function(t){ce(t.event)},ondragend:i})}function X_(t){var e=t.get("hoverLinkOnHandle");return!!(null==e?t.get("realtime"):e)}function U_(t){return"vertical"===t?"ns-resize":"ew-resize"}var j_={type:"selectDataRange",event:"dataRangeSelected",update:"update"},q_=function(t,e){e.eachComponent({mainType:"visualMap",query:t},(function(e){e.setSelected(t.selected)}))},K_=[{createOnAllSeries:!0,reset:function(t,e){var n=[];return e.eachComponent("visualMap",(function(e){var i,o,r,s,l,u=t.pipelineContext;!e.isTargetSeries(t)||u&&u.large||n.push((i=e.stateList,o=e.targetVisuals,r=Zt(e.getValueState,e),s=e.getDataDimensionIndex(t.getData()),l={},a(i,(function(t){var e=Ju.prepareVisualTypes(o[t]);l[t]=e})),{progress:function(t,e){var n,i;function a(t){return Lo(e,i,t)}function u(t,n){Po(e,i,t,n)}null!=s&&(n=e.getDimensionIndex(s));for(var d=e.getStore();null!=(i=t.next());){var c=e.getRawDataItem(i);if(!c||!1!==c.visualMap)for(var h=null!=s?d.get(n,i):i,p=r(h),g=o[p],f=l[p],y=0,v=f.length;y<v;y++){var m=f[y];g[m]&&g[m].applyVisual(h,a,u)}}}}))})),n}},{createOnAllSeries:!0,reset:function(t,e){var n=t.getData(),i=[];e.eachComponent("visualMap",(function(e){if(e.isTargetSeries(t)){var o=e.getVisualMeta(Zt(J_,null,t,e))||{stops:[],outerColors:[]},r=e.getDataDimensionIndex(n);r>=0&&(o.dimension=r,i.push(o))}})),t.getData().setVisual("visualMeta",i)}}];function J_(t,e,n,i){for(var o=e.targetVisuals[i],r=Ju.prepareVisualTypes(o),a={color:Ho(t.getData(),"color")},s=0,l=r.length;s<l;s++){var u=r[s],d=o["opacity"===u?"__alphaForOpacity":u];d&&d.applyVisual(n,c,h)}return a.color;function c(t){return a[t]}function h(t,e){a[t]=e}}var $_=a;function Q_(t){var e=t&&t.visualMap;mt(e)||(e=e?[e]:[]),$_(e,(function(t){if(t){tb(t,"splitList")&&!tb(t,"pieces")&&(t.pieces=t.splitList,delete t.splitList);var e=t.pieces;e&&mt(e)&&$_(e,(function(t){xt(t)&&(tb(t,"start")&&!tb(t,"min")&&(t.min=t.start),tb(t,"end")&&!tb(t,"max")&&(t.max=t.end))}))}}))}function tb(t,e){return t&&t.hasOwnProperty&&t.hasOwnProperty(e)}var eb=!1;function nb(t){eb||(eb=!0,t.registerSubTypeDefaulter("visualMap",(function(t){return t.categories||(t.pieces?t.pieces.length>0:t.splitNumber>0)&&!t.calculable?"piecewise":"continuous"})),t.registerAction(j_,q_),a(K_,(function(e){t.registerVisual(t.PRIORITY.VISUAL.COMPONENT,e)})),t.registerPreprocessor(Q_))}function ib(t){t.registerComponentModel(R_),t.registerComponentView(Z_),nb(t)}var ob=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e._pieceList=[],e}return e(n,t),n.prototype.optionUpdated=function(e,n){t.prototype.optionUpdated.apply(this,arguments),this.resetExtent();var i=this._mode=this._determineMode();this._pieceList=[],rb[this._mode].call(this,this._pieceList),this._resetSelected(e,n);var o=this.option.categories;this.resetVisual((function(t,e){"categories"===i?(t.mappingMethod="category",t.categories=ct(o)):(t.dataExtent=this.getExtent(),t.mappingMethod="piecewise",t.pieceList=d(this._pieceList,(function(t){return t=ct(t),"inRange"!==e&&(t.visual=null),t})))}))},n.prototype.completeVisualOption=function(){var e=this.option,n={},i=Ju.listVisualTypes(),o=this.isCategory();function r(t,e,n){return t&&t[e]&&t[e].hasOwnProperty(n)}a(e.pieces,(function(t){a(i,(function(e){t.hasOwnProperty(e)&&(n[e]=1)}))})),a(n,(function(t,n){var i=!1;a(this.stateList,(function(t){i=i||r(e,t,n)||r(e.target,t,n)}),this),!i&&a(this.stateList,(function(t){(e[t]||(e[t]={}))[n]=M_(n,"inRange"===t?"active":"inactive",o)}))}),this),t.prototype.completeVisualOption.apply(this,arguments)},n.prototype._resetSelected=function(t,e){var n=this.option,i=this._pieceList,o=(e?n:t).selected||{};if(n.selected=o,a(i,(function(t,e){var n=this.getSelectedMapKey(t);o.hasOwnProperty(n)||(o[n]=!0)}),this),"single"===n.selectedMode){var r=!1;a(i,(function(t,e){var n=this.getSelectedMapKey(t);o[n]&&(r?o[n]=!1:r=!0)}),this)}},n.prototype.getItemSymbol=function(){return this.get("itemSymbol")},n.prototype.getSelectedMapKey=function(t){return"categories"===this._mode?t.value+"":t.index+""},n.prototype.getPieceList=function(){return this._pieceList},n.prototype._determineMode=function(){var t=this.option;return t.pieces&&t.pieces.length>0?"pieces":this.option.categories?"categories":"splitNumber"},n.prototype.setSelected=function(t){this.option.selected=ct(t)},n.prototype.getValueState=function(t){var e=Ju.findPieceIndex(t,this._pieceList);return null!=e&&this.option.selected[this.getSelectedMapKey(this._pieceList[e])]?"inRange":"outOfRange"},n.prototype.findTargetDataIndices=function(t){var e=[],n=this._pieceList;return this.eachTargetSeries((function(i){var o=[],r=i.getData();r.each(this.getDataDimensionIndex(r),(function(e,i){Ju.findPieceIndex(e,n)===t&&o.push(i)}),this),e.push({seriesId:i.id,dataIndex:o})}),this),e},n.prototype.getRepresentValue=function(t){var e;if(this.isCategory())e=t.value;else if(null!=t.value)e=t.value;else{var n=t.interval||[];e=n[0]===-1/0&&n[1]===1/0?0:(n[0]+n[1])/2}return e},n.prototype.getVisualMeta=function(t){if(!this.isCategory()){var e=[],n=["",""],i=this,o=this._pieceList.slice();if(o.length){var r=o[0].interval[0];r!==-1/0&&o.unshift({interval:[-1/0,r]}),(r=o[o.length-1].interval[1])!==1/0&&o.push({interval:[r,1/0]})}else o.push({interval:[-1/0,1/0]});var s=-1/0;return a(o,(function(t){var e=t.interval;e&&(e[0]>s&&l([s,e[0]],"outOfRange"),l(e.slice()),s=e[1])}),this),{stops:e,outerColors:n}}function l(o,r){var a=i.getRepresentValue({interval:o});r||(r=i.getValueState(a));var s=t(a,r);o[0]===-1/0?n[0]=s:o[1]===1/0?n[1]=s:e.push({value:o[0],color:s},{value:o[1],color:s})}},n.type="visualMap.piecewise",n.defaultOption=Ti(k_.defaultOption,{selected:null,minOpen:!1,maxOpen:!1,align:"auto",itemWidth:20,itemHeight:14,itemSymbol:"roundRect",pieces:null,categories:null,splitNumber:5,selectedMode:"multiple",itemGap:10,hoverLink:!0}),n}(k_),rb={splitNumber:function(t){var e=this.option,n=Math.min(e.precision,20),i=this.getExtent(),o=e.splitNumber;o=Math.max(parseInt(o,10),1),e.splitNumber=o;for(var r=(i[1]-i[0])/o;+r.toFixed(n)!==r&&n<5;)n++;e.precision=n,r=+r.toFixed(n),e.minOpen&&t.push({interval:[-1/0,i[0]],close:[0,0]});for(var s=0,l=i[0];s<o;l+=r,s++){var u=s===o-1?i[1]:l+r;t.push({interval:[l,u],close:[1,1]})}e.maxOpen&&t.push({interval:[i[1],1/0],close:[0,0]}),jo(t),a(t,(function(t,e){t.index=e,t.text=this.formatValueText(t.interval)}),this)},categories:function(t){var e=this.option;a(e.categories,(function(e){t.push({text:this.formatValueText(e,!0),value:e})}),this),ab(e,t)},pieces:function(t){var e=this.option;a(e.pieces,(function(e,n){xt(e)||(e={value:e});var i={text:"",index:n};if(null!=e.label&&(i.text=e.label),e.hasOwnProperty("value")){var o=i.value=e.value;i.interval=[o,o],i.close=[1,1]}else{for(var r=i.interval=[],a=i.close=[0,0],s=[1,0,1],l=[-1/0,1/0],u=[],d=0;d<2;d++){for(var c=[["gte","gt","min"],["lte","lt","max"]][d],h=0;h<3&&null==r[d];h++)r[d]=e[c[h]],a[d]=s[h],u[d]=2===h;null==r[d]&&(r[d]=l[d])}u[0]&&r[1]===1/0&&(a[0]=0),u[1]&&r[0]===-1/0&&(a[1]=0),r[0]===r[1]&&a[0]&&a[1]&&(i.value=r[0])}i.visual=Ju.retrieveVisuals(e),t.push(i)}),this),ab(e,t),jo(t),a(t,(function(t){var e=t.close,n=[["<","≤"][e[1]],[">","≥"][e[0]]];t.text=t.text||this.formatValueText(null!=t.value?t.value:t.interval,!1,n)}),this)}};function ab(t,e){var n=t.inverse;("vertical"===t.orient?!n:n)&&e.reverse()}var sb=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.doRender=function(){var t=this.group;t.removeAll();var e=this.visualMapModel,n=e.get("textGap"),i=e.textStyleModel,o=i.getFont(),r=i.getTextColor(),s=this._getItemAlign(),l=e.itemSize,u=this._getViewData(),d=u.endsText,c=Ge(e.get("showLabel",!0),!d),h=!e.get("selectedMode");d&&this._renderEndsText(t,d[0],l,c,s),a(u.viewPieceList,(function(i){var a=i.piece,u=new It;u.onclick=Zt(this._onItemClick,this,a),this._enableHoverLink(u,i.indexInModelPieceList);var d=e.getRepresentValue(a);if(this._createItemSymbol(u,d,[0,0,l[0],l[1]],h),c){var p=this.visualMapModel.getValueState(d);u.add(new an({style:{x:"right"===s?-n:l[0]+n,y:l[1]/2,text:a.text,verticalAlign:"middle",align:s,font:o,fill:r,opacity:"outOfRange"===p?.5:1},silent:h}))}t.add(u)}),this),d&&this._renderEndsText(t,d[1],l,c,s),qo(e.get("orient"),t,e.get("itemGap")),this.renderBackground(t),this.positionGroup(t)},n.prototype._enableHoverLink=function(t,e){var n=this;t.on("mouseover",(function(){return i("highlight")})).on("mouseout",(function(){return i("downplay")}));var i=function(t){var i=n.visualMapModel;i.option.hoverLink&&n.api.dispatchAction({type:t,batch:B_(i.findTargetDataIndices(e),i)})}},n.prototype._getItemAlign=function(){var t=this.visualMapModel,e=t.option;if("vertical"===e.orient)return z_(t,this.api,t.itemSize);var n=e.align;return n&&"auto"!==n||(n="left"),n},n.prototype._renderEndsText=function(t,e,n,i,o){if(e){var r=new It,a=this.visualMapModel.textStyleModel;r.add(new an({style:sn(a,{x:i?"right"===o?n[0]:0:n[0]/2,y:n[1]/2,verticalAlign:"middle",align:i?o:"center",text:e})})),t.add(r)}},n.prototype._getViewData=function(){var t=this.visualMapModel,e=d(t.getPieceList(),(function(t,e){return{piece:t,indexInModelPieceList:e}})),n=t.get("text"),i=t.get("orient"),o=t.get("inverse");return("horizontal"===i?o:!o)?e.reverse():n&&(n=n.slice().reverse()),{viewPieceList:e,endsText:n}},n.prototype._createItemSymbol=function(t,e,n,i){var o=At(this.getControllerVisual(e,"symbol"),n[0],n[1],n[2],n[3],this.getControllerVisual(e,"color"));o.silent=i,t.add(o)},n.prototype._onItemClick=function(t){var e=this.visualMapModel,n=e.option,i=n.selectedMode;if(i){var o=ct(n.selected),r=e.getSelectedMapKey(t);"single"===i||!0===i?(o[r]=!0,a(o,(function(t,e){o[e]=e===r}))):o[r]=!o[r],this.api.dispatchAction({type:"selectDataRange",from:this.uid,visualMapId:this.visualMapModel.id,selected:o})}},n.type="visualMap.piecewise",n}(O_);function lb(t){t.registerComponentModel(ob),t.registerComponentView(sb),nb(t)}var ub={label:{enabled:!0},decal:{show:!1}},db=h(),cb={};function hb(t,e){var n=t.getModel("aria");if(n.get("enabled")){var i=ct(ub);jt(i.label,t.getLocaleModel().get("aria"),!1),jt(n.option,i,!1),function(){if(n.getModel("decal").get("show")){var e=Ie();t.eachSeries((function(t){if(!t.isColorBySeries()){var n=e.get(t.type);n||(n={},e.set(t.type,n)),db(t).scope=n}})),t.eachRawSeries((function(e){if(!t.isSeriesFiltered(e))if(f(e.enableAriaDecal))e.enableAriaDecal();else{var n=e.getData();if(e.isColorBySeries()){var i=en(e.ecModel,e.name,cb,t.getSeriesCount()),o=n.getVisual("decal");n.setVisual("decal",u(o,i))}else{var r=e.getRawData(),a={},s=db(e).scope;n.each((function(t){var e=n.getRawIndex(t);a[e]=t}));var l=r.count();r.each((function(t){var i=a[t],o=r.getName(t)||t+"",d=en(e.ecModel,o,s,l),c=n.getItemVisual(i,"decal");n.setItemVisual(i,"decal",u(c,d))}))}}function u(t,e){var n=t?H(H({},e),t):e;return n.dirty=!0,n}}))}}(),function(){var i=e.getZr().dom;if(!i)return;var r=t.getLocaleModel().get("aria"),a=n.getModel("label");if(a.option=Ot(a.option,r),!a.get("enabled"))return;if(i.setAttribute("role","img"),a.get("description"))return void i.setAttribute("aria-label",a.get("description"));var s,l=t.getSeriesCount(),u=a.get(["data","maxCount"])||10,d=a.get(["series","maxCount"])||10,h=Math.min(l,d);if(l<1)return;var p=function(){var e=t.get("title");e&&e.length&&(e=e[0]);return e&&e.text}();s=p?o(a.get(["general","withTitle"]),{title:p}):a.get(["general","withoutTitle"]);var g=[];s+=o(l>1?a.get(["series","multiple","prefix"]):a.get(["series","single","prefix"]),{seriesCount:l}),t.eachSeries((function(e,n){if(n<h){var i=void 0,r=e.get("name")?"withName":"withoutName";i=o(i=l>1?a.get(["series","multiple",r]):a.get(["series","single",r]),{seriesId:e.seriesIndex,seriesName:e.get("name"),seriesType:(b=e.subType,S=t.getLocaleModel().get(["series","typeNames"]),S[b]||S.chart)});var s=e.getData();if(s.count()>u)i+=o(a.get(["data","partialData"]),{displayCnt:u});else i+=a.get(["data","allData"]);for(var d=a.get(["data","separator","middle"]),p=a.get(["data","separator","end"]),f=a.get(["data","excludeDimensionId"]),y=[],v=0;v<s.count();v++)if(v<u){var m=s.getName(v),x=f?c(s.getValues(v),(function(t,e){return-1===L(f,e)})):s.getValues(v),_=a.get(["data",m?"withName":"withoutName"]);y.push(o(_,{name:m,value:x.join(d)}))}i+=y.join(d)+p,g.push(i)}var b,S}));var f=a.getModel(["series","multiple","separator"]),y=f.get("middle"),v=f.get("end");s+=g.join(y)+v,i.setAttribute("aria-label",s)}()}function o(t,e){if(!G(t))return t;var n=t;return a(e,(function(t,e){n=n.replace(new RegExp("\\{\\s*"+e+"\\s*\\}","g"),t)})),n}}function pb(t){if(t&&t.aria){var e=t.aria;null!=e.show&&(e.enabled=e.show),e.label=e.label||{},a(["description","general","series","data"],(function(t){null!=e[t]&&(e.label[t]=e[t])}))}}var gb={value:"eq","<":"lt","<=":"lte",">":"gt",">=":"gte","=":"eq","!=":"ne","<>":"ne"},fb=function(){function t(t){if(null==(this._condVal=G(t)?new RegExp(t):$o(t)?t:null)){pi("")}}return t.prototype.evaluate=function(t){var e=typeof t;return G(e)?this._condVal.test(t):!!Y(e)&&this._condVal.test(t+"")},t}(),yb=function(){function t(){}return t.prototype.evaluate=function(){return this.value},t}(),vb=function(){function t(){}return t.prototype.evaluate=function(){for(var t=this.children,e=0;e<t.length;e++)if(!t[e].evaluate())return!1;return!0},t}(),mb=function(){function t(){}return t.prototype.evaluate=function(){for(var t=this.children,e=0;e<t.length;e++)if(t[e].evaluate())return!0;return!1},t}(),xb=function(){function t(){}return t.prototype.evaluate=function(){return!this.child.evaluate()},t}(),_b=function(){function t(){}return t.prototype.evaluate=function(){for(var t=!!this.valueParser,e=(0,this.getValue)(this.valueGetterParam),n=t?this.valueParser(e):null,i=0;i<this.subCondList.length;i++)if(!this.subCondList[i].evaluate(t?n:e))return!1;return!0},t}();function bb(t,e){if(!0===t||!1===t){var n=new yb;return n.value=t,n}return wb(t)||pi(""),t.and?Sb("and",t,e):t.or?Sb("or",t,e):t.not?function(t,e){var n=t.not,i="";wb(n)||pi(i);var o=new xb;o.child=bb(n,e),o.child||pi(i);return o}(t,e):function(t,e){for(var n="",i=e.prepareGetValue(t),o=[],r=y(t),a=t.parser,s=a?Ko(a):null,l=0;l<r.length;l++){var u=r[l];if("parser"!==u&&!e.valueGetterAttrMap.get(u)){var d=ge(gb,u)?gb[u]:u,c=t[u],h=s?s(c):c,p=Jo(d,h)||"reg"===d&&new fb(h);p||pi(n),o.push(p)}}o.length||pi(n);var g=new _b;return g.valueGetterParam=i,g.valueParser=s,g.getValue=e.getValue,g.subCondList=o,g}(t,e)}function Sb(t,e,n){var i=e[t];mt(i)||pi(""),i.length||pi("");var o="and"===t?new vb:new mb;return o.children=d(i,(function(t){return bb(t,n)})),o.children.length||pi(""),o}function wb(t){return xt(t)&&!Bi(t)}var Mb=function(){function t(t,e){this._cond=bb(t,e)}return t.prototype.evaluate=function(){return this._cond.evaluate()},t}();var Ib={type:"echarts:filter",transform:function(t){for(var e,n,i,o=t.upstream,r=(n=t.config,i={valueGetterAttrMap:Ie({dimension:!0}),prepareGetValue:function(t){var e=t.dimension;ge(t,"dimension")||pi("");var n=o.getDimensionInfo(e);return n||pi(""),{dimIdx:n.index}},getValue:function(t){return o.retrieveValueFromItem(e,t.dimIdx)}},new Mb(n,i)),a=[],s=0,l=o.count();s<l;s++)e=o.getRawDataItem(s),r.evaluate()&&a.push(e);return{data:a}}},Ab={type:"echarts:sort",transform:function(t){var e=t.upstream,n=t.config,i="",o=nn(n);o.length||pi(i);var r=[];a(o,(function(t){var n=t.dimension,o=t.order,a=t.parser,s=t.incomparable;if(null==n&&pi(i),"asc"!==o&&"desc"!==o&&pi(i),s&&"min"!==s&&"max"!==s){pi("")}if("asc"!==o&&"desc"!==o){pi("")}var l=e.getDimensionInfo(n);l||pi(i);var u=a?Ko(a):null;a&&!u&&pi(i),r.push({dimIdx:l.index,parser:u,comparator:new Qo(o,s)})}));var s=e.sourceFormat;s!==hi&&s!==tr&&pi(i);for(var l=[],u=0,d=e.count();u<d;u++)l.push(e.getRawDataItem(u));return l.sort((function(t,n){for(var i=0;i<r.length;i++){var o=r[i],a=e.retrieveValueFromItem(t,o.dimIdx),s=e.retrieveValueFromItem(n,o.dimIdx);o.parser&&(a=o.parser(a),s=o.parser(s));var l=o.comparator.evaluate(a,s);if(0!==l)return l}return 0})),{data:l}}};var Tb=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="dataset",e}return e(n,t),n.prototype.init=function(e,n,i){t.prototype.init.call(this,e,n,i),this._sourceManager=new er(this),nr(this)},n.prototype.mergeOption=function(e,n){t.prototype.mergeOption.call(this,e,n),nr(this)},n.prototype.optionUpdated=function(){this._sourceManager.dirty()},n.prototype.getSourceManager=function(){return this._sourceManager},n.type="dataset",n.defaultOption={seriesLayoutBy:ir},n}(Qt),Cb=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="dataset",e}return e(n,t),n.type="dataset",n}(oe);var Db=U.CMD;function Lb(t,e){return Math.abs(t-e)<1e-5}function Pb(t){var e,n,i,o,r,a,s,l,u,d,c,h,p,g,f,y,v,m,x,_,b,S,w,M,I=t.data,A=t.len(),T=[],C=0,D=0,L=0,P=0;function k(t,n){e&&e.length>2&&T.push(e),e=[t,n]}function N(t,n,i,o){Lb(t,i)&&Lb(n,o)||e.push(t,n,i,o,i,o)}for(var R=0;R<A;){var V=I[R++],O=1===R;switch(O&&(L=C=I[R],P=D=I[R+1],V!==Db.L&&V!==Db.C&&V!==Db.Q||(e=[L,P])),V){case Db.M:C=L=I[R++],D=P=I[R++],k(L,P);break;case Db.L:N(C,D,n=I[R++],i=I[R++]),C=n,D=i;break;case Db.C:e.push(I[R++],I[R++],I[R++],I[R++],C=I[R++],D=I[R++]);break;case Db.Q:n=I[R++],i=I[R++],o=I[R++],r=I[R++],e.push(C+2/3*(n-C),D+2/3*(i-D),o+2/3*(n-o),r+2/3*(i-r),o,r),C=o,D=r;break;case Db.A:var E=I[R++],z=I[R++],B=I[R++],G=I[R++],F=I[R++],W=I[R++]+F;R+=1;var H=!I[R++];n=Math.cos(F)*B+E,i=Math.sin(F)*G+z,O?k(L=n,P=i):N(C,D,n,i),C=Math.cos(W)*B+E,D=Math.sin(W)*G+z;for(var Z=(H?-1:1)*Math.PI/2,Y=F;H?Y>W:Y<W;Y+=Z){var X=H?Math.max(Y+Z,W):Math.min(Y+Z,W);a=Y,s=X,l=E,u=z,d=B,c=G,h=void 0,p=void 0,g=void 0,f=void 0,y=void 0,v=void 0,m=void 0,x=void 0,_=void 0,b=void 0,S=void 0,w=void 0,M=void 0,h=Math.abs(s-a),p=4*Math.tan(h/4)/3,g=s<a?-1:1,f=Math.cos(a),y=Math.sin(a),v=Math.cos(s),m=Math.sin(s),x=f*d+l,_=y*c+u,b=v*d+l,S=m*c+u,w=d*p*g,M=c*p*g,e.push(x-w*y,_+M*f,b+w*m,S-M*v,b,S)}break;case Db.R:L=C=I[R++],P=D=I[R++],n=L+I[R++],i=P+I[R++],k(n,P),N(n,P,n,i),N(n,i,L,i),N(L,i,L,P),N(L,P,n,P);break;case Db.Z:e&&N(C,D,L,P),C=L,D=P}}return e&&e.length>2&&T.push(e),T}function kb(t,e,n,i,o,r,a,s,l,u){if(Lb(t,n)&&Lb(e,i)&&Lb(o,a)&&Lb(r,s))l.push(a,s);else{var d=2/u,c=d*d,h=a-t,p=s-e,g=Math.sqrt(h*h+p*p);h/=g,p/=g;var f=n-t,y=i-e,v=o-a,m=r-s,x=f*f+y*y,_=v*v+m*m;if(x<c&&_<c)l.push(a,s);else{var b=h*f+p*y,S=-h*v-p*m;if(x-b*b<c&&b>=0&&_-S*S<c&&S>=0)l.push(a,s);else{var w=[],M=[];or(t,n,o,a,.5,w),or(e,i,r,s,.5,M),kb(w[0],M[0],w[1],M[1],w[2],M[2],w[3],M[3],l,u),kb(w[4],M[4],w[5],M[5],w[6],M[6],w[7],M[7],l,u)}}}}function Nb(t,e,n){var i=t[e],o=t[1-e],r=Math.abs(i/o),a=Math.ceil(Math.sqrt(r*n)),s=Math.floor(n/a);0===s&&(s=1,a=n);for(var l=[],u=0;u<a;u++)l.push(s);var d=n-a*s;if(d>0)for(u=0;u<d;u++)l[u%a]+=1;return l}function Rb(t,e,n){for(var i=t.r0,o=t.r,r=t.startAngle,a=t.endAngle,s=Math.abs(a-r),l=s*o,u=o-i,d=l>Math.abs(u),c=Nb([l,u],d?0:1,e),h=(d?s:u)/c.length,p=0;p<c.length;p++)for(var g=(d?u:s)/c[p],f=0;f<c[p];f++){var y={};d?(y.startAngle=r+h*p,y.endAngle=r+h*(p+1),y.r0=i+g*f,y.r=i+g*(f+1)):(y.startAngle=r+g*f,y.endAngle=r+g*(f+1),y.r0=i+h*p,y.r=i+h*(p+1)),y.clockwise=t.clockwise,y.cx=t.cx,y.cy=t.cy,n.push(y)}}function Vb(t,e,n,i){return t*i-n*e}function Ob(t,e,n,i,o,r,a,s){var l=n-t,u=i-e,d=a-o,c=s-r,h=Vb(d,c,l,u);if(Math.abs(h)<1e-6)return null;var p=Vb(t-o,e-r,d,c)/h;return p<0||p>1?null:new jn(p*l+t,p*u+e)}function Eb(t,e,n){var i=new jn;jn.sub(i,n,e),i.normalize();var o=new jn;return jn.sub(o,t,e),o.dot(i)}function zb(t,e){var n=t[t.length-1];n&&n[0]===e[0]&&n[1]===e[1]||t.push(e)}function Bb(t){var e=t.points,n=[],i=[];Xe(e,n,i);var o=new s(n[0],n[1],i[0]-n[0],i[1]-n[1]),r=o.width,a=o.height,l=o.x,u=o.y,d=new jn,c=new jn;return r>a?(d.x=c.x=l+r/2,d.y=u,c.y=u+a):(d.y=c.y=u+a/2,d.x=l,c.x=l+r),function(t,e,n){for(var i=t.length,o=[],r=0;r<i;r++){var a=t[r],s=t[(r+1)%i],l=Ob(a[0],a[1],s[0],s[1],e.x,e.y,n.x,n.y);l&&o.push({projPt:Eb(l,e,n),pt:l,idx:r})}if(o.length<2)return[{points:t},{points:t}];o.sort((function(t,e){return t.projPt-e.projPt}));var u=o[0],d=o[o.length-1];if(d.idx<u.idx){var c=u;u=d,d=c}var h=[u.pt.x,u.pt.y],p=[d.pt.x,d.pt.y],g=[h],f=[p];for(r=u.idx+1;r<=d.idx;r++)zb(g,t[r].slice());for(zb(g,p),zb(g,h),r=d.idx+1;r<=u.idx+i;r++)zb(f,t[r%i].slice());return zb(f,h),zb(f,p),[{points:g},{points:f}]}(e,d,c)}function Gb(t,e,n,i){if(1===n)i.push(e);else{var o=Math.floor(n/2),r=t(e);Gb(t,r[0],o,i),Gb(t,r[1],n-o,i)}return i}function Fb(t,e){var n,i=[],o=t.shape;switch(t.type){case"rect":!function(t,e,n){for(var i=t.width,o=t.height,r=i>o,a=Nb([i,o],r?0:1,e),s=r?"width":"height",l=r?"height":"width",u=r?"x":"y",d=r?"y":"x",c=t[s]/a.length,h=0;h<a.length;h++)for(var p=t[l]/a[h],g=0;g<a[h];g++){var f={};f[u]=h*c,f[d]=g*p,f[s]=c,f[l]=p,f.x+=t.x,f.y+=t.y,n.push(f)}}(o,e,i),n=pe;break;case"sector":Rb(o,e,i),n=Zn;break;case"circle":Rb({r0:0,r:o.r,startAngle:0,endAngle:2*Math.PI,cx:o.cx,cy:o.cy},e,i),n=Zn;break;default:var r=t.getComputedTransform(),a=r?Math.sqrt(Math.max(r[0]*r[0]+r[1]*r[1],r[2]*r[2]+r[3]*r[3])):1,s=d(function(t,e){var n=Pb(t),i=[];e=e||1;for(var o=0;o<n.length;o++){var r=n[o],a=[],s=r[0],l=r[1];a.push(s,l);for(var u=2;u<r.length;){var d=r[u++],c=r[u++],h=r[u++],p=r[u++],g=r[u++],f=r[u++];kb(s,l,d,c,h,p,g,f,a,e),s=g,l=f}i.push(a)}return i}(t.getUpdatedPathProxy(),a),(function(t){return function(t){for(var e=[],n=0;n<t.length;)e.push([t[n++],t[n++]]);return e}(t)})),l=s.length;if(0===l)Gb(Bb,{points:s[0]},e,i);else if(l===e)for(var u=0;u<l;u++)i.push({points:s[u]});else{var c=0,h=d(s,(function(t){var e=[],n=[];Xe(t,e,n);var i=(n[1]-e[1])*(n[0]-e[0]);return c+=i,{poly:t,area:i}}));h.sort((function(t,e){return e.area-t.area}));var p=e;for(u=0;u<l;u++){var g=h[u];if(p<=0)break;var f=u===l-1?p:Math.ceil(g.area/c*e);f<0||(Gb(Bb,{points:g.poly},f,i),p-=f)}}n=Nt}if(!n)return function(t,e){for(var n=[],i=0;i<e;i++)n.push(rr(t));return n}(t,e);var y,v,m=[];for(u=0;u<i.length;u++){var x=new n;x.setShape(i[u]),y=t,(v=x).setStyle(y.style),v.z=y.z,v.z2=y.z2,v.zlevel=y.zlevel,m.push(x)}return m}function Wb(t,e){var n=t.length,i=e.length;if(n===i)return[t,e];for(var o=[],r=[],a=n<i?t:e,s=Math.min(n,i),l=Math.abs(i-n)/6,u=(s-2)/6,d=Math.ceil(l/u)+1,c=[a[0],a[1]],h=l,p=2;p<s;){var g=a[p-2],f=a[p-1],y=a[p++],v=a[p++],m=a[p++],x=a[p++],_=a[p++],b=a[p++];if(h<=0)c.push(y,v,m,x,_,b);else{for(var S=Math.min(h,d-1)+1,w=1;w<=S;w++){var M=w/S;or(g,y,m,_,M,o),or(f,v,x,b,M,r),g=o[3],f=r[3],c.push(o[1],r[1],o[2],r[2],g,f),y=o[5],v=r[5],m=o[6],x=r[6]}h-=S-1}}return a===t?[c,e]:[t,c]}function Hb(t,e){for(var n=t.length,i=t[n-2],o=t[n-1],r=[],a=0;a<e.length;)r[a++]=i,r[a++]=o;return r}function Zb(t){for(var e=0,n=0,i=0,o=t.length,r=0,a=o-2;r<o;a=r,r+=2){var s=t[a],l=t[a+1],u=t[r],d=t[r+1],c=s*d-u*l;e+=c,n+=(s+u)*c,i+=(l+d)*c}return 0===e?[t[0]||0,t[1]||0]:[n/e/3,i/e/3,e]}function Yb(t,e,n,i){for(var o=(t.length-2)/6,r=1/0,a=0,s=t.length,l=s-2,u=0;u<o;u++){for(var d=6*u,c=0,h=0;h<s;h+=2){var p=0===h?d:(d+h-2)%l+2,g=t[p]-n[0],f=t[p+1]-n[1],y=e[h]-i[0]-g,v=e[h+1]-i[1]-f;c+=y*y+v*v}c<r&&(r=c,a=u)}return a}function Xb(t){for(var e=[],n=t.length,i=0;i<n;i+=2)e[i]=t[n-i-2],e[i+1]=t[n-i-1];return e}function Ub(t){return t.__isCombineMorphing}var jb="__mOriginal_";function qb(t,e,n){var i=jb+e,o=t[i]||t[e];t[i]||(t[i]=t[e]);var r=n.replace,a=n.after,s=n.before;t[e]=function(){var t,e=arguments;return s&&s.apply(this,e),t=r?r.apply(this,e):o.apply(this,e),a&&a.apply(this,e),t}}function Kb(t,e){var n=jb+e;t[n]&&(t[e]=t[n],t[n]=null)}function Jb(t,e){for(var n=0;n<t.length;n++)for(var i=t[n],o=0;o<i.length;){var r=i[o],a=i[o+1];i[o++]=e[0]*r+e[2]*a+e[4],i[o++]=e[1]*r+e[3]*a+e[5]}}function $b(t,e){var n=t.getUpdatedPathProxy(),i=e.getUpdatedPathProxy(),o=function(t,e){for(var n,i,o,r=[],a=[],s=0;s<Math.max(t.length,e.length);s++){var l=t[s],u=e[s],d=void 0,c=void 0;l?u?(i=d=(n=Wb(l,u))[0],o=c=n[1]):(c=Hb(o||l,l),d=l):(d=Hb(i||u,u),c=u),r.push(d),a.push(c)}return[r,a]}(Pb(n),Pb(i)),r=o[0],a=o[1],s=t.getComputedTransform(),l=e.getComputedTransform();s&&Jb(r,s),l&&Jb(a,l),qb(e,"updateTransform",{replace:function(){this.transform=null}}),e.transform=null;var u=function(t,e,n,i){for(var o,r=[],a=0;a<t.length;a++){var s=t[a],l=e[a],u=Zb(s),d=Zb(l);null==o&&(o=u[2]<0!=d[2]<0);var c=[],h=[],p=0,g=1/0,f=[],y=s.length;o&&(s=Xb(s));for(var v=6*Yb(s,l,u,d),m=y-2,x=0;x<m;x+=2){var _=(v+x)%m+2;c[x+2]=s[_]-u[0],c[x+3]=s[_+1]-u[1]}c[0]=s[v]-u[0],c[1]=s[v+1]-u[1];for(var b=i/n,S=-i/2;S<=i/2;S+=b){var w=Math.sin(S),M=Math.cos(S),I=0;for(x=0;x<s.length;x+=2){var A=c[x],T=c[x+1],C=l[x]-d[0],D=l[x+1]-d[1],L=C*M-D*w,P=C*w+D*M;f[x]=L,f[x+1]=P;var k=L-A,N=P-T;I+=k*k+N*N}if(I<g){g=I,p=S;for(var R=0;R<f.length;R++)h[R]=f[R]}}r.push({from:c,to:h,fromCp:u,toCp:d,rotation:-p})}return r}(r,a,10,Math.PI),d=[];qb(e,"buildPath",{replace:function(t){for(var n=e.__morphT,i=1-n,o=[],r=0;r<u.length;r++){var a=u[r],s=a.from,l=a.to,c=a.rotation*n,h=a.fromCp,p=a.toCp,g=Math.sin(c),f=Math.cos(c);ar(o,h,p,n);for(var y=0;y<s.length;y+=2){var v=s[y],m=s[y+1],x=v*i+(w=l[y])*n,_=m*i+(M=l[y+1])*n;d[y]=x*f-_*g+o[0],d[y+1]=x*g+_*f+o[1]}var b=d[0],S=d[1];t.moveTo(b,S);for(y=2;y<s.length;){var w=d[y++],M=d[y++],I=d[y++],A=d[y++],T=d[y++],C=d[y++];b===w&&S===M&&I===T&&A===C?t.lineTo(T,C):t.bezierCurveTo(w,M,I,A,T,C),b=T,S=C}}}})}function Qb(t,e,n){if(!t||!e)return e;var i=n.done,o=n.during;return $b(t,e),e.__morphT=0,e.animateTo({__morphT:1},Ot({during:function(t){e.dirtyShape(),o&&o(t)},done:function(){Kb(e,"buildPath"),Kb(e,"updateTransform"),e.__morphT=-1,e.createPathProxy(),e.dirtyShape(),i&&i()}},n)),e}function tS(t,e,n,i,o,r){t=o===n?0:Math.round(32767*(t-n)/(o-n)),e=r===i?0:Math.round(32767*(e-i)/(r-i));for(var a,s=0,l=32768;l>0;l/=2){var u=0,d=0;(t&l)>0&&(u=1),(e&l)>0&&(d=1),s+=l*l*(3*u^d),0===d&&(1===u&&(t=l-1-t,e=l-1-e),a=t,t=e,e=a)}return s}function eS(t){var e=1/0,n=1/0,i=-1/0,o=-1/0,r=d(t,(function(t){var r=t.getBoundingRect(),a=t.getComputedTransform(),s=r.x+r.width/2+(a?a[4]:0),l=r.y+r.height/2+(a?a[5]:0);return e=Math.min(s,e),n=Math.min(l,n),i=Math.max(s,i),o=Math.max(l,o),[s,l]}));return d(r,(function(r,a){return{cp:r,z:tS(r[0],r[1],e,n,i,o),path:t[a]}})).sort((function(t,e){return t.z-e.z})).map((function(t){return t.path}))}function nS(t){return Fb(t.path,t.count)}function iS(t){return mt(t[0])}function oS(t,e){for(var n=[],i=t.length,o=0;o<i;o++)n.push({one:t[o],many:[]});for(o=0;o<e.length;o++){var r=e[o].length,a=void 0;for(a=0;a<r;a++)n[a%i].many.push(e[o][a])}var s=0;for(o=i-1;o>=0;o--)if(!n[o].many.length){var l=n[s].many;if(l.length<=1){if(!s)return n;s=0}r=l.length;var u=Math.ceil(r/2);n[o].many=l.slice(u,r),n[s].many=l.slice(0,u),s++}return n}var rS={clone:function(t){for(var e=[],n=1-Math.pow(1-t.path.style.opacity,1/t.count),i=0;i<t.count;i++){var o=rr(t.path);o.setStyle("opacity",n),e.push(o)}return e},split:null};function aS(t,e,n,i,o,r){if(t.length&&e.length){var a=Oi("update",i,o);if(a&&a.duration>0){var s,l,u=i.getModel("universalTransition").get("delay"),d=Object.assign({setToFinal:!0},a);iS(t)&&(s=t,l=e),iS(e)&&(s=e,l=t);for(var c=s?s===t:t.length>e.length,h=s?oS(l,s):oS(c?e:t,[c?t:e]),g=0,f=0;f<h.length;f++)g+=h[f].many.length;var y=0;for(f=0;f<h.length;f++)v(h[f],c,y,g),y+=h[f].many.length}}function v(t,e,i,o,a){var s=t.many,l=t.one;if(1!==s.length||a)for(var c=Ot({dividePath:rS[n],individualDelay:u&&function(t,e,n,r){return u(t+i,o)}},d),h=e?function(t,e,n){var i=[];!function t(e){for(var n=0;n<e.length;n++){var o=e[n];Ub(o)?t(o.childrenRef()):o instanceof q&&i.push(o)}}(t);var o=i.length;if(!o)return{fromIndividuals:[],toIndividuals:[],count:0};var r=(n.dividePath||nS)({path:e,count:o});if(r.length!==o)return{fromIndividuals:[],toIndividuals:[],count:0};i=eS(i),r=eS(r);for(var a=n.done,s=n.during,l=n.individualDelay,u=new p,d=0;d<o;d++){var c=i[d],h=r[d];h.parent=e,h.copyTransform(u),l||$b(c,h)}function g(t){for(var e=0;e<r.length;e++)r[e].addSelfToZr(t)}function f(){e.__isCombineMorphing=!1,e.__morphT=-1,e.childrenRef=null,Kb(e,"addSelfToZr"),Kb(e,"removeSelfFromZr")}e.__isCombineMorphing=!0,e.childrenRef=function(){return r},qb(e,"addSelfToZr",{after:function(t){g(t)}}),qb(e,"removeSelfFromZr",{after:function(t){for(var e=0;e<r.length;e++)r[e].removeSelfFromZr(t)}});var y=r.length;if(l){var v=y,m=function(){0==--v&&(f(),a&&a())};for(d=0;d<y;d++){var x=l?Ot({delay:(n.delay||0)+l(d,y,i[d],r[d]),done:m},n):n;Qb(i[d],r[d],x)}}else e.__morphT=0,e.animateTo({__morphT:1},Ot({during:function(t){for(var n=0;n<y;n++){var i=r[n];i.__morphT=e.__morphT,i.dirtyShape()}s&&s(t)},done:function(){f();for(var e=0;e<t.length;e++)Kb(t[e],"updateTransform");a&&a()}},n));return e.__zr&&g(e.__zr),{fromIndividuals:i,toIndividuals:r,count:y}}(s,l,c):function(t,e,n){var i=e.length,o=[],r=n.dividePath||nS;if(Ub(t)){!function t(e){for(var n=0;n<e.length;n++){var i=e[n];Ub(i)?t(i.childrenRef()):i instanceof q&&o.push(i)}}(t.childrenRef());var a=o.length;if(a<i)for(var s=0,l=a;l<i;l++)o.push(rr(o[s++%a]));o.length=i}else{o=r({path:t,count:i});var u=t.getComputedTransform();for(l=0;l<o.length;l++)o[l].setLocalTransform(u);if(o.length!==i)return{fromIndividuals:[],toIndividuals:[],count:0}}o=eS(o),e=eS(e);var d=n.individualDelay;for(l=0;l<i;l++){var c=d?Ot({delay:(n.delay||0)+d(l,i,o[l],e[l])},n):n;Qb(o[l],e[l],c)}return{fromIndividuals:o,toIndividuals:e,count:e.length}}(l,s,c),g=h.fromIndividuals,f=h.toIndividuals,y=g.length,m=0;m<y;m++){x=u?Ot({delay:u(m,y)},d):d;r(g[m],f[m],e?s[m]:t.one,e?t.one:s[m],x)}else{var x,_=e?s[0]:l,b=e?l:s[0];if(Ub(_))v({many:[_],one:b},!0,i,o,!0);else Qb(_,b,x=u?Ot({delay:u(i,o)},d):d),r(_,b,_,b,x)}}}function sS(t){if(!t)return[];if(mt(t)){for(var e=[],n=0;n<t.length;n++)e.push(sS(t[n]));return e}var i=[];return t.traverse((function(t){t instanceof q&&!t.disableMorphing&&!t.invisible&&!t.ignore&&i.push(t)})),i}var lS=h();function uS(t,e,n,i){var o=function(t,e){for(var n=t.dimensions,i=0;i<n.length;i++){var o=t.getDimensionInfo(n[i]);if(o&&0===o.otherDims[e])return n[i]}}(t,i?"itemChildGroupId":"itemGroupId");if(o){var r=function(t,e,n){var i=t.getDimensionInfo(n),o=i&&i.ordinalMeta;if(i){var r=t.get(i.name,e);return o&&o.categories[r]||r+""}}(t,e,o);return r}var a=t.getRawDataItem(e),s=i?"childGroupId":"groupId";return a&&a[s]?a[s]+"":i?void 0:n||t.getId(e)}function dS(t){var e=[];return a(t,(function(t){var n=t.data,i=t.dataGroupId;if(!(n.count()>1e4))for(var o=n.getIndices(),r=0;r<o.length;r++)e.push({data:n,groupId:uS(n,r,i,!1),childGroupId:uS(n,r,i,!0),divide:t.divide,dataIndex:r})})),e}function cS(t,e,n){t.traverse((function(t){t instanceof q&&D(t,{style:{opacity:0}},e,{dataIndex:n,isFrom:!0})}))}function hS(t){if(t.parent){var e=t.getComputedTransform();t.setLocalTransform(e),t.parent.remove(t)}}function pS(t){t.stopAnimation(),t.isGroup&&t.traverse((function(t){t.stopAnimation()}))}function gS(t,e,n){var i=dS(t),o=dS(e);function r(t,e,n,i,o){(n||t)&&e.animateFrom({style:n&&n!==t?H(H({},n.style),t.style):t.style},o)}var s=!1,l=0,u=Ie(),h=Ie();i.forEach((function(t){t.groupId&&u.set(t.groupId,!0),t.childGroupId&&h.set(t.childGroupId,!0)}));for(var p=0;p<o.length;p++){var g=o[p].groupId;if(h.get(g)){l=1;break}var f=o[p].childGroupId;if(f&&u.get(f)){l=2;break}}function y(t,e){return function(n){var i=n.data,o=n.dataIndex;return e?i.getId(o):t?1===l?n.childGroupId:n.groupId:2===l?n.childGroupId:n.groupId}}var v=function(t,e){var n=t.length;if(n!==e.length)return!1;for(var i=0;i<n;i++){var o=t[i],r=e[i];if(o.data.getId(o.dataIndex)!==r.data.getId(r.dataIndex))return!1}return!0}(i,o),m={};if(!v)for(p=0;p<o.length;p++){var x=o[p],_=x.data.getItemGraphicEl(x.dataIndex);_&&(m[_.id]=!0)}function b(t,e){var n=i[e],a=o[t],l=a.data.hostModel,u=n.data.getItemGraphicEl(n.dataIndex),d=a.data.getItemGraphicEl(a.dataIndex);u!==d?u&&m[u.id]||d&&(pS(d),u?(pS(u),hS(u),s=!0,aS(sS(u),sS(d),a.divide,l,t,r)):cS(d,l,t)):d&&function(t,e,n){var i=Oi("update",n,e);i&&t.traverse((function(t){if(t instanceof Te){var e=lr(t);e&&t.animateFrom({style:e},i)}}))}(d,a.dataIndex,l)}new ln(i,o,y(!0,v),y(!1,v),null,"multiple").update(b).updateManyToOne((function(t,e){var n=o[t],l=n.data,u=l.hostModel,h=l.getItemGraphicEl(n.dataIndex),p=c(d(e,(function(t){return i[t].data.getItemGraphicEl(i[t].dataIndex)})),(function(t){return t&&t!==h&&!m[t.id]}));h&&(pS(h),p.length?(a(p,(function(t){pS(t),hS(t)})),s=!0,aS(sS(p),sS(h),n.divide,u,t,r)):cS(h,u,n.dataIndex))})).updateOneToMany((function(t,e){var n=i[e],l=n.data.getItemGraphicEl(n.dataIndex);if(!l||!m[l.id]){var u=c(d(t,(function(t){return o[t].data.getItemGraphicEl(o[t].dataIndex)})),(function(t){return t&&t!==l})),h=o[t[0]].data.hostModel;u.length&&(a(u,(function(t){return pS(t)})),l?(pS(l),hS(l),s=!0,aS(sS(l),sS(u),n.divide,h,t[0],r)):a(u,(function(e){return cS(e,h,t[0])})))}})).updateManyToMany((function(t,e){new ln(e,t,(function(t){return i[t].data.getId(i[t].dataIndex)}),(function(t){return o[t].data.getId(o[t].dataIndex)})).update((function(n,i){b(t[n],e[i])})).execute()})).execute(),s&&a(e,(function(t){var e=t.data.hostModel,i=e&&n.getViewOfSeriesModel(e),o=Oi("update",e,0);i&&e.isAnimationEnabled()&&o&&o.duration>0&&i.group.traverse((function(t){t instanceof q&&!t.animators.length&&t.animateFrom({style:{opacity:0}},o)}))}))}function fS(t){var e=t.getModel("universalTransition").get("seriesKey");return e||t.id}function yS(t){return mt(t)?t.sort().join(","):t}function vS(t){if(t.hostModel)return t.hostModel.getModel("universalTransition").get("divideShape")}function mS(t,e){for(var n=0;n<t.length;n++){if(null!=e.seriesIndex&&e.seriesIndex===t[n].seriesIndex||null!=e.seriesId&&e.seriesId===t[n].id)return n}}Lt([ur]),Lt([function(t){t.registerPainter("svg",Ja)}]),Lt([dr,cr,hr,function(t){Lt(Pt),t.registerSeriesModel($a),t.registerChartView(ns),t.registerLayout(Tt("scatter"))},function(t){Lt(ys),t.registerChartView(ss),t.registerSeriesModel(ls),t.registerLayout(is),t.registerProcessor(le("radar")),t.registerPreprocessor(as)},function(t){Lt(Yl),t.registerChartView(Al),t.registerSeriesModel(Tl),t.registerLayout(Dl),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,Cl),Ye("map",t.registerAction)},function(t){t.registerChartView(iu),t.registerSeriesModel(Iu),t.registerLayout(Tu),t.registerVisual(Cu),function(t){t.registerAction({type:"treeExpandAndCollapse",event:"treeExpandAndCollapse",update:"update"},(function(t,e){e.eachComponent({mainType:"series",subType:"tree",query:t},(function(e){var n=t.dataIndex,i=e.getData().tree.getNodeByDataIndex(n);i.isExpand=!i.isExpand}))})),t.registerAction({type:"treeRoam",event:"treeRoam",update:"none"},(function(t,e,n){e.eachComponent({mainType:"series",subType:"tree",query:t},(function(e){var i=Wl(e.coordinateSystem,t,void 0,n);e.setCenter&&e.setCenter(i.center),e.setZoom&&e.setZoom(i.zoom)}))}))}(t)},function(t){t.registerSeriesModel(Pu),t.registerChartView(Uu),t.registerVisual(dd),t.registerLayout(wd),function(t){for(var e=0;e<Du.length;e++)t.registerAction({type:Du[e],update:"updateView"},St);t.registerAction({type:"treemapRootToNode",update:"updateView"},(function(t,e){e.eachComponent({mainType:"series",subType:"treemap",query:t},(function(e,n){var i=bu(t,["treemapZoomToNode","treemapRootToNode"],e);if(i){var o=e.getViewRoot();o&&(t.direction=wu(o,i.node)?"rollUp":"drillDown"),e.resetViewRoot(i.node)}}))}))}(t)},function(t){t.registerChartView(Mc),t.registerSeriesModel(Pc),t.registerProcessor(Dd),t.registerVisual(Ld),t.registerVisual(kd),t.registerLayout(Wd),t.registerLayout(t.PRIORITY.VISUAL.POST_CHART_LAYOUT,Kd),t.registerLayout($d),t.registerCoordinateSystem("graphView",{dimensions:Pl.dimensions,create:Qd}),t.registerAction({type:"focusNodeAdjacency",event:"focusNodeAdjacency",update:"series:focusNodeAdjacency"},St),t.registerAction({type:"unfocusNodeAdjacency",event:"unfocusNodeAdjacency",update:"series:unfocusNodeAdjacency"},St),t.registerAction(kc,(function(t,e,n){e.eachComponent({mainType:"series",query:t},(function(e){var i=Wl(e.coordinateSystem,t,void 0,n);e.setCenter&&e.setCenter(i.center),e.setZoom&&e.setZoom(i.zoom)}))}))},function(t){t.registerChartView(Oc),t.registerSeriesModel(Ec)},function(t){t.registerChartView(Gc),t.registerSeriesModel(Fc),t.registerLayout(Wc),t.registerProcessor(le("funnel"))},function(t){Lt(gp),t.registerChartView(Hc),t.registerSeriesModel(jc),t.registerVisual(t.PRIORITY.VISUAL.BRUSH,Jc)},function(t){t.registerChartView(vp),t.registerSeriesModel(xp),t.registerLayout(_p),t.registerVisual(kp),t.registerAction({type:"dragNode",event:"dragnode",update:"update"},(function(t,e){e.eachComponent({mainType:"series",subType:"sankey",query:t},(function(e){e.setNodePosition(t.dataIndex,[t.localX,t.localY])}))}))},function(t){t.registerSeriesModel(Rp),t.registerChartView(Vp),t.registerLayout(Wp),t.registerTransform(Hp)},function(t){t.registerChartView(Qp),t.registerSeriesModel(dg),t.registerPreprocessor(cg),t.registerVisual(Jp),t.registerLayout(hg)},function(t){t.registerChartView(yg),t.registerSeriesModel(vg),t.registerLayout(Tt("effectScatter"))},function(t){t.registerChartView(Ig),t.registerSeriesModel(Dg),t.registerLayout(Mg),t.registerVisual(Pg)},function(t){t.registerChartView(Rg),t.registerSeriesModel(Vg)},function(t){t.registerChartView(Bg),t.registerSeriesModel(of),t.registerLayout(t.PRIORITY.VISUAL.LAYOUT,Re(Di,"pictorialBar")),t.registerLayout(t.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,Li("pictorialBar"))},function(t){t.registerChartView(rf),t.registerSeriesModel(af),t.registerLayout(sf),t.registerProcessor(le("themeRiver"))},function(t){t.registerChartView(hf),t.registerSeriesModel(pf),t.registerLayout(Re(yf,"sunburst")),t.registerProcessor(Re(le,"sunburst")),t.registerVisual(mf),function(t){t.registerAction({type:df,update:"updateView"},(function(t,e){e.eachComponent({mainType:"series",subType:"sunburst",query:t},(function(e,n){var i=bu(t,[df],e);if(i){var o=e.getViewRoot();o&&(t.direction=wu(o,i.node)?"rollUp":"drillDown"),e.resetViewRoot(i.node)}}))})),t.registerAction({type:cf,update:"none"},(function(t,e,n){t=H({},t),e.eachComponent({mainType:"series",subType:"sunburst",query:t},(function(e){var n=bu(t,[cf],e);n&&(t.dataIndex=n.node.dataIndex)})),n.dispatchAction(H(t,{type:"highlight"}))})),t.registerAction({type:"sunburstUnhighlight",update:"updateView"},(function(t,e,n){t=H({},t),n.dispatchAction(H(t,{type:"downplay"}))}))}(t)},function(t){t.registerChartView(uy),t.registerSeriesModel(Sf)}]),Lt(pr),Lt((function(t){Lt(to),$i.registerAxisPointerClass("PolarAxisPointer",Ty),t.registerCoordinateSystem("polar",Fy),t.registerComponentModel(Dy),t.registerComponentView(iv),si(t,"angle",Py,ev),si(t,"radius",ky,nv),t.registerComponentView(Xy),t.registerComponentView(Ky),t.registerLayout(Re(tv,"bar"))})),Lt(Yl),Lt((function(t){Lt(to),$i.registerAxisPointerClass("SingleAxisPointer",vv),t.registerComponentView(bv),t.registerComponentView(sv),t.registerComponentModel(uv),si(t,"single",uv,uv.defaultOption),t.registerCoordinateSystem("single",gv)})),Lt(gp),Lt((function(t){t.registerComponentModel(Sv),t.registerComponentView(Mv),t.registerCoordinateSystem("calendar",Av)})),Lt((function(t){t.registerComponentModel(kv),t.registerComponentView(Vv),t.registerPreprocessor((function(t){var e=t.graphic;mt(e)?e[0]&&e[0].elements?t.graphic=[t.graphic[0]]:t.graphic=[{elements:e}]:e&&!e.elements&&(t.graphic=[{elements:[e]}])}))})),Lt((function(t){t.registerComponentModel(lm),t.registerComponentView(um),am("saveAsImage",dm),am("magicType",pm),am("dataView",_m),am("dataZoom",Gm),am("restore",Im),Lt(im)})),Lt(gr),Lt(to),Lt((function(t){t.registerComponentView(sx),t.registerComponentModel(lx),t.registerPreprocessor(Zm),t.registerVisual(t.PRIORITY.VISUAL.BRUSH,nx),t.registerAction({type:"brush",event:"brush",update:"updateVisual"},(function(t,e){e.eachComponent({mainType:"brush",query:t},(function(e){e.setAreas(t.areas)}))})),t.registerAction({type:"brushSelect",event:"brushSelected",update:"none"},St),t.registerAction({type:"brushEnd",event:"brushEnd",update:"none"},St),am("brush",cx)})),Lt(fr),Lt((function(t){t.registerComponentModel(px),t.registerComponentView(mx),t.registerSubTypeDefaulter("timeline",(function(){return"slider"})),function(t){t.registerAction({type:"timelineChange",event:"timelineChanged",update:"prepareAndUpdate"},(function(t,e,n){var i=e.getComponent("timeline");return i&&null!=t.currentIndex&&(i.setCurrentIndex(t.currentIndex),!i.get("loop",!0)&&i.isIndexMax()&&i.getPlayState()&&(i.setPlayState(!1),n.dispatchAction({type:"timelinePlayChange",playState:!1,from:t.from}))),e.resetOption("timeline",{replaceMerge:i.get("replaceMerge",!0)}),Ot({currentIndex:i.option.currentIndex},t)})),t.registerAction({type:"timelinePlayChange",event:"timelinePlayChanged",update:"update"},(function(t,e){var n=e.getComponent("timeline");n&&null!=t.playState&&n.setPlayState(t.playState)}))}(t),t.registerPreprocessor(bx)})),Lt((function(t){t.registerComponentModel(Cx),t.registerComponentView(Gx),t.registerPreprocessor((function(t){Mx(t.series,"markPoint")&&(t.markPoint=t.markPoint||{})}))})),Lt((function(t){t.registerComponentModel(Fx),t.registerComponentView(jx),t.registerPreprocessor((function(t){Mx(t.series,"markLine")&&(t.markLine=t.markLine||{})}))})),Lt((function(t){t.registerComponentModel(qx),t.registerComponentView(i_),t.registerPreprocessor((function(t){Mx(t.series,"markArea")&&(t.markArea=t.markArea||{})}))})),Lt(yr),Lt((function(t){Lt(g_),Lt(w_)})),Lt(g_),Lt(w_),Lt((function(t){Lt(ib),Lt(lb)})),Lt(ib),Lt(lb),Lt((function(t){t.registerPreprocessor(pb),t.registerVisual(t.PRIORITY.VISUAL.ARIA,hb)})),Lt((function(t){t.registerTransform(Ib),t.registerTransform(Ab)})),Lt((function(t){t.registerComponentModel(Tb),t.registerComponentView(Cb)})),Lt((function(t){t.registerUpdateLifecycle("series:beforeupdate",(function(t,e,n){a(nn(n.seriesTransition),(function(t){a(nn(t.to),(function(t){for(var e=n.updatedSeries,i=0;i<e.length;i++)(null!=t.seriesIndex&&t.seriesIndex===e[i].seriesIndex||null!=t.seriesId&&t.seriesId===e[i].id)&&(e[i][sr]=!0)}))}))})),t.registerUpdateLifecycle("series:transition",(function(t,e,n){var i=lS(e);if(i.oldSeries&&n.updatedSeries&&n.optionChanged){var o=n.seriesTransition;if(o)a(nn(o),(function(t){!function(t,e,n,i){var o=[],r=[];a(nn(t.from),(function(t){var n=mS(e.oldSeries,t);n>=0&&o.push({dataGroupId:e.oldDataGroupIds[n],data:e.oldData[n],divide:vS(e.oldData[n]),groupIdDim:t.dimension})})),a(nn(t.to),(function(t){var i=mS(n.updatedSeries,t);if(i>=0){var o=n.updatedSeries[i].getData();r.push({dataGroupId:e.oldDataGroupIds[i],data:o,divide:vS(o),groupIdDim:t.dimension})}})),o.length>0&&r.length>0&&gS(o,r,i)}(t,i,n,e)}));else{var r=function(t,e){var n=Ie(),i=Ie(),o=Ie();return a(t.oldSeries,(function(e,n){var r=t.oldDataGroupIds[n],s=t.oldData[n],l=fS(e),u=yS(l);i.set(u,{dataGroupId:r,data:s}),mt(l)&&a(l,(function(t){o.set(t,{key:u,dataGroupId:r,data:s})}))})),a(e.updatedSeries,(function(t){if(t.isUniversalTransitionEnabled()&&t.isAnimationEnabled()){var e=t.get("dataGroupId"),r=t.getData(),s=fS(t),l=yS(s),u=i.get(l);if(u)n.set(l,{oldSeries:[{dataGroupId:u.dataGroupId,divide:vS(u.data),data:u.data}],newSeries:[{dataGroupId:e,divide:vS(r),data:r}]});else if(mt(s)){var d=[];a(s,(function(t){var e=i.get(t);e.data&&d.push({dataGroupId:e.dataGroupId,divide:vS(e.data),data:e.data})})),d.length&&n.set(l,{oldSeries:d,newSeries:[{dataGroupId:e,data:r,divide:vS(r)}]})}else{var c=o.get(s);if(c){var h=n.get(c.key);h||(h={oldSeries:[{dataGroupId:c.dataGroupId,data:c.data,divide:vS(c.data)}],newSeries:[]},n.set(c.key,h)),h.newSeries.push({dataGroupId:e,data:r,divide:vS(r)})}}}})),n}(i,n);a(r.keys(),(function(t){var n=r.get(t);gS(n.oldSeries,n.newSeries,e)}))}a(n.updatedSeries,(function(t){t[sr]&&(t[sr]=!1)}))}for(var s=t.getSeries(),l=i.oldSeries=[],u=i.oldDataGroupIds=[],d=i.oldData=[],c=0;c<s.length;c++){var h=s[c].getData();h.count()<1e4&&(l.push(s[c]),u.push(s[c].get("dataGroupId")),d.push(h))}}))})),Lt((function(t){t.registerUpdateLifecycle("series:beforeupdate",(function(t,e,n){var i=Br(e).labelManager;i||(i=Br(e).labelManager=new zr),i.clearLabels()})),t.registerUpdateLifecycle("series:layoutlabels",(function(t,e,n){var i=Br(e).labelManager;n.updatedSeries.forEach((function(t){i.addLabelsOfSeries(e.getViewOfSeriesModel(t))})),i.updateLayoutConfig(e),i.layout(e),i.processLabelsOverall()}))}));
