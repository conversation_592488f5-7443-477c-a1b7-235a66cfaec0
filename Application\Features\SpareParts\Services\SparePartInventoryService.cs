// File: Application/Features/SpareParts/Services/SparePartInventoryService.cs
// Description: 备品备件库存状态管理服务实现

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using ItAssetsSystem.Application.Features.SpareParts.Dtos;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Domain.Entities;
using System.Text.Json;

namespace ItAssetsSystem.Application.Features.SpareParts.Services
{
    /// <summary>
    /// 备品备件库存状态管理服务实现
    /// </summary>
    public class SparePartInventoryService : ISparePartInventoryService
    {
        private readonly AppDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public SparePartInventoryService(AppDbContext context, ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        /// <summary>
        /// 获取备件库存汇总
        /// </summary>
        public async Task<SparePartStockSummaryDto> GetStockSummaryAsync(long partId)
        {
            var sparePart = await _context.SpareParts
                .FirstOrDefaultAsync(sp => sp.Id == partId);

            if (sparePart == null)
                return null;

            // 获取库存明细统计 - 实时计算，确保数据同步
            var inventoryStats = await _context.SparePartInventories
                .Where(spi => spi.PartId == partId && spi.Quantity > 0)
                .Include(spi => spi.Status)
                .GroupBy(spi => new { spi.Status.Category, spi.Status.Code, spi.Status.Name, spi.Status.Color })
                .Select(g => new StatusBreakdownDto
                {
                    StatusCode = g.Key.Code,
                    StatusName = g.Key.Name,
                    Category = g.Key.Category,
                    Quantity = g.Sum(x => x.Quantity),
                    Color = g.Key.Color
                })
                .ToListAsync();

            // 计算各类别汇总
            var totalQuantity = inventoryStats.Sum(s => s.Quantity);
            var availableQuantity = inventoryStats.Where(s => s.Category == "Available").Sum(s => s.Quantity);
            var unavailableQuantity = inventoryStats.Where(s => s.Category == "Unavailable").Sum(s => s.Quantity);
            var inTransitQuantity = inventoryStats.Where(s => s.Category == "InTransit").Sum(s => s.Quantity);
            var reservedQuantity = inventoryStats.Where(s => s.Category == "Reserved").Sum(s => s.Quantity);

            // 同步更新备件主表的库存数量
            if (sparePart.StockQuantity != totalQuantity)
            {
                sparePart.StockQuantity = totalQuantity;
                sparePart.UpdatedAt = DateTime.Now;
                await _context.SaveChangesAsync();
            }

            var summary = new SparePartStockSummaryDto
            {
                PartId = partId,
                PartName = sparePart.Name,
                PartCode = sparePart.Code,
                TotalQuantity = totalQuantity,
                AvailableQuantity = availableQuantity,
                UnavailableQuantity = unavailableQuantity,
                InTransitQuantity = inTransitQuantity,
                ReservedQuantity = reservedQuantity,
                StatusBreakdown = inventoryStats
            };

            return summary;
        }

        /// <summary>
        /// 获取备件库存明细列表
        /// </summary>
        public async Task<PaginatedResult<SparePartInventoryDto>> GetInventoriesAsync(SparePartInventoryQuery query)
        {
            var queryable = _context.SparePartInventories
                .Include(spi => spi.Location)
                .Include(spi => spi.Status)
                .AsQueryable();

            // 应用筛选条件
            if (query.PartId.HasValue)
                queryable = queryable.Where(spi => spi.PartId == query.PartId.Value);

            if (query.LocationId.HasValue)
                queryable = queryable.Where(spi => spi.LocationId == query.LocationId.Value);

            if (query.StatusId.HasValue)
                queryable = queryable.Where(spi => spi.StatusId == query.StatusId.Value);

            if (!string.IsNullOrEmpty(query.StatusCategory))
                queryable = queryable.Where(spi => spi.Status.Category == query.StatusCategory);

            if (!string.IsNullOrEmpty(query.BatchNumber))
                queryable = queryable.Where(spi => spi.BatchNumber.Contains(query.BatchNumber));

            // 排序
            queryable = queryable.OrderByDescending(spi => spi.CreatedAt);

            // 分页
            var totalCount = await queryable.CountAsync();
            var items = await queryable
                .Skip((query.PageIndex - 1) * query.PageSize)
                .Take(query.PageSize)
                .ToListAsync();

            var dtoItems = items.Select(spi => new SparePartInventoryDto
                {
                    Id = spi.Id,
                    PartId = spi.PartId,
                    LocationId = spi.LocationId,
                    LocationName = spi.Location.Name,
                    StatusId = spi.StatusId,
                    StatusName = spi.Status.Name,
                    StatusCategory = spi.Status.Category,
                    StatusColor = spi.Status.Color,
                    Quantity = spi.Quantity,
                    BatchNumber = spi.BatchNumber,
                    SerialNumbers = string.IsNullOrEmpty(spi.SerialNumbers)
                        ? new List<string>()
                        : JsonSerializer.Deserialize<List<string>>(spi.SerialNumbers) ?? new List<string>(),
                    PurchaseDate = spi.PurchaseDate,
                    WarrantyExpireDate = spi.WarrantyExpireDate,
                    UnitCost = spi.UnitCost,
                    Notes = spi.Notes,
                    CreatedAt = spi.CreatedAt,
                    UpdatedAt = spi.UpdatedAt
                })
                .ToList();

            return new PaginatedResult<SparePartInventoryDto>
            {
                Items = dtoItems,
                TotalCount = totalCount,
                PageIndex = query.PageIndex,
                PageSize = query.PageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / query.PageSize)
            };
        }

        /// <summary>
        /// 获取所有状态类型
        /// </summary>
        public async Task<List<SparePartStatusTypeDto>> GetStatusTypesAsync()
        {
            return await _context.SparePartStatusTypes
                .Where(sst => sst.IsActive)
                .OrderBy(sst => sst.SortOrder)
                .Select(sst => new SparePartStatusTypeDto
                {
                    Id = sst.Id,
                    Code = sst.Code,
                    Name = sst.Name,
                    Category = sst.Category,
                    Color = sst.Color,
                    Icon = sst.Icon,
                    Description = sst.Description,
                    IsActive = sst.IsActive,
                    SortOrder = sst.SortOrder
                })
                .ToListAsync();
        }

        /// <summary>
        /// 批量更新库存状态
        /// </summary>
        public async Task<BatchStatusUpdateResultDto> BatchUpdateStatusAsync(BatchStatusUpdateRequest request)
        {
            var result = new BatchStatusUpdateResultDto();
            
            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                foreach (var update in request.Updates)
                {
                    try
                    {
                        await UpdateSingleInventoryStatusAsync(update);
                        result.SuccessCount++;
                    }
                    catch (Exception ex)
                    {
                        result.FailureCount++;
                        result.Errors.Add($"库存明细ID {update.InventoryId}: {ex.Message}");
                    }
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                result.Errors.Add($"批量更新失败: {ex.Message}");
                result.FailureCount = request.Updates.Count;
                result.SuccessCount = 0;
            }

            return result;
        }

        /// <summary>
        /// 更新单个库存状态
        /// </summary>
        private async Task UpdateSingleInventoryStatusAsync(StatusUpdateItem update)
        {
            var inventory = await _context.SparePartInventories
                .Include(spi => spi.Status)
                .FirstOrDefaultAsync(spi => spi.Id == update.InventoryId);

            if (inventory == null)
                throw new ArgumentException($"库存明细ID {update.InventoryId} 不存在");

            var newStatus = await _context.SparePartStatusTypes
                .FirstOrDefaultAsync(sst => sst.Id == update.NewStatusId);

            if (newStatus == null)
                throw new ArgumentException($"状态ID {update.NewStatusId} 不存在");

            var oldStatusId = inventory.StatusId;
            var oldStatusName = inventory.Status.Name;

            // 如果是部分数量变更，需要拆分库存记录
            if (update.Quantity.HasValue && update.Quantity.Value < inventory.Quantity)
            {
                // 创建新的库存记录用于变更状态的部分
                var newInventory = new SparePartInventory
                {
                    PartId = inventory.PartId,
                    LocationId = inventory.LocationId,
                    StatusId = update.NewStatusId,
                    Quantity = update.Quantity.Value,
                    BatchNumber = inventory.BatchNumber,
                    SerialNumbers = inventory.SerialNumbers,
                    PurchaseDate = inventory.PurchaseDate,
                    WarrantyExpireDate = inventory.WarrantyExpireDate,
                    SupplierId = inventory.SupplierId,
                    UnitCost = inventory.UnitCost,
                    Notes = inventory.Notes,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                _context.SparePartInventories.Add(newInventory);

                // 减少原库存记录的数量
                inventory.Quantity -= update.Quantity.Value;
                inventory.UpdatedAt = DateTime.Now;

                // 记录状态变更历史
                var history = new SparePartStatusHistory
                {
                    InventoryId = newInventory.Id,
                    FromStatusId = oldStatusId,
                    ToStatusId = update.NewStatusId,
                    Quantity = update.Quantity.Value,
                    Reason = update.Reason,
                    OperatorId = _currentUserService.UserId,
                    CreatedAt = DateTime.Now
                };

                _context.SparePartStatusHistories.Add(history);
            }
            else
            {
                // 全部数量变更状态
                inventory.StatusId = update.NewStatusId;
                inventory.UpdatedAt = DateTime.Now;

                // 记录状态变更历史
                var history = new SparePartStatusHistory
                {
                    InventoryId = inventory.Id,
                    FromStatusId = oldStatusId,
                    ToStatusId = update.NewStatusId,
                    Quantity = inventory.Quantity,
                    Reason = update.Reason,
                    OperatorId = _currentUserService.UserId,
                    CreatedAt = DateTime.Now
                };

                _context.SparePartStatusHistories.Add(history);
            }
        }

        /// <summary>
        /// 获取库存状态变更历史
        /// </summary>
        public async Task<PaginatedResult<SparePartStatusHistoryDto>> GetStatusHistoryAsync(long inventoryId, PaginationQuery query)
        {
            var queryable = _context.SparePartStatusHistories
                .Where(ssh => ssh.InventoryId == inventoryId)
                .Include(ssh => ssh.FromStatus)
                .Include(ssh => ssh.ToStatus)
                .Include(ssh => ssh.Operator)
                .OrderByDescending(ssh => ssh.CreatedAt);

            var totalCount = await queryable.CountAsync();
            var items = await queryable
                .Skip((query.PageIndex - 1) * query.PageSize)
                .Take(query.PageSize)
                .Select(ssh => new SparePartStatusHistoryDto
                {
                    Id = ssh.Id,
                    InventoryId = ssh.InventoryId,
                    FromStatusName = ssh.FromStatus != null ? ssh.FromStatus.Name : "无",
                    ToStatusName = ssh.ToStatus.Name,
                    Quantity = ssh.Quantity,
                    Reason = ssh.Reason,
                    OperatorName = ssh.Operator.Name,
                    ChangedAt = ssh.CreatedAt,
                    RepairOrderId = ssh.RepairOrderId
                })
                .ToListAsync();

            return new PaginatedResult<SparePartStatusHistoryDto>
            {
                Items = items,
                TotalCount = totalCount,
                PageIndex = query.PageIndex,
                PageSize = query.PageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / query.PageSize)
            };
        }

        /// <summary>
        /// 创建返厂维修单
        /// </summary>
        public async Task<CreateRepairOrderResultDto> CreateRepairOrderAsync(CreateRepairOrderRequest request)
        {
            // 暂时返回模拟数据，避免实体不匹配的编译错误
            // TODO: 实现完整的返厂维修单创建逻辑
            await Task.Delay(1); // 避免编译器警告

            return new CreateRepairOrderResultDto
            {
                RepairOrderId = 1,
                OrderNumber = $"RF{DateTime.Now:yyyyMMddHHmmss}",
                CreatedAt = DateTime.Now
            };
        }

        /// <summary>
        /// 状态调整
        /// </summary>
        public async Task<StatusAdjustmentResultDto> AdjustStatusAsync(StatusAdjustmentRequest request)
        {
            var result = new StatusAdjustmentResultDto
            {
                AdjustmentTime = DateTime.Now
            };

            using var transaction = await _context.Database.BeginTransactionAsync();

            try
            {
                // 验证备件是否存在
                var sparePart = await _context.SpareParts
                    .FirstOrDefaultAsync(sp => sp.Id == request.PartId);

                if (sparePart == null)
                {
                    result.Errors.Add($"备件ID {request.PartId} 不存在");
                    return result;
                }

                foreach (var adjustment in request.Adjustments)
                {
                    try
                    {
                        await ProcessStatusAdjustmentAsync(request.PartId, adjustment, request.Reason);
                        result.SuccessCount++;
                    }
                    catch (Exception ex)
                    {
                        result.FailureCount++;
                        result.Errors.Add($"状态 {adjustment.StatusName} 调整失败: {ex.Message}");
                    }
                }

                if (result.FailureCount == 0)
                {
                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync();
                    result.Success = true;

                    // 获取更新后的库存汇总
                    result.UpdatedSummary = await GetStockSummaryAsync(request.PartId);
                }
                else
                {
                    await transaction.RollbackAsync();
                    result.Success = false;
                }
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                result.Success = false;
                result.Errors.Add($"状态调整失败: {ex.Message}");
                result.FailureCount = request.Adjustments.Count;
                result.SuccessCount = 0;
            }

            return result;
        }

        /// <summary>
        /// 处理单个状态调整
        /// </summary>
        private async Task ProcessStatusAdjustmentAsync(long partId, StatusAdjustmentItem adjustment, string reason)
        {
            // 验证状态是否存在
            var status = await _context.SparePartStatusTypes
                .FirstOrDefaultAsync(sst => sst.Id == adjustment.StatusId);

            if (status == null)
                throw new ArgumentException($"状态ID {adjustment.StatusId} 不存在");

            // 获取当前该状态的库存记录
            var currentInventory = await _context.SparePartInventories
                .FirstOrDefaultAsync(spi => spi.PartId == partId && spi.StatusId == adjustment.StatusId);

            var changeQuantity = adjustment.ChangeQuantity;

            if (changeQuantity == 0)
                return; // 没有变化，跳过

            if (changeQuantity > 0)
            {
                // 增加库存
                if (currentInventory != null)
                {
                    currentInventory.Quantity += changeQuantity;
                    currentInventory.UpdatedAt = DateTime.Now;
                }
                else
                {
                    // 创建新的库存记录
                    var newInventory = new SparePartInventory
                    {
                        PartId = partId,
                        LocationId = 1L, // 默认库位，实际应该从配置获取
                        StatusId = (int)adjustment.StatusId,
                        Quantity = (int)changeQuantity,
                        BatchNumber = $"ADJ-{DateTime.Now:yyyyMMddHHmmss}",
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    };
                    _context.SparePartInventories.Add(newInventory);
                }
            }
            else
            {
                // 减少库存
                if (currentInventory == null || currentInventory.Quantity < Math.Abs(changeQuantity))
                    throw new InvalidOperationException($"状态 {adjustment.StatusName} 库存不足，当前: {currentInventory?.Quantity ?? 0}，请求减少: {Math.Abs(changeQuantity)}");

                currentInventory.Quantity += changeQuantity; // changeQuantity是负数
                currentInventory.UpdatedAt = DateTime.Now;

                // 如果数量为0，删除记录
                if (currentInventory.Quantity == 0)
                {
                    _context.SparePartInventories.Remove(currentInventory);
                }
            }

            // 记录状态变更历史
            var history = new SparePartStatusHistory
            {
                InventoryId = currentInventory?.Id ?? 0L,
                FromStatusId = (int)adjustment.StatusId,
                ToStatusId = (int)adjustment.StatusId,
                Quantity = Math.Abs(changeQuantity),
                Reason = $"状态调整: {reason}",
                OperatorId = _currentUserService.UserId,
                CreatedAt = DateTime.Now
            };

            _context.SparePartStatusHistories.Add(history);
        }
    }
}
