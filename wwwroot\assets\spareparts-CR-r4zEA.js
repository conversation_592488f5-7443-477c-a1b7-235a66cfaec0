import{bg as a,a8 as t}from"./index-CkwLz8y6.js";import{b as s,s as r,d as e,e as n,u as i,c,f as o,g as l,h,i as u,j as g,k as y,l as d,m as p,n as f,o as m,p as L,q as w}from"./spareparts-Cv2l4Tzu.js";const T=a("spareparts",{state:()=>({loading:!1,formLoading:!1,types:[],typesTree:[],typesLoading:!1,locations:[],locationsLoading:!1,spareParts:[],sparePartsTotal:0,sparePartsLoading:!1,currentSparePart:null,transactions:[],transactionsTotal:0,transactionsLoading:!1,areas:[]}),getters:{typeOptions:a=>a.types.map((a=>({label:a.name,value:a.id}))),locationOptions:a=>a.locations.map((a=>({label:`${a.name} (${a.area||"未知区域"})`,value:a.id}))),locationOptionsByArea:a=>t=>a.locations.filter((a=>a.area===t)).map((a=>({label:a.name,value:a.id}))),areaOptions:a=>a.areas.map((a=>({label:a,value:a}))),lowStockCount:a=>a.spareParts.filter((a=>a.quantity<=a.min_threshold)).length,warningStockCount:a=>a.spareParts.filter((a=>a.quantity>a.min_threshold&&a.quantity<=a.warning_threshold)).length},actions:{async initializeStore(){try{await Promise.all([this.fetchTypes(),this.fetchLocations(),this.fetchAreas()])}catch(a){t.error("初始化备品备件模块失败，请刷新重试")}},async fetchTypes(){try{this.typesLoading=!0;const a=await w();a.success?this.types=a.data:t.warning(a.message||"获取备件类型失败")}catch(a){t.error("获取备件类型失败，请稍后重试")}finally{this.typesLoading=!1}},async fetchTypesTree(){try{this.typesLoading=!0;const a=await L();a.success?this.typesTree=a.data:t.warning(a.message||"获取备件类型树失败")}catch(a){t.error("获取备件类型树失败，请稍后重试")}finally{this.typesLoading=!1}},async createType(a){try{this.formLoading=!0;const s=await m(a);return s.success?(t.success("创建备件类型成功"),await this.fetchTypes(),this.typesTree.length>0&&await this.fetchTypesTree(),s.data):(t.warning(s.message||"创建备件类型失败"),null)}catch(s){return t.error("创建备件类型失败，请稍后重试"),null}finally{this.formLoading=!1}},async updateType(a,s){try{this.formLoading=!0;const r=await f(a,s);return r.success?(t.success("更新备件类型成功"),await this.fetchTypes(),this.typesTree.length>0&&await this.fetchTypesTree(),r.data):(t.warning(r.message||"更新备件类型失败"),null)}catch(r){return t.error("更新备件类型失败，请稍后重试"),null}finally{this.formLoading=!1}},async deleteType(a){try{this.formLoading=!0;const s=await p(a);return s.success?(t.success("删除备件类型成功"),await this.fetchTypes(),this.typesTree.length>0&&await this.fetchTypesTree(),!0):(t.warning(s.message||"删除备件类型失败"),!1)}catch(s){return t.error("删除备件类型失败，请稍后重试"),!1}finally{this.formLoading=!1}},async fetchLocations(){try{this.locationsLoading=!0;const a=await d();return a.success?(this.locations=a.data,this.locations):(t.warning(a.message||"获取库位失败"),[])}catch(a){return t.error("获取库位失败，请稍后重试"),[]}finally{this.locationsLoading=!1}},async fetchAreas(){try{const a=await y();return a.success?(this.areas=a.data||[],this.areas):(t.warning(a.message||"获取区域失败"),[])}catch(a){return t.error("获取区域失败，请稍后重试"),[]}},async createLocation(a){try{this.formLoading=!0;const s=await g(a);return s.success?(t.success("创建库位成功"),await this.fetchLocations(),s.data):(t.warning(s.message||"创建库位失败"),null)}catch(s){return t.error("创建库位失败，请稍后重试"),null}finally{this.formLoading=!1}},async updateLocation(a,s){try{this.formLoading=!0;const r=await u(a,s);return r.success?(t.success("更新库位成功"),await this.fetchLocations(),r.data):(t.warning(r.message||"更新库位失败"),null)}catch(r){return t.error("更新库位失败，请稍后重试"),null}finally{this.formLoading=!1}},async deleteLocation(a){try{this.formLoading=!0;const s=await h(a);return s.success?(t.success("删除库位成功"),await this.fetchLocations(),!0):(t.warning(s.message||"删除库位失败"),!1)}catch(s){return t.error("删除库位失败，请稍后重试"),!1}finally{this.formLoading=!1}},async fetchSpareParts(a={}){try{this.sparePartsLoading=!0;const s=await l(a);return s.success?(this.spareParts=s.data.items||s.data,this.sparePartsTotal=s.data.total||s.data.length,{items:this.spareParts,total:this.sparePartsTotal}):(t.warning(s.message||"获取备件列表失败"),{items:[],total:0})}catch(s){return t.error("获取备件列表失败，请稍后重试"),{items:[],total:0}}finally{this.sparePartsLoading=!1}},async fetchSparePart(a){try{this.loading=!0;const s=await o(a);return s.success?(this.currentSparePart=s.data,s.data):(t.warning(s.message||"获取备件详情失败"),null)}catch(s){return t.error("获取备件详情失败，请稍后重试"),null}finally{this.loading=!1}},async createSparePart(a){try{this.formLoading=!0;const s=await c(a);return s.success?(t.success("创建备件成功"),s.data):(t.warning(s.message||"创建备件失败"),null)}catch(s){return t.error("创建备件失败，请稍后重试"),null}finally{this.formLoading=!1}},async updateSparePart(a,s){try{this.formLoading=!0;const r=await i(a,s);return r.success?(t.success("更新备件成功"),this.currentSparePart&&this.currentSparePart.id===a&&(this.currentSparePart={...this.currentSparePart,...s}),r.data):(t.warning(r.message||"更新备件失败"),null)}catch(r){return t.error("更新备件失败，请稍后重试"),null}finally{this.formLoading=!1}},async deleteSparePart(a){try{this.formLoading=!0;const s=await n(a);return s.success?(t.success("删除备件成功"),this.currentSparePart&&this.currentSparePart.id===a&&(this.currentSparePart=null),!0):(t.warning(s.message||"删除备件失败"),!1)}catch(s){return t.error("删除备件失败，请稍后重试"),!1}finally{this.formLoading=!1}},async fetchTransactions(a={}){try{this.transactionsLoading=!0;const s=await e(a);return s.success?(this.transactions=s.data.items||s.data,this.transactionsTotal=s.data.total||s.data.length,{items:this.transactions,total:this.transactionsTotal}):(t.warning(s.message||"获取出入库记录失败"),{items:[],total:0})}catch(s){return t.error("获取出入库记录失败，请稍后重试"),{items:[],total:0}}finally{this.transactionsLoading=!1}},async createInbound(a){try{this.formLoading=!0;const s=await r(a);return s.success?(t.success("入库操作成功"),s.data):(t.warning(s.message||"入库操作失败"),null)}catch(s){return t.error("入库操作失败，请稍后重试"),null}finally{this.formLoading=!1}},async createOutbound(a){try{this.formLoading=!0;const r=await s(a);return r.success?(t.success("出库操作成功"),r.data):(t.warning(r.message||"出库操作失败"),null)}catch(r){return t.error("出库操作失败，请稍后重试"),null}finally{this.formLoading=!1}},resetStore(){this.loading=!1,this.formLoading=!1,this.types=[],this.typesTree=[],this.typesLoading=!1,this.locations=[],this.locationsLoading=!1,this.spareParts=[],this.sparePartsTotal=0,this.sparePartsLoading=!1,this.currentSparePart=null,this.transactions=[],this.transactionsTotal=0,this.transactionsLoading=!1,this.areas=[]}}});export{T as u};
