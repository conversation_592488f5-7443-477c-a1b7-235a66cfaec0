import{_ as e,r as a,m as t,b as o,d as i,e as n,w as s,f as r,aB as l,a as c,aJ as d,o as u,A as m,aq as p,Y as f,by as b,aR as v,aE as w,a8 as h,a1 as _}from"./index-CkwLz8y6.js";const C={class:"role-management-container"},T={class:"page-header"},g={class:"page-actions"},y=e({__name:"roles",setup(e){const y=a(!1),k=a([]),x=a(null);t((()=>{I()}));const I=()=>{y.value=!0,setTimeout((()=>{k.value=[{id:1,name:"系统管理员",code:"admin",description:"系统管理员，拥有系统的所有权限",userCount:3,createTime:"2023-01-01 08:00:00"},{id:2,name:"资产管理员",code:"asset_manager",description:"资产管理员，可以管理IT资产、位置和维护记录",userCount:8,createTime:"2023-01-02 09:15:00"},{id:3,name:"采购员",code:"purchaser",description:"采购员，负责IT资产的采购和入库管理",userCount:5,createTime:"2023-01-03 10:30:00"},{id:4,name:"维护人员",code:"maintenance",description:"维护人员，负责IT资产的故障维修和维护",userCount:12,createTime:"2023-01-04 11:45:00"},{id:5,name:"普通用户",code:"user",description:"普通用户，可以查看资产信息和提交故障报告",userCount:42,createTime:"2023-01-05 13:00:00"}],y.value=!1}),500)},z=()=>{h.info("打开新建角色对话框")};return(e,a)=>{const t=c("el-button"),B=c("el-table-column"),$=c("el-table"),j=c("el-card"),q=d("loading");return u(),o("div",C,[i("div",T,[a[1]||(a[1]=i("h2",{class:"page-title"},"角色管理",-1)),i("div",g,[n(t,{type:"primary",onClick:z,icon:r(l)},{default:s((()=>a[0]||(a[0]=[m(" 新建角色 ")]))),_:1},8,["icon"])])]),n(j,{class:"data-card"},{default:s((()=>[p((u(),f($,{ref_key:"roleTable",ref:x,data:k.value,border:"",style:{width:"100%"}},{default:s((()=>[n(B,{prop:"name",label:"角色名称",width:"180"}),n(B,{prop:"code",label:"角色编码",width:"180"}),n(B,{prop:"description",label:"描述","min-width":"200","show-overflow-tooltip":""}),n(B,{prop:"userCount",label:"用户数",width:"100",align:"center"}),n(B,{prop:"createTime",label:"创建时间",width:"170",sortable:""}),n(B,{label:"操作",width:"230",fixed:"right"},{default:s((e=>[n(t,{type:"text",size:"small",onClick:a=>{return t=e.row,void h.info(`为角色"${t.name}"分配权限`);var t},icon:r(b)},{default:s((()=>a[2]||(a[2]=[m(" 分配权限 ")]))),_:2},1032,["onClick","icon"]),n(t,{type:"text",size:"small",onClick:a=>{return t=e.row,void h.info(`编辑角色：${t.name}`);var t},icon:r(v)},{default:s((()=>a[3]||(a[3]=[m(" 编辑 ")]))),_:2},1032,["onClick","icon"]),n(t,{type:"text",size:"small",onClick:a=>{var t;"admin"!==(t=e.row).code?_.confirm(`确定要删除角色"${t.name}"吗？删除后将影响拥有该角色的用户权限。`,"删除角色",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}).then((()=>{h.success("角色已删除"),I()})).catch((()=>{})):h.warning("系统管理员角色不能删除")},icon:r(w),disabled:"admin"===e.row.code},{default:s((()=>a[4]||(a[4]=[m(" 删除 ")]))),_:2},1032,["onClick","icon","disabled"])])),_:1})])),_:1},8,["data"])),[[q,y.value]])])),_:1})])}}},[["__scopeId","data-v-5dc7e6e2"]]);export{y as default};
