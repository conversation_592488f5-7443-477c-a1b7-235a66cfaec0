/**
 * API版本管理器
 * 文件路径: src/api/version-manager.js
 * 功能描述: 管理API版本切换，支持A/B测试和灰度发布
 */

import request from '@/utils/request'

/**
 * API版本管理器类
 */
class ApiVersionManager {
  constructor() {
    this.currentVersions = {
      user: 'v1',
      asset: 'v1', 
      task: 'v1',
      location: 'v1',
      fault: 'v1',
      purchase: 'v1'
    }
    this.versionConfig = null
    this.userId = null
    this.initialized = false
  }

  /**
   * 初始化版本管理器
   * @param {number} userId 用户ID
   */
  async initialize(userId = null) {
    try {
      this.userId = userId
      
      // 获取版本配置
      await this.loadVersionConfig()
      
      // 如果有用户ID，获取用户特定的版本配置
      if (userId) {
        await this.loadUserVersions(userId)
      }
      
      this.initialized = true
      console.log('API版本管理器初始化完成:', this.currentVersions)
    } catch (error) {
      console.warn('API版本管理器初始化失败，使用默认版本:', error)
      this.initialized = true
    }
  }

  /**
   * 加载版本配置
   */
  async loadVersionConfig() {
    try {
      const response = await request.get('/api/apiversion/config')
      if (response.success) {
        this.versionConfig = response.data
      }
    } catch (error) {
      console.warn('加载版本配置失败:', error)
    }
  }

  /**
   * 加载用户版本配置
   * @param {number} userId 用户ID
   */
  async loadUserVersions(userId) {
    try {
      const response = await request.get(`/api/apiversion/user/${userId}`)
      if (response.success && response.data.versions) {
        this.currentVersions = { ...this.currentVersions, ...response.data.versions }
      }
    } catch (error) {
      console.warn('加载用户版本配置失败:', error)
    }
  }

  /**
   * 获取服务的API版本
   * @param {string} serviceName 服务名称
   * @returns {string} API版本
   */
  getServiceVersion(serviceName) {
    if (!this.initialized) {
      console.warn('版本管理器未初始化，使用默认版本')
      return 'v1'
    }
    
    return this.currentVersions[serviceName] || 'v1'
  }

  /**
   * 获取API基础路径
   * @param {string} serviceName 服务名称
   * @returns {string} API基础路径
   */
  getApiBasePath(serviceName) {
    const version = this.getServiceVersion(serviceName)
    return version === 'v1' ? '/api' : `/api/${version}`
  }

  /**
   * 切换服务版本
   * @param {string} serviceName 服务名称
   * @param {string} targetVersion 目标版本
   */
  async switchServiceVersion(serviceName, targetVersion) {
    try {
      const response = await request.post('/api/apiversion/switch', {
        serviceName,
        targetVersion
      })
      
      if (response.success) {
        this.currentVersions[serviceName] = targetVersion
        console.log(`服务 ${serviceName} 版本切换成功: ${targetVersion}`)
        
        // 触发版本切换事件
        this.emitVersionChangeEvent(serviceName, targetVersion)
        
        return true
      }
    } catch (error) {
      console.error('切换服务版本失败:', error)
      return false
    }
  }

  /**
   * 启用A/B测试
   * @param {Object} config A/B测试配置
   */
  async enableABTest(config) {
    try {
      const response = await request.post('/api/apiversion/abtest', {
        enabled: true,
        ...config
      })
      
      if (response.success) {
        console.log('A/B测试启用成功')
        return true
      }
    } catch (error) {
      console.error('启用A/B测试失败:', error)
      return false
    }
  }

  /**
   * 获取版本统计
   */
  async getVersionStatistics() {
    try {
      const response = await request.get('/api/apiversion/statistics')
      return response.success ? response.data : null
    } catch (error) {
      console.error('获取版本统计失败:', error)
      return null
    }
  }

  /**
   * 触发版本切换事件
   * @param {string} serviceName 服务名称
   * @param {string} version 新版本
   */
  emitVersionChangeEvent(serviceName, version) {
    const event = new CustomEvent('apiVersionChanged', {
      detail: { serviceName, version }
    })
    window.dispatchEvent(event)
  }

  /**
   * 监听版本切换事件
   * @param {Function} callback 回调函数
   */
  onVersionChange(callback) {
    window.addEventListener('apiVersionChanged', callback)
  }

  /**
   * 移除版本切换事件监听
   * @param {Function} callback 回调函数
   */
  offVersionChange(callback) {
    window.removeEventListener('apiVersionChanged', callback)
  }

  /**
   * 检查服务是否支持指定版本
   * @param {string} serviceName 服务名称
   * @param {string} version 版本号
   * @returns {boolean} 是否支持
   */
  isVersionSupported(serviceName, version) {
    const supportedVersions = {
      user: ['v1', 'v1.1'],
      asset: ['v1', 'v1.1'],
      task: ['v1', 'v1.1'],
      location: ['v1'],
      fault: ['v1'],
      purchase: ['v1']
    }
    
    return supportedVersions[serviceName]?.includes(version) || false
  }

  /**
   * 获取所有支持的版本
   * @returns {Object} 支持的版本映射
   */
  getSupportedVersions() {
    return {
      user: ['v1', 'v1.1'],
      asset: ['v1', 'v1.1'],
      task: ['v1', 'v1.1'],
      location: ['v1'],
      fault: ['v1'],
      purchase: ['v1']
    }
  }

  /**
   * 重置为默认版本
   */
  resetToDefaultVersions() {
    this.currentVersions = {
      user: 'v1',
      asset: 'v1',
      task: 'v1',
      location: 'v1',
      fault: 'v1',
      purchase: 'v1'
    }
    console.log('已重置为默认版本')
  }

  /**
   * 获取当前版本状态
   * @returns {Object} 当前版本状态
   */
  getCurrentVersionStatus() {
    return {
      versions: { ...this.currentVersions },
      initialized: this.initialized,
      userId: this.userId,
      config: this.versionConfig
    }
  }
}

// 创建全局实例
const versionManager = new ApiVersionManager()

// 导出实例和类
export { versionManager, ApiVersionManager }
export default versionManager
