/**
 * 智能制造监控系统 - 实时数据服务
 * 提供工厂数据的获取、缓存和实时更新功能
 */

import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import locationApi from '@/api/location'

class FactoryDataService {
  constructor() {
    this.locations = ref([])
    this.departments = ref([])
    this.isLoading = ref(false)
    this.lastUpdate = ref(new Date())
    this.connectionStatus = ref('connected')
    this.updateInterval = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    
    // 数据缓存
    this.dataCache = new Map()
    this.cacheExpiry = 30 * 1000 // 30秒缓存
    
    // WebSocket连接 (如果后端支持)
    this.ws = null
    this.wsUrl = this.getWebSocketUrl()
    
    this.initializeService()
  }

  getWebSocketUrl() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const host = window.location.host
    return `${protocol}//${host}/api/factory/realtime`
  }

  async initializeService() {
    try {
      await this.loadInitialData()
      this.startRealTimeUpdates()
      this.connectWebSocket()
    } catch (error) {
      console.error('初始化工厂数据服务失败:', error)
      ElMessage.error('数据服务初始化失败')
    }
  }

  async loadInitialData() {
    this.isLoading.value = true
    try {
      // 并行加载基础数据
      const [locationsData, departmentsData] = await Promise.all([
        this.loadFactoryLocations(),
        this.loadDepartments()
      ])
      
      this.locations.value = locationsData
      this.departments.value = departmentsData
      this.lastUpdate.value = new Date()
      
      console.log(`已加载 ${locationsData.length} 个工位数据`)
    } catch (error) {
      console.error('加载初始数据失败:', error)
      // 降级到模拟数据
      this.loadMockData()
    } finally {
      this.isLoading.value = false
    }
  }

  async loadFactoryLocations() {
    const cacheKey = 'factory_locations'
    
    // 检查缓存
    if (this.isCacheValid(cacheKey)) {
      return this.dataCache.get(cacheKey).data
    }

    try {
      // 尝试从真实API获取数据
      const response = await locationApi.searchLocations({
        locationType: 4, // 工位类型
        pageIndex: 1,
        pageSize: 200
      })
      
      if (response.success && response.data?.items) {
        const enrichedData = this.enrichLocationData(response.data.items)
        this.setCacheData(cacheKey, enrichedData)
        return enrichedData
      }
    } catch (error) {
      console.warn('API获取失败，使用模拟数据:', error)
    }
    
    // 降级到模拟数据
    return this.generateMockWorkstationData()
  }

  async loadDepartments() {
    try {
      // 这里可以调用真实的部门API
      return [
        { departmentId: 1, departmentName: '区域1-生产线A' },
        { departmentId: 2, departmentName: '区域2-装配线B' },
        { departmentId: 3, departmentName: '区域3-质检区' },
        { departmentId: 4, departmentName: '区域4-包装线' },
        { departmentId: 5, departmentName: '区域5-预处理' },
        { departmentId: 6, departmentName: '区域6-主生产线' },
        { departmentId: 7, departmentName: '区域7-成品区' }
      ]
    } catch (error) {
      console.error('加载部门数据失败:', error)
      return []
    }
  }

  enrichLocationData(locations) {
    return locations.map(location => ({
      ...location,
      // 添加实时状态数据
      status: this.calculateLocationStatus(location),
      efficiency: this.calculateEfficiency(location),
      uptime: this.calculateUptime(location),
      faultCount: this.getFaultCount(location),
      // 添加布局坐标
      x: this.getLocationX(location.locationId),
      y: this.getLocationY(location.locationId),
      // 标记特殊工位
      isHighlighted: this.isHighlightedLocation(location.locationId),
      // 区域信息
      zoneId: this.getZoneId(location.locationId),
      zoneColor: this.getZoneColor(location.locationId)
    }))
  }

  calculateLocationStatus(location) {
    // 基于故障数量和效率计算状态
    if (location.faultCount > 0) return 'error'
    if (location.efficiency < 60) return 'warning'
    if (location.efficiency === 0) return 'idle'
    return 'operational'
  }

  calculateEfficiency(location) {
    // 模拟效率计算 - 实际项目中应该从设备数据计算
    return Math.floor(Math.random() * 30) + 70
  }

  calculateUptime(location) {
    return Math.floor(Math.random() * 20) + 80
  }

  getFaultCount(location) {
    // 模拟故障数量
    return Math.random() < 0.1 ? Math.floor(Math.random() * 3) + 1 : 0
  }

  getLocationX(locationId) {
    // 根据工位ID计算X坐标 - 这里使用简化的布局算法
    const id = parseInt(locationId)
    if (id <= 5) return 60 + (id - 1) * 35
    if (id <= 20) return 140 + ((id - 6) % 5) * 35
    if (id <= 60) return 240 + ((id - 21) % 8) * 35
    if (id <= 80) return 560 + ((id - 61) % 4) * 35
    return 60 + ((id - 81) % 10) * 35
  }

  getLocationY(locationId) {
    const id = parseInt(locationId)
    if (id <= 5) return 60
    if (id <= 20) return 60 + Math.floor((id - 6) / 5) * 35
    if (id <= 60) return 160 + Math.floor((id - 21) / 8) * 35
    if (id <= 80) return 60 + Math.floor((id - 61) / 4) * 35
    return 300 + Math.floor((id - 81) / 10) * 35
  }

  isHighlightedLocation(locationId) {
    // 标记关键工位
    const criticalLocations = [4, 22, 61, 145]
    return criticalLocations.includes(parseInt(locationId))
  }

  getZoneId(locationId) {
    const id = parseInt(locationId)
    if (id <= 20) return 'zone1'
    if (id <= 40) return 'zone2'
    if (id <= 60) return 'zone3'
    if (id <= 80) return 'zone4'
    if (id <= 100) return 'zone5'
    if (id <= 120) return 'zone6'
    return 'zone7'
  }

  getZoneColor(locationId) {
    const zoneColors = {
      zone1: '#00bcd4',
      zone2: '#00bcd4',
      zone3: '#00bcd4',
      zone4: '#2196f3',
      zone5: '#ffc107',
      zone6: '#9e9e9e',
      zone7: '#f44336'
    }
    return zoneColors[this.getZoneId(locationId)]
  }

  generateMockWorkstationData() {
    const workstations = []
    const statuses = ['operational', 'warning', 'error', 'idle']
    const statusWeights = [0.7, 0.15, 0.1, 0.05]

    for (let i = 1; i <= 145; i++) {
      const random = Math.random()
      let status = 'operational'
      let cumulative = 0

      for (let j = 0; j < statusWeights.length; j++) {
        cumulative += statusWeights[j]
        if (random < cumulative) {
          status = statuses[j]
          break
        }
      }

      const zoneId = this.getZoneId(i)
      const zoneColor = this.getZoneColor(i)

      workstations.push({
        locationId: i,
        locationName: `工位${i.toString().padStart(3, '0')}`,
        locationCode: `WS${i.toString().padStart(3, '0')}`,
        departmentName: `区域${Math.ceil(i / 21)}-生产线`,
        status: status,
        efficiency: Math.floor(Math.random() * 30) + 70,
        uptime: Math.floor(Math.random() * 20) + 80,
        assetCount: Math.floor(Math.random() * 5) + 2,
        taskCount: Math.floor(Math.random() * 8) + 1,
        faultCount: status === 'error' ? Math.floor(Math.random() * 3) + 1 :
                    status === 'warning' ? Math.floor(Math.random() * 2) : 0,
        lastUpdate: new Date(),
        x: this.getLocationX(i),
        y: this.getLocationY(i),
        zoneColor: zoneColor,
        zoneId: zoneId,
        isHighlighted: this.isHighlightedLocation(i)
      })
    }

    return workstations
  }

  startRealTimeUpdates() {
    // 每5秒更新一次数据
    this.updateInterval = setInterval(() => {
      this.updateRealTimeData()
    }, 5000)
  }

  updateRealTimeData() {
    if (this.locations.value.length === 0) return

    let hasChanges = false

    // 随机更新工位状态和效率
    this.locations.value.forEach(location => {
      if (Math.random() < 0.05) { // 5%概率更新
        const oldEfficiency = location.efficiency
        location.efficiency = Math.max(0, Math.min(100, 
          location.efficiency + (Math.random() - 0.5) * 10
        ))
        
        if (Math.abs(location.efficiency - oldEfficiency) > 5) {
          hasChanges = true
        }

        // 随机状态变化
        if (Math.random() < 0.02) { // 2%概率状态变化
          const statuses = ['operational', 'warning', 'error', 'idle']
          const newStatus = statuses[Math.floor(Math.random() * statuses.length)]
          if (newStatus !== location.status) {
            location.status = newStatus
            location.faultCount = newStatus === 'error' ? 
              Math.floor(Math.random() * 3) + 1 : 0
            hasChanges = true
          }
        }
      }
    })

    if (hasChanges) {
      this.lastUpdate.value = new Date()
      this.broadcastDataChange()
    }
  }

  connectWebSocket() {
    if (!this.wsUrl) return

    try {
      this.ws = new WebSocket(this.wsUrl)
      
      this.ws.onopen = () => {
        console.log('WebSocket连接已建立')
        this.connectionStatus.value = 'connected'
        this.reconnectAttempts = 0
      }

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          this.handleWebSocketMessage(data)
        } catch (error) {
          console.error('WebSocket消息解析失败:', error)
        }
      }

      this.ws.onclose = () => {
        console.log('WebSocket连接已关闭')
        this.connectionStatus.value = 'disconnected'
        this.attemptReconnect()
      }

      this.ws.onerror = (error) => {
        console.error('WebSocket错误:', error)
        this.connectionStatus.value = 'error'
      }
    } catch (error) {
      console.error('WebSocket连接失败:', error)
    }
  }

  handleWebSocketMessage(data) {
    switch (data.type) {
      case 'workstation_update':
        this.updateWorkstationData(data.payload)
        break
      case 'system_alert':
        this.handleSystemAlert(data.payload)
        break
      case 'batch_update':
        this.handleBatchUpdate(data.payload)
        break
      default:
        console.log('未知WebSocket消息类型:', data.type)
    }
  }

  updateWorkstationData(payload) {
    const location = this.locations.value.find(loc => 
      loc.locationId === payload.locationId
    )
    
    if (location) {
      Object.assign(location, payload)
      this.lastUpdate.value = new Date()
      this.broadcastDataChange()
    }
  }

  handleSystemAlert(payload) {
    ElMessage({
      type: payload.severity || 'warning',
      message: payload.message,
      duration: 5000
    })
  }

  handleBatchUpdate(payload) {
    payload.updates.forEach(update => {
      this.updateWorkstationData(update)
    })
  }

  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        this.connectWebSocket()
      }, Math.pow(2, this.reconnectAttempts) * 1000) // 指数退避
    } else {
      console.log('WebSocket重连次数已达上限')
      ElMessage.error('实时连接失败，将使用定时更新模式')
    }
  }

  broadcastDataChange() {
    // 发送自定义事件通知组件数据已更新
    window.dispatchEvent(new CustomEvent('factory-data-updated', {
      detail: {
        locations: this.locations.value,
        lastUpdate: this.lastUpdate.value
      }
    }))
  }

  isCacheValid(key) {
    const cached = this.dataCache.get(key)
    if (!cached) return false
    return Date.now() - cached.timestamp < this.cacheExpiry
  }

  setCacheData(key, data) {
    this.dataCache.set(key, {
      data,
      timestamp: Date.now()
    })
  }

  // 计算属性
  getStats() {
    return computed(() => {
      const total = this.locations.value.length
      const operational = this.locations.value.filter(l => l.status === 'operational').length
      const warning = this.locations.value.filter(l => l.status === 'warning').length
      const error = this.locations.value.filter(l => l.status === 'error').length
      const idle = this.locations.value.filter(l => l.status === 'idle').length
      
      return {
        total,
        operational,
        warning,
        error,
        idle,
        operationalPercent: total > 0 ? Math.round(operational / total * 100) : 0
      }
    })
  }

  getAvgEfficiency() {
    return computed(() => {
      if (this.locations.value.length === 0) return 0
      const totalEfficiency = this.locations.value.reduce((sum, loc) => sum + loc.efficiency, 0)
      return Math.round(totalEfficiency / this.locations.value.length)
    })
  }

  getPriorityWorkstations() {
    return computed(() => {
      return this.locations.value
        .filter(loc => loc.status === 'warning' || loc.status === 'error')
        .sort((a, b) => {
          if (a.status === 'error' && b.status !== 'error') return -1
          if (b.status === 'error' && a.status !== 'error') return 1
          return b.faultCount - a.faultCount
        })
        .slice(0, 10)
    })
  }

  // 搜索功能
  searchLocations(searchTerm) {
    if (!searchTerm) return []
    
    const term = searchTerm.toLowerCase()
    return this.locations.value.filter(loc => 
      loc.locationName.toLowerCase().includes(term) ||
      loc.locationCode.toLowerCase().includes(term) ||
      loc.departmentName.toLowerCase().includes(term)
    ).slice(0, 10)
  }

  // 筛选功能
  filterLocations(filters) {
    let filtered = this.locations.value

    if (filters.statusFilters && filters.statusFilters.length < 4) {
      filtered = filtered.filter(loc => filters.statusFilters.includes(loc.status))
    }

    if (filters.departmentId) {
      filtered = filtered.filter(loc => loc.departmentId === filters.departmentId)
    }

    if (filters.searchTerm) {
      const term = filters.searchTerm.toLowerCase()
      filtered = filtered.filter(loc => 
        loc.locationName.toLowerCase().includes(term) ||
        loc.locationCode.toLowerCase().includes(term)
      )
    }

    return filtered
  }

  // 获取工位详情
  async getWorkstationDetails(locationId) {
    try {
      // 这里可以调用具体的API获取工位详情
      return {
        assets: await this.getWorkstationAssets(locationId),
        tasks: await this.getWorkstationTasks(locationId),
        faults: await this.getWorkstationFaults(locationId),
        metrics: await this.getWorkstationMetrics(locationId)
      }
    } catch (error) {
      console.error('获取工位详情失败:', error)
      return this.getMockWorkstationDetails()
    }
  }

  async getWorkstationAssets(locationId) {
    // 模拟API调用
    return [
      { id: 1, name: '数控机床-001', assetCode: 'NC001', brand: 'FANUC', status: 1, statusText: '运行中' },
      { id: 2, name: '质检仪-002', assetCode: 'QI002', brand: 'Zeiss', status: 1, statusText: '正常' }
    ]
  }

  async getWorkstationTasks(locationId) {
    return [
      { id: 1, title: '日常巡检', priority: 2, status: '进行中', assigneeName: '张三' },
      { id: 2, title: '设备维护', priority: 1, status: '待开始', assigneeName: '李四' }
    ]
  }

  async getWorkstationFaults(locationId) {
    return [
      { id: 1, description: '温度异常', severity: '警告级别', status: '处理中', createdAt: '2025-06-02T10:30:00' }
    ]
  }

  async getWorkstationMetrics(locationId) {
    return {
      temperature: Math.round(Math.random() * 20 + 20),
      pressure: Math.round(Math.random() * 10 + 95),
      vibration: Math.round(Math.random() * 5 + 2),
      power: Math.round(Math.random() * 100 + 200)
    }
  }

  getMockWorkstationDetails() {
    return {
      assets: [],
      tasks: [],
      faults: [],
      metrics: {}
    }
  }

  // 清理资源
  destroy() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval)
    }
    
    if (this.ws) {
      this.ws.close()
    }
    
    this.dataCache.clear()
  }
}

// 创建单例实例
const factoryDataService = new FactoryDataService()

export default factoryDataService