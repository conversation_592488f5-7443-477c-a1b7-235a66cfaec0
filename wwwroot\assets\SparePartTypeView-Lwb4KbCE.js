import{_ as e,r as a,c as l,m as t,b as n,e as d,w as i,a as o,ag as s,o as u,d as r,a9 as c,af as p,Y as m,f as v,A as f,t as y,as as _,am as b,an as g,ao as h,F as k,h as V,ar as w,a1 as C}from"./index-C7OOw0MO.js";import{u as x}from"./spareparts-PUry4Rrk.js";import"./spareparts-NQmkyISR.js";const T={class:"spare-part-type-view"},U={class:"card-header"},F={class:"card-content"},j={class:"tree-container"},z={class:"custom-tree-node"},L={class:"node-label"},q={class:"node-code"},B={class:"node-actions"},A={key:0,class:"type-details"},E={class:"dialog-footer"},I=e({__name:"SparePartTypeView",setup(e){const I=x(),N=a(!1),P=a(!1),R=a(null),S=a(null),Y=a({name:"",code:"",parent_id:null,description:""}),$={name:[{required:!0,message:"请输入类型名称",trigger:"blur"},{min:1,max:50,message:"长度在 1 到 50 个字符",trigger:"blur"}],code:[{required:!0,message:"请输入类型编码",trigger:"blur"},{min:1,max:20,message:"长度在 1 到 20 个字符",trigger:"blur"}]},D=l((()=>{let e=[];if(P.value&&Y.value.id){e.push(Y.value.id);const a=l=>{I.types.filter((e=>e.parent_id===l)).forEach((l=>{e.push(l.id),a(l.id)}))};a(Y.value.id)}return[{label:"无 (作为根类型)",value:null},...I.types.filter((a=>!e.includes(a.id))).map((e=>({label:e.name,value:e.id})))]})),G=e=>{if(null===e)return"无 (根类型)";const a=I.types.find((a=>a.id===e));return a?a.name:"未知"},H=e=>{R.value=e},J=e=>{P.value=!1,Y.value={name:"",code:"",parent_id:e,description:""},N.value=!0,w((()=>{S.value&&S.value.resetFields()}))},K=async()=>{if(S.value)try{if(await S.value.validate(),P.value){const{id:e,...a}=Y.value;await I.updateType(e,a)}else await I.createType(Y.value);N.value=!1}catch(e){}},M=()=>{Y.value={name:"",code:"",parent_id:null,description:""},w((()=>{S.value&&S.value.resetFields()}))};return t((async()=>{await I.fetchTypes(),await I.fetchTypesTree()})),(e,a)=>{const l=o("el-button"),t=o("el-tooltip"),x=o("el-tree"),O=o("el-empty"),Q=o("el-descriptions-item"),W=o("el-descriptions"),X=o("el-card"),Z=o("el-input"),ee=o("el-form-item"),ae=o("el-option"),le=o("el-select"),te=o("el-form"),ne=o("el-dialog"),de=s("loading");return u(),n("div",T,[d(X,{class:"type-card",shadow:"hover"},{header:i((()=>[r("div",U,[a[9]||(a[9]=r("span",null,"备件类型管理",-1)),d(l,{type:"primary",onClick:a[0]||(a[0]=e=>J(null))},{default:i((()=>a[8]||(a[8]=[f(" 新增根类型 ")]))),_:1})])])),default:i((()=>[r("div",F,[r("div",j,[p((u(),m(x,{data:v(I).typesTree,"node-key":"id","default-expand-all":"","expand-on-click-node":!1,"highlight-current":"","check-strictly":!0,onNodeClick:H},{default:i((({node:e,data:a})=>[r("span",z,[r("span",L,[f(y(a.name)+" ",1),r("span",q,"["+y(a.code)+"]",1)]),r("span",B,[d(t,{content:"新增子类型",placement:"top"},{default:i((()=>[d(l,{class:"action-btn",type:"primary",icon:v(b),circle:"",size:"small",onClick:_((e=>J(a.id)),["stop"])},null,8,["icon","onClick"])])),_:2},1024),d(t,{content:"编辑",placement:"top"},{default:i((()=>[d(l,{class:"action-btn",type:"info",icon:v(g),circle:"",size:"small",onClick:_((e=>(e=>{P.value=!0,Y.value={id:e.id,name:e.name,code:e.code,parent_id:e.parent_id,description:e.description||""},N.value=!0,w((()=>{S.value&&S.value.clearValidate()}))})(a)),["stop"])},null,8,["icon","onClick"])])),_:2},1024),d(t,{content:"删除",placement:"top"},{default:i((()=>[d(l,{class:"action-btn",type:"danger",icon:v(h),circle:"",size:"small",onClick:_((e=>(e=>{C.confirm(`确定要删除类型"${e.name}"吗？如果该类型下有子类型或关联的备件，将无法删除。`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{await I.deleteType(e.id)&&R.value&&R.value.id===e.id&&(R.value=null)})).catch((()=>{}))})(a)),["stop"])},null,8,["icon","onClick"])])),_:2},1024)])])])),_:1},8,["data"])),[[de,v(I).typesLoading]]),v(I).typesLoading||0!==v(I).typesTree.length?c("",!0):(u(),m(O,{key:0,description:"暂无类型数据"},{default:i((()=>[d(l,{type:"primary",onClick:a[1]||(a[1]=e=>J(null))},{default:i((()=>a[10]||(a[10]=[f("新增类型")]))),_:1})])),_:1}))]),R.value?(u(),n("div",A,[a[11]||(a[11]=r("h3",null,"类型详情",-1)),d(W,{column:1,border:""},{default:i((()=>[d(Q,{label:"类型名称"},{default:i((()=>[f(y(R.value.name),1)])),_:1}),d(Q,{label:"类型编码"},{default:i((()=>[f(y(R.value.code),1)])),_:1}),d(Q,{label:"上级类型"},{default:i((()=>[f(y(G(R.value.parent_id)),1)])),_:1}),d(Q,{label:"描述"},{default:i((()=>[f(y(R.value.description||"无"),1)])),_:1})])),_:1})])):c("",!0)])])),_:1}),d(ne,{modelValue:N.value,"onUpdate:modelValue":a[7]||(a[7]=e=>N.value=e),title:P.value?"编辑类型":"新增类型",width:"500px","close-on-click-modal":!1,onClosed:M},{footer:i((()=>[r("div",E,[d(l,{onClick:a[6]||(a[6]=e=>N.value=!1)},{default:i((()=>a[12]||(a[12]=[f("取消")]))),_:1}),d(l,{type:"primary",onClick:K,loading:v(I).formLoading},{default:i((()=>a[13]||(a[13]=[f(" 确认 ")]))),_:1},8,["loading"])])])),default:i((()=>[d(te,{ref_key:"typeFormRef",ref:S,model:Y.value,rules:$,"label-width":"100px","label-position":"right","status-icon":""},{default:i((()=>[d(ee,{label:"类型名称",prop:"name"},{default:i((()=>[d(Z,{modelValue:Y.value.name,"onUpdate:modelValue":a[2]||(a[2]=e=>Y.value.name=e),placeholder:"请输入类型名称"},null,8,["modelValue"])])),_:1}),d(ee,{label:"类型编码",prop:"code"},{default:i((()=>[d(Z,{modelValue:Y.value.code,"onUpdate:modelValue":a[3]||(a[3]=e=>Y.value.code=e),placeholder:"请输入类型编码"},null,8,["modelValue"])])),_:1}),d(ee,{label:"上级类型",prop:"parent_id"},{default:i((()=>[d(le,{modelValue:Y.value.parent_id,"onUpdate:modelValue":a[4]||(a[4]=e=>Y.value.parent_id=e),placeholder:"请选择上级类型",clearable:"",style:{width:"100%"}},{default:i((()=>[(u(!0),n(k,null,V(D.value,(e=>(u(),m(ae,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),d(ee,{label:"描述",prop:"description"},{default:i((()=>[d(Z,{modelValue:Y.value.description,"onUpdate:modelValue":a[5]||(a[5]=e=>Y.value.description=e),placeholder:"请输入类型描述",type:"textarea",rows:3},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])])}}},[["__scopeId","data-v-aa6a6e4b"]]);export{I as default};
