import{_ as e,r as t,m as s,b as i,d as n,e as a,w as o,f as l,am as d,bf as r,bg as p,a as c,ag as m,o as u,A as y,af as h,Y as f,a6 as w,bh as _,at as b,t as k,Z as v,a9 as g,an as x,ao as C,a8 as T,a1 as z}from"./index-C7OOw0MO.js";const A={class:"menu-management-container"},U={class:"page-header"},$={class:"page-actions"},B={key:0},M={key:1},R={key:2},S={key:1},j=e({__name:"menus",setup(e){const j=t(!1),F=t(null),I=t([]);s((()=>{L()}));const L=()=>{j.value=!0,setTimeout((()=>{I.value=[{id:1,title:"仪表盘",icon:"Odometer",path:"/main/dashboard",component:"dashboard/index",type:"menu",permission:"dashboard:view",sort:1,hidden:!1},{id:2,title:"资产管理",icon:"Monitor",path:"/main/assets",component:"assets/index",type:"directory",permission:"assets",sort:2,hidden:!1,children:[{id:21,title:"资产列表",icon:"List",path:"/main/assets/list",component:"assets/list",type:"menu",permission:"assets:list",sort:1,hidden:!1,children:[{id:211,title:"查看",icon:"",path:"",component:"",type:"button",permission:"assets:list:view",sort:1,hidden:!1},{id:212,title:"新增",icon:"",path:"",component:"",type:"button",permission:"assets:list:add",sort:2,hidden:!1},{id:213,title:"编辑",icon:"",path:"",component:"",type:"button",permission:"assets:list:edit",sort:3,hidden:!1},{id:214,title:"删除",icon:"",path:"",component:"",type:"button",permission:"assets:list:delete",sort:4,hidden:!1}]},{id:22,title:"资产类型",icon:"SetUp",path:"/main/assets/types",component:"assets/types",type:"menu",permission:"assets:types",sort:2,hidden:!1}]},{id:3,title:"系统管理",icon:"Setting",path:"/main/system",component:"system/index",type:"directory",permission:"system",sort:10,hidden:!1,children:[{id:31,title:"用户管理",icon:"User",path:"/main/system/users",component:"system/users",type:"menu",permission:"system:users",sort:1,hidden:!1},{id:32,title:"角色管理",icon:"UserFilled",path:"/main/system/roles",component:"system/roles",type:"menu",permission:"system:roles",sort:2,hidden:!1},{id:33,title:"菜单管理",icon:"Menu",path:"/main/system/menus",component:"system/menus",type:"menu",permission:"system:menus",sort:3,hidden:!1}]}],j.value=!1}),500)},O=()=>{F.value.expandAllRows()},Y=()=>{F.value.collapseAllRows()},Z=()=>{T.info("打开新建菜单对话框")};return(e,t)=>{const s=c("el-button"),q=c("el-icon"),D=c("el-table-column"),E=c("el-tag"),G=c("el-table"),H=c("el-card"),J=m("loading");return u(),i("div",A,[n("div",U,[t[3]||(t[3]=n("h2",{class:"page-title"},"菜单管理",-1)),n("div",$,[a(s,{type:"primary",onClick:Z,icon:l(d)},{default:o((()=>t[0]||(t[0]=[y(" 新建菜单 ")]))),_:1},8,["icon"]),a(s,{type:"success",onClick:O,icon:l(r)},{default:o((()=>t[1]||(t[1]=[y(" 展开所有 ")]))),_:1},8,["icon"]),a(s,{type:"info",onClick:Y,icon:l(p)},{default:o((()=>t[2]||(t[2]=[y(" 折叠所有 ")]))),_:1},8,["icon"])])]),a(H,{class:"data-card"},{default:o((()=>[h((u(),f(G,{ref_key:"menuTable",ref:F,data:I.value,"row-key":"id",border:"","default-expand-all":"","tree-props":{children:"children",hasChildren:"hasChildren"}},{default:o((()=>[a(D,{prop:"title",label:"菜单名称","min-width":"180"},{default:o((e=>["menu"===e.row.type?(u(),i("span",B,[a(q,null,{default:o((()=>[a(l(w))])),_:1})])):"directory"===e.row.type?(u(),i("span",M,[a(q,null,{default:o((()=>[a(l(_))])),_:1})])):(u(),i("span",R,[a(q,null,{default:o((()=>[a(l(b))])),_:1})])),y(" "+k(e.row.title),1)])),_:1}),a(D,{prop:"icon",label:"图标",width:"100"},{default:o((e=>[e.row.icon?(u(),f(q,{key:0},{default:o((()=>[(u(),f(v(e.row.icon)))])),_:2},1024)):(u(),i("span",S,"-"))])),_:1}),a(D,{prop:"path",label:"路由路径","min-width":"150"}),a(D,{prop:"type",label:"类型",width:"100"},{default:o((e=>["directory"===e.row.type?(u(),f(E,{key:0,type:"warning"},{default:o((()=>t[4]||(t[4]=[y("目录")]))),_:1})):"menu"===e.row.type?(u(),f(E,{key:1,type:"success"},{default:o((()=>t[5]||(t[5]=[y("菜单")]))),_:1})):"button"===e.row.type?(u(),f(E,{key:2,type:"info"},{default:o((()=>t[6]||(t[6]=[y("按钮")]))),_:1})):g("",!0)])),_:1}),a(D,{prop:"permission",label:"权限标识","min-width":"150"}),a(D,{prop:"sort",label:"排序",width:"80",align:"center"}),a(D,{prop:"hidden",label:"可见",width:"80",align:"center"},{default:o((e=>[e.row.hidden?(u(),f(E,{key:1,type:"info"},{default:o((()=>t[8]||(t[8]=[y("隐藏")]))),_:1})):(u(),f(E,{key:0,type:"success"},{default:o((()=>t[7]||(t[7]=[y("显示")]))),_:1}))])),_:1}),a(D,{label:"操作",width:"230",fixed:"right"},{default:o((e=>["button"!==e.row.type?(u(),f(s,{key:0,type:"text",size:"small",onClick:t=>{return s=e.row,void T.info(`为菜单"${s.title}"添加子菜单`);var s},icon:l(d)},{default:o((()=>t[9]||(t[9]=[y(" 添加 ")]))),_:2},1032,["onClick","icon"])):g("",!0),a(s,{type:"text",size:"small",onClick:t=>{return s=e.row,void T.info(`编辑菜单：${s.title}`);var s},icon:l(x)},{default:o((()=>t[10]||(t[10]=[y(" 编辑 ")]))),_:2},1032,["onClick","icon"]),a(s,{type:"text",size:"small",onClick:t=>{var s;(s=e.row).children&&s.children.length>0?T.warning("该菜单下有子菜单，请先删除子菜单"):z.confirm(`确定要删除菜单"${s.title}"吗？`,"删除菜单",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}).then((()=>{T.success("菜单已删除"),L()})).catch((()=>{}))},icon:l(C)},{default:o((()=>t[11]||(t[11]=[y(" 删除 ")]))),_:2},1032,["onClick","icon"])])),_:1})])),_:1},8,["data"])),[[J,j.value]])])),_:1})])}}},[["__scopeId","data-v-7a4d8442"]]);export{j as default};
