import{_ as a,j as s,b as t,d as e,t as n,f as c,e as o,w as i,a as r,u as l,o as u,A as m,a1 as d}from"./index-CkwLz8y6.js";import{s as p}from"./system-9jEcQzSp.js";const y={class:"basic-layout"},f={class:"basic-header"},h={class:"site-title"},_={class:"user-menu"},b={class:"main-content"},v=a({__name:"BasicLayout",setup(a){const v=l(),w=s(),x=()=>{d.confirm("确定要退出登录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{await w.logout(),v.push("/login")}catch(a){}})).catch((()=>{}))};return(a,s)=>{const l=r("el-button"),d=r("router-view");return u(),t("div",y,[e("header",f,[e("h1",h,n(c(p).name),1),e("div",_,[o(l,{type:"primary",size:"small",onClick:x},{default:i((()=>s[0]||(s[0]=[m("退出登录")]))),_:1})])]),e("main",b,[o(d)])])}}},[["__scopeId","data-v-341124a4"]]);export{v as default};
