/**
 * 前端性能监控服务
 * 文件路径: src/services/performance-monitor.js
 * 功能描述: 监控API调用性能，对比不同版本的性能差异
 */

/**
 * 性能监控类
 */
class PerformanceMonitor {
  constructor() {
    this.metrics = new Map() // 存储性能指标
    this.comparisons = new Map() // 存储版本对比数据
    this.isEnabled = true
    this.maxRecords = 1000 // 最大记录数
  }

  /**
   * 监控API调用性能
   * @param {string} apiName API名称
   * @param {string} version API版本
   * @param {Function} apiCall API调用函数
   * @returns {Promise} API调用结果
   */
  async monitorApiCall(apiName, version, apiCall) {
    if (!this.isEnabled) {
      return await apiCall()
    }

    const startTime = performance.now()
    const startMemory = this.getMemoryUsage()
    let success = true
    let error = null
    let result = null

    try {
      result = await apiCall()
      return result
    } catch (err) {
      success = false
      error = err.message
      throw err
    } finally {
      const endTime = performance.now()
      const endMemory = this.getMemoryUsage()
      
      const metric = {
        apiName,
        version,
        duration: endTime - startTime,
        memoryUsed: endMemory - startMemory,
        success,
        error,
        timestamp: new Date(),
        responseSize: this.getResponseSize(result),
        networkTime: this.getNetworkTime(),
        renderTime: this.getRenderTime()
      }

      this.recordMetric(metric)
    }
  }

  /**
   * 记录性能指标
   * @param {Object} metric 性能指标
   */
  recordMetric(metric) {
    const key = `${metric.apiName}_${metric.version}`
    
    if (!this.metrics.has(key)) {
      this.metrics.set(key, [])
    }

    const records = this.metrics.get(key)
    records.push(metric)

    // 限制记录数量
    if (records.length > this.maxRecords) {
      records.shift()
    }

    // 触发性能分析
    this.analyzePerformance(metric.apiName)
  }

  /**
   * 分析性能数据
   * @param {string} apiName API名称
   */
  analyzePerformance(apiName) {
    const v1Key = `${apiName}_v1`
    const v1_1Key = `${apiName}_v1.1`

    const v1Metrics = this.metrics.get(v1Key) || []
    const v1_1Metrics = this.metrics.get(v1_1Key) || []

    if (v1Metrics.length > 0 && v1_1Metrics.length > 0) {
      const comparison = this.compareVersions(v1Metrics, v1_1Metrics)
      this.comparisons.set(apiName, comparison)
      
      // 触发性能对比事件
      this.emitPerformanceComparison(apiName, comparison)
    }
  }

  /**
   * 对比两个版本的性能
   * @param {Array} v1Metrics V1版本指标
   * @param {Array} v1_1Metrics V1.1版本指标
   * @returns {Object} 对比结果
   */
  compareVersions(v1Metrics, v1_1Metrics) {
    const v1Stats = this.calculateStats(v1Metrics)
    const v1_1Stats = this.calculateStats(v1_1Metrics)

    return {
      v1: v1Stats,
      v1_1: v1_1Stats,
      comparison: {
        durationImprovement: this.calculateImprovement(v1Stats.avgDuration, v1_1Stats.avgDuration),
        memoryImprovement: this.calculateImprovement(v1Stats.avgMemory, v1_1Stats.avgMemory),
        successRateImprovement: this.calculateImprovement(v1Stats.successRate, v1_1Stats.successRate, true),
        responseSizeImprovement: this.calculateImprovement(v1Stats.avgResponseSize, v1_1Stats.avgResponseSize),
        recommendation: this.generateRecommendation(v1Stats, v1_1Stats)
      },
      lastUpdated: new Date()
    }
  }

  /**
   * 计算统计数据
   * @param {Array} metrics 性能指标数组
   * @returns {Object} 统计结果
   */
  calculateStats(metrics) {
    if (metrics.length === 0) {
      return {
        count: 0,
        avgDuration: 0,
        minDuration: 0,
        maxDuration: 0,
        avgMemory: 0,
        avgResponseSize: 0,
        successRate: 0,
        errorRate: 0
      }
    }

    const durations = metrics.map(m => m.duration)
    const memories = metrics.map(m => m.memoryUsed)
    const responseSizes = metrics.map(m => m.responseSize)
    const successCount = metrics.filter(m => m.success).length

    return {
      count: metrics.length,
      avgDuration: this.average(durations),
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      p95Duration: this.percentile(durations, 95),
      avgMemory: this.average(memories),
      avgResponseSize: this.average(responseSizes),
      successRate: (successCount / metrics.length) * 100,
      errorRate: ((metrics.length - successCount) / metrics.length) * 100,
      recentTrend: this.calculateTrend(durations.slice(-10)) // 最近10次的趋势
    }
  }

  /**
   * 计算改进百分比
   * @param {number} oldValue 旧值
   * @param {number} newValue 新值
   * @param {boolean} higherIsBetter 是否数值越高越好
   * @returns {number} 改进百分比
   */
  calculateImprovement(oldValue, newValue, higherIsBetter = false) {
    if (oldValue === 0) return 0
    
    const improvement = higherIsBetter 
      ? ((newValue - oldValue) / oldValue) * 100
      : ((oldValue - newValue) / oldValue) * 100
    
    return Math.round(improvement * 100) / 100
  }

  /**
   * 生成推荐建议
   * @param {Object} v1Stats V1统计数据
   * @param {Object} v1_1Stats V1.1统计数据
   * @returns {string} 推荐建议
   */
  generateRecommendation(v1Stats, v1_1Stats) {
    const durationImprovement = this.calculateImprovement(v1Stats.avgDuration, v1_1Stats.avgDuration)
    const memoryImprovement = this.calculateImprovement(v1Stats.avgMemory, v1_1Stats.avgMemory)
    const successImprovement = this.calculateImprovement(v1Stats.successRate, v1_1Stats.successRate, true)

    if (durationImprovement > 10 && memoryImprovement > 5 && successImprovement >= 0) {
      return 'strong_recommend_v1_1' // 强烈推荐V1.1
    } else if (durationImprovement > 5 && successImprovement >= 0) {
      return 'recommend_v1_1' // 推荐V1.1
    } else if (durationImprovement < -10 || successImprovement < -5) {
      return 'recommend_v1' // 推荐V1
    } else {
      return 'neutral' // 中性
    }
  }

  /**
   * 获取性能对比报告
   * @param {string} apiName API名称
   * @returns {Object} 对比报告
   */
  getPerformanceComparison(apiName) {
    return this.comparisons.get(apiName) || null
  }

  /**
   * 获取所有性能对比报告
   * @returns {Object} 所有对比报告
   */
  getAllPerformanceComparisons() {
    const result = {}
    for (const [apiName, comparison] of this.comparisons) {
      result[apiName] = comparison
    }
    return result
  }

  /**
   * 获取实时性能数据
   * @returns {Object} 实时性能数据
   */
  getRealTimeMetrics() {
    const result = {}
    for (const [key, metrics] of this.metrics) {
      const recent = metrics.slice(-10) // 最近10次调用
      if (recent.length > 0) {
        result[key] = {
          recentCalls: recent.length,
          avgDuration: this.average(recent.map(m => m.duration)),
          successRate: (recent.filter(m => m.success).length / recent.length) * 100,
          lastCall: recent[recent.length - 1].timestamp
        }
      }
    }
    return result
  }

  /**
   * 清除性能数据
   * @param {string} apiName 可选，指定API名称
   */
  clearMetrics(apiName = null) {
    if (apiName) {
      this.metrics.delete(`${apiName}_v1`)
      this.metrics.delete(`${apiName}_v1.1`)
      this.comparisons.delete(apiName)
    } else {
      this.metrics.clear()
      this.comparisons.clear()
    }
  }

  /**
   * 导出性能数据
   * @returns {Object} 导出的数据
   */
  exportData() {
    return {
      metrics: Object.fromEntries(this.metrics),
      comparisons: Object.fromEntries(this.comparisons),
      exportTime: new Date()
    }
  }

  /**
   * 导入性能数据
   * @param {Object} data 导入的数据
   */
  importData(data) {
    if (data.metrics) {
      this.metrics = new Map(Object.entries(data.metrics))
    }
    if (data.comparisons) {
      this.comparisons = new Map(Object.entries(data.comparisons))
    }
  }

  // 辅助方法
  average(arr) {
    return arr.length > 0 ? arr.reduce((a, b) => a + b, 0) / arr.length : 0
  }

  percentile(arr, p) {
    const sorted = [...arr].sort((a, b) => a - b)
    const index = Math.ceil((p / 100) * sorted.length) - 1
    return sorted[index] || 0
  }

  calculateTrend(arr) {
    if (arr.length < 2) return 'stable'
    const recent = arr.slice(-5)
    const older = arr.slice(-10, -5)
    const recentAvg = this.average(recent)
    const olderAvg = this.average(older)
    
    if (recentAvg < olderAvg * 0.9) return 'improving'
    if (recentAvg > olderAvg * 1.1) return 'degrading'
    return 'stable'
  }

  getMemoryUsage() {
    return performance.memory ? performance.memory.usedJSHeapSize : 0
  }

  getResponseSize(response) {
    try {
      return JSON.stringify(response).length
    } catch {
      return 0
    }
  }

  getNetworkTime() {
    // 简化实现，实际可以通过Resource Timing API获取
    return 0
  }

  getRenderTime() {
    // 简化实现，实际可以通过Performance Observer获取
    return 0
  }

  emitPerformanceComparison(apiName, comparison) {
    const event = new CustomEvent('performanceComparison', {
      detail: { apiName, comparison }
    })
    window.dispatchEvent(event)
  }

  /**
   * 启用/禁用性能监控
   * @param {boolean} enabled 是否启用
   */
  setEnabled(enabled) {
    this.isEnabled = enabled
  }
}

// 创建全局实例
const performanceMonitor = new PerformanceMonitor()

export { performanceMonitor, PerformanceMonitor }
export default performanceMonitor
