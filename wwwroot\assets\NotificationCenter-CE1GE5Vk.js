import{_ as a,a as e,b as t,o as i,e as n,F as s,h as o,i as l,d as c,w as d,Y as r,Z as u,t as f,a9 as k,A as m,as as v,y as p,B as y,aL as h,b1 as C,l as T,j as _,r as b,c as g,m as w,q as A,f as R,u as I}from"./index-C7OOw0MO.js";import{n as V}from"./notification-service-BchTNjTa.js";const N={class:"notification-list"},M={key:0,class:"empty-state"},S=["onClick"],$={class:"notification-icon"},x={class:"notification-content"},U={class:"notification-title"},j={class:"notification-message"},z={class:"notification-time"},D={class:"notification-actions"},L=a({__name:"NotificationList",props:{notifications:{type:Array,default:()=>[]}},emits:["read","click"],setup(a,{emit:T}){const _=T,b=a=>{switch(a){case"TaskAssigned":case"TaskStatusChanged":case"TaskContentChanged":return C;case"TaskComment":case"TaskAttachmentAdded":return h;case"TaskMention":return y;case"TaskOverdue":return"Timer";default:return p}},g=a=>{if(!a)return"";const e=new Date(a),t=new Date,i=Math.floor((t-e)/6e4);return i<1?"刚刚":i<60?`${i}分钟前`:i<1440?`${Math.floor(i/60)}小时前`:i<10080?`${Math.floor(i/1440)}天前`:e.toLocaleDateString("zh-CN")};return(p,y)=>{const h=e("el-empty"),C=e("el-icon"),T=e("el-button");return i(),t("div",N,[0===a.notifications.length?(i(),t("div",M,[n(h,{description:"暂无通知"})])):(i(!0),t(s,{key:1},o(a.notifications,(a=>(i(),t("div",{key:a.id||a.notificationId,class:l(["notification-item",{unread:!a.isRead&&!a.read}]),onClick:e=>(a=>{_("click",a)})(a)},[c("div",$,[n(C,null,{default:d((()=>[(i(),r(u(b(a.type))))])),_:2},1024)]),c("div",x,[c("div",U,f(a.title||"无标题"),1),c("div",j,f(a.content||a.message||"无内容"),1),c("div",z,f(g(a.timestamp||a.creationTimestamp||a.createdAt)),1)]),c("div",D,[a.isRead||a.read?k("",!0):(i(),r(T,{key:0,type:"primary",link:"",size:"small",onClick:v((e=>{return t=a.id||a.notificationId,void _("read",t);var t}),["stop"])},{default:d((()=>y[0]||(y[0]=[m(" 标为已读 ")]))),_:2},1032,["onClick"]))])],10,S)))),128))])}}},[["__scopeId","data-v-0e9a6274"]]),O={class:"notification-center"},B={class:"notification-drawer-content"},q={class:"notification-header"},F={class:"header-actions"},P={class:"notification-footer"},Y={class:"notification-trigger"},Z={class:"notification-dropdown-content"},E={class:"notification-header"},G={class:"header-actions"},H={class:"notification-footer"},J=a({__name:"NotificationCenter",props:{visible:{type:Boolean,default:!1},mode:{type:String,default:"dropdown"}},emits:["update:visible","view-task"],setup(a,{emit:s}){const o=a,l=s,u=I(),f=T(),v=_(),y=b("all"),h=g((()=>f.notifications)),C=g((()=>f.unreadCount)),N=g((()=>f.hasUnread)),M=g((()=>"drawer"===o.mode)),S=g((()=>h.value.filter((a=>["TaskAssigned","TaskStatusChanged","TaskComment","TaskAttachmentAdded","TaskMention","TaskOverdue","TaskContentChanged"].includes(a.type))))),$=g((()=>h.value.filter((a=>!["TaskAssigned","TaskStatusChanged","TaskComment","TaskAttachmentAdded","TaskMention","TaskOverdue","TaskContentChanged"].includes(a.type))))),x=async a=>{if(a)try{await f.fetchNotifications(!0)}catch(e){}},U=()=>{},j=async a=>{try{await f.markAsRead(a)}catch(e){}},z=async()=>{try{await f.markAllAsRead()}catch(a){}},D=a=>{a.read||a.isRead||j(a.id);const e="Task"===a.referenceType?a.referenceId:"Task"===a.resourceType?a.resourceId:a.taskId;e&&(M.value?l("view-task",e):u.push(`/main/tasks/detail/${e}`))},J=()=>{try{M.value&&l("update:visible",!1),u.push("/main/notifications").then((()=>{})).catch((a=>{}))}catch(a){}},K=()=>{V.sendTestNotification(v.userId)};return w((async()=>{try{await f.fetchNotifications(!0),M.value||(f.startPolling(),v.userId&&await V.initConnection(v.userId))}catch(a){}})),A((()=>{M.value||V.disconnect()})),(s,o)=>{const l=e("el-button"),u=e("el-tab-pane"),f=e("el-tabs"),v=e("el-drawer"),T=e("el-badge"),_=e("el-dropdown");return i(),t("div",O,[M.value?(i(),r(v,{key:0,"model-value":a.visible,"onUpdate:modelValue":o[1]||(o[1]=a=>s.$emit("update:visible",a)),title:"通知中心",direction:"rtl",size:"400px"},{default:d((()=>[c("div",B,[c("div",q,[c("div",F,[N.value?(i(),r(l,{key:0,link:"",type:"primary",onClick:z},{default:d((()=>o[3]||(o[3]=[m(" 全部标为已读 ")]))),_:1})):k("",!0),n(l,{link:"",type:"warning",onClick:K},{default:d((()=>o[4]||(o[4]=[m(" 测试实时通知 ")]))),_:1})])]),n(f,{modelValue:y.value,"onUpdate:modelValue":o[0]||(o[0]=a=>y.value=a),onTabClick:U},{default:d((()=>[n(u,{label:"全部",name:"all"},{default:d((()=>[n(L,{notifications:h.value,onRead:j,onClick:D},null,8,["notifications"])])),_:1}),n(u,{label:"任务",name:"task"},{default:d((()=>[n(L,{notifications:S.value,onRead:j,onClick:D},null,8,["notifications"])])),_:1}),n(u,{label:"系统",name:"system"},{default:d((()=>[n(L,{notifications:$.value,onRead:j,onClick:D},null,8,["notifications"])])),_:1})])),_:1},8,["modelValue"]),c("div",P,[n(l,{type:"primary",onClick:J,style:{cursor:"pointer !important"}},{default:d((()=>o[5]||(o[5]=[m(" 查看所有通知 ")]))),_:1})])])])),_:1},8,["model-value"])):(i(),r(_,{key:1,trigger:"click",onVisibleChange:x},{dropdown:d((()=>[c("div",Z,[c("div",E,[o[7]||(o[7]=c("h3",null,"通知中心",-1)),c("div",G,[N.value?(i(),r(l,{key:0,link:"",type:"primary",onClick:z},{default:d((()=>o[6]||(o[6]=[m(" 全部标为已读 ")]))),_:1})):k("",!0)])]),n(f,{modelValue:y.value,"onUpdate:modelValue":o[2]||(o[2]=a=>y.value=a),onTabClick:U},{default:d((()=>[n(u,{label:"全部",name:"all"},{default:d((()=>[n(L,{notifications:h.value,onRead:j,onClick:D},null,8,["notifications"])])),_:1}),n(u,{label:"任务",name:"task"},{default:d((()=>[n(L,{notifications:S.value,onRead:j,onClick:D},null,8,["notifications"])])),_:1}),n(u,{label:"系统",name:"system"},{default:d((()=>[n(L,{notifications:$.value,onRead:j,onClick:D},null,8,["notifications"])])),_:1})])),_:1},8,["modelValue"]),c("div",H,[n(l,{link:"",onClick:J,style:{cursor:"pointer !important"}},{default:d((()=>o[8]||(o[8]=[m(" 查看全部通知 ")]))),_:1})])])])),default:d((()=>[c("span",Y,[n(T,{value:C.value,hidden:0===C.value,class:"notification-badge"},{default:d((()=>[n(l,{class:"notification-icon-button",icon:R(p),circle:"",type:"primary",text:""},null,8,["icon"])])),_:1},8,["value","hidden"])])])),_:1}))])}}},[["__scopeId","data-v-6c921f99"]]);export{J as N};
