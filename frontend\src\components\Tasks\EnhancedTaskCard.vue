<template>
  <div 
    class="enhanced-task-card"
    :class="[
      `priority-${task.priority?.toLowerCase()}`,
      `status-${task.status?.toLowerCase()}`,
      { 
        'selected': selected,
        'overdue': isOverdue,
        'due-soon': isDueSoon
      }
    ]"
    @click="$emit('click', task)"
  >
    <!-- 选择框 -->
    <div v-if="selectable" class="task-select" @click.stop>
      <el-checkbox 
        :model-value="selected"
        @change="$emit('select', task.taskId, $event)"
      />
    </div>

    <!-- 任务头部 -->
    <div class="task-header">
      <div class="task-title-row">
        <div class="task-title-container">
          <h4 class="task-title" :title="task.name">{{ task.name }}</h4>
          <!-- 完成水印 - 卡片版本 -->
          <div
            v-if="task.status === 'Done' && task.completedByUserName"
            class="completion-watermark-card"
            :style="{ '--watermark-color': task.completionWatermarkColor || '#409EFF' }"
          >
            <span class="watermark-text-card">{{ task.completedByUserName }}</span>
          </div>
        </div>
        <div class="task-actions">
          <!-- 优先级指示器 -->
          <el-tag
            :type="getPriorityTagType(task.priority)"
            size="small"
            class="priority-tag"
          >
            {{ getPriorityText(task.priority) }}
          </el-tag>

          <!-- 快速操作菜单 -->
          <el-dropdown @command="handleQuickAction" trigger="click">
            <el-button type="text" size="small" class="action-btn">
              <el-icon><More /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="edit">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-dropdown-item>
                <el-dropdown-item command="assign">
                  <el-icon><User /></el-icon>
                  重新分配
                </el-dropdown-item>
                <el-dropdown-item command="clone">
                  <el-icon><DocumentCopy /></el-icon>
                  克隆任务
                </el-dropdown-item>
                <el-dropdown-item command="delete" divided>
                  <el-icon><Delete /></el-icon>
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 任务描述 -->
      <p v-if="task.description" class="task-description">
        {{ truncateText(task.description, 80) }}
      </p>
    </div>

    <!-- 负责人信息 -->
    <div class="assignee-section">
      <UserAvatarStack
        :users="getAllAssignees(task)"
        :is-main-user-primary="true"
        :max-users="4"
        avatar-size="8"
        :overlap="-12"
        class="small"
      />
    </div>

    <!-- 任务进度 -->
    <div v-if="task.progress !== undefined" class="task-progress">
      <div class="progress-header">
        <span class="progress-label">进度</span>
        <span class="progress-value">{{ task.progress }}%</span>
      </div>
      <el-progress 
        :percentage="task.progress" 
        :stroke-width="4"
        :show-text="false"
        :color="getProgressColor(task.progress)"
      />
    </div>

    <!-- 任务元数据 -->
    <div class="task-meta">
      <div class="meta-row">
        <!-- 截止日期 -->
        <div v-if="task.planEndDate" class="due-date">
          <el-icon><Calendar /></el-icon>
          <span :class="{ 'overdue-text': isOverdue, 'due-soon-text': isDueSoon }">
            {{ formatDueDate(task.planEndDate) }}
          </span>
        </div>
        
        <!-- 任务类型 -->
        <div v-if="task.taskType && task.taskType !== 'Normal'" class="task-type">
          <el-icon><Flag /></el-icon>
          <span>{{ getTaskTypeText(task.taskType) }}</span>
        </div>
      </div>

      <!-- 附件和评论数量 -->
      <div class="engagement-stats">
        <div v-if="task.attachmentCount > 0" class="stat-item">
          <el-icon><Paperclip /></el-icon>
          <span>{{ task.attachmentCount }}</span>
        </div>
        <div v-if="task.commentCount > 0" class="stat-item">
          <el-icon><ChatLineRound /></el-icon>
          <span>{{ task.commentCount }}</span>
        </div>
        <div v-if="task.points > 0" class="stat-item points">
          <el-icon><Medal /></el-icon>
          <span>{{ task.points }}</span>
        </div>
      </div>
    </div>

    <!-- 状态变更快捷按钮 -->
    <div class="quick-status-actions">
      <el-button-group size="small">
        <el-button 
          v-if="task.status !== 'Done'"
          @click.stop="quickStatusChange('Done')"
          type="success"
          size="small"
        >
          <el-icon><Check /></el-icon>
          完成
        </el-button>
        <el-button 
          v-if="task.status === 'Todo'"
          @click.stop="quickStatusChange('InProgress')"
          type="primary"
          size="small"
        >
          <el-icon><VideoPlay /></el-icon>
          开始
        </el-button>
        <el-button 
          v-if="task.status === 'InProgress'"
          @click.stop="quickStatusChange('Todo')"
          type="warning"
          size="small"
        >
          <el-icon><VideoPause /></el-icon>
          暂停
        </el-button>
      </el-button-group>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  More, Edit, User, DocumentCopy, Delete, Check, VideoPlay, VideoPause,
  Calendar, Flag, Paperclip, ChatLineRound, Medal
} from '@element-plus/icons-vue'
import UserAvatar from '@/components/UserAvatar.vue'
import UserAvatarStack from '@/components/UserAvatarStack.vue'
import { formatDate, formatRelativeTime } from '@/utils/format'

export default {
  name: 'EnhancedTaskCard',

  components: {
    More, Edit, User, DocumentCopy, Delete, Check, VideoPlay, VideoPause,
    Calendar, Flag, Paperclip, ChatLineRound, Medal,
    UserAvatar, UserAvatarStack
  },

  props: {
    task: {
      type: Object,
      required: true
    },
    selected: {
      type: Boolean,
      default: false
    },
    selectable: {
      type: Boolean,
      default: true
    },
    maxCollaboratorsVisible: {
      type: Number,
      default: 3
    }
  },

  emits: ['click', 'select', 'quickAction', 'statusChange'],

  setup(props, { emit }) {
    // 协作人员处理
    const collaborators = computed(() => {
      if (props.task.collaborators) {
        return props.task.collaborators
      }
      if (props.task.assignees) {
        return props.task.assignees.filter(a => a.role !== 'Primary')
      }
      return []
    })

    const visibleCollaborators = computed(() => {
      return collaborators.value.slice(0, props.maxCollaboratorsVisible)
    })

    const hiddenCollaboratorsCount = computed(() => {
      return Math.max(0, collaborators.value.length - props.maxCollaboratorsVisible)
    })

    // 日期相关计算
    const isOverdue = computed(() => {
      if (!props.task.planEndDate || props.task.status === 'Done') return false
      return new Date(props.task.planEndDate) < new Date()
    })

    const isDueSoon = computed(() => {
      if (!props.task.planEndDate || props.task.status === 'Done') return false
      const dueDate = new Date(props.task.planEndDate)
      const today = new Date()
      const diffDays = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24))
      return diffDays <= 2 && diffDays >= 0
    })

    // 格式化方法
    const formatDueDate = (date) => {
      return formatRelativeTime(date)
    }

    const truncateText = (text, maxLength) => {
      if (!text || text.length <= maxLength) return text
      return text.substring(0, maxLength) + '...'
    }

    const getPriorityTagType = (priority) => {
      const types = {
        'Low': 'info',
        'Medium': 'warning', 
        'High': 'danger'
      }
      return types[priority] || 'info'
    }

    const getPriorityText = (priority) => {
      const texts = {
        'Low': '低',
        'Medium': '中',
        'High': '高'
      }
      return texts[priority] || priority
    }

    const getTaskTypeText = (taskType) => {
      const texts = {
        'Periodic': '周期任务',
        'Emergency': '紧急任务',
        'Maintenance': '维护任务'
      }
      return texts[taskType] || taskType
    }

    const getProgressColor = (progress) => {
      if (progress >= 80) return '#67c23a'
      if (progress >= 50) return '#e6a23c'
      if (progress >= 20) return '#409eff'
      return '#f56c6c'
    }

    // 事件处理
    const handleQuickAction = (command) => {
      emit('quickAction', {
        action: command,
        task: props.task
      })
    }

    const quickStatusChange = (newStatus) => {
      emit('statusChange', {
        taskId: props.task.taskId,
        oldStatus: props.task.status,
        newStatus: newStatus,
        task: props.task
      })
    }

    // 获取所有负责人和协作者
    const getAllAssignees = (task) => {
      if (!task) return [];
      
      const assignees = [];
      const processedUserIds = new Set(); // 防止重复添加同一用户
      
      // 记录开始处理日志
      console.log(`处理任务[${task.taskId}]的负责人信息`);
      
      // 添加主负责人
      if (task.assigneeUserId) {
        // 尝试获取可能的头像路径
        const avatarUrl = task.assigneeAvatarUrl || task.assigneeAvatar || 
                         (task.assignee?.avatarUrl) || (task.assignee?.avatar) || ''
        console.log(`主负责人头像URL(raw): ${avatarUrl}`)
        
        assignees.push({
          id: task.assigneeUserId,
          userId: task.assigneeUserId,
          name: task.assigneeUserName || '未知用户',
          userName: task.assigneeUserName || '未知用户',
          avatarUrl: avatarUrl,
          role: 'Primary'
        });
        processedUserIds.add(task.assigneeUserId);
        console.log(`添加主负责人: ${task.assigneeUserName || '未知用户'}(ID:${task.assigneeUserId})`);
      }
      
      // 处理Assignees字段（优先使用，包含完整信息）
      if (task.assignees && Array.isArray(task.assignees) && task.assignees.length > 0) {
        console.log(`处理assignees数组数据，包含${task.assignees.length}条记录`);
        
        task.assignees.forEach(collab => {
          // 防止重复添加主负责人
          if (!collab) return;
          
          const userId = collab.userId || collab.id;
          if (!userId || processedUserIds.has(userId)) return;
          
          // 尝试获取可能的头像路径
          const avatarUrl = collab.avatarUrl || collab.avatar || 
                          (collab.user?.avatarUrl) || (collab.user?.avatar) || '';
          console.log(`协作者头像URL(raw): ${avatarUrl}`)
          
          assignees.push({
            id: userId,
            userId: userId,
            name: collab.userName || collab.name || '未知用户',
            userName: collab.userName || collab.name || '未知用户',
            avatarUrl: avatarUrl,
            role: collab.role || (userId === task.assigneeUserId ? 'Primary' : 'Collaborator')
          });
          processedUserIds.add(userId);
          
          console.log(`从assignees添加: ${collab.userName || collab.name || '未知用户'}(ID:${userId})`);
        });
      }
      // 处理collaborators字段
      else if (collaborators.value && collaborators.value.length > 0) {
        console.log(`处理collaborators数据，包含${collaborators.value.length}条记录`);
        
        collaborators.value.forEach(collab => {
          // 防止重复添加主负责人或已添加的协作者
          if (!collab) return;
          
          const userId = collab.userId || collab.id;
          if (!userId || processedUserIds.has(userId)) return;
          
          // 尝试获取可能的头像路径
          const avatarUrl = collab.avatarUrl || collab.avatar || collab.userAvatarUrl || 
                          (collab.user?.avatarUrl) || (collab.user?.avatar) || '';
          console.log(`协作者头像URL(raw): ${avatarUrl}`)
          
          assignees.push({
            id: userId,
            userId: userId,
            name: collab.userName || collab.name || '未知用户',
            userName: collab.userName || collab.name || '未知用户',
            avatarUrl: avatarUrl,
            role: 'Collaborator'
          });
          processedUserIds.add(userId);
          
          console.log(`从collaborators添加: ${collab.userName || collab.name || '未知用户'}(ID:${userId})`);
        });
      }
      // 如果没有assignees或collaborators，尝试使用participants
      else if (task.participants && Array.isArray(task.participants) && task.participants.length > 0) {
        console.log(`处理participants数据，包含${task.participants.length}条记录`);
        
        task.participants.forEach(collab => {
          // 防止重复添加主负责人
          if (!collab) return;
          
          const userId = collab.id || collab.userId;
          if (!userId || processedUserIds.has(userId) || userId === task.assigneeUserId) return;
          
          // 尝试获取可能的头像路径
          const avatarUrl = collab.avatarUrl || collab.avatar || 
                          (collab.user?.avatarUrl) || (collab.user?.avatar) || '';
          console.log(`参与者头像URL(raw): ${avatarUrl}`)
          
          assignees.push({
            id: userId,
            userId: userId,
            name: collab.name || collab.userName || '未知用户',
            userName: collab.name || collab.userName || '未知用户',
            avatarUrl: avatarUrl,
            role: 'Collaborator'
          });
          processedUserIds.add(userId);
          
          console.log(`从participants添加: ${collab.name || collab.userName || '未知用户'}(ID:${userId})`);
        });
      }
      
      // 记录结果
      if (assignees.length === 0) {
        console.warn('任务没有负责人信息:', task);
      } else {
        console.log(`任务有${assignees.length}个负责人:`, assignees.map(a => ({
          name: a.name,
          role: a.role,
          avatarUrl: a.avatarUrl
        })));
      }
      
      return assignees;
    }

    return {
      collaborators,
      visibleCollaborators,
      hiddenCollaboratorsCount,
      isOverdue,
      isDueSoon,
      formatDueDate,
      truncateText,
      getPriorityTagType,
      getPriorityText,
      getTaskTypeText,
      getProgressColor,
      handleQuickAction,
      quickStatusChange,
      getAllAssignees
    }
  }
}
</script>

<style scoped>
.enhanced-task-card {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.enhanced-task-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.enhanced-task-card.selected {
  border-color: #409eff;
  background: #f0f9ff;
}

.enhanced-task-card.overdue {
  border-left: 4px solid #f56c6c;
}

.enhanced-task-card.due-soon {
  border-left: 4px solid #e6a23c;
}

/* 优先级边框 */
.enhanced-task-card.priority-high {
  border-top: 3px solid #f56c6c;
}

.enhanced-task-card.priority-medium {
  border-top: 3px solid #e6a23c;
}

.enhanced-task-card.priority-low {
  border-top: 3px solid #909399;
}

.task-select {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 10;
}

.task-header {
  margin-bottom: 12px;
}

/* 在看板视图中，为任务头部添加左边距以避免与选择框重叠 */
.enhanced-task-card.in-kanban .task-header {
  padding-left: 36px;
}

.enhanced-task-card.in-kanban .task-select {
  position: absolute;
  top: 12px;
  left: 12px;
  z-index: 10;
}

/* 确保任务标题不与选择框重叠 */
.enhanced-task-card.in-kanban .task-title-row {
  margin-left: 0;
  padding-left: 36px;
}

.enhanced-task-card.in-kanban .task-title {
  margin-left: 0;
  padding-left: 0;
}

/* 其他内容区域也需要左边距 */
.enhanced-task-card.in-kanban .task-description,
.enhanced-task-card.in-kanban .assignee-section,
.enhanced-task-card.in-kanban .task-progress,
.enhanced-task-card.in-kanban .task-meta,
.enhanced-task-card.in-kanban .quick-status-actions {
  padding-left: 36px;
}

/* 重置任务头部的padding，因为我们已经在子元素上设置了 */
.enhanced-task-card.in-kanban .task-header {
  padding-left: 0;
}

.task-title-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.task-title-container {
  position: relative;
  flex: 1;
  margin-right: 12px;
}

.task-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  line-height: 1.4;
  word-break: break-word;
}

/* 卡片水印样式 */
.completion-watermark-card {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-12deg);
  pointer-events: none;
  z-index: 2;
  user-select: none;
}

.watermark-text-card {
  font-size: 12px;
  font-weight: bold;
  color: var(--watermark-color, #409EFF);
  opacity: 0.12;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  border: 1.5px solid var(--watermark-color, #409EFF);
  padding: 3px 6px;
  border-radius: 4px;
  background: transparent;
  white-space: nowrap;
  transition: opacity 0.3s ease;
}

.enhanced-task-card:hover .watermark-text-card {
  opacity: 0.2;
}

.task-actions {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-left: 8px;
}

.priority-tag {
  font-size: 11px;
  height: 20px;
  line-height: 18px;
}

.action-btn {
  padding: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.enhanced-task-card:hover .action-btn {
  opacity: 1;
}

.task-description {
  font-size: 12px;
  color: #606266;
  margin: 0;
  line-height: 1.5;
}

.assignee-section {
  margin-bottom: 12px;
}

.task-progress {
  margin-bottom: 12px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.progress-label {
  font-size: 11px;
  color: #909399;
}

.progress-value {
  font-size: 11px;
  font-weight: 600;
  color: #303133;
}

.task-meta {
  margin-bottom: 12px;
}

.meta-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.due-date,
.task-type {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #606266;
}

.overdue-text {
  color: #f56c6c !important;
  font-weight: 600;
}

.due-soon-text {
  color: #e6a23c !important;
  font-weight: 600;
}

.engagement-stats {
  display: flex;
  gap: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 11px;
  color: #909399;
}

.stat-item.points {
  color: #e6a23c;
  font-weight: 600;
}

.quick-status-actions {
  margin-top: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.enhanced-task-card:hover .quick-status-actions {
  opacity: 1;
}

.quick-status-actions .el-button {
  font-size: 11px;
  padding: 4px 8px;
  height: 24px;
}
</style>