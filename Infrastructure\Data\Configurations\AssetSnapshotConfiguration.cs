// File: Infrastructure/Data/Configurations/AssetSnapshotConfiguration.cs
// Description: AssetSnapshot实体配置

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ItAssetsSystem.Domain.Entities;

namespace ItAssetsSystem.Infrastructure.Data.Configurations;

/// <summary>
/// AssetSnapshot实体配置
/// </summary>
public class AssetSnapshotConfiguration : IEntityTypeConfiguration<AssetSnapshot>
{
    public void Configure(EntityTypeBuilder<AssetSnapshot> builder)
    {
        builder.ToTable("AssetSnapshots");
        builder.HasKey(e => e.Id);
        
        builder.Property(e => e.Id)
            .HasColumnName("Id")
            .ValueGeneratedOnAdd();
            
        builder.Property(e => e.SnapshotDate)
            .HasColumnName("SnapshotDate")
            .IsRequired();
            
        builder.Property(e => e.AssetId)
            .HasColumnName("AssetId")
            .IsRequired();
            
        builder.Property(e => e.AssetCode)
            .HasColumnName("AssetCode")
            .HasMaxLength(50)
            .IsRequired();
            
        builder.Property(e => e.FinancialCode)
            .HasColumnName("FinancialCode")
            .HasMaxLength(50);
            
        builder.Property(e => e.AssetName)
            .HasColumnName("AssetName")
            .HasMaxLength(200)
            .IsRequired();
            
        builder.Property(e => e.AssetTypeId)
            .HasColumnName("AssetTypeId")
            .IsRequired();
            
        builder.Property(e => e.LocationId)
            .HasColumnName("LocationId");
            
        builder.Property(e => e.DepartmentId)
            .HasColumnName("DepartmentId");
            
        builder.Property(e => e.Status)
            .HasColumnName("Status")
            .IsRequired();
            
        builder.Property(e => e.Price)
            .HasColumnName("Price")
            .HasColumnType("decimal(18,2)");
            
        builder.Property(e => e.PurchaseDate)
            .HasColumnName("PurchaseDate");

        // 索引
        builder.HasIndex(e => new { e.SnapshotDate, e.AssetId })
            .IsUnique()
            .HasDatabaseName("UK_AssetSnapshots_Date_AssetId");
            
        builder.HasIndex(e => e.SnapshotDate)
            .HasDatabaseName("IX_AssetSnapshots_SnapshotDate");
            
        builder.HasIndex(e => e.AssetId)
            .HasDatabaseName("IX_AssetSnapshots_AssetId");
    }
}
