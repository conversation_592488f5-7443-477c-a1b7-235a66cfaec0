#!/usr/bin/env python
"""
AugmentCode-Free Windows 启动脚本
这个脚本是专为 Windows 系统设计的 AugmentCode-Free 工具启动界面
"""

import sys
import subprocess
from pathlib import Path

def print_banner():
    """打印欢迎横幅"""
    print("=" * 60)
    print("🚀 AugmentCode-Free - VS Code 维护工具 (Windows 版)")
    print("=" * 60)
    print("这个工具可以帮助你：")
    print("1. 清理 VS Code 数据库中的特定条目")
    print("2. 修改 VS Code 遥测ID")
    print("3. 运行所有工具")
    print("=" * 60)

def show_menu():
    """显示主菜单"""
    print("\n📋 请选择要执行的操作：")
    print("1. 清理 VS Code 数据库 (clean-db)")
    print("2. 修改 VS Code 遥测ID (modify-ids)")
    print("3. 运行所有工具 (run-all)")
    print("4. 显示帮助信息")
    print("5. 退出")
    print("-" * 40)

def run_command(cmd_args):
    """运行命令 - Windows 版本"""
    try:
        # 在 Windows 上使用 python 而不是 python3
        cmd = ["python", "-m", "augment_tools_core.cli"] + cmd_args
        print(f"\n🔄 执行命令: {' '.join(cmd)}")
        print("-" * 40)
        result = subprocess.run(cmd, check=True)
        print("-" * 40)
        print("✅ 命令执行完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 命令执行失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⚠️  操作被用户取消")
        return False

def main():
    """主函数"""
    print_banner()
    
    while True:
        show_menu()
        try:
            choice = input("请输入选项 (1-5): ").strip()
            
            if choice == "1":
                keyword = input("请输入要清理的关键字 (默认: augment): ").strip()
                if not keyword:
                    keyword = "augment"
                run_command(["clean-db", "--keyword", keyword])
                
            elif choice == "2":
                print("\n⚠️  注意：这将修改 VS Code 的遥测ID")
                confirm = input("确定要继续吗？(y/N): ").strip().lower()
                if confirm in ['y', 'yes']:
                    run_command(["modify-ids"])
                else:
                    print("操作已取消")
                    
            elif choice == "3":
                keyword = input("请输入要清理的关键字 (默认: augment): ").strip()
                if not keyword:
                    keyword = "augment"
                print("\n⚠️  注意：这将执行所有工具（清理数据库 + 修改遥测ID）")
                confirm = input("确定要继续吗？(y/N): ").strip().lower()
                if confirm in ['y', 'yes']:
                    run_command(["run-all", "--keyword", keyword])
                else:
                    print("操作已取消")
                    
            elif choice == "4":
                run_command(["--help"])
                
            elif choice == "5":
                print("\n👋 感谢使用 AugmentCode-Free！")
                break
                
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n\n👋 感谢使用 AugmentCode-Free！")
            break
        except EOFError:
            print("\n\n👋 感谢使用 AugmentCode-Free！")
            break

if __name__ == "__main__":
    # 检查是否在 Windows 上运行
    import platform
    if platform.system() != "Windows":
        print("⚠️  警告: 此脚本专为 Windows 设计，在其他系统上可能会有问题")
        print("     请考虑使用 main.py 替代")
        input("按 Enter 键继续...")
    
    main()