import{b3 as e,c as t,b4 as n,f as a,r as o,a5 as r,b5 as l,b6 as i,p as s,m as u,as as c,q as d,b7 as p,_ as f,b as h,a9 as g,o as m,d as v,e as b,t as y,w,A as k,a as _,ac as D,B as S,aa as E,Y as C,b8 as T,n as I,b9 as x,ba as O,ab as A,af as N,aB as M,bb as P,F as U,h as V,bc as F,a8 as j,i as B,y as R,au as Y,G as X,C as L,aR as z,aE as H,Z as W,a1 as $}from"./index-CkwLz8y6.js";import{u as q,T as G,a as K,B as Q,b as Z,E as J}from"./BatchStatusDialog-Bn-Q6xtq.js";import{N as ee}from"./NotificationCenter-BgpTKwJk.js";import{t as te}from"./task-Uzj9rZkj.js";import{f as ne}from"./format-DfhXadVZ.js";import"./UserAvatarStack-DuXt8B2E.js";import"./UserSelect-BSDGnNm1.js";import"./notification-service-BTmwzUoW.js";var ae=Object.defineProperty,oe=Object.getOwnPropertySymbols,re=Object.prototype.hasOwnProperty,le=Object.prototype.propertyIsEnumerable,ie=(e,t,n)=>t in e?ae(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,se=(e,t)=>{for(var n in t||(t={}))re.call(t,n)&&ie(e,n,t[n]);if(oe)for(var n of oe(t))le.call(t,n)&&ie(e,n,t[n]);return e},ue=(e,t)=>{var n={};for(var a in e)re.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&oe)for(var a of oe(e))t.indexOf(a)<0&&le.call(e,a)&&(n[a]=e[a]);return n};function ce(e,t,n){return n>=0&&n<e.length&&e.splice(n,0,e.splice(t,1)[0]),e}function de(e,t){return Array.isArray(e)&&e.splice(t,1),e}function pe(e,t,n){return Array.isArray(e)&&e.splice(t,0,n),e}function fe(e,t,n){const a=e.children[n];e.insertBefore(t,a)}function he(e){e.parentNode&&e.parentNode.removeChild(e)}function ge(e,t){Object.keys(e).forEach((n=>{t(n,e[n])}))}const me=Object.assign;
/**!
 * Sortable 1.15.2
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function ve(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function be(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ve(Object(n),!0).forEach((function(t){we(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ve(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ye(e){return(ye="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function we(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ke(){return ke=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},ke.apply(this,arguments)}function _e(e,t){if(null==e)return{};var n,a,o=function(e,t){if(null==e)return{};var n,a,o={},r=Object.keys(e);for(a=0;a<r.length;a++)n=r[a],!(t.indexOf(n)>=0)&&(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(a=0;a<r.length;a++)n=r[a],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function De(e){if("undefined"!=typeof window&&window.navigator)return!!navigator.userAgent.match(e)}var Se=De(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),Ee=De(/Edge/i),Ce=De(/firefox/i),Te=De(/safari/i)&&!De(/chrome/i)&&!De(/android/i),Ie=De(/iP(ad|od|hone)/i),xe=De(/chrome/i)&&De(/android/i),Oe={capture:!1,passive:!1};function Ae(e,t,n){e.addEventListener(t,n,!Se&&Oe)}function Ne(e,t,n){e.removeEventListener(t,n,!Se&&Oe)}function Me(e,t){if(t){if(">"===t[0]&&(t=t.substring(1)),e)try{if(e.matches)return e.matches(t);if(e.msMatchesSelector)return e.msMatchesSelector(t);if(e.webkitMatchesSelector)return e.webkitMatchesSelector(t)}catch(n){return!1}return!1}}function Pe(e){return e.host&&e!==document&&e.host.nodeType?e.host:e.parentNode}function Ue(e,t,n,a){if(e){n=n||document;do{if(null!=t&&(">"===t[0]?e.parentNode===n&&Me(e,t):Me(e,t))||a&&e===n)return e;if(e===n)break}while(e=Pe(e))}return null}var Ve,Fe=/\s+/g;function je(e,t,n){if(e&&t)if(e.classList)e.classList[n?"add":"remove"](t);else{var a=(" "+e.className+" ").replace(Fe," ").replace(" "+t+" "," ");e.className=(a+(n?" "+t:"")).replace(Fe," ")}}function Be(e,t,n){var a=e&&e.style;if(a){if(void 0===n)return document.defaultView&&document.defaultView.getComputedStyle?n=document.defaultView.getComputedStyle(e,""):e.currentStyle&&(n=e.currentStyle),void 0===t?n:n[t];!(t in a)&&-1===t.indexOf("webkit")&&(t="-webkit-"+t),a[t]=n+("string"==typeof n?"":"px")}}function Re(e,t){var n="";if("string"==typeof e)n=e;else do{var a=Be(e,"transform");a&&"none"!==a&&(n=a+" "+n)}while(!t&&(e=e.parentNode));var o=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return o&&new o(n)}function Ye(e,t,n){if(e){var a=e.getElementsByTagName(t),o=0,r=a.length;if(n)for(;o<r;o++)n(a[o],o);return a}return[]}function Xe(){return document.scrollingElement||document.documentElement}function Le(e,t,n,a,o){if(e.getBoundingClientRect||e===window){var r,l,i,s,u,c,d;if(e!==window&&e.parentNode&&e!==Xe()?(l=(r=e.getBoundingClientRect()).top,i=r.left,s=r.bottom,u=r.right,c=r.height,d=r.width):(l=0,i=0,s=window.innerHeight,u=window.innerWidth,c=window.innerHeight,d=window.innerWidth),(t||n)&&e!==window&&(o=o||e.parentNode,!Se))do{if(o&&o.getBoundingClientRect&&("none"!==Be(o,"transform")||n&&"static"!==Be(o,"position"))){var p=o.getBoundingClientRect();l-=p.top+parseInt(Be(o,"border-top-width")),i-=p.left+parseInt(Be(o,"border-left-width")),s=l+r.height,u=i+r.width;break}}while(o=o.parentNode);if(a&&e!==window){var f=Re(o||e),h=f&&f.a,g=f&&f.d;f&&(s=(l/=g)+(c/=g),u=(i/=h)+(d/=h))}return{top:l,left:i,bottom:s,right:u,width:d,height:c}}}function ze(e,t,n){for(var a=Ge(e,!0),o=Le(e)[t];a;){if(!(o>=Le(a)[n]))return a;if(a===Xe())break;a=Ge(a,!1)}return!1}function He(e,t,n,a){for(var o=0,r=0,l=e.children;r<l.length;){if("none"!==l[r].style.display&&l[r]!==Jt.ghost&&(a||l[r]!==Jt.dragged)&&Ue(l[r],n.draggable,e,!1)){if(o===t)return l[r];o++}r++}return null}function We(e,t){for(var n=e.lastElementChild;n&&(n===Jt.ghost||"none"===Be(n,"display")||t&&!Me(n,t));)n=n.previousElementSibling;return n||null}function $e(e,t){var n=0;if(!e||!e.parentNode)return-1;for(;e=e.previousElementSibling;)"TEMPLATE"!==e.nodeName.toUpperCase()&&e!==Jt.clone&&(!t||Me(e,t))&&n++;return n}function qe(e){var t=0,n=0,a=Xe();if(e)do{var o=Re(e),r=o.a,l=o.d;t+=e.scrollLeft*r,n+=e.scrollTop*l}while(e!==a&&(e=e.parentNode));return[t,n]}function Ge(e,t){if(!e||!e.getBoundingClientRect)return Xe();var n=e,a=!1;do{if(n.clientWidth<n.scrollWidth||n.clientHeight<n.scrollHeight){var o=Be(n);if(n.clientWidth<n.scrollWidth&&("auto"==o.overflowX||"scroll"==o.overflowX)||n.clientHeight<n.scrollHeight&&("auto"==o.overflowY||"scroll"==o.overflowY)){if(!n.getBoundingClientRect||n===document.body)return Xe();if(a||t)return n;a=!0}}}while(n=n.parentNode);return Xe()}function Ke(e,t){return Math.round(e.top)===Math.round(t.top)&&Math.round(e.left)===Math.round(t.left)&&Math.round(e.height)===Math.round(t.height)&&Math.round(e.width)===Math.round(t.width)}function Qe(e,t){return function(){if(!Ve){var n=arguments;1===n.length?e.call(this,n[0]):e.apply(this,n),Ve=setTimeout((function(){Ve=void 0}),t)}}}function Ze(e,t,n){e.scrollLeft+=t,e.scrollTop+=n}function Je(e){var t=window.Polymer,n=window.jQuery||window.Zepto;return t&&t.dom?t.dom(e).cloneNode(!0):n?n(e).clone(!0)[0]:e.cloneNode(!0)}function et(e,t,n){var a={};return Array.from(e.children).forEach((function(o){var r,l,i,s;if(Ue(o,t.draggable,e,!1)&&!o.animated&&o!==n){var u=Le(o);a.left=Math.min(null!==(r=a.left)&&void 0!==r?r:1/0,u.left),a.top=Math.min(null!==(l=a.top)&&void 0!==l?l:1/0,u.top),a.right=Math.max(null!==(i=a.right)&&void 0!==i?i:-1/0,u.right),a.bottom=Math.max(null!==(s=a.bottom)&&void 0!==s?s:-1/0,u.bottom)}})),a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}var tt="Sortable"+(new Date).getTime();function nt(){var e,t=[];return{captureAnimationState:function(){(t=[],this.options.animation)&&[].slice.call(this.el.children).forEach((function(e){if("none"!==Be(e,"display")&&e!==Jt.ghost){t.push({target:e,rect:Le(e)});var n=be({},t[t.length-1].rect);if(e.thisAnimationDuration){var a=Re(e,!0);a&&(n.top-=a.f,n.left-=a.e)}e.fromRect=n}}))},addAnimationState:function(e){t.push(e)},removeAnimationState:function(e){t.splice(function(e,t){for(var n in e)if(e.hasOwnProperty(n))for(var a in t)if(t.hasOwnProperty(a)&&t[a]===e[n][a])return Number(n);return-1}(t,{target:e}),1)},animateAll:function(n){var a=this;if(!this.options.animation)return clearTimeout(e),void("function"==typeof n&&n());var o=!1,r=0;t.forEach((function(e){var t=0,n=e.target,l=n.fromRect,i=Le(n),s=n.prevFromRect,u=n.prevToRect,c=e.rect,d=Re(n,!0);d&&(i.top-=d.f,i.left-=d.e),n.toRect=i,n.thisAnimationDuration&&Ke(s,i)&&!Ke(l,i)&&(c.top-i.top)/(c.left-i.left)==(l.top-i.top)/(l.left-i.left)&&(t=function(e,t,n,a){return Math.sqrt(Math.pow(t.top-e.top,2)+Math.pow(t.left-e.left,2))/Math.sqrt(Math.pow(t.top-n.top,2)+Math.pow(t.left-n.left,2))*a.animation}(c,s,u,a.options)),Ke(i,l)||(n.prevFromRect=l,n.prevToRect=i,t||(t=a.options.animation),a.animate(n,c,i,t)),t&&(o=!0,r=Math.max(r,t),clearTimeout(n.animationResetTimer),n.animationResetTimer=setTimeout((function(){n.animationTime=0,n.prevFromRect=null,n.fromRect=null,n.prevToRect=null,n.thisAnimationDuration=null}),t),n.thisAnimationDuration=t)})),clearTimeout(e),o?e=setTimeout((function(){"function"==typeof n&&n()}),r):"function"==typeof n&&n(),t=[]},animate:function(e,t,n,a){if(a){Be(e,"transition",""),Be(e,"transform","");var o=Re(this.el),r=o&&o.a,l=o&&o.d,i=(t.left-n.left)/(r||1),s=(t.top-n.top)/(l||1);e.animatingX=!!i,e.animatingY=!!s,Be(e,"transform","translate3d("+i+"px,"+s+"px,0)"),this.forRepaintDummy=function(e){return e.offsetWidth}(e),Be(e,"transition","transform "+a+"ms"+(this.options.easing?" "+this.options.easing:"")),Be(e,"transform","translate3d(0,0,0)"),"number"==typeof e.animated&&clearTimeout(e.animated),e.animated=setTimeout((function(){Be(e,"transition",""),Be(e,"transform",""),e.animated=!1,e.animatingX=!1,e.animatingY=!1}),a)}}}}var at=[],ot={initializeByDefault:!0},rt={mount:function(e){for(var t in ot)ot.hasOwnProperty(t)&&!(t in e)&&(e[t]=ot[t]);at.forEach((function(t){if(t.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")})),at.push(e)},pluginEvent:function(e,t,n){var a=this;this.eventCanceled=!1,n.cancel=function(){a.eventCanceled=!0};var o=e+"Global";at.forEach((function(a){t[a.pluginName]&&(t[a.pluginName][o]&&t[a.pluginName][o](be({sortable:t},n)),t.options[a.pluginName]&&t[a.pluginName][e]&&t[a.pluginName][e](be({sortable:t},n)))}))},initializePlugins:function(e,t,n,a){for(var o in at.forEach((function(a){var o=a.pluginName;if(e.options[o]||a.initializeByDefault){var r=new a(e,t,e.options);r.sortable=e,r.options=e.options,e[o]=r,ke(n,r.defaults)}})),e.options)if(e.options.hasOwnProperty(o)){var r=this.modifyOption(e,o,e.options[o]);void 0!==r&&(e.options[o]=r)}},getEventProperties:function(e,t){var n={};return at.forEach((function(a){"function"==typeof a.eventProperties&&ke(n,a.eventProperties.call(t[a.pluginName],e))})),n},modifyOption:function(e,t,n){var a;return at.forEach((function(o){e[o.pluginName]&&o.optionListeners&&"function"==typeof o.optionListeners[t]&&(a=o.optionListeners[t].call(e[o.pluginName],n))})),a}};var lt=["evt"],it=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=n.evt,o=_e(n,lt);rt.pluginEvent.bind(Jt)(e,t,be({dragEl:ut,parentEl:ct,ghostEl:dt,rootEl:pt,nextEl:ft,lastDownEl:ht,cloneEl:gt,cloneHidden:mt,dragStarted:xt,putSortable:_t,activeSortable:Jt.active,originalEvent:a,oldIndex:vt,oldDraggableIndex:yt,newIndex:bt,newDraggableIndex:wt,hideGhostForTarget:Gt,unhideGhostForTarget:Kt,cloneNowHidden:function(){mt=!0},cloneNowShown:function(){mt=!1},dispatchSortableEvent:function(e){st({sortable:t,name:e,originalEvent:a})}},o))};function st(e){!function(e){var t=e.sortable,n=e.rootEl,a=e.name,o=e.targetEl,r=e.cloneEl,l=e.toEl,i=e.fromEl,s=e.oldIndex,u=e.newIndex,c=e.oldDraggableIndex,d=e.newDraggableIndex,p=e.originalEvent,f=e.putSortable,h=e.extraEventProperties;if(t=t||n&&n[tt]){var g,m=t.options,v="on"+a.charAt(0).toUpperCase()+a.substr(1);!window.CustomEvent||Se||Ee?(g=document.createEvent("Event")).initEvent(a,!0,!0):g=new CustomEvent(a,{bubbles:!0,cancelable:!0}),g.to=l||n,g.from=i||n,g.item=o||n,g.clone=r,g.oldIndex=s,g.newIndex=u,g.oldDraggableIndex=c,g.newDraggableIndex=d,g.originalEvent=p,g.pullMode=f?f.lastPutMode:void 0;var b=be(be({},h),rt.getEventProperties(a,t));for(var y in b)g[y]=b[y];n&&n.dispatchEvent(g),m[v]&&m[v].call(t,g)}}(be({putSortable:_t,cloneEl:gt,targetEl:ut,rootEl:pt,oldIndex:vt,oldDraggableIndex:yt,newIndex:bt,newDraggableIndex:wt},e))}var ut,ct,dt,pt,ft,ht,gt,mt,vt,bt,yt,wt,kt,_t,Dt,St,Et,Ct,Tt,It,xt,Ot,At,Nt,Mt,Pt=!1,Ut=!1,Vt=[],Ft=!1,jt=!1,Bt=[],Rt=!1,Yt=[],Xt="undefined"!=typeof document,Lt=Ie,zt=Ee||Se?"cssFloat":"float",Ht=Xt&&!xe&&!Ie&&"draggable"in document.createElement("div"),Wt=function(){if(Xt){if(Se)return!1;var e=document.createElement("x");return e.style.cssText="pointer-events:auto","auto"===e.style.pointerEvents}}(),$t=function(e,t){var n=Be(e),a=parseInt(n.width)-parseInt(n.paddingLeft)-parseInt(n.paddingRight)-parseInt(n.borderLeftWidth)-parseInt(n.borderRightWidth),o=He(e,0,t),r=He(e,1,t),l=o&&Be(o),i=r&&Be(r),s=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+Le(o).width,u=i&&parseInt(i.marginLeft)+parseInt(i.marginRight)+Le(r).width;if("flex"===n.display)return"column"===n.flexDirection||"column-reverse"===n.flexDirection?"vertical":"horizontal";if("grid"===n.display)return n.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(o&&l.float&&"none"!==l.float){var c="left"===l.float?"left":"right";return!r||"both"!==i.clear&&i.clear!==c?"horizontal":"vertical"}return o&&("block"===l.display||"flex"===l.display||"table"===l.display||"grid"===l.display||s>=a&&"none"===n[zt]||r&&"none"===n[zt]&&s+u>a)?"vertical":"horizontal"},qt=function(e){function t(e,n){return function(a,o,r,l){var i=a.options.group.name&&o.options.group.name&&a.options.group.name===o.options.group.name;if(null==e&&(n||i))return!0;if(null==e||!1===e)return!1;if(n&&"clone"===e)return e;if("function"==typeof e)return t(e(a,o,r,l),n)(a,o,r,l);var s=(n?a:o).options.group.name;return!0===e||"string"==typeof e&&e===s||e.join&&e.indexOf(s)>-1}}var n={},a=e.group;(!a||"object"!=ye(a))&&(a={name:a}),n.name=a.name,n.checkPull=t(a.pull,!0),n.checkPut=t(a.put),n.revertClone=a.revertClone,e.group=n},Gt=function(){!Wt&&dt&&Be(dt,"display","none")},Kt=function(){!Wt&&dt&&Be(dt,"display","")};Xt&&!xe&&document.addEventListener("click",(function(e){if(Ut)return e.preventDefault(),e.stopPropagation&&e.stopPropagation(),e.stopImmediatePropagation&&e.stopImmediatePropagation(),Ut=!1,!1}),!0);var Qt=function(e){if(ut){var t=function(e,t){var n;return Vt.some((function(a){var o=a[tt].options.emptyInsertThreshold;if(o&&!We(a)){var r=Le(a),l=e>=r.left-o&&e<=r.right+o,i=t>=r.top-o&&t<=r.bottom+o;if(l&&i)return n=a}})),n}((e=e.touches?e.touches[0]:e).clientX,e.clientY);if(t){var n={};for(var a in e)e.hasOwnProperty(a)&&(n[a]=e[a]);n.target=n.rootEl=t,n.preventDefault=void 0,n.stopPropagation=void 0,t[tt]._onDragOver(n)}}},Zt=function(e){ut&&ut.parentNode[tt]._isOutsideThisEl(e.target)};function Jt(e,t){if(!e||!e.nodeType||1!==e.nodeType)throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(e));this.el=e,this.options=t=ke({},t),e[tt]=this;var n={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(e.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return $t(e,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(e,t){e.setData("Text",t.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:!1!==Jt.supportPointer&&"PointerEvent"in window&&!Te,emptyInsertThreshold:5};for(var a in rt.initializePlugins(this,e,n),n)!(a in t)&&(t[a]=n[a]);for(var o in qt(t),this)"_"===o.charAt(0)&&"function"==typeof this[o]&&(this[o]=this[o].bind(this));this.nativeDraggable=!t.forceFallback&&Ht,this.nativeDraggable&&(this.options.touchStartThreshold=1),t.supportPointer?Ae(e,"pointerdown",this._onTapStart):(Ae(e,"mousedown",this._onTapStart),Ae(e,"touchstart",this._onTapStart)),this.nativeDraggable&&(Ae(e,"dragover",this),Ae(e,"dragenter",this)),Vt.push(this.el),t.store&&t.store.get&&this.sort(t.store.get(this)||[]),ke(this,nt())}function en(e,t,n,a,o,r,l,i){var s,u,c=e[tt],d=c.options.onMove;return!window.CustomEvent||Se||Ee?(s=document.createEvent("Event")).initEvent("move",!0,!0):s=new CustomEvent("move",{bubbles:!0,cancelable:!0}),s.to=t,s.from=e,s.dragged=n,s.draggedRect=a,s.related=o||t,s.relatedRect=r||Le(t),s.willInsertAfter=i,s.originalEvent=l,e.dispatchEvent(s),d&&(u=d.call(c,s,l)),u}function tn(e){e.draggable=!1}function nn(){Rt=!1}function an(e){for(var t=e.tagName+e.className+e.src+e.href+e.textContent,n=t.length,a=0;n--;)a+=t.charCodeAt(n);return a.toString(36)}function on(e){return setTimeout(e,0)}function rn(e){return clearTimeout(e)}Jt.prototype={constructor:Jt,_isOutsideThisEl:function(e){!this.el.contains(e)&&e!==this.el&&(Ot=null)},_getDirection:function(e,t){return"function"==typeof this.options.direction?this.options.direction.call(this,e,t,ut):this.options.direction},_onTapStart:function(e){if(e.cancelable){var t=this,n=this.el,a=this.options,o=a.preventOnFilter,r=e.type,l=e.touches&&e.touches[0]||e.pointerType&&"touch"===e.pointerType&&e,i=(l||e).target,s=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||i,u=a.filter;if(function(e){Yt.length=0;for(var t=e.getElementsByTagName("input"),n=t.length;n--;){var a=t[n];a.checked&&Yt.push(a)}}(n),!ut&&!(/mousedown|pointerdown/.test(r)&&0!==e.button||a.disabled)&&!s.isContentEditable&&(this.nativeDraggable||!Te||!i||"SELECT"!==i.tagName.toUpperCase())&&!((i=Ue(i,a.draggable,n,!1))&&i.animated||ht===i)){if(vt=$e(i),yt=$e(i,a.draggable),"function"==typeof u){if(u.call(this,e,i,this))return st({sortable:t,rootEl:s,name:"filter",targetEl:i,toEl:n,fromEl:n}),it("filter",t,{evt:e}),void(o&&e.cancelable&&e.preventDefault())}else if(u&&(u=u.split(",").some((function(a){if(a=Ue(s,a.trim(),n,!1))return st({sortable:t,rootEl:a,name:"filter",targetEl:i,fromEl:n,toEl:n}),it("filter",t,{evt:e}),!0}))))return void(o&&e.cancelable&&e.preventDefault());a.handle&&!Ue(s,a.handle,n,!1)||this._prepareDragStart(e,l,i)}}},_prepareDragStart:function(e,t,n){var a,o=this,r=o.el,l=o.options,i=r.ownerDocument;if(n&&!ut&&n.parentNode===r){var s=Le(n);if(pt=r,ct=(ut=n).parentNode,ft=ut.nextSibling,ht=n,kt=l.group,Jt.dragged=ut,Dt={target:ut,clientX:(t||e).clientX,clientY:(t||e).clientY},Tt=Dt.clientX-s.left,It=Dt.clientY-s.top,this._lastX=(t||e).clientX,this._lastY=(t||e).clientY,ut.style["will-change"]="all",a=function(){it("delayEnded",o,{evt:e}),Jt.eventCanceled?o._onDrop():(o._disableDelayedDragEvents(),!Ce&&o.nativeDraggable&&(ut.draggable=!0),o._triggerDragStart(e,t),st({sortable:o,name:"choose",originalEvent:e}),je(ut,l.chosenClass,!0))},l.ignore.split(",").forEach((function(e){Ye(ut,e.trim(),tn)})),Ae(i,"dragover",Qt),Ae(i,"mousemove",Qt),Ae(i,"touchmove",Qt),Ae(i,"mouseup",o._onDrop),Ae(i,"touchend",o._onDrop),Ae(i,"touchcancel",o._onDrop),Ce&&this.nativeDraggable&&(this.options.touchStartThreshold=4,ut.draggable=!0),it("delayStart",this,{evt:e}),!l.delay||l.delayOnTouchOnly&&!t||this.nativeDraggable&&(Ee||Se))a();else{if(Jt.eventCanceled)return void this._onDrop();Ae(i,"mouseup",o._disableDelayedDrag),Ae(i,"touchend",o._disableDelayedDrag),Ae(i,"touchcancel",o._disableDelayedDrag),Ae(i,"mousemove",o._delayedDragTouchMoveHandler),Ae(i,"touchmove",o._delayedDragTouchMoveHandler),l.supportPointer&&Ae(i,"pointermove",o._delayedDragTouchMoveHandler),o._dragStartTimer=setTimeout(a,l.delay)}}},_delayedDragTouchMoveHandler:function(e){var t=e.touches?e.touches[0]:e;Math.max(Math.abs(t.clientX-this._lastX),Math.abs(t.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){ut&&tn(ut),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;Ne(e,"mouseup",this._disableDelayedDrag),Ne(e,"touchend",this._disableDelayedDrag),Ne(e,"touchcancel",this._disableDelayedDrag),Ne(e,"mousemove",this._delayedDragTouchMoveHandler),Ne(e,"touchmove",this._delayedDragTouchMoveHandler),Ne(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,t){t=t||"touch"==e.pointerType&&e,!this.nativeDraggable||t?this.options.supportPointer?Ae(document,"pointermove",this._onTouchMove):Ae(document,t?"touchmove":"mousemove",this._onTouchMove):(Ae(ut,"dragend",this),Ae(pt,"dragstart",this._onDragStart));try{document.selection?on((function(){document.selection.empty()})):window.getSelection().removeAllRanges()}catch(n){}},_dragStarted:function(e,t){if(Pt=!1,pt&&ut){it("dragStarted",this,{evt:t}),this.nativeDraggable&&Ae(document,"dragover",Zt);var n=this.options;!e&&je(ut,n.dragClass,!1),je(ut,n.ghostClass,!0),Jt.active=this,e&&this._appendGhost(),st({sortable:this,name:"start",originalEvent:t})}else this._nulling()},_emulateDragOver:function(){if(St){this._lastX=St.clientX,this._lastY=St.clientY,Gt();for(var e=document.elementFromPoint(St.clientX,St.clientY),t=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint(St.clientX,St.clientY))!==t;)t=e;if(ut.parentNode[tt]._isOutsideThisEl(e),t)do{if(t[tt]){if(t[tt]._onDragOver({clientX:St.clientX,clientY:St.clientY,target:e,rootEl:t})&&!this.options.dragoverBubble)break}e=t}while(t=t.parentNode);Kt()}},_onTouchMove:function(e){if(Dt){var t=this.options,n=t.fallbackTolerance,a=t.fallbackOffset,o=e.touches?e.touches[0]:e,r=dt&&Re(dt,!0),l=dt&&r&&r.a,i=dt&&r&&r.d,s=Lt&&Mt&&qe(Mt),u=(o.clientX-Dt.clientX+a.x)/(l||1)+(s?s[0]-Bt[0]:0)/(l||1),c=(o.clientY-Dt.clientY+a.y)/(i||1)+(s?s[1]-Bt[1]:0)/(i||1);if(!Jt.active&&!Pt){if(n&&Math.max(Math.abs(o.clientX-this._lastX),Math.abs(o.clientY-this._lastY))<n)return;this._onDragStart(e,!0)}if(dt){r?(r.e+=u-(Et||0),r.f+=c-(Ct||0)):r={a:1,b:0,c:0,d:1,e:u,f:c};var d="matrix(".concat(r.a,",").concat(r.b,",").concat(r.c,",").concat(r.d,",").concat(r.e,",").concat(r.f,")");Be(dt,"webkitTransform",d),Be(dt,"mozTransform",d),Be(dt,"msTransform",d),Be(dt,"transform",d),Et=u,Ct=c,St=o}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!dt){var e=this.options.fallbackOnBody?document.body:pt,t=Le(ut,!0,Lt,!0,e),n=this.options;if(Lt){for(Mt=e;"static"===Be(Mt,"position")&&"none"===Be(Mt,"transform")&&Mt!==document;)Mt=Mt.parentNode;Mt!==document.body&&Mt!==document.documentElement?(Mt===document&&(Mt=Xe()),t.top+=Mt.scrollTop,t.left+=Mt.scrollLeft):Mt=Xe(),Bt=qe(Mt)}je(dt=ut.cloneNode(!0),n.ghostClass,!1),je(dt,n.fallbackClass,!0),je(dt,n.dragClass,!0),Be(dt,"transition",""),Be(dt,"transform",""),Be(dt,"box-sizing","border-box"),Be(dt,"margin",0),Be(dt,"top",t.top),Be(dt,"left",t.left),Be(dt,"width",t.width),Be(dt,"height",t.height),Be(dt,"opacity","0.8"),Be(dt,"position",Lt?"absolute":"fixed"),Be(dt,"zIndex","100000"),Be(dt,"pointerEvents","none"),Jt.ghost=dt,e.appendChild(dt),Be(dt,"transform-origin",Tt/parseInt(dt.style.width)*100+"% "+It/parseInt(dt.style.height)*100+"%")}},_onDragStart:function(e,t){var n=this,a=e.dataTransfer,o=n.options;it("dragStart",this,{evt:e}),Jt.eventCanceled?this._onDrop():(it("setupClone",this),Jt.eventCanceled||((gt=Je(ut)).removeAttribute("id"),gt.draggable=!1,gt.style["will-change"]="",this._hideClone(),je(gt,this.options.chosenClass,!1),Jt.clone=gt),n.cloneId=on((function(){it("clone",n),!Jt.eventCanceled&&(n.options.removeCloneOnHide||pt.insertBefore(gt,ut),n._hideClone(),st({sortable:n,name:"clone"}))})),!t&&je(ut,o.dragClass,!0),t?(Ut=!0,n._loopId=setInterval(n._emulateDragOver,50)):(Ne(document,"mouseup",n._onDrop),Ne(document,"touchend",n._onDrop),Ne(document,"touchcancel",n._onDrop),a&&(a.effectAllowed="move",o.setData&&o.setData.call(n,a,ut)),Ae(document,"drop",n),Be(ut,"transform","translateZ(0)")),Pt=!0,n._dragStartId=on(n._dragStarted.bind(n,t,e)),Ae(document,"selectstart",n),xt=!0,Te&&Be(document.body,"user-select","none"))},_onDragOver:function(e){var t,n,a,o,r=this.el,l=e.target,i=this.options,s=i.group,u=Jt.active,c=kt===s,d=i.sort,p=_t||u,f=this,h=!1;if(!Rt){if(void 0!==e.preventDefault&&e.cancelable&&e.preventDefault(),l=Ue(l,i.draggable,r,!0),x("dragOver"),Jt.eventCanceled)return h;if(ut.contains(e.target)||l.animated&&l.animatingX&&l.animatingY||f._ignoreWhileAnimating===l)return A(!1);if(Ut=!1,u&&!i.disabled&&(c?d||(a=ct!==pt):_t===this||(this.lastPutMode=kt.checkPull(this,u,ut,e))&&s.checkPut(this,u,ut,e))){if(o="vertical"===this._getDirection(e,l),t=Le(ut),x("dragOverValid"),Jt.eventCanceled)return h;if(a)return ct=pt,O(),this._hideClone(),x("revert"),Jt.eventCanceled||(ft?pt.insertBefore(ut,ft):pt.appendChild(ut)),A(!0);var g=We(r,i.draggable);if(!g||function(e,t,n){var a=Le(We(n.el,n.options.draggable)),o=et(n.el,n.options,dt),r=10;return t?e.clientX>o.right+r||e.clientY>a.bottom&&e.clientX>a.left:e.clientY>o.bottom+r||e.clientX>a.right&&e.clientY>a.top}(e,o,this)&&!g.animated){if(g===ut)return A(!1);if(g&&r===e.target&&(l=g),l&&(n=Le(l)),!1!==en(pt,r,ut,t,l,n,e,!!l))return O(),g&&g.nextSibling?r.insertBefore(ut,g.nextSibling):r.appendChild(ut),ct=r,N(),A(!0)}else if(g&&function(e,t,n){var a=Le(He(n.el,0,n.options,!0)),o=et(n.el,n.options,dt),r=10;return t?e.clientX<o.left-r||e.clientY<a.top&&e.clientX<a.right:e.clientY<o.top-r||e.clientY<a.bottom&&e.clientX<a.left}(e,o,this)){var m=He(r,0,i,!0);if(m===ut)return A(!1);if(n=Le(l=m),!1!==en(pt,r,ut,t,l,n,e,!1))return O(),r.insertBefore(ut,m),ct=r,N(),A(!0)}else if(l.parentNode===r){n=Le(l);var v,b,y,w=ut.parentNode!==r,k=!function(e,t,n){var a=n?e.left:e.top,o=n?e.right:e.bottom,r=n?e.width:e.height,l=n?t.left:t.top,i=n?t.right:t.bottom,s=n?t.width:t.height;return a===l||o===i||a+r/2===l+s/2}(ut.animated&&ut.toRect||t,l.animated&&l.toRect||n,o),_=o?"top":"left",D=ze(l,"top","top")||ze(ut,"top","top"),S=D?D.scrollTop:void 0;if(Ot!==l&&(b=n[_],Ft=!1,jt=!k&&i.invertSwap||w),v=function(e,t,n,a,o,r,l,i){var s=a?e.clientY:e.clientX,u=a?n.height:n.width,c=a?n.top:n.left,d=a?n.bottom:n.right,p=!1;if(!l)if(i&&Nt<u*o){if(!Ft&&(1===At?s>c+u*r/2:s<d-u*r/2)&&(Ft=!0),Ft)p=!0;else if(1===At?s<c+Nt:s>d-Nt)return-At}else if(s>c+u*(1-o)/2&&s<d-u*(1-o)/2)return function(e){return $e(ut)<$e(e)?1:-1}(t);return p=p||l,p&&(s<c+u*r/2||s>d-u*r/2)?s>c+u/2?1:-1:0}(e,l,n,o,k?1:i.swapThreshold,null==i.invertedSwapThreshold?i.swapThreshold:i.invertedSwapThreshold,jt,Ot===l),0!==v){var E=$e(ut);do{E-=v,y=ct.children[E]}while(y&&("none"===Be(y,"display")||y===dt))}if(0===v||y===l)return A(!1);Ot=l,At=v;var C=l.nextElementSibling,T=!1,I=en(pt,r,ut,t,l,n,e,T=1===v);if(!1!==I)return(1===I||-1===I)&&(T=1===I),Rt=!0,setTimeout(nn,30),O(),T&&!C?r.appendChild(ut):l.parentNode.insertBefore(ut,T?C:l),D&&Ze(D,0,S-D.scrollTop),ct=ut.parentNode,void 0!==b&&!jt&&(Nt=Math.abs(b-Le(l)[_])),N(),A(!0)}if(r.contains(ut))return A(!1)}return!1}function x(i,s){it(i,f,be({evt:e,isOwner:c,axis:o?"vertical":"horizontal",revert:a,dragRect:t,targetRect:n,canSort:d,fromSortable:p,target:l,completed:A,onMove:function(n,a){return en(pt,r,ut,t,n,Le(n),e,a)},changed:N},s))}function O(){x("dragOverAnimationCapture"),f.captureAnimationState(),f!==p&&p.captureAnimationState()}function A(t){return x("dragOverCompleted",{insertion:t}),t&&(c?u._hideClone():u._showClone(f),f!==p&&(je(ut,_t?_t.options.ghostClass:u.options.ghostClass,!1),je(ut,i.ghostClass,!0)),_t!==f&&f!==Jt.active?_t=f:f===Jt.active&&_t&&(_t=null),p===f&&(f._ignoreWhileAnimating=l),f.animateAll((function(){x("dragOverAnimationComplete"),f._ignoreWhileAnimating=null})),f!==p&&(p.animateAll(),p._ignoreWhileAnimating=null)),(l===ut&&!ut.animated||l===r&&!l.animated)&&(Ot=null),!i.dragoverBubble&&!e.rootEl&&l!==document&&(ut.parentNode[tt]._isOutsideThisEl(e.target),!t&&Qt(e)),!i.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),h=!0}function N(){bt=$e(ut),wt=$e(ut,i.draggable),st({sortable:f,name:"change",toEl:r,newIndex:bt,newDraggableIndex:wt,originalEvent:e})}},_ignoreWhileAnimating:null,_offMoveEvents:function(){Ne(document,"mousemove",this._onTouchMove),Ne(document,"touchmove",this._onTouchMove),Ne(document,"pointermove",this._onTouchMove),Ne(document,"dragover",Qt),Ne(document,"mousemove",Qt),Ne(document,"touchmove",Qt)},_offUpEvents:function(){var e=this.el.ownerDocument;Ne(e,"mouseup",this._onDrop),Ne(e,"touchend",this._onDrop),Ne(e,"pointerup",this._onDrop),Ne(e,"touchcancel",this._onDrop),Ne(document,"selectstart",this)},_onDrop:function(e){var t=this.el,n=this.options;bt=$e(ut),wt=$e(ut,n.draggable),it("drop",this,{evt:e}),ct=ut&&ut.parentNode,bt=$e(ut),wt=$e(ut,n.draggable),Jt.eventCanceled||(Pt=!1,jt=!1,Ft=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),rn(this.cloneId),rn(this._dragStartId),this.nativeDraggable&&(Ne(document,"drop",this),Ne(t,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),Te&&Be(document.body,"user-select",""),Be(ut,"transform",""),e&&(xt&&(e.cancelable&&e.preventDefault(),!n.dropBubble&&e.stopPropagation()),dt&&dt.parentNode&&dt.parentNode.removeChild(dt),(pt===ct||_t&&"clone"!==_t.lastPutMode)&&gt&&gt.parentNode&&gt.parentNode.removeChild(gt),ut&&(this.nativeDraggable&&Ne(ut,"dragend",this),tn(ut),ut.style["will-change"]="",xt&&!Pt&&je(ut,_t?_t.options.ghostClass:this.options.ghostClass,!1),je(ut,this.options.chosenClass,!1),st({sortable:this,name:"unchoose",toEl:ct,newIndex:null,newDraggableIndex:null,originalEvent:e}),pt!==ct?(bt>=0&&(st({rootEl:ct,name:"add",toEl:ct,fromEl:pt,originalEvent:e}),st({sortable:this,name:"remove",toEl:ct,originalEvent:e}),st({rootEl:ct,name:"sort",toEl:ct,fromEl:pt,originalEvent:e}),st({sortable:this,name:"sort",toEl:ct,originalEvent:e})),_t&&_t.save()):bt!==vt&&bt>=0&&(st({sortable:this,name:"update",toEl:ct,originalEvent:e}),st({sortable:this,name:"sort",toEl:ct,originalEvent:e})),Jt.active&&((null==bt||-1===bt)&&(bt=vt,wt=yt),st({sortable:this,name:"end",toEl:ct,originalEvent:e}),this.save())))),this._nulling()},_nulling:function(){it("nulling",this),pt=ut=ct=dt=ft=gt=ht=mt=Dt=St=xt=bt=wt=vt=yt=Ot=At=_t=kt=Jt.dragged=Jt.ghost=Jt.clone=Jt.active=null,Yt.forEach((function(e){e.checked=!0})),Yt.length=Et=Ct=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":ut&&(this._onDragOver(e),(t=e).dataTransfer&&(t.dataTransfer.dropEffect="move"),t.cancelable&&t.preventDefault());break;case"selectstart":e.preventDefault()}var t},toArray:function(){for(var e,t=[],n=this.el.children,a=0,o=n.length,r=this.options;a<o;a++)Ue(e=n[a],r.draggable,this.el,!1)&&t.push(e.getAttribute(r.dataIdAttr)||an(e));return t},sort:function(e,t){var n={},a=this.el;this.toArray().forEach((function(e,t){var o=a.children[t];Ue(o,this.options.draggable,a,!1)&&(n[e]=o)}),this),t&&this.captureAnimationState(),e.forEach((function(e){n[e]&&(a.removeChild(n[e]),a.appendChild(n[e]))})),t&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,t){return Ue(e,t||this.options.draggable,this.el,!1)},option:function(e,t){var n=this.options;if(void 0===t)return n[e];var a=rt.modifyOption(this,e,t);n[e]=void 0!==a?a:t,"group"===e&&qt(n)},destroy:function(){it("destroy",this);var e=this.el;e[tt]=null,Ne(e,"mousedown",this._onTapStart),Ne(e,"touchstart",this._onTapStart),Ne(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(Ne(e,"dragover",this),Ne(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),(function(e){e.removeAttribute("draggable")})),this._onDrop(),this._disableDelayedDragEvents(),Vt.splice(Vt.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!mt){if(it("hideClone",this),Jt.eventCanceled)return;Be(gt,"display","none"),this.options.removeCloneOnHide&&gt.parentNode&&gt.parentNode.removeChild(gt),mt=!0}},_showClone:function(e){if("clone"===e.lastPutMode){if(mt){if(it("showClone",this),Jt.eventCanceled)return;ut.parentNode!=pt||this.options.group.revertClone?ft?pt.insertBefore(gt,ft):pt.appendChild(gt):pt.insertBefore(gt,ut),this.options.group.revertClone&&this.animate(ut,gt),Be(gt,"display",""),mt=!1}}else this._hideClone()}},Xt&&Ae(document,"touchmove",(function(e){(Jt.active||Pt)&&e.cancelable&&e.preventDefault()})),Jt.utils={on:Ae,off:Ne,css:Be,find:Ye,is:function(e,t){return!!Ue(e,t,e,!1)},extend:function(e,t){if(e&&t)for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e},throttle:Qe,closest:Ue,toggleClass:je,clone:Je,index:$e,nextTick:on,cancelNextTick:rn,detectDirection:$t,getChild:He},Jt.get=function(e){return e[tt]},Jt.mount=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];t[0].constructor===Array&&(t=t[0]),t.forEach((function(e){if(!e.prototype||!e.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(e));e.utils&&(Jt.utils=be(be({},Jt.utils),e.utils)),rt.mount(e)}))},Jt.create=function(e,t){return new Jt(e,t)},Jt.version="1.15.2";var ln,sn,un,cn,dn,pn,fn=[],hn=!1;function gn(){fn.forEach((function(e){clearInterval(e.pid)})),fn=[]}function mn(){clearInterval(pn)}var vn=Qe((function(e,t,n,a){if(t.scroll){var o,r=(e.touches?e.touches[0]:e).clientX,l=(e.touches?e.touches[0]:e).clientY,i=t.scrollSensitivity,s=t.scrollSpeed,u=Xe(),c=!1;sn!==n&&(sn=n,gn(),ln=t.scroll,o=t.scrollFn,!0===ln&&(ln=Ge(n,!0)));var d=0,p=ln;do{var f=p,h=Le(f),g=h.top,m=h.bottom,v=h.left,b=h.right,y=h.width,w=h.height,k=void 0,_=void 0,D=f.scrollWidth,S=f.scrollHeight,E=Be(f),C=f.scrollLeft,T=f.scrollTop;f===u?(k=y<D&&("auto"===E.overflowX||"scroll"===E.overflowX||"visible"===E.overflowX),_=w<S&&("auto"===E.overflowY||"scroll"===E.overflowY||"visible"===E.overflowY)):(k=y<D&&("auto"===E.overflowX||"scroll"===E.overflowX),_=w<S&&("auto"===E.overflowY||"scroll"===E.overflowY));var I=k&&(Math.abs(b-r)<=i&&C+y<D)-(Math.abs(v-r)<=i&&!!C),x=_&&(Math.abs(m-l)<=i&&T+w<S)-(Math.abs(g-l)<=i&&!!T);if(!fn[d])for(var O=0;O<=d;O++)fn[O]||(fn[O]={});(fn[d].vx!=I||fn[d].vy!=x||fn[d].el!==f)&&(fn[d].el=f,fn[d].vx=I,fn[d].vy=x,clearInterval(fn[d].pid),(0!=I||0!=x)&&(c=!0,fn[d].pid=setInterval(function(){a&&0===this.layer&&Jt.active._onTouchMove(dn);var t=fn[this.layer].vy?fn[this.layer].vy*s:0,n=fn[this.layer].vx?fn[this.layer].vx*s:0;"function"==typeof o&&"continue"!==o.call(Jt.dragged.parentNode[tt],n,t,e,dn,fn[this.layer].el)||Ze(fn[this.layer].el,n,t)}.bind({layer:d}),24))),d++}while(t.bubbleScroll&&p!==u&&(p=Ge(p,!1)));hn=c}}),30),bn=function(e){var t=e.originalEvent,n=e.putSortable,a=e.dragEl,o=e.activeSortable,r=e.dispatchSortableEvent,l=e.hideGhostForTarget,i=e.unhideGhostForTarget;if(t){var s=n||o;l();var u=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t,c=document.elementFromPoint(u.clientX,u.clientY);i(),s&&!s.el.contains(c)&&(r("spill"),this.onSpill({dragEl:a,putSortable:n}))}};function yn(){}function wn(){}function kn(e){return null==e?e:JSON.parse(JSON.stringify(e))}yn.prototype={startIndex:null,dragStart:function(e){var t=e.oldDraggableIndex;this.startIndex=t},onSpill:function(e){var t=e.dragEl,n=e.putSortable;this.sortable.captureAnimationState(),n&&n.captureAnimationState();var a=He(this.sortable.el,this.startIndex,this.options);a?this.sortable.el.insertBefore(t,a):this.sortable.el.appendChild(t),this.sortable.animateAll(),n&&n.animateAll()},drop:bn},ke(yn,{pluginName:"revertOnSpill"}),wn.prototype={onSpill:function(e){var t=e.dragEl,n=e.putSortable||this.sortable;n.captureAnimationState(),t.parentNode&&t.parentNode.removeChild(t),n.animateAll()},drop:bn},ke(wn,{pluginName:"removeOnSpill"}),Jt.mount(new function(){function e(){for(var e in this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0},this)"_"===e.charAt(0)&&"function"==typeof this[e]&&(this[e]=this[e].bind(this))}return e.prototype={dragStarted:function(e){var t=e.originalEvent;this.sortable.nativeDraggable?Ae(document,"dragover",this._handleAutoScroll):this.options.supportPointer?Ae(document,"pointermove",this._handleFallbackAutoScroll):t.touches?Ae(document,"touchmove",this._handleFallbackAutoScroll):Ae(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(e){var t=e.originalEvent;!this.options.dragOverBubble&&!t.rootEl&&this._handleAutoScroll(t)},drop:function(){this.sortable.nativeDraggable?Ne(document,"dragover",this._handleAutoScroll):(Ne(document,"pointermove",this._handleFallbackAutoScroll),Ne(document,"touchmove",this._handleFallbackAutoScroll),Ne(document,"mousemove",this._handleFallbackAutoScroll)),mn(),gn(),clearTimeout(Ve),Ve=void 0},nulling:function(){dn=sn=ln=hn=pn=un=cn=null,fn.length=0},_handleFallbackAutoScroll:function(e){this._handleAutoScroll(e,!0)},_handleAutoScroll:function(e,t){var n=this,a=(e.touches?e.touches[0]:e).clientX,o=(e.touches?e.touches[0]:e).clientY,r=document.elementFromPoint(a,o);if(dn=e,t||this.options.forceAutoScrollFallback||Ee||Se||Te){vn(e,this.options,r,t);var l=Ge(r,!0);hn&&(!pn||a!==un||o!==cn)&&(pn&&mn(),pn=setInterval((function(){var r=Ge(document.elementFromPoint(a,o),!0);r!==l&&(l=r,gn()),vn(e,n.options,r,t)}),10),un=a,cn=o)}else{if(!this.options.bubbleScroll||Ge(r,!0)===Xe())return void gn();vn(e,this.options,Ge(r,!1),!1)}}},ke(e,{pluginName:"scroll",initializeByDefault:!0})}),Jt.mount(wn,yn);let _n=null,Dn=null;function Sn(e=null,t=null){_n=e,Dn=t}const En=Symbol("cloneElement");function Cn(...e){var t,n;const o=null==(t=i())?void 0:t.proxy;let r=null;const l=e[0];let[,f,h]=e;Array.isArray(a(f))||(h=f,f=null);let g=null;const{immediate:m=!0,clone:v=kn,customUpdate:b}=null!=(n=a(h))?n:{};const y={onUpdate:function(e){if(b)return void b(e);const{from:t,item:n,oldIndex:o,oldDraggableIndex:r,newDraggableIndex:l}=e;if(he(n),fe(t,n,o),p(f)){const e=[...a(f)];f.value=ce(e,r,l)}else ce(a(f),r,l)},onStart:function(e){var t;const{from:n,oldIndex:o,item:l}=e;r=Array.from(n.childNodes);const i=a(null==(t=a(f))?void 0:t[o]),s=v(i);Sn(i,s),l[En]=s},onAdd:function(e){const t=e.item[En];if(!function(e){return void 0===e}(t)){if(he(e.item),p(f)){const n=[...a(f)];return void(f.value=pe(n,e.newDraggableIndex,t))}pe(a(f),e.newDraggableIndex,t)}},onRemove:function(e){const{from:t,item:n,oldIndex:o,oldDraggableIndex:r,pullMode:l,clone:i}=e;if(fe(t,n,o),"clone"!==l)if(p(f)){const e=[...a(f)];f.value=de(e,r)}else de(a(f),r);else he(i)},onEnd:function(e){const{newIndex:t,oldIndex:n,from:a,to:o}=e;let l=null;const i=t===n&&a===o;try{if(i){let e=null;null==r||r.some(((t,n)=>{if(e&&(null==r?void 0:r.length)!==o.childNodes.length)return a.insertBefore(e,t.nextSibling),!0;const l=o.childNodes[n];e=null==o?void 0:o.replaceChild(t,l)}))}}catch(s){l=s}finally{r=null}c((()=>{if(Sn(),l)throw l}))}};function w(e){const t=a(l);return e||(e=function(e){return"string"==typeof e}(t)?function(e,t=document){var n;let a=null;return a="function"==typeof(null==t?void 0:t.querySelector)?null==(n=null==t?void 0:t.querySelector)?void 0:n.call(t,e):document.querySelector(e),a}(t,null==o?void 0:o.$el):t),e&&!function(e){return e instanceof HTMLElement}(e)&&(e=e.$el),e}function k(){var e;const t=null!=(e=a(h))?e:{},{immediate:n,clone:o}=t,r=ue(t,["immediate","clone"]);return ge(r,((e,t)=>{(function(e){return 111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97)})(e)&&(r[e]=(e,...n)=>(me(e,{data:_n,clonedData:Dn}),t(e,...n)))})),function(e,t){const n=se({},e);return Object.keys(t).forEach((a=>{n[a]?n[a]=function(e,t,n=null){return function(...a){return e.apply(n,a),t.apply(n,a)}}(e[a],t[a]):n[a]=t[a]})),n}(null===f?{}:y,r)}const _=e=>{e=w(e),g&&D.destroy(),g=new Jt(e,k())};s((()=>h),(()=>{g&&ge(k(),((e,t)=>{null==g||g.option(e,t)}))}),{deep:!0});const D={option:(e,t)=>null==g?void 0:g.option(e,t),destroy:()=>{null==g||g.destroy(),g=null},save:()=>null==g?void 0:g.save(),toArray:()=>null==g?void 0:g.toArray(),closest:(...e)=>null==g?void 0:g.closest(...e)};return function(e){i()?u(e):c(e)}((()=>{m&&_()})),function(e){i()&&d(e)}(D.destroy),se({start:_,pause:()=>null==D?void 0:D.option("disabled",!0),resume:()=>null==D?void 0:D.option("disabled",!1)},D)}const Tn=["update","start","add","remove","choose","unchoose","end","sort","filter","clone","move","change"],In=e({name:"VueDraggable",model:{prop:"modelValue",event:"update:modelValue"},props:["clone","animation","ghostClass","group","sort","disabled","store","handle","draggable","swapThreshold","invertSwap","invertedSwapThreshold","removeCloneOnHide","direction","chosenClass","dragClass","ignore","filter","preventOnFilter","easing","setData","dropBubble","dragoverBubble","dataIdAttr","delay","delayOnTouchOnly","touchStartThreshold","forceFallback","fallbackClass","fallbackOnBody","fallbackTolerance","fallbackOffset","supportPointer","emptyInsertThreshold","scroll","forceAutoScrollFallback","scrollSensitivity","scrollSpeed","bubbleScroll","modelValue","tag","target","customUpdate",...Tn.map((e=>`on${e.replace(/^\S/,(e=>e.toUpperCase()))}`))],emits:["update:modelValue",...Tn],setup(e,{slots:i,emit:s,expose:u,attrs:c}){const d=Tn.reduce(((e,t)=>(e[`on${t.replace(/^\S/,(e=>e.toUpperCase()))}`]=(...e)=>s(t,...e),e)),{}),p=t((()=>{const t=n(e),{modelValue:o}=t,r=ue(t,["modelValue"]),l=Object.entries(r).reduce(((e,[t,n])=>{const o=a(n);return void 0!==o&&(e[t]=o),e}),{});return se(se({},d),function(e){return Object.keys(e).reduce(((t,n)=>(void 0!==e[n]&&(t[function(e){return e.replace(/-(\w)/g,((e,t)=>t?t.toUpperCase():""))}(n)]=e[n]),t)),{})}(se(se({},c),l)))})),f=t({get:()=>e.modelValue,set:e=>s("update:modelValue",e)}),h=o(),g=r(Cn(e.target||h,f,p));return u(g),()=>{var t;return l(e.tag||"div",{ref:h},null==(t=null==i?void 0:i.default)?void 0:t.call(i,g))}}}),xn={class:"preview-header"},On={class:"task-id"},An={class:"task-title"},Nn={class:"task-info"},Mn={class:"info-item"},Pn={key:0,class:"info-item"},Un={key:1,class:"info-item"},Vn={key:0,class:"task-description"},Fn={class:"preview-footer"},jn={key:1,class:"comment-count"},Bn=f({__name:"TaskPreviewPopup",props:{task:{type:Object,default:null},top:{type:Number,default:0},left:{type:Number,default:0}},setup(e){const t=e=>{switch(null==e?void 0:e.toLowerCase()){case"todo":return"未开始";case"inprogress":case"doing":return"进行中";case"done":return"已完成";case"overdue":return"已逾期";default:return e||"未知"}},n=e=>{switch(null==e?void 0:e.toLowerCase()){case"todo":default:return"info";case"inprogress":case"doing":return"warning";case"done":return"success";case"overdue":return"danger"}},o=e=>{switch(null==e?void 0:e.toLowerCase()){case"high":return"高优先级";case"medium":return"中优先级";case"low":return"低优先级";case"urgent":return"紧急";default:return e||"未设置"}},r=e=>{switch(null==e?void 0:e.toLowerCase()){case"normal":return"普通任务";case"periodic":return"周期任务";case"pdca":return"PDCA任务";default:return e||"未知类型"}},l=e=>{if(!e)return"";try{const t=new Date(e);return isNaN(t.getTime())?e:t.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"})}catch(t){return e}};return(i,s)=>{var u,c;const d=_("el-tag"),p=_("el-icon");return e.task?(m(),h("div",{key:0,class:"task-preview-popup",style:I({top:`${e.top}px`,left:`${e.left}px`})},[v("div",xn,[v("span",On,"#"+y(e.task.taskId),1),b(d,{type:n(e.task.status),size:"small",effect:"light",round:""},{default:w((()=>[k(y(t(e.task.status)),1)])),_:1},8,["type"])]),v("h3",An,y(e.task.name||e.task.title),1),v("div",Nn,[v("div",Mn,[b(p,null,{default:w((()=>[b(a(D))])),_:1}),v("span",null,y(l(e.task.planEndDate)||"未设置截止日期"),1)]),e.task.assigneeUserId?(m(),h("div",Pn,[b(p,null,{default:w((()=>[b(a(S))])),_:1}),v("span",null,y((null==(u=e.task.assignee)?void 0:u.name)||`用户ID: ${e.task.assigneeUserId}`),1)])):g("",!0),e.task.priority?(m(),h("div",Un,[b(p,null,{default:w((()=>[b(a(E))])),_:1}),v("span",null,y(o(e.task.priority)),1)])):g("",!0)]),e.task.description?(m(),h("div",Vn,y((f=e.task.description,x=150,f?f.length>x?f.substring(0,x)+"...":f:"")),1)):g("",!0),v("div",Fn,[e.task.taskType?(m(),C(d,{key:0,size:"small",type:"info",effect:"plain"},{default:w((()=>[k(y(r(e.task.taskType)),1)])),_:1})):g("",!0),(null==(c=e.task.comments)?void 0:c.length)?(m(),h("div",jn,[b(p,null,{default:w((()=>[b(a(T))])),_:1}),v("span",null,y(e.task.comments.length),1)])):g("",!0)])],4)):g("",!0);var f,x}}},[["__scopeId","data-v-3ee4394e"]]),Rn={name:"QuickTaskCreator",components:{Plus:M,Close:N,Check:A,More:O,Star:E,DocumentCopy:x},props:{alwaysExpanded:{type:Boolean,default:!1},triggerText:{type:String,default:"快速创建任务"},triggerClass:{type:String,default:""},defaultAssigneeId:{type:Number,default:null},onCreated:{type:Function,default:null}},emits:["created","expand","collapse","expand-to-full-form"],setup(e,{emit:n}){const a=o(null),l=o(e.alwaysExpanded),i=o(!1),c=o([]),d=o([]),p=o([]),f=o(!1),h=r({name:"",assigneeUserIds:[],priority:"Medium",planEndDate:null}),g=t((()=>c.value.filter((e=>!h.assigneeUserIds.includes(e.id))))),m=()=>{h.name="",h.assigneeUserIds=[],h.priority="Medium",h.planEndDate=null,f.value=!1,a.value&&a.value.clearValidate()},v=()=>{p.value=[],(h.name.includes("bug")||h.name.includes("修复"))&&p.value.push({id:"bug-fix",title:"Bug修复任务",description:"建议设置为高优先级，分配给技术团队",confidence:85,priority:"High",assigneeUserIds:c.value.filter((e=>"developer"===e.role)).map((e=>e.id))}),(h.name.includes("紧急")||h.name.includes("urgent"))&&p.value.push({id:"urgent-task",title:"紧急任务处理",description:"建议立即分配并设置今日截止",confidence:90,priority:"High",dueDate:new Date})};return s((()=>h.name),(e=>{e.length>2&&v()})),u((()=>{(async()=>{try{const e=await F.getUserList();(e.success||e.data)&&(c.value=e.data)}catch(e){}})(),(async()=>{d.value=[{id:1,name:"日常检查",title:"日常设备检查",priority:"Medium",dueInDays:1},{id:2,name:"紧急维修",title:"设备紧急维修",priority:"High",dueInDays:0},{id:3,name:"月度报告",title:"月度工作报告",priority:"Low",dueInDays:7}]})()})),{formRef:a,isExpanded:l,submitting:i,users:c,templates:d,smartSuggestions:p,showSuggestions:f,form:h,rules:{name:[{required:!0,message:"请输入任务名称",trigger:"blur"}],assigneeUserIds:[{type:"array",message:"请选择负责人",trigger:"change"}],priority:[{required:!0,message:"请选择优先级",trigger:"change"}]},availableCollaborators:g,expandCreator:()=>{l.value=!0,n("expand")},collapseCreator:()=>{l.value=!1,m(),n("collapse")},resetForm:m,submitForm:async()=>{a.value&&await a.value.validate((async(e,t)=>{if(e){i.value=!0;try{let e=null,t=[];h.assigneeUserIds.length>0&&(e=h.assigneeUserIds[0],t=h.assigneeUserIds.slice(1));const a={name:h.name,priority:h.priority,status:"Todo",assigneeUserId:e,assigneeUserIds:h.assigneeUserIds,collaboratorUserIds:t,planEndDate:h.planEndDate?new Date(h.planEndDate).toISOString():null},o=await te.createTask(a);m(),n("created",o.data),j.success("任务创建成功")}catch(a){j.error(`创建任务失败: ${a.message||"未知错误"}`)}finally{i.value=!1}}}))},expandToFullForm:()=>{n("expand-to-full-form",{title:h.name,assigneeUserIds:h.assigneeUserIds,priority:h.priority,dueDate:h.planEndDate})},applyTemplate:e=>{if(h.name=e.title,h.assigneeUserIds=e.defaultAssigneeIds,h.priority=e.priority,e.dueInDays){const t=new Date;t.setDate(t.getDate()+e.dueInDays),h.planEndDate=t}j.success(`已应用模板：${e.name}`)},applySuggestion:e=>{e.assigneeUserIds&&(h.assigneeUserIds=e.assigneeUserIds),e.priority&&(h.priority=e.priority),e.collaborators&&(h.collaboratorUserIds=e.collaborators),f.value=!1,j.success("已应用建议")},generateSmartSuggestions:v}}},Yn={class:"quick-task-creator"},Xn={class:"title"},Ln={class:"user-option"},zn={class:"user-dept"};const Hn=f(Rn,[["render",function(e,t,n,a,o,r){const l=_("el-input"),i=_("el-form-item"),s=_("el-avatar"),u=_("el-option"),c=_("el-select"),d=_("el-date-picker"),p=_("el-button"),f=_("el-form");return m(),h("div",Yn,[v("div",Xn,[P(e.$slots,"title",{},(()=>[t[4]||(t[4]=k("快速创建任务"))]),!0)]),b(f,{model:a.form,rules:a.rules,ref:"formRef","label-width":"80px",size:"small"},{default:w((()=>[b(i,{label:"任务名称",prop:"name"},{default:w((()=>[b(l,{modelValue:a.form.name,"onUpdate:modelValue":t[0]||(t[0]=e=>a.form.name=e),placeholder:"请输入任务名称"},null,8,["modelValue"])])),_:1}),b(i,{label:"负责人",prop:"assigneeUserIds"},{default:w((()=>[b(c,{modelValue:a.form.assigneeUserIds,"onUpdate:modelValue":t[1]||(t[1]=e=>a.form.assigneeUserIds=e),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",filterable:"",placeholder:"请选择负责人(多选)",loading:e.loading,style:{width:"100%"}},{default:w((()=>[(m(!0),h(U,null,V(a.users,(e=>(m(),C(u,{key:e.id,label:e.name,value:e.id},{default:w((()=>[v("div",Ln,[e.avatarUrl?(m(),C(s,{key:0,size:24,src:e.avatarUrl},{default:w((()=>[k(y(e.name.substring(0,1)),1)])),_:2},1032,["src"])):g("",!0),v("span",null,y(e.name),1),v("span",zn,y(e.department),1)])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),b(i,{label:"优先级",prop:"priority"},{default:w((()=>[b(c,{modelValue:a.form.priority,"onUpdate:modelValue":t[2]||(t[2]=e=>a.form.priority=e),placeholder:"请选择优先级"},{default:w((()=>[b(u,{label:"高",value:"High"}),b(u,{label:"中",value:"Medium"}),b(u,{label:"低",value:"Low"})])),_:1},8,["modelValue"])])),_:1}),b(i,{label:"截止日期",prop:"planEndDate"},{default:w((()=>[b(d,{modelValue:a.form.planEndDate,"onUpdate:modelValue":t[3]||(t[3]=e=>a.form.planEndDate=e),type:"date",placeholder:"选择日期"},null,8,["modelValue"])])),_:1}),b(i,null,{default:w((()=>[b(p,{type:"primary",onClick:a.submitForm,loading:a.submitting},{default:w((()=>t[5]||(t[5]=[k("创建")]))),_:1},8,["onClick","loading"]),b(p,{onClick:a.resetForm},{default:w((()=>t[6]||(t[6]=[k("重置")]))),_:1},8,["onClick"])])),_:1})])),_:1},8,["model","rules"])])}],["__scopeId","data-v-30abedb0"]]),Wn={class:"task-header"},$n={class:"header-content"},qn={class:"header-left"},Gn={class:"header-right"},Kn={class:"flex items-center gap-2"},Qn={class:"text-sm text-gray-600 dark:text-gray-300"},Zn={class:"flex items-center gap-2"},Jn={key:1,class:"kanban-board"},ea={class:"task-column-header"},ta={class:"column-title-wrapper"},na={class:"task-column-title"},aa={class:"task-count"},oa={class:"task-cards-container"},ra={key:2,class:"list-view"},la={class:"flex items-center gap-2"},ia=["onClick"],sa=f({__name:"ModernKanbanView",setup(e){const n=q(),l=o("kanban"),i=o(!1),s=o(""),c=r({status:"",priority:"",assigneeId:""}),d=o(!1),p=o(!1),f=o(!1),D=o(!1),E=o(!1),T=o(null),I=o([]),x=o(null),O=r({top:0,left:0}),A=["Todo","InProgress","Done","Cancelled"],N=o([]),P=r([]),F=t((()=>{let e=n.tasks;if(s.value){const t=s.value.toLowerCase();e=e.filter((e=>{var n,a;return(null==(n=e.name)?void 0:n.toLowerCase().includes(t))||(null==(a=e.description)?void 0:a.toLowerCase().includes(t))}))}return c.status&&(e=e.filter((e=>e.status===c.status))),c.priority&&(e=e.filter((e=>e.priority===c.priority))),e})),te=t((()=>[{status:"Todo",title:"待办任务",icon:"Memo"},{status:"InProgress",title:"进行中",icon:"Loading"},{status:"Done",title:"已完成",icon:"SuccessFilled"},{status:"Overdue",title:"已逾期",icon:"WarningFilled"}].map((e=>({...e,tasks:F.value.filter((t=>t.status===e.status))}))))),ae=t((()=>P.filter((e=>!e.read)).length)),oe=(e,t)=>{if((null==t?void 0:t.ctrlKey)||(null==t?void 0:t.metaKey)){const t=I.value.indexOf(e);t>-1?I.value.splice(t,1):I.value.push(e)}else I.value=I.value.includes(e)?[]:[e]},re=()=>{I.value=[]},le=e=>{T.value=e,p.value=!0},ie=e=>({Todo:"待处理",InProgress:"进行中",Done:"已完成",Cancelled:"已取消"}[e]||e),se=async e=>{try{await $.confirm("确定要删除这个任务吗？","确认删除",{type:"warning"}),await n.deleteTask(e),j.success("任务删除成功")}catch(t){"cancel"!==t&&j.error(t.message||"删除失败")}},ue=e=>{n.fetchTasks(),e&&e.taskId&&setTimeout((()=>{le(e)}),500)},ce=async e=>{try{await n.batchAssignTasks(I.value,e),j.success("批量分配成功"),re()}catch(t){j.error(t.message||"批量分配失败")}},de=async()=>{try{await $.confirm(`确定要删除选中的 ${I.value.length} 个任务吗？`,"确认删除",{type:"warning"}),await n.batchDeleteTasks(I.value),j.success("批量删除成功"),re()}catch(e){"cancel"!==e&&j.error(e.message||"批量删除失败")}},pe=()=>{},fe=async e=>{const{action:t,task:n}=e;try{switch(t){case"edit":break;case"assign":I.value=[n.taskId],f.value=!0;break;case"clone":await ge(n);break;case"delete":await se(n.taskId)}}catch(a){j.error(a.message||"操作失败")}},he=async e=>{const{taskId:t,newStatus:a,oldStatus:o}=e;try{await n.updateTaskStatus(t,a),j.success("任务状态已更新")}catch(r){j.error(r.message||"状态更新失败")}},ge=async e=>{try{const t={...e,name:`${e.name} (副本)`,status:"Todo",progress:0,createTime:null,updateTime:null};delete t.taskId,await n.createTask(t),j.success("任务克隆成功")}catch(t){j.error(t.message||"任务克隆失败")}},me=e=>{},ve=e=>{I.value=e.map((e=>e.taskId))};return u((async()=>{await n.fetchTasks()})),(e,t)=>{const o=_("el-icon"),r=_("el-button"),u=_("el-badge"),P=_("el-input"),$=_("el-option"),q=_("el-select"),ge=_("el-button-group"),be=_("el-table-column"),ye=_("el-tag"),we=_("el-progress"),ke=_("el-table");return m(),h("div",{class:B(["modern-task-management",{dark:i.value}])},[v("div",Wn,[v("div",$n,[v("div",qn,[t[16]||(t[16]=v("h1",{class:"page-title"},"任务管理",-1)),ae.value>0?(m(),C(u,{key:0,value:ae.value,class:"notification-badge"},{default:w((()=>[b(r,{circle:"",onClick:t[0]||(t[0]=e=>E.value=!E.value),class:"notification-btn"},{default:w((()=>[b(o,null,{default:w((()=>[b(a(R))])),_:1})])),_:1})])),_:1},8,["value"])):g("",!0)]),v("div",Gn,[b(P,{modelValue:s.value,"onUpdate:modelValue":t[1]||(t[1]=e=>s.value=e),placeholder:"搜索任务...","prefix-icon":"Search",class:"search-input",clearable:""},null,8,["modelValue"]),b(q,{modelValue:c.status,"onUpdate:modelValue":t[2]||(t[2]=e=>c.status=e),placeholder:"状态",clearable:"",class:"filter-select"},{default:w((()=>[b($,{label:"全部",value:""}),(m(),h(U,null,V(A,(e=>b($,{key:e,label:ie(e),value:e},null,8,["label","value"]))),64))])),_:1},8,["modelValue"]),b(q,{modelValue:c.priority,"onUpdate:modelValue":t[3]||(t[3]=e=>c.priority=e),placeholder:"优先级",clearable:"",class:"filter-select"},{default:w((()=>[b($,{label:"全部",value:""}),b($,{label:"高",value:"High"}),b($,{label:"中",value:"Medium"}),b($,{label:"低",value:"Low"})])),_:1},8,["modelValue"]),b(ge,{class:"view-toggle"},{default:w((()=>[b(r,{type:"kanban"===l.value?"primary":"",onClick:t[4]||(t[4]=e=>l.value="kanban")},{default:w((()=>[b(o,null,{default:w((()=>[b(a(Y))])),_:1})])),_:1},8,["type"]),b(r,{type:"list"===l.value?"primary":"",onClick:t[5]||(t[5]=e=>l.value="list")},{default:w((()=>[b(o,null,{default:w((()=>[b(a(X))])),_:1})])),_:1},8,["type"])])),_:1}),b(Hn,{"trigger-text":"快速创建","trigger-class":"quick-create-btn",onCreated:ue,onExpandToFullForm:me}),b(r,{onClick:t[6]||(t[6]=e=>d.value=!0),class:"detail-create-btn"},{default:w((()=>[b(o,null,{default:w((()=>[b(a(L))])),_:1}),t[17]||(t[17]=k(" 详细创建 "))])),_:1})])])]),I.value.length>0?(m(),h("div",{key:0,class:B(["batch-actions-bar",{visible:I.value.length>0}])},[v("div",Kn,[v("span",Qn," 已选择 "+y(I.value.length)+" 个任务 ",1),b(r,{size:"small",onClick:re},{default:w((()=>t[18]||(t[18]=[k("取消选择")]))),_:1})]),v("div",Zn,[b(r,{size:"small",onClick:t[7]||(t[7]=e=>f.value=!0)},{default:w((()=>[b(o,null,{default:w((()=>[b(a(S))])),_:1}),t[19]||(t[19]=k(" 批量分配 "))])),_:1}),b(r,{size:"small",onClick:t[8]||(t[8]=e=>D.value=!0)},{default:w((()=>[b(o,null,{default:w((()=>[b(a(z))])),_:1}),t[20]||(t[20]=k(" 批量修改状态 "))])),_:1}),b(r,{size:"small",type:"danger",onClick:de},{default:w((()=>[b(o,null,{default:w((()=>[b(a(H))])),_:1}),t[21]||(t[21]=k(" 批量删除 "))])),_:1})])],2)):g("",!0),"kanban"===l.value?(m(),h("div",Jn,[(m(!0),h(U,null,V(te.value,(e=>{return m(),h("div",{key:e.status,class:"task-column"},[v("div",ea,[v("div",ta,[b(o,{class:B(`text-${t=e.status,{Todo:"blue",InProgress:"orange",Done:"green",Overdue:"red"}[t]||"gray"}`)},{default:w((()=>[(m(),C(W(e.icon)))])),_:2},1032,["class"]),v("span",na,y(e.title),1),v("span",aa,y(e.tasks.length),1)]),"已逾期"!==e.status?(m(),C(r,{key:0,size:"small",text:"",onClick:t=>{e.status}},{default:w((()=>[b(o,null,{default:w((()=>[b(a(M))])),_:1})])),_:2},1032,["onClick"])):g("",!0)]),v("div",oa,[b(a(In),{modelValue:e.tasks,"onUpdate:modelValue":t=>e.tasks=t,animation:250,group:"tasks","item-key":"taskId",class:"task-list","ghost-class":"ghost-card","drag-class":"drag-card",onStart:pe,onEnd:t=>(async(e,t)=>{const a=e.item.dataset.taskId;if(a&&"已逾期"!==t)try{await n.updateTaskStatus(a,t),j.success("任务状态更新成功")}catch(o){j.error(o.message||"状态更新失败"),await n.fetchTasks()}})(t,e.status)},{default:w((()=>[(m(!0),h(U,null,V(e.tasks,(e=>(m(),C(J,{key:e.taskId,task:e,selected:I.value.includes(e.taskId),class:B(["in-kanban"]),onSelect:oe,onClick:le,onQuickAction:fe,onStatusChange:he},null,8,["task","selected"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue","onEnd"])])]);var t})),128))])):"list"===l.value?(m(),h("div",ra,[b(ke,{data:F.value,onSelectionChange:ve,stripe:"",class:"modern-table"},{default:w((()=>[b(be,{type:"selection",width:"55"}),b(be,{prop:"name",label:"任务名称","min-width":"200"},{default:w((({row:e})=>{return[v("div",la,[b(ye,{type:(t=e.priority,{High:"danger",Medium:"warning",Low:"success"}[t]||""),size:"small",round:""},{default:w((()=>[k(y(e.priority),1)])),_:2},1032,["type"]),v("span",{class:"font-medium cursor-pointer hover:text-blue-600",onClick:t=>le(e)},y(e.name),9,ia)])];var t})),_:1}),b(be,{prop:"status",label:"状态",width:"100"},{default:w((({row:e})=>{return[b(ye,{type:(t=e.status,{Todo:"",InProgress:"warning",Done:"success",Overdue:"danger"}[t]||""),size:"small"},{default:w((()=>[k(y(e.status),1)])),_:2},1032,["type"])];var t})),_:1}),b(be,{prop:"assigneeUserName",label:"负责人",width:"120"}),b(be,{prop:"planEndDate",label:"截止时间",width:"120"},{default:w((({row:e})=>[v("span",{class:B({"text-red-500":e.isOverdue})},y(a(ne)(e.planEndDate)),3)])),_:1}),b(be,{prop:"progress",label:"进度",width:"100"},{default:w((({row:e})=>[b(we,{percentage:e.progress,"stroke-width":6},null,8,["percentage"])])),_:1}),b(be,{label:"操作",width:"150",fixed:"right"},{default:w((({row:e})=>[b(r,{size:"small",text:"",onClick:t=>le(e)},{default:w((()=>t[22]||(t[22]=[k(" 查看 ")]))),_:2},1032,["onClick"]),b(r,{size:"small",text:"",onClick:e=>{}},{default:w((()=>t[23]||(t[23]=[k(" 编辑 ")]))),_:2},1032,["onClick"]),b(r,{size:"small",text:"",type:"danger",onClick:t=>se(e.taskId)},{default:w((()=>t[24]||(t[24]=[k(" 删除 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])])):g("",!0),b(G,{modelValue:p.value,"onUpdate:modelValue":t[9]||(t[9]=e=>p.value=e),task:T.value,onSave:e.saveTask,onDelete:se,onClose:t[10]||(t[10]=e=>p.value=!1)},null,8,["modelValue","task","onSave"]),b(K,{modelValue:d.value,"onUpdate:modelValue":t[11]||(t[11]=e=>d.value=e),task:e.taskForm,"edit-mode":e.editMode,onSave:e.saveTask,onClose:e.closeTaskDialog},null,8,["modelValue","task","edit-mode","onSave","onClose"]),b(Q,{modelValue:f.value,"onUpdate:modelValue":t[12]||(t[12]=e=>f.value=e),"team-members":N.value,onAssign:ce,onClose:t[13]||(t[13]=e=>f.value=!1)},null,8,["modelValue","team-members"]),b(Z,{visible:D.value,"onUpdate:visible":t[14]||(t[14]=e=>D.value=e),"task-ids":I.value},null,8,["visible","task-ids"]),x.value?(m(),C(Bn,{key:3,task:x.value,top:O.top,left:O.left},null,8,["task","top","left"])):g("",!0),b(ee,{visible:E.value,"onUpdate:visible":t[15]||(t[15]=e=>E.value=e),onViewTask:le},null,8,["visible"])],2)}}},[["__scopeId","data-v-aade3151"]]);export{sa as default};
