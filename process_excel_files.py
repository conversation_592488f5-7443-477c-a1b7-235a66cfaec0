#!/usr/bin/env python3
"""
处理意图步骤信息和话术集Excel文件
按短编码设计原则重新编码step_code
"""

import pandas as pd
import re
from collections import defaultdict

def generate_short_code_mapping(df, script_df=None):
    """
    根据step_code生成短编码映射
    按业务类型分类编码
    """
    # 提取process中的step_code
    step_codes_from_process = []
    for _, row in df.iterrows():
        process_content = str(row.get('process', ''))
        # 提取step_code
        step_code_matches = re.findall(r'step_code:\s*([^\s\n]+)', process_content)
        step_codes_from_process.extend(step_code_matches)

    # 提取话术集中的step_code
    step_codes_from_script = []
    if script_df is not None and len(script_df.columns) >= 4:
        step_codes_from_script = script_df.iloc[:, 3].dropna().astype(str).tolist()

    # 合并所有step_code并去重
    all_step_codes = list(set(step_codes_from_process + step_codes_from_script))
    print(f"从process中找到 {len(set(step_codes_from_process))} 个step_code")
    print(f"从话术集中找到 {len(set(step_codes_from_script))} 个step_code")
    print(f"总共 {len(all_step_codes)} 个唯一的step_code")

    # 按业务类型分类
    categories = {
        'confirm': [],      # 确认类 - C
        'guide': [],        # 引导类 - G
        'transfer': [],     # 转人工类 - T
        'send': [],         # 发送类 - S
        'explain': [],      # 解释类 - E
        'ask': [],          # 询问类 - A
        'normal': [],       # 正常流程 - N
        'other': []         # 其他 - Z (改为Z避免与0混淆)
    }

    # 分类step_code
    for code in all_step_codes:
        code_lower = code.lower()
        # 修改1: 所有transfer_to_human_开头的都归类为transfer
        if code.startswith('transfer_to_human'):
            categories['transfer'].append(code)
        elif 'confirm' in code_lower:
            categories['confirm'].append(code)
        elif 'guide' in code_lower:
            categories['guide'].append(code)
        elif 'transfer' in code_lower:
            categories['transfer'].append(code)
        elif 'send' in code_lower or 'link' in code_lower:
            categories['send'].append(code)
        elif 'explain' in code_lower:
            categories['explain'].append(code)
        elif 'ask' in code_lower:
            categories['ask'].append(code)
        elif 'normal' in code_lower:
            categories['normal'].append(code)
        else:
            categories['other'].append(code)

    # 生成短编码映射
    mapping = {}
    counters = defaultdict(int)

    # 修改2: O改为Z
    category_prefixes = {
        'confirm': 'C',
        'guide': 'G',
        'transfer': 'T',
        'send': 'S',
        'explain': 'E',
        'ask': 'A',
        'normal': 'N',
        'other': 'Z'
    }

    for category, codes in categories.items():
        prefix = category_prefixes[category]
        for code in sorted(codes):  # 排序保证一致性
            counters[prefix] += 1
            short_code = f"{prefix}{counters[prefix]:02d}"
            mapping[code] = short_code
            print(f"{code} -> {short_code}")

    return mapping

def update_process_content(process_content, code_mapping):
    """
    更新process内容中的step_code
    """
    if pd.isna(process_content):
        return process_content
    
    updated_content = str(process_content)
    
    # 替换step_code
    for old_code, new_code in code_mapping.items():
        # 匹配 step_code:old_code 的模式
        pattern = rf'(step_code:\s*){re.escape(old_code)}(\s|$|\n)'
        replacement = rf'\1{new_code}\2'
        updated_content = re.sub(pattern, replacement, updated_content)
    
    return updated_content

def process_intent_steps_file(filename, script_filename):
    """
    处理意图步骤信息文件
    """
    print(f"处理文件: {filename}")

    # 读取Excel文件
    df = pd.read_excel(filename)
    print(f"读取到 {len(df)} 行数据")
    print(f"列名: {list(df.columns)}")

    # 读取话术集文件以获取所有step_code
    script_df = pd.read_excel(script_filename)
    print(f"话术集文件读取到 {len(script_df)} 行数据")

    # 生成短编码映射（包含话术集中的step_code）
    code_mapping = generate_short_code_mapping(df, script_df)

    # 复制D列(process)到E列，并更新step_code
    if 'process' in df.columns:
        df['updated_process'] = df['process'].apply(
            lambda x: update_process_content(x, code_mapping)
        )
        print("已将更新后的process内容写入E列(updated_process)")
    else:
        print("警告: 未找到process列")

    # 保存更新后的文件
    output_filename = filename.replace('.xlsx', '_updated.xlsx')
    df.to_excel(output_filename, index=False)
    print(f"已保存更新后的文件: {output_filename}")

    return code_mapping

def process_script_file(filename, code_mapping):
    """
    处理话术集文件
    """
    print(f"\n处理文件: {filename}")
    
    # 读取Excel文件
    df = pd.read_excel(filename)
    print(f"读取到 {len(df)} 行数据")
    print(f"列名: {list(df.columns)}")
    
    # 假设D列是旧code，在E列写入新code
    if len(df.columns) >= 4:  # 确保有D列
        old_code_col = df.columns[3]  # D列 (索引3)
        print(f"D列名称: {old_code_col}")
        
        # 创建E列映射
        new_codes = []
        for _, row in df.iterrows():
            old_code = str(row.iloc[3]) if not pd.isna(row.iloc[3]) else ''
            new_code = code_mapping.get(old_code, old_code)  # 如果找不到映射就保持原值
            new_codes.append(new_code)
        
        # 添加E列
        df['new_step_code'] = new_codes
        print("已在E列添加新的step_code")
        
        # 显示映射结果示例
        print("\n映射示例:")
        for i in range(min(10, len(df))):
            old_val = df.iloc[i, 3]
            new_val = df.iloc[i, -1]
            if str(old_val) != str(new_val):
                print(f"  {old_val} -> {new_val}")
    
    # 保存更新后的文件
    output_filename = filename.replace('.xlsx', '_updated.xlsx')
    df.to_excel(output_filename, index=False)
    print(f"已保存更新后的文件: {output_filename}")

def main():
    """
    主函数
    """
    print("🚀 开始处理Excel文件...")

    # 文件名
    intent_file = "意图步骤信息20250610151050.xlsx"
    script_file = "意图步骤话术集v220250610151108.xlsx"

    try:
        # 1. 处理意图步骤信息文件（同时读取话术集获取所有step_code）
        code_mapping = process_intent_steps_file(intent_file, script_file)

        # 2. 处理话术集文件
        process_script_file(script_file, code_mapping)

        print("\n✅ 处理完成!")
        print(f"生成了 {len(code_mapping)} 个编码映射")

        # 按分类输出映射关系
        print("\n📋 完整的编码映射关系:")

        # 按前缀分组显示
        categories = {'C': '确认类', 'G': '引导类', 'T': '转人工类', 'S': '发送类',
                     'E': '解释类', 'A': '询问类', 'N': '正常流程类', 'Z': '其他类'}

        for prefix, category_name in categories.items():
            category_mappings = {k: v for k, v in code_mapping.items() if v.startswith(prefix)}
            if category_mappings:
                print(f"\n### {category_name} ({prefix}01-{prefix}{len(category_mappings):02d})")
                for old_code, new_code in sorted(category_mappings.items(), key=lambda x: x[1]):
                    print(f"  {old_code} -> {new_code}")

    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
