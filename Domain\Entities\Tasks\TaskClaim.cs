// File: Domain/Entities/Tasks/TaskClaim.cs
// Description: 任务领取记录实体

using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Domain.Entities.Tasks
{
    /// <summary>
    /// 任务领取记录实体
    /// </summary>
    [Table("task_claims")]
    public class TaskClaim
    {
        /// <summary>
        /// 领取记录ID
        /// </summary>
        [Key]
        [Column("claim_id")]
        public long ClaimId { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        [Required]
        [Column("task_id")]
        public long TaskId { get; set; }

        /// <summary>
        /// 领取用户ID
        /// </summary>
        [Required]
        [Column("claimed_by")]
        public int ClaimedBy { get; set; }

        /// <summary>
        /// 班次ID
        /// </summary>
        [Required]
        [Column("shift_id")]
        public long ShiftId { get; set; }

        /// <summary>
        /// 领取时间
        /// </summary>
        [Required]
        [Column("claimed_at")]
        public DateTime ClaimedAt { get; set; }

        /// <summary>
        /// 领取日期
        /// </summary>
        [Required]
        [Column("claim_date")]
        public DateTime ClaimDate { get; set; }

        /// <summary>
        /// 领取状态 (Claimed-已领取, Started-已开始, Completed-已完成, Cancelled-已取消)
        /// </summary>
        [Required]
        [MaxLength(20)]
        [Column("claim_status")]
        public string ClaimStatus { get; set; } = "Claimed";

        /// <summary>
        /// 开始时间
        /// </summary>
        [Column("started_at")]
        public DateTime? StartedAt { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        [Column("completed_at")]
        public DateTime? CompletedAt { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(1000)]
        [Column("notes")]
        public string? Notes { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; }

        // 导航属性
        /// <summary>
        /// 任务
        /// </summary>
        [ForeignKey("TaskId")]
        public virtual Task Task { get; set; } = null!;

        /// <summary>
        /// 领取用户
        /// </summary>
        [ForeignKey("ClaimedBy")]
        public virtual Models.Entities.User ClaimedByUser { get; set; } = null!;

        /// <summary>
        /// 班次
        /// </summary>
        [ForeignKey("ShiftId")]
        public virtual WorkShift WorkShift { get; set; } = null!;
    }
}
