企业级智能资产管理平台 - 终极实施方案
版本: 13.0 (最终版)

好的，我已经详细分析了您提供的完整项目代码，包括数据库结构、后端和前端的实现。现在为您提供一份中文的、完整的解决方案。

问题的核心与我之前的分析一致：
1.  **前端内容不显示**：主要原因是前端代码在处理从后端接收到的数据时，属性命名的大小写规范（`camelCase` vs `PascalCase`）不一致导致的。
2.  **部门信息不正确**：后端的统计查询逻辑未能根据资产的`Location`（位置）来动态获取其所属的正确部门和负责人。

下面是针对这两个问题的详细修复方案。

---

### 1. 修复前端内容显示问题

此问题的根源在于 `AssetAnalyticsWorkbench.vue` 组件。该组件在不同地方使用了两种不同的大小写规范来访问数据属性。例如，它期望从API返回的顶层对象属性是`camelCase`（例如 `statisticsResult.value.detailedData`），但在处理表格列的辅助函数（如 `getColumnLabel`）中，却使用了 `PascalCase`（例如 `AssetCode`）作为键。

标准的Web开发实践中，C#后端会默认将`PascalCase`的属性序列化为`camelCase`的JSON，前端JavaScript则统一使用`camelCase`来处理。为了解决这个问题，我们需要在前端统一使用 `camelCase`。

#### **修改 `claude/AssetAnalyticsWorkbenchvue.md`**

请对以下几个函数进行修改，将其中用作键（key）的`PascalCase`属性全部改为`camelCase`。

**A. 修改 `getColumnLabel` 函数**

这个函数将数据字段名映射为表格的列标题。

```javascript
// 修改前
const getColumnLabel = (key) => {
  const labelMap = {
    Id: 'ID',
    AssetCode: '资产编号',
    AssetName: '资产名称',
    AssetType: '资产类型',
    Department: '部门',
    Location: '位置',
    Status: '状态',
    Price: '价值(万元)',
    CreatedAt: '创建时间'
  }
  return labelMap[key] || key
}

// 修改后 (使用 camelCase)
const getColumnLabel = (key) => {
  const labelMap = {
    id: 'ID',
    assetCode: '资产编号',
    assetName: '资产名称',
    assetType: '资产类型',
    department: '部门',
    location: '位置',
    status: '状态',
    price: '价值(万元)',
    createdAt: '创建时间'
  }
  return labelMap[key] || key
}
```

**B. 修改 `getColumnWidth` 函数**

此函数定义了特定列的宽度。

```javascript
// 修改前
const getColumnWidth = (key) => {
  const widthMap = {
    Id: 80,
    AssetCode: 120,
    AssetName: 150,
    Status: 100,
    Price: 120
  }
  return widthMap[key] || undefined
}

// 修改后 (使用 camelCase)
const getColumnWidth = (key) => {
  const widthMap = {
    id: 80,
    assetCode: 120,
    assetName: 150,
    status: 100,
    price: 120
  }
  return widthMap[key] || undefined
}
```

**C. 修改 `getColumnFormatter` 函数**

此函数用于格式化特定列的显示内容。

```javascript
// 修改前
const getColumnFormatter = (key) => {
  if (key === 'Price') {
    return (row) => row[key] ? `¥${Number(row[key]).toFixed(2)}万` : '-'
  }
  if (key === 'CreatedAt') {
    return (row) => row[key] ? new Date(row[key]).toLocaleDateString() : '-'
  }
  return undefined
}

// 修改后 (使用 camelCase)
const getColumnFormatter = (key) => {
  if (key === 'price') {
    return (row) => row[key] ? `¥${Number(row[key]).toFixed(2)}万` : '-'
  }
  if (key === 'createdAt') {
    return (row) => row[key] ? new Date(row[key]).toLocaleDateString() : '-'
  }
  return undefined
}
```

完成以上修改后，前端组件将能够正确解析和渲染从后端获取的数据，表格和图表即可正常显示。

---

### 2. 修复后端部门获取逻辑

您关于通过资产位置路径获取部门和负责人的分析非常准确。该逻辑需要在后端的仓储层（Repository）中实现。根据您的数据库结构文件 `20250607itassets数据库.sql`，`locations` 表包含 `Path` 和 `DefaultDepartmentId` 字段，`departments` 表包含 `ManagerId` 字段，这完全支持您的逻辑。

您需要找到 `IStatisticsRepository` 接口的实现类（通常是 `StatisticsRepository.cs`），并修改其中的 `GetDynamicStatisticsAsync` 方法。

#### **修改 `StatisticsRepository.cs` 中的 `GetDynamicStatisticsAsync` 方法**

以下是推荐的实现方式，它会高效地执行查询，避免 N+1 问题。

```csharp
// 在仓储实现类中 (例如: Infrastructure/Persistence/Repositories/StatisticsRepository.cs)
using ItAssetsSystem.Application.Features.Statistics.Dtos;
using ItAssetsSystem.Application.Features.Statistics.Queries;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.EntityFrameworkCore;

public class StatisticsRepository : IStatisticsRepository
{
    private readonly ApplicationDbContext _context;

    public StatisticsRepository(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<DynamicStatisticsResultDto> GetDynamicStatisticsAsync(
        DynamicStatisticsQuery query,
        CancellationToken cancellationToken)
    {
        // ... 此处保留原有的聚合数据查询(aggregatedData)和汇总(summary)逻辑 ...

        // 核心修改：获取详细数据 (detailedData) 的逻辑
        var assetsQuery = _context.Assets
                                  .Include(a => a.Location) // 包含位置信息
                                  .AsQueryable();

        // ... 在这里应用来自 query.QueryRequest.Filters 的筛选条件 ...
        
        // 1. 将查询结果投影到中间对象，包含资产实体和位置路径
        var intermediateData = await assetsQuery
            .Select(a => new {
                AssetEntity = a,
                LocationPath = a.Location.Path
            })
            // 应用分页
            .Skip((query.QueryRequest.Pagination.Page - 1) * query.QueryRequest.Pagination.Size)
            .Take(query.QueryRequest.Pagination.Size)
            .ToListAsync(cancellationToken);

        // 2. 从路径中解析出第三个值作为 locationId
        var locationIds = intermediateData
            .Select(item => {
                var pathParts = item.LocationPath?.Split('/');
                if (pathParts?.Length > 2 && int.TryParse(pathParts[2], out int locId))
                {
                    return (int?)locId;
                }
                return null;
            })
            .Where(id => id.HasValue)
            .Distinct()
            .ToList();

        // 3. 一次性查询所有相关的部门和负责人信息
        var departmentInfoMap = await _context.Locations
            .Where(loc => locationIds.Contains(loc.Id) && loc.DefaultDepartmentId.HasValue)
            .Include(loc => loc.DefaultDepartment) // 包含部门
                .ThenInclude(dept => dept.Manager) // 包含部门的负责人(Personnel)
            .ToDictionaryAsync(loc => loc.Id, loc => new {
                DepartmentName = loc.DefaultDepartment.Name,
                ManagerName = loc.DefaultDepartment.Manager != null ? loc.DefaultDepartment.Manager.Name : "无"
            }, cancellationToken);

        // 4. 构建最终的详细数据列表
        var detailedData = intermediateData.Select(item => {
            var departmentName = "未分配部门";
            var managerName = "N/A";

            var pathParts = item.LocationPath?.Split('/');
            if (pathParts?.Length > 2 && int.TryParse(pathParts[2], out int locId))
            {
                if (departmentInfoMap.TryGetValue(locId, out var info))
                {
                    departmentName = info.DepartmentName;
                    managerName = info.ManagerName;
                }
            }
            
            // 假设您的详细数据DTO是 AssetSnapshotDto 或者类似的结构
            // 注意：您可能需要在DTO中增加 ManagerName 字段
            return new DetailedAssetDto { // 请替换为正确的DTO类名
                Id = item.AssetEntity.Id,
                AssetCode = item.AssetEntity.AssetCode,
                AssetName = item.AssetEntity.AssetName,
                AssetType = item.AssetEntity.AssetType.Name, // 假设有导航属性
                Department = departmentName, // 使用动态获取的部门名称
                Location = item.AssetEntity.Location.Name,
                Status = item.AssetEntity.Status.ToString(), // 或其他状态转换逻辑
                Price = item.AssetEntity.Price,
                CreatedAt = item.AssetEntity.CreatedAt,
                // ManagerName = managerName // 如果DTO有此字段
            };
        }).ToList();
        
        // 构建并返回最终的 DynamicStatisticsResultDto 对象
        var result = new DynamicStatisticsResultDto
        {
            AggregatedData = ..., // 保留聚合数据
            DetailedData = detailedData, // 使用新生成的详细数据
            Summary = ..., // 保留汇总信息
            Pagination = ... // 保留分页信息
        };

        return result;
    }
}
```

通过以上后端逻辑的修改，`detailedData` 中的每条资产记录都将包含根据其位置路径动态查询到的正确部门名称，从而解决了数据源头上的问题。
核心策略: 核心模块冻结，新功能模块化、现代化开发。
交付物: 包含问题诊断、后端修复指南，以及一个具备专业视觉和交互效果的、可直接预览的前端分析平台完整代码。

第一部分：代码审查与修复方案 (claude/ 目录)
核心结论：您新增的文件展现了非常先进的设计思路，但其实施路径与我们共同确定的架构规范存在偏差，无法直接运行。主要问题包括架构原则违规（如Api层直接访问数据库）、职责划分不清（“胖”控制器）和引入未定义的Zone概念。本方案将指导您如何将这些优秀的想法，以正确、健壮的方式融入我们的系统中。

1.1 待办事项：暂停集成 zone.js
问题: zone.js 引入了未经后端定义的全新Zone概念，与现有Location功能高度重叠，且包含硬编码数据。

行动计划: 暂停集成此文件。必须先召开需求评审会议，明确Zone与现有Location的关系。如果确认需要，必须先完成后端的设计与开发，然后才能进行前端实现。

1.2 修复后端代码 (.cs 文件)
目标：将所有后端代码修复至符合 Clean Architecture 规范，使其可编译、可运行、可维护。

文件 1: StatisticsController.cs (重构)
问题: 架构违规，控制器包含了本应属于应用层的业务逻辑。

修复方案: 彻底简化控制器，使其只负责接收HTTP请求，并通过MediatR将业务逻辑分发给Handler处理。

// File: ItAssetsSystem.Api/V2/Controllers/StatisticsController.cs
// Description: 新的 V2 统计API端点，严格遵循CQRS/MediatR模式。

using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Features.Statistics.Queries;
using ItAssetsSystem.Application.Models; 

namespace ItAssetsSystem.Api.V2.Controllers
{
    [ApiController]
    [Route("api/v2/[controller]")]
    public class StatisticsController : ControllerBase
    {
        private readonly ISender _sender;
        public StatisticsController(ISender sender) => _sender = sender;

        [HttpPost("query")]
        [ProducesResponseType(typeof(DynamicStatisticsResultDto), 200)]
        [ProducesResponseType(typeof(Error), 400)]
        public async Task<IActionResult> Query([FromBody] DynamicStatisticsQuery query)
        {
            var result = await _sender.Send(query);
            return result.IsSuccess 
                ? Ok(new { success = true, data = result.Value }) 
                : BadRequest(new { success = false, error = result.Error });
        }
    }
}

文件 2: DynamicStatisticsQueryHandler.cs (重构)
问题: 架构违规，直接依赖DbContext。

修复方案: Handler必须依赖于Domain层定义的仓储接口IStatisticsRepository，将数据访问逻辑完全解耦。

// File: ItAssetsSystem.Application/Features/Statistics/Queries/DynamicStatisticsQueryHandler.cs
// Description: 处理动态统计查询的 MediatR Handler，依赖于仓储接口。

using MediatR;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Domain.Interfaces;
using ItAssetsSystem.Application.Models;
using System.Threading;
using System.Threading.Tasks;

namespace ItAssetsSystem.Application.Features.Statistics.Queries
{
    public class DynamicStatisticsQueryHandler : IRequestHandler<DynamicStatisticsQuery, Result<DynamicStatisticsResultDto>>
    {
        private readonly IStatisticsRepository _statisticsRepository;
        private readonly ILogger<DynamicStatisticsQueryHandler> _logger;

        public DynamicStatisticsQueryHandler(IStatisticsRepository statisticsRepository, ILogger<DynamicStatisticsQueryHandler> logger)
        {
            _statisticsRepository = statisticsRepository;
            _logger = logger;
        }

        public async Task<Result<DynamicStatisticsResultDto>> Handle(DynamicStatisticsQuery request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("开始处理动态统计查询: {Query}", request);
            try
            {
                // 验证逻辑（更佳实践是使用 FluentValidation 和 MediatR Pipeline Behavior）
                var data = await _statisticsRepository.GetDynamicStatisticsAsync(request, cancellationToken);
                return Result.Success(data);
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "处理动态统计查询时发生异常。");
                return Result.Failure<DynamicStatisticsResultDto>(new Error("Query.Exception", "查询过程中发生严重错误。"));
            }
        }
    }
}

... (其他后端文件的修复方案与上一版相同，此处不再赘述，以保持文档的聚焦性) ...

第二部分：前端革命 - 交付“99分”的智能分析工作台 (最终预览版)
目标：提供一个视觉效果卓越、交互体验一流的单页面应用（SPA）原型，作为新分析模块的最终形态。此版本将完全按照您的设计参考图进行重构，支持深/浅色主题切换，并引入一个创新的**“工厂位置热力图”**来满足您的可视化需求。

操作: 使用以下完整代码创建您的 frontend/src/views/asset/AssetAnalyticsWorkbench.vue 文件。这是一个独立的、可直接在浏览器中运行的文件。

<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业级智能资产分析平台</title>
    <!-- Chosen Palette: Corporate Intelligence (Light/Dark variants) -->
    <!-- Application Structure Plan: The application is architected as a professional BI dashboard. The structure is a responsive grid layout that adapts from a single column on mobile to a complex multi-column layout on desktops. Key components are: 1) High-level KPI cards. 2) A primary combination chart for multi-dimensional analysis. 3) A secondary doughnut chart for status distribution. 4) An innovative "Location Heatmap" implemented as an interactive CSS grid to visualize asset density in factory locations. 5) A detailed data table. This structure enables a top-down data exploration workflow, providing a superior analytical experience with theme switching capabilities. -->
    <!-- Visualization & Content Choices:
        - Theme Switcher -> Goal: Enhance UX -> Viz: HTML Button -> Interaction: Toggles a class on the `<html>` element, triggering CSS variable changes. -> Justification: Meets the explicit user requirement for theme switching, enhancing visual appeal and user comfort. -> Library: Vanilla JS + Tailwind.
        - KPI Cards -> Goal: Inform -> Viz: Styled HTML cards with icons, hover effects. -> Interaction: Dynamic updates. -> Justification: Visually impactful summary of key metrics. -> Library: Vanilla JS + Tailwind.
        - Primary Chart -> Goal: Compare/Trends -> Viz: Chart.js Bar/Line Combo Chart -> Interaction: Clickable bars, toggleable datasets. -> Justification: Allows simultaneous comparison of two metrics (e.g., count and value). -> Library: Chart.js.
        - Status Chart -> Goal: Proportions -> Viz: Chart.js Doughnut Chart -> Interaction: Hover to see details. -> Justification: Best for showing parts of a whole. -> Library: Chart.js.
        - Location Heatmap -> Goal: Show Distribution -> Viz: Interactive CSS Grid -> Interaction: Hover to see details and get visual feedback. -> Justification: A creative, non-map solution to represent asset density in a factory layout, fulfilling the user's need for location visualization without geographical maps. -> Library: Vanilla JS + Tailwind.
        - Data Table -> Goal: Organize/Drill-Down -> Viz: Professional HTML Table -> Interaction: Sortable, paginated. -> Justification: The final step for inspecting raw data. -> Library: Vanilla JS + Tailwind.
    -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --bg-light: #f4f7fe; --text-light-primary: #1e293b; --text-light-secondary: #64748b; --card-light: #ffffff; --border-light: #e2e8f0;
            --bg-dark: #0f172a; --text-dark-primary: #e2e8f0; --text-dark-secondary: #94a3b8; --card-dark: #1e293b; --border-dark: #334155;
            --primary: #4f46e5; --secondary: #8b5cf6; --success: #10b981; --warning: #f59e0b; --danger: #ef4444; --info: #3b82f6;
        }
        body { font-family: 'Inter', sans-serif; transition: background-color 0.3s, color 0.3s; }
        .card { transition: background-color 0.3s, border-color 0.3s, box-shadow 0.3s, transform 0.3s; }
        .card:hover { transform: translateY(-5px); }
        .chart-container { position: relative; width: 100%; height: 300px; }
        
        html.light { background-color: var(--bg-light); color: var(--text-light-primary); }
        html.light .card { background-color: var(--card-light); border: 1px solid var(--border-light); box-shadow: 0 4px 6px -1px rgb(0 0 0/0.07); }
        html.light .card:hover { box-shadow: 0 10px 15px -3px rgb(0 0 0/0.1); }
        html.light .text-primary { color: var(--text-light-primary); }
        html.light .text-secondary { color: var(--text-light-secondary); }
        html.light .gradient-text { background: linear-gradient(90deg, #4f46e5, #3b82f6); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; }
        html.light .table-header { background-color: #f8fafc; }
        html.light .table-row:hover { background-color: #f1f5f9; }

        html.dark { background-color: var(--bg-dark); color: var(--text-dark-primary); }
        html.dark .card { background: rgba(30, 41, 59, 0.7); backdrop-filter: blur(10px); border: 1px solid var(--border-dark); box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2); }
        html.dark .card:hover { border-color: rgba(79, 70, 229, 0.3); box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4); }
        html.dark .text-primary { color: var(--text-dark-primary); }
        html.dark .text-secondary { color: var(--text-dark-secondary); }
        html.dark .gradient-text { background: linear-gradient(90deg, #a78bfa, #818cf8); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; }
        html.dark .table-header { background-color: #334155; }
        html.dark .table-row:hover { background-color: #27364d; }
    </style>
</head>
<body class="p-0 m-0">
    <div id="app-container" class="min-h-screen">
        <div class="max-w-[1800px] mx-auto p-4 sm:p-6 lg:p-8">
            <header class="flex flex-wrap justify-between items-center mb-8 gap-4">
                <div>
                    <h1 class="text-3xl font-bold gradient-text">智能资产分析平台</h1>
                    <p class="text-secondary mt-1">实时洞察企业资产分布、状态与价值</p>
                </div>
                <div class="flex items-center space-x-4">
                     <button id="theme-toggle" class="card p-2 rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white">
                        <i id="theme-icon" class="fas fa-sun text-xl text-amber-400"></i>
                    </button>
                </div>
            </header>
            
            <div id="kpi-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"></div>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                <div class="lg:col-span-2 card p-6 rounded-2xl">
                    <h2 class="text-xl font-bold text-primary mb-4">资产分布分析</h2>
                    <div class="chart-container"><canvas id="distributionChart"></canvas></div>
                </div>
                <div class="card p-6 rounded-2xl">
                    <h2 class="text-xl font-bold text-primary mb-4">资产状态分布</h2>
                    <div class="chart-container"><canvas id="statusChart"></canvas></div>
                </div>
            </div>

             <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                <div class="lg:col-span-1 card p-6 rounded-2xl">
                    <h2 class="text-xl font-bold text-primary mb-4">位置热力图 (资产数量)</h2>
                    <div id="location-heatmap" class="grid grid-cols-4 gap-2 h-full min-h-[300px]"></div>
                </div>
                <div class="lg:col-span-2 card p-6 rounded-2xl">
                     <h2 class="text-xl font-bold text-primary mb-4">资产明细</h2>
                     <div class="overflow-x-auto h-[350px]">
                        <table class="w-full text-sm text-left"><thead class="text-xs uppercase sticky top-0 bg-inherit table-header"><tr id="table-header-row"></tr></thead><tbody id="table-body"></tbody></table>
                     </div>
                </div>
            </div>
        </div>
    </div>
<script>
document.addEventListener('DOMContentLoaded', () => {
    const THEME = {
        LIGHT: { grid: '#e5e7eb', ticks: '#475569', legend: '#334155', textSecondary: '#64748b' },
        DARK: { grid: 'rgba(255, 255, 255, 0.1)', ticks: '#94a3b8', legend: '#cbd5e1', textSecondary: '#94a3b8' }
    };
    let currentTheme = localStorage.getItem('theme') || 'dark';
    const chartInstances = {};
    const mockData = {
        kpis: { totalAssets: 1248, totalValue: 2856.7, activeRate: 86.2, maintenanceCount: 74 },
        distribution: { labels: ['服务器', '电脑', '网络', '办公', '生产', '交通'], counts: [86, 542, 124, 287, 98, 24], values: [856, 423, 312, 187, 568, 268] },
        status: { labels: ['在用', '闲置', '维修中', '报废'], values: [1078, 152, 74, 60] },
        locations: { '数据中心': 95, '研发中心': 88, '销售一部': 75, '生产A线': 72, '行政办公区': 65, '仓库': 50, '会议中心': 45, '测试实验室': 30 },
        details: [ { code: 'AST-SVR-001', name: '数据库服务器', category: '服务器', department: '技术部', value: 85000 }, { code: 'AST-CMP-024', name: '设计工作站', category: '电脑设备', department: '研发部', value: 23500 }, { code: 'AST-PRT-008', name: '彩色激光打印机', category: '办公设备', department: '行政部', value: 8200 }, { code: 'AST-VHC-005', name: '公司商务车', category: '交通工具', department: '行政部', value: 268000 } ]
    };

    function setTheme(theme) {
        document.documentElement.className = theme;
        document.getElementById('theme-icon').className = `fas text-xl text-amber-400 ${theme === 'light' ? 'fa-moon' : 'fa-sun'}`;
        localStorage.setItem('theme', theme);
        renderAllCharts();
    }

    function renderKPIs() {
        const kpiGrid = document.getElementById('kpi-grid');
        const kpiData = [
            { label: '资产总数', value: mockData.kpis.totalAssets.toLocaleString(), icon: 'fa-boxes-stacked', color: 'var(--info)' },
            { label: '资产总值', value: `¥${mockData.kpis.totalValue.toLocaleString()}万`, icon: 'fa-coins', color: 'var(--success)' },
            { label: '在用率', value: `${mockData.kpis.activeRate}%`, icon: 'fa-check-circle', color: 'var(--success)' },
            { label: '待维修资产', value: mockData.kpis.maintenanceCount, icon: 'fa-tools', color: 'var(--warning)' }
        ];
        kpiGrid.innerHTML = kpiData.map(kpi => `
            <div class="card p-6 rounded-2xl">
                <div class="flex justify-between items-center">
                    <div>
                        <p class="text-sm font-medium text-secondary">${kpi.label}</p>
                        <h3 class="text-3xl font-bold mt-2 text-primary">${kpi.value}</h3>
                    </div>
                    <div class="w-16 h-16 rounded-xl flex items-center justify-center" style="background-color: ${kpi.color}20;">
                        <i class="fas ${kpi.icon} text-3xl" style="color: ${kpi.color};"></i>
                    </div>
                </div>
            </div>`).join('');
    }

    function createChart(id, type, data, options) {
        if (chartInstances[id]) chartInstances[id].destroy();
        const ctx = document.getElementById(id).getContext('2d');
        chartInstances[id] = new Chart(ctx, { type, data, options });
    }

    function renderDistributionChart() {
        const themeConfig = THEME[currentTheme];
        createChart('distributionChart', 'bar', {
            labels: mockData.distribution.labels,
            datasets: [
                { type: 'bar', label: '资产数量', data: mockData.distribution.counts, backgroundColor: 'rgba(79, 70, 229, 0.6)', borderColor: 'var(--primary)', borderWidth: 1, borderRadius: 6, yAxisID: 'y' },
                { type: 'line', label: '资产价值(万)', data: mockData.distribution.values, backgroundColor: 'var(--secondary)', borderColor: 'var(--secondary)', borderWidth: 2, tension: 0.4, pointRadius: 4, pointBackgroundColor: 'var(--secondary)', yAxisID: 'y1' }
            ]
        }, {
            responsive: true, maintainAspectRatio: false,
            plugins: { legend: { labels: { color: themeConfig.legend } } },
            scales: {
                x: { grid: { color: themeConfig.grid }, ticks: { color: themeConfig.ticks } },
                y: { type: 'linear', display: true, position: 'left', grid: { color: themeConfig.grid }, ticks: { color: themeConfig.ticks }, beginAtZero: true, title: { display: true, text: '数量', color: themeConfig.textSecondary } },
                y1: { type: 'linear', display: true, position: 'right', grid: { drawOnChartArea: false }, ticks: { color: themeConfig.ticks }, beginAtZero: true, title: { display: true, text: '价值(万)', color: themeConfig.textSecondary } }
            }
        });
    }
    
    function renderStatusChart() {
        const themeConfig = THEME[currentTheme];
        const cardBgColor = getComputedStyle(document.documentElement).getPropertyValue(`--card-${currentTheme}`).trim();
        createChart('statusChart', 'doughnut', {
            labels: mockData.status.labels,
            datasets: [{ data: mockData.status.values, backgroundColor: ['#10b981', '#64748b', '#f59e0b', '#ef4444'], borderColor: cardBgColor, borderWidth: 4 }]
        }, {
            responsive: true, maintainAspectRatio: false,
            plugins: { legend: { position: 'bottom', labels: { color: themeConfig.legend, boxWidth: 12, padding: 20 } } },
            cutout: '70%'
        });
    }

    function renderLocationHeatmap() {
        const heatmap = document.getElementById('location-heatmap');
        const maxAssets = Math.max(...Object.values(mockData.locations));
        heatmap.innerHTML = Object.entries(mockData.locations).sort((a,b) => b[1] - a[1]).map(([name, count]) => {
            const intensity = Math.round((count / maxAssets) * 7) + 2; // Tailwind color scale 200-900
            const bgColor = currentTheme === 'light' ? `rgba(79, 70, 229, ${0.1 * intensity})` : `rgba(79, 70, 229, ${0.2 + 0.08 * intensity})`;
            return `<div class="p-3 rounded-lg flex flex-col justify-center items-center text-center cursor-pointer transition hover:scale-105 hover:shadow-lg hover:shadow-indigo-500/30" style="background-color: ${bgColor};">
                <p class="text-sm font-semibold text-primary">${name}</p>
                <p class="text-2xl font-bold text-primary">${count}</p>
            </div>`;
        }).join('');
    }

    function renderTable() {
        const header = document.getElementById('table-header-row');
        const body = document.getElementById('table-body');
        const headers = ['资产编号', '名称', '类别', '部门', '价值(¥)'];
        header.innerHTML = headers.map(h => `<th class="px-4 py-3 text-left text-secondary">${h}</th>`).join('');
        body.innerHTML = mockData.details.map(d => `
            <tr class="table-row border-b" style="border-color: var(--border-dark);">
                <td class="px-4 py-3 font-medium text-info">${d.code}</td>
                <td class="px-4 py-3 text-primary">${d.name}</td>
                <td class="px-4 py-3 text-secondary">${d.category}</td>
                <td class="px-4 py-3 text-secondary">${d.department}</td>
                <td class="px-4 py-3 text-right font-semibold text-primary">${d.value.toLocaleString()}</td>
            </tr>
        `).join('');
    }

    function renderAllCharts() {
        renderDistributionChart();
        renderStatusChart();
    }

    function init() {
        document.getElementById('theme-toggle').addEventListener('click', () => {
            currentTheme = currentTheme === 'light' ? 'dark' : 'light';
            setTheme(currentTheme);
        });
        setTheme(currentTheme);
        renderKPIs();
        renderLocationHeatmap();
        renderTable();
    }

    init();
});
</script>
</body>
</html>
