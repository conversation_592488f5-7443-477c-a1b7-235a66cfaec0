<template>
  <div class="modern-factory-dashboard">
    <!-- Enhanced Header -->
    <header class="dashboard-header">
      <div class="header-container">
        <div class="brand-section">
          <div class="brand-icon">
            <el-icon size="32"><Cpu /></el-icon>
            <div class="icon-glow"></div>
          </div>
          <div class="brand-content">
            <h1 class="brand-title">智能制造监控系统</h1>
            <p class="brand-subtitle">实时工厂状态监控 • {{ factoryStore.stats.total }}个工位</p>
          </div>
          <div class="status-badge" :class="factoryStore.systemStatus">
            <div class="status-indicator"></div>
            <span>系统{{ factoryStore.systemStatusText }}</span>
          </div>
        </div>

        <div class="header-actions">
          <!-- Enhanced Search -->
          <div class="search-container">
            <el-input
              v-model="searchTerm"
              placeholder="搜索工位编号或设备名称..."
              class="smart-search"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon class="search-icon"><Search /></el-icon>
              </template>
            </el-input>
            
            <!-- Search Results Dropdown -->
            <div v-if="factoryStore.searchResults.length > 0 && searchTerm" class="search-results">
              <div 
                v-for="result in factoryStore.searchResults.slice(0, 5)" 
                :key="result.locationId"
                class="search-result-item"
                @click="selectSearchResult(result)"
              >
                <div class="result-status" :class="result.status"></div>
                <div class="result-content">
                  <span class="result-name">{{ result.locationName }}</span>
                  <span class="result-info">{{ result.efficiency }}% 效率</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="action-buttons">
            <!-- Filter with Badge -->
            <el-popover placement="bottom" trigger="click" width="350" popper-class="filter-popover">
              <template #reference>
                <el-button 
                  class="action-btn filter-btn" 
                  :class="{ 'active': factoryStore.hasActiveFilters }"
                >
                  <el-icon><Filter /></el-icon>
                  <span>筛选</span>
                  <el-badge 
                    v-if="factoryStore.activeFilterCount > 0" 
                    :value="factoryStore.activeFilterCount" 
                    class="filter-badge" 
                  />
                </el-button>
              </template>
              
              <div class="filter-content">
                <div class="filter-section">
                  <label>状态筛选</label>
                  <el-checkbox-group v-model="factoryStore.statusFilters">
                    <el-checkbox label="operational">正常运行</el-checkbox>
                    <el-checkbox label="warning">警告状态</el-checkbox>
                    <el-checkbox label="error">故障状态</el-checkbox>
                    <el-checkbox label="idle">空闲工位</el-checkbox>
                  </el-checkbox-group>
                </div>
                
                <div class="filter-section">
                  <label>部门</label>
                  <el-select v-model="factoryStore.filterForm.departmentId" placeholder="选择部门" clearable>
                    <el-option
                      v-for="dept in factoryStore.departments"
                      :key="dept.departmentId"
                      :label="dept.departmentName"
                      :value="dept.departmentId"
                    />
                  </el-select>
                </div>
                
                <div class="filter-actions">
                  <el-button size="small" @click="factoryStore.resetFilters()">重置</el-button>
                  <el-button size="small" type="primary" @click="applyFilters">应用</el-button>
                </div>
              </div>
            </el-popover>

            <!-- View Mode Toggle -->
            <el-button-group class="view-toggle">
              <el-button 
                :class="{ 'active': factoryStore.viewMode === 'layout' }"
                @click="factoryStore.setViewMode('layout')"
                class="view-btn"
                title="布局视图"
              >
                <el-icon><Grid /></el-icon>
              </el-button>
              <el-button 
                :class="{ 'active': factoryStore.viewMode === 'list' }"
                @click="factoryStore.setViewMode('list')"
                class="view-btn"
                title="列表视图"
              >
                <el-icon><List /></el-icon>
              </el-button>
            </el-button-group>

            <!-- Refresh Button -->
            <el-button 
              @click="refreshData"
              :loading="factoryStore.isLoading"
              class="action-btn refresh-btn"
              :class="{ 'refreshing': factoryStore.isLoading }"
              title="刷新数据"
            >
              <el-icon class="refresh-icon"><Refresh /></el-icon>
            </el-button>

            <!-- Fullscreen Button -->
            <el-button 
              @click="toggleFullScreen"
              class="action-btn fullscreen-btn"
              :class="{ 'active': factoryStore.isFullScreen }"
              :title="factoryStore.isFullScreen ? '退出全屏' : '全屏'"
            >
              <el-icon>
                <FullScreen v-if="!factoryStore.isFullScreen" />
                <Close v-else />
              </el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="dashboard-main">
      <div class="dashboard-container">
        <!-- Enhanced Stats Panel -->
        <div class="stats-panel">
          <!-- Real-time Overview -->
          <div class="stats-card overview-card">
            <div class="card-header">
              <h3 class="card-title">实时概览</h3>
              <div class="update-indicator">
                <div class="pulse-dot"></div>
                <span class="update-time">{{ factoryStore.formattedTime }}</span>
              </div>
            </div>
            
            <div class="stats-grid">
              <div 
                v-for="(status, key) in statusCards" 
                :key="key"
                class="modern-stat-card" 
                :class="key"
                @click="factoryStore.filterByStatus(key)"
              >
                <div class="stat-visual">
                  <div class="stat-icon-container" :class="key">
                    <el-icon class="stat-icon">
                      <component :is="status.icon" />
                    </el-icon>
                    <div class="icon-glow" :class="key"></div>
                  </div>
                  <div class="stat-progress">
                    <div 
                      class="progress-bar" 
                      :class="key"
                      :style="{ width: getStatusPercent(key) + '%' }"
                    ></div>
                  </div>
                </div>
                <div class="stat-data">
                  <div class="stat-value">{{ factoryStore.stats[key] }}</div>
                  <div class="stat-label">{{ status.label }}</div>
                  <div v-if="key === 'operational'" class="stat-percent">
                    {{ factoryStore.stats.operationalPercent }}%
                  </div>
                </div>
              </div>
            </div>

            <!-- System Metrics -->
            <div class="system-metrics">
              <div class="metric-item">
                <div class="metric-value">{{ factoryStore.avgEfficiency }}%</div>
                <div class="metric-label">平均效率</div>
              </div>
              <div class="metric-item">
                <div class="metric-value">{{ factoryStore.totalAssets }}</div>
                <div class="metric-label">总设备数</div>
              </div>
            </div>
          </div>

          <!-- Priority Workstations -->
          <div class="stats-card priority-card">
            <div class="card-header">
              <h3 class="card-title">重点监控</h3>
              <el-badge :value="factoryStore.priorityWorkstations.length" class="priority-badge" />
            </div>
            
            <div class="priority-list">
              <div 
                v-for="workstation in factoryStore.priorityWorkstations.slice(0, 5)" 
                :key="workstation.locationId"
                class="priority-item"
                @click="selectWorkstation(workstation.locationId)"
              >
                <div class="priority-status" :class="workstation.status"></div>
                <div class="priority-content">
                  <div class="priority-name">{{ workstation.locationName }}</div>
                  <div class="priority-info">
                    {{ workstation.efficiency }}% 效率
                    <span v-if="workstation.faultCount > 0" class="fault-count">
                      • {{ workstation.faultCount }} 故障
                    </span>
                  </div>
                </div>
                <el-icon class="priority-arrow"><ArrowRight /></el-icon>
              </div>
            </div>
          </div>

          <!-- Zone Statistics -->
          <div class="stats-card zone-card">
            <div class="card-header">
              <h3 class="card-title">区域统计</h3>
            </div>
            
            <div class="zone-list">
              <div 
                v-for="(zone, zoneId) in factoryStore.zoneStats" 
                :key="zoneId"
                class="zone-item"
              >
                <div class="zone-info">
                  <div class="zone-name">{{ getZoneName(zoneId) }}</div>
                  <div class="zone-metrics">
                    <span class="zone-total">{{ zone.total }}个工位</span>
                    <span class="zone-efficiency">{{ zone.efficiency }}%效率</span>
                  </div>
                </div>
                <div class="zone-status">
                  <div class="status-indicators">
                    <span class="status-dot operational" v-if="zone.operational > 0">{{ zone.operational }}</span>
                    <span class="status-dot warning" v-if="zone.warning > 0">{{ zone.warning }}</span>
                    <span class="status-dot error" v-if="zone.error > 0">{{ zone.error }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Factory Layout -->
        <div class="factory-layout">
          <div class="layout-header">
            <div class="layout-info">
              <div class="info-section">
                <span class="info-label">显示工位</span>
                <span class="info-value">{{ factoryStore.displayedCount }} / {{ factoryStore.stats.total }}</span>
              </div>
              <div class="status-legend">
                <div class="legend-item" v-for="(status, key) in statusCards" :key="key">
                  <div class="legend-dot" :class="key"></div>
                  <span>{{ status.label }}</span>
                </div>
              </div>
            </div>
            
            <div class="layout-controls">
              <el-button-group class="zoom-controls">
                <el-button 
                  size="small" 
                  @click="factoryStore.zoomOut()" 
                  :disabled="factoryStore.zoomLevel <= 0.5"
                  title="缩小"
                >
                  <el-icon><ZoomOut /></el-icon>
                </el-button>
                <el-button size="small" @click="factoryStore.resetZoom()" title="重置缩放">
                  {{ Math.round(factoryStore.zoomLevel * 100) }}%
                </el-button>
                <el-button 
                  size="small" 
                  @click="factoryStore.zoomIn()" 
                  :disabled="factoryStore.zoomLevel >= 2"
                  title="放大"
                >
                  <el-icon><ZoomIn /></el-icon>
                </el-button>
              </el-button-group>
            </div>
          </div>

          <!-- Layout View -->
          <div v-if="factoryStore.viewMode === 'layout'" class="factory-floor" ref="factoryFloor">
            <div class="factory-container" :style="{ transform: `scale(${factoryStore.zoomLevel})` }">
              <!-- Background Grid -->
              <svg class="factory-grid" viewBox="0 0 1100 600" preserveAspectRatio="xMidYMid meet">
                <defs>
                  <pattern id="mainGrid" width="40" height="40" patternUnits="userSpaceOnUse">
                    <path d="M 40 0 L 0 0 0 40" fill="none" stroke="rgba(59, 130, 246, 0.1)" stroke-width="1"/>
                  </pattern>
                  <pattern id="subGrid" width="10" height="10" patternUnits="userSpaceOnUse">
                    <path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(59, 130, 246, 0.05)" stroke-width="0.5"/>
                  </pattern>
                </defs>
                
                <rect width="100%" height="100%" fill="url(#subGrid)" />
                <rect width="100%" height="100%" fill="url(#mainGrid)" />
                
                <!-- Zone boundaries -->
                <g stroke-width="2" fill="none">
                  <rect x="40" y="40" width="250" height="100" stroke="#00bcd4" stroke-dasharray="5,5" opacity="0.6" />
                  <text x="45" y="35" fill="#00bcd4" font-size="12" font-weight="bold">区域1-生产线A</text>
                  
                  <rect x="330" y="40" width="250" height="100" stroke="#00bcd4" stroke-dasharray="5,5" opacity="0.6" />
                  <text x="335" y="35" fill="#00bcd4" font-size="12" font-weight="bold">区域2-装配线B</text>
                  
                  <rect x="600" y="40" width="200" height="100" stroke="#2196f3" stroke-dasharray="5,5" opacity="0.6" />
                  <text x="605" y="35" fill="#2196f3" font-size="12" font-weight="bold">区域3-质检区</text>
                  
                  <rect x="40" y="180" width="300" height="120" stroke="#9e9e9e" stroke-dasharray="5,5" opacity="0.6" />
                  <text x="45" y="175" fill="#9e9e9e" font-size="12" font-weight="bold">区域4-主生产线</text>
                  
                  <rect x="380" y="350" width="400" height="120" stroke="#ffc107" stroke-dasharray="5,5" opacity="0.6" />
                  <text x="385" y="345" fill="#ffc107" font-size="12" font-weight="bold">区域5-包装线</text>
                </g>
              </svg>

              <!-- Workstation Cells -->
              <WorkstationCell
                v-for="location in factoryStore.filteredLocations"
                :key="location.locationId"
                :workstation="location"
                :is-selected="factoryStore.selectedLocationId === location.locationId"
                :is-hovered="factoryStore.hoveredLocationId === location.locationId"
                :cell-size="cellSize"
                @click="selectWorkstation"
                @mouseenter="factoryStore.setHoveredLocation"
                @mouseleave="factoryStore.clearHoveredLocation"
              />
            </div>
          </div>

          <!-- List View -->
          <div v-else class="factory-list">
            <el-table 
              :data="factoryStore.filteredLocations" 
              height="100%"
              @row-click="handleRowClick"
              highlight-current-row
            >
              <el-table-column prop="locationCode" label="工位编号" width="100" />
              <el-table-column prop="locationName" label="工位名称" min-width="120" />
              <el-table-column prop="departmentName" label="所属部门" min-width="120" />
              <el-table-column prop="efficiency" label="效率" width="80" align="center">
                <template #default="{ row }">
                  <span :class="getEfficiencyClass(row.efficiency)">{{ row.efficiency }}%</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="factoryStore.getStatusTagType(row.status)" size="small">
                    {{ factoryStore.getStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="assetCount" label="设备数" width="80" align="center" />
              <el-table-column prop="taskCount" label="任务数" width="80" align="center" />
              <el-table-column prop="faultCount" label="故障数" width="80" align="center">
                <template #default="{ row }">
                  <span v-if="row.faultCount > 0" class="fault-count-text">{{ row.faultCount }}</span>
                  <span v-else class="no-fault">0</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center">
                <template #default="{ row }">
                  <el-button size="small" @click.stop="selectWorkstation(row.locationId)">
                    详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </main>

    <!-- Workstation Details Drawer -->
    <el-drawer
      v-model="showDetailsDrawer"
      title="工位详情"
      size="450px"
      direction="rtl"
    >
      <WorkstationDetails 
        v-if="factoryStore.selectedLocation"
        :workstation="factoryStore.selectedLocation"
        :details="factoryStore.selectedLocationDetails"
      />
    </el-drawer>

    <!-- Footer -->
    <footer class="dashboard-footer">
      <div class="footer-content">
        <div class="footer-info">
          <span class="system-name">智能制造监控系统</span>
          <span class="version">v2.5.1</span>
          <span class="update-time">最后更新: {{ factoryStore.formattedTime }}</span>
        </div>
        
        <div class="footer-stats">
          <div class="stat-item">
            <div class="stat-dot operational"></div>
            <span>运行正常 ({{ factoryStore.stats.operational }})</span>
          </div>
          <div class="stat-item">
            <div class="stat-dot warning"></div>
            <span>警告 ({{ factoryStore.stats.warning }})</span>
          </div>
          <div class="stat-item">
            <div class="stat-dot error"></div>
            <span>故障 ({{ factoryStore.stats.error }})</span>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Cpu, Search, Filter, Refresh, FullScreen, Close,
  Check, Warning, Close as CloseIcon, Setting, ArrowRight,
  ZoomIn, ZoomOut, Grid, List
} from '@element-plus/icons-vue'
import { useFactoryStore } from '@/stores/modules/factory'
import WorkstationCell from '@/components/Tasks/WorkstationCell.vue'
import WorkstationDetails from './components/WorkstationDetails.vue'

// Store
const factoryStore = useFactoryStore()

// Reactive data
const searchTerm = ref('')
const showDetailsDrawer = ref(false)
const factoryFloor = ref(null)

// Status cards configuration
const statusCards = {
  operational: { icon: Check, label: '运行正常' },
  warning: { icon: Warning, label: '警告状态' },
  error: { icon: CloseIcon, label: '故障状态' },
  idle: { icon: Setting, label: '空闲工位' }
}

// Cell size configuration
const cellSize = ref({ width: 32, height: 28 })

// Computed properties
const getStatusPercent = (status) => {
  const total = factoryStore.stats.total
  return total > 0 ? (factoryStore.stats[status] / total * 100) : 0
}

const getZoneName = (zoneId) => {
  const zoneNames = {
    zone1: '区域1',
    zone2: '区域2', 
    zone3: '区域3',
    zone4: '区域4',
    zone5: '区域5',
    zone6: '区域6',
    zone7: '区域7'
  }
  return zoneNames[zoneId] || '未知区域'
}

const getEfficiencyClass = (efficiency) => {
  if (efficiency >= 80) return 'efficiency-high'
  if (efficiency >= 60) return 'efficiency-medium'
  return 'efficiency-low'
}

// Event handlers
const handleSearch = (value) => {
  factoryStore.searchWorkstations(value)
}

const selectSearchResult = (result) => {
  factoryStore.selectSearchResult(result)
  selectWorkstation(result.locationId)
}

const selectWorkstation = (locationId) => {
  factoryStore.selectLocation(locationId)
  showDetailsDrawer.value = true
}

const handleRowClick = (row) => {
  selectWorkstation(row.locationId)
}

const refreshData = async () => {
  await factoryStore.refreshData()
}

const toggleFullScreen = () => {
  factoryStore.toggleFullScreen()
}

const applyFilters = () => {
  ElMessage.success('筛选条件已应用')
}

// Watch for fullscreen changes
const handleFullScreenChange = () => {
  factoryStore.setFullScreen(!!document.fullscreenElement)
}

// Responsive cell size
const updateCellSize = () => {
  const width = window.innerWidth
  if (width < 768) {
    cellSize.value = { width: 24, height: 20 }
  } else if (width < 1200) {
    cellSize.value = { width: 28, height: 24 }
  } else {
    cellSize.value = { width: 32, height: 28 }
  }
}

// Watch for selected location changes
watch(() => factoryStore.selectedLocationId, (newId) => {
  showDetailsDrawer.value = !!newId
})

// Lifecycle
onMounted(async () => {
  await factoryStore.initializeFactory()
  
  document.addEventListener('fullscreenchange', handleFullScreenChange)
  window.addEventListener('resize', updateCellSize)
  updateCellSize()
})

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullScreenChange)
  window.addEventListener('resize', updateCellSize)
  factoryStore.destroy()
})
</script>

<style scoped>
/* Import the enhanced styles from our previous dashboard */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --color-industrial-900: #0c1a25;
  --color-industrial-800: #122738;
  --color-industrial-700: #1a3650;
  --color-industrial-600: #23527c;
  --color-industrial-500: #2e6da4;
  --color-industrial-400: #3d8fd1;
  --color-industrial-300: #66b0ff;
  --color-status-operational: #10b981;
  --color-status-warning: #f59e0b;
  --color-status-error: #ef4444;
  --color-status-idle: #3b82f6;
}

.modern-factory-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--color-industrial-900) 0%, var(--color-industrial-800) 100%);
  color: #e2e8f0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  position: relative;
  overflow-x: hidden;
}

/* Background pattern */
.modern-factory-dashboard::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 1px 1px, rgba(66, 153, 225, 0.05) 1px, transparent 0),
    radial-gradient(circle at 1px 1px, rgba(66, 153, 225, 0.03) 1px, transparent 0);
  background-size: 40px 40px, 20px 20px;
  background-position: 0 0, 20px 20px;
  pointer-events: none;
  z-index: -1;
}

/* Header Styles */
.dashboard-header {
  padding: 1.25rem 1.5rem;
  background: rgba(18, 39, 56, 0.95);
  backdrop-filter: blur(16px);
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.header-container {
  max-width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
}

.brand-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.brand-icon {
  position: relative;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--color-industrial-500), var(--color-industrial-400));
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 25px rgba(45, 122, 164, 0.3);
}

.icon-glow {
  position: absolute;
  inset: -2px;
  background: linear-gradient(135deg, var(--color-industrial-400), var(--color-industrial-300));
  border-radius: 18px;
  z-index: -1;
  opacity: 0.5;
  filter: blur(4px);
}

.brand-content {
  flex: 1;
}

.brand-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #f1f5f9, #66b0ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.025em;
}

.brand-subtitle {
  font-size: 0.875rem;
  color: #94a3b8;
  margin: 0.25rem 0 0 0;
  font-weight: 400;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border-radius: 12px;
  padding: 0.75rem 1rem;
  border: 1px solid;
  background: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.3);
}

.status-badge.warning {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
}

.status-badge.offline {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color-status-operational);
  animation: pulse 2s infinite;
}

.status-badge.warning .status-indicator {
  background: var(--color-status-warning);
}

.status-badge.offline .status-indicator {
  background: var(--color-status-error);
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-container {
  position: relative;
}

.smart-search {
  width: 280px;
  transition: all 0.3s ease;
}

.smart-search:focus-within {
  width: 320px;
}

.search-icon {
  color: var(--color-industrial-400);
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(18, 39, 56, 0.95);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  margin-top: 0.5rem;
  z-index: 50;
  max-height: 200px;
  overflow-y: auto;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.search-result-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

.search-result-item:hover {
  background: rgba(59, 130, 246, 0.1);
}

.result-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.result-status.operational { background: var(--color-status-operational); }
.result-status.warning { background: var(--color-status-warning); }
.result-status.error { background: var(--color-status-error); }
.result-status.idle { background: var(--color-status-idle); }

.result-content {
  flex: 1;
}

.result-name {
  font-weight: 500;
  color: #e2e8f0;
  font-size: 0.875rem;
  display: block;
}

.result-info {
  font-size: 0.75rem;
  color: #94a3b8;
  margin-top: 0.125rem;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.action-btn {
  min-width: 44px;
  height: 44px;
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.3);
  background: rgba(18, 39, 56, 0.6);
  color: #94a3b8;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0 1rem;
  font-weight: 500;
}

.action-btn:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.5);
  color: #e2e8f0;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.action-btn.active {
  background: var(--color-industrial-500);
  border-color: var(--color-industrial-400);
  color: white;
  box-shadow: 0 4px 15px rgba(45, 122, 164, 0.3);
}

.filter-btn.active {
  background: var(--color-status-warning);
  border-color: rgba(245, 158, 11, 0.5);
  color: white;
}

.view-toggle {
  display: flex;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.view-btn {
  border: none;
  border-radius: 0;
  background: rgba(18, 39, 56, 0.6);
  color: #94a3b8;
  min-width: 44px;
  height: 44px;
  transition: all 0.3s ease;
}

.view-btn:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #e2e8f0;
}

.view-btn.active {
  background: var(--color-industrial-500);
  color: white;
}

/* Main Content */
.dashboard-main {
  flex: 1;
  padding: 1.5rem;
  max-width: 100%;
}

.dashboard-container {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 2rem;
  height: calc(100vh - 180px);
  max-width: 100%;
}

/* Stats Panel */
.stats-panel {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  height: fit-content;
}

.stats-card {
  background: rgba(18, 39, 56, 0.8);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.stats-card:hover {
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  color: #e2e8f0;
}

.update-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pulse-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--color-status-operational);
  animation: pulse 2s infinite;
}

.update-time {
  font-size: 0.75rem;
  color: #94a3b8;
  font-weight: 400;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.modern-stat-card {
  background: rgba(26, 54, 80, 0.6);
  border-radius: 16px;
  padding: 1.25rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid rgba(59, 130, 246, 0.2);
  position: relative;
  overflow: hidden;
}

.modern-stat-card:hover {
  transform: translateY(-4px) scale(1.02);
  border-color: rgba(59, 130, 246, 0.4);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.4);
}

.modern-stat-card.operational:hover {
  border-color: rgba(16, 185, 129, 0.4);
  box-shadow: 0 12px 32px rgba(16, 185, 129, 0.2);
}

.modern-stat-card.warning:hover {
  border-color: rgba(245, 158, 11, 0.4);
  box-shadow: 0 12px 32px rgba(245, 158, 11, 0.2);
}

.modern-stat-card.error:hover {
  border-color: rgba(239, 68, 68, 0.4);
  box-shadow: 0 12px 32px rgba(239, 68, 68, 0.2);
}

.stat-visual {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.stat-icon-container {
  position: relative;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(26, 54, 80, 0.8);
}

.stat-icon-container.operational {
  background: rgba(16, 185, 129, 0.2);
  color: var(--color-status-operational);
}

.stat-icon-container.warning {
  background: rgba(245, 158, 11, 0.2);
  color: var(--color-status-warning);
}

.stat-icon-container.error {
  background: rgba(239, 68, 68, 0.2);
  color: var(--color-status-error);
}

.stat-icon-container.idle {
  background: rgba(59, 130, 246, 0.2);
  color: var(--color-status-idle);
}

.stat-icon {
  font-size: 1.25rem;
  position: relative;
  z-index: 2;
}

.icon-glow {
  position: absolute;
  inset: -2px;
  border-radius: 14px;
  z-index: 1;
  opacity: 0.3;
  filter: blur(4px);
}

.icon-glow.operational {
  background: var(--color-status-operational);
}

.icon-glow.warning {
  background: var(--color-status-warning);
}

.icon-glow.error {
  background: var(--color-status-error);
}

.icon-glow.idle {
  background: var(--color-status-idle);
}

.stat-progress {
  flex: 1;
  height: 4px;
  background: rgba(26, 54, 80, 0.8);
  border-radius: 2px;
  margin-left: 1rem;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: var(--color-status-operational);
  border-radius: 2px;
  transition: width 0.5s ease;
}

.progress-bar.warning {
  background: var(--color-status-warning);
}

.progress-bar.error {
  background: var(--color-status-error);
}

.progress-bar.idle {
  background: var(--color-status-idle);
}

.stat-data {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  line-height: 1;
  color: #e2e8f0;
}

.stat-label {
  font-size: 0.75rem;
  color: #94a3b8;
  font-weight: 400;
  margin: 0;
}

.stat-percent {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-status-operational);
  margin: 0;
}

/* System Metrics */
.system-metrics {
  display: flex;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(59, 130, 246, 0.3);
}

.metric-item {
  flex: 1;
  text-align: center;
}

.metric-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--color-industrial-400);
}

.metric-label {
  font-size: 0.75rem;
  color: #94a3b8;
  margin-top: 0.25rem;
}

/* Priority List */
.priority-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  max-height: 200px;
  overflow-y: auto;
}

.priority-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(26, 54, 80, 0.6);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.priority-item:hover {
  background: rgba(59, 130, 246, 0.1);
  transform: translateX(4px);
  border-color: rgba(59, 130, 246, 0.4);
}

.priority-status {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.priority-status.operational { background: var(--color-status-operational); }
.priority-status.warning { background: var(--color-status-warning); }
.priority-status.error { background: var(--color-status-error); }
.priority-status.idle { background: var(--color-status-idle); }

.priority-content {
  flex: 1;
}

.priority-name {
  font-weight: 600;
  font-size: 0.875rem;
  color: #e2e8f0;
}

.priority-info {
  font-size: 0.75rem;
  color: #94a3b8;
  margin-top: 0.125rem;
}

.fault-count {
  color: var(--color-status-error);
  font-weight: 500;
}

/* Zone Statistics */
.zone-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.zone-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(26, 54, 80, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.zone-info {
  flex: 1;
}

.zone-name {
  font-weight: 600;
  font-size: 0.875rem;
  color: #e2e8f0;
}

.zone-metrics {
  font-size: 0.75rem;
  color: #94a3b8;
  margin-top: 0.125rem;
}

.zone-total {
  margin-right: 1rem;
}

.zone-status {
  display: flex;
  align-items: center;
}

.status-indicators {
  display: flex;
  gap: 0.25rem;
}

.status-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.625rem;
  font-weight: 600;
  color: white;
}

.status-dot.operational {
  background: var(--color-status-operational);
}

.status-dot.warning {
  background: var(--color-status-warning);
}

.status-dot.error {
  background: var(--color-status-error);
}

/* Factory Layout */
.factory-layout {
  background: rgba(18, 39, 56, 0.8);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 20px;
  padding: 1.5rem;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

.layout-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
}

.layout-info {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.info-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-label {
  font-size: 0.875rem;
  color: #94a3b8;
  font-weight: 400;
}

.info-value {
  font-size: 1rem;
  font-weight: 600;
  color: #e2e8f0;
}

.status-legend {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: #94a3b8;
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.legend-dot.operational { background: var(--color-status-operational); }
.legend-dot.warning { background: var(--color-status-warning); }
.legend-dot.error { background: var(--color-status-error); }
.legend-dot.idle { background: var(--color-status-idle); }

.layout-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.zoom-controls {
  display: flex;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.zoom-controls .el-button {
  border: none;
  border-radius: 0;
  background: rgba(18, 39, 56, 0.6);
  color: #94a3b8;
  min-width: 40px;
  height: 36px;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.zoom-controls .el-button:hover:not(:disabled) {
  background: rgba(59, 130, 246, 0.1);
  color: #e2e8f0;
}

.zoom-controls .el-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Factory Floor */
.factory-floor {
  position: relative;
  flex: 1;
  overflow: auto;
  background: radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.08) 0%, rgba(12, 26, 37, 0.9) 70%);
  border-radius: 16px;
  border: 2px solid rgba(59, 130, 246, 0.2);
}

.factory-container {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
  transform-origin: top left;
  position: relative;
  min-height: 600px;
}

.factory-grid {
  width: 100%;
  height: 100%;
  pointer-events: none;
  opacity: 0.8;
}

/* Factory List */
.factory-list {
  height: calc(100% - 80px);
}

.efficiency-high {
  color: var(--color-status-operational);
  font-weight: 600;
}

.efficiency-medium {
  color: var(--color-status-warning);
  font-weight: 600;
}

.efficiency-low {
  color: var(--color-status-error);
  font-weight: 600;
}

.fault-count-text {
  color: var(--color-status-error);
  font-weight: 600;
}

.no-fault {
  color: #94a3b8;
}

/* Footer */
.dashboard-footer {
  padding: 1rem 1.5rem;
  background: rgba(18, 39, 56, 0.8);
  backdrop-filter: blur(16px);
  border-top: 1px solid rgba(59, 130, 246, 0.3);
}

.footer-content {
  max-width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.footer-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #94a3b8;
}

.system-name {
  color: #e2e8f0;
  font-weight: 500;
}

.version {
  color: var(--color-industrial-400);
  font-weight: 500;
}

.footer-stats {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #94a3b8;
}

.stat-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

/* Filter Popover */
:deep(.filter-popover) {
  background: rgba(18, 39, 56, 0.95);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

.filter-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-section label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #e2e8f0;
}

.filter-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
  padding-top: 1rem;
  border-top: 1px solid rgba(59, 130, 246, 0.2);
}

/* Animations */
@keyframes pulse {
  0%, 100% { 
    opacity: 1;
    transform: scale(1);
  }
  50% { 
    opacity: 0.7;
    transform: scale(1.05);
  }
}

/* Responsive Design */
@media (max-width: 1400px) {
  .dashboard-container {
    grid-template-columns: 320px 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}

@media (max-width: 1200px) {
  .dashboard-container {
    grid-template-columns: 280px 1fr;
    gap: 1.5rem;
  }
  
  .brand-title {
    font-size: 1.5rem;
  }
  
  .smart-search {
    width: 220px;
  }
  
  .smart-search:focus-within {
    width: 260px;
  }
}

@media (max-width: 768px) {
  .dashboard-main {
    padding: 1rem;
  }
  
  .dashboard-container {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
    gap: 1rem;
  }
  
  .stats-panel {
    order: 2;
  }
  
  .factory-layout {
    order: 1;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .header-container {
    flex-direction: column;
    gap: 1rem;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .smart-search {
    width: 100%;
    max-width: 280px;
  }
  
  .layout-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .layout-info {
    justify-content: space-between;
  }
}

/* Element Plus Customization */
:deep(.el-input__wrapper) {
  background: rgba(18, 39, 56, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgba(59, 130, 246, 0.5);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: var(--color-industrial-400);
  box-shadow: 0 0 15px rgba(61, 143, 209, 0.3);
}

:deep(.el-input__inner) {
  color: #e2e8f0;
  background: transparent;
}

:deep(.el-input__inner::placeholder) {
  color: #94a3b8;
}

:deep(.el-button) {
  border-radius: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

:deep(.el-button--primary) {
  background: var(--color-industrial-500);
  border-color: var(--color-industrial-400);
}

:deep(.el-button--primary:hover) {
  background: var(--color-industrial-400);
  border-color: var(--color-industrial-300);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(45, 122, 164, 0.3);
}

:deep(.el-checkbox) {
  color: #e2e8f0;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background: var(--color-industrial-500);
  border-color: var(--color-industrial-400);
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(18, 39, 56, 0.8);
}

:deep(.el-table) {
  background: transparent;
}

:deep(.el-table th) {
  background: rgba(26, 54, 80, 0.8);
  color: #e2e8f0;
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
}

:deep(.el-table td) {
  background: rgba(18, 39, 56, 0.6);
  color: #e2e8f0;
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

:deep(.el-table__row:hover) {
  background: rgba(59, 130, 246, 0.1);
}

:deep(.el-drawer) {
  background: rgba(18, 39, 56, 0.95);
  backdrop-filter: blur(16px);
}

:deep(.el-drawer__header) {
  background: rgba(26, 54, 80, 0.8);
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
  color: #e2e8f0;
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(18, 39, 56, 0.6);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-industrial-500);
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-industrial-400);
}
</style>