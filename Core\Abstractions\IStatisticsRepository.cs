using ItAssetsSystem.Application.Features.Statistics.Dtos;
using ItAssetsSystem.Application.Features.Statistics.Queries;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace ItAssetsSystem.Core.Abstractions
{
    /// <summary>
    /// Repository interface for statistics operations
    /// </summary>
    public interface IStatisticsRepository
    {
        /// <summary>
        /// Executes a dynamic statistics query with configurable dimensions and metrics
        /// </summary>
        /// <param name="query">The dynamic query containing dimensions, metrics, and filters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Aggregated statistics result</returns>
        Task<DynamicStatisticsResultDto> GetDynamicStatisticsAsync(
            DynamicStatisticsQuery query, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets asset snapshot data for historical trends
        /// </summary>
        /// <param name="startDate">Start date for the snapshot range</param>
        /// <param name="endDate">End date for the snapshot range</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Collection of asset snapshots</returns>
        Task<IEnumerable<AssetSnapshotDto>> GetAssetSnapshotsAsync(
            DateTime? startDate = null,
            DateTime? endDate = null,
            CancellationToken cancellationToken = default);
    }
}