<template>
  <div class="smart-factory-dashboard">
    <!-- Enhanced Header -->
    <header class="enhanced-header">
      <div class="header-container">
        <div class="header-brand">
          <div class="brand-icon">
            <el-icon size="32"><Cpu /></el-icon>
            <div class="icon-glow"></div>
          </div>
          <div class="brand-content">
            <h1 class="brand-title">智能制造监控系统</h1>
            <p class="brand-subtitle">实时工厂状态监控 • {{ stats.total }}个工位</p>
          </div>
          <div class="status-badge">
            <div class="status-indicator" :class="systemStatus"></div>
            <span class="status-text">系统{{ systemStatusText }}</span>
          </div>
        </div>
        
        <div class="header-actions">
          <!-- Enhanced Search -->
          <div class="search-container">
            <el-input
              v-model="searchTerm"
              placeholder="搜索工位编号或设备名称..."
              class="smart-search"
              clearable
            >
              <template #prefix>
                <el-icon class="search-icon"><Search /></el-icon>
              </template>
            </el-input>
            <div v-if="searchResults.length > 0 && searchTerm" class="search-results">
              <div 
                v-for="result in searchResults.slice(0, 5)" 
                :key="result.locationId"
                class="search-result-item"
                @click="selectSearchResult(result)"
              >
                <div class="result-status" :class="result.status"></div>
                <div class="result-content">
                  <span class="result-name">{{ result.locationName }}</span>
                  <span class="result-info">{{ result.efficiency }}% 效率</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Enhanced Filter Popover -->
          <el-popover placement="bottom" trigger="click" width="350" popper-class="filter-popover">
            <template #reference>
              <el-button class="action-btn filter-btn" :class="{ 'active': hasActiveFilters }">
                <el-icon><Filter /></el-icon>
                <span>筛选</span>
                <el-badge v-if="activeFilterCount > 0" :value="activeFilterCount" class="filter-badge" />
              </el-button>
            </template>
            <div class="space-y-3">
              <div>
                <label class="block text-sm font-medium mb-1">部门</label>
                <el-select v-model="filterForm.departmentId" placeholder="选择部门" clearable size="small" style="width: 100%">
                  <el-option
                    v-for="dept in departments"
                    :key="dept.departmentId"
                    :label="dept.departmentName"
                    :value="dept.departmentId"
                  />
                </el-select>
              </div>
              <div>
                <label class="block text-sm font-medium mb-1">位置类型</label>
                <el-select v-model="filterForm.locationType" placeholder="选择类型" clearable size="small" style="width: 100%">
                  <el-option label="工厂" :value="1" />
                  <el-option label="车间" :value="2" />
                  <el-option label="工序" :value="3" />
                  <el-option label="工位" :value="4" />
                </el-select>
              </div>
              <div>
                <el-checkbox v-model="filterForm.onlyWithAssets">仅显示有设备的位置</el-checkbox>
              </div>
              <div class="flex gap-2">
                <el-button size="small" @click="applyFilter" type="primary">应用</el-button>
                <el-button size="small" @click="resetFilter">重置</el-button>
              </div>
            </div>
          </el-popover>
          
          <!-- Action Buttons -->
          <div class="action-buttons">
            <!-- Refresh with animation -->
            <el-button 
              @click="handleRefresh"
              :loading="refreshing"
              class="action-btn refresh-btn"
              :class="{ 'refreshing': refreshing }"
              title="刷新数据"
            >
              <el-icon class="refresh-icon"><Refresh /></el-icon>
            </el-button>
            
            <!-- View Mode Toggle -->
            <el-button-group class="view-toggle">
              <el-button 
                :class="{ 'active': viewMode === 'layout' }"
                @click="viewMode = 'layout'"
                class="view-btn"
                title="布局视图"
              >
                <el-icon><Grid /></el-icon>
              </el-button>
              <el-button 
                :class="{ 'active': viewMode === 'list' }"
                @click="viewMode = 'list'"
                class="view-btn"
                title="列表视图"
              >
                <el-icon><List /></el-icon>
              </el-button>
            </el-button-group>
            
            <!-- Fullscreen with enhanced style -->
            <el-button 
              @click="toggleFullScreen"
              class="action-btn fullscreen-btn"
              :class="{ 'active': isFullScreen }"
              :title="isFullScreen ? '退出全屏' : '全屏'"
            >
              <el-icon><FullScreen v-if="!isFullScreen" /><Close v-else /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </header>

    <!-- Enhanced Main Content -->
    <main class="enhanced-main">
      <div class="dashboard-container">
        <!-- Enhanced Stats Panel -->
        <div class="enhanced-stats-panel">
          <!-- Real-time Overview Card -->
          <div class="stats-card overview-card">
            <div class="card-header">
              <div class="header-content">
                <h3 class="card-title">实时概览</h3>
                <div class="update-indicator">
                  <div class="pulse-dot"></div>
                  <span class="update-time">{{ formattedTime }}</span>
                </div>
              </div>
            </div>
            
            <div class="enhanced-stats-grid">
              <div class="modern-stat-card operational" @click="filterByStatus('operational')">
                <div class="stat-visual">
                  <div class="stat-icon-container operational">
                    <el-icon class="stat-icon"><Check /></el-icon>
                    <div class="icon-glow operational"></div>
                  </div>
                  <div class="stat-progress">
                    <div class="progress-bar" :style="{ width: stats.operationalPercent + '%' }"></div>
                  </div>
                </div>
                <div class="stat-data">
                  <div class="stat-value">{{ stats.operational }}</div>
                  <div class="stat-label">运行正常</div>
                  <div class="stat-percent">{{ stats.operationalPercent }}%</div>
                </div>
              </div>
              
              <div class="modern-stat-card warning" @click="filterByStatus('warning')">
                <div class="stat-visual">
                  <div class="stat-icon-container warning">
                    <el-icon class="stat-icon"><Warning /></el-icon>
                    <div class="icon-glow warning"></div>
                  </div>
                  <div class="stat-progress">
                    <div class="progress-bar warning" :style="{ width: (stats.warning / stats.total * 100) + '%' }"></div>
                  </div>
                </div>
                <div class="stat-data">
                  <div class="stat-value">{{ stats.warning }}</div>
                  <div class="stat-label">警告状态</div>
                </div>
              </div>
              
              <div class="modern-stat-card error" @click="filterByStatus('error')">
                <div class="stat-visual">
                  <div class="stat-icon-container error">
                    <el-icon class="stat-icon"><CloseIcon /></el-icon>
                    <div class="icon-glow error"></div>
                  </div>
                  <div class="stat-progress">
                    <div class="progress-bar error" :style="{ width: (stats.error / stats.total * 100) + '%' }"></div>
                  </div>
                </div>
                <div class="stat-data">
                  <div class="stat-value">{{ stats.error }}</div>
                  <div class="stat-label">错误状态</div>
                </div>
              </div>
              
              <div class="modern-stat-card idle" @click="filterByStatus('idle')">
                <div class="stat-visual">
                  <div class="stat-icon-container idle">
                    <el-icon class="stat-icon"><Setting /></el-icon>
                    <div class="icon-glow idle"></div>
                  </div>
                  <div class="stat-progress">
                    <div class="progress-bar idle" :style="{ width: (stats.idle / stats.total * 100) + '%' }"></div>
                  </div>
                </div>
                <div class="stat-data">
                  <div class="stat-value">{{ stats.idle }}</div>
                  <div class="stat-label">空闲工位</div>
                </div>
              </div>
            </div>

            <div class="efficiency-summary">
              <div class="efficiency-item">
                <div class="efficiency-value">{{ avgEfficiency }}%</div>
                <div class="efficiency-label">平均效率</div>
              </div>
              <div class="efficiency-item">
                <div class="efficiency-value">{{ totalAssets }}</div>
                <div class="efficiency-label">总设备数</div>
              </div>
            </div>
          </el-card>

          <!-- Filter Panel -->
          <el-card class="filter-card">
            <template #header>
              <span>筛选条件</span>
            </template>
            
            <div class="filter-content">
              <el-checkbox-group v-model="statusFilters">
                <el-checkbox label="operational">运行正常</el-checkbox>
                <el-checkbox label="warning">警告状态</el-checkbox>
                <el-checkbox label="error">错误状态</el-checkbox>
                <el-checkbox label="idle">空闲工位</el-checkbox>
              </el-checkbox-group>
            </div>
          </el-card>

          <!-- Priority Workstations -->
          <el-card class="priority-card">
            <template #header>
              <span>重点监控</span>
            </template>
            
            <div class="priority-list">
              <div 
                v-for="workstation in priorityWorkstations" 
                :key="workstation.locationId"
                class="priority-item"
                @click="handleLocationClick(workstation.locationId)"
              >
                <div class="priority-status" :class="workstation.status"></div>
                <div class="priority-content">
                  <div class="priority-name">{{ workstation.locationName }}</div>
                  <div class="priority-info">{{ workstation.efficiency }}% 效率</div>
                </div>
                <el-icon class="priority-arrow"><ArrowRight /></el-icon>
              </div>
            </div>
          </el-card>
        </div>

        <!-- Enhanced Factory Layout -->
        <div class="enhanced-factory-layout">
          <div class="layout-header">
            <div class="layout-info">
              <div class="info-section">
                <span class="info-label">显示工位</span>
                <span class="info-value">{{ displayedLocations.length }} / {{ stats.total }}</span>
              </div>
              <div class="status-indicators">
                <div class="status-dot operational" title="正常运行"></div>
                <div class="status-dot warning" title="警告状态"></div>
                <div class="status-dot error" title="故障状态"></div>
                <div class="status-dot idle" title="空闲状态"></div>
              </div>
            </div>
            <div class="layout-controls">
              <el-button-group class="zoom-controls">
                <el-button size="small" @click="zoomOut" :disabled="zoomLevel <= 0.5" title="缩小">
                  <el-icon><ZoomOut /></el-icon>
                </el-button>
                <el-button size="small" @click="resetZoom" title="重置缩放">
                  {{ Math.round(zoomLevel * 100) }}%
                </el-button>
                <el-button size="small" @click="zoomIn" :disabled="zoomLevel >= 2" title="放大">
                  <el-icon><ZoomIn /></el-icon>
                </el-button>
              </el-button-group>
            </div>
          </div>

          <!-- Enhanced Layout View -->
          <div v-if="viewMode === 'layout'" class="enhanced-factory-floor" ref="factoryFloor">
            <!-- Advanced Factory Grid -->
            <div class="factory-container" :style="{ transform: `scale(${zoomLevel})` }">
              <svg class="advanced-grid" viewBox="0 0 1100 600" preserveAspectRatio="xMidYMid meet">
                <defs>
                  <!-- Enhanced Grid Pattern -->
                  <pattern id="mainGrid" width="40" height="40" patternUnits="userSpaceOnUse">
                    <path d="M 40 0 L 0 0 0 40" fill="none" stroke="rgba(59, 130, 246, 0.1)" stroke-width="1"/>
                  </pattern>
                  <pattern id="subGrid" width="10" height="10" patternUnits="userSpaceOnUse">
                    <path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(59, 130, 246, 0.05)" stroke-width="0.5"/>
                  </pattern>
                  
                  <!-- Glowing effects -->
                  <filter id="glow">
                    <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                    <feMerge> 
                      <feMergeNode in="coloredBlur"/>
                      <feMergeNode in="SourceGraphic"/> 
                    </feMerge>
                  </filter>
                </defs>
                
                <!-- Background layers -->
                <rect width="100%" height="100%" fill="url(#subGrid)" />
                <rect width="100%" height="100%" fill="url(#mainGrid)" />

              <!-- 区域标识线 - 根据您的实际布局调整 -->
              <g stroke-width="2" fill="none">
                <!-- 区域1：左上角青色区域 -->
                <rect x="40" y="40" width="250" height="100" stroke="#00bcd4" stroke-dasharray="5,5" />
                <text x="45" y="35" fill="#00bcd4" font-size="12" font-weight="bold">区域1-生产线A</text>

                <!-- 区域2：中上青色区域 -->
                <rect x="330" y="40" width="250" height="100" stroke="#00bcd4" stroke-dasharray="5,5" />
                <text x="335" y="35" fill="#00bcd4" font-size="12" font-weight="bold">区域2-装配线B</text>

                <!-- 区域3：右上角青色区域 -->
                <rect x="600" y="40" width="100" height="100" stroke="#00bcd4" stroke-dasharray="5,5" />
                <text x="605" y="35" fill="#00bcd4" font-size="10" font-weight="bold">区域3-质检区</text>

                <!-- 区域4：右侧蓝色区域 -->
                <rect x="710" y="40" width="100" height="140" stroke="#2196f3" stroke-dasharray="5,5" />
                <text x="715" y="35" fill="#2196f3" font-size="12" font-weight="bold">区域4-包装线</text>

                <!-- 区域5：左下黄色区域 -->
                <rect x="40" y="230" width="120" height="40" stroke="#ffc107" stroke-dasharray="5,5" />
                <text x="45" y="225" fill="#ffc107" font-size="10" font-weight="bold">区域5-预处理</text>

                <!-- 区域6：中下灰色区域 -->
                <rect x="300" y="160" width="280" height="120" stroke="#9e9e9e" stroke-dasharray="5,5" />
                <text x="305" y="155" fill="#9e9e9e" font-size="12" font-weight="bold">区域6-主生产线</text>

                <!-- 区域7：右下红色区域 -->
                <rect x="710" y="230" width="100" height="80" stroke="#f44336" stroke-dasharray="5,5" />
                <text x="715" y="225" fill="#f44336" font-size="10" font-weight="bold">区域7-成品区</text>
              </g>
            </svg>

            <!-- Workstation Cells -->
            <div 
              v-for="location in displayedLocations" 
              :key="location.locationId"
              class="workstation-cell"
              :class="[
                `status-${location.status}`, 
                { 
                  'highlighted': location.isHighlighted,
                  'selected': selectedLocationId === location.locationId,
                  'hovered': hoveredLocationId === location.locationId
                }
              ]"
              :style="getWorkstationStyle(location)"
              @click="handleLocationClick(location.locationId)"
              @mouseenter="handleLocationMouseEnter(location.locationId)"
              @mouseleave="handleLocationMouseLeave"
            >
              <!-- 简化显示内容 - 只显示工位编号或状态图标 -->
              <div class="cell-content">
                <el-icon v-if="location.status === 'error'" class="status-icon error">
                  <CloseIcon />
                </el-icon>
                <el-icon v-else-if="location.status === 'warning'" class="status-icon warning">
                  <Warning />
                </el-icon>
                <el-icon v-else-if="location.status === 'operational'" class="status-icon operational">
                  <Check />
                </el-icon>
                <span v-else class="cell-number">{{ location.locationId }}</span>
              </div>

              <!-- Fault Count Badge -->
              <div v-if="location.faultCount > 0" class="fault-badge">
                {{ location.faultCount }}
              </div>

              <!-- Hover Tooltip -->
              <div v-if="hoveredLocationId === location.locationId" class="hover-tooltip">
                {{ location.locationName }} - {{ location.efficiency }}%
              </div>
            </div>
          </div>

          <!-- List View -->
          <div v-else class="factory-list">
            <el-table :data="displayedLocations" height="100%">
              <el-table-column prop="locationCode" label="工位编号" width="100" />
              <el-table-column prop="locationName" label="工位名称" />
              <el-table-column prop="departmentName" label="所属部门" />
              <el-table-column prop="efficiency" label="效率" width="80">
                <template #default="{ row }">
                  <span>{{ row.efficiency }}%</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getStatusTagType(row.status)">
                    {{ getStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="assetCount" label="设备数" width="80" />
              <el-table-column prop="taskCount" label="任务数" width="80" />
              <el-table-column label="操作" width="100">
                <template #default="{ row }">
                  <el-button size="small" @click="handleLocationClick(row.locationId)">
                    详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </main>

    <!-- Footer -->
    <footer class="p-3 bg-gray-800/80 backdrop-blur-sm border-t border-gray-700">
      <div class="max-w-7xl mx-auto flex flex-wrap items-center justify-between text-xs gap-2">
        <div class="text-gray-400">
          <span class="text-gray-300 font-medium">IT资产管理系统</span> v2.0
        </div>
        
        <div class="flex items-center gap-4">
          <div class="flex items-center">
            <span class="w-2 h-2 rounded-full bg-green-500 mr-1"></span>
            <span>正常 ({{ stats.operational }})</span>
          </div>
          <div class="flex items-center">
            <span class="w-2 h-2 rounded-full bg-yellow-500 mr-1"></span>
            <span>警告 ({{ stats.warning }})</span>
          </div>
          <div class="flex items-center">
            <span class="w-2 h-2 rounded-full bg-red-500 mr-1"></span>
            <span>故障 ({{ stats.error }})</span>
          </div>
          <div class="flex items-center">
            <span class="w-2 h-2 rounded-full bg-blue-500 mr-1"></span>
            <span>空闲 ({{ stats.idle }})</span>
          </div>
        </div>
      </div>
    </footer>

    <!-- Workstation Details Drawer -->
    <el-drawer
      v-model="detailDrawerVisible"
      title="工位详情"
      direction="rtl"
      size="600px"
    >
      <div v-if="selectedLocation" class="workstation-detail">
        <!-- Basic Info -->
        <el-card class="detail-card">
          <template #header>
            <div class="detail-header">
              <div class="detail-icon" :class="`status-${selectedLocation.status}`">
                <el-icon size="24"><Cpu /></el-icon>
              </div>
              <div class="detail-title">
                <h2>{{ selectedLocation.locationName }}</h2>
                <p class="detail-subtitle">
                  {{ selectedLocation.departmentName }} | 
                  {{ getStatusText(selectedLocation.status) }}
                </p>
              </div>
            </div>
          </template>

          <!-- Core Metrics -->
          <div class="metrics-grid">
            <div class="metric-item">
              <div class="metric-value">{{ selectedLocation.efficiency }}%</div>
              <div class="metric-label">运行效率</div>
            </div>
            <div class="metric-item">
              <div class="metric-value">{{ selectedLocation.uptime }}%</div>
              <div class="metric-label">开机率</div>
            </div>
            <div class="metric-item">
              <div class="metric-value">{{ selectedLocation.assetCount }}</div>
              <div class="metric-label">设备数量</div>
            </div>
            <div class="metric-item">
              <div class="metric-value">{{ selectedLocation.taskCount }}</div>
              <div class="metric-label">任务数量</div>
            </div>
          </div>
        </el-card>

        <!-- Detail Tabs -->
        <el-tabs v-model="activeTab" class="detail-tabs">
          <el-tab-pane label="设备资产" name="assets">
            <div class="assets-list">
              <el-table :data="workstationAssets" max-height="300">
                <el-table-column prop="name" label="设备名称" />
                <el-table-column prop="assetCode" label="资产编号" />
                <el-table-column prop="brand" label="品牌" />
                <el-table-column prop="status" label="状态">
                  <template #default="{ row }">
                    <el-tag :type="row.status === 1 ? 'success' : 'warning'">
                      {{ row.statusText }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="活跃任务" name="tasks">
            <div class="tasks-list">
              <el-table :data="workstationTasks" max-height="300">
                <el-table-column prop="title" label="任务标题" />
                <el-table-column prop="priority" label="优先级">
                  <template #default="{ row }">
                    <el-tag :type="getPriorityTagType(row.priority)">
                      {{ getPriorityText(row.priority) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" />
                <el-table-column prop="assigneeName" label="负责人" />
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="故障报告" name="faults">
            <div class="faults-list">
              <el-table :data="workstationFaults" max-height="300">
                <el-table-column prop="description" label="故障描述" />
                <el-table-column prop="severity" label="严重等级">
                  <template #default="{ row }">
                    <el-tag :type="getSeverityTagType(row.severity)">
                      {{ row.severity }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="处理状态" />
                <el-table-column prop="createdAt" label="报告时间">
                  <template #default="{ row }">
                    {{ formatDateTime(row.createdAt) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>

        <!-- Action Buttons -->
        <div class="detail-actions">
          <el-button type="primary" @click="diagnoseWorkstation">
            <el-icon><Tools /></el-icon>
            设备诊断
          </el-button>
          <el-button @click="viewWorkstationReport">
            <el-icon><Document /></el-icon>
            查看报告
          </el-button>
          <el-button @click="scheduleMaintenace">
            <el-icon><Calendar /></el-icon>
            安排维护
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Search, Filter, Refresh, FullScreen, Close, Cpu,
  Check, Warning, Close as CloseIcon, Setting, ArrowRight,
  Tools, Document, Calendar, ZoomIn, ZoomOut, List, Grid
} from '@element-plus/icons-vue'
import locationApi from '@/api/location'
import { assetApi } from '@/api/asset'
import { taskApi } from '@/api/task'
import faultApi from '@/api/fault'

// 响应式数据
const locations = ref([])
const departments = ref([])
const selectedLocationId = ref(null)
const selectedLocation = ref(null)
const hoveredLocationId = ref(null)
const searchTerm = ref('')
const searchResults = ref([])
const isFullScreen = ref(false)
const lastUpdate = ref(new Date())
const refreshing = ref(false)
const viewMode = ref('layout')
const zoomLevel = ref(1)
const systemStatus = ref('online')
const activeFilterCount = ref(0)
const activeTab = ref('assets')
const detailDrawerVisible = ref(false)
const statusFilters = ref(['operational', 'warning', 'error', 'idle'])

// 工位数据
const workstationAssets = ref([])
const workstationTasks = ref([])
const workstationFaults = ref([])

// 筛选表单
const filterForm = ref({
  departmentId: null,
  locationType: null,
  onlyWithAssets: false
})

// 新增计算属性
const systemStatusText = computed(() => {
  const statusMap = {
    'online': '正常',
    'warning': '警告', 
    'offline': '离线'
  }
  return statusMap[systemStatus.value] || '未知'
})

const hasActiveFilters = computed(() => {
  return statusFilters.value.length < 4 || 
         filterForm.value.departmentId || 
         filterForm.value.locationType || 
         filterForm.value.onlyWithAssets
})

// 新增事件处理函数
const selectSearchResult = (result) => {
  searchTerm.value = ''
  searchResults.value = []
  handleLocationClick(result.locationId)
}

const filterByStatus = (status) => {
  statusFilters.value = [status]
  ElMessage.info(`已筛选 ${getStatusText(status)} 工位`)
}

const zoomIn = () => {
  if (zoomLevel.value < 2) {
    zoomLevel.value = Math.min(2, zoomLevel.value + 0.1)
  }
}

const zoomOut = () => {
  if (zoomLevel.value > 0.5) {
    zoomLevel.value = Math.max(0.5, zoomLevel.value - 0.1)
  }
}

const resetZoom = () => {
  zoomLevel.value = 1
}

// 更新系统状态
const updateSystemStatus = () => {
  const errorCount = stats.value.error
  const warningCount = stats.value.warning
  
  if (errorCount > 0) {
    systemStatus.value = 'offline'
  } else if (warningCount > 0) {
    systemStatus.value = 'warning'
  } else {
    systemStatus.value = 'online'
  }
}

// 生成145个工位数据 - 根据您的实际工厂布局图
const generateWorkstationData = () => {
  const workstations = []
  const statuses = ['operational', 'warning', 'error', 'idle']
  const statusWeights = [0.7, 0.15, 0.1, 0.05] // 70%正常, 15%警告, 10%故障, 5%空闲

  // 根据您的实际截图重新定义工厂布局 - 7个区域，145个点位
  const layoutConfig = {
    // 上方区域的工位布局 - 按照截图中的实际排列
    topArea: [
      // 第一排工位 (最上方)
      { id: 1, x: 140, y: 60, zone: 'zone1' },
      { id: 2, x: 170, y: 60, zone: 'zone1' },
      { id: 3, x: 200, y: 60, zone: 'zone1' },
      { id: 4, x: 230, y: 60, zone: 'zone1' },
      { id: 5, x: 260, y: 60, zone: 'zone1' },
      { id: 6, x: 290, y: 60, zone: 'zone1' },

      // 第二排工位
      { id: 7, x: 140, y: 90, zone: 'zone1' },
      { id: 8, x: 170, y: 90, zone: 'zone1' },
      { id: 9, x: 200, y: 90, zone: 'zone1' },
      { id: 10, x: 230, y: 90, zone: 'zone1' },
      { id: 11, x: 260, y: 90, zone: 'zone1' },
      { id: 12, x: 290, y: 90, zone: 'zone1' },

      // 第三排工位
      { id: 13, x: 140, y: 120, zone: 'zone1' },
      { id: 14, x: 170, y: 120, zone: 'zone1' },
      { id: 15, x: 200, y: 120, zone: 'zone1' },
      { id: 16, x: 230, y: 120, zone: 'zone1' },
      { id: 17, x: 260, y: 120, zone: 'zone1' },
      { id: 18, x: 290, y: 120, zone: 'zone1' },

      // 中间区域工位
      { id: 19, x: 350, y: 60, zone: 'zone2' },
      { id: 20, x: 380, y: 60, zone: 'zone2' },
      { id: 21, x: 410, y: 60, zone: 'zone2' },
      { id: 22, x: 440, y: 60, zone: 'zone2' },
      { id: 23, x: 470, y: 60, zone: 'zone2' },
      { id: 24, x: 500, y: 60, zone: 'zone2' },
      { id: 25, x: 530, y: 60, zone: 'zone2' },
      { id: 26, x: 560, y: 60, zone: 'zone2' },
      { id: 27, x: 590, y: 60, zone: 'zone2' },

      { id: 28, x: 350, y: 90, zone: 'zone2' },
      { id: 29, x: 380, y: 90, zone: 'zone2' },
      { id: 30, x: 410, y: 90, zone: 'zone2' },
      { id: 31, x: 440, y: 90, zone: 'zone2' },
      { id: 32, x: 470, y: 90, zone: 'zone2' },
      { id: 33, x: 500, y: 90, zone: 'zone2' },
      { id: 34, x: 530, y: 90, zone: 'zone2' },
      { id: 35, x: 560, y: 90, zone: 'zone2' },
      { id: 36, x: 590, y: 90, zone: 'zone2' },

      { id: 37, x: 350, y: 120, zone: 'zone2' },
      { id: 38, x: 380, y: 120, zone: 'zone2' },
      { id: 39, x: 410, y: 120, zone: 'zone2' },
      { id: 40, x: 440, y: 120, zone: 'zone2' },
      { id: 41, x: 470, y: 120, zone: 'zone2' },
      { id: 42, x: 500, y: 120, zone: 'zone2' },
      { id: 43, x: 530, y: 120, zone: 'zone2' },
      { id: 44, x: 560, y: 120, zone: 'zone2' },
      { id: 45, x: 590, y: 120, zone: 'zone2' },

      { id: 46, x: 350, y: 150, zone: 'zone2' },
      { id: 47, x: 380, y: 150, zone: 'zone2' },
      { id: 48, x: 410, y: 150, zone: 'zone2' },
      { id: 49, x: 440, y: 150, zone: 'zone2' },
      { id: 50, x: 470, y: 150, zone: 'zone2' },
      { id: 51, x: 500, y: 150, zone: 'zone2' },
      { id: 52, x: 530, y: 150, zone: 'zone2' },
      { id: 53, x: 560, y: 150, zone: 'zone2' },
      { id: 54, x: 590, y: 150, zone: 'zone2' },

      // 右上角区域
      { id: 55, x: 620, y: 60, zone: 'zone3' },
      { id: 56, x: 650, y: 60, zone: 'zone3' },
      { id: 57, x: 680, y: 60, zone: 'zone3' },
      { id: 58, x: 710, y: 60, zone: 'zone3' },
      { id: 59, x: 740, y: 60, zone: 'zone3' },
      { id: 60, x: 770, y: 60, zone: 'zone3' },

      { id: 61, x: 620, y: 90, zone: 'zone3' },
      { id: 62, x: 650, y: 90, zone: 'zone3' },
      { id: 63, x: 680, y: 90, zone: 'zone3' },
      { id: 64, x: 710, y: 90, zone: 'zone3' },
      { id: 65, x: 740, y: 90, zone: 'zone3' },
      { id: 66, x: 770, y: 90, zone: 'zone3' },

      { id: 67, x: 620, y: 120, zone: 'zone3' },
      { id: 68, x: 650, y: 120, zone: 'zone3' },
      { id: 69, x: 680, y: 120, zone: 'zone3' },
      { id: 70, x: 710, y: 120, zone: 'zone3' },
      { id: 71, x: 740, y: 120, zone: 'zone3' },
      { id: 72, x: 770, y: 120, zone: 'zone3' }
    ],

    // 中间区域工位
    middleArea: [
      { id: 73, x: 320, y: 200, zone: 'zone2' },
      { id: 74, x: 350, y: 200, zone: 'zone2' },
      { id: 75, x: 380, y: 200, zone: 'zone2' },
      { id: 76, x: 410, y: 200, zone: 'zone2' },
      { id: 77, x: 440, y: 200, zone: 'zone2' },
      { id: 78, x: 470, y: 200, zone: 'zone2' },
      { id: 79, x: 500, y: 200, zone: 'zone2' },
      { id: 80, x: 530, y: 200, zone: 'zone2' },
      { id: 81, x: 560, y: 200, zone: 'zone2' },
      { id: 82, x: 590, y: 200, zone: 'zone2' },

      { id: 83, x: 320, y: 230, zone: 'zone2' },
      { id: 84, x: 350, y: 230, zone: 'zone2' },
      { id: 85, x: 380, y: 230, zone: 'zone2' },
      { id: 86, x: 410, y: 230, zone: 'zone2' },
      { id: 87, x: 440, y: 230, zone: 'zone2' },
      { id: 88, x: 470, y: 230, zone: 'zone2' },
      { id: 89, x: 500, y: 230, zone: 'zone2' },
      { id: 90, x: 530, y: 230, zone: 'zone2' },
      { id: 91, x: 560, y: 230, zone: 'zone2' },
      { id: 92, x: 590, y: 230, zone: 'zone2' },

      { id: 93, x: 320, y: 260, zone: 'zone2' },
      { id: 94, x: 350, y: 260, zone: 'zone2' },
      { id: 95, x: 380, y: 260, zone: 'zone2' },
      { id: 96, x: 410, y: 260, zone: 'zone2' },
      { id: 97, x: 440, y: 260, zone: 'zone2' },
      { id: 98, x: 470, y: 260, zone: 'zone2' },
      { id: 99, x: 500, y: 260, zone: 'zone2' },
      { id: 100, x: 530, y: 260, zone: 'zone2' }
    ],

    // 左侧区域
    leftArea: [
      { id: 101, x: 60, y: 200, zone: 'zone1' },
      { id: 102, x: 90, y: 200, zone: 'zone1' },
      { id: 103, x: 120, y: 200, zone: 'zone1' },
      { id: 104, x: 150, y: 200, zone: 'zone1' },
      { id: 105, x: 180, y: 200, zone: 'zone1' },

      { id: 106, x: 60, y: 230, zone: 'zone1' },
      { id: 107, x: 90, y: 230, zone: 'zone1' },
      { id: 108, x: 120, y: 230, zone: 'zone1' },
      { id: 109, x: 150, y: 230, zone: 'zone1' },
      { id: 110, x: 180, y: 230, zone: 'zone1' }
    ],

    // 右侧区域
    rightArea: [
      { id: 111, x: 650, y: 200, zone: 'zone4' },
      { id: 112, x: 680, y: 200, zone: 'zone4' },
      { id: 113, x: 710, y: 200, zone: 'zone4' },
      { id: 114, x: 740, y: 200, zone: 'zone4' },
      { id: 115, x: 770, y: 200, zone: 'zone4' },

      { id: 116, x: 650, y: 230, zone: 'zone4' },
      { id: 117, x: 680, y: 230, zone: 'zone4' },
      { id: 118, x: 710, y: 230, zone: 'zone4' },
      { id: 119, x: 740, y: 230, zone: 'zone4' },
      { id: 120, x: 770, y: 230, zone: 'zone4' }
    ],

    // 下方区域
    bottomArea: [
      // 区域5 - 预处理
      { id: 121, x: 120, y: 350, zone: 'zone5' },
      { id: 122, x: 150, y: 350, zone: 'zone5' },
      { id: 123, x: 180, y: 350, zone: 'zone5' },
      { id: 124, x: 210, y: 350, zone: 'zone5' },

      // 区域6 - 主生产线
      { id: 125, x: 320, y: 350, zone: 'zone6' },
      { id: 126, x: 350, y: 350, zone: 'zone6' },
      { id: 127, x: 380, y: 350, zone: 'zone6' },
      { id: 128, x: 410, y: 350, zone: 'zone6' },
      { id: 129, x: 440, y: 350, zone: 'zone6' },
      { id: 130, x: 470, y: 350, zone: 'zone6' },
      { id: 131, x: 500, y: 350, zone: 'zone6' },
      { id: 132, x: 530, y: 350, zone: 'zone6' },
      { id: 133, x: 560, y: 350, zone: 'zone6' },
      { id: 134, x: 590, y: 350, zone: 'zone6' },

      // 区域7 - 成品区
      { id: 135, x: 650, y: 350, zone: 'zone7' },
      { id: 136, x: 680, y: 350, zone: 'zone7' },
      { id: 137, x: 710, y: 350, zone: 'zone7' },
      { id: 138, x: 740, y: 350, zone: 'zone7' },
      { id: 139, x: 770, y: 350, zone: 'zone7' },

      { id: 140, x: 650, y: 380, zone: 'zone7' },
      { id: 141, x: 680, y: 380, zone: 'zone7' },
      { id: 142, x: 710, y: 380, zone: 'zone7' },
      { id: 143, x: 740, y: 380, zone: 'zone7' },
      { id: 144, x: 770, y: 380, zone: 'zone7' },
      { id: 145, x: 800, y: 380, zone: 'zone7' }
    ],

    // 区域定义
    zones: {
      zone1: { name: '区域1-生产线A', color: '#00bcd4' },
      zone2: { name: '区域2-装配线B', color: '#00bcd4' },
      zone3: { name: '区域3-质检区', color: '#00bcd4' },
      zone4: { name: '区域4-包装线', color: '#2196f3' },
      zone5: { name: '区域5-预处理', color: '#ffc107' },
      zone6: { name: '区域6-主生产线', color: '#9e9e9e' },
      zone7: { name: '区域7-成品区', color: '#f44336' }
    }
  }

  // 根据新的布局配置生成工位数据
  const allAreas = [
    ...layoutConfig.topArea,
    ...layoutConfig.middleArea,
    ...layoutConfig.leftArea,
    ...layoutConfig.rightArea,
    ...layoutConfig.bottomArea
  ]

  allAreas.forEach(position => {
    const random = Math.random()
    let status = 'operational'
    let cumulative = 0

    for (let j = 0; j < statusWeights.length; j++) {
      cumulative += statusWeights[j]
      if (random < cumulative) {
        status = statuses[j]
        break
      }
    }

    const zone = layoutConfig.zones[position.zone]

    workstations.push({
      locationId: position.id,
      locationName: `${zone.name}-工位${position.id.toString().padStart(3, '0')}`,
      locationCode: `WS${position.id.toString().padStart(3, '0')}`,
      departmentName: zone.name,
      status: status,
      efficiency: Math.floor(Math.random() * 30) + 70,
      uptime: Math.floor(Math.random() * 20) + 80,
      assetCount: Math.floor(Math.random() * 5) + 2,
      taskCount: Math.floor(Math.random() * 8) + 1,
      faultCount: status === 'error' ? Math.floor(Math.random() * 3) + 1 :
                  status === 'warning' ? Math.floor(Math.random() * 2) : 0,
      lastUpdate: new Date(),
      // 实际的工位坐标
      x: position.x,
      y: position.y,
      zoneColor: zone.color,
      zoneId: position.zone,
      isHighlighted: false
    })
  })
  
  return workstations
}



// 工位定位样式 - 调整为矩形块样式
const getWorkstationStyle = (workstation) => {
  return {
    position: 'absolute',
    left: `${workstation.x}px`,
    top: `${workstation.y}px`,
    width: '24px',
    height: '20px',
    transform: hoveredLocationId.value === workstation.locationId ? 'scale(1.2)' : 'scale(1)',
    zIndex: hoveredLocationId.value === workstation.locationId ? 10 : 1
  }
}

// 状态标签类型
const getStatusTagType = (status) => {
  switch (status) {
    case 'operational': return 'success'
    case 'warning': return 'warning'
    case 'error': return 'danger'
    case 'idle': return 'info'
    default: return 'info'
  }
}

// 状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'operational': return '运行正常'
    case 'warning': return '警告状态'
    case 'error': return '故障状态'
    case 'idle': return '空闲状态'
    default: return '未知状态'
  }
}

// 优先级标签类型
const getPriorityTagType = (priority) => {
  switch (priority) {
    case 1: return 'danger'
    case 2: return 'warning'
    case 3: return 'success'
    default: return 'info'
  }
}

// 优先级文本
const getPriorityText = (priority) => {
  switch (priority) {
    case 1: return '高优先级'
    case 2: return '中优先级'
    case 3: return '低优先级'
    default: return '普通'
  }
}

// 严重等级标签类型
const getSeverityTagType = (severity) => {
  if (severity.includes('严重')) return 'danger'
  if (severity.includes('警告')) return 'warning'
  return 'info'
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 计算属性
const displayedLocations = computed(() => {
  let filtered = locations.value

  // 状态过滤
  if (statusFilters.value.length < 4) {
    filtered = filtered.filter(loc => statusFilters.value.includes(loc.status))
  }

  // 搜索过滤
  if (searchTerm.value) {
    filtered = filtered.filter(loc => 
      loc.locationName.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
      loc.locationCode.toLowerCase().includes(searchTerm.value.toLowerCase())
    )
  }
  
  // 更新搜索结果
  if (searchTerm.value && searchResults.value.length === 0) {
    searchResults.value = filtered.slice(0, 5)
  }

  return filtered
})

const stats = computed(() => {
  const total = locations.value.length
  const operational = locations.value.filter(l => l.status === 'operational').length
  const warning = locations.value.filter(l => l.status === 'warning').length
  const error = locations.value.filter(l => l.status === 'error').length
  const idle = locations.value.filter(l => l.status === 'idle').length
  
  return {
    total,
    operational,
    warning,
    error,
    idle,
    operationalPercent: total > 0 ? Math.round(operational / total * 100) : 0
  }
})

const avgEfficiency = computed(() => {
  if (locations.value.length === 0) return 0
  const totalEfficiency = locations.value.reduce((sum, loc) => sum + loc.efficiency, 0)
  return Math.round(totalEfficiency / locations.value.length)
})

const totalAssets = computed(() => {
  return locations.value.reduce((sum, loc) => sum + loc.assetCount, 0)
})

const priorityWorkstations = computed(() => {
  return locations.value
    .filter(loc => loc.status === 'warning' || loc.status === 'error')
    .slice(0, 5)
})

const formattedTime = computed(() => {
  return lastUpdate.value.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
})

// 事件处理
const handleLocationClick = async (locationId) => {
  selectedLocationId.value = locationId
  selectedLocation.value = locations.value.find(loc => loc.locationId === locationId)
  detailDrawerVisible.value = true
  
  // 加载工位详细数据
  await loadWorkstationDetails(locationId)
}

const handleLocationMouseEnter = (locationId) => {
  hoveredLocationId.value = locationId
}

const handleLocationMouseLeave = () => {
  hoveredLocationId.value = null
}

// 加载工位详细数据
const loadWorkstationDetails = async (locationId) => {
  try {
    // 模拟API调用 - 实际项目中替换为真实API
    workstationAssets.value = [
      { id: 1, name: '数控机床-001', assetCode: 'NC001', brand: 'FANUC', status: 1, statusText: '运行中' },
      { id: 2, name: '质检仪-002', assetCode: 'QI002', brand: 'Zeiss', status: 1, statusText: '正常' },
      { id: 3, name: '传送带-003', assetCode: 'CB003', brand: '本土', status: 2, statusText: '维护中' }
    ]
    
    workstationTasks.value = [
      { id: 1, title: '日常巡检', priority: 2, status: '进行中', assigneeName: '张三' },
      { id: 2, title: '设备维护', priority: 1, status: '待开始', assigneeName: '李四' },
      { id: 3, title: '质量检查', priority: 3, status: '已完成', assigneeName: '王五' }
    ]
    
    workstationFaults.value = [
      { id: 1, description: '温度异常', severity: '警告级别', status: '处理中', createdAt: '2025-06-02T10:30:00' },
      { id: 2, description: '振动超标', severity: '严重级别', status: '已修复', createdAt: '2025-06-01T14:20:00' }
    ]
  } catch (error) {
    console.error('加载工位详情失败:', error)
    ElMessage.error('加载工位详情失败')
  }
}

// 工位操作
const diagnoseWorkstation = () => {
  ElMessage.info('正在启动设备诊断...')
}

const viewWorkstationReport = () => {
  ElMessage.info('正在生成工位报告...')
}

const scheduleMaintenace = () => {
  ElMessage.info('正在安排维护计划...')
}

const handleRefresh = async () => {
  refreshing.value = true
  try {
    // 重新生成工位数据
    locations.value = generateWorkstationData()
    lastUpdate.value = new Date()
    ElMessage.success('数据已刷新')
  } catch (error) {
    console.error('刷新失败:', error)
    ElMessage.error('刷新失败')
  } finally {
    refreshing.value = false
  }
}

const applyFilter = async () => {
  try {
    // 这里可以调用真实的API进行筛选
    // const response = await locationApi.searchLocations(filterForm.value)
    ElMessage.success('筛选条件已应用')
  } catch (error) {
    console.error('筛选失败:', error)
    ElMessage.error('筛选失败')
  }
}

const resetFilter = () => {
  filterForm.value = {
    departmentId: null,
    locationType: null,
    onlyWithAssets: false
  }
  statusFilters.value = ['operational', 'warning', 'error', 'idle']
  searchTerm.value = ''
}

const toggleFullScreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen().catch(err => {
      console.error(`全屏模式错误: ${err.message}`)
    })
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen()
    }
  }
}

// 数据初始化
const initDashboard = () => {
  // 生成145个工位的模拟数据
  locations.value = generateWorkstationData()
  
  // 模拟部门数据 - 匹配7个区域
  departments.value = [
    { departmentId: 1, departmentName: '区域1-生产线A' },
    { departmentId: 2, departmentName: '区域2-装配线B' },
    { departmentId: 3, departmentName: '区域3-质检区' },
    { departmentId: 4, departmentName: '区域4-包装线' },
    { departmentId: 5, departmentName: '区域5-预处理' },
    { departmentId: 6, departmentName: '区域6-主生产线' },
    { departmentId: 7, departmentName: '区域7-成品区' }
  ]
  
  // 更新系统状态
  updateSystemStatus()
  
  // 定时更新数据
  setInterval(() => {
    lastUpdate.value = new Date()
    updateSystemStatus()
    
    // 随机更新一些工位的效率
    locations.value.forEach(loc => {
      if (Math.random() < 0.1) { // 10%概率更新
        loc.efficiency = Math.max(0, Math.min(100, loc.efficiency + (Math.random() - 0.5) * 10))
      }
    })
  }, 5000)
}

// 全屏监听
const handleFullScreenChange = () => {
  isFullScreen.value = !!document.fullscreenElement
}

// 生命周期
onMounted(() => {
  initDashboard()
  document.addEventListener('fullscreenchange', handleFullScreenChange)
})

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullScreenChange)
})
</script>

<style scoped>
/* Enhanced Modern Factory Dashboard */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --color-industrial-900: #0c1a25;
  --color-industrial-800: #122738;
  --color-industrial-700: #1a3650;
  --color-industrial-600: #23527c;
  --color-industrial-500: #2e6da4;
  --color-industrial-400: #3d8fd1;
  --color-industrial-300: #66b0ff;
  --color-status-operational: #10b981;
  --color-status-warning: #f59e0b;
  --color-status-error: #ef4444;
  --color-status-idle: #3b82f6;
}

.smart-factory-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--color-industrial-900) 0%, var(--color-industrial-800) 100%);
  color: #e2e8f0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  position: relative;
  overflow-x: hidden;
}

/* Background pattern */
.smart-factory-dashboard::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 1px 1px, rgba(66, 153, 225, 0.05) 1px, transparent 0),
    radial-gradient(circle at 1px 1px, rgba(66, 153, 225, 0.03) 1px, transparent 0);
  background-size: 40px 40px, 20px 20px;
  background-position: 0 0, 20px 20px;
  pointer-events: none;
  z-index: -1;
}

/* Header Styles */
.dashboard-header {
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(45deg, #3b82f6, #1d4ed8);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  box-shadow: 0 8px 25px -8px rgba(59, 130, 246, 0.3);
}

.header-title h1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(45deg, #f1f5f9, #cbd5e1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-title p {
  font-size: 0.875rem;
  color: #94a3b8;
  margin: 0;
}

.update-badge {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.2);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  margin-left: 1rem;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.search-input {
  width: 200px;
}

/* Main Layout */
.dashboard-main {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 1.5rem;
  height: calc(100vh - 140px);
}

/* Stats Panel */
.stats-panel {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.stats-card {
  background: rgba(30, 41, 59, 0.6);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.update-time {
  font-size: 0.75rem;
  color: #64748b;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba(51, 65, 85, 0.3);
  border-radius: 12px;
  border-left: 4px solid;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.3);
}

.stat-item.operational {
  border-left-color: #22c55e;
}

.stat-item.warning {
  border-left-color: #eab308;
}

.stat-item.error {
  border-left-color: #ef4444;
}

.stat-item.idle {
  border-left-color: #6b7280;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.operational .stat-icon {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.warning .stat-icon {
  background: rgba(234, 179, 8, 0.2);
  color: #eab308;
}

.error .stat-icon {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.idle .stat-icon {
  background: rgba(107, 114, 128, 0.2);
  color: #6b7280;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
}

.stat-label {
  font-size: 0.75rem;
  color: #94a3b8;
  margin-top: 0.25rem;
}

.stat-percent {
  font-size: 0.875rem;
  font-weight: 600;
  color: #22c55e;
}

.efficiency-summary {
  display: flex;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(59, 130, 246, 0.2);
}

.efficiency-item {
  flex: 1;
  text-align: center;
}

.efficiency-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #3b82f6;
}

.efficiency-label {
  font-size: 0.75rem;
  color: #94a3b8;
  margin-top: 0.25rem;
}

/* Filter Panel */
.filter-card {
  background: rgba(30, 41, 59, 0.6);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 16px;
}

.filter-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Priority Panel */
.priority-card {
  background: rgba(30, 41, 59, 0.6);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 16px;
  flex: 1;
}

.priority-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-height: 200px;
  overflow-y: auto;
}

.priority-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(51, 65, 85, 0.3);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.priority-item:hover {
  background: rgba(51, 65, 85, 0.5);
  transform: translateX(4px);
}

.priority-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.priority-status.warning {
  background: #eab308;
}

.priority-status.error {
  background: #ef4444;
}

.priority-content {
  flex: 1;
}

.priority-name {
  font-weight: 600;
  font-size: 0.875rem;
}

.priority-info {
  font-size: 0.75rem;
  color: #94a3b8;
}

.priority-arrow {
  color: #64748b;
}

/* Factory Layout */
.factory-layout {
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 16px;
  padding: 1rem;
  overflow: hidden;
}

.layout-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

.view-selector {
  width: 120px;
}

/* Factory Floor */
.factory-floor {
  position: relative;
  height: calc(100% - 60px);
  overflow: auto;
  background: radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.05) 0%, rgba(15, 23, 42, 0.8) 70%);
  border-radius: 12px;
  border: 2px solid rgba(59, 130, 246, 0.1);
}

.factory-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* Workstation Cells - 调整为矩形块样式 */
.workstation-cell {
  position: absolute;
  width: 24px;
  height: 20px;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 0.5rem;
  font-weight: 600;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.workstation-cell:hover {
  transform: scale(1.5) translateZ(0);
  z-index: 10;
  box-shadow: 0 8px 16px -4px rgba(0, 0, 0, 0.4);
  border-width: 2px;
}

.workstation-cell.highlighted {
  animation: pulse 2s infinite;
}

.workstation-cell.selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
}

.workstation-cell.status-operational {
  background: #22c55e;
  border-color: rgba(0, 0, 0, 0.3);
  color: #ffffff;
}

.workstation-cell.status-warning {
  background: #f59e0b;
  border-color: rgba(0, 0, 0, 0.3);
  color: #ffffff;
}

.workstation-cell.status-error {
  background: #ef4444;
  border-color: rgba(0, 0, 0, 0.3);
  color: #ffffff;
  animation: errorBlink 2s infinite;
}

.workstation-cell.status-idle {
  background: #6b7280;
  border-color: rgba(0, 0, 0, 0.3);
  color: #ffffff;
}

.cell-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.status-icon {
  font-size: 10px;
}

.workstation-cell:hover .status-icon {
  font-size: 14px;
}

.cell-number {
  font-size: 8px;
  font-weight: 700;
  color: #ffffff;
}

.workstation-cell:hover .cell-number {
  font-size: 10px;
}

.fault-badge {
  position: absolute;
  top: -3px;
  right: -3px;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 8px;
  height: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.4rem;
  font-weight: 700;
}

.workstation-cell:hover .fault-badge {
  width: 12px;
  height: 12px;
  font-size: 0.5rem;
  top: -6px;
  right: -6px;
}

.hover-tooltip {
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.6rem;
  white-space: nowrap;
  z-index: 20;
  min-width: 80px;
  text-align: center;
}

/* List View */
.factory-list {
  height: calc(100% - 60px);
}

/* Detail Drawer */
.workstation-detail {
  padding: 1rem;
}

.detail-card {
  margin-bottom: 1.5rem;
}

.detail-header {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.detail-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.detail-icon.status-operational {
  background: linear-gradient(45deg, #22c55e, #16a34a);
  color: white;
}

.detail-icon.status-warning {
  background: linear-gradient(45deg, #eab308, #ca8a04);
  color: white;
}

.detail-icon.status-error {
  background: linear-gradient(45deg, #ef4444, #dc2626);
  color: white;
}

.detail-icon.status-idle {
  background: linear-gradient(45deg, #6b7280, #4b5563);
  color: white;
}

.detail-title h2 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.detail-subtitle {
  color: #94a3b8;
  font-size: 0.875rem;
  margin: 0.25rem 0 0 0;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  margin-top: 1rem;
}

.metric-item {
  text-align: center;
  padding: 1rem;
  background: rgba(51, 65, 85, 0.3);
  border-radius: 8px;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #3b82f6;
}

.metric-label {
  font-size: 0.75rem;
  color: #94a3b8;
  margin-top: 0.25rem;
}

.detail-tabs {
  margin-top: 1.5rem;
}

.detail-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(59, 130, 246, 0.2);
}

/* Animations */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes errorBlink {
  0%, 100% { background: linear-gradient(135deg, rgba(239, 68, 68, 0.8), rgba(185, 28, 28, 0.9)); }
  50% { background: linear-gradient(135deg, rgba(239, 68, 68, 1), rgba(185, 28, 28, 1)); }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .dashboard-main {
    grid-template-columns: 250px 1fr;
  }
  
  .workstation-cell {
    width: 60px;
    height: 45px;
    font-size: 0.625rem;
  }
}

@media (max-width: 768px) {
  .dashboard-main {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }
  
  .stats-panel {
    order: 2;
  }
  
  .factory-layout {
    order: 1;
  }
  
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .header-controls {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
}

/* Element Plus Customization */
:deep(.el-card__header) {
  background: rgba(51, 65, 85, 0.3);
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

:deep(.el-card__body) {
  background: transparent;
}

:deep(.el-drawer__header) {
  background: rgba(30, 41, 59, 0.8);
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

:deep(.el-drawer__body) {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

:deep(.el-table) {
  background: transparent;
}

:deep(.el-table th) {
  background: rgba(51, 65, 85, 0.5);
  color: #f1f5f9;
}

:deep(.el-table td) {
  background: rgba(30, 41, 59, 0.3);
  color: #e2e8f0;
}

:deep(.el-table--border::after) {
  background: rgba(59, 130, 246, 0.2);
}

:deep(.el-table::before) {
  background: rgba(59, 130, 246, 0.2);
}

:deep(.el-tabs__nav-wrap::after) {
  background: rgba(59, 130, 246, 0.2);
}

:deep(.el-tabs__active-bar) {
  background: #3b82f6;
}

:deep(.el-tabs__item.is-active) {
  color: #3b82f6;
}
</style>