/**
 * 用户API接口 V1.1 - 新接口化版本
 * 文件路径: src/api/v1.1/user.js
 * 功能描述: 基于新的统一服务接口的用户管理API
 */

import request from '@/utils/request'

/**
 * 用户API V1.1
 */
const userApiV1_1 = {
  /**
   * 获取用户列表 (V1.1)
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  getUserList(params) {
    const apiParams = {
      pageIndex: params.pageIndex || params.pageNumber || 1,
      pageSize: params.pageSize || 20,
      keyword: params.keyword || params.searchTerm || '',
      department: params.department || '',
      role: params.role || '',
      status: params.status || '',
      sortBy: params.sortBy || 'CreatedAt',
      sortDirection: params.sortDirection || 'desc'
    }
    
    console.log('V1.1 获取用户列表，参数:', apiParams)
    return request.get('/api/v1.1/user', { params: apiParams })
  },

  /**
   * 获取用户详情 (V1.1)
   * @param {number|string} id 用户ID
   * @returns {Promise}
   */
  getUserById(id) {
    console.log('V1.1 获取用户详情，ID:', id)
    return request.get(`/api/v1.1/user/${id}`)
  },

  /**
   * 创建用户 (V1.1)
   * @param {Object} data 用户数据
   * @returns {Promise}
   */
  createUser(data) {
    const apiData = {
      username: data.username,
      email: data.email,
      fullName: data.fullName || data.name,
      department: data.department,
      role: data.role || 'User',
      phoneNumber: data.phoneNumber || data.phone,
      isActive: data.isActive !== undefined ? data.isActive : true
    }
    
    console.log('V1.1 创建用户，数据:', apiData)
    return request.post('/api/v1.1/user', apiData)
  },

  /**
   * 更新用户 (V1.1)
   * @param {number|string} id 用户ID
   * @param {Object} data 用户数据
   * @returns {Promise}
   */
  updateUser(id, data) {
    const apiData = {
      username: data.username,
      email: data.email,
      fullName: data.fullName || data.name,
      department: data.department,
      role: data.role,
      phoneNumber: data.phoneNumber || data.phone,
      isActive: data.isActive
    }
    
    console.log('V1.1 更新用户，ID:', id, '数据:', apiData)
    return request.put(`/api/v1.1/user/${id}`, apiData)
  },

  /**
   * 删除用户 (V1.1)
   * @param {number|string} id 用户ID
   * @returns {Promise}
   */
  deleteUser(id) {
    console.log('V1.1 删除用户，ID:', id)
    return request.delete(`/api/v1.1/user/${id}`)
  },

  /**
   * 修改用户密码 (V1.1)
   * @param {number|string} id 用户ID
   * @param {Object} data 密码数据
   * @returns {Promise}
   */
  changeUserPassword(id, data) {
    const apiData = {
      currentPassword: data.currentPassword,
      newPassword: data.newPassword,
      confirmPassword: data.confirmPassword
    }
    
    console.log('V1.1 修改用户密码，ID:', id)
    return request.post(`/api/v1.1/user/${id}/change-password`, apiData)
  },

  /**
   * 重置用户密码 (V1.1)
   * @param {number|string} id 用户ID
   * @returns {Promise}
   */
  resetUserPassword(id) {
    console.log('V1.1 重置用户密码，ID:', id)
    return request.post(`/api/v1.1/user/${id}/reset-password`)
  },

  /**
   * 获取用户统计信息 (V1.1)
   * @param {number|string} id 用户ID
   * @returns {Promise}
   */
  getUserStatistics(id) {
    console.log('V1.1 获取用户统计信息，ID:', id)
    return request.get(`/api/v1.1/user/${id}/statistics`)
  },

  /**
   * 搜索用户 (V1.1)
   * @param {Object} params 搜索参数
   * @returns {Promise}
   */
  searchUsers(params) {
    const apiParams = {
      keyword: params.keyword || params.search || '',
      department: params.department || '',
      role: params.role || '',
      limit: params.limit || 10
    }
    
    console.log('V1.1 搜索用户，参数:', apiParams)
    return request.get('/api/v1.1/user/search', { params: apiParams })
  },

  /**
   * 批量获取用户信息 (V1.1)
   * @param {Array} userIds 用户ID数组
   * @returns {Promise}
   */
  getUsersByIds(userIds) {
    console.log('V1.1 批量获取用户信息，IDs:', userIds)
    return request.post('/api/v1.1/user/batch', { userIds })
  },

  /**
   * 获取用户权限 (V1.1)
   * @param {number|string} id 用户ID
   * @returns {Promise}
   */
  getUserPermissions(id) {
    console.log('V1.1 获取用户权限，ID:', id)
    return request.get(`/api/v1.1/user/${id}/permissions`)
  },

  /**
   * 更新用户权限 (V1.1)
   * @param {number|string} id 用户ID
   * @param {Object} data 权限数据
   * @returns {Promise}
   */
  updateUserPermissions(id, data) {
    console.log('V1.1 更新用户权限，ID:', id, '数据:', data)
    return request.put(`/api/v1.1/user/${id}/permissions`, data)
  },

  /**
   * 获取用户角色 (V1.1)
   * @param {number|string} id 用户ID
   * @returns {Promise}
   */
  getUserRoles(id) {
    console.log('V1.1 获取用户角色，ID:', id)
    return request.get(`/api/v1.1/user/${id}/roles`)
  },

  /**
   * 更新用户角色 (V1.1)
   * @param {number|string} id 用户ID
   * @param {Object} data 角色数据
   * @returns {Promise}
   */
  updateUserRoles(id, data) {
    console.log('V1.1 更新用户角色，ID:', id, '数据:', data)
    return request.put(`/api/v1.1/user/${id}/roles`, data)
  },

  /**
   * 导出用户数据 (V1.1)
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  exportUsers(params) {
    console.log('V1.1 导出用户数据，参数:', params)
    return request.post('/api/v1.1/user/export', params, {
      responseType: 'blob'
    })
  },

  /**
   * 导入用户数据 (V1.1)
   * @param {FormData} formData 包含Excel文件的表单数据
   * @returns {Promise}
   */
  importUsers(formData) {
    console.log('V1.1 导入用户数据')
    return request.post('/api/v1.1/user/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 健康检查 (V1.1)
   * @returns {Promise}
   */
  healthCheck() {
    return request.get('/api/v1.1/user/health')
  }
}

// 兼容性方法 - 保持与V1 API相同的方法名
userApiV1_1.getUsers = userApiV1_1.getUserList
userApiV1_1.getUserDetail = userApiV1_1.getUserById
userApiV1_1.searchMentionUsers = userApiV1_1.searchUsers

export default userApiV1_1
