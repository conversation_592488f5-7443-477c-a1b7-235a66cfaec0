using System;

namespace ItAssetsSystem.Application.Features.AssetStatistics.Dtos
{
    /// <summary>
    /// 资产趋势查询参数DTO
    /// </summary>
    public class AssetTrendQueryDto
    {
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 统计类型 (total, normal, fault, maintenance)
        /// </summary>
        public string StatType { get; set; } = "total";

        /// <summary>
        /// 资产类型ID
        /// </summary>
        public int? AssetTypeId { get; set; }

        /// <summary>
        /// 区域ID
        /// </summary>
        public int? RegionId { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public int? DepartmentId { get; set; }
    }
}