#!/usr/bin/env python3
"""
创建发布包脚本
将打包好的可执行文件和相关文档打包成发布版本
"""

import os
import sys
import shutil
import zipfile
from pathlib import Path
from datetime import datetime

def create_release_package():
    """创建发布包"""
    print("📦 创建 AugmentCode-Free 发布包...")
    
    # 检查可执行文件是否存在
    exe_name = "AugmentCode-Free"
    if sys.platform == "win32":
        exe_name += ".exe"
    
    exe_path = Path("dist") / exe_name
    if not exe_path.exists():
        print("❌ 找不到可执行文件，请先运行构建脚本")
        return False
    
    # 创建发布目录
    release_dir = Path("release")
    if release_dir.exists():
        shutil.rmtree(release_dir)
    release_dir.mkdir()
    
    # 复制可执行文件
    shutil.copy2(exe_path, release_dir / exe_name)
    print(f"✅ 复制可执行文件: {exe_name}")
    
    # 复制文档
    docs_to_copy = [
        ("README.md", "README.md"),
        ("BUILD_INSTRUCTIONS.md", "构建说明.md"),
    ]
    
    for src, dst in docs_to_copy:
        if Path(src).exists():
            shutil.copy2(src, release_dir / dst)
            print(f"✅ 复制文档: {dst}")
    
    # 创建用户使用说明
    user_guide = """# AugmentCode-Free 使用指南

## 🚀 快速开始

### 运行程序
直接双击可执行文件或在命令行中运行：

**Windows:**
```
AugmentCode-Free.exe
```

**Linux/macOS:**
```
./AugmentCode-Free
```

### 功能说明
1. **清理 VS Code 数据库** - 清理数据库中的特定条目
2. **修改 VS Code 遥测ID** - 修改遥测标识符
3. **运行所有工具** - 执行所有维护操作

## ⚠️ 重要提示

- 使用前请关闭所有 VS Code 实例
- 建议在使用前备份重要数据
- 本工具仅用于合法的软件维护目的

## 🔒 安全说明

- 本程序已编译，源码完全隐藏
- 不包含恶意代码，仅用于VS Code维护
- 可以安全地在离线环境中使用

## 📞 技术支持

如有问题或建议，请联系开发者。

---
构建时间: {build_time}
版本: 1.0.0
"""
    
    build_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    with open(release_dir / "使用指南.txt", "w", encoding="utf-8") as f:
        f.write(user_guide.format(build_time=build_time))
    print("✅ 创建使用指南")
    
    # 创建版本信息文件
    version_info = f"""AugmentCode-Free v1.0.0

构建信息:
- 构建时间: {build_time}
- 平台: {sys.platform}
- Python版本: {sys.version}
- 可执行文件: {exe_name}
- 文件大小: {exe_path.stat().st_size / (1024*1024):.1f} MB

特性:
✅ 源码完全隐藏
✅ 单文件可执行
✅ 无需Python环境
✅ 跨平台支持
✅ 离线运行
"""
    
    with open(release_dir / "版本信息.txt", "w", encoding="utf-8") as f:
        f.write(version_info)
    print("✅ 创建版本信息")
    
    # 创建ZIP压缩包
    zip_name = f"AugmentCode-Free-v1.0.0-{sys.platform}.zip"
    zip_path = Path(zip_name)
    
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in release_dir.rglob('*'):
            if file_path.is_file():
                arcname = file_path.relative_to(release_dir)
                zipf.write(file_path, arcname)
                print(f"📁 添加到压缩包: {arcname}")
    
    print(f"\n🎉 发布包创建完成！")
    print(f"📁 发布目录: {release_dir.absolute()}")
    print(f"📦 压缩包: {zip_path.absolute()}")
    
    # 显示文件列表
    print(f"\n📋 发布包内容:")
    for file_path in release_dir.rglob('*'):
        if file_path.is_file():
            size = file_path.stat().st_size / (1024*1024)
            print(f"  - {file_path.name} ({size:.1f} MB)")
    
    print(f"\n📦 压缩包大小: {zip_path.stat().st_size / (1024*1024):.1f} MB")
    
    return True

def main():
    """主函数"""
    print("🚀 AugmentCode-Free 发布包创建工具")
    print("=" * 50)
    
    if create_release_package():
        print("\n✅ 发布包创建成功！")
        print("现在您可以将压缩包分发给其他用户了。")
    else:
        print("\n❌ 发布包创建失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
