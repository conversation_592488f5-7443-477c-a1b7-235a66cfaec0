// File: Infrastructure/Services/TaskClaimGenerationService.cs
// Description: 任务领取按钮生成后台服务

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Application.Features.Tasks.Services;
using ItAssetsSystem.Core.Abstractions;

namespace ItAssetsSystem.Infrastructure.Services
{
    /// <summary>
    /// 任务领取按钮生成后台服务
    /// </summary>
    public class TaskClaimGenerationService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<TaskClaimGenerationService> _logger;
        private readonly TimeSpan _checkInterval = TimeSpan.FromMinutes(1); // 每分钟检查一次

        public TaskClaimGenerationService(
            IServiceProvider serviceProvider,
            ILogger<TaskClaimGenerationService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("任务领取按钮生成服务已启动");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await ProcessTaskClaimGenerationAsync();
                    await ProcessTaskRemindersAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "处理任务领取生成时发生错误");
                }

                await Task.Delay(_checkInterval, stoppingToken);
            }

            _logger.LogInformation("任务领取按钮生成服务已停止");
        }

        /// <summary>
        /// 处理任务领取按钮生成
        /// </summary>
        private async Task ProcessTaskClaimGenerationAsync()
        {
            using var scope = _serviceProvider.CreateScope();
            var workShiftService = scope.ServiceProvider.GetRequiredService<WorkShiftService>();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<TaskClaimGenerationService>>();

            try
            {
                // 获取当前时间应该生成任务领取按钮的班次
                var shiftsToGenerate = await workShiftService.GetShiftsForTaskClaimGenerationAsync();

                if (shiftsToGenerate.Any())
                {
                    logger.LogInformation("检测到 {Count} 个班次需要生成任务领取按钮", shiftsToGenerate.Count);

                    foreach (var shift in shiftsToGenerate)
                    {
                        await GenerateTaskClaimButtonsForShiftAsync(shift, scope.ServiceProvider);
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "处理任务领取按钮生成时发生错误");
            }
        }

        /// <summary>
        /// 为指定班次生成任务领取按钮
        /// </summary>
        private async Task GenerateTaskClaimButtonsForShiftAsync(
            Domain.Entities.Tasks.WorkShift shift,
            IServiceProvider serviceProvider)
        {
            var logger = serviceProvider.GetRequiredService<ILogger<TaskClaimGenerationService>>();
            var notificationService = serviceProvider.GetRequiredService<INotificationService>();

            try
            {
                // 这里可以实现具体的任务领取按钮生成逻辑
                // 例如：向该班次的所有用户发送通知，提醒他们可以领取任务

                logger.LogInformation("为班次 {ShiftName} ({ShiftCode}) 生成任务领取按钮", 
                    shift.ShiftName, shift.ShiftCode);

                // 发送班次任务领取通知
                var message = $"【{shift.ShiftName}】班次任务领取时间到了，请及时领取今日任务！";
                
                // 这里可以根据实际需求实现通知逻辑
                // await notificationService.NotifyShiftTaskClaimAsync(shift.ShiftId, message);
                
                logger.LogInformation("已为班次 {ShiftName} 发送任务领取通知", shift.ShiftName);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "为班次 {ShiftName} 生成任务领取按钮时发生错误", shift.ShiftName);
            }
        }

        /// <summary>
        /// 处理任务提醒
        /// </summary>
        private async Task ProcessTaskRemindersAsync()
        {
            using var scope = _serviceProvider.CreateScope();
            var taskReminderService = scope.ServiceProvider.GetRequiredService<TaskReminderService>();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<TaskClaimGenerationService>>();

            try
            {
                var processedCount = await taskReminderService.ProcessDueRemindersAsync();
                
                if (processedCount > 0)
                {
                    logger.LogInformation("处理了 {Count} 个到期任务提醒", processedCount);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "处理任务提醒时发生错误");
            }
        }
    }
}
