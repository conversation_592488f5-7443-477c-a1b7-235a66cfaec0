# 任务查询API测试指南

## 问题诊断步骤

### 1. 检查服务状态
确保后端服务正在运行：
```bash
dotnet run
```

### 2. 测试API直接调用
使用curl或Postman测试API端点：

```bash
# 测试任务列表API
curl -X GET "http://localhost:5001/api/v2/tasks?pageNumber=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json"
```

### 3. 查看后端日志
关注以下日志信息：
- `[INF] 开始优化批量映射{Count}个任务`
- `[INF] 收集到的ID统计`
- `[INF] 使用优化的批量映射方法成功处理{Count}个任务`
- `[WRN] 优化的批量映射方法失败，降级到原始方法`

### 4. 前端错误排查
检查浏览器开发者工具的网络标签页：
1. 查看请求URL是否正确：`/api/v2/tasks`
2. 查看HTTP状态码
3. 查看响应内容

### 5. 常见问题和解决方案

#### 问题1：认证错误 (401 Unauthorized)
- **症状**：前端显示"获取任务列表失败"，后端返回401
- **解决**：检查JWT token是否有效，用户是否已登录

#### 问题2：路由不匹配 (404 Not Found)
- **症状**：API调用返回404
- **解决**：确认控制器路由配置正确

#### 问题3：优化方法异常 (500 Internal Server Error)
- **症状**：看到降级日志，使用原始方法
- **解决**：检查批量查询方法的实现

## 测试用例

### 基本功能测试
1. **空列表测试**
   ```json
   GET /api/v2/tasks?pageNumber=1&pageSize=10
   ```

2. **带筛选条件测试**
   ```json
   GET /api/v2/tasks?status=Todo&priority=High&pageNumber=1&pageSize=10
   ```

3. **搜索测试**
   ```json
   GET /api/v2/tasks?searchTerm=测试&pageNumber=1&pageSize=10
   ```

### 性能测试
1. **大量数据测试**
   ```json
   GET /api/v2/tasks?pageNumber=1&pageSize=50
   ```

2. **响应时间测试**
   - 记录API调用开始时间
   - 记录响应完成时间
   - 计算总耗时

## 预期结果

### 成功响应格式
```json
{
  "success": true,
  "data": [
    {
      "taskId": 1,
      "name": "任务名称",
      "status": "Todo",
      "priority": "Medium",
      "assigneeUserId": 1,
      "assigneeUserName": "张三",
      "creatorUserId": 1,
      "creatorUserName": "李四",
      "subTaskCount": 0,
      "commentCount": 2,
      "attachmentCount": 1,
      // ... 其他字段
    }
  ],
  "message": "获取任务列表成功"
}
```

### 优化效果指标
- **响应时间**：应从1.9秒减少到200-500毫秒
- **数据库查询**：应从N+1次减少到7次固定查询
- **日志输出**：应看到优化相关的成功日志

## 故障排除

### 如果优化方法失败
1. 检查TaskRepository中批量方法的实现
2. 检查CoreDataQueryService中批量方法的实现
3. 查看具体的异常堆栈信息
4. 系统会自动降级到原始方法，保证功能可用

### 如果完全无法访问
1. 检查服务是否启动
2. 检查端口是否正确（默认5001）
3. 检查防火墙设置
4. 检查数据库连接

## 临时解决方案

如果优化版本有问题，可以临时禁用优化：

在TaskService.cs的GetTasksAsync方法中，将：
```csharp
var taskDtos = await MapTasksToDtosOptimizedAsync(tasks);
```

替换为：
```csharp
var taskDtos = new List<TaskDto>();
foreach (var task in tasks)
{
    var taskDto = await MapTaskToDtoAsync(task);
    if (taskDto != null) taskDtos.Add(taskDto);
}
```

这样可以恢复到原始的工作方式。