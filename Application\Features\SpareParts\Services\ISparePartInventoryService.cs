// File: Application/Features/SpareParts/Services/ISparePartInventoryService.cs
// Description: 备品备件库存状态管理服务接口

using System.Threading.Tasks;
using System.Collections.Generic;
using ItAssetsSystem.Application.Features.SpareParts.Dtos;
using ItAssetsSystem.Application.Common.Dtos;

namespace ItAssetsSystem.Application.Features.SpareParts.Services
{
    /// <summary>
    /// 备品备件库存状态管理服务接口
    /// </summary>
    public interface ISparePartInventoryService
    {
        /// <summary>
        /// 获取备件库存汇总
        /// </summary>
        /// <param name="partId">备件ID</param>
        /// <returns>库存汇总信息</returns>
        Task<SparePartStockSummaryDto> GetStockSummaryAsync(long partId);
        
        /// <summary>
        /// 获取备件库存明细列表
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>库存明细列表</returns>
        Task<PaginatedResult<SparePartInventoryDto>> GetInventoriesAsync(SparePartInventoryQuery query);
        
        /// <summary>
        /// 获取所有状态类型
        /// </summary>
        /// <returns>状态类型列表</returns>
        Task<List<SparePartStatusTypeDto>> GetStatusTypesAsync();
        
        /// <summary>
        /// 批量更新库存状态
        /// </summary>
        /// <param name="request">状态更新请求</param>
        /// <returns>更新结果</returns>
        Task<BatchStatusUpdateResultDto> BatchUpdateStatusAsync(BatchStatusUpdateRequest request);
        
        /// <summary>
        /// 获取库存状态变更历史
        /// </summary>
        /// <param name="inventoryId">库存明细ID</param>
        /// <param name="query">分页参数</param>
        /// <returns>状态变更历史</returns>
        Task<PaginatedResult<SparePartStatusHistoryDto>> GetStatusHistoryAsync(long inventoryId, PaginationQuery query);
        
        /// <summary>
        /// 创建返厂维修单
        /// </summary>
        /// <param name="request">返厂维修单创建请求</param>
        /// <returns>创建结果</returns>
        Task<CreateRepairOrderResultDto> CreateRepairOrderAsync(CreateRepairOrderRequest request);

        /// <summary>
        /// 状态调整
        /// </summary>
        /// <param name="request">状态调整请求</param>
        /// <returns>调整结果</returns>
        Task<StatusAdjustmentResultDto> AdjustStatusAsync(StatusAdjustmentRequest request);
    }
}
