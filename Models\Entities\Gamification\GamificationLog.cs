using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Models.Entities.Gamification
{
    /// <summary>
    /// 游戏化事件日志实体
    /// </summary>
    [Table("gamification_log")]
    public class GamificationLog
    {
        /// <summary>
        /// 日志ID
        /// </summary>
        [Key]
        [Column("LogId")]
        public long LogId { get; set; }

        /// <summary>
        /// 关联用户ID
        /// </summary>
        [Column("UserId")]
        public long UserId { get; set; }

        /// <summary>
        /// 事件发生时间
        /// </summary>
        [Column("Timestamp")]
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 事件类型
        /// </summary>
        [Column("EventType")]
        [MaxLength(100)]
        public string EventType { get; set; } = string.Empty;

        /// <summary>
        /// 经验值变动
        /// </summary>
        [Column("XPChange")]
        public int XPChange { get; set; } = 0;

        /// <summary>
        /// 积分变动
        /// </summary>
        [Column("PointsChange")]
        public int PointsChange { get; set; } = 0;

        /// <summary>
        /// 变动前等级
        /// </summary>
        [Column("LevelBefore")]
        public int? LevelBefore { get; set; }

        /// <summary>
        /// 变动后等级
        /// </summary>
        [Column("LevelAfter")]
        public int? LevelAfter { get; set; }

        /// <summary>
        /// 变动原因文字描述
        /// </summary>
        [Column("Reason")]
        [MaxLength(255)]
        public string? Reason { get; set; }

        /// <summary>
        /// 关联的任务ID
        /// </summary>
        [Column("RelatedTaskId")]
        public long? RelatedTaskId { get; set; }

        /// <summary>
        /// 关联的徽章ID
        /// </summary>
        [Column("RelatedBadgeId")]
        public long? RelatedBadgeId { get; set; }

        /// <summary>
        /// 关联的物品ID
        /// </summary>
        [Column("RelatedItemId")]
        public long? RelatedItemId { get; set; }

        /// <summary>
        /// 其他元数据 (JSON格式)
        /// </summary>
        [Column("Metadata")]
        public string? Metadata { get; set; }
    }

    /// <summary>
    /// 游戏化事件类型
    /// </summary>
    public static class GamificationEventType
    {
        /// <summary>
        /// 任务完成
        /// </summary>
        public const string TaskCompleted = "TaskCompleted";

        /// <summary>
        /// 任务领取
        /// </summary>
        public const string TaskClaimed = "TaskClaimed";

        /// <summary>
        /// 任务创建
        /// </summary>
        public const string TaskCreated = "TaskCreated";

        /// <summary>
        /// 任务更新
        /// </summary>
        public const string TaskUpdated = "TaskUpdated";

        /// <summary>
        /// 徽章获得
        /// </summary>
        public const string BadgeEarned = "BadgeEarned";

        /// <summary>
        /// 等级提升
        /// </summary>
        public const string LevelUp = "LevelUp";

        /// <summary>
        /// 积分消费
        /// </summary>
        public const string PointsSpent = "PointsSpent";

        /// <summary>
        /// 每日登录
        /// </summary>
        public const string DailyLogin = "DailyLogin";

        /// <summary>
        /// 评论添加
        /// </summary>
        public const string CommentAdded = "CommentAdded";

        /// <summary>
        /// 连续完成任务
        /// </summary>
        public const string StreakMaintained = "StreakMaintained";

        /// <summary>
        /// 按时完成任务
        /// </summary>
        public const string OnTimeCompletion = "OnTimeCompletion";
    }
}
