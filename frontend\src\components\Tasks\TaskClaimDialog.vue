<template>
  <el-dialog
    v-model="dialogVisible"
    title="任务领取"
    width="600px"
    :before-close="handleClose"
  >
    <div class="task-claim-content">
      <!-- 可领取任务列表 -->
      <div class="form-section">
        <label class="form-label">选择要领取的任务:</label>
        <el-select
          v-model="selectedTaskId"
          placeholder="请选择任务"
          filterable
          clearable
          style="width: 100%"
          :loading="loadingTasks"
        >
          <el-option
            v-for="task in availableTasks"
            :key="task.taskId"
            :label="`${task.name} (${task.priority})`"
            :value="task.taskId"
          >
            <div class="task-option">
              <div class="task-info">
                <span class="task-name">{{ task.name }}</span>
                <el-tag 
                  :type="getPriorityType(task.priority)" 
                  size="small"
                  class="task-priority"
                >
                  {{ task.priority }}
                </el-tag>
              </div>
              <div class="task-meta">
                <span class="task-type">{{ task.taskType }}</span>
                <span v-if="task.planEndDate" class="task-deadline">
                  截止: {{ formatDate(task.planEndDate) }}
                </span>
              </div>
            </div>
          </el-option>
        </el-select>
      </div>

      <!-- 任务详情预览 -->
      <div v-if="selectedTask" class="task-preview">
        <h4>任务详情</h4>
        <div class="task-details">
          <div class="detail-row">
            <span class="label">任务名称:</span>
            <span class="value">{{ selectedTask.name }}</span>
          </div>
          <div class="detail-row">
            <span class="label">任务描述:</span>
            <span class="value">{{ selectedTask.description || '无描述' }}</span>
          </div>
          <div class="detail-row">
            <span class="label">优先级:</span>
            <el-tag :type="getPriorityType(selectedTask.priority)" size="small">
              {{ selectedTask.priority }}
            </el-tag>
          </div>
          <div class="detail-row" v-if="selectedTask.planEndDate">
            <span class="label">计划完成时间:</span>
            <span class="value">{{ formatDateTime(selectedTask.planEndDate) }}</span>
          </div>
        </div>
      </div>

      <!-- 备注 -->
      <div class="form-section">
        <label class="form-label">备注 (可选):</label>
        <el-input
          v-model="notes"
          type="textarea"
          :rows="3"
          placeholder="请输入领取任务的备注信息..."
          maxlength="500"
          show-word-limit
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleClaim"
          :loading="claiming"
          :disabled="!selectedTaskId"
        >
          确认领取
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import workShiftApi from '@/api/workShift'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loadingTasks = ref(false)
const claiming = ref(false)
const availableTasks = ref([])
const selectedTaskId = ref(null)
const notes = ref('')

// 计算属性
const selectedTask = computed(() => {
  return availableTasks.value.find(task => task.taskId === selectedTaskId.value)
})

// 方法
const loadAvailableTasks = async () => {
  loadingTasks.value = true
  try {
    const response = await workShiftApi.getAvailableTasks()
    if (response.success) {
      availableTasks.value = response.data?.items || []
    } else {
      ElMessage.error('获取可领取任务失败')
    }
  } catch (error) {
    console.error('获取可领取任务失败:', error)
    ElMessage.error('获取可领取任务失败')
  } finally {
    loadingTasks.value = false
  }
}

const handleClaim = async () => {
  if (!selectedTaskId.value) {
    ElMessage.warning('请选择要领取的任务')
    return
  }

  claiming.value = true
  try {
    const response = await workShiftApi.claimTask({
      taskId: selectedTaskId.value,
      notes: notes.value
    })

    if (response.success) {
      ElMessage.success('任务领取成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(response.message || '任务领取失败')
    }
  } catch (error) {
    console.error('任务领取失败:', error)
    ElMessage.error('任务领取失败')
  } finally {
    claiming.value = false
  }
}

const handleClose = () => {
  selectedTaskId.value = null
  notes.value = ''
  dialogVisible.value = false
}

const getPriorityType = (priority) => {
  const typeMap = {
    'Critical': 'danger',
    'High': 'warning',
    'Medium': 'primary',
    'Low': 'info'
  }
  return typeMap[priority] || 'primary'
}

const formatDate = (dateStr) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateStr) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible) {
    loadAvailableTasks()
  }
})
</script>

<style scoped>
.task-claim-content {
  padding: 0 4px;
}

.form-section {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #303133;
}

.task-option {
  padding: 8px 0;
}

.task-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.task-name {
  font-weight: 500;
  color: #303133;
}

.task-priority {
  margin-left: 8px;
}

.task-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #909399;
}

.task-preview {
  background-color: #f5f7fa;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 20px;
}

.task-preview h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
}

.task-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.detail-row .label {
  min-width: 100px;
  font-size: 13px;
  color: #606266;
}

.detail-row .value {
  flex: 1;
  font-size: 13px;
  color: #303133;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
