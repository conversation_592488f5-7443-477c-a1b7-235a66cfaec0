<template>
  <div class="workstation-details">
    <div class="details-header">
      <div class="workstation-icon" :class="`status-${workstation.status}`">
        <el-icon size="32">
          <Check v-if="workstation.status === 'operational'" />
          <Warning v-else-if="workstation.status === 'warning'" />
          <Close v-else-if="workstation.status === 'error'" />
          <Setting v-else />
        </el-icon>
      </div>
      <div class="workstation-info">
        <h2>{{ workstation.locationName }}</h2>
        <p class="workstation-code">编号: {{ workstation.locationCode }}</p>
        <el-tag :type="getStatusType(workstation.status)" size="large">
          {{ getStatusText(workstation.status) }}
        </el-tag>
      </div>
    </div>

    <!-- Key Metrics -->
    <div class="metrics-section">
      <h3>关键指标</h3>
      <div class="metrics-grid">
        <div class="metric-card">
          <div class="metric-icon efficiency">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="metric-content">
            <div class="metric-value" :class="getEfficiencyClass(workstation.efficiency)">
              {{ workstation.efficiency }}%
            </div>
            <div class="metric-label">生产效率</div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-icon uptime">
            <el-icon><Timer /></el-icon>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ workstation.uptime }}%</div>
            <div class="metric-label">设备开机率</div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-icon assets">
            <el-icon><Box /></el-icon>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ workstation.assetCount }}</div>
            <div class="metric-label">设备数量</div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-icon tasks">
            <el-icon><Document /></el-icon>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ workstation.taskCount }}</div>
            <div class="metric-label">活跃任务</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Status Information -->
    <div class="status-section" v-if="workstation.faultCount > 0">
      <h3>状态信息</h3>
      <div class="status-alerts">
        <div class="alert-item" :class="workstation.status">
          <el-icon class="alert-icon">
            <Warning v-if="workstation.status === 'warning'" />
            <Close v-else />
          </el-icon>
          <div class="alert-content">
            <div class="alert-title">
              {{ workstation.status === 'error' ? '设备故障' : '性能警告' }}
            </div>
            <div class="alert-description">
              检测到 {{ workstation.faultCount }} 个异常项，需要及时处理
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="activity-section">
      <h3>最近活动</h3>
      <div class="activity-list">
        <div class="activity-item">
          <div class="activity-time">2分钟前</div>
          <div class="activity-desc">设备状态检查完成</div>
        </div>
        <div class="activity-item">
          <div class="activity-time">15分钟前</div>
          <div class="activity-desc">生产效率更新</div>
        </div>
        <div class="activity-item">
          <div class="activity-time">1小时前</div>
          <div class="activity-desc">日常维护记录</div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-section">
      <el-button type="primary" @click="runDiagnostics">
        <el-icon><Tools /></el-icon>
        设备诊断
      </el-button>
      <el-button @click="viewReports">
        <el-icon><Document /></el-icon>
        查看报告
      </el-button>
      <el-button @click="scheduleMaintenance">
        <el-icon><Calendar /></el-icon>
        安排维护
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { 
  Check, Warning, Close, Setting, Timer, 
  Box, Document, Tools, Calendar, TrendCharts
} from '@element-plus/icons-vue'

const props = defineProps({
  workstation: {
    type: Object,
    required: true
  },
  details: {
    type: Object,
    default: () => ({})
  }
})

const getStatusType = (status) => {
  const types = {
    operational: 'success',
    warning: 'warning', 
    error: 'danger',
    idle: 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    operational: '正常运行',
    warning: '警告状态',
    error: '故障状态', 
    idle: '空闲状态'
  }
  return texts[status] || '未知状态'
}

const getEfficiencyClass = (efficiency) => {
  if (efficiency >= 80) return 'high'
  if (efficiency >= 60) return 'medium'
  return 'low'
}

const runDiagnostics = () => {
  ElMessage.info('正在启动设备诊断...')
}

const viewReports = () => {
  ElMessage.info('正在生成工位报告...')
}

const scheduleMaintenance = () => {
  ElMessage.info('正在安排维护计划...')
}
</script>

<style scoped>
.workstation-details {
  padding: 0;
}

/* Header */
.details-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(18, 39, 56, 0.9), rgba(26, 54, 80, 0.8));
  border-radius: 12px;
  margin-bottom: 1.5rem;
}

.workstation-icon {
  width: 80px;
  height: 80px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.workstation-icon.status-operational {
  background: linear-gradient(135deg, #10b981, #059669);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.workstation-icon.status-warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
}

.workstation-icon.status-error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

.workstation-icon.status-idle {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.workstation-info {
  flex: 1;
}

.workstation-info h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #e2e8f0;
}

.workstation-code {
  margin: 0 0 1rem 0;
  color: #94a3b8;
  font-size: 0.875rem;
}

/* Metrics */
.metrics-section {
  margin-bottom: 1.5rem;
}

.metrics-section h3 {
  margin: 0 0 1rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #e2e8f0;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.metric-card {
  background: rgba(26, 54, 80, 0.6);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
}

.metric-card:hover {
  border-color: rgba(59, 130, 246, 0.4);
  background: rgba(26, 54, 80, 0.8);
}

.metric-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.metric-icon.efficiency {
  background: linear-gradient(135deg, #10b981, #059669);
}

.metric-icon.uptime {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.metric-icon.assets {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.metric-icon.tasks {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #e2e8f0;
  line-height: 1;
}

.metric-value.high {
  color: #10b981;
}

.metric-value.medium {
  color: #f59e0b;
}

.metric-value.low {
  color: #ef4444;
}

.metric-label {
  font-size: 0.75rem;
  color: #94a3b8;
  margin-top: 0.25rem;
}

/* Status Alerts */
.status-section {
  margin-bottom: 1.5rem;
}

.status-section h3 {
  margin: 0 0 1rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #e2e8f0;
}

.status-alerts {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  border-radius: 12px;
  border-left: 4px solid;
}

.alert-item.warning {
  background: rgba(245, 158, 11, 0.1);
  border-left-color: #f59e0b;
}

.alert-item.error {
  background: rgba(239, 68, 68, 0.1);
  border-left-color: #ef4444;
}

.alert-icon {
  color: #f59e0b;
  margin-top: 0.125rem;
}

.alert-item.error .alert-icon {
  color: #ef4444;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 0.25rem;
}

.alert-description {
  font-size: 0.875rem;
  color: #94a3b8;
}

/* Activity */
.activity-section {
  margin-bottom: 1.5rem;
}

.activity-section h3 {
  margin: 0 0 1rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #e2e8f0;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.activity-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(26, 54, 80, 0.4);
  border-radius: 8px;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.activity-time {
  font-size: 0.75rem;
  color: #94a3b8;
  white-space: nowrap;
}

.activity-desc {
  font-size: 0.875rem;
  color: #e2e8f0;
  text-align: right;
}

/* Actions */
.action-section {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
  padding-top: 1rem;
  border-top: 1px solid rgba(59, 130, 246, 0.2);
}

.action-section .el-button {
  flex: 1;
  min-width: 120px;
}

/* Responsive */
@media (max-width: 640px) {
  .details-header {
    flex-direction: column;
    text-align: center;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .action-section {
    flex-direction: column;
  }
  
  .action-section .el-button {
    flex: none;
  }
}

/* Element Plus Overrides */
:deep(.el-tag) {
  border: none;
  font-weight: 500;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border: none;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}
</style>