// File: frontend/src/api/statistics.js
// Description: 统计分析API接口，支持动态查询和数据分析

import request from '@/utils/request'

const API_BASE = '/v2/statistics'

export const statisticsApi = {
  /**
   * 执行动态统计查询
   * @param {Object} queryRequest 查询请求参数
   * @returns {Promise} 查询结果
   */
  executeQuery(queryRequest) {
    return request({
      url: `${API_BASE}/query`,
      method: 'post',
      data: queryRequest
    })
  },

  /**
   * 获取支持的查询维度列表
   * @returns {Promise} 维度列表
   */
  getDimensions() {
    return request({
      url: `${API_BASE}/dimensions`,
      method: 'get'
    })
  },

  /**
   * 获取支持的度量指标列表
   * @returns {Promise} 度量指标列表
   */
  getMetrics() {
    return request({
      url: `${API_BASE}/metrics`,
      method: 'get'
    })
  },

  /**
   * 导出统计数据
   * @param {Object} queryRequest 查询请求参数
   * @param {String} format 导出格式 (excel, csv)
   * @returns {Promise} 导出结果
   */
  exportData(queryRequest, format = 'excel') {
    return request({
      url: `${API_BASE}/export`,
      method: 'post',
      data: {
        ...queryRequest,
        format
      },
      responseType: 'blob'
    })
  },

  /**
   * 获取预定义分析模板
   * @returns {Promise} 分析模板列表
   */
  getAnalysisTemplates() {
    return request({
      url: `${API_BASE}/templates`,
      method: 'get'
    })
  },

  /**
   * 保存分析模板
   * @param {Object} template 模板数据
   * @returns {Promise} 保存结果
   */
  saveAnalysisTemplate(template) {
    return request({
      url: `${API_BASE}/templates`,
      method: 'post',
      data: template
    })
  },

  /**
   * 获取资产快照数据
   * @param {Object} params 包含日期等筛选条件的参数
   * @returns {Promise} 资产快照数据
   */
  getAssetSnapshots(params) {
    return request({
      url: '/v2/assetsnapshots', // 符合RESTful风格的 V2 API 路由
      method: 'get',
      params
    })
  }
}

/**
 * 执行动态统计查询
 * @param {object} query - 包含 dimensions, metrics, filters 的查询对象
 * @returns {Promise}
 */
export function executeDynamicQuery(query) {
  return request({
    url: '/v2/statistics/query', // 正确的 V2 API 路由
    method: 'post',
    data: query // 使用 POST 并将查询对象放在请求体中
  })
}

/**
 * 获取资产快照数据
 * @param {object} params - 包含日期等筛选条件的参数
 * @returns {Promise}
 */
export function getAssetSnapshot(params) {
  return request({
    url: '/v2/assetsnapshots', // 符合RESTful风格的 V2 API 路由
    method: 'get',
    params
  })
}

export default statisticsApi