using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models.Entities.Gamification;
using ItAssetsSystem.Models.DTOs.Gamification;
using ItAssetsSystem.Services.Interfaces;
using System.Text.Json;

namespace ItAssetsSystem.Services
{
    /// <summary>
    /// 游戏化服务实现
    /// </summary>
    public class GamificationService : IGamificationService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<GamificationService> _logger;

        // 游戏化配置常量
        private const int BASE_XP_PER_LEVEL = 100;
        private const int TASK_CLAIM_XP = 5;
        private const int TASK_CLAIM_POINTS = 10;
        private const int TASK_COMPLETE_XP = 20;
        private const int TASK_COMPLETE_POINTS = 50;
        private const int TASK_ONTIME_BONUS_XP = 10;
        private const int TASK_ONTIME_BONUS_POINTS = 20;
        private const int TASK_CREATE_XP = 3;
        private const int TASK_CREATE_POINTS = 5;
        private const int TASK_UPDATE_XP = 2;
        private const int TASK_UPDATE_POINTS = 3;

        public GamificationService(AppDbContext context, ILogger<GamificationService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 获取用户游戏化统计信息
        /// </summary>
        public async Task<GamificationUserStatsDto?> GetUserStatsAsync(int userId)
        {
            try
            {
                var userStats = await _context.GamificationUserStats
                    .Include(s => s.User)
                    .FirstOrDefaultAsync(s => s.CoreUserId == userId);

                if (userStats == null)
                {
                    // 如果用户没有游戏化数据，初始化一个
                    return await InitializeUserStatsAsync(userId);
                }

                var currentLevelXP = CalculateXPForLevel(userStats.CurrentLevel);
                var nextLevelXP = CalculateXPForLevel(userStats.CurrentLevel + 1);
                var levelProgress = nextLevelXP > currentLevelXP 
                    ? (decimal)(userStats.CurrentXP - currentLevelXP) / (nextLevelXP - currentLevelXP) * 100 
                    : 100;

                return new GamificationUserStatsDto
                {
                    UserId = userId,
                    UserName = userStats.User?.Name ?? "未知用户",
                    CurrentXP = userStats.CurrentXP,
                    CurrentLevel = userStats.CurrentLevel,
                    CurrentLevelXP = currentLevelXP,
                    NextLevelXP = nextLevelXP,
                    LevelProgress = Math.Max(0, Math.Min(100, levelProgress)),
                    PointsBalance = userStats.PointsBalance,
                    CompletedTasksCount = userStats.CompletedTasksCount,
                    OnTimeTasksCount = userStats.OnTimeTasksCount,
                    OnTimeRate = userStats.CompletedTasksCount > 0 
                        ? (decimal)userStats.OnTimeTasksCount / userStats.CompletedTasksCount * 100 
                        : 0,
                    StreakCount = userStats.StreakCount,
                    LastActivityTimestamp = userStats.LastActivityTimestamp
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户 {UserId} 游戏化统计信息时发生错误", userId);
                return null;
            }
        }

        /// <summary>
        /// 初始化用户游戏化数据
        /// </summary>
        public async Task<GamificationUserStatsDto> InitializeUserStatsAsync(int userId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    throw new ArgumentException($"用户 {userId} 不存在");
                }

                var userStats = new GamificationUserStats
                {
                    CoreUserId = userId,
                    CurrentXP = 0,
                    CurrentLevel = 1,
                    PointsBalance = 0,
                    CompletedTasksCount = 0,
                    OnTimeTasksCount = 0,
                    StreakCount = 0,
                    LastActivityTimestamp = DateTime.Now,
                    LastUpdatedTimestamp = DateTime.Now
                };

                _context.GamificationUserStats.Add(userStats);
                await _context.SaveChangesAsync();

                // 记录初始化事件
                await LogEventAsync(userId, "UserInitialized", 0, 0, "用户游戏化数据初始化");

                return new GamificationUserStatsDto
                {
                    UserId = userId,
                    UserName = user.Name ?? "未知用户",
                    CurrentXP = 0,
                    CurrentLevel = 1,
                    CurrentLevelXP = 0,
                    NextLevelXP = CalculateXPForLevel(2),
                    LevelProgress = 0,
                    PointsBalance = 0,
                    CompletedTasksCount = 0,
                    OnTimeTasksCount = 0,
                    OnTimeRate = 0,
                    StreakCount = 0,
                    LastActivityTimestamp = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化用户 {UserId} 游戏化数据时发生错误", userId);
                throw;
            }
        }

        /// <summary>
        /// 任务领取奖励
        /// </summary>
        public async Task<GamificationRewardDto> ClaimTaskRewardAsync(int userId, long taskId)
        {
            try
            {
                var userStats = await GetOrCreateUserStatsAsync(userId);
                var oldLevel = userStats.CurrentLevel;

                // 奖励配置
                var xpGained = TASK_CLAIM_XP;
                var pointsGained = TASK_CLAIM_POINTS;

                // 更新用户统计
                userStats.CurrentXP += xpGained;
                userStats.PointsBalance += pointsGained;
                userStats.LastActivityTimestamp = DateTime.Now;
                userStats.LastUpdatedTimestamp = DateTime.Now;

                // 检查是否升级
                var newLevel = CalculateLevelFromXP(userStats.CurrentXP);
                var leveledUp = newLevel > oldLevel;
                if (leveledUp)
                {
                    userStats.CurrentLevel = newLevel;
                }

                await _context.SaveChangesAsync();

                // 记录事件日志
                await LogEventAsync(userId, GamificationEventType.TaskClaimed, xpGained, pointsGained, 
                    "任务领取奖励", taskId);

                if (leveledUp)
                {
                    await LogEventAsync(userId, GamificationEventType.LevelUp, 0, 0, 
                        $"升级到等级 {newLevel}", taskId);
                }

                return new GamificationRewardDto
                {
                    Success = true,
                    Message = "任务领取成功！",
                    XPGained = xpGained,
                    PointsGained = pointsGained,
                    LeveledUp = leveledUp,
                    NewLevel = leveledUp ? newLevel : null,
                    RewardDetails = $"获得 {xpGained} 经验值和 {pointsGained} 金币"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理用户 {UserId} 任务 {TaskId} 领取奖励时发生错误", userId, taskId);
                return new GamificationRewardDto
                {
                    Success = false,
                    Message = "奖励发放失败: " + ex.Message
                };
            }
        }

        /// <summary>
        /// 任务完成奖励
        /// </summary>
        public async Task<GamificationRewardDto> CompleteTaskRewardAsync(int userId, long taskId, bool isOnTime = false)
        {
            try
            {
                var userStats = await GetOrCreateUserStatsAsync(userId);
                var oldLevel = userStats.CurrentLevel;

                // 基础奖励
                var xpGained = TASK_COMPLETE_XP;
                var pointsGained = TASK_COMPLETE_POINTS;

                // 按时完成奖励
                if (isOnTime)
                {
                    xpGained += TASK_ONTIME_BONUS_XP;
                    pointsGained += TASK_ONTIME_BONUS_POINTS;
                    userStats.OnTimeTasksCount++;
                }

                // 更新用户统计
                userStats.CurrentXP += xpGained;
                userStats.PointsBalance += pointsGained;
                userStats.CompletedTasksCount++;
                userStats.LastActivityTimestamp = DateTime.Now;
                userStats.LastUpdatedTimestamp = DateTime.Now;

                // 检查是否升级
                var newLevel = CalculateLevelFromXP(userStats.CurrentXP);
                var leveledUp = newLevel > oldLevel;
                if (leveledUp)
                {
                    userStats.CurrentLevel = newLevel;
                }

                await _context.SaveChangesAsync();

                // 记录事件日志
                var eventType = isOnTime ? GamificationEventType.OnTimeCompletion : GamificationEventType.TaskCompleted;
                var reason = isOnTime ? "按时完成任务奖励" : "任务完成奖励";
                
                await LogEventAsync(userId, eventType, xpGained, pointsGained, reason, taskId);

                if (leveledUp)
                {
                    await LogEventAsync(userId, GamificationEventType.LevelUp, 0, 0, 
                        $"升级到等级 {newLevel}", taskId);
                }

                var rewardDetails = $"获得 {xpGained} 经验值和 {pointsGained} 金币";
                if (isOnTime)
                {
                    rewardDetails += " (包含按时完成奖励)";
                }

                return new GamificationRewardDto
                {
                    Success = true,
                    Message = "任务完成！",
                    XPGained = xpGained,
                    PointsGained = pointsGained,
                    LeveledUp = leveledUp,
                    NewLevel = leveledUp ? newLevel : null,
                    RewardDetails = rewardDetails
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理用户 {UserId} 任务 {TaskId} 完成奖励时发生错误", userId, taskId);
                return new GamificationRewardDto
                {
                    Success = false,
                    Message = "奖励发放失败: " + ex.Message
                };
            }
        }

        /// <summary>
        /// 任务创建奖励
        /// </summary>
        public async Task<GamificationRewardDto> CreateTaskRewardAsync(int userId, long taskId)
        {
            return await ProcessSimpleRewardAsync(userId, taskId, TASK_CREATE_XP, TASK_CREATE_POINTS, 
                GamificationEventType.TaskCreated, "任务创建奖励");
        }

        /// <summary>
        /// 任务更新奖励
        /// </summary>
        public async Task<GamificationRewardDto> UpdateTaskRewardAsync(int userId, long taskId)
        {
            return await ProcessSimpleRewardAsync(userId, taskId, TASK_UPDATE_XP, TASK_UPDATE_POINTS,
                GamificationEventType.TaskUpdated, "任务更新奖励");
        }

        /// <summary>
        /// 获取用户每日任务统计
        /// </summary>
        public async Task<DailyTaskStatsDto> GetDailyTaskStatsAsync(int userId, DateTime date)
        {
            try
            {
                var targetDate = date.Date;

                // 获取当日任务领取记录
                var claims = await _context.TaskClaims
                    .Where(c => c.ClaimedBy == userId && c.ClaimDate.Date == targetDate)
                    .ToListAsync();

                // 获取当日游戏化日志
                var logs = await _context.GamificationLogs
                    .Where(l => l.UserId == userId && l.Timestamp.Date == targetDate)
                    .ToListAsync();

                var claimedCount = claims.Count;
                var completedCount = claims.Count(c => c.ClaimStatus == "Completed");
                var onTimeCount = claims.Count(c => c.ClaimStatus == "Completed" && c.CompletedAt <= c.Task?.PlanEndDate);

                var totalXP = logs.Sum(l => l.XPChange);
                var totalPoints = logs.Sum(l => l.PointsChange);

                return new DailyTaskStatsDto
                {
                    UserId = userId,
                    Date = targetDate,
                    ClaimedTasksCount = claimedCount,
                    CompletedTasksCount = completedCount,
                    OnTimeTasksCount = onTimeCount,
                    TotalXPGained = totalXP,
                    TotalPointsGained = totalPoints,
                    CompletionRate = claimedCount > 0 ? (decimal)completedCount / claimedCount * 100 : 0,
                    OnTimeRate = completedCount > 0 ? (decimal)onTimeCount / completedCount * 100 : 0
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户 {UserId} 日期 {Date} 每日任务统计时发生错误", userId, date);
                throw;
            }
        }

        /// <summary>
        /// 获取用户排行榜信息
        /// </summary>
        public async Task<UserLeaderboardDto?> GetUserLeaderboardAsync(int userId, LeaderboardType leaderboardType)
        {
            try
            {
                var leaderboard = await GetTopLeaderboardAsync(leaderboardType, 100);
                return leaderboard.FirstOrDefault(u => u.UserId == userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户 {UserId} 排行榜信息时发生错误", userId);
                return null;
            }
        }

        /// <summary>
        /// 获取排行榜前N名
        /// </summary>
        public async Task<List<UserLeaderboardDto>> GetTopLeaderboardAsync(LeaderboardType leaderboardType, int topN = 10)
        {
            try
            {
                var query = _context.GamificationUserStats
                    .Include(s => s.User)
                    .AsQueryable();

                // 根据排行榜类型过滤数据
                DateTime startDate;
                string periodName;

                switch (leaderboardType)
                {
                    case LeaderboardType.Weekly:
                        startDate = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
                        periodName = "本周";
                        break;
                    case LeaderboardType.Monthly:
                        startDate = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
                        periodName = "本月";
                        break;
                    case LeaderboardType.AllTime:
                    default:
                        startDate = DateTime.MinValue;
                        periodName = "总榜";
                        break;
                }

                var leaderboard = await query
                    .Where(s => s.LastActivityTimestamp >= startDate)
                    .OrderByDescending(s => s.PointsBalance)
                    .ThenByDescending(s => s.CurrentXP)
                    .ThenByDescending(s => s.CompletedTasksCount)
                    .Take(topN)
                    .Select(s => new UserLeaderboardDto
                    {
                        UserId = s.CoreUserId,
                        UserName = s.User.Name ?? "未知用户",
                        Department = s.User.Department,
                        Points = s.PointsBalance,
                        Level = s.CurrentLevel,
                        CompletedTasksCount = s.CompletedTasksCount,
                        StreakCount = s.StreakCount,
                        LeaderboardType = leaderboardType.ToString(),
                        LeaderboardPeriod = periodName
                    })
                    .ToListAsync();

                // 设置排名
                for (int i = 0; i < leaderboard.Count; i++)
                {
                    leaderboard[i].Rank = i + 1;
                }

                return leaderboard;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取排行榜时发生错误");
                return new List<UserLeaderboardDto>();
            }
        }

        /// <summary>
        /// 记录游戏化事件
        /// </summary>
        public async Task<GamificationLog> LogEventAsync(int userId, string eventType, int xpChange = 0, int pointsChange = 0,
            string? reason = null, long? relatedTaskId = null, string? metadata = null)
        {
            try
            {
                var userStats = await _context.GamificationUserStats
                    .FirstOrDefaultAsync(s => s.CoreUserId == userId);

                var log = new GamificationLog
                {
                    UserId = userStats?.UserId ?? 0,
                    Timestamp = DateTime.Now,
                    EventType = eventType,
                    XPChange = xpChange,
                    PointsChange = pointsChange,
                    LevelBefore = userStats?.CurrentLevel,
                    LevelAfter = userStats?.CurrentLevel,
                    Reason = reason,
                    RelatedTaskId = relatedTaskId,
                    Metadata = metadata
                };

                _context.GamificationLogs.Add(log);
                await _context.SaveChangesAsync();

                return log;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录游戏化事件时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 计算等级所需经验值
        /// </summary>
        public int CalculateXPForLevel(int level)
        {
            if (level <= 1) return 0;
            return (level - 1) * BASE_XP_PER_LEVEL + (level - 1) * (level - 2) * 10;
        }

        /// <summary>
        /// 根据经验值计算等级
        /// </summary>
        public int CalculateLevelFromXP(int xp)
        {
            int level = 1;
            while (CalculateXPForLevel(level + 1) <= xp)
            {
                level++;
            }
            return level;
        }

        /// <summary>
        /// 获取周统计汇总 - 所有人按周的任务创建、领取、完成数量
        /// </summary>
        public async Task<List<WeeklyTaskStatsDto>> GetWeeklyStatsAsync(int weekOffset = 0)
        {
            try
            {
                var targetDate = DateTime.Today.AddDays(weekOffset * 7);
                var weekStart = targetDate.AddDays(-(int)targetDate.DayOfWeek);
                var weekEnd = weekStart.AddDays(6);

                // 使用原生SQL查询周统计视图
                var weeklyStats = await _context.Database.SqlQueryRaw<WeeklyTaskStatsDto>(@"
                    SELECT
                        u.Id as UserId,
                        u.Name as UserName,
                        u.Department,
                        COALESCE(weekly_claims.ClaimedCount, 0) as WeeklyClaimedCount,
                        COALESCE(weekly_completed.CompletedCount, 0) as WeeklyCompletedCount,
                        COALESCE(weekly_created.CreatedCount, 0) as WeeklyCreatedCount,
                        COALESCE(gus.PointsBalance, 0) as TotalPoints,
                        COALESCE(gus.CurrentLevel, 1) as CurrentLevel,
                        COALESCE(gus.CompletedTasksCount, 0) as TotalCompletedTasks,
                        YEARWEEK({0}, 1) as WeekNumber,
                        {1} as WeekStartDate,
                        {2} as WeekEndDate,
                        CASE
                            WHEN COALESCE(weekly_claims.ClaimedCount, 0) > 0
                            THEN ROUND(COALESCE(weekly_completed.CompletedCount, 0) * 100.0 / weekly_claims.ClaimedCount, 2)
                            ELSE 0
                        END as WeeklyCompletionRate,
                        ROUND((COALESCE(weekly_created.CreatedCount, 0) * 2 +
                               COALESCE(weekly_claims.ClaimedCount, 0) * 1 +
                               COALESCE(weekly_completed.CompletedCount, 0) * 3) / 6.0, 2) as WeeklyActivityScore,
                        0 as Rank
                    FROM users u
                    LEFT JOIN gamification_userstats gus ON u.Id = gus.CoreUserId
                    LEFT JOIN (
                        SELECT
                            claimed_by as UserId,
                            COUNT(*) as ClaimedCount
                        FROM task_claims
                        WHERE claim_date BETWEEN {3} AND {4}
                        GROUP BY claimed_by
                    ) weekly_claims ON u.Id = weekly_claims.UserId
                    LEFT JOIN (
                        SELECT
                            claimed_by as UserId,
                            COUNT(*) as CompletedCount
                        FROM task_claims
                        WHERE claim_date BETWEEN {5} AND {6}
                        AND claim_status = 'Completed'
                        GROUP BY claimed_by
                    ) weekly_completed ON u.Id = weekly_completed.UserId
                    LEFT JOIN (
                        SELECT
                            CreatedBy as UserId,
                            COUNT(*) as CreatedCount
                        FROM tasks_v2
                        WHERE DATE(CreatedAt) BETWEEN {7} AND {8}
                        GROUP BY CreatedBy
                    ) weekly_created ON u.Id = weekly_created.UserId
                    WHERE u.IsActive = 1
                    ORDER BY TotalPoints DESC, WeeklyCompletedCount DESC, WeeklyClaimedCount DESC",
                    targetDate, weekStart, weekEnd, weekStart, weekEnd, weekStart, weekEnd, weekStart, weekEnd)
                    .ToListAsync();

                // 设置排名
                for (int i = 0; i < weeklyStats.Count; i++)
                {
                    weeklyStats[i].Rank = i + 1;
                }

                return weeklyStats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取周统计汇总时发生错误");
                return new List<WeeklyTaskStatsDto>();
            }
        }

        /// <summary>
        /// 获取用户周统计详情
        /// </summary>
        public async Task<WeeklyTaskStatsDto?> GetUserWeeklyStatsAsync(int userId, int weekOffset = 0)
        {
            try
            {
                var weeklyStats = await GetWeeklyStatsAsync(weekOffset);
                return weeklyStats.FirstOrDefault(s => s.UserId == userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户 {UserId} 周统计详情时发生错误", userId);
                return null;
            }
        }

        // 私有辅助方法

        /// <summary>
        /// 获取或创建用户统计数据
        /// </summary>
        private async Task<GamificationUserStats> GetOrCreateUserStatsAsync(int userId)
        {
            var userStats = await _context.GamificationUserStats
                .FirstOrDefaultAsync(s => s.CoreUserId == userId);

            if (userStats == null)
            {
                await InitializeUserStatsAsync(userId);
                userStats = await _context.GamificationUserStats
                    .FirstOrDefaultAsync(s => s.CoreUserId == userId);
            }

            return userStats!;
        }

        /// <summary>
        /// 处理简单奖励
        /// </summary>
        private async Task<GamificationRewardDto> ProcessSimpleRewardAsync(int userId, long taskId,
            int xpGained, int pointsGained, string eventType, string reason)
        {
            try
            {
                var userStats = await GetOrCreateUserStatsAsync(userId);
                var oldLevel = userStats.CurrentLevel;

                userStats.CurrentXP += xpGained;
                userStats.PointsBalance += pointsGained;
                userStats.LastActivityTimestamp = DateTime.Now;
                userStats.LastUpdatedTimestamp = DateTime.Now;

                var newLevel = CalculateLevelFromXP(userStats.CurrentXP);
                var leveledUp = newLevel > oldLevel;
                if (leveledUp)
                {
                    userStats.CurrentLevel = newLevel;
                }

                await _context.SaveChangesAsync();
                await LogEventAsync(userId, eventType, xpGained, pointsGained, reason, taskId);

                if (leveledUp)
                {
                    await LogEventAsync(userId, GamificationEventType.LevelUp, 0, 0,
                        $"升级到等级 {newLevel}", taskId);
                }

                return new GamificationRewardDto
                {
                    Success = true,
                    Message = "操作成功！",
                    XPGained = xpGained,
                    PointsGained = pointsGained,
                    LeveledUp = leveledUp,
                    NewLevel = leveledUp ? newLevel : null,
                    RewardDetails = $"获得 {xpGained} 经验值和 {pointsGained} 金币"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理简单奖励时发生错误");
                return new GamificationRewardDto
                {
                    Success = false,
                    Message = "奖励发放失败: " + ex.Message
                };
            }
        }
    }
}
