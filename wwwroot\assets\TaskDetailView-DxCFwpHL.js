import{_ as a,s as e,r as t,c as s,m as l,aq as n,aJ as i,b as u,a9 as c,d as r,e as o,w as d,t as v,f as m,aE as f,a8 as p,bc as y,a as g,u as h,o as b,A as _,aG as w,i as k,aM as D,F as z,ao as S,aS as A,Y as x,bp as $,aD as C,h as I,bj as T,a1 as M,bd as V}from"./index-CkwLz8y6.js";import{t as B}from"./task-Uzj9rZkj.js";import{f as N,z as U}from"./zh-CN-B1csyosV.js";import"./en-US-BvtvdVHO.js";const E={class:"task-detail-view page-container"},j={key:0,class:"content-wrapper"},F={class:"page-header mb-4"},q={class:"header-left"},G={class:"page-title"},L={class:"header-right"},O={class:"card-header"},P={class:"info-grid"},Y={class:"info-item"},H={class:"item-value"},J={class:"info-item"},K={class:"item-value"},R={class:"info-item"},Q={class:"item-value assignee"},W={class:"info-item"},X={class:"item-value"},Z={class:"info-item"},aa={class:"info-item"},ea={class:"item-value"},ta={class:"info-item"},sa={class:"item-value"},la={class:"info-item"},na={class:"item-value"},ia={class:"info-item"},ua={class:"item-value"},ca={class:"description-section"},ra={class:"description-content"},oa={class:"card-header"},da={key:0,class:"attachments-list"},va={class:"attachment-name"},ma={class:"attachment-size"},fa={class:"attachment-actions"},pa={class:"comment-section"},ya={class:"comments-list thin-scrollbar"},ga={key:0},ha={class:"comment-content"},ba={class:"comment-header"},_a={class:"comment-user"},wa={class:"comment-time"},ka={class:"comment-text"},Da={class:"comment-form"},za={class:"activity-logs thin-scrollbar"},Sa={key:0},Aa={class:"activity-content"},xa={class:"activity-text"},$a={class:"activity-user"},Ca={class:"activity-time"},Ia={key:1,class:"task-not-found"},Ta=a({__name:"TaskDetailView",setup(a){const Ta=h(),Ma=e().params.id,Va=t(!0),Ba=t(!1),Na=t(!1),Ua=t(null),Ea=t([]),ja=t([]),Fa=t(""),qa=t(!1),Ga=t(!1),La=t(!1),Oa=t(),Pa=t([]),Ya=t(null),Ha=t("comments"),Ja=t([]),Ka=s((()=>Pa.value.map((a=>({name:a.name,size:a.size,uid:a.uid,status:a.status})))));async function Ra(){if(!Ma)return p.error("无效的任务ID"),void(Va.value=!1);Va.value=!0;try{if(0===Ja.value.length)try{const a=await y.getUserList({pageSize:1e3});a&&Array.isArray(a.list)?Ja.value=a.list:a&&Array.isArray(a)?Ja.value=a:Ja.value=[]}catch(a){Ja.value=[]}const e=await B.getTaskDetail(Ma);e&&e.data?(Ua.value=function(a){const e=a.endDate&&"completed"!==a.status&&new Date(a.endDate)<new Date;return{...a,isOverdue:e,originalApiStatus:a.status,attachments:Array.isArray(a.attachments)?a.attachments:[]}}(e.data),Qa(),async function(){if(!Ma)return;La.value=!0;try{const a=await B.getTaskActivityLog(Ma);ja.value=(null==a?void 0:a.data)||[]}catch(a){ja.value=[]}finally{La.value=!1}}()):(Ua.value=null,p.error((null==e?void 0:e.message)||"获取任务详情失败"))}catch(e){p.error("加载任务详情时出错"),Ua.value=null}finally{Va.value=!1}}async function Qa(){if(Ma){Ga.value=!0;try{const a=await B.getComments(Ma);Ea.value=(null==a?void 0:a.data)||[]}catch(a){Ea.value=[]}finally{Ga.value=!1}}}async function Wa(a){if(Ua.value&&a!==Ua.value.originalApiStatus){Ba.value=!0;try{await B.updateTaskStatus(Ma,{status:a}),p.success("任务状态更新成功"),Ua.value.originalApiStatus=a,Ua.value.isOverdue=fe(Ua.value)}catch(e){p.error("更新任务状态失败"),Ua.value.status=Ua.value.originalApiStatus}finally{Ba.value=!1}}}async function Xa(){if(Fa.value.trim()&&Ma){qa.value=!0;try{await B.addComment(Ma,{content:Fa.value}),await Qa(),Fa.value="",p.success("评论已发表")}catch(a){p.error("发表评论失败")}finally{qa.value=!1}}}function Za(){M.confirm("确定要删除此任务吗？此操作不可恢复。","确认删除任务",{confirmButtonText:"确认删除",cancelButtonText:"取消",type:"warning"}).then((async()=>{Na.value=!0;try{await B.deleteTask(Ma),p.success("任务删除成功"),Ta.push("/main/tasks/list")}catch(a){p.error("删除任务失败")}finally{Na.value=!1}})).catch((()=>p.info("删除已取消")))}function ae(a){const e=a.size/1024/1024<10;return e||p.error("附件大小不能超过 10MB!"),e}function ee(a,e){p.warning(`限制上传 5 个文件，本次选择了 ${a.length} 个`)}async function te(a){const{file:e,onSuccess:t,onError:s,onProgress:l}=a,n=new FormData;n.append("file",e);try{const a=await B.uploadTaskAttachment(Ma,n);if(!a||!a.success)throw new Error((null==a?void 0:a.message)||"上传失败");p.success(`${e.name} 上传成功`),t(a.data),Ra()}catch(i){p.error(`${e.name} 上传失败: ${i.message}`),s(i)}}function se(a,e){}function le(){Ta.go(-1)}function ne(a){return Ja.value.find((e=>e.id===a))||null}function ie(a){var e;return(null==(e=ne(a))?void 0:e.name)||"未知"}function ue(a){const e=ne(a);return V(null==e?void 0:e.avatar)}function ce(a){if(!a||"string"!=typeof a)return"?";const e=a.split(" ");let t=e[0].substring(0,1).toUpperCase();return e.length>1&&(t+=e[e.length-1].substring(0,1).toUpperCase()),t}function re(a){a.target.style.display="none"}function oe(a){return{low:"低",medium:"中",high:"高"}[a]||"中"}function de(a,e=!0){if(!a)return"";try{const t=new Date(a);if(isNaN(t.getTime()))return a;const s=t.getFullYear(),l=String(t.getMonth()+1).padStart(2,"0"),n=String(t.getDate()).padStart(2,"0");if(e){const a=String(t.getHours()).padStart(2,"0");return`${s}-${l}-${n} ${a}:${String(t.getMinutes()).padStart(2,"0")}`}return`${s}-${l}-${n}`}catch(t){return a}}function ve(a){if(!a)return"";try{const e=new Date(a);return isNaN(e.getTime())?"无效日期":N(e,{addSuffix:!0,locale:U})}catch(e){return"无效日期"}}function me(a){if(0===a)return"0 B";if(!a||isNaN(a)||a<0)return"N/A";const e=Math.floor(Math.log(a)/Math.log(1024));return parseFloat((a/Math.pow(1024,e)).toFixed(1))+" "+["B","KB","MB","GB"][e]}function fe(a){return a.endDate&&"completed"!==a.status&&new Date(a.endDate)<new Date}return l((()=>{Ra()})),(a,e)=>{const t=g("el-icon"),s=g("el-button"),l=g("el-option"),y=g("el-select"),h=g("el-tag"),V=g("el-avatar"),N=g("el-divider"),U=g("el-card"),Ta=g("el-empty"),Pa=g("el-col"),Ja=g("el-input"),Qa=g("el-tab-pane"),ne=g("el-tabs"),pe=g("el-row"),ye=i("loading");return n((b(),u("div",E,[Ua.value&&!Va.value?(b(),u("div",j,[r("div",F,[r("div",q,[o(s,{onClick:le,text:"",size:"small",class:"back-button"},{default:d((()=>[o(t,null,{default:d((()=>[o(m(w))])),_:1}),e[3]||(e[3]=_(" 返回 "))])),_:1}),r("h2",G,v(Ua.value.title),1)]),r("div",L,[o(s,{type:"danger",onClick:Za,icon:m(f),loading:Na.value},{default:d((()=>e[4]||(e[4]=[_("删除")]))),_:1},8,["icon","loading"])])]),o(pe,{gutter:20},{default:d((()=>[o(Pa,{xs:24,sm:16,md:17},{default:d((()=>[o(U,{shadow:"never",class:"details-card mb-4"},{header:d((()=>[r("div",O,[r("span",null,[o(t,null,{default:d((()=>[o(m(A))])),_:1}),e[5]||(e[5]=_(" 基本信息"))])])])),default:d((()=>{return[r("div",P,[r("div",Y,[e[6]||(e[6]=r("span",{class:"item-label"},"状态:",-1)),r("span",H,[o(y,{modelValue:Ua.value.status,"onUpdate:modelValue":e[0]||(e[0]=a=>Ua.value.status=a),size:"small",onChange:Wa,loading:Ba.value},{default:d((()=>[o(l,{label:"未开始",value:"unstarted"}),o(l,{label:"进行中",value:"in-progress"}),o(l,{label:"已完成",value:"completed"})])),_:1},8,["modelValue","loading"])])]),r("div",J,[e[7]||(e[7]=r("span",{class:"item-label"},"优先级:",-1)),r("span",K,[o(h,{type:(a=Ua.value.priority,{low:"success",medium:"warning",high:"danger"}[a]||"info"),size:"small",effect:"light",round:""},{default:d((()=>[_(v(oe(Ua.value.priority)),1)])),_:1},8,["type"])])]),r("div",R,[e[8]||(e[8]=r("span",{class:"item-label"},"负责人:",-1)),r("span",Q,[o(V,{size:24,src:ue(Ua.value.assigneeId),class:"small-avatar",onError:re},{default:d((()=>[_(v(ce(ie(Ua.value.assigneeId))),1)])),_:1},8,["src"]),r("span",null,v(ie(Ua.value.assigneeId)),1)])]),r("div",W,[e[9]||(e[9]=r("span",{class:"item-label"},"创建时间:",-1)),r("span",X,v(de(Ua.value.createDate)||"-"),1)]),r("div",Z,[e[10]||(e[10]=r("span",{class:"item-label"},"截止时间:",-1)),r("span",{class:k(["item-value",{overdue:fe(Ua.value)&&"completed"!==Ua.value.status}])},[o(t,null,{default:d((()=>[o(m(D))])),_:1}),_(" "+v(de(Ua.value.endDate,!1)||"未设置"),1)],2)]),Ua.value.isPeriodic?(b(),u(z,{key:0},[r("div",aa,[e[12]||(e[12]=r("span",{class:"item-label"},"任务类型:",-1)),r("span",ea,[o(h,{type:"success",size:"small"},{default:d((()=>e[11]||(e[11]=[_("周期性")]))),_:1})])]),r("div",ta,[e[13]||(e[13]=r("span",{class:"item-label"},"开始日期:",-1)),r("span",sa,v(de(Ua.value.startDate,!1)||"-"),1)]),r("div",la,[e[14]||(e[14]=r("span",{class:"item-label"},"结束日期:",-1)),r("span",na,v(de(Ua.value.endDate,!1)||"-"),1)]),r("div",ia,[e[15]||(e[15]=r("span",{class:"item-label"},"频率:",-1)),r("span",ua,v(Ua.value.frequency||"-"),1)])],64)):c("",!0)]),o(N),r("div",ca,[r("h4",null,[o(t,null,{default:d((()=>[o(m(S))])),_:1}),e[16]||(e[16]=_(" 任务描述"))]),r("p",ra,v(Ua.value.description||"暂无描述"),1)])];var a})),_:1}),o(U,{shadow:"never",class:"attachments-card mb-4"},{header:d((()=>[r("div",oa,[r("span",null,[o(t,null,{default:d((()=>[o(m(T))])),_:1}),e[17]||(e[17]=_(" 附件"))])])])),default:d((()=>[o(m($),{ref_key:"uploadRef",ref:Oa,action:"#","http-request":te,"on-remove":se,"file-list":Ka.value,"before-upload":ae,"on-exceed":ee,multiple:"",limit:5,"list-type":"text",class:"upload-area"},{tip:d((()=>e[19]||(e[19]=[r("div",{class:"el-upload__tip"},"单个文件不超过10MB，最多上传5个文件",-1)]))),default:d((()=>[o(s,{type:"primary",icon:m(C)},{default:d((()=>e[18]||(e[18]=[_("点击上传")]))),_:1},8,["icon"])])),_:1},8,["file-list"]),Ua.value.attachments&&Ua.value.attachments.length>0?(b(),u("div",da,[(b(!0),u(z,null,I(Ua.value.attachments,(a=>(b(),u("div",{key:a.id||a.name,class:"attachment-item"},[o(t,null,{default:d((()=>[o(m(S))])),_:1}),r("span",va,v(a.name),1),r("span",ma,"("+v(me(a.size))+")",1),r("div",fa,[o(s,{type:"danger",link:"",size:"small",icon:m(f),onClick:e=>function(a){(null==a?void 0:a.id)&&M.confirm(`确定删除附件 "${a.name}"?`,"确认删除",{type:"warning"}).then((async()=>{Ya.value=a.id;try{await B.deleteTaskAttachment(Ma,a.id),p.success("附件删除成功"),Ra()}catch(e){p.error("删除附件失败")}finally{Ya.value=null}})).catch((()=>p.info("删除已取消")))}(a),loading:Ya.value===a.id},{default:d((()=>e[20]||(e[20]=[_("删除")]))),_:2},1032,["icon","onClick","loading"])])])))),128))])):(b(),x(Ta,{key:1,description:"暂无附件"}))])),_:1})])),_:1}),o(Pa,{xs:24,sm:8,md:7},{default:d((()=>[o(U,{shadow:"never",class:"activity-card"},{default:d((()=>[o(ne,{modelValue:Ha.value,"onUpdate:modelValue":e[2]||(e[2]=a=>Ha.value=a)},{default:d((()=>[o(Qa,{label:"评论",name:"comments",lazy:""},{default:d((()=>[r("div",pa,[n((b(),u("div",ya,[Ea.value.length>0?(b(),u("div",ga,[(b(!0),u(z,null,I(Ea.value,(a=>(b(),u("div",{key:a.id,class:"comment-item"},[o(V,{size:32,src:ue(a.userId),class:"comment-avatar",onError:re},{default:d((()=>[_(v(ce(ie(a.userId))),1)])),_:2},1032,["src"]),r("div",ha,[r("div",ba,[r("span",_a,v(ie(a.userId)),1),r("span",wa,v(ve(a.createDate||a.time)),1)]),r("div",ka,v(a.content),1)])])))),128))])):(b(),x(Ta,{key:1,description:"暂无评论"}))])),[[ye,Ga.value]]),r("div",Da,[o(Ja,{modelValue:Fa.value,"onUpdate:modelValue":e[1]||(e[1]=a=>Fa.value=a),type:"textarea",rows:3,placeholder:"输入评论...",disabled:qa.value,resize:"none"},null,8,["modelValue","disabled"]),o(s,{type:"primary",onClick:Xa,loading:qa.value,disabled:!Fa.value.trim(),class:"submit-comment-btn"},{default:d((()=>e[21]||(e[21]=[_("发表")]))),_:1},8,["loading","disabled"])])])])),_:1}),o(Qa,{label:"活动日志",name:"logs",lazy:""},{default:d((()=>[n((b(),u("div",za,[ja.value.length>0?(b(),u("div",Sa,[(b(!0),u(z,null,I(ja.value,(a=>(b(),u("div",{key:a.id,class:"activity-item"},[o(V,{size:28,src:ue(a.userId),class:"activity-avatar",onError:re},{default:d((()=>[_(v(ce(ie(a.userId))),1)])),_:2},1032,["src"]),r("div",Aa,[r("span",xa,[r("span",$a,v(ie(a.userId)),1),_(" "+v(a.action),1)]),r("span",Ca,v(ve(a.createDate||a.time)),1)])])))),128))])):(b(),x(Ta,{key:1,description:"暂无活动记录"}))])),[[ye,La.value]])])),_:1})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1})])):Va.value?c("",!0):(b(),u("div",Ia,[o(Ta,{description:"任务不存在或加载失败"},{default:d((()=>[o(s,{onClick:le},{default:d((()=>e[22]||(e[22]=[_("返回任务列表")]))),_:1})])),_:1})]))])),[[ye,Va.value]])}}},[["__scopeId","data-v-74b61ccc"]]);export{Ta as default};
