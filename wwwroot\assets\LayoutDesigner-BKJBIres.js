import{_ as e,r as a,a5 as l,c as t,m as s,q as n,a as i,b as o,o as u,d as r,a9 as d,e as v,ax as c,t as h,w as g,A as p,f as m,aB as w,R as x,ab as y,aC as f,aD as b,V as k,Y as M,i as z,F as Y,h as X,n as V,aw as Z,aE as C,aF as $,aG as _,aH as W,z as D,a8 as S,a1 as E}from"./index-CkwLz8y6.js";const L={class:"layout-designer"},G={class:"designer-toolbar"},U={class:"toolbar-section"},H={class:"layout-stats"},R={class:"stat-item"},I={class:"stat-value"},F={class:"stat-item"},O={class:"stat-value"},A={class:"toolbar-actions"},P={class:"file-operations"},j={key:0,class:"operation-status"},B={key:1,class:"selection-details"},N={class:"details-content"},J={class:"detail-item"},K={class:"value"},T={class:"detail-item"},q={class:"value"},Q={class:"detail-item"},ee={key:0,class:"selected-zones-list"},ae={class:"zones-tags"},le={class:"zones-list"},te={class:"zone-items"},se=["onClick"],ne={class:"zone-name"},ie={class:"zone-info"},oe={class:"canvas-container"},ue={key:0,class:"selection-info"},re=["onMousedown","onClick"],de={key:0,class:"resize-handles"},ve=["onMousedown"],ce=["onMousedown"],he=["onMousedown"],ge=["onMousedown"],pe=["onMousedown"],me=["onMousedown"],we=["onMousedown"],xe=["onMousedown"],ye={key:0,class:"properties-panel"},fe={class:"fine-tune-controls"},be={class:"tune-grid"},ke={class:"tune-row"},Me={class:"tune-step"},ze={key:1,class:"canvas-config-panel"},Ye={class:"canvas-info"},Xe={class:"info-item"},Ve={class:"info-item"},Ze={class:"preview-container"},Ce=e({__name:"FactoryLayoutDesigner",emits:["layout-saved","layout-updated"],setup(e,{emit:Ce}){const $e=a([]),_e=a(null),We=a([]),De=a(null),Se=a(null),Ee=a(!1),Le=a(null),Ge=a(null),Ue=a(5),He=a({width:2e3,height:1200,minWidth:1200,minHeight:800,maxWidth:4e3,maxHeight:2400,gridSize:20,showGrid:!0,backgroundColor:"#fafafa"}),Re=a(!1),Ie=a({startX:0,startY:0,endX:0,endY:0,visible:!1}),Fe=l({isDragging:!1,isResizing:!1,isGroupDragging:!1,startX:0,startY:0,startZoneX:0,startZoneY:0,startZoneWidth:0,startZoneHeight:0,resizeDirection:"",groupStartPositions:[]}),Oe=[{id:1,name:"区域1",x:1800,y:100,width:120,height:800,rows:25,cols:1,color:"#4A90E2",startWorkstation:1,gapX:2,gapY:2},{id:2,name:"区域2",x:600,y:800,width:1100,height:300,rows:5,cols:22,color:"#F5A623",startWorkstation:26,gapX:2,gapY:2},{id:3,name:"区域3",x:100,y:800,width:400,height:300,rows:5,cols:8,color:"#4A90E2",startWorkstation:136,gapX:2,gapY:2},{id:4,name:"区域4",x:100,y:100,width:450,height:600,rows:10,cols:9,color:"#F8A5C2",startWorkstation:176,gapX:2,gapY:2},{id:5,name:"区域5",x:600,y:100,width:550,height:600,rows:10,cols:11,color:"#50E3C2",startWorkstation:266,gapX:2,gapY:2},{id:6,name:"区域6",x:1200,y:100,width:550,height:600,rows:10,cols:11,color:"#BD10E0",startWorkstation:376,gapX:2,gapY:2}],Ae=t((()=>$e.value.reduce(((e,a)=>e+a.rows*a.cols),0))),Pe=e=>({left:`${e.x}px`,top:`${e.y}px`,width:`${e.width}px`,height:`${e.height}px`,borderColor:e.color}),je=e=>{const a=e.gapX||2,l=e.gapY||2;return{gridTemplateColumns:`repeat(${e.cols}, 1fr)`,gridTemplateRows:`repeat(${e.rows}, 1fr)`,gap:`${l}px ${a}px`}},Be=(e,a)=>e.startWorkstation+a-1,Ne=()=>{if(!He.value.showGrid)return{display:"none"};const e=He.value.gridSize,a=5*e;return{position:"absolute",top:"0",left:"0",width:"100%",height:"100%",pointerEvents:"none",zIndex:1,backgroundImage:"\n      linear-gradient(to right, #d1d5db 1px, transparent 1px),\n      linear-gradient(to bottom, #d1d5db 1px, transparent 1px),\n      linear-gradient(to right, #e5e7eb 1px, transparent 1px),\n      linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)\n    ",backgroundSize:`\n      ${a}px ${a}px,\n      ${a}px ${a}px,\n      ${e}px ${e}px,\n      ${e}px ${e}px\n    `,opacity:.6}},Je=e=>{Re.value||Fe.isDragging||Fe.isGroupDragging||(e.target.classList.contains("canvas-grid")||e.target.classList.contains("grid-background-layer"))&&(e.ctrlKey||(_e.value=null,We.value=[],Ie.value.visible=!1))},Ke=()=>{const e=Math.max(...$e.value.map((e=>e.id)),0)+1,a={id:e,name:`区域${e}`,x:100+20*(e-1),y:50+20*(e-1),width:120,height:80,rows:2,cols:3,color:`#${Math.floor(16777215*Math.random()).toString(16)}`,startWorkstation:Te(),gapX:2,gapY:2};$e.value.push(a),_e.value=a,S.success("区域添加成功")},Te=()=>{let e=0;return $e.value.forEach((a=>{const l=a.startWorkstation+a.rows*a.cols-1;l>e&&(e=l)})),e+1},qe=e=>$e.value.find((a=>a.id===e)),Qe=(e,a)=>{var l;a&&a.stopPropagation(),a&&a.ctrlKey?We.value.includes(e.id)?(We.value=We.value.filter((a=>a!==e.id)),(null==(l=_e.value)?void 0:l.id)===e.id&&(_e.value=We.value.length>0?$e.value.find((e=>e.id===We.value[0])):null)):(We.value.push(e.id),_e.value=e):(We.value=[e.id],_e.value=e)},ea=()=>{if(We.value.length<=1)return{};const e=$e.value.filter((e=>We.value.includes(e.id)));if(0===e.length)return{};let a=1/0,l=1/0,t=-1/0,s=-1/0;e.forEach((e=>{a=Math.min(a,e.x),l=Math.min(l,e.y),t=Math.max(t,e.x+e.width),s=Math.max(s,e.y+e.height)}));return a-=10,l-=10,t+=10,s+=10,{position:"absolute",left:`${a}px`,top:`${l}px`,width:t-a+"px",height:s-l+"px",border:"3px dashed #10b981",background:"rgba(16, 185, 129, 0.05)",borderRadius:"8px",pointerEvents:"none",zIndex:999}},aa=e=>{e.target.classList.contains("draggable-zone")||e.target.closest(".draggable-zone")||e.target.classList.contains("resize-handle")||la(e)},la=e=>{e.ctrlKey||(We.value=[],_e.value=null);const a=e.currentTarget.getBoundingClientRect();Ie.value.startX=e.clientX-a.left,Ie.value.startY=e.clientY-a.top,Ie.value.endX=Ie.value.startX,Ie.value.endY=Ie.value.startY,Ie.value.visible=!0,Re.value=!0,e.preventDefault()},ta=e=>{const a=Math.min(Ie.value.startX,Ie.value.endX),l=Math.min(Ie.value.startY,Ie.value.endY),t=Math.max(Ie.value.startX,Ie.value.endX),s=Math.max(Ie.value.startY,Ie.value.endY),n=e.x,i=e.y,o=e.x+e.width,u=e.y+e.height;return!(t<n||a>o||s<i||l>u)},sa=e=>{E.confirm("确定要删除这个区域吗？","确认删除",{type:"warning"}).then((()=>{var a;$e.value=$e.value.filter((a=>a.id!==e)),(null==(a=_e.value)?void 0:a.id)===e&&(_e.value=null),S.success("区域删除成功")})).catch((()=>{}))},na=()=>{E.confirm("确定要重置为默认布局吗？","确认重置",{type:"warning"}).then((()=>{$e.value=[...Oe],_e.value=null,S.success("布局重置成功")})).catch((()=>{}))},ia=()=>{oa()},oa=()=>{let e=1;$e.value.sort(((e,a)=>e.id-a.id)).forEach((a=>{a.startWorkstation=e,e+=a.rows*a.cols}))},ua=e=>{if(!_e.value)return void S.warning("请先选择一个区域");const a=_e.value,l=Ue.value,t=He.value.width-a.width,s=He.value.height-a.height;switch(e){case"up":a.y=Math.max(0,a.y-l);break;case"down":a.y=Math.min(s,a.y+l);break;case"left":a.x=Math.max(0,a.x-l);break;case"right":a.x=Math.min(t,a.x+l)}},ra=(e,a,l)=>{Se.value=e,Fe.isResizing=!0,Fe.resizeDirection=a,Fe.startX=l.clientX,Fe.startY=l.clientY,Fe.startZoneX=e.x,Fe.startZoneY=e.y,Fe.startZoneWidth=e.width,Fe.startZoneHeight=e.height,l.preventDefault(),l.stopPropagation()},da=e=>{if(Re.value)(e=>{var a;if(!Re.value)return;const l=null==(a=Le.value)?void 0:a.querySelector(".canvas-grid");if(!l)return;const t=l.getBoundingClientRect(),s=Math.max(0,Math.min(He.value.width,e.clientX-t.left)),n=Math.max(0,Math.min(He.value.height,e.clientY-t.top));Ie.value.endX=s,Ie.value.endY=n;const i=[];if($e.value.forEach((e=>{ta(e)&&i.push(e.id)})),e.ctrlKey){const e=[...new Set([...We.value,...i])];We.value=e}else We.value=i})(e);else{if(Fe.isGroupDragging){const a=e.clientX-Fe.startX,l=e.clientY-Fe.startY;let t=1/0,s=1/0,n=-1/0,i=-1/0;Fe.groupStartPositions.forEach((e=>{const o=$e.value.find((a=>a.id===e.id));if(o){const u=e.startX+a,r=e.startY+l;t=Math.min(t,u),s=Math.min(s,r),n=Math.max(n,u+o.width),i=Math.max(i,r+o.height)}}));let o=a,u=l;t<0?o=a-t:n>He.value.width&&(o=a-(n-He.value.width)),s<0?u=l-s:i>He.value.height&&(u=l-(i-He.value.height)),Fe.groupStartPositions.forEach((e=>{const a=$e.value.find((a=>a.id===e.id));a&&(a.x=e.startX+o,a.y=e.startY+u)})),Math.abs(a)%20==0||Math.abs(l)}if(Fe.isDragging&&De.value){const a=e.clientX-Fe.startX,l=e.clientY-Fe.startY,t=He.value.width-De.value.width,s=He.value.height-De.value.height;De.value.x=Math.max(0,Math.min(t,Fe.startZoneX+a)),De.value.y=Math.max(0,Math.min(s,Fe.startZoneY+l))}if(Fe.isResizing&&Se.value){const a=e.clientX-Fe.startX,l=e.clientY-Fe.startY,t=Se.value,s=Fe.resizeDirection,n=He.value.width,i=He.value.height;if("e"===s)t.width=Math.max(50,Math.min(n-t.x,Fe.startZoneWidth+a));else if("w"===s){Math.max(50,Fe.startZoneWidth-a);const e=Fe.startZoneX+Fe.startZoneWidth-50;t.x=Math.max(0,Math.min(e,Fe.startZoneX+a)),t.width=Fe.startZoneWidth+(Fe.startZoneX-t.x)}else if("s"===s)t.height=Math.max(40,Math.min(i-t.y,Fe.startZoneHeight+l));else if("n"===s){Math.max(40,Fe.startZoneHeight-l);const e=Fe.startZoneY+Fe.startZoneHeight-40;t.y=Math.max(0,Math.min(e,Fe.startZoneY+l)),t.height=Fe.startZoneHeight+(Fe.startZoneY-t.y)}else if("ne"===s){t.width=Math.max(50,Math.min(n-t.x,Fe.startZoneWidth+a));const e=Fe.startZoneY+Fe.startZoneHeight-40;t.y=Math.max(0,Math.min(e,Fe.startZoneY+l)),t.height=Fe.startZoneHeight+(Fe.startZoneY-t.y)}else if("nw"===s){const e=Fe.startZoneX+Fe.startZoneWidth-50;t.x=Math.max(0,Math.min(e,Fe.startZoneX+a)),t.width=Fe.startZoneWidth+(Fe.startZoneX-t.x);const s=Fe.startZoneY+Fe.startZoneHeight-40;t.y=Math.max(0,Math.min(s,Fe.startZoneY+l)),t.height=Fe.startZoneHeight+(Fe.startZoneY-t.y)}else if("se"===s)t.width=Math.max(50,Math.min(n-t.x,Fe.startZoneWidth+a)),t.height=Math.max(40,Math.min(i-t.y,Fe.startZoneHeight+l));else if("sw"===s){const e=Fe.startZoneX+Fe.startZoneWidth-50;t.x=Math.max(0,Math.min(e,Fe.startZoneX+a)),t.width=Fe.startZoneWidth+(Fe.startZoneX-t.x),t.height=Math.max(40,Math.min(i-t.y,Fe.startZoneHeight+l))}}}},va=e=>{var a;Re.value?Re.value&&(Re.value=!1,Ie.value.visible=!1,We.value.length>0?(_e.value=$e.value.find((e=>e.id===We.value[0])),We.value.length>1?S.success(`已选中 ${We.value.length} 个区域，可拖拽进行编组移动`):S.success(`已选中区域: ${null==(a=_e.value)?void 0:a.name}`)):_e.value=null):((Fe.isDragging||Fe.isGroupDragging)&&(Fe.isGroupDragging||Fe.isDragging&&De.value&&(_e.value=De.value,We.value.includes(De.value.id)||(We.value=[De.value.id]))),Fe.isDragging=!1,Fe.isResizing=!1,Fe.isGroupDragging=!1,Fe.groupStartPositions=[],De.value=null,Se.value=null)},ca=e=>{"Escape"===e.key?(We.value=[],_e.value=null,Ie.value.visible=!1,Re.value=!1):"Delete"===e.key&&(We.value.length>0||_e.value)&&(We.value.length>0?E.confirm(`确定要删除这 ${We.value.length} 个区域吗？`,"批量删除",{type:"warning"}).then((()=>{$e.value=$e.value.filter((e=>!We.value.includes(e.id))),We.value=[],_e.value=null,S.success("区域删除成功")})).catch((()=>{})):_e.value&&sa(_e.value.id))},ha=()=>{Ee.value=!0},ga=()=>{const e={zones:$e.value.map((e=>({id:e.id,name:e.name,x:e.x,y:e.y,width:e.width,height:e.height,rows:e.rows,cols:e.cols,color:e.color,startWorkstation:e.startWorkstation,gapX:e.gapX||2,gapY:e.gapY||2,totalWorkstations:e.rows*e.cols}))),totalWorkstations:$e.value.reduce(((e,a)=>e+a.rows*a.cols),0),canvas:{width:He.value.width,height:He.value.height,gridSize:He.value.gridSize,showGrid:He.value.showGrid,backgroundColor:He.value.backgroundColor},metadata:{version:"2.0",timestamp:(new Date).toISOString(),totalZones:$e.value.length,designerVersion:"FactoryLayoutDesigner v2.0"}};ya("layout-saved",e),S.success("布局保存成功")},pa=()=>{var e;null==(e=Ge.value)||e.click()},ma=e=>{const a=e.target.files[0];if(!a)return;if(!a.name.endsWith(".json"))return void S.error("请选择JSON格式的文件");const l=new FileReader;l.onload=e=>{try{const a=JSON.parse(e.target.result);wa(a)}catch(a){S.error("文件格式错误，请检查JSON文件")}},l.readAsText(a),e.target.value=""},wa=e=>{var a;try{if(!e.zones||!Array.isArray(e.zones))throw new Error("配置文件格式错误：缺少zones数组");const l=["id","name","x","y","width","height","rows","cols","color","startWorkstation"];for(const a of e.zones){for(const e of l)if(void 0===a[e]||null===a[e])throw new Error(`区域 ${a.name||a.id} 缺少必要字段: ${e}`);void 0===a.gapX&&(a.gapX=2),void 0===a.gapY&&(a.gapY=2)}if(e.canvas&&(He.value={...He.value,...e.canvas}),$e.value=[...e.zones],_e.value=null,We.value=[],S.success(`成功导入 ${e.zones.length} 个区域的布局配置`),null==(a=e.metadata)?void 0:a.timestamp){const a=new Date(e.metadata.timestamp).toLocaleString("zh-CN");S.info(`布局创建时间: ${a}`)}S.info(`画布尺寸: ${He.value.width} × ${He.value.height}`)}catch(l){S.error(`导入失败: ${l.message}`)}},xa=()=>{const e={zones:$e.value.map((e=>({id:e.id,name:e.name,x:e.x,y:e.y,width:e.width,height:e.height,rows:e.rows,cols:e.cols,color:e.color,startWorkstation:e.startWorkstation,gapX:e.gapX||2,gapY:e.gapY||2,totalWorkstations:e.rows*e.cols}))),canvas:{width:He.value.width,height:He.value.height,gridSize:He.value.gridSize,showGrid:He.value.showGrid,backgroundColor:He.value.backgroundColor,boundaries:{minX:0,minY:0,maxX:He.value.width,maxY:He.value.height}},metadata:{version:"2.0",timestamp:(new Date).toISOString(),totalZones:$e.value.length,totalWorkstations:$e.value.reduce(((e,a)=>e+a.rows*a.cols),0),designerVersion:"FactoryLayoutDesigner v2.0",exportedBy:"FactoryLayoutDesigner",proportions:{aspectRatio:He.value.width/He.value.height,gridDensity:He.value.width*He.value.height/(He.value.gridSize*He.value.gridSize)}}},a=new Blob([JSON.stringify(e,null,2)],{type:"application/json"}),l=URL.createObjectURL(a),t=document.createElement("a");t.href=l,t.download=`factory-layout-${Date.now()}.json`,t.click(),URL.revokeObjectURL(l),S.success("配置导出成功")},ya=Ce;return s((()=>{$e.value=[...Oe],document.addEventListener("mousemove",da),document.addEventListener("mouseup",va),document.addEventListener("keydown",ca)})),n((()=>{document.removeEventListener("mousemove",da),document.removeEventListener("mouseup",va),document.removeEventListener("keydown",ca)})),(e,a)=>{var l;const t=i("el-button"),s=i("el-alert"),n=i("el-input"),E=i("el-form-item"),Ce=i("el-color-picker"),Oe=i("el-input-number"),Te=i("el-option"),la=i("el-select"),ta=i("el-form"),da=i("el-switch"),va=i("el-dialog");return u(),o("div",L,[r("div",G,[r("div",U,[a[37]||(a[37]=r("h3",null,"布局设计器",-1)),r("div",H,[r("div",R,[a[24]||(a[24]=r("span",{class:"stat-label"},"总工位数:",-1)),r("span",I,h(Ae.value),1)]),r("div",F,[a[25]||(a[25]=r("span",{class:"stat-label"},"区域数:",-1)),r("span",O,h($e.value.length),1)])]),r("div",A,[v(t,{type:"primary",onClick:Ke,icon:m(w)},{default:g((()=>a[26]||(a[26]=[p("添加区域")]))),_:1},8,["icon"]),v(t,{onClick:na,icon:m(x)},{default:g((()=>a[27]||(a[27]=[p("重置布局")]))),_:1},8,["icon"]),v(t,{type:"success",onClick:ga,icon:m(y)},{default:g((()=>a[28]||(a[28]=[p("保存布局")]))),_:1},8,["icon"]),v(t,{onClick:ha,icon:m(f)},{default:g((()=>a[29]||(a[29]=[p("预览")]))),_:1},8,["icon"]),r("div",P,[v(t,{type:"info",onClick:pa,icon:m(b)},{default:g((()=>a[30]||(a[30]=[p("导入布局")]))),_:1},8,["icon"]),v(t,{onClick:xa,icon:m(k)},{default:g((()=>a[31]||(a[31]=[p("导出布局")]))),_:1},8,["icon"]),r("input",{ref_key:"fileInput",ref:Ge,type:"file",accept:".json",onChange:ma,style:{display:"none"}},null,544)])]),We.value.length>1||Fe.isGroupDragging||Re.value?(u(),o("div",j,[Fe.isGroupDragging?(u(),M(s,{key:0,title:"🔥 编组移动中",description:`正在移动 ${We.value.length} 个区域`,type:"warning",closable:!1,"show-icon":""},null,8,["description"])):Re.value?(u(),M(s,{key:1,title:"📦 框选模式",description:"拖拽鼠标进行框选，按住Ctrl键可追加选择",type:"info",closable:!1,"show-icon":""})):We.value.length>1?(u(),M(s,{key:2,title:"🎯 编组模式激活",description:`已选择 ${We.value.length} 个区域，拖拽任意区域进行编组移动`,type:"success",closable:!1,"show-icon":""},null,8,["description"])):d("",!0)])):d("",!0),We.value.length>0?(u(),o("div",B,[a[36]||(a[36]=r("h5",null,"选择状态详情",-1)),r("div",N,[r("div",J,[a[32]||(a[32]=r("span",{class:"label"},"选中区域:",-1)),r("span",K,h(We.value.length)+" 个",1)]),r("div",T,[a[33]||(a[33]=r("span",{class:"label"},"当前区域:",-1)),r("span",q,h((null==(l=_e.value)?void 0:l.name)||"无"),1)]),r("div",Q,[a[34]||(a[34]=r("span",{class:"label"},"状态:",-1)),r("span",{class:z(["value status",Fe.isGroupDragging?"dragging":Re.value?"selecting":We.value.length>1?"multi":1===We.value.length?"single":"none"])},h(Fe.isGroupDragging?"编组拖拽中":Re.value?"框选中":We.value.length>1?"多选编组":1===We.value.length?"单选":"未选择"),3)]),We.value.length>1?(u(),o("div",ee,[a[35]||(a[35]=r("span",{class:"label"},"选中列表:",-1)),r("div",ae,[(u(!0),o(Y,null,X(We.value,(e=>{var a,l;return u(),o("span",{key:e,class:"zone-tag",style:V({backgroundColor:null==(a=qe(e))?void 0:a.color})},h(null==(l=qe(e))?void 0:l.name),5)})),128))])])):d("",!0)])])):d("",!0),a[38]||(a[38]=c('<div class="operation-tips" data-v-67cd2a68><h5 data-v-67cd2a68>操作提示</h5><div class="tip-item" data-v-67cd2a68><span class="tip-key" data-v-67cd2a68>Ctrl + 点击</span><span class="tip-desc" data-v-67cd2a68>多选区域</span></div><div class="tip-item" data-v-67cd2a68><span class="tip-key" data-v-67cd2a68>拖拽空白</span><span class="tip-desc" data-v-67cd2a68>框选区域</span></div><div class="tip-item" data-v-67cd2a68><span class="tip-key" data-v-67cd2a68>拖拽区域</span><span class="tip-desc" data-v-67cd2a68>移动/编组移动</span></div><div class="tip-item" data-v-67cd2a68><span class="tip-key" data-v-67cd2a68>Esc</span><span class="tip-desc" data-v-67cd2a68>取消选择</span></div></div>',1))]),r("div",le,[a[39]||(a[39]=r("h4",null,"区域列表",-1)),r("div",te,[(u(!0),o(Y,null,X($e.value,(e=>{var a;return u(),o("div",{key:e.id,class:z(["zone-item",{active:(null==(a=_e.value)?void 0:a.id)===e.id}]),onClick:a=>Qe(e)},[r("div",{class:"zone-color",style:V({backgroundColor:e.color})},null,4),r("span",ne,h(e.name),1),r("div",ie,h(e.rows)+"×"+h(e.cols),1),v(t,{size:"small",type:"danger",icon:m(C),onClick:Z((a=>sa(e.id)),["stop"])},null,8,["icon","onClick"])],10,se)})),128))])])]),r("div",{class:"designer-canvas",ref_key:"canvasRef",ref:Le},[r("div",oe,[r("div",{class:"canvas-grid",style:V({width:`${He.value.width}px`,height:`${He.value.height}px`,backgroundColor:He.value.backgroundColor,border:"2px solid #e5e7eb",borderRadius:"8px",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)",position:"relative"}),onMousedown:aa,onClick:Je},[r("div",{class:"grid-background-layer",style:V(Ne())},null,4),(u(!0),o(Y,null,X($e.value,(e=>(u(),o("div",{key:`header-${e.id}`,class:"zone-header-external",style:V({left:`${e.x}px`,top:e.y-25+"px",color:e.color,fontWeight:"bold",fontSize:"12px",position:"absolute",zIndex:10,pointerEvents:"none"})},h(e.name)+" ("+h(e.rows)+"×"+h(e.cols)+") ",5)))),128)),Ie.value.visible?(u(),o("div",{key:0,class:"selection-box",style:V({position:"absolute",left:`${Math.min(Ie.value.startX,Ie.value.endX)}px`,top:`${Math.min(Ie.value.startY,Ie.value.endY)}px`,width:`${Math.abs(Ie.value.endX-Ie.value.startX)}px`,height:`${Math.abs(Ie.value.endY-Ie.value.startY)}px`,border:"2px dashed #3b82f6",background:"rgba(59, 130, 246, 0.1)",pointerEvents:"none",zIndex:1e3})},[Re.value?(u(),o("div",ue," 框选中 "+h(We.value.length)+" 个区域 ",1)):d("",!0)],4)):d("",!0),We.value.length>1?(u(),o("div",{key:1,class:"multi-selection-border",style:V(ea())},null,4)):d("",!0),(u(!0),o(Y,null,X($e.value,(e=>{var a,l,t;return u(),o("div",{key:e.id,class:z(["draggable-zone",{selected:(null==(a=_e.value)?void 0:a.id)===e.id||We.value.includes(e.id),"multi-selected":We.value.includes(e.id)&&We.value.length>1,"single-selected":We.value.includes(e.id)&&1===We.value.length,"being-resized":(null==(l=Se.value)?void 0:l.id)===e.id,"group-dragging":Fe.isGroupDragging&&We.value.includes(e.id)}]),style:V(Pe(e)),onMousedown:a=>((e,a)=>{if(a.target.classList.contains("resize-handle"))return;a.stopPropagation();const l=We.value.includes(e.id),t=We.value.length>1;l&&t?(Fe.isGroupDragging=!0,Fe.isDragging=!1,Fe.startX=a.clientX,Fe.startY=a.clientY,Fe.groupStartPositions=We.value.map((e=>{const a=$e.value.find((a=>a.id===e));return{id:e,startX:a.x,startY:a.y}})),S.success(`开始编组拖拽 ${We.value.length} 个区域`),_e.value=e):(We.value.includes(e.id)||(a.ctrlKey?We.value.push(e.id):We.value=[e.id]),_e.value=e,Fe.isGroupDragging=!1,Fe.isDragging=!0,Fe.startX=a.clientX,Fe.startY=a.clientY,Fe.startZoneX=e.x,Fe.startZoneY=e.y,De.value=e),a.preventDefault()})(e,a),onClick:a=>Qe(e,a)},[r("div",{class:"workstation-grid",style:V(je(e))},[(u(!0),o(Y,null,X(e.rows*e.cols,(a=>(u(),o("div",{key:a,class:"workstation-preview",style:V({backgroundColor:e.color})},h(Be(e,a)),5)))),128))],4),(null==(t=_e.value)?void 0:t.id)===e.id?(u(),o("div",de,[r("div",{class:"resize-handle nw",onMousedown:Z((a=>ra(e,"nw",a)),["stop"])},null,40,ve),r("div",{class:"resize-handle ne",onMousedown:Z((a=>ra(e,"ne",a)),["stop"])},null,40,ce),r("div",{class:"resize-handle sw",onMousedown:Z((a=>ra(e,"sw",a)),["stop"])},null,40,he),r("div",{class:"resize-handle se",onMousedown:Z((a=>ra(e,"se",a)),["stop"])},null,40,ge),r("div",{class:"resize-handle n",onMousedown:Z((a=>ra(e,"n",a)),["stop"])},null,40,pe),r("div",{class:"resize-handle s",onMousedown:Z((a=>ra(e,"s",a)),["stop"])},null,40,me),r("div",{class:"resize-handle w",onMousedown:Z((a=>ra(e,"w",a)),["stop"])},null,40,we),r("div",{class:"resize-handle e",onMousedown:Z((a=>ra(e,"e",a)),["stop"])},null,40,xe)])):d("",!0)],46,re)})),128))],36)])],512),_e.value?(u(),o("div",ye,[a[46]||(a[46]=r("h4",null,"区域属性",-1)),v(ta,{model:_e.value,"label-width":"80px",size:"small"},{default:g((()=>[v(E,{label:"区域名称"},{default:g((()=>[v(n,{modelValue:_e.value.name,"onUpdate:modelValue":a[0]||(a[0]=e=>_e.value.name=e)},null,8,["modelValue"])])),_:1}),v(E,{label:"颜色"},{default:g((()=>[v(Ce,{modelValue:_e.value.color,"onUpdate:modelValue":a[1]||(a[1]=e=>_e.value.color=e)},null,8,["modelValue"])])),_:1}),v(E,{label:"行数"},{default:g((()=>[v(Oe,{modelValue:_e.value.rows,"onUpdate:modelValue":a[2]||(a[2]=e=>_e.value.rows=e),min:1,max:20,onChange:ia},null,8,["modelValue"])])),_:1}),v(E,{label:"列数"},{default:g((()=>[v(Oe,{modelValue:_e.value.cols,"onUpdate:modelValue":a[3]||(a[3]=e=>_e.value.cols=e),min:1,max:20,onChange:ia},null,8,["modelValue"])])),_:1}),v(E,{label:"位置X"},{default:g((()=>[v(Oe,{modelValue:_e.value.x,"onUpdate:modelValue":a[4]||(a[4]=e=>_e.value.x=e),min:0,max:He.value.width-_e.value.width},null,8,["modelValue","max"])])),_:1}),v(E,{label:"位置Y"},{default:g((()=>[v(Oe,{modelValue:_e.value.y,"onUpdate:modelValue":a[5]||(a[5]=e=>_e.value.y=e),min:0,max:He.value.height-_e.value.height},null,8,["modelValue","max"])])),_:1}),v(E,{label:"宽度"},{default:g((()=>[v(Oe,{modelValue:_e.value.width,"onUpdate:modelValue":a[6]||(a[6]=e=>_e.value.width=e),min:50,max:He.value.width-_e.value.x},null,8,["modelValue","max"])])),_:1}),v(E,{label:"高度"},{default:g((()=>[v(Oe,{modelValue:_e.value.height,"onUpdate:modelValue":a[7]||(a[7]=e=>_e.value.height=e),min:40,max:He.value.height-_e.value.y},null,8,["modelValue","max"])])),_:1}),v(E,{label:"工位起始"},{default:g((()=>[v(Oe,{modelValue:_e.value.startWorkstation,"onUpdate:modelValue":a[8]||(a[8]=e=>_e.value.startWorkstation=e),min:1,max:1e3,onChange:oa},null,8,["modelValue"])])),_:1}),v(E,{label:"水平间距"},{default:g((()=>[v(Oe,{modelValue:_e.value.gapX,"onUpdate:modelValue":a[9]||(a[9]=e=>_e.value.gapX=e),min:0,max:20,step:1,placeholder:"px"},null,8,["modelValue"])])),_:1}),v(E,{label:"垂直间距"},{default:g((()=>[v(Oe,{modelValue:_e.value.gapY,"onUpdate:modelValue":a[10]||(a[10]=e=>_e.value.gapY=e),min:0,max:20,step:1,placeholder:"px"},null,8,["modelValue"])])),_:1}),r("div",fe,[a[44]||(a[44]=r("h5",null,"精确微调",-1)),r("div",be,[v(t,{size:"small",onClick:a[11]||(a[11]=e=>ua("up")),icon:m($)},{default:g((()=>a[40]||(a[40]=[p("上移")]))),_:1},8,["icon"]),r("div",ke,[v(t,{size:"small",onClick:a[12]||(a[12]=e=>ua("left")),icon:m(_)},{default:g((()=>a[41]||(a[41]=[p("左移")]))),_:1},8,["icon"]),v(t,{size:"small",onClick:a[13]||(a[13]=e=>ua("right")),icon:m(W)},{default:g((()=>a[42]||(a[42]=[p("右移")]))),_:1},8,["icon"])]),v(t,{size:"small",onClick:a[14]||(a[14]=e=>ua("down")),icon:m(D)},{default:g((()=>a[43]||(a[43]=[p("下移")]))),_:1},8,["icon"])]),r("div",Me,[v(E,{label:"微调步长"},{default:g((()=>[v(la,{modelValue:Ue.value,"onUpdate:modelValue":a[15]||(a[15]=e=>Ue.value=e),size:"small"},{default:g((()=>[v(Te,{label:"1px",value:1}),v(Te,{label:"5px",value:5}),v(Te,{label:"10px",value:10}),v(Te,{label:"20px",value:20})])),_:1},8,["modelValue"])])),_:1})])]),v(t,{type:"danger",onClick:a[16]||(a[16]=e=>sa(_e.value.id)),icon:m(C)},{default:g((()=>a[45]||(a[45]=[p("删除区域")]))),_:1},8,["icon"])])),_:1},8,["model"])])):d("",!0),_e.value?d("",!0):(u(),o("div",ze,[a[50]||(a[50]=r("h4",null,"画布配置",-1)),v(ta,{model:He.value,"label-width":"80px",size:"small"},{default:g((()=>[v(E,{label:"画布宽度"},{default:g((()=>[v(Oe,{modelValue:He.value.width,"onUpdate:modelValue":a[17]||(a[17]=e=>He.value.width=e),min:He.value.minWidth,max:He.value.maxWidth,step:100},null,8,["modelValue","min","max"])])),_:1}),v(E,{label:"画布高度"},{default:g((()=>[v(Oe,{modelValue:He.value.height,"onUpdate:modelValue":a[18]||(a[18]=e=>He.value.height=e),min:He.value.minHeight,max:He.value.maxHeight,step:100},null,8,["modelValue","min","max"])])),_:1}),v(E,{label:"网格大小"},{default:g((()=>[v(Oe,{modelValue:He.value.gridSize,"onUpdate:modelValue":a[19]||(a[19]=e=>He.value.gridSize=e),min:10,max:50,step:5},null,8,["modelValue"])])),_:1}),v(E,{label:"显示网格"},{default:g((()=>[v(da,{modelValue:He.value.showGrid,"onUpdate:modelValue":a[20]||(a[20]=e=>He.value.showGrid=e)},null,8,["modelValue"])])),_:1}),v(E,{label:"背景色"},{default:g((()=>[v(Ce,{modelValue:He.value.backgroundColor,"onUpdate:modelValue":a[21]||(a[21]=e=>He.value.backgroundColor=e)},null,8,["modelValue"])])),_:1}),r("div",Ye,[a[49]||(a[49]=r("h5",null,"画布信息",-1)),r("div",Xe,[a[47]||(a[47]=r("span",null,"尺寸比例:",-1)),r("span",null,h((He.value.width/He.value.height).toFixed(2))+":1",1)]),r("div",Ve,[a[48]||(a[48]=r("span",null,"总面积:",-1)),r("span",null,h((He.value.width*He.value.height/1e4).toFixed(1))+"万px²",1)])])])),_:1},8,["model"])])),v(va,{modelValue:Ee.value,"onUpdate:modelValue":a[23]||(a[23]=e=>Ee.value=e),title:"布局预览",width:"98%","close-on-click-modal":!1,class:"preview-dialog",fullscreen:!1,top:"2vh"},{footer:g((()=>[v(t,{onClick:a[22]||(a[22]=e=>Ee.value=!1)},{default:g((()=>a[51]||(a[51]=[p("关闭")]))),_:1}),v(t,{type:"primary",onClick:xa},{default:g((()=>a[52]||(a[52]=[p("导出配置")]))),_:1})])),default:g((()=>[r("div",Ze,[r("div",{class:"preview-canvas",style:V({width:`${He.value.width}px`,height:`${He.value.height}px`,position:"relative",margin:"0 auto"})},[r("div",{class:"grid-background-layer",style:V(Ne())},null,4),(u(!0),o(Y,null,X($e.value,(e=>(u(),o("div",{key:`preview-header-${e.id}`,class:"preview-zone-header-external",style:V({left:`${e.x}px`,top:e.y-20+"px",color:e.color,fontWeight:"bold",fontSize:"11px",position:"absolute",zIndex:10,pointerEvents:"none"})},h(e.name)+" ("+h(e.rows)+"×"+h(e.cols)+") ",5)))),128)),(u(!0),o(Y,null,X($e.value,(e=>(u(),o("div",{key:e.id,class:"preview-zone",style:V(Pe(e))},[r("div",{class:"workstation-grid",style:V(je(e))},[(u(!0),o(Y,null,X(e.rows*e.cols,(a=>(u(),o("div",{key:a,class:"workstation-preview",style:V({backgroundColor:e.color})},h(Be(e,a)),5)))),128))],4)],4)))),128))],4)])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-67cd2a68"]]),$e={class:"layout-designer-page"},_e=e({__name:"LayoutDesigner",setup(e){const a=e=>{localStorage.setItem("factoryLayoutConfig",JSON.stringify(e)),S.success(`布局配置已保存！总工位数: ${e.totalWorkstations}`)};return(e,l)=>(u(),o("div",$e,[v(Ce,{onLayoutSaved:a})]))}},[["__scopeId","data-v-994f6202"]]);export{_e as default};
