import{_ as e,l as a,r as t,c as i,m as s,b as n,e as l,w as c,a8 as o,a as r,u as d,o as u,d as p,a9 as k,Y as f,F as g,h as m,i as v,Z as y,t as C,as as h,A as T,y as S,b1 as z,J as _,bj as A,bp as b,a1 as w}from"./index-C7OOw0MO.js";const $={class:"notifications-page"},x={class:"card-header"},M={class:"header-actions"},I={class:"notification-list"},P=["onClick"],D={class:"notification-icon"},O={class:"notification-content"},U={class:"notification-title"},V={class:"notification-message"},j={class:"notification-time"},N={class:"notification-actions"},F={key:0,class:"pagination-wrapper"},H={class:"notification-list"},R=["onClick"],Y={class:"notification-icon"},J={class:"notification-content"},L={class:"notification-title"},Z={class:"notification-message"},q={class:"notification-time"},B={class:"notification-actions"},E={class:"notification-list"},G=["onClick"],K={class:"notification-icon"},Q={class:"notification-content"},W={class:"notification-title"},X={class:"notification-message"},ee={class:"notification-time"},ae={class:"notification-actions"},te=e({__name:"NotificationsListView",setup(e){const te=d(),ie=a(),se=t("all"),ne=t(!1),le=t(0),ce=t({currentPage:1,pageSize:20}),oe=i((()=>ie.notifications)),re=i((()=>ie.hasUnread)),de=i((()=>oe.value.filter((e=>["TaskAssigned","TaskStatusChanged","TaskComment","TaskAttachmentAdded","TaskMention","TaskOverdue","TaskContentChanged"].includes(e.type))))),ue=i((()=>oe.value.filter((e=>!["TaskAssigned","TaskStatusChanged","TaskComment","TaskAttachmentAdded","TaskMention","TaskOverdue","TaskContentChanged"].includes(e.type))))),pe=async()=>{ne.value=!0;try{await ie.fetchNotifications({page:ce.value.currentPage,pageSize:ce.value.pageSize,force:!0}),le.value=ie.total}catch(e){o.error("加载通知失败")}finally{ne.value=!1}},ke=()=>{},fe=async e=>{try{await ie.markAsRead(e),o.success("已标记为已读")}catch(a){o.error("标记已读失败")}},ge=async()=>{try{await ie.markAllAsRead(),o.success("已全部标记为已读")}catch(e){o.error("全部标记已读失败")}},me=async e=>{try{await w.confirm("确定要删除此通知吗？","删除通知",{type:"warning"}),await ie.deleteNotification(e),o.success("删除成功")}catch(a){"cancel"!==a&&o.error("删除通知失败")}},ve=e=>{fe(e.id),"Task"===e.referenceType&&e.referenceId?te.push(`/main/tasks/detail/${e.referenceId}`):"Task"===e.resourceType&&e.resourceId?te.push(`/main/tasks/detail/${e.resourceId}`):e.taskId&&te.push(`/main/tasks/detail/${e.taskId}`)},ye=e=>{ce.value.pageSize=e,ce.value.currentPage=1,pe()},Ce=e=>{ce.value.currentPage=e,pe()},he=e=>{if(!e)return"";const a=new Date(e),t=new Date-a;if(t<864e5){if(t<36e5){return`${Math.floor(t/6e4)} 分钟前`}return`${Math.floor(t/36e5)} 小时前`}if(t<6048e5){return`星期${["日","一","二","三","四","五","六"][a.getDay()]} ${a.getHours().toString().padStart(2,"0")}:${a.getMinutes().toString().padStart(2,"0")}`}return`${a.getFullYear()}-${(a.getMonth()+1).toString().padStart(2,"0")}-${a.getDate().toString().padStart(2,"0")} ${a.getHours().toString().padStart(2,"0")}:${a.getMinutes().toString().padStart(2,"0")}`},Te=e=>{switch(e){case"TaskAssigned":case"TaskAttachmentAdded":case"TaskContentChanged":return z;case"TaskStatusChanged":return b;case"TaskComment":return A;case"TaskMention":case"Test":default:return S;case"TaskOverdue":return _}},Se=e=>{switch(e){case"TaskAssigned":case"TaskMention":return"#67c23a";case"TaskOverdue":return"#f56c6c";case"TaskStatusChanged":case"TaskContentChanged":return"#e6a23c";case"TaskComment":case"TaskAttachmentAdded":return"#409eff";default:return"#909399"}};return s((()=>{pe()})),(e,a)=>{const t=r("el-button"),i=r("el-empty"),s=r("el-icon"),o=r("el-pagination"),d=r("el-tab-pane"),S=r("el-tabs"),z=r("el-card");return u(),n("div",$,[l(z,{class:"notifications-card"},{header:c((()=>[p("div",x,[a[4]||(a[4]=p("h2",null,"通知中心",-1)),p("div",M,[re.value?(u(),f(t,{key:0,type:"primary",onClick:ge},{default:c((()=>a[3]||(a[3]=[T(" 全部标为已读 ")]))),_:1})):k("",!0)])])])),default:c((()=>[l(S,{modelValue:se.value,"onUpdate:modelValue":a[2]||(a[2]=e=>se.value=e),onTabClick:ke},{default:c((()=>[l(d,{label:"全部",name:"all"},{default:c((()=>[p("div",I,[0===oe.value.length?(u(),f(i,{key:0,description:"暂无通知"})):k("",!0),(u(!0),n(g,null,m(oe.value,(e=>(u(),n("div",{key:e.id,class:v(["notification-item",{unread:!e.read}]),onClick:a=>ve(e)},[p("div",D,[l(s,{size:20,color:Se(e.type)},{default:c((()=>[(u(),f(y(Te(e.type))))])),_:2},1032,["color"])]),p("div",O,[p("div",U,C(e.title),1),p("div",V,C(e.content||e.message),1),p("div",j,C(he(e.timestamp||e.creationTimestamp)),1)]),p("div",N,[e.read?k("",!0):(u(),f(t,{key:0,type:"text",size:"small",onClick:h((a=>fe(e.id)),["stop"])},{default:c((()=>a[5]||(a[5]=[T(" 标为已读 ")]))),_:2},1032,["onClick"])),l(t,{type:"text",size:"small",onClick:h((a=>me(e.id)),["stop"])},{default:c((()=>a[6]||(a[6]=[T(" 删除 ")]))),_:2},1032,["onClick"])])],10,P)))),128))]),oe.value.length>0?(u(),n("div",F,[l(o,{"current-page":ce.value.currentPage,"onUpdate:currentPage":a[0]||(a[0]=e=>ce.value.currentPage=e),"page-size":ce.value.pageSize,"onUpdate:pageSize":a[1]||(a[1]=e=>ce.value.pageSize=e),"page-sizes":[10,20,50,100],small:!1,disabled:ne.value,background:!0,layout:"total, sizes, prev, pager, next, jumper",total:le.value,onSizeChange:ye,onCurrentChange:Ce},null,8,["current-page","page-size","disabled","total"])])):k("",!0)])),_:1}),l(d,{label:"任务",name:"task"},{default:c((()=>[p("div",H,[0===de.value.length?(u(),f(i,{key:0,description:"暂无任务通知"})):k("",!0),(u(!0),n(g,null,m(de.value,(e=>(u(),n("div",{key:e.id,class:v(["notification-item",{unread:!e.read}]),onClick:a=>ve(e)},[p("div",Y,[l(s,{size:20,color:Se(e.type)},{default:c((()=>[(u(),f(y(Te(e.type))))])),_:2},1032,["color"])]),p("div",J,[p("div",L,C(e.title),1),p("div",Z,C(e.content||e.message),1),p("div",q,C(he(e.timestamp||e.creationTimestamp)),1)]),p("div",B,[e.read?k("",!0):(u(),f(t,{key:0,type:"text",size:"small",onClick:h((a=>fe(e.id)),["stop"])},{default:c((()=>a[7]||(a[7]=[T(" 标为已读 ")]))),_:2},1032,["onClick"])),l(t,{type:"text",size:"small",onClick:h((a=>me(e.id)),["stop"])},{default:c((()=>a[8]||(a[8]=[T(" 删除 ")]))),_:2},1032,["onClick"])])],10,R)))),128))])])),_:1}),l(d,{label:"系统",name:"system"},{default:c((()=>[p("div",E,[0===ue.value.length?(u(),f(i,{key:0,description:"暂无系统通知"})):k("",!0),(u(!0),n(g,null,m(ue.value,(e=>(u(),n("div",{key:e.id,class:v(["notification-item",{unread:!e.read}]),onClick:a=>ve(e)},[p("div",K,[l(s,{size:20,color:Se(e.type)},{default:c((()=>[(u(),f(y(Te(e.type))))])),_:2},1032,["color"])]),p("div",Q,[p("div",W,C(e.title),1),p("div",X,C(e.content||e.message),1),p("div",ee,C(he(e.timestamp||e.creationTimestamp)),1)]),p("div",ae,[e.read?k("",!0):(u(),f(t,{key:0,type:"text",size:"small",onClick:h((a=>fe(e.id)),["stop"])},{default:c((()=>a[9]||(a[9]=[T(" 标为已读 ")]))),_:2},1032,["onClick"])),l(t,{type:"text",size:"small",onClick:h((a=>me(e.id)),["stop"])},{default:c((()=>a[10]||(a[10]=[T(" 删除 ")]))),_:2},1032,["onClick"])])],10,G)))),128))])])),_:1})])),_:1},8,["modelValue"])])),_:1})])}}},[["__scopeId","data-v-7b829eff"]]);export{te as default};
