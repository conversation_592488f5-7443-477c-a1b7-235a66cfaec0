namespace ItAssetsSystem.Application.Features.AssetStatistics.Dtos
{
    /// <summary>
    /// 资产部门统计DTO
    /// </summary>
    public class AssetDepartmentStatisticsDto
    {
        /// <summary>
        /// 部门ID
        /// </summary>
        public int DepartmentId { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        public string DepartmentName { get; set; }

        /// <summary>
        /// 资产数量
        /// </summary>
        public int AssetCount { get; set; }

        /// <summary>
        /// 正常数量
        /// </summary>
        public int NormalCount { get; set; }

        /// <summary>
        /// 故障数量
        /// </summary>
        public int FaultCount { get; set; }

        /// <summary>
        /// 维修中数量
        /// </summary>
        public int MaintenanceCount { get; set; }

        /// <summary>
        /// 正常率
        /// </summary>
        public decimal NormalRate { get; set; }

        /// <summary>
        /// 故障率
        /// </summary>
        public decimal FaultRate { get; set; }

        /// <summary>
        /// 占比百分比
        /// </summary>
        public decimal Percentage { get; set; }
    }
}