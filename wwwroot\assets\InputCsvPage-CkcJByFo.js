import{_ as a,r as e,m as t,b as s,e as l,w as i,bv as n,a as c,o as r,a7 as d,A as o,d as u,Y as p,a8 as v}from"./index-C7OOw0MO.js";const m={class:"input-csv-page"},y={key:1,class:"empty-tip"},w=a({__name:"InputCsvPage",setup(a){const w=e(""),h=e([]),g=async()=>{try{const a=await n.get("/api/v2/inputs");a.data.success&&(h.value=a.data.data)}catch(a){h.value=[]}},_=async()=>{if(w.value.trim())try{const a=await n.post("/api/v2/inputs",{content:w.value});a.data.success?(v.success("提交成功"),w.value="",await g()):v.error(a.data.message||"提交失败")}catch(a){v.error("网络错误，提交失败")}else v.warning("请输入条码内容")};return t(g),(a,e)=>{const t=c("el-input"),b=c("el-button"),f=c("el-card"),x=c("el-table-column"),k=c("el-table");return r(),s("div",m,[l(f,{shadow:"hover",class:"input-area"},{default:i((()=>[l(t,{modelValue:w.value,"onUpdate:modelValue":e[0]||(e[0]=a=>w.value=a),placeholder:"请输入条码，回车或点击提交",onKeyup:d(_,["enter"]),clearable:"",style:{width:"320px","margin-right":"12px"}},null,8,["modelValue"]),l(b,{type:"primary",onClick:_},{default:i((()=>e[1]||(e[1]=[o("提交")]))),_:1})])),_:1}),l(f,{shadow:"never",class:"list-area",style:{"margin-top":"24px"}},{default:i((()=>[e[3]||(e[3]=u("div",{class:"list-title"},"已输入内容",-1)),h.value.length?(r(),p(k,{key:0,data:h.value,style:{width:"100%","margin-top":"8px"},size:"small"},{default:i((()=>[l(x,{label:"序号",type:"index",width:"60"}),l(x,{prop:"content",label:"条码"}),l(x,{prop:"time",label:"时间序号",width:"200"}),l(x,{label:"操作",width:"100"},{default:i((a=>[l(b,{type:"danger",size:"small",onClick:e=>(async a=>{try{await n.delete(`/api/v2/inputs/${encodeURIComponent(a.time)}`),v.success("删除成功"),await g()}catch(e){v.error("删除失败")}})(a.row)},{default:i((()=>e[2]||(e[2]=[o("删除")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])):(r(),s("div",y,"暂无数据"))])),_:1})])}}},[["__scopeId","data-v-25f79e4c"]]);export{w as default};
