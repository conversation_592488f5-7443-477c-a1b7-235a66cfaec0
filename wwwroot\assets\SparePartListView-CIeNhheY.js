import{_ as e,r as l,a5 as a,c as o,m as t,a8 as d,b as r,d as u,aq as n,e as i,w as s,aJ as m,Y as p,a as c,o as b,A as f,a7 as v,F as g,h as V,i as y,t as h,a9 as _}from"./index-CkwLz8y6.js";import{u as k}from"./spareparts-CR-r4zEA.js";import{g as I,c as w,u as U,s as x,b as S}from"./spareparts-Cv2l4Tzu.js";const C={class:"spare-part-list-view"},q={class:"page-header"},T={class:"filter-container"},z={class:"pagination-container"},N={class:"dialog-footer"},F={class:"dialog-footer"},O={class:"dialog-footer"},Q=e({__name:"SparePartListView",setup(e){const Q=l(!1),j=l([]),A=l(0),B=l(!1),$=l("add"),P=l(null),R=l(!1),D=l(!1),E=l(null),K=l(null),L=l({}),J=k(),Y=a({pageIndex:1,pageSize:10,name:"",code:"",typeId:"",stockStatus:"",sortBy:"",sortOrder:""}),G=a({id:null,code:"",materialNumber:"",name:"",typeId:"",specification:"",brand:"",unit:"",initialStock:0,warningThreshold:10,minStock:5,locationId:"",price:null,remarks:""}),H=a({partId:null,quantity:1,locationId:"",reasonType:1,referenceNumber:"",remarks:""}),M=a({partId:null,quantity:1,locationId:"",reasonType:3,referenceNumber:"",relatedAssetId:null,relatedFaultId:null,remarks:""}),W={code:[{required:!0,message:"请输入备件编号",trigger:"blur"},{max:50,message:"长度不能超过50个字符",trigger:"blur"}],materialNumber:[{max:50,message:"长度不能超过50个字符",trigger:"blur"}],name:[{required:!0,message:"请输入备件名称",trigger:"blur"},{max:100,message:"长度不能超过100个字符",trigger:"blur"}],typeId:[{required:!0,message:"请选择备件类型",trigger:"change"}],unit:[{required:!0,message:"请输入单位",trigger:"blur"},{max:10,message:"长度不能超过10个字符",trigger:"blur"}],locationId:[{required:!0,message:"请选择库位",trigger:"change"}]},X={quantity:[{required:!0,message:"请输入入库数量",trigger:"blur"},{type:"number",min:1,message:"数量必须大于0",trigger:"blur"}],locationId:[{required:!0,message:"请选择库位",trigger:"change"}],reasonType:[{required:!0,message:"请选择入库类型",trigger:"change"}]},Z={quantity:[{required:!0,message:"请输入出库数量",trigger:"blur"},{type:"number",min:1,message:"数量必须大于0",trigger:"blur"}],locationId:[{required:!0,message:"请选择库位",trigger:"change"}],reasonType:[{required:!0,message:"请选择出库类型",trigger:"change"}]},ee=o((()=>J.typesTree)),le=o((()=>J.locations));t((async()=>{await Promise.all([J.fetchTypesTree(),J.fetchLocations()]),ae()}));const ae=async()=>{Q.value=!0;try{const e=await I({pageIndex:Y.pageIndex,pageSize:Y.pageSize,name:Y.name||void 0,code:Y.code||void 0,typeId:Y.typeId||void 0,stockStatus:Y.stockStatus||void 0,sortBy:Y.sortBy||void 0,sortOrder:Y.sortOrder||void 0});e.success?(j.value=e.data.items,A.value=e.data.totalCount):d.error(e.message||"获取备件列表失败")}catch(e){d.error("获取备件列表失败")}finally{Q.value=!1}},oe=()=>{Y.pageIndex=1,ae()},te=()=>{Y.name="",Y.code="",Y.typeId="",Y.stockStatus="",Y.sortBy="",Y.sortOrder="",oe()},de=e=>{e.prop&&e.order?(Y.sortBy=e.prop,Y.sortOrder="ascending"===e.order?"asc":"desc"):(Y.sortBy="",Y.sortOrder=""),ae()},re=e=>{Y.pageSize=e,ae()},ue=e=>{Y.pageIndex=e,ae()},ne=()=>{$.value="add",ie(),B.value=!0},ie=()=>{"add"===$.value&&Object.keys(G).forEach((e=>{G[e]="warningThreshold"===e?10:"minStock"===e?5:"initialStock"===e?0:""})),P.value&&P.value.resetFields()},se=async()=>{P.value&&await P.value.validate((async e=>{if(!e)return!1;try{let e;e="add"===$.value?await w(G):await U(G.id,G),e.success?(d.success("add"===$.value?"新增成功":"更新成功"),B.value=!1,ae()):d.error(e.message||("add"===$.value?"新增失败":"更新失败"))}catch(l){d.error("add"===$.value?"新增失败":"更新失败")}}))},me=async()=>{E.value&&await E.value.validate((async e=>{if(!e)return!1;try{const e=await x(H);e.success?(d.success("入库成功"),R.value=!1,ae()):d.error(e.message||"入库失败")}catch(l){d.error("入库失败")}}))},pe=async()=>{K.value&&await K.value.validate((async e=>{if(!e)return!1;try{const e=await S(M);e.success?(d.success("出库成功"),D.value=!1,ae()):d.error(e.message||"出库失败")}catch(l){d.error("出库失败")}}))};return(e,l)=>{const a=c("el-button"),o=c("el-input"),t=c("el-form-item"),k=c("el-option"),I=c("el-select"),w=c("el-form"),U=c("el-table-column"),x=c("el-table"),S=c("el-pagination"),J=c("el-input-number"),ae=c("el-dialog"),ce=m("loading");return b(),r("div",C,[u("div",q,[l[40]||(l[40]=u("h2",null,"备件台账管理",-1)),i(a,{type:"primary",onClick:ne},{default:s((()=>l[39]||(l[39]=[f("新增备件")]))),_:1})]),u("div",T,[i(w,{inline:!0,model:Y,class:"demo-form-inline"},{default:s((()=>[i(t,{label:"备件名称"},{default:s((()=>[i(o,{modelValue:Y.name,"onUpdate:modelValue":l[0]||(l[0]=e=>Y.name=e),placeholder:"备件名称",clearable:"",onKeyup:v(oe,["enter"])},null,8,["modelValue"])])),_:1}),i(t,{label:"备件编号"},{default:s((()=>[i(o,{modelValue:Y.code,"onUpdate:modelValue":l[1]||(l[1]=e=>Y.code=e),placeholder:"备件编号",clearable:"",onKeyup:v(oe,["enter"])},null,8,["modelValue"])])),_:1}),i(t,{label:"备件类型"},{default:s((()=>[i(I,{modelValue:Y.typeId,"onUpdate:modelValue":l[2]||(l[2]=e=>Y.typeId=e),placeholder:"备件类型",clearable:""},{default:s((()=>[(b(!0),r(g,null,V(ee.value,(e=>(b(),p(k,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(t,{label:"库存状态"},{default:s((()=>[i(I,{modelValue:Y.stockStatus,"onUpdate:modelValue":l[3]||(l[3]=e=>Y.stockStatus=e),placeholder:"库存状态",clearable:""},{default:s((()=>[i(k,{label:"正常",value:"normal"}),i(k,{label:"预警",value:"warning"}),i(k,{label:"不足",value:"danger"})])),_:1},8,["modelValue"])])),_:1}),i(t,null,{default:s((()=>[i(a,{type:"primary",onClick:oe},{default:s((()=>l[41]||(l[41]=[f("查询")]))),_:1}),i(a,{onClick:te},{default:s((()=>l[42]||(l[42]=[f("重置")]))),_:1})])),_:1})])),_:1},8,["model"])]),n((b(),p(x,{data:j.value,border:"",style:{width:"100%"},onSortChange:de},{default:s((()=>[i(U,{prop:"code",label:"备件编号",width:"120",sortable:"custom"}),i(U,{prop:"materialNumber",label:"物料编号",width:"120",sortable:"custom"}),i(U,{prop:"name",label:"备件名称",width:"150",sortable:"custom"}),i(U,{prop:"typeName",label:"备件类型",width:"120"}),i(U,{prop:"specification",label:"规格型号",width:"120"}),i(U,{prop:"brand",label:"品牌",width:"100"}),i(U,{prop:"stockQuantity",label:"库存数量",width:"100",sortable:"custom"},{default:s((e=>{return[u("span",{class:y((l=e.row,l.stockQuantity<=l.minStock?"stock-danger":l.stockQuantity<=l.warningThreshold?"stock-warning":"stock-normal"))},h(e.row.stockQuantity)+" "+h(e.row.unit),3)];var l})),_:1}),i(U,{prop:"minStock",label:"最小库存",width:"100"}),i(U,{prop:"locationName",label:"库位",width:"120"}),i(U,{prop:"price",label:"单价(元)",width:"100"},{default:s((e=>[f(h(e.row.price?e.row.price.toFixed(2):"-"),1)])),_:1}),i(U,{label:"操作",width:"280",fixed:"right"},{default:s((e=>[i(a,{type:"info",size:"small",onClick:l=>{return a=e.row,void d.info(`查看备件详情：${a.name}`);var a}},{default:s((()=>l[43]||(l[43]=[f("详情")]))),_:2},1032,["onClick"]),i(a,{type:"primary",size:"small",onClick:l=>{return a=e.row,$.value="edit",Object.keys(G).forEach((e=>{G[e]=a[e]})),void(B.value=!0);var a}},{default:s((()=>l[44]||(l[44]=[f("编辑")]))),_:2},1032,["onClick"]),i(a,{type:"warning",size:"small",onClick:l=>{return a=e.row,void d.info(`申请返厂：${a.name}`);var a}},{default:s((()=>l[45]||(l[45]=[f("返厂")]))),_:2},1032,["onClick"]),i(a,{type:"success",size:"small",onClick:l=>{return a=e.row,L.value=a,H.partId=a.id,H.locationId=a.locationId,H.quantity=1,void(R.value=!0);var a}},{default:s((()=>l[46]||(l[46]=[f("入库")]))),_:2},1032,["onClick"]),i(a,{type:"danger",size:"small",onClick:l=>{return a=e.row,L.value=a,M.partId=a.id,M.locationId=a.locationId,M.quantity=1,M.relatedAssetId=null,M.relatedFaultId=null,void(D.value=!0);var a}},{default:s((()=>l[47]||(l[47]=[f("出库")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[ce,Q.value]]),u("div",z,[i(S,{currentPage:Y.pageIndex,"onUpdate:currentPage":l[4]||(l[4]=e=>Y.pageIndex=e),"page-size":Y.pageSize,"onUpdate:pageSize":l[5]||(l[5]=e=>Y.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:A.value,onSizeChange:re,onCurrentChange:ue},null,8,["currentPage","page-size","total"])]),i(ae,{modelValue:B.value,"onUpdate:modelValue":l[20]||(l[20]=e=>B.value=e),title:"add"===$.value?"新增备件":"编辑备件",width:"650px",onClose:ie},{footer:s((()=>[u("div",N,[i(a,{onClick:l[19]||(l[19]=e=>B.value=!1)},{default:s((()=>l[48]||(l[48]=[f("取消")]))),_:1}),i(a,{type:"primary",onClick:se},{default:s((()=>l[49]||(l[49]=[f("确定")]))),_:1})])])),default:s((()=>[i(w,{ref_key:"formRef",ref:P,model:G,rules:W,"label-width":"100px",style:{"max-height":"500px","overflow-y":"auto"}},{default:s((()=>[i(t,{label:"备件编号",prop:"code"},{default:s((()=>[i(o,{modelValue:G.code,"onUpdate:modelValue":l[6]||(l[6]=e=>G.code=e),placeholder:"请输入备件编号"},null,8,["modelValue"])])),_:1}),i(t,{label:"物料编号",prop:"materialNumber"},{default:s((()=>[i(o,{modelValue:G.materialNumber,"onUpdate:modelValue":l[7]||(l[7]=e=>G.materialNumber=e),placeholder:"请输入物料编号"},null,8,["modelValue"])])),_:1}),i(t,{label:"备件名称",prop:"name"},{default:s((()=>[i(o,{modelValue:G.name,"onUpdate:modelValue":l[8]||(l[8]=e=>G.name=e),placeholder:"请输入备件名称"},null,8,["modelValue"])])),_:1}),i(t,{label:"备件类型",prop:"typeId"},{default:s((()=>[i(I,{modelValue:G.typeId,"onUpdate:modelValue":l[9]||(l[9]=e=>G.typeId=e),placeholder:"请选择备件类型",style:{width:"100%"}},{default:s((()=>[(b(!0),r(g,null,V(ee.value,(e=>(b(),p(k,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(t,{label:"规格型号",prop:"specification"},{default:s((()=>[i(o,{modelValue:G.specification,"onUpdate:modelValue":l[10]||(l[10]=e=>G.specification=e),placeholder:"请输入规格型号"},null,8,["modelValue"])])),_:1}),i(t,{label:"品牌",prop:"brand"},{default:s((()=>[i(o,{modelValue:G.brand,"onUpdate:modelValue":l[11]||(l[11]=e=>G.brand=e),placeholder:"请输入品牌"},null,8,["modelValue"])])),_:1}),i(t,{label:"单位",prop:"unit"},{default:s((()=>[i(o,{modelValue:G.unit,"onUpdate:modelValue":l[12]||(l[12]=e=>G.unit=e),placeholder:"请输入单位"},null,8,["modelValue"])])),_:1}),"add"===$.value?(b(),p(t,{key:0,label:"初始库存",prop:"initialStock"},{default:s((()=>[i(J,{modelValue:G.initialStock,"onUpdate:modelValue":l[13]||(l[13]=e=>G.initialStock=e),min:0,placeholder:"请输入初始库存"},null,8,["modelValue"])])),_:1})):_("",!0),i(t,{label:"预警阈值",prop:"warningThreshold"},{default:s((()=>[i(J,{modelValue:G.warningThreshold,"onUpdate:modelValue":l[14]||(l[14]=e=>G.warningThreshold=e),min:0,placeholder:"请输入预警阈值"},null,8,["modelValue"])])),_:1}),i(t,{label:"最小库存",prop:"minStock"},{default:s((()=>[i(J,{modelValue:G.minStock,"onUpdate:modelValue":l[15]||(l[15]=e=>G.minStock=e),min:0,placeholder:"请输入最小库存"},null,8,["modelValue"])])),_:1}),i(t,{label:"库位",prop:"locationId"},{default:s((()=>[i(I,{modelValue:G.locationId,"onUpdate:modelValue":l[16]||(l[16]=e=>G.locationId=e),placeholder:"请选择库位",style:{width:"100%"}},{default:s((()=>[(b(!0),r(g,null,V(le.value,(e=>(b(),p(k,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(t,{label:"价格(元)",prop:"price"},{default:s((()=>[i(J,{modelValue:G.price,"onUpdate:modelValue":l[17]||(l[17]=e=>G.price=e),min:0,precision:2,placeholder:"请输入价格"},null,8,["modelValue"])])),_:1}),i(t,{label:"备注",prop:"remarks"},{default:s((()=>[i(o,{modelValue:G.remarks,"onUpdate:modelValue":l[18]||(l[18]=e=>G.remarks=e),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"]),i(ae,{modelValue:R.value,"onUpdate:modelValue":l[28]||(l[28]=e=>R.value=e),title:"备件入库",width:"550px"},{footer:s((()=>[u("div",F,[i(a,{onClick:l[27]||(l[27]=e=>R.value=!1)},{default:s((()=>l[50]||(l[50]=[f("取消")]))),_:1}),i(a,{type:"primary",onClick:me},{default:s((()=>l[51]||(l[51]=[f("确定")]))),_:1})])])),default:s((()=>[i(w,{ref_key:"inboundFormRef",ref:E,model:H,rules:X,"label-width":"100px"},{default:s((()=>[i(t,{label:"备件名称"},{default:s((()=>[i(o,{modelValue:L.value.name,"onUpdate:modelValue":l[21]||(l[21]=e=>L.value.name=e),disabled:""},null,8,["modelValue"])])),_:1}),i(t,{label:"当前库存"},{default:s((()=>[i(o,{value:`${L.value.stockQuantity} ${L.value.unit||""}`,disabled:""},null,8,["value"])])),_:1}),i(t,{label:"入库数量",prop:"quantity"},{default:s((()=>[i(J,{modelValue:H.quantity,"onUpdate:modelValue":l[22]||(l[22]=e=>H.quantity=e),min:1},null,8,["modelValue"])])),_:1}),i(t,{label:"库位",prop:"locationId"},{default:s((()=>[i(I,{modelValue:H.locationId,"onUpdate:modelValue":l[23]||(l[23]=e=>H.locationId=e),placeholder:"请选择库位"},{default:s((()=>[(b(!0),r(g,null,V(le.value,(e=>(b(),p(k,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(t,{label:"入库类型",prop:"reasonType"},{default:s((()=>[i(I,{modelValue:H.reasonType,"onUpdate:modelValue":l[24]||(l[24]=e=>H.reasonType=e),placeholder:"请选择入库类型"},{default:s((()=>[i(k,{label:"采购入库",value:1}),i(k,{label:"退回入库",value:2}),i(k,{label:"盘点调整",value:5})])),_:1},8,["modelValue"])])),_:1}),i(t,{label:"关联单号",prop:"referenceNumber"},{default:s((()=>[i(o,{modelValue:H.referenceNumber,"onUpdate:modelValue":l[25]||(l[25]=e=>H.referenceNumber=e),placeholder:"请输入关联单号"},null,8,["modelValue"])])),_:1}),i(t,{label:"备注",prop:"remarks"},{default:s((()=>[i(o,{modelValue:H.remarks,"onUpdate:modelValue":l[26]||(l[26]=e=>H.remarks=e),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),i(ae,{modelValue:D.value,"onUpdate:modelValue":l[38]||(l[38]=e=>D.value=e),title:"备件出库",width:"550px"},{footer:s((()=>[u("div",O,[i(a,{onClick:l[37]||(l[37]=e=>D.value=!1)},{default:s((()=>l[52]||(l[52]=[f("取消")]))),_:1}),i(a,{type:"primary",onClick:pe},{default:s((()=>l[53]||(l[53]=[f("确定")]))),_:1})])])),default:s((()=>[i(w,{ref_key:"outboundFormRef",ref:K,model:M,rules:Z,"label-width":"100px"},{default:s((()=>[i(t,{label:"备件名称"},{default:s((()=>[i(o,{modelValue:L.value.name,"onUpdate:modelValue":l[29]||(l[29]=e=>L.value.name=e),disabled:""},null,8,["modelValue"])])),_:1}),i(t,{label:"当前库存"},{default:s((()=>[i(o,{value:`${L.value.stockQuantity} ${L.value.unit||""}`,disabled:""},null,8,["value"])])),_:1}),i(t,{label:"出库数量",prop:"quantity"},{default:s((()=>[i(J,{modelValue:M.quantity,"onUpdate:modelValue":l[30]||(l[30]=e=>M.quantity=e),min:1,max:L.value.stockQuantity},null,8,["modelValue","max"])])),_:1}),i(t,{label:"库位",prop:"locationId"},{default:s((()=>[i(I,{modelValue:M.locationId,"onUpdate:modelValue":l[31]||(l[31]=e=>M.locationId=e),placeholder:"请选择库位"},{default:s((()=>[(b(!0),r(g,null,V(le.value,(e=>(b(),p(k,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(t,{label:"出库类型",prop:"reasonType"},{default:s((()=>[i(I,{modelValue:M.reasonType,"onUpdate:modelValue":l[32]||(l[32]=e=>M.reasonType=e),placeholder:"请选择出库类型"},{default:s((()=>[i(k,{label:"领用出库",value:3}),i(k,{label:"报废出库",value:4}),i(k,{label:"盘点调整",value:5})])),_:1},8,["modelValue"])])),_:1}),i(t,{label:"关联单号",prop:"referenceNumber"},{default:s((()=>[i(o,{modelValue:M.referenceNumber,"onUpdate:modelValue":l[33]||(l[33]=e=>M.referenceNumber=e),placeholder:"请输入关联单号"},null,8,["modelValue"])])),_:1}),i(t,{label:"关联资产",prop:"relatedAssetId"},{default:s((()=>[i(o,{modelValue:M.relatedAssetId,"onUpdate:modelValue":l[34]||(l[34]=e=>M.relatedAssetId=e),placeholder:"请输入关联资产ID"},null,8,["modelValue"])])),_:1}),i(t,{label:"关联故障",prop:"relatedFaultId"},{default:s((()=>[i(o,{modelValue:M.relatedFaultId,"onUpdate:modelValue":l[35]||(l[35]=e=>M.relatedFaultId=e),placeholder:"请输入关联故障ID"},null,8,["modelValue"])])),_:1}),i(t,{label:"备注",prop:"remarks"},{default:s((()=>[i(o,{modelValue:M.remarks,"onUpdate:modelValue":l[36]||(l[36]=e=>M.remarks=e),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-413e7ed6"]]);export{Q as default};
