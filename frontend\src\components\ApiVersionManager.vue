<template>
  <div class="api-version-manager">
    <el-card class="version-card">
      <template #header>
        <div class="card-header">
          <span>API版本管理</span>
          <el-button 
            type="primary" 
            size="small" 
            @click="refreshStatus"
            :loading="loading"
          >
            刷新状态
          </el-button>
        </div>
      </template>

      <!-- 当前版本状态 -->
      <div class="version-status">
        <h4>当前版本状态</h4>
        <el-row :gutter="16">
          <el-col 
            v-for="(version, service) in currentVersions" 
            :key="service"
            :span="8"
          >
            <el-card class="service-card" shadow="hover">
              <div class="service-info">
                <div class="service-name">{{ getServiceDisplayName(service) }}</div>
                <div class="service-version">
                  <el-tag 
                    :type="getVersionTagType(version)"
                    size="small"
                  >
                    {{ version }}
                  </el-tag>
                </div>
                <div class="service-actions">
                  <el-button 
                    size="mini" 
                    @click="showSwitchDialog(service)"
                  >
                    切换版本
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 健康检查状态 -->
      <div class="health-status" v-if="healthStatus">
        <h4>健康检查状态</h4>
        <el-table :data="healthTableData" size="small">
          <el-table-column prop="service" label="服务" width="120">
            <template #default="{ row }">
              {{ getServiceDisplayName(row.service) }}
            </template>
          </el-table-column>
          <el-table-column prop="version" label="版本" width="80">
            <template #default="{ row }">
              <el-tag size="mini" :type="getVersionTagType(row.version)">
                {{ row.version }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag 
                size="mini" 
                :type="getStatusTagType(row.status)"
              >
                {{ getStatusDisplayName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="timestamp" label="检查时间">
            <template #default="{ row }">
              {{ formatTime(row.timestamp) }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- A/B测试控制 -->
      <div class="ab-test-control">
        <h4>A/B测试控制</h4>
        <el-row :gutter="16" align="middle">
          <el-col :span="6">
            <el-switch
              v-model="abTestEnabled"
              @change="toggleABTest"
              active-text="启用A/B测试"
              inactive-text="禁用A/B测试"
            />
          </el-col>
          <el-col :span="18">
            <el-button 
              type="success" 
              size="small" 
              @click="showStatistics"
            >
              查看统计
            </el-button>
            <el-button 
              type="warning" 
              size="small" 
              @click="resetVersions"
            >
              重置版本
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 版本切换对话框 -->
    <el-dialog
      v-model="switchDialogVisible"
      title="切换API版本"
      width="400px"
    >
      <div v-if="currentSwitchService">
        <p>当前服务: <strong>{{ getServiceDisplayName(currentSwitchService) }}</strong></p>
        <p>当前版本: <el-tag>{{ currentVersions[currentSwitchService] }}</el-tag></p>
        <el-form>
          <el-form-item label="目标版本:">
            <el-select v-model="targetVersion" placeholder="选择版本">
              <el-option
                v-for="version in getSupportedVersions(currentSwitchService)"
                :key="version"
                :label="version"
                :value="version"
                :disabled="version === currentVersions[currentSwitchService]"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="switchDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="confirmSwitch"
          :loading="switching"
        >
          确认切换
        </el-button>
      </template>
    </el-dialog>

    <!-- 统计对话框 -->
    <el-dialog
      v-model="statisticsDialogVisible"
      title="版本使用统计"
      width="600px"
    >
      <div v-if="statistics">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="总服务数">
            {{ statistics.totalServices }}
          </el-descriptions-item>
          <el-descriptions-item label="启用服务数">
            {{ statistics.enabledServices }}
          </el-descriptions-item>
          <el-descriptions-item label="V1版本服务">
            {{ statistics.v1Services }}
          </el-descriptions-item>
          <el-descriptions-item label="V1.1版本服务">
            {{ statistics.v1_1Services }}
          </el-descriptions-item>
          <el-descriptions-item label="A/B测试状态">
            <el-tag :type="statistics.abTestEnabled ? 'success' : 'info'">
              {{ statistics.abTestEnabled ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="版本切换状态">
            <el-tag :type="statistics.versionSwitchingEnabled ? 'success' : 'info'">
              {{ statistics.versionSwitchingEnabled ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="statisticsDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { useApiAdapter } from '@/plugins/api-adapter'

export default {
  name: 'ApiVersionManager',
  setup() {
    const {
      apiAdapter,
      getCurrentVersions,
      getHealthStatus,
      switchVersion,
      enableABTest,
      getVersionStatistics
    } = useApiAdapter()

    return {
      apiAdapter,
      getCurrentVersions,
      getHealthStatus,
      switchVersion,
      enableABTest,
      getVersionStatistics
    }
  },
  data() {
    return {
      loading: false,
      switching: false,
      currentVersions: {},
      healthStatus: null,
      abTestEnabled: false,
      statistics: null,
      
      // 对话框状态
      switchDialogVisible: false,
      statisticsDialogVisible: false,
      currentSwitchService: null,
      targetVersion: ''
    }
  },
  computed: {
    healthTableData() {
      if (!this.healthStatus) return []
      
      return Object.entries(this.healthStatus).map(([service, data]) => ({
        service,
        version: data.version,
        status: data.status,
        timestamp: data.timestamp,
        error: data.error
      }))
    }
  },
  async mounted() {
    await this.refreshStatus()
  },
  methods: {
    async refreshStatus() {
      this.loading = true
      try {
        this.currentVersions = this.getCurrentVersions()
        this.healthStatus = await this.getHealthStatus()
      } catch (error) {
        this.$message.error('刷新状态失败: ' + error.message)
      } finally {
        this.loading = false
      }
    },

    showSwitchDialog(service) {
      this.currentSwitchService = service
      this.targetVersion = ''
      this.switchDialogVisible = true
    },

    async confirmSwitch() {
      if (!this.targetVersion) {
        this.$message.warning('请选择目标版本')
        return
      }

      this.switching = true
      try {
        const success = await this.switchVersion(this.currentSwitchService, this.targetVersion)
        if (success) {
          this.$message.success('版本切换成功')
          this.switchDialogVisible = false
          await this.refreshStatus()
        } else {
          this.$message.error('版本切换失败')
        }
      } catch (error) {
        this.$message.error('版本切换失败: ' + error.message)
      } finally {
        this.switching = false
      }
    },

    async toggleABTest(enabled) {
      try {
        const success = await this.enableABTest({ enabled })
        if (success) {
          this.$message.success(enabled ? 'A/B测试已启用' : 'A/B测试已禁用')
        } else {
          this.$message.error('A/B测试状态切换失败')
          this.abTestEnabled = !enabled // 回滚状态
        }
      } catch (error) {
        this.$message.error('A/B测试状态切换失败: ' + error.message)
        this.abTestEnabled = !enabled // 回滚状态
      }
    },

    async showStatistics() {
      try {
        this.statistics = await this.getVersionStatistics()
        this.statisticsDialogVisible = true
      } catch (error) {
        this.$message.error('获取统计信息失败: ' + error.message)
      }
    },

    resetVersions() {
      this.$confirm('确定要重置所有服务到默认版本吗？', '确认重置', {
        type: 'warning'
      }).then(() => {
        this.apiAdapter.resetToDefaultVersions()
        this.$message.success('已重置到默认版本')
        this.refreshStatus()
      }).catch(() => {
        // 用户取消
      })
    },

    getServiceDisplayName(service) {
      const names = {
        user: '用户服务',
        asset: '资产服务',
        task: '任务服务',
        location: '位置服务',
        fault: '故障服务',
        purchase: '采购服务'
      }
      return names[service] || service
    },

    getVersionTagType(version) {
      return version === 'v1.1' ? 'success' : 'primary'
    },

    getStatusTagType(status) {
      const types = {
        healthy: 'success',
        error: 'danger',
        unknown: 'warning'
      }
      return types[status] || 'info'
    },

    getStatusDisplayName(status) {
      const names = {
        healthy: '健康',
        error: '错误',
        unknown: '未知'
      }
      return names[status] || status
    },

    getSupportedVersions(service) {
      const supported = this.apiAdapter.getSupportedVersions()
      return supported[service] || ['v1']
    },

    formatTime(timestamp) {
      if (!timestamp) return '-'
      return new Date(timestamp).toLocaleString()
    }
  }
}
</script>

<style scoped>
.api-version-manager {
  padding: 20px;
}

.version-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.version-status,
.health-status,
.ab-test-control {
  margin-bottom: 30px;
}

.service-card {
  margin-bottom: 16px;
}

.service-info {
  text-align: center;
}

.service-name {
  font-weight: bold;
  margin-bottom: 8px;
}

.service-version {
  margin-bottom: 12px;
}

.service-actions {
  margin-top: 8px;
}

h4 {
  margin-bottom: 16px;
  color: #303133;
  border-bottom: 2px solid #e4e7ed;
  padding-bottom: 8px;
}
</style>
