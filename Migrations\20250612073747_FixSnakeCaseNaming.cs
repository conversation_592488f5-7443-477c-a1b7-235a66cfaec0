﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ItAssetsSystem.Migrations
{
    public partial class FixSnakeCaseNaming : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AssetHistories_assets_AssetId",
                table: "AssetHistories");

            migrationBuilder.DropForeignKey(
                name: "FK_AssetHistories_users_OperatorId",
                table: "AssetHistories");

            migrationBuilder.DropForeignKey(
                name: "FK_AssetReceives_assets_AssetId",
                table: "AssetReceives");

            migrationBuilder.DropForeignKey(
                name: "FK_AssetReceives_locations_LocationId",
                table: "AssetReceives");

            migrationBuilder.DropForeignKey(
                name: "FK_AssetReceives_PurchaseOrders_PurchaseOrderId",
                table: "AssetReceives");

            migrationBuilder.DropForeignKey(
                name: "FK_AssetReceives_ReturnToFactories_ReturnToFactoryId",
                table: "AssetReceives");

            migrationBuilder.DropForeignKey(
                name: "FK_AssetReceives_users_ReceiverId",
                table: "AssetReceives");

            migrationBuilder.DropForeignKey(
                name: "FK_assets_assettypes_AssetTypeId",
                table: "assets");

            migrationBuilder.DropForeignKey(
                name: "FK_assets_departments_DepartmentId",
                table: "assets");

            migrationBuilder.DropForeignKey(
                name: "FK_assets_locations_LocationId",
                table: "assets");

            migrationBuilder.DropForeignKey(
                name: "FK_assettypes_assettypes_ParentId",
                table: "assettypes");

            migrationBuilder.DropForeignKey(
                name: "FK_attachments_comments_CommentId",
                table: "attachments");

            migrationBuilder.DropForeignKey(
                name: "FK_attachments_tasks_TaskId",
                table: "attachments");

            migrationBuilder.DropForeignKey(
                name: "FK_attachments_users_UploaderUserId",
                table: "attachments");

            migrationBuilder.DropForeignKey(
                name: "FK_AuditLogs_users_UserId",
                table: "AuditLogs");

            migrationBuilder.DropForeignKey(
                name: "FK_comments_comments_ParentCommentId",
                table: "comments");

            migrationBuilder.DropForeignKey(
                name: "FK_comments_tasks_TaskId",
                table: "comments");

            migrationBuilder.DropForeignKey(
                name: "FK_comments_users_UserId",
                table: "comments");

            migrationBuilder.DropForeignKey(
                name: "FK_departments_departments_ParentId",
                table: "departments");

            migrationBuilder.DropForeignKey(
                name: "FK_FaultRecords_assets_AssetId",
                table: "FaultRecords");

            migrationBuilder.DropForeignKey(
                name: "FK_FaultRecords_FaultTypes_FaultTypeId",
                table: "FaultRecords");

            migrationBuilder.DropForeignKey(
                name: "FK_FaultRecords_locations_LocationId",
                table: "FaultRecords");

            migrationBuilder.DropForeignKey(
                name: "FK_FaultRecords_users_AssigneeId",
                table: "FaultRecords");

            migrationBuilder.DropForeignKey(
                name: "FK_FaultRecords_users_ReporterId",
                table: "FaultRecords");

            migrationBuilder.DropForeignKey(
                name: "FK_FaultTypes_FaultTypes_ParentId",
                table: "FaultTypes");

            migrationBuilder.DropForeignKey(
                name: "FK_locationhistories_assets_AssetId",
                table: "locationhistories");

            migrationBuilder.DropForeignKey(
                name: "FK_locationhistories_locations_NewLocationId",
                table: "locationhistories");

            migrationBuilder.DropForeignKey(
                name: "FK_locationhistories_locations_OldLocationId",
                table: "locationhistories");

            migrationBuilder.DropForeignKey(
                name: "FK_locationhistories_users_OperatorId",
                table: "locationhistories");

            migrationBuilder.DropForeignKey(
                name: "FK_locations_departments_DefaultDepartmentId",
                table: "locations");

            migrationBuilder.DropForeignKey(
                name: "FK_locations_locations_ParentId",
                table: "locations");

            migrationBuilder.DropForeignKey(
                name: "FK_locations_users_DefaultResponsiblePersonId",
                table: "locations");

            migrationBuilder.DropForeignKey(
                name: "FK_locationusers_locations_location_id",
                table: "locationusers");

            migrationBuilder.DropForeignKey(
                name: "FK_locationusers_personnel_personnel_id",
                table: "locationusers");

            migrationBuilder.DropForeignKey(
                name: "FK_locationusers_users_UserId",
                table: "locationusers");

            migrationBuilder.DropForeignKey(
                name: "FK_MaintenanceOrders_assets_AssetId",
                table: "MaintenanceOrders");

            migrationBuilder.DropForeignKey(
                name: "FK_MaintenanceOrders_FaultRecords_FaultRecordId",
                table: "MaintenanceOrders");

            migrationBuilder.DropForeignKey(
                name: "FK_MaintenanceOrders_locations_LocationId",
                table: "MaintenanceOrders");

            migrationBuilder.DropForeignKey(
                name: "FK_MaintenanceOrders_users_AssigneeId",
                table: "MaintenanceOrders");

            migrationBuilder.DropForeignKey(
                name: "FK_Menus_Menus_ParentId",
                table: "Menus");

            migrationBuilder.DropForeignKey(
                name: "FK_pdcaplans_tasks_TaskId",
                table: "pdcaplans");

            migrationBuilder.DropForeignKey(
                name: "FK_pdcaplans_users_creator_user_id",
                table: "pdcaplans");

            migrationBuilder.DropForeignKey(
                name: "FK_pdcaplans_users_responsible_person_id",
                table: "pdcaplans");

            migrationBuilder.DropForeignKey(
                name: "FK_periodictaskschedules_tasks_template_task_id",
                table: "periodictaskschedules");

            migrationBuilder.DropForeignKey(
                name: "FK_periodictaskschedules_users_creator_user_id",
                table: "periodictaskschedules");

            migrationBuilder.DropForeignKey(
                name: "FK_personnel_departments_department_id",
                table: "personnel");

            migrationBuilder.DropForeignKey(
                name: "FK_PurchaseItems_assettypes_AssetTypeId",
                table: "PurchaseItems");

            migrationBuilder.DropForeignKey(
                name: "FK_PurchaseItems_PurchaseOrders_PurchaseOrderId",
                table: "PurchaseItems");

            migrationBuilder.DropForeignKey(
                name: "FK_PurchaseOrders_Suppliers_SupplierId",
                table: "PurchaseOrders");

            migrationBuilder.DropForeignKey(
                name: "FK_PurchaseOrders_users_RequesterId",
                table: "PurchaseOrders");

            migrationBuilder.DropForeignKey(
                name: "FK_quick_memo_categories_users_user_id",
                table: "quick_memo_categories");

            migrationBuilder.DropForeignKey(
                name: "FK_quick_memos_quick_memo_categories_category_id",
                table: "quick_memos");

            migrationBuilder.DropForeignKey(
                name: "FK_quick_memos_users_user_id",
                table: "quick_memos");

            migrationBuilder.DropForeignKey(
                name: "FK_RefreshTokens_users_UserId",
                table: "RefreshTokens");

            migrationBuilder.DropForeignKey(
                name: "FK_ReturnToFactories_assets_AssetId",
                table: "ReturnToFactories");

            migrationBuilder.DropForeignKey(
                name: "FK_ReturnToFactories_Suppliers_SupplierId",
                table: "ReturnToFactories");

            migrationBuilder.DropForeignKey(
                name: "FK_ReturnToFactories_users_OperatorId",
                table: "ReturnToFactories");

            migrationBuilder.DropForeignKey(
                name: "FK_rolemenus_Menus_MenuId",
                table: "rolemenus");

            migrationBuilder.DropForeignKey(
                name: "FK_rolemenus_roles_RoleId",
                table: "rolemenus");

            migrationBuilder.DropForeignKey(
                name: "FK_rolepermissions_permissions_PermissionId",
                table: "rolepermissions");

            migrationBuilder.DropForeignKey(
                name: "FK_rolepermissions_roles_RoleId",
                table: "rolepermissions");

            migrationBuilder.DropForeignKey(
                name: "FK_spare_part_transactions_spare_part_locations_location_id",
                table: "spare_part_transactions");

            migrationBuilder.DropForeignKey(
                name: "FK_spare_part_transactions_spare_parts_part_id",
                table: "spare_part_transactions");

            migrationBuilder.DropForeignKey(
                name: "FK_spare_part_types_spare_part_types_parent_id",
                table: "spare_part_types");

            migrationBuilder.DropForeignKey(
                name: "FK_spare_parts_spare_part_locations_location_id",
                table: "spare_parts");

            migrationBuilder.DropForeignKey(
                name: "FK_spare_parts_spare_part_types_type_id",
                table: "spare_parts");

            migrationBuilder.DropForeignKey(
                name: "FK_taskassignees_tasks_TaskId",
                table: "taskassignees");

            migrationBuilder.DropForeignKey(
                name: "FK_taskassignees_users_AssignedByUserId",
                table: "taskassignees");

            migrationBuilder.DropForeignKey(
                name: "FK_taskassignees_users_UserId",
                table: "taskassignees");

            migrationBuilder.DropForeignKey(
                name: "FK_taskhistory_attachments_AttachmentId",
                table: "taskhistory");

            migrationBuilder.DropForeignKey(
                name: "FK_taskhistory_comments_CommentId",
                table: "taskhistory");

            migrationBuilder.DropForeignKey(
                name: "FK_taskhistory_tasks_TaskId",
                table: "taskhistory");

            migrationBuilder.DropForeignKey(
                name: "FK_taskhistory_users_UserId",
                table: "taskhistory");

            migrationBuilder.DropForeignKey(
                name: "FK_tasks_assets_AssetId",
                table: "tasks");

            migrationBuilder.DropForeignKey(
                name: "FK_tasks_locations_LocationId",
                table: "tasks");

            migrationBuilder.DropForeignKey(
                name: "FK_tasks_tasks_ParentTaskId",
                table: "tasks");

            migrationBuilder.DropForeignKey(
                name: "FK_tasks_tasks_PreviousInstanceTaskId",
                table: "tasks");

            migrationBuilder.DropForeignKey(
                name: "FK_tasks_users_AssigneeUserId",
                table: "tasks");

            migrationBuilder.DropForeignKey(
                name: "FK_tasks_users_CreatorUserId",
                table: "tasks");

            migrationBuilder.DropForeignKey(
                name: "FK_userroles_roles_RoleId",
                table: "userroles");

            migrationBuilder.DropForeignKey(
                name: "FK_userroles_users_UserId",
                table: "userroles");

            migrationBuilder.DropForeignKey(
                name: "FK_users_departments_DepartmentId",
                table: "users");

            migrationBuilder.DropForeignKey(
                name: "FK_users_roles_DefaultRoleId",
                table: "users");

            migrationBuilder.DropPrimaryKey(
                name: "PK_users",
                table: "users");

            migrationBuilder.DropPrimaryKey(
                name: "PK_userroles",
                table: "userroles");

            migrationBuilder.DropPrimaryKey(
                name: "PK_tasks",
                table: "tasks");

            migrationBuilder.DropIndex(
                name: "IX_tasks_PreviousInstanceTaskId",
                table: "tasks");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Suppliers",
                table: "Suppliers");

            migrationBuilder.DropPrimaryKey(
                name: "PK_spare_parts",
                table: "spare_parts");

            migrationBuilder.DropIndex(
                name: "IX_spare_parts_code",
                table: "spare_parts");

            migrationBuilder.DropIndex(
                name: "IX_spare_parts_quantity",
                table: "spare_parts");

            migrationBuilder.DropPrimaryKey(
                name: "PK_spare_part_types",
                table: "spare_part_types");

            migrationBuilder.DropIndex(
                name: "IX_spare_part_types_code",
                table: "spare_part_types");

            migrationBuilder.DropIndex(
                name: "IX_spare_part_types_path",
                table: "spare_part_types");

            migrationBuilder.DropPrimaryKey(
                name: "PK_spare_part_transactions",
                table: "spare_part_transactions");

            migrationBuilder.DropIndex(
                name: "IX_spare_part_transactions_batch_number",
                table: "spare_part_transactions");

            migrationBuilder.DropIndex(
                name: "IX_spare_part_transactions_transaction_time",
                table: "spare_part_transactions");

            migrationBuilder.DropIndex(
                name: "IX_spare_part_transactions_type",
                table: "spare_part_transactions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_spare_part_locations",
                table: "spare_part_locations");

            migrationBuilder.DropIndex(
                name: "IX_spare_part_locations_area",
                table: "spare_part_locations");

            migrationBuilder.DropIndex(
                name: "IX_spare_part_locations_code",
                table: "spare_part_locations");

            migrationBuilder.DropPrimaryKey(
                name: "PK_roles",
                table: "roles");

            migrationBuilder.DropPrimaryKey(
                name: "PK_rolepermissions",
                table: "rolepermissions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_rolemenus",
                table: "rolemenus");

            migrationBuilder.DropPrimaryKey(
                name: "PK_ReturnToFactories",
                table: "ReturnToFactories");

            migrationBuilder.DropIndex(
                name: "IX_ReturnToFactories_OperatorId",
                table: "ReturnToFactories");

            migrationBuilder.DropPrimaryKey(
                name: "PK_quick_memos",
                table: "quick_memos");

            migrationBuilder.DropIndex(
                name: "ix_quick_memos_user_category",
                table: "quick_memos");

            migrationBuilder.DropIndex(
                name: "ix_quick_memos_user_pinned_updated",
                table: "quick_memos");

            migrationBuilder.DropPrimaryKey(
                name: "PK_quick_memo_categories",
                table: "quick_memo_categories");

            migrationBuilder.DropIndex(
                name: "ix_quick_memo_categories_user_name",
                table: "quick_memo_categories");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PurchaseOrders",
                table: "PurchaseOrders");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PurchaseItems",
                table: "PurchaseItems");

            migrationBuilder.DropPrimaryKey(
                name: "PK_personnel",
                table: "personnel");

            migrationBuilder.DropPrimaryKey(
                name: "PK_permissions",
                table: "permissions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Menus",
                table: "Menus");

            migrationBuilder.DropPrimaryKey(
                name: "PK_locationusers",
                table: "locationusers");

            migrationBuilder.DropPrimaryKey(
                name: "PK_locations",
                table: "locations");

            migrationBuilder.DropPrimaryKey(
                name: "PK_locationhistories",
                table: "locationhistories");

            migrationBuilder.DropPrimaryKey(
                name: "PK_FaultRecords",
                table: "FaultRecords");

            migrationBuilder.DropPrimaryKey(
                name: "PK_departments",
                table: "departments");

            migrationBuilder.DropPrimaryKey(
                name: "PK_comments",
                table: "comments");

            migrationBuilder.DropPrimaryKey(
                name: "PK_attachments",
                table: "attachments");

            migrationBuilder.DropPrimaryKey(
                name: "PK_assettypes",
                table: "assettypes");

            migrationBuilder.DropPrimaryKey(
                name: "PK_assets",
                table: "assets");

            migrationBuilder.DropPrimaryKey(
                name: "PK_taskhistory",
                table: "taskhistory");

            migrationBuilder.DropPrimaryKey(
                name: "PK_taskassignees",
                table: "taskassignees");

            migrationBuilder.DropPrimaryKey(
                name: "PK_RefreshTokens",
                table: "RefreshTokens");

            migrationBuilder.DropPrimaryKey(
                name: "PK_periodictaskschedules",
                table: "periodictaskschedules");

            migrationBuilder.DropIndex(
                name: "IX_periodictaskschedules_template_task_id",
                table: "periodictaskschedules");

            migrationBuilder.DropPrimaryKey(
                name: "PK_pdcaplans",
                table: "pdcaplans");

            migrationBuilder.DropIndex(
                name: "IX_pdcaplans_TaskId",
                table: "pdcaplans");

            migrationBuilder.DropPrimaryKey(
                name: "PK_MaintenanceOrders",
                table: "MaintenanceOrders");

            migrationBuilder.DropPrimaryKey(
                name: "PK_FaultTypes",
                table: "FaultTypes");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AuditLogs",
                table: "AuditLogs");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AssetReceives",
                table: "AssetReceives");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AssetHistories",
                table: "AssetHistories");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "ReturnToFactories");

            migrationBuilder.DropColumn(
                name: "Reason",
                table: "ReturnToFactories");

            migrationBuilder.DropColumn(
                name: "ReturnDate",
                table: "ReturnToFactories");

            migrationBuilder.DropColumn(
                name: "UpdatedBy",
                table: "ReturnToFactories");

            migrationBuilder.DropColumn(
                name: "IsAssetGenerated",
                table: "PurchaseItems");

            migrationBuilder.RenameTable(
                name: "Suppliers",
                newName: "suppliers");

            migrationBuilder.RenameTable(
                name: "ReturnToFactories",
                newName: "returntofactories");

            migrationBuilder.RenameTable(
                name: "PurchaseOrders",
                newName: "purchaseorders");

            migrationBuilder.RenameTable(
                name: "PurchaseItems",
                newName: "purchaseitems");

            migrationBuilder.RenameTable(
                name: "Menus",
                newName: "menus");

            migrationBuilder.RenameTable(
                name: "FaultRecords",
                newName: "faultrecords");

            migrationBuilder.RenameTable(
                name: "taskhistory",
                newName: "task_histories");

            migrationBuilder.RenameTable(
                name: "taskassignees",
                newName: "task_assignees");

            migrationBuilder.RenameTable(
                name: "RefreshTokens",
                newName: "refresh_tokens");

            migrationBuilder.RenameTable(
                name: "periodictaskschedules",
                newName: "periodic_task_schedules");

            migrationBuilder.RenameTable(
                name: "pdcaplans",
                newName: "pdca_plans");

            migrationBuilder.RenameTable(
                name: "MaintenanceOrders",
                newName: "maintenance_orders");

            migrationBuilder.RenameTable(
                name: "FaultTypes",
                newName: "fault_types");

            migrationBuilder.RenameTable(
                name: "AuditLogs",
                newName: "audit_logs");

            migrationBuilder.RenameTable(
                name: "AssetReceives",
                newName: "asset_receives");

            migrationBuilder.RenameTable(
                name: "AssetHistories",
                newName: "asset_histories");

            migrationBuilder.RenameIndex(
                name: "IX_users_Username",
                table: "users",
                newName: "ix_users_username");

            migrationBuilder.RenameIndex(
                name: "IX_users_Email",
                table: "users",
                newName: "ix_users_email");

            migrationBuilder.RenameIndex(
                name: "IX_users_DepartmentId",
                table: "users",
                newName: "ix_users_department_id");

            migrationBuilder.RenameIndex(
                name: "IX_users_DefaultRoleId",
                table: "users",
                newName: "ix_users_default_role_id");

            migrationBuilder.RenameColumn(
                name: "RoleId",
                table: "userroles",
                newName: "role_id");

            migrationBuilder.RenameColumn(
                name: "UserId",
                table: "userroles",
                newName: "user_id");

            migrationBuilder.RenameIndex(
                name: "IX_userroles_RoleId",
                table: "userroles",
                newName: "ix_userroles_role_id");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "tasks",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Progress",
                table: "tasks",
                newName: "progress");

            migrationBuilder.RenameColumn(
                name: "Priority",
                table: "tasks",
                newName: "priority");

            migrationBuilder.RenameColumn(
                name: "Points",
                table: "tasks",
                newName: "points");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "tasks",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "tasks",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "TaskType",
                table: "tasks",
                newName: "task_type");

            migrationBuilder.RenameColumn(
                name: "ProjectId",
                table: "tasks",
                newName: "project_id");

            migrationBuilder.RenameColumn(
                name: "PreviousInstanceTaskId",
                table: "tasks",
                newName: "previous_instance_task_id");

            migrationBuilder.RenameColumn(
                name: "PlanStartDate",
                table: "tasks",
                newName: "plan_start_date");

            migrationBuilder.RenameColumn(
                name: "PlanEndDate",
                table: "tasks",
                newName: "plan_end_date");

            migrationBuilder.RenameColumn(
                name: "PeriodicTaskScheduleId",
                table: "tasks",
                newName: "periodic_task_schedule_id");

            migrationBuilder.RenameColumn(
                name: "ParentTaskId",
                table: "tasks",
                newName: "parent_task_id");

            migrationBuilder.RenameColumn(
                name: "PDCAStage",
                table: "tasks",
                newName: "pdca_stage");

            migrationBuilder.RenameColumn(
                name: "LocationId",
                table: "tasks",
                newName: "location_id");

            migrationBuilder.RenameColumn(
                name: "LastUpdatedTimestamp",
                table: "tasks",
                newName: "last_updated_timestamp");

            migrationBuilder.RenameColumn(
                name: "IsOverdueAcknowledged",
                table: "tasks",
                newName: "is_overdue_acknowledged");

            migrationBuilder.RenameColumn(
                name: "IsDeleted",
                table: "tasks",
                newName: "is_deleted");

            migrationBuilder.RenameColumn(
                name: "CreatorUserId",
                table: "tasks",
                newName: "creator_user_id");

            migrationBuilder.RenameColumn(
                name: "CreationTimestamp",
                table: "tasks",
                newName: "creation_timestamp");

            migrationBuilder.RenameColumn(
                name: "AssigneeUserId",
                table: "tasks",
                newName: "assignee_user_id");

            migrationBuilder.RenameColumn(
                name: "AssetId",
                table: "tasks",
                newName: "asset_id");

            migrationBuilder.RenameColumn(
                name: "ActualStartDate",
                table: "tasks",
                newName: "actual_start_date");

            migrationBuilder.RenameColumn(
                name: "ActualEndDate",
                table: "tasks",
                newName: "actual_end_date");

            migrationBuilder.RenameColumn(
                name: "TaskId",
                table: "tasks",
                newName: "task_id");

            migrationBuilder.RenameIndex(
                name: "IX_tasks_ParentTaskId",
                table: "tasks",
                newName: "ix_tasks_parent_task_id");

            migrationBuilder.RenameIndex(
                name: "IX_tasks_LocationId",
                table: "tasks",
                newName: "ix_tasks_location_id");

            migrationBuilder.RenameIndex(
                name: "IX_tasks_CreatorUserId",
                table: "tasks",
                newName: "ix_tasks_creator_user_id");

            migrationBuilder.RenameIndex(
                name: "IX_tasks_AssigneeUserId",
                table: "tasks",
                newName: "ix_tasks_assignee_user_id");

            migrationBuilder.RenameIndex(
                name: "IX_tasks_AssetId",
                table: "tasks",
                newName: "ix_tasks_asset_id");

            migrationBuilder.RenameColumn(
                name: "Notes",
                table: "suppliers",
                newName: "notes");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "suppliers",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Code",
                table: "suppliers",
                newName: "code");

            migrationBuilder.RenameColumn(
                name: "Address",
                table: "suppliers",
                newName: "address");

            migrationBuilder.RenameColumn(
                name: "UpdatedAt",
                table: "suppliers",
                newName: "updated_at");

            migrationBuilder.RenameColumn(
                name: "IsActive",
                table: "suppliers",
                newName: "is_active");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "suppliers",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "ContactPhone",
                table: "suppliers",
                newName: "contact_phone");

            migrationBuilder.RenameColumn(
                name: "ContactPerson",
                table: "suppliers",
                newName: "contact_person");

            migrationBuilder.RenameColumn(
                name: "ContactEmail",
                table: "suppliers",
                newName: "contact_email");

            migrationBuilder.RenameIndex(
                name: "IX_spare_parts_type_id",
                table: "spare_parts",
                newName: "ix_spare_parts_type_id");

            migrationBuilder.RenameIndex(
                name: "IX_spare_parts_location_id",
                table: "spare_parts",
                newName: "ix_spare_parts_location_id");

            migrationBuilder.RenameIndex(
                name: "IX_spare_part_types_parent_id",
                table: "spare_part_types",
                newName: "ix_spare_part_types_parent_id");

            migrationBuilder.RenameIndex(
                name: "IX_spare_part_transactions_part_id",
                table: "spare_part_transactions",
                newName: "ix_spare_part_transactions_part_id");

            migrationBuilder.RenameIndex(
                name: "IX_spare_part_transactions_location_id",
                table: "spare_part_transactions",
                newName: "ix_spare_part_transactions_location_id");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "roles",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "roles",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "Code",
                table: "roles",
                newName: "code");

            migrationBuilder.RenameColumn(
                name: "UpdatedAt",
                table: "roles",
                newName: "updated_at");

            migrationBuilder.RenameColumn(
                name: "IsActive",
                table: "roles",
                newName: "is_active");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "roles",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "PermissionId",
                table: "rolepermissions",
                newName: "permission_id");

            migrationBuilder.RenameColumn(
                name: "RoleId",
                table: "rolepermissions",
                newName: "role_id");

            migrationBuilder.RenameIndex(
                name: "IX_rolepermissions_PermissionId",
                table: "rolepermissions",
                newName: "ix_rolepermissions_permission_id");

            migrationBuilder.RenameColumn(
                name: "UpdatedAt",
                table: "rolemenus",
                newName: "updated_at");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "rolemenus",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "MenuId",
                table: "rolemenus",
                newName: "menu_id");

            migrationBuilder.RenameColumn(
                name: "RoleId",
                table: "rolemenus",
                newName: "role_id");

            migrationBuilder.RenameIndex(
                name: "IX_rolemenus_MenuId",
                table: "rolemenus",
                newName: "ix_rolemenus_menu_id");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "returntofactories",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "UpdatedAt",
                table: "returntofactories",
                newName: "updated_at");

            migrationBuilder.RenameColumn(
                name: "SupplierId",
                table: "returntofactories",
                newName: "supplier_id");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "returntofactories",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "AssetId",
                table: "returntofactories",
                newName: "asset_id");

            migrationBuilder.RenameColumn(
                name: "Result",
                table: "returntofactories",
                newName: "repair_result");

            migrationBuilder.RenameColumn(
                name: "Remarks",
                table: "returntofactories",
                newName: "notes");

            migrationBuilder.RenameColumn(
                name: "OperatorId",
                table: "returntofactories",
                newName: "sender_id");

            migrationBuilder.RenameColumn(
                name: "ExpectedReturnDate",
                table: "returntofactories",
                newName: "send_time");

            migrationBuilder.RenameColumn(
                name: "ActualReturnDate",
                table: "returntofactories",
                newName: "estimated_return_time");

            migrationBuilder.RenameIndex(
                name: "IX_ReturnToFactories_SupplierId",
                table: "returntofactories",
                newName: "ix_returntofactories_supplier_id");

            migrationBuilder.RenameIndex(
                name: "IX_ReturnToFactories_AssetId",
                table: "returntofactories",
                newName: "ix_returntofactories_asset_id");

            migrationBuilder.RenameIndex(
                name: "IX_quick_memos_category_id",
                table: "quick_memos",
                newName: "ix_quick_memos_category_id");

            migrationBuilder.RenameColumn(
                name: "RequesterId",
                table: "purchaseorders",
                newName: "ApplicantId");

            migrationBuilder.RenameColumn(
                name: "OrderNumber",
                table: "purchaseorders",
                newName: "OrderCode");

            migrationBuilder.RenameColumn(
                name: "ExpectedDeliveryDate",
                table: "purchaseorders",
                newName: "EstimatedDeliveryDate");

            migrationBuilder.RenameIndex(
                name: "IX_PurchaseOrders_SupplierId",
                table: "purchaseorders",
                newName: "ix_purchaseorders_supplier_id");

            migrationBuilder.RenameIndex(
                name: "IX_PurchaseOrders_RequesterId",
                table: "purchaseorders",
                newName: "ix_purchaseorders_requester_id");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "purchaseitems",
                newName: "ItemName");

            migrationBuilder.RenameIndex(
                name: "IX_PurchaseItems_PurchaseOrderId",
                table: "purchaseitems",
                newName: "ix_purchaseitems_purchase_order_id");

            migrationBuilder.RenameIndex(
                name: "IX_PurchaseItems_AssetTypeId",
                table: "purchaseitems",
                newName: "ix_purchaseitems_asset_type_id");

            migrationBuilder.RenameIndex(
                name: "IX_personnel_department_id",
                table: "personnel",
                newName: "ix_personnel_department_id");

            migrationBuilder.RenameColumn(
                name: "Type",
                table: "permissions",
                newName: "type");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "permissions",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "permissions",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "Code",
                table: "permissions",
                newName: "code");

            migrationBuilder.RenameColumn(
                name: "IsSystem",
                table: "permissions",
                newName: "is_system");

            migrationBuilder.RenameIndex(
                name: "IX_permissions_Code",
                table: "permissions",
                newName: "ix_permissions_code");

            migrationBuilder.RenameColumn(
                name: "Path",
                table: "menus",
                newName: "path");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "menus",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Icon",
                table: "menus",
                newName: "icon");

            migrationBuilder.RenameColumn(
                name: "Component",
                table: "menus",
                newName: "component");

            migrationBuilder.RenameColumn(
                name: "Code",
                table: "menus",
                newName: "code");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "menus",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "UpdatedAt",
                table: "menus",
                newName: "updated_at");

            migrationBuilder.RenameColumn(
                name: "SortOrder",
                table: "menus",
                newName: "sort_order");

            migrationBuilder.RenameColumn(
                name: "ParentId",
                table: "menus",
                newName: "parent_id");

            migrationBuilder.RenameColumn(
                name: "IsVisible",
                table: "menus",
                newName: "is_visible");

            migrationBuilder.RenameColumn(
                name: "IsActive",
                table: "menus",
                newName: "is_active");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "menus",
                newName: "created_at");

            migrationBuilder.RenameIndex(
                name: "IX_Menus_ParentId",
                table: "menus",
                newName: "ix_menus_parent_id");

            migrationBuilder.RenameColumn(
                name: "UserId",
                table: "locationusers",
                newName: "user_id");

            migrationBuilder.RenameIndex(
                name: "IX_locationusers_personnel_id",
                table: "locationusers",
                newName: "ix_locationusers_personnel_id");

            migrationBuilder.RenameIndex(
                name: "IX_locationusers_UserId",
                table: "locationusers",
                newName: "ix_locationusers_user_id");

            migrationBuilder.RenameColumn(
                name: "Type",
                table: "locations",
                newName: "type");

            migrationBuilder.RenameColumn(
                name: "Path",
                table: "locations",
                newName: "path");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "locations",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "locations",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "Code",
                table: "locations",
                newName: "code");

            migrationBuilder.RenameColumn(
                name: "UpdatedAt",
                table: "locations",
                newName: "updated_at");

            migrationBuilder.RenameColumn(
                name: "ParentId",
                table: "locations",
                newName: "parent_id");

            migrationBuilder.RenameColumn(
                name: "IsActive",
                table: "locations",
                newName: "is_active");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "locations",
                newName: "created_at");

            migrationBuilder.RenameIndex(
                name: "IX_locations_ParentId",
                table: "locations",
                newName: "ix_locations_parent_id");

            migrationBuilder.RenameIndex(
                name: "IX_locations_DefaultResponsiblePersonId",
                table: "locations",
                newName: "ix_locations_default_responsible_person_id");

            migrationBuilder.RenameIndex(
                name: "IX_locations_DefaultDepartmentId",
                table: "locations",
                newName: "ix_locations_default_department_id");

            migrationBuilder.RenameIndex(
                name: "IX_locationhistories_OperatorId",
                table: "locationhistories",
                newName: "ix_locationhistories_operator_id");

            migrationBuilder.RenameIndex(
                name: "IX_locationhistories_OldLocationId",
                table: "locationhistories",
                newName: "ix_locationhistories_old_location_id");

            migrationBuilder.RenameIndex(
                name: "IX_locationhistories_NewLocationId",
                table: "locationhistories",
                newName: "ix_locationhistories_new_location_id");

            migrationBuilder.RenameIndex(
                name: "IX_locationhistories_AssetId",
                table: "locationhistories",
                newName: "ix_locationhistories_asset_id");

            migrationBuilder.RenameIndex(
                name: "IX_FaultRecords_ReporterId",
                table: "faultrecords",
                newName: "ix_faultrecords_reporter_id");

            migrationBuilder.RenameIndex(
                name: "IX_FaultRecords_LocationId",
                table: "faultrecords",
                newName: "ix_faultrecords_location_id");

            migrationBuilder.RenameIndex(
                name: "IX_FaultRecords_FaultTypeId",
                table: "faultrecords",
                newName: "ix_faultrecords_fault_type_id");

            migrationBuilder.RenameIndex(
                name: "IX_FaultRecords_AssigneeId",
                table: "faultrecords",
                newName: "ix_faultrecords_assignee_id");

            migrationBuilder.RenameIndex(
                name: "IX_FaultRecords_AssetId",
                table: "faultrecords",
                newName: "ix_faultrecords_asset_id");

            migrationBuilder.RenameColumn(
                name: "Path",
                table: "departments",
                newName: "path");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "departments",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "departments",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "Code",
                table: "departments",
                newName: "code");

            migrationBuilder.RenameColumn(
                name: "UpdatedAt",
                table: "departments",
                newName: "updated_at");

            migrationBuilder.RenameColumn(
                name: "ParentId",
                table: "departments",
                newName: "parent_id");

            migrationBuilder.RenameColumn(
                name: "ManagerId",
                table: "departments",
                newName: "manager_id");

            migrationBuilder.RenameColumn(
                name: "IsActive",
                table: "departments",
                newName: "is_active");

            migrationBuilder.RenameColumn(
                name: "DeputyManagerId",
                table: "departments",
                newName: "deputy_manager_id");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "departments",
                newName: "created_at");

            migrationBuilder.RenameIndex(
                name: "IX_departments_ParentId",
                table: "departments",
                newName: "ix_departments_parent_id");

            migrationBuilder.RenameColumn(
                name: "Content",
                table: "comments",
                newName: "content");

            migrationBuilder.RenameColumn(
                name: "UserId",
                table: "comments",
                newName: "user_id");

            migrationBuilder.RenameColumn(
                name: "TaskId",
                table: "comments",
                newName: "task_id");

            migrationBuilder.RenameColumn(
                name: "ParentCommentId",
                table: "comments",
                newName: "parent_comment_id");

            migrationBuilder.RenameColumn(
                name: "MentionedUserIds",
                table: "comments",
                newName: "mentioned_user_ids");

            migrationBuilder.RenameColumn(
                name: "LastUpdatedTimestamp",
                table: "comments",
                newName: "last_updated_timestamp");

            migrationBuilder.RenameColumn(
                name: "IsPinned",
                table: "comments",
                newName: "is_pinned");

            migrationBuilder.RenameColumn(
                name: "IsEdited",
                table: "comments",
                newName: "is_edited");

            migrationBuilder.RenameColumn(
                name: "CreationTimestamp",
                table: "comments",
                newName: "creation_timestamp");

            migrationBuilder.RenameColumn(
                name: "CommentId",
                table: "comments",
                newName: "comment_id");

            migrationBuilder.RenameIndex(
                name: "IX_comments_UserId",
                table: "comments",
                newName: "ix_comments_user_id");

            migrationBuilder.RenameIndex(
                name: "IX_comments_TaskId",
                table: "comments",
                newName: "ix_comments_task_id");

            migrationBuilder.RenameIndex(
                name: "IX_comments_ParentCommentId",
                table: "comments",
                newName: "ix_comments_parent_comment_id");

            migrationBuilder.RenameColumn(
                name: "UploaderUserId",
                table: "attachments",
                newName: "uploader_user_id");

            migrationBuilder.RenameColumn(
                name: "TaskId",
                table: "attachments",
                newName: "task_id");

            migrationBuilder.RenameColumn(
                name: "StoredFileName",
                table: "attachments",
                newName: "stored_file_name");

            migrationBuilder.RenameColumn(
                name: "StorageType",
                table: "attachments",
                newName: "storage_type");

            migrationBuilder.RenameColumn(
                name: "IsPreviewable",
                table: "attachments",
                newName: "is_previewable");

            migrationBuilder.RenameColumn(
                name: "FileType",
                table: "attachments",
                newName: "file_type");

            migrationBuilder.RenameColumn(
                name: "FileSize",
                table: "attachments",
                newName: "file_size");

            migrationBuilder.RenameColumn(
                name: "FilePath",
                table: "attachments",
                newName: "file_path");

            migrationBuilder.RenameColumn(
                name: "FileName",
                table: "attachments",
                newName: "file_name");

            migrationBuilder.RenameColumn(
                name: "CreationTimestamp",
                table: "attachments",
                newName: "creation_timestamp");

            migrationBuilder.RenameColumn(
                name: "CommentId",
                table: "attachments",
                newName: "comment_id");

            migrationBuilder.RenameColumn(
                name: "AttachmentId",
                table: "attachments",
                newName: "attachment_id");

            migrationBuilder.RenameIndex(
                name: "IX_attachments_UploaderUserId",
                table: "attachments",
                newName: "ix_attachments_uploader_user_id");

            migrationBuilder.RenameIndex(
                name: "IX_attachments_TaskId",
                table: "attachments",
                newName: "ix_attachments_task_id");

            migrationBuilder.RenameIndex(
                name: "IX_attachments_CommentId",
                table: "attachments",
                newName: "ix_attachments_comment_id");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "assettypes",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "assettypes",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "Code",
                table: "assettypes",
                newName: "code");

            migrationBuilder.RenameColumn(
                name: "UpdatedAt",
                table: "assettypes",
                newName: "updated_at");

            migrationBuilder.RenameColumn(
                name: "ParentId",
                table: "assettypes",
                newName: "parent_id");

            migrationBuilder.RenameColumn(
                name: "IsActive",
                table: "assettypes",
                newName: "is_active");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "assettypes",
                newName: "created_at");

            migrationBuilder.RenameIndex(
                name: "IX_assettypes_ParentId",
                table: "assettypes",
                newName: "ix_assettypes_parent_id");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "assets",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Price",
                table: "assets",
                newName: "price");

            migrationBuilder.RenameColumn(
                name: "Notes",
                table: "assets",
                newName: "notes");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "assets",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Model",
                table: "assets",
                newName: "model");

            migrationBuilder.RenameColumn(
                name: "Brand",
                table: "assets",
                newName: "brand");

            migrationBuilder.RenameColumn(
                name: "WarrantyExpireDate",
                table: "assets",
                newName: "warranty_expire_date");

            migrationBuilder.RenameColumn(
                name: "UpdatedAt",
                table: "assets",
                newName: "updated_at");

            migrationBuilder.RenameColumn(
                name: "SerialNumber",
                table: "assets",
                newName: "serial_number");

            migrationBuilder.RenameColumn(
                name: "PurchaseDate",
                table: "assets",
                newName: "purchase_date");

            migrationBuilder.RenameColumn(
                name: "LocationId",
                table: "assets",
                newName: "location_id");

            migrationBuilder.RenameColumn(
                name: "FinancialCode",
                table: "assets",
                newName: "financial_code");

            migrationBuilder.RenameColumn(
                name: "DepartmentId",
                table: "assets",
                newName: "department_id");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "assets",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "AssetTypeId",
                table: "assets",
                newName: "asset_type_id");

            migrationBuilder.RenameColumn(
                name: "AssetCode",
                table: "assets",
                newName: "asset_code");

            migrationBuilder.RenameIndex(
                name: "IX_assets_LocationId",
                table: "assets",
                newName: "ix_assets_location_id");

            migrationBuilder.RenameIndex(
                name: "IX_assets_DepartmentId",
                table: "assets",
                newName: "ix_assets_department_id");

            migrationBuilder.RenameIndex(
                name: "IX_assets_AssetTypeId",
                table: "assets",
                newName: "ix_assets_asset_type_id");

            migrationBuilder.RenameColumn(
                name: "Timestamp",
                table: "task_histories",
                newName: "timestamp");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "task_histories",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "UserId",
                table: "task_histories",
                newName: "user_id");

            migrationBuilder.RenameColumn(
                name: "TaskId",
                table: "task_histories",
                newName: "task_id");

            migrationBuilder.RenameColumn(
                name: "OldValue",
                table: "task_histories",
                newName: "old_value");

            migrationBuilder.RenameColumn(
                name: "NewValue",
                table: "task_histories",
                newName: "new_value");

            migrationBuilder.RenameColumn(
                name: "FieldName",
                table: "task_histories",
                newName: "field_name");

            migrationBuilder.RenameColumn(
                name: "CommentId",
                table: "task_histories",
                newName: "comment_id");

            migrationBuilder.RenameColumn(
                name: "AttachmentId",
                table: "task_histories",
                newName: "attachment_id");

            migrationBuilder.RenameColumn(
                name: "ActionType",
                table: "task_histories",
                newName: "action_type");

            migrationBuilder.RenameColumn(
                name: "TaskHistoryId",
                table: "task_histories",
                newName: "task_history_id");

            migrationBuilder.RenameIndex(
                name: "IX_taskhistory_UserId",
                table: "task_histories",
                newName: "ix_task_histories_user_id");

            migrationBuilder.RenameIndex(
                name: "IX_taskhistory_TaskId",
                table: "task_histories",
                newName: "ix_task_histories_task_id");

            migrationBuilder.RenameIndex(
                name: "IX_taskhistory_CommentId",
                table: "task_histories",
                newName: "ix_task_histories_comment_id");

            migrationBuilder.RenameIndex(
                name: "IX_taskhistory_AttachmentId",
                table: "task_histories",
                newName: "ix_task_histories_attachment_id");

            migrationBuilder.RenameColumn(
                name: "UserId",
                table: "task_assignees",
                newName: "user_id");

            migrationBuilder.RenameColumn(
                name: "TaskId",
                table: "task_assignees",
                newName: "task_id");

            migrationBuilder.RenameColumn(
                name: "AssignmentType",
                table: "task_assignees",
                newName: "assignment_type");

            migrationBuilder.RenameColumn(
                name: "AssignmentTimestamp",
                table: "task_assignees",
                newName: "assignment_timestamp");

            migrationBuilder.RenameColumn(
                name: "AssignedByUserId",
                table: "task_assignees",
                newName: "assigned_by_user_id");

            migrationBuilder.RenameColumn(
                name: "TaskAssigneeId",
                table: "task_assignees",
                newName: "task_assignee_id");

            migrationBuilder.RenameIndex(
                name: "IX_taskassignees_UserId",
                table: "task_assignees",
                newName: "ix_task_assignees_user_id");

            migrationBuilder.RenameIndex(
                name: "IX_taskassignees_TaskId",
                table: "task_assignees",
                newName: "ix_task_assignees_task_id");

            migrationBuilder.RenameIndex(
                name: "IX_taskassignees_AssignedByUserId",
                table: "task_assignees",
                newName: "ix_task_assignees_assigned_by_user_id");

            migrationBuilder.RenameColumn(
                name: "Token",
                table: "refresh_tokens",
                newName: "token");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "refresh_tokens",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "UserId",
                table: "refresh_tokens",
                newName: "user_id");

            migrationBuilder.RenameColumn(
                name: "JwtId",
                table: "refresh_tokens",
                newName: "jwt_id");

            migrationBuilder.RenameColumn(
                name: "IsUsed",
                table: "refresh_tokens",
                newName: "is_used");

            migrationBuilder.RenameColumn(
                name: "IsRevoked",
                table: "refresh_tokens",
                newName: "is_revoked");

            migrationBuilder.RenameColumn(
                name: "ExpiryDate",
                table: "refresh_tokens",
                newName: "expiry_date");

            migrationBuilder.RenameColumn(
                name: "AddedDate",
                table: "refresh_tokens",
                newName: "added_date");

            migrationBuilder.RenameIndex(
                name: "IX_RefreshTokens_UserId",
                table: "refresh_tokens",
                newName: "ix_refresh_tokens_user_id");

            migrationBuilder.RenameIndex(
                name: "IX_periodictaskschedules_creator_user_id",
                table: "periodic_task_schedules",
                newName: "ix_periodic_task_schedules_creator_user_id");

            migrationBuilder.RenameColumn(
                name: "TaskId",
                table: "pdca_plans",
                newName: "task_id");

            migrationBuilder.RenameIndex(
                name: "IX_pdcaplans_responsible_person_id",
                table: "pdca_plans",
                newName: "ix_pdca_plans_responsible_person_id");

            migrationBuilder.RenameIndex(
                name: "IX_pdcaplans_creator_user_id",
                table: "pdca_plans",
                newName: "ix_pdca_plans_creator_user_id");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "maintenance_orders",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Result",
                table: "maintenance_orders",
                newName: "result");

            migrationBuilder.RenameColumn(
                name: "Remarks",
                table: "maintenance_orders",
                newName: "remarks");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "maintenance_orders",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "maintenance_orders",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "UpdatedBy",
                table: "maintenance_orders",
                newName: "updated_by");

            migrationBuilder.RenameColumn(
                name: "UpdatedAt",
                table: "maintenance_orders",
                newName: "updated_at");

            migrationBuilder.RenameColumn(
                name: "PlannedCompletionDate",
                table: "maintenance_orders",
                newName: "planned_completion_date");

            migrationBuilder.RenameColumn(
                name: "OrderNumber",
                table: "maintenance_orders",
                newName: "order_number");

            migrationBuilder.RenameColumn(
                name: "MaintenanceType",
                table: "maintenance_orders",
                newName: "maintenance_type");

            migrationBuilder.RenameColumn(
                name: "MaintenanceDate",
                table: "maintenance_orders",
                newName: "maintenance_date");

            migrationBuilder.RenameColumn(
                name: "MaintenanceCost",
                table: "maintenance_orders",
                newName: "maintenance_cost");

            migrationBuilder.RenameColumn(
                name: "LocationId",
                table: "maintenance_orders",
                newName: "location_id");

            migrationBuilder.RenameColumn(
                name: "FaultRecordId",
                table: "maintenance_orders",
                newName: "fault_record_id");

            migrationBuilder.RenameColumn(
                name: "CreatedBy",
                table: "maintenance_orders",
                newName: "created_by");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "maintenance_orders",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "AssigneeId",
                table: "maintenance_orders",
                newName: "assignee_id");

            migrationBuilder.RenameColumn(
                name: "AssetId",
                table: "maintenance_orders",
                newName: "asset_id");

            migrationBuilder.RenameColumn(
                name: "ActualCompletionDate",
                table: "maintenance_orders",
                newName: "actual_completion_date");

            migrationBuilder.RenameIndex(
                name: "IX_MaintenanceOrders_LocationId",
                table: "maintenance_orders",
                newName: "ix_maintenance_orders_location_id");

            migrationBuilder.RenameIndex(
                name: "IX_MaintenanceOrders_FaultRecordId",
                table: "maintenance_orders",
                newName: "ix_maintenance_orders_fault_record_id");

            migrationBuilder.RenameIndex(
                name: "IX_MaintenanceOrders_AssigneeId",
                table: "maintenance_orders",
                newName: "ix_maintenance_orders_assignee_id");

            migrationBuilder.RenameIndex(
                name: "IX_MaintenanceOrders_AssetId",
                table: "maintenance_orders",
                newName: "ix_maintenance_orders_asset_id");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "fault_types",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "fault_types",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "Code",
                table: "fault_types",
                newName: "code");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "fault_types",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "UpdatedAt",
                table: "fault_types",
                newName: "updated_at");

            migrationBuilder.RenameColumn(
                name: "SortOrder",
                table: "fault_types",
                newName: "sort_order");

            migrationBuilder.RenameColumn(
                name: "ParentId",
                table: "fault_types",
                newName: "parent_id");

            migrationBuilder.RenameColumn(
                name: "IsActive",
                table: "fault_types",
                newName: "is_active");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "fault_types",
                newName: "created_at");

            migrationBuilder.RenameIndex(
                name: "IX_FaultTypes_ParentId",
                table: "fault_types",
                newName: "ix_fault_types_parent_id");

            migrationBuilder.RenameColumn(
                name: "Username",
                table: "audit_logs",
                newName: "username");

            migrationBuilder.RenameColumn(
                name: "Target",
                table: "audit_logs",
                newName: "target");

            migrationBuilder.RenameColumn(
                name: "Result",
                table: "audit_logs",
                newName: "result");

            migrationBuilder.RenameColumn(
                name: "Module",
                table: "audit_logs",
                newName: "module");

            migrationBuilder.RenameColumn(
                name: "Function",
                table: "audit_logs",
                newName: "function");

            migrationBuilder.RenameColumn(
                name: "Content",
                table: "audit_logs",
                newName: "content");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "audit_logs",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "UserId",
                table: "audit_logs",
                newName: "user_id");

            migrationBuilder.RenameColumn(
                name: "UpdatedAt",
                table: "audit_logs",
                newName: "updated_at");

            migrationBuilder.RenameColumn(
                name: "TargetId",
                table: "audit_logs",
                newName: "target_id");

            migrationBuilder.RenameColumn(
                name: "IPAddress",
                table: "audit_logs",
                newName: "ip_address");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "audit_logs",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "ActionType",
                table: "audit_logs",
                newName: "action_type");

            migrationBuilder.RenameIndex(
                name: "IX_AuditLogs_UserId",
                table: "audit_logs",
                newName: "ix_audit_logs_user_id");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "asset_receives",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Remarks",
                table: "asset_receives",
                newName: "remarks");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "asset_receives",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "UpdatedBy",
                table: "asset_receives",
                newName: "updated_by");

            migrationBuilder.RenameColumn(
                name: "UpdatedAt",
                table: "asset_receives",
                newName: "updated_at");

            migrationBuilder.RenameColumn(
                name: "ReturnToFactoryId",
                table: "asset_receives",
                newName: "return_to_factory_id");

            migrationBuilder.RenameColumn(
                name: "ReceiverId",
                table: "asset_receives",
                newName: "receiver_id");

            migrationBuilder.RenameColumn(
                name: "ReceiveType",
                table: "asset_receives",
                newName: "receive_type");

            migrationBuilder.RenameColumn(
                name: "ReceiveDate",
                table: "asset_receives",
                newName: "receive_date");

            migrationBuilder.RenameColumn(
                name: "PurchaseOrderId",
                table: "asset_receives",
                newName: "purchase_order_id");

            migrationBuilder.RenameColumn(
                name: "LocationId",
                table: "asset_receives",
                newName: "location_id");

            migrationBuilder.RenameColumn(
                name: "InspectionResult",
                table: "asset_receives",
                newName: "inspection_result");

            migrationBuilder.RenameColumn(
                name: "CreatedBy",
                table: "asset_receives",
                newName: "created_by");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "asset_receives",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "AssetId",
                table: "asset_receives",
                newName: "asset_id");

            migrationBuilder.RenameIndex(
                name: "IX_AssetReceives_ReturnToFactoryId",
                table: "asset_receives",
                newName: "ix_asset_receives_return_to_factory_id");

            migrationBuilder.RenameIndex(
                name: "IX_AssetReceives_ReceiverId",
                table: "asset_receives",
                newName: "ix_asset_receives_receiver_id");

            migrationBuilder.RenameIndex(
                name: "IX_AssetReceives_PurchaseOrderId",
                table: "asset_receives",
                newName: "ix_asset_receives_purchase_order_id");

            migrationBuilder.RenameIndex(
                name: "IX_AssetReceives_LocationId",
                table: "asset_receives",
                newName: "ix_asset_receives_location_id");

            migrationBuilder.RenameIndex(
                name: "IX_AssetReceives_AssetId",
                table: "asset_receives",
                newName: "ix_asset_receives_asset_id");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "asset_histories",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "asset_histories",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "UpdatedAt",
                table: "asset_histories",
                newName: "updated_at");

            migrationBuilder.RenameColumn(
                name: "OperatorId",
                table: "asset_histories",
                newName: "operator_id");

            migrationBuilder.RenameColumn(
                name: "OperationType",
                table: "asset_histories",
                newName: "operation_type");

            migrationBuilder.RenameColumn(
                name: "OperationTime",
                table: "asset_histories",
                newName: "operation_time");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "asset_histories",
                newName: "created_at");

            migrationBuilder.RenameColumn(
                name: "AssetId",
                table: "asset_histories",
                newName: "asset_id");

            migrationBuilder.RenameIndex(
                name: "IX_AssetHistories_OperatorId",
                table: "asset_histories",
                newName: "ix_asset_histories_operator_id");

            migrationBuilder.RenameIndex(
                name: "IX_AssetHistories_AssetId",
                table: "asset_histories",
                newName: "ix_asset_histories_asset_id");

            migrationBuilder.AddColumn<string>(
                name: "material_number",
                table: "spare_parts",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<DateTime>(
                name: "actual_return_time",
                table: "returntofactories",
                type: "datetime(6)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "code",
                table: "returntofactories",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<int>(
                name: "fault_record_id",
                table: "returntofactories",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<bool>(
                name: "in_warranty",
                table: "returntofactories",
                type: "tinyint(1)",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<decimal>(
                name: "repair_cost",
                table: "returntofactories",
                type: "decimal(65,30)",
                nullable: true);

            migrationBuilder.AlterColumn<bool>(
                name: "is_pinned",
                table: "quick_memos",
                type: "tinyint(1)",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "tinyint(1)",
                oldDefaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "ApplicationTime",
                table: "purchaseorders",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "ApprovalTime",
                table: "purchaseorders",
                type: "datetime(6)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ApproverId",
                table: "purchaseorders",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "purchaseorders",
                type: "longtext",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "purchaseorders",
                type: "longtext",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<int>(
                name: "AssetTypeId",
                table: "purchaseitems",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddColumn<string>(
                name: "ItemCode",
                table: "purchaseitems",
                type: "longtext",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<int>(
                name: "AssetId",
                table: "faultrecords",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddColumn<long>(
                name: "template_task_task_id",
                table: "periodic_task_schedules",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddPrimaryKey(
                name: "pk_users",
                table: "users",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_userroles",
                table: "userroles",
                columns: new[] { "user_id", "role_id" });

            migrationBuilder.AddPrimaryKey(
                name: "pk_tasks",
                table: "tasks",
                column: "task_id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_suppliers",
                table: "suppliers",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_spare_parts",
                table: "spare_parts",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_spare_part_types",
                table: "spare_part_types",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_spare_part_transactions",
                table: "spare_part_transactions",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_spare_part_locations",
                table: "spare_part_locations",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_roles",
                table: "roles",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_rolepermissions",
                table: "rolepermissions",
                columns: new[] { "role_id", "permission_id" });

            migrationBuilder.AddPrimaryKey(
                name: "pk_rolemenus",
                table: "rolemenus",
                columns: new[] { "role_id", "menu_id" });

            migrationBuilder.AddPrimaryKey(
                name: "pk_returntofactories",
                table: "returntofactories",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_quick_memos",
                table: "quick_memos",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_quick_memo_categories",
                table: "quick_memo_categories",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_purchaseorders",
                table: "purchaseorders",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_purchaseitems",
                table: "purchaseitems",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_personnel",
                table: "personnel",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_permissions",
                table: "permissions",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_menus",
                table: "menus",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_locationusers",
                table: "locationusers",
                columns: new[] { "location_id", "personnel_id", "user_type" });

            migrationBuilder.AddPrimaryKey(
                name: "pk_locations",
                table: "locations",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_locationhistories",
                table: "locationhistories",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_faultrecords",
                table: "faultrecords",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_departments",
                table: "departments",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_comments",
                table: "comments",
                column: "comment_id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_attachments",
                table: "attachments",
                column: "attachment_id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_assettypes",
                table: "assettypes",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_assets",
                table: "assets",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_task_histories",
                table: "task_histories",
                column: "task_history_id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_task_assignees",
                table: "task_assignees",
                column: "task_assignee_id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_refresh_tokens",
                table: "refresh_tokens",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_periodic_task_schedules",
                table: "periodic_task_schedules",
                column: "periodic_task_schedule_id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_pdca_plans",
                table: "pdca_plans",
                column: "pdca_plan_id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_maintenance_orders",
                table: "maintenance_orders",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_fault_types",
                table: "fault_types",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_audit_logs",
                table: "audit_logs",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_asset_receives",
                table: "asset_receives",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_asset_histories",
                table: "asset_histories",
                column: "id");

            migrationBuilder.CreateTable(
                name: "AssetSnapshots",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    SnapshotDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    AssetId = table.Column<int>(type: "int", nullable: false),
                    AssetCode = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    FinancialCode = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    AssetName = table.Column<string>(type: "varchar(200)", maxLength: 200, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    AssetTypeId = table.Column<int>(type: "int", nullable: false),
                    LocationId = table.Column<int>(type: "int", nullable: true),
                    DepartmentId = table.Column<int>(type: "int", nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false),
                    Price = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    PurchaseDate = table.Column<DateTime>(type: "datetime(6)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_asset_snapshots", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "notifications",
                columns: table => new
                {
                    notification_id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    user_id = table.Column<int>(type: "int", nullable: false),
                    title = table.Column<string>(type: "varchar(255)", maxLength: 255, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    content = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    type = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    reference_id = table.Column<long>(type: "bigint", nullable: true),
                    reference_type = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    is_read = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    creation_timestamp = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    read_timestamp = table.Column<DateTime>(type: "datetime(6)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_notifications", x => x.notification_id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "periodic_task_schedule_assignees",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    periodic_task_schedule_id = table.Column<long>(type: "bigint", nullable: false),
                    user_id = table.Column<int>(type: "int", nullable: false),
                    created_at = table.Column<DateTime>(type: "datetime(6)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_periodic_task_schedule_assignees", x => x.id);
                    table.ForeignKey(
                        name: "fk_periodic_task_schedule_assignees_periodic_task_schedules_per",
                        column: x => x.periodic_task_schedule_id,
                        principalTable: "periodic_task_schedules",
                        principalColumn: "periodic_task_schedule_id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "RepairOrders",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    OrderCode = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    SupplierId = table.Column<int>(type: "int", nullable: false),
                    SendDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    ExpectedReturnDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    ActualReturnDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    total_cost = table.Column<decimal>(type: "decimal(65,30)", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false),
                    notes = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    creator_id = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_repair_orders", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RepairOrders_Suppliers_SupplierId",
                        column: x => x.SupplierId,
                        principalTable: "suppliers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_RepairOrders_Users_CreatorId",
                        column: x => x.creator_id,
                        principalTable: "users",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "RepairItems",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    RepairOrderId = table.Column<int>(type: "int", nullable: false),
                    AssetId = table.Column<int>(type: "int", nullable: false),
                    FaultRecordId = table.Column<int>(type: "int", nullable: true),
                    Description = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    RepairCost = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    RepairStatus = table.Column<int>(type: "int", nullable: false),
                    RepairResult = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4")
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_repair_items", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RepairItems_Assets_AssetId",
                        column: x => x.AssetId,
                        principalTable: "assets",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_RepairItems_FaultRecords_FaultRecordId",
                        column: x => x.FaultRecordId,
                        principalTable: "faultrecords",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_RepairItems_RepairOrders_RepairOrderId",
                        column: x => x.RepairOrderId,
                        principalTable: "RepairOrders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "ix_quick_memo_categories_user_id",
                table: "quick_memo_categories",
                column: "user_id");

            migrationBuilder.CreateIndex(
                name: "ix_purchaseorders_approver_id",
                table: "purchaseorders",
                column: "ApproverId");

            migrationBuilder.CreateIndex(
                name: "ix_periodic_task_schedules_template_task_task_id",
                table: "periodic_task_schedules",
                column: "template_task_task_id");

            migrationBuilder.CreateIndex(
                name: "ix_pdca_plans_task_id",
                table: "pdca_plans",
                column: "task_id");

            migrationBuilder.CreateIndex(
                name: "IX_AssetSnapshots_AssetId",
                table: "AssetSnapshots",
                column: "AssetId");

            migrationBuilder.CreateIndex(
                name: "IX_AssetSnapshots_SnapshotDate",
                table: "AssetSnapshots",
                column: "SnapshotDate");

            migrationBuilder.CreateIndex(
                name: "UK_AssetSnapshots_Date_AssetId",
                table: "AssetSnapshots",
                columns: new[] { "SnapshotDate", "AssetId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_periodic_task_schedule_assignees_periodic_task_schedule_id",
                table: "periodic_task_schedule_assignees",
                column: "periodic_task_schedule_id");

            migrationBuilder.CreateIndex(
                name: "IX_RepairItems_AssetId",
                table: "RepairItems",
                column: "AssetId");

            migrationBuilder.CreateIndex(
                name: "IX_RepairItems_FaultRecordId",
                table: "RepairItems",
                column: "FaultRecordId");

            migrationBuilder.CreateIndex(
                name: "IX_RepairItems_RepairOrderId",
                table: "RepairItems",
                column: "RepairOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_RepairItems_RepairStatus",
                table: "RepairItems",
                column: "RepairStatus");

            migrationBuilder.CreateIndex(
                name: "ix_repair_orders_creator_id",
                table: "RepairOrders",
                column: "creator_id");

            migrationBuilder.CreateIndex(
                name: "IX_RepairOrders_OrderCode",
                table: "RepairOrders",
                column: "OrderCode",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_RepairOrders_SendDate",
                table: "RepairOrders",
                column: "SendDate");

            migrationBuilder.CreateIndex(
                name: "IX_RepairOrders_Status",
                table: "RepairOrders",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_RepairOrders_SupplierId",
                table: "RepairOrders",
                column: "SupplierId");

            migrationBuilder.AddForeignKey(
                name: "fk_asset_histories_assets_asset_id",
                table: "asset_histories",
                column: "asset_id",
                principalTable: "assets",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_asset_histories_users_operator_id",
                table: "asset_histories",
                column: "operator_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_asset_receives_assets_asset_id",
                table: "asset_receives",
                column: "asset_id",
                principalTable: "assets",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_asset_receives_locations_location_id",
                table: "asset_receives",
                column: "location_id",
                principalTable: "locations",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_asset_receives_purchase_orders_purchase_order_id",
                table: "asset_receives",
                column: "purchase_order_id",
                principalTable: "purchaseorders",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "fk_asset_receives_return_to_factories_return_to_factory_id",
                table: "asset_receives",
                column: "return_to_factory_id",
                principalTable: "returntofactories",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "fk_asset_receives_users_receiver_id",
                table: "asset_receives",
                column: "receiver_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_assets_asset_types_asset_type_id",
                table: "assets",
                column: "asset_type_id",
                principalTable: "assettypes",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_assets_departments_department_id",
                table: "assets",
                column: "department_id",
                principalTable: "departments",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_assets_locations_location_id",
                table: "assets",
                column: "location_id",
                principalTable: "locations",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_assettypes_assettypes_parent_id",
                table: "assettypes",
                column: "parent_id",
                principalTable: "assettypes",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_attachments_comments_comment_id",
                table: "attachments",
                column: "comment_id",
                principalTable: "comments",
                principalColumn: "comment_id");

            migrationBuilder.AddForeignKey(
                name: "fk_attachments_tasks_task_id",
                table: "attachments",
                column: "task_id",
                principalTable: "tasks",
                principalColumn: "task_id");

            migrationBuilder.AddForeignKey(
                name: "fk_attachments_users_uploader_user_id",
                table: "attachments",
                column: "uploader_user_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_audit_logs_users_user_id",
                table: "audit_logs",
                column: "user_id",
                principalTable: "users",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_comments_comments_parent_comment_id",
                table: "comments",
                column: "parent_comment_id",
                principalTable: "comments",
                principalColumn: "comment_id");

            migrationBuilder.AddForeignKey(
                name: "fk_comments_tasks_task_id",
                table: "comments",
                column: "task_id",
                principalTable: "tasks",
                principalColumn: "task_id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_comments_users_user_id",
                table: "comments",
                column: "user_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_departments_departments_parent_id",
                table: "departments",
                column: "parent_id",
                principalTable: "departments",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_fault_types_fault_types_parent_id",
                table: "fault_types",
                column: "parent_id",
                principalTable: "fault_types",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_faultrecords_assets_asset_id",
                table: "faultrecords",
                column: "AssetId",
                principalTable: "assets",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_faultrecords_fault_types_fault_type_id",
                table: "faultrecords",
                column: "FaultTypeId",
                principalTable: "fault_types",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_faultrecords_locations_location_id",
                table: "faultrecords",
                column: "LocationId",
                principalTable: "locations",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_faultrecords_users_assignee_id",
                table: "faultrecords",
                column: "AssigneeId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_faultrecords_users_reporter_id",
                table: "faultrecords",
                column: "ReporterId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_locationhistories_assets_asset_id",
                table: "locationhistories",
                column: "AssetId",
                principalTable: "assets",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_locationhistories_locations_new_location_id",
                table: "locationhistories",
                column: "NewLocationId",
                principalTable: "locations",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_locationhistories_locations_old_location_id",
                table: "locationhistories",
                column: "OldLocationId",
                principalTable: "locations",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_locationhistories_users_operator_id",
                table: "locationhistories",
                column: "OperatorId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_locations_departments_default_department_id",
                table: "locations",
                column: "DefaultDepartmentId",
                principalTable: "departments",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_locations_locations_parent_id",
                table: "locations",
                column: "parent_id",
                principalTable: "locations",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_locations_users_default_responsible_person_id",
                table: "locations",
                column: "DefaultResponsiblePersonId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_locationusers_locations_location_id",
                table: "locationusers",
                column: "location_id",
                principalTable: "locations",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_locationusers_personnel_personnel_id",
                table: "locationusers",
                column: "personnel_id",
                principalTable: "personnel",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_locationusers_users_user_id",
                table: "locationusers",
                column: "user_id",
                principalTable: "users",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_maintenance_orders_assets_asset_id",
                table: "maintenance_orders",
                column: "asset_id",
                principalTable: "assets",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_maintenance_orders_fault_records_fault_record_id",
                table: "maintenance_orders",
                column: "fault_record_id",
                principalTable: "faultrecords",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "fk_maintenance_orders_locations_location_id",
                table: "maintenance_orders",
                column: "location_id",
                principalTable: "locations",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_maintenance_orders_users_assignee_id",
                table: "maintenance_orders",
                column: "assignee_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_menus_menus_parent_id",
                table: "menus",
                column: "parent_id",
                principalTable: "menus",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_pdca_plans_tasks_task_id",
                table: "pdca_plans",
                column: "task_id",
                principalTable: "tasks",
                principalColumn: "task_id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_pdca_plans_users_creator_user_id",
                table: "pdca_plans",
                column: "creator_user_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_pdca_plans_users_responsible_person_id",
                table: "pdca_plans",
                column: "responsible_person_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_periodic_task_schedules_tasks_template_task_task_id",
                table: "periodic_task_schedules",
                column: "template_task_task_id",
                principalTable: "tasks",
                principalColumn: "task_id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_periodic_task_schedules_users_creator_user_id",
                table: "periodic_task_schedules",
                column: "creator_user_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_personnel_departments_department_id",
                table: "personnel",
                column: "department_id",
                principalTable: "departments",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_purchaseitems_assettypes_asset_type_id",
                table: "purchaseitems",
                column: "AssetTypeId",
                principalTable: "assettypes",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_purchaseitems_purchaseorders_purchase_order_id",
                table: "purchaseitems",
                column: "PurchaseOrderId",
                principalTable: "purchaseorders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_purchaseorders_suppliers_supplier_id",
                table: "purchaseorders",
                column: "SupplierId",
                principalTable: "suppliers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_purchaseorders_users_approver_id",
                table: "purchaseorders",
                column: "ApproverId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "fk_purchaseorders_users_requester_id",
                table: "purchaseorders",
                column: "ApplicantId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_quick_memo_categories_users_user_id",
                table: "quick_memo_categories",
                column: "user_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_quick_memos_quick_memo_categories_category_id",
                table: "quick_memos",
                column: "category_id",
                principalTable: "quick_memo_categories",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_quick_memos_users_user_id",
                table: "quick_memos",
                column: "user_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_refresh_tokens_users_user_id",
                table: "refresh_tokens",
                column: "user_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_returntofactories_assets_asset_id",
                table: "returntofactories",
                column: "asset_id",
                principalTable: "assets",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_returntofactories_suppliers_supplier_id",
                table: "returntofactories",
                column: "supplier_id",
                principalTable: "suppliers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_rolemenus_menus_menu_id",
                table: "rolemenus",
                column: "menu_id",
                principalTable: "menus",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_rolemenus_roles_role_id",
                table: "rolemenus",
                column: "role_id",
                principalTable: "roles",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_rolepermissions_permissions_permission_id",
                table: "rolepermissions",
                column: "permission_id",
                principalTable: "permissions",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_rolepermissions_roles_role_id",
                table: "rolepermissions",
                column: "role_id",
                principalTable: "roles",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_spare_part_transactions_spare_part_locations_location_id",
                table: "spare_part_transactions",
                column: "location_id",
                principalTable: "spare_part_locations",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_spare_part_transactions_spare_parts_part_id",
                table: "spare_part_transactions",
                column: "part_id",
                principalTable: "spare_parts",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_spare_part_types_spare_part_types_parent_id",
                table: "spare_part_types",
                column: "parent_id",
                principalTable: "spare_part_types",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_spare_parts_spare_part_locations_location_id",
                table: "spare_parts",
                column: "location_id",
                principalTable: "spare_part_locations",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_spare_parts_spare_part_types_type_id",
                table: "spare_parts",
                column: "type_id",
                principalTable: "spare_part_types",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_task_assignees_tasks_task_id",
                table: "task_assignees",
                column: "task_id",
                principalTable: "tasks",
                principalColumn: "task_id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_task_assignees_users_assigned_by_user_id",
                table: "task_assignees",
                column: "assigned_by_user_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_task_assignees_users_user_id",
                table: "task_assignees",
                column: "user_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_task_histories_attachments_attachment_id",
                table: "task_histories",
                column: "attachment_id",
                principalTable: "attachments",
                principalColumn: "attachment_id");

            migrationBuilder.AddForeignKey(
                name: "fk_task_histories_comments_comment_id",
                table: "task_histories",
                column: "comment_id",
                principalTable: "comments",
                principalColumn: "comment_id");

            migrationBuilder.AddForeignKey(
                name: "fk_task_histories_tasks_task_id",
                table: "task_histories",
                column: "task_id",
                principalTable: "tasks",
                principalColumn: "task_id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_task_histories_users_user_id",
                table: "task_histories",
                column: "user_id",
                principalTable: "users",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_tasks_assets_asset_id",
                table: "tasks",
                column: "asset_id",
                principalTable: "assets",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_tasks_locations_location_id",
                table: "tasks",
                column: "location_id",
                principalTable: "locations",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_tasks_tasks_parent_task_id",
                table: "tasks",
                column: "parent_task_id",
                principalTable: "tasks",
                principalColumn: "task_id");

            migrationBuilder.AddForeignKey(
                name: "fk_tasks_users_assignee_user_id",
                table: "tasks",
                column: "assignee_user_id",
                principalTable: "users",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_tasks_users_creator_user_id",
                table: "tasks",
                column: "creator_user_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_userroles_roles_role_id",
                table: "userroles",
                column: "role_id",
                principalTable: "roles",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_userroles_users_user_id",
                table: "userroles",
                column: "user_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_users_departments_department_id",
                table: "users",
                column: "DepartmentId",
                principalTable: "departments",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_users_roles_default_role_id",
                table: "users",
                column: "DefaultRoleId",
                principalTable: "roles",
                principalColumn: "id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_asset_histories_assets_asset_id",
                table: "asset_histories");

            migrationBuilder.DropForeignKey(
                name: "fk_asset_histories_users_operator_id",
                table: "asset_histories");

            migrationBuilder.DropForeignKey(
                name: "fk_asset_receives_assets_asset_id",
                table: "asset_receives");

            migrationBuilder.DropForeignKey(
                name: "fk_asset_receives_locations_location_id",
                table: "asset_receives");

            migrationBuilder.DropForeignKey(
                name: "fk_asset_receives_purchase_orders_purchase_order_id",
                table: "asset_receives");

            migrationBuilder.DropForeignKey(
                name: "fk_asset_receives_return_to_factories_return_to_factory_id",
                table: "asset_receives");

            migrationBuilder.DropForeignKey(
                name: "fk_asset_receives_users_receiver_id",
                table: "asset_receives");

            migrationBuilder.DropForeignKey(
                name: "fk_assets_asset_types_asset_type_id",
                table: "assets");

            migrationBuilder.DropForeignKey(
                name: "fk_assets_departments_department_id",
                table: "assets");

            migrationBuilder.DropForeignKey(
                name: "fk_assets_locations_location_id",
                table: "assets");

            migrationBuilder.DropForeignKey(
                name: "fk_assettypes_assettypes_parent_id",
                table: "assettypes");

            migrationBuilder.DropForeignKey(
                name: "fk_attachments_comments_comment_id",
                table: "attachments");

            migrationBuilder.DropForeignKey(
                name: "fk_attachments_tasks_task_id",
                table: "attachments");

            migrationBuilder.DropForeignKey(
                name: "fk_attachments_users_uploader_user_id",
                table: "attachments");

            migrationBuilder.DropForeignKey(
                name: "fk_audit_logs_users_user_id",
                table: "audit_logs");

            migrationBuilder.DropForeignKey(
                name: "fk_comments_comments_parent_comment_id",
                table: "comments");

            migrationBuilder.DropForeignKey(
                name: "fk_comments_tasks_task_id",
                table: "comments");

            migrationBuilder.DropForeignKey(
                name: "fk_comments_users_user_id",
                table: "comments");

            migrationBuilder.DropForeignKey(
                name: "fk_departments_departments_parent_id",
                table: "departments");

            migrationBuilder.DropForeignKey(
                name: "fk_fault_types_fault_types_parent_id",
                table: "fault_types");

            migrationBuilder.DropForeignKey(
                name: "fk_faultrecords_assets_asset_id",
                table: "faultrecords");

            migrationBuilder.DropForeignKey(
                name: "fk_faultrecords_fault_types_fault_type_id",
                table: "faultrecords");

            migrationBuilder.DropForeignKey(
                name: "fk_faultrecords_locations_location_id",
                table: "faultrecords");

            migrationBuilder.DropForeignKey(
                name: "fk_faultrecords_users_assignee_id",
                table: "faultrecords");

            migrationBuilder.DropForeignKey(
                name: "fk_faultrecords_users_reporter_id",
                table: "faultrecords");

            migrationBuilder.DropForeignKey(
                name: "fk_locationhistories_assets_asset_id",
                table: "locationhistories");

            migrationBuilder.DropForeignKey(
                name: "fk_locationhistories_locations_new_location_id",
                table: "locationhistories");

            migrationBuilder.DropForeignKey(
                name: "fk_locationhistories_locations_old_location_id",
                table: "locationhistories");

            migrationBuilder.DropForeignKey(
                name: "fk_locationhistories_users_operator_id",
                table: "locationhistories");

            migrationBuilder.DropForeignKey(
                name: "fk_locations_departments_default_department_id",
                table: "locations");

            migrationBuilder.DropForeignKey(
                name: "fk_locations_locations_parent_id",
                table: "locations");

            migrationBuilder.DropForeignKey(
                name: "fk_locations_users_default_responsible_person_id",
                table: "locations");

            migrationBuilder.DropForeignKey(
                name: "fk_locationusers_locations_location_id",
                table: "locationusers");

            migrationBuilder.DropForeignKey(
                name: "fk_locationusers_personnel_personnel_id",
                table: "locationusers");

            migrationBuilder.DropForeignKey(
                name: "fk_locationusers_users_user_id",
                table: "locationusers");

            migrationBuilder.DropForeignKey(
                name: "fk_maintenance_orders_assets_asset_id",
                table: "maintenance_orders");

            migrationBuilder.DropForeignKey(
                name: "fk_maintenance_orders_fault_records_fault_record_id",
                table: "maintenance_orders");

            migrationBuilder.DropForeignKey(
                name: "fk_maintenance_orders_locations_location_id",
                table: "maintenance_orders");

            migrationBuilder.DropForeignKey(
                name: "fk_maintenance_orders_users_assignee_id",
                table: "maintenance_orders");

            migrationBuilder.DropForeignKey(
                name: "fk_menus_menus_parent_id",
                table: "menus");

            migrationBuilder.DropForeignKey(
                name: "fk_pdca_plans_tasks_task_id",
                table: "pdca_plans");

            migrationBuilder.DropForeignKey(
                name: "fk_pdca_plans_users_creator_user_id",
                table: "pdca_plans");

            migrationBuilder.DropForeignKey(
                name: "fk_pdca_plans_users_responsible_person_id",
                table: "pdca_plans");

            migrationBuilder.DropForeignKey(
                name: "fk_periodic_task_schedules_tasks_template_task_task_id",
                table: "periodic_task_schedules");

            migrationBuilder.DropForeignKey(
                name: "fk_periodic_task_schedules_users_creator_user_id",
                table: "periodic_task_schedules");

            migrationBuilder.DropForeignKey(
                name: "fk_personnel_departments_department_id",
                table: "personnel");

            migrationBuilder.DropForeignKey(
                name: "fk_purchaseitems_assettypes_asset_type_id",
                table: "purchaseitems");

            migrationBuilder.DropForeignKey(
                name: "fk_purchaseitems_purchaseorders_purchase_order_id",
                table: "purchaseitems");

            migrationBuilder.DropForeignKey(
                name: "fk_purchaseorders_suppliers_supplier_id",
                table: "purchaseorders");

            migrationBuilder.DropForeignKey(
                name: "fk_purchaseorders_users_approver_id",
                table: "purchaseorders");

            migrationBuilder.DropForeignKey(
                name: "fk_purchaseorders_users_requester_id",
                table: "purchaseorders");

            migrationBuilder.DropForeignKey(
                name: "fk_quick_memo_categories_users_user_id",
                table: "quick_memo_categories");

            migrationBuilder.DropForeignKey(
                name: "fk_quick_memos_quick_memo_categories_category_id",
                table: "quick_memos");

            migrationBuilder.DropForeignKey(
                name: "fk_quick_memos_users_user_id",
                table: "quick_memos");

            migrationBuilder.DropForeignKey(
                name: "fk_refresh_tokens_users_user_id",
                table: "refresh_tokens");

            migrationBuilder.DropForeignKey(
                name: "fk_returntofactories_assets_asset_id",
                table: "returntofactories");

            migrationBuilder.DropForeignKey(
                name: "fk_returntofactories_suppliers_supplier_id",
                table: "returntofactories");

            migrationBuilder.DropForeignKey(
                name: "fk_rolemenus_menus_menu_id",
                table: "rolemenus");

            migrationBuilder.DropForeignKey(
                name: "fk_rolemenus_roles_role_id",
                table: "rolemenus");

            migrationBuilder.DropForeignKey(
                name: "fk_rolepermissions_permissions_permission_id",
                table: "rolepermissions");

            migrationBuilder.DropForeignKey(
                name: "fk_rolepermissions_roles_role_id",
                table: "rolepermissions");

            migrationBuilder.DropForeignKey(
                name: "fk_spare_part_transactions_spare_part_locations_location_id",
                table: "spare_part_transactions");

            migrationBuilder.DropForeignKey(
                name: "fk_spare_part_transactions_spare_parts_part_id",
                table: "spare_part_transactions");

            migrationBuilder.DropForeignKey(
                name: "fk_spare_part_types_spare_part_types_parent_id",
                table: "spare_part_types");

            migrationBuilder.DropForeignKey(
                name: "fk_spare_parts_spare_part_locations_location_id",
                table: "spare_parts");

            migrationBuilder.DropForeignKey(
                name: "fk_spare_parts_spare_part_types_type_id",
                table: "spare_parts");

            migrationBuilder.DropForeignKey(
                name: "fk_task_assignees_tasks_task_id",
                table: "task_assignees");

            migrationBuilder.DropForeignKey(
                name: "fk_task_assignees_users_assigned_by_user_id",
                table: "task_assignees");

            migrationBuilder.DropForeignKey(
                name: "fk_task_assignees_users_user_id",
                table: "task_assignees");

            migrationBuilder.DropForeignKey(
                name: "fk_task_histories_attachments_attachment_id",
                table: "task_histories");

            migrationBuilder.DropForeignKey(
                name: "fk_task_histories_comments_comment_id",
                table: "task_histories");

            migrationBuilder.DropForeignKey(
                name: "fk_task_histories_tasks_task_id",
                table: "task_histories");

            migrationBuilder.DropForeignKey(
                name: "fk_task_histories_users_user_id",
                table: "task_histories");

            migrationBuilder.DropForeignKey(
                name: "fk_tasks_assets_asset_id",
                table: "tasks");

            migrationBuilder.DropForeignKey(
                name: "fk_tasks_locations_location_id",
                table: "tasks");

            migrationBuilder.DropForeignKey(
                name: "fk_tasks_tasks_parent_task_id",
                table: "tasks");

            migrationBuilder.DropForeignKey(
                name: "fk_tasks_users_assignee_user_id",
                table: "tasks");

            migrationBuilder.DropForeignKey(
                name: "fk_tasks_users_creator_user_id",
                table: "tasks");

            migrationBuilder.DropForeignKey(
                name: "fk_userroles_roles_role_id",
                table: "userroles");

            migrationBuilder.DropForeignKey(
                name: "fk_userroles_users_user_id",
                table: "userroles");

            migrationBuilder.DropForeignKey(
                name: "fk_users_departments_department_id",
                table: "users");

            migrationBuilder.DropForeignKey(
                name: "fk_users_roles_default_role_id",
                table: "users");

            migrationBuilder.DropTable(
                name: "AssetSnapshots");

            migrationBuilder.DropTable(
                name: "notifications");

            migrationBuilder.DropTable(
                name: "periodic_task_schedule_assignees");

            migrationBuilder.DropTable(
                name: "RepairItems");

            migrationBuilder.DropTable(
                name: "RepairOrders");

            migrationBuilder.DropPrimaryKey(
                name: "pk_users",
                table: "users");

            migrationBuilder.DropPrimaryKey(
                name: "pk_userroles",
                table: "userroles");

            migrationBuilder.DropPrimaryKey(
                name: "pk_tasks",
                table: "tasks");

            migrationBuilder.DropPrimaryKey(
                name: "pk_suppliers",
                table: "suppliers");

            migrationBuilder.DropPrimaryKey(
                name: "pk_spare_parts",
                table: "spare_parts");

            migrationBuilder.DropPrimaryKey(
                name: "pk_spare_part_types",
                table: "spare_part_types");

            migrationBuilder.DropPrimaryKey(
                name: "pk_spare_part_transactions",
                table: "spare_part_transactions");

            migrationBuilder.DropPrimaryKey(
                name: "pk_spare_part_locations",
                table: "spare_part_locations");

            migrationBuilder.DropPrimaryKey(
                name: "pk_roles",
                table: "roles");

            migrationBuilder.DropPrimaryKey(
                name: "pk_rolepermissions",
                table: "rolepermissions");

            migrationBuilder.DropPrimaryKey(
                name: "pk_rolemenus",
                table: "rolemenus");

            migrationBuilder.DropPrimaryKey(
                name: "pk_returntofactories",
                table: "returntofactories");

            migrationBuilder.DropPrimaryKey(
                name: "pk_quick_memos",
                table: "quick_memos");

            migrationBuilder.DropPrimaryKey(
                name: "pk_quick_memo_categories",
                table: "quick_memo_categories");

            migrationBuilder.DropIndex(
                name: "ix_quick_memo_categories_user_id",
                table: "quick_memo_categories");

            migrationBuilder.DropPrimaryKey(
                name: "pk_purchaseorders",
                table: "purchaseorders");

            migrationBuilder.DropIndex(
                name: "ix_purchaseorders_approver_id",
                table: "purchaseorders");

            migrationBuilder.DropPrimaryKey(
                name: "pk_purchaseitems",
                table: "purchaseitems");

            migrationBuilder.DropPrimaryKey(
                name: "pk_personnel",
                table: "personnel");

            migrationBuilder.DropPrimaryKey(
                name: "pk_permissions",
                table: "permissions");

            migrationBuilder.DropPrimaryKey(
                name: "pk_menus",
                table: "menus");

            migrationBuilder.DropPrimaryKey(
                name: "pk_locationusers",
                table: "locationusers");

            migrationBuilder.DropPrimaryKey(
                name: "pk_locations",
                table: "locations");

            migrationBuilder.DropPrimaryKey(
                name: "pk_locationhistories",
                table: "locationhistories");

            migrationBuilder.DropPrimaryKey(
                name: "pk_faultrecords",
                table: "faultrecords");

            migrationBuilder.DropPrimaryKey(
                name: "pk_departments",
                table: "departments");

            migrationBuilder.DropPrimaryKey(
                name: "pk_comments",
                table: "comments");

            migrationBuilder.DropPrimaryKey(
                name: "pk_attachments",
                table: "attachments");

            migrationBuilder.DropPrimaryKey(
                name: "pk_assettypes",
                table: "assettypes");

            migrationBuilder.DropPrimaryKey(
                name: "pk_assets",
                table: "assets");

            migrationBuilder.DropPrimaryKey(
                name: "pk_task_histories",
                table: "task_histories");

            migrationBuilder.DropPrimaryKey(
                name: "pk_task_assignees",
                table: "task_assignees");

            migrationBuilder.DropPrimaryKey(
                name: "pk_refresh_tokens",
                table: "refresh_tokens");

            migrationBuilder.DropPrimaryKey(
                name: "pk_periodic_task_schedules",
                table: "periodic_task_schedules");

            migrationBuilder.DropIndex(
                name: "ix_periodic_task_schedules_template_task_task_id",
                table: "periodic_task_schedules");

            migrationBuilder.DropPrimaryKey(
                name: "pk_pdca_plans",
                table: "pdca_plans");

            migrationBuilder.DropIndex(
                name: "ix_pdca_plans_task_id",
                table: "pdca_plans");

            migrationBuilder.DropPrimaryKey(
                name: "pk_maintenance_orders",
                table: "maintenance_orders");

            migrationBuilder.DropPrimaryKey(
                name: "pk_fault_types",
                table: "fault_types");

            migrationBuilder.DropPrimaryKey(
                name: "pk_audit_logs",
                table: "audit_logs");

            migrationBuilder.DropPrimaryKey(
                name: "pk_asset_receives",
                table: "asset_receives");

            migrationBuilder.DropPrimaryKey(
                name: "pk_asset_histories",
                table: "asset_histories");

            migrationBuilder.DropColumn(
                name: "material_number",
                table: "spare_parts");

            migrationBuilder.DropColumn(
                name: "actual_return_time",
                table: "returntofactories");

            migrationBuilder.DropColumn(
                name: "code",
                table: "returntofactories");

            migrationBuilder.DropColumn(
                name: "fault_record_id",
                table: "returntofactories");

            migrationBuilder.DropColumn(
                name: "in_warranty",
                table: "returntofactories");

            migrationBuilder.DropColumn(
                name: "repair_cost",
                table: "returntofactories");

            migrationBuilder.DropColumn(
                name: "ApplicationTime",
                table: "purchaseorders");

            migrationBuilder.DropColumn(
                name: "ApprovalTime",
                table: "purchaseorders");

            migrationBuilder.DropColumn(
                name: "ApproverId",
                table: "purchaseorders");

            migrationBuilder.DropColumn(
                name: "Description",
                table: "purchaseorders");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "purchaseorders");

            migrationBuilder.DropColumn(
                name: "ItemCode",
                table: "purchaseitems");

            migrationBuilder.DropColumn(
                name: "template_task_task_id",
                table: "periodic_task_schedules");

            migrationBuilder.RenameTable(
                name: "suppliers",
                newName: "Suppliers");

            migrationBuilder.RenameTable(
                name: "returntofactories",
                newName: "ReturnToFactories");

            migrationBuilder.RenameTable(
                name: "purchaseorders",
                newName: "PurchaseOrders");

            migrationBuilder.RenameTable(
                name: "purchaseitems",
                newName: "PurchaseItems");

            migrationBuilder.RenameTable(
                name: "menus",
                newName: "Menus");

            migrationBuilder.RenameTable(
                name: "faultrecords",
                newName: "FaultRecords");

            migrationBuilder.RenameTable(
                name: "task_histories",
                newName: "taskhistory");

            migrationBuilder.RenameTable(
                name: "task_assignees",
                newName: "taskassignees");

            migrationBuilder.RenameTable(
                name: "refresh_tokens",
                newName: "RefreshTokens");

            migrationBuilder.RenameTable(
                name: "periodic_task_schedules",
                newName: "periodictaskschedules");

            migrationBuilder.RenameTable(
                name: "pdca_plans",
                newName: "pdcaplans");

            migrationBuilder.RenameTable(
                name: "maintenance_orders",
                newName: "MaintenanceOrders");

            migrationBuilder.RenameTable(
                name: "fault_types",
                newName: "FaultTypes");

            migrationBuilder.RenameTable(
                name: "audit_logs",
                newName: "AuditLogs");

            migrationBuilder.RenameTable(
                name: "asset_receives",
                newName: "AssetReceives");

            migrationBuilder.RenameTable(
                name: "asset_histories",
                newName: "AssetHistories");

            migrationBuilder.RenameIndex(
                name: "ix_users_username",
                table: "users",
                newName: "IX_users_Username");

            migrationBuilder.RenameIndex(
                name: "ix_users_email",
                table: "users",
                newName: "IX_users_Email");

            migrationBuilder.RenameIndex(
                name: "ix_users_department_id",
                table: "users",
                newName: "IX_users_DepartmentId");

            migrationBuilder.RenameIndex(
                name: "ix_users_default_role_id",
                table: "users",
                newName: "IX_users_DefaultRoleId");

            migrationBuilder.RenameColumn(
                name: "role_id",
                table: "userroles",
                newName: "RoleId");

            migrationBuilder.RenameColumn(
                name: "user_id",
                table: "userroles",
                newName: "UserId");

            migrationBuilder.RenameIndex(
                name: "ix_userroles_role_id",
                table: "userroles",
                newName: "IX_userroles_RoleId");

            migrationBuilder.RenameColumn(
                name: "status",
                table: "tasks",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "progress",
                table: "tasks",
                newName: "Progress");

            migrationBuilder.RenameColumn(
                name: "priority",
                table: "tasks",
                newName: "Priority");

            migrationBuilder.RenameColumn(
                name: "points",
                table: "tasks",
                newName: "Points");

            migrationBuilder.RenameColumn(
                name: "name",
                table: "tasks",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "description",
                table: "tasks",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "task_type",
                table: "tasks",
                newName: "TaskType");

            migrationBuilder.RenameColumn(
                name: "project_id",
                table: "tasks",
                newName: "ProjectId");

            migrationBuilder.RenameColumn(
                name: "previous_instance_task_id",
                table: "tasks",
                newName: "PreviousInstanceTaskId");

            migrationBuilder.RenameColumn(
                name: "plan_start_date",
                table: "tasks",
                newName: "PlanStartDate");

            migrationBuilder.RenameColumn(
                name: "plan_end_date",
                table: "tasks",
                newName: "PlanEndDate");

            migrationBuilder.RenameColumn(
                name: "periodic_task_schedule_id",
                table: "tasks",
                newName: "PeriodicTaskScheduleId");

            migrationBuilder.RenameColumn(
                name: "pdca_stage",
                table: "tasks",
                newName: "PDCAStage");

            migrationBuilder.RenameColumn(
                name: "parent_task_id",
                table: "tasks",
                newName: "ParentTaskId");

            migrationBuilder.RenameColumn(
                name: "location_id",
                table: "tasks",
                newName: "LocationId");

            migrationBuilder.RenameColumn(
                name: "last_updated_timestamp",
                table: "tasks",
                newName: "LastUpdatedTimestamp");

            migrationBuilder.RenameColumn(
                name: "is_overdue_acknowledged",
                table: "tasks",
                newName: "IsOverdueAcknowledged");

            migrationBuilder.RenameColumn(
                name: "is_deleted",
                table: "tasks",
                newName: "IsDeleted");

            migrationBuilder.RenameColumn(
                name: "creator_user_id",
                table: "tasks",
                newName: "CreatorUserId");

            migrationBuilder.RenameColumn(
                name: "creation_timestamp",
                table: "tasks",
                newName: "CreationTimestamp");

            migrationBuilder.RenameColumn(
                name: "assignee_user_id",
                table: "tasks",
                newName: "AssigneeUserId");

            migrationBuilder.RenameColumn(
                name: "asset_id",
                table: "tasks",
                newName: "AssetId");

            migrationBuilder.RenameColumn(
                name: "actual_start_date",
                table: "tasks",
                newName: "ActualStartDate");

            migrationBuilder.RenameColumn(
                name: "actual_end_date",
                table: "tasks",
                newName: "ActualEndDate");

            migrationBuilder.RenameColumn(
                name: "task_id",
                table: "tasks",
                newName: "TaskId");

            migrationBuilder.RenameIndex(
                name: "ix_tasks_parent_task_id",
                table: "tasks",
                newName: "IX_tasks_ParentTaskId");

            migrationBuilder.RenameIndex(
                name: "ix_tasks_location_id",
                table: "tasks",
                newName: "IX_tasks_LocationId");

            migrationBuilder.RenameIndex(
                name: "ix_tasks_creator_user_id",
                table: "tasks",
                newName: "IX_tasks_CreatorUserId");

            migrationBuilder.RenameIndex(
                name: "ix_tasks_assignee_user_id",
                table: "tasks",
                newName: "IX_tasks_AssigneeUserId");

            migrationBuilder.RenameIndex(
                name: "ix_tasks_asset_id",
                table: "tasks",
                newName: "IX_tasks_AssetId");

            migrationBuilder.RenameColumn(
                name: "notes",
                table: "Suppliers",
                newName: "Notes");

            migrationBuilder.RenameColumn(
                name: "name",
                table: "Suppliers",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "code",
                table: "Suppliers",
                newName: "Code");

            migrationBuilder.RenameColumn(
                name: "address",
                table: "Suppliers",
                newName: "Address");

            migrationBuilder.RenameColumn(
                name: "updated_at",
                table: "Suppliers",
                newName: "UpdatedAt");

            migrationBuilder.RenameColumn(
                name: "is_active",
                table: "Suppliers",
                newName: "IsActive");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "Suppliers",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "contact_phone",
                table: "Suppliers",
                newName: "ContactPhone");

            migrationBuilder.RenameColumn(
                name: "contact_person",
                table: "Suppliers",
                newName: "ContactPerson");

            migrationBuilder.RenameColumn(
                name: "contact_email",
                table: "Suppliers",
                newName: "ContactEmail");

            migrationBuilder.RenameIndex(
                name: "ix_spare_parts_type_id",
                table: "spare_parts",
                newName: "IX_spare_parts_type_id");

            migrationBuilder.RenameIndex(
                name: "ix_spare_parts_location_id",
                table: "spare_parts",
                newName: "IX_spare_parts_location_id");

            migrationBuilder.RenameIndex(
                name: "ix_spare_part_types_parent_id",
                table: "spare_part_types",
                newName: "IX_spare_part_types_parent_id");

            migrationBuilder.RenameIndex(
                name: "ix_spare_part_transactions_part_id",
                table: "spare_part_transactions",
                newName: "IX_spare_part_transactions_part_id");

            migrationBuilder.RenameIndex(
                name: "ix_spare_part_transactions_location_id",
                table: "spare_part_transactions",
                newName: "IX_spare_part_transactions_location_id");

            migrationBuilder.RenameColumn(
                name: "name",
                table: "roles",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "description",
                table: "roles",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "code",
                table: "roles",
                newName: "Code");

            migrationBuilder.RenameColumn(
                name: "updated_at",
                table: "roles",
                newName: "UpdatedAt");

            migrationBuilder.RenameColumn(
                name: "is_active",
                table: "roles",
                newName: "IsActive");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "roles",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "permission_id",
                table: "rolepermissions",
                newName: "PermissionId");

            migrationBuilder.RenameColumn(
                name: "role_id",
                table: "rolepermissions",
                newName: "RoleId");

            migrationBuilder.RenameIndex(
                name: "ix_rolepermissions_permission_id",
                table: "rolepermissions",
                newName: "IX_rolepermissions_PermissionId");

            migrationBuilder.RenameColumn(
                name: "updated_at",
                table: "rolemenus",
                newName: "UpdatedAt");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "rolemenus",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "menu_id",
                table: "rolemenus",
                newName: "MenuId");

            migrationBuilder.RenameColumn(
                name: "role_id",
                table: "rolemenus",
                newName: "RoleId");

            migrationBuilder.RenameIndex(
                name: "ix_rolemenus_menu_id",
                table: "rolemenus",
                newName: "IX_rolemenus_MenuId");

            migrationBuilder.RenameColumn(
                name: "status",
                table: "ReturnToFactories",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "updated_at",
                table: "ReturnToFactories",
                newName: "UpdatedAt");

            migrationBuilder.RenameColumn(
                name: "supplier_id",
                table: "ReturnToFactories",
                newName: "SupplierId");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "ReturnToFactories",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "asset_id",
                table: "ReturnToFactories",
                newName: "AssetId");

            migrationBuilder.RenameColumn(
                name: "sender_id",
                table: "ReturnToFactories",
                newName: "OperatorId");

            migrationBuilder.RenameColumn(
                name: "send_time",
                table: "ReturnToFactories",
                newName: "ExpectedReturnDate");

            migrationBuilder.RenameColumn(
                name: "repair_result",
                table: "ReturnToFactories",
                newName: "Result");

            migrationBuilder.RenameColumn(
                name: "notes",
                table: "ReturnToFactories",
                newName: "Remarks");

            migrationBuilder.RenameColumn(
                name: "estimated_return_time",
                table: "ReturnToFactories",
                newName: "ActualReturnDate");

            migrationBuilder.RenameIndex(
                name: "ix_returntofactories_supplier_id",
                table: "ReturnToFactories",
                newName: "IX_ReturnToFactories_SupplierId");

            migrationBuilder.RenameIndex(
                name: "ix_returntofactories_asset_id",
                table: "ReturnToFactories",
                newName: "IX_ReturnToFactories_AssetId");

            migrationBuilder.RenameIndex(
                name: "ix_quick_memos_category_id",
                table: "quick_memos",
                newName: "IX_quick_memos_category_id");

            migrationBuilder.RenameColumn(
                name: "OrderCode",
                table: "PurchaseOrders",
                newName: "OrderNumber");

            migrationBuilder.RenameColumn(
                name: "EstimatedDeliveryDate",
                table: "PurchaseOrders",
                newName: "ExpectedDeliveryDate");

            migrationBuilder.RenameColumn(
                name: "ApplicantId",
                table: "PurchaseOrders",
                newName: "RequesterId");

            migrationBuilder.RenameIndex(
                name: "ix_purchaseorders_supplier_id",
                table: "PurchaseOrders",
                newName: "IX_PurchaseOrders_SupplierId");

            migrationBuilder.RenameIndex(
                name: "ix_purchaseorders_requester_id",
                table: "PurchaseOrders",
                newName: "IX_PurchaseOrders_RequesterId");

            migrationBuilder.RenameColumn(
                name: "ItemName",
                table: "PurchaseItems",
                newName: "Name");

            migrationBuilder.RenameIndex(
                name: "ix_purchaseitems_purchase_order_id",
                table: "PurchaseItems",
                newName: "IX_PurchaseItems_PurchaseOrderId");

            migrationBuilder.RenameIndex(
                name: "ix_purchaseitems_asset_type_id",
                table: "PurchaseItems",
                newName: "IX_PurchaseItems_AssetTypeId");

            migrationBuilder.RenameIndex(
                name: "ix_personnel_department_id",
                table: "personnel",
                newName: "IX_personnel_department_id");

            migrationBuilder.RenameColumn(
                name: "type",
                table: "permissions",
                newName: "Type");

            migrationBuilder.RenameColumn(
                name: "name",
                table: "permissions",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "description",
                table: "permissions",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "code",
                table: "permissions",
                newName: "Code");

            migrationBuilder.RenameColumn(
                name: "is_system",
                table: "permissions",
                newName: "IsSystem");

            migrationBuilder.RenameIndex(
                name: "ix_permissions_code",
                table: "permissions",
                newName: "IX_permissions_Code");

            migrationBuilder.RenameColumn(
                name: "path",
                table: "Menus",
                newName: "Path");

            migrationBuilder.RenameColumn(
                name: "name",
                table: "Menus",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "icon",
                table: "Menus",
                newName: "Icon");

            migrationBuilder.RenameColumn(
                name: "component",
                table: "Menus",
                newName: "Component");

            migrationBuilder.RenameColumn(
                name: "code",
                table: "Menus",
                newName: "Code");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "Menus",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "updated_at",
                table: "Menus",
                newName: "UpdatedAt");

            migrationBuilder.RenameColumn(
                name: "sort_order",
                table: "Menus",
                newName: "SortOrder");

            migrationBuilder.RenameColumn(
                name: "parent_id",
                table: "Menus",
                newName: "ParentId");

            migrationBuilder.RenameColumn(
                name: "is_visible",
                table: "Menus",
                newName: "IsVisible");

            migrationBuilder.RenameColumn(
                name: "is_active",
                table: "Menus",
                newName: "IsActive");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "Menus",
                newName: "CreatedAt");

            migrationBuilder.RenameIndex(
                name: "ix_menus_parent_id",
                table: "Menus",
                newName: "IX_Menus_ParentId");

            migrationBuilder.RenameColumn(
                name: "user_id",
                table: "locationusers",
                newName: "UserId");

            migrationBuilder.RenameIndex(
                name: "ix_locationusers_personnel_id",
                table: "locationusers",
                newName: "IX_locationusers_personnel_id");

            migrationBuilder.RenameIndex(
                name: "ix_locationusers_user_id",
                table: "locationusers",
                newName: "IX_locationusers_UserId");

            migrationBuilder.RenameColumn(
                name: "type",
                table: "locations",
                newName: "Type");

            migrationBuilder.RenameColumn(
                name: "path",
                table: "locations",
                newName: "Path");

            migrationBuilder.RenameColumn(
                name: "name",
                table: "locations",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "description",
                table: "locations",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "code",
                table: "locations",
                newName: "Code");

            migrationBuilder.RenameColumn(
                name: "updated_at",
                table: "locations",
                newName: "UpdatedAt");

            migrationBuilder.RenameColumn(
                name: "parent_id",
                table: "locations",
                newName: "ParentId");

            migrationBuilder.RenameColumn(
                name: "is_active",
                table: "locations",
                newName: "IsActive");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "locations",
                newName: "CreatedAt");

            migrationBuilder.RenameIndex(
                name: "ix_locations_parent_id",
                table: "locations",
                newName: "IX_locations_ParentId");

            migrationBuilder.RenameIndex(
                name: "ix_locations_default_responsible_person_id",
                table: "locations",
                newName: "IX_locations_DefaultResponsiblePersonId");

            migrationBuilder.RenameIndex(
                name: "ix_locations_default_department_id",
                table: "locations",
                newName: "IX_locations_DefaultDepartmentId");

            migrationBuilder.RenameIndex(
                name: "ix_locationhistories_operator_id",
                table: "locationhistories",
                newName: "IX_locationhistories_OperatorId");

            migrationBuilder.RenameIndex(
                name: "ix_locationhistories_old_location_id",
                table: "locationhistories",
                newName: "IX_locationhistories_OldLocationId");

            migrationBuilder.RenameIndex(
                name: "ix_locationhistories_new_location_id",
                table: "locationhistories",
                newName: "IX_locationhistories_NewLocationId");

            migrationBuilder.RenameIndex(
                name: "ix_locationhistories_asset_id",
                table: "locationhistories",
                newName: "IX_locationhistories_AssetId");

            migrationBuilder.RenameIndex(
                name: "ix_faultrecords_reporter_id",
                table: "FaultRecords",
                newName: "IX_FaultRecords_ReporterId");

            migrationBuilder.RenameIndex(
                name: "ix_faultrecords_location_id",
                table: "FaultRecords",
                newName: "IX_FaultRecords_LocationId");

            migrationBuilder.RenameIndex(
                name: "ix_faultrecords_fault_type_id",
                table: "FaultRecords",
                newName: "IX_FaultRecords_FaultTypeId");

            migrationBuilder.RenameIndex(
                name: "ix_faultrecords_assignee_id",
                table: "FaultRecords",
                newName: "IX_FaultRecords_AssigneeId");

            migrationBuilder.RenameIndex(
                name: "ix_faultrecords_asset_id",
                table: "FaultRecords",
                newName: "IX_FaultRecords_AssetId");

            migrationBuilder.RenameColumn(
                name: "path",
                table: "departments",
                newName: "Path");

            migrationBuilder.RenameColumn(
                name: "name",
                table: "departments",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "description",
                table: "departments",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "code",
                table: "departments",
                newName: "Code");

            migrationBuilder.RenameColumn(
                name: "updated_at",
                table: "departments",
                newName: "UpdatedAt");

            migrationBuilder.RenameColumn(
                name: "parent_id",
                table: "departments",
                newName: "ParentId");

            migrationBuilder.RenameColumn(
                name: "manager_id",
                table: "departments",
                newName: "ManagerId");

            migrationBuilder.RenameColumn(
                name: "is_active",
                table: "departments",
                newName: "IsActive");

            migrationBuilder.RenameColumn(
                name: "deputy_manager_id",
                table: "departments",
                newName: "DeputyManagerId");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "departments",
                newName: "CreatedAt");

            migrationBuilder.RenameIndex(
                name: "ix_departments_parent_id",
                table: "departments",
                newName: "IX_departments_ParentId");

            migrationBuilder.RenameColumn(
                name: "content",
                table: "comments",
                newName: "Content");

            migrationBuilder.RenameColumn(
                name: "user_id",
                table: "comments",
                newName: "UserId");

            migrationBuilder.RenameColumn(
                name: "task_id",
                table: "comments",
                newName: "TaskId");

            migrationBuilder.RenameColumn(
                name: "parent_comment_id",
                table: "comments",
                newName: "ParentCommentId");

            migrationBuilder.RenameColumn(
                name: "mentioned_user_ids",
                table: "comments",
                newName: "MentionedUserIds");

            migrationBuilder.RenameColumn(
                name: "last_updated_timestamp",
                table: "comments",
                newName: "LastUpdatedTimestamp");

            migrationBuilder.RenameColumn(
                name: "is_pinned",
                table: "comments",
                newName: "IsPinned");

            migrationBuilder.RenameColumn(
                name: "is_edited",
                table: "comments",
                newName: "IsEdited");

            migrationBuilder.RenameColumn(
                name: "creation_timestamp",
                table: "comments",
                newName: "CreationTimestamp");

            migrationBuilder.RenameColumn(
                name: "comment_id",
                table: "comments",
                newName: "CommentId");

            migrationBuilder.RenameIndex(
                name: "ix_comments_user_id",
                table: "comments",
                newName: "IX_comments_UserId");

            migrationBuilder.RenameIndex(
                name: "ix_comments_task_id",
                table: "comments",
                newName: "IX_comments_TaskId");

            migrationBuilder.RenameIndex(
                name: "ix_comments_parent_comment_id",
                table: "comments",
                newName: "IX_comments_ParentCommentId");

            migrationBuilder.RenameColumn(
                name: "uploader_user_id",
                table: "attachments",
                newName: "UploaderUserId");

            migrationBuilder.RenameColumn(
                name: "task_id",
                table: "attachments",
                newName: "TaskId");

            migrationBuilder.RenameColumn(
                name: "stored_file_name",
                table: "attachments",
                newName: "StoredFileName");

            migrationBuilder.RenameColumn(
                name: "storage_type",
                table: "attachments",
                newName: "StorageType");

            migrationBuilder.RenameColumn(
                name: "is_previewable",
                table: "attachments",
                newName: "IsPreviewable");

            migrationBuilder.RenameColumn(
                name: "file_type",
                table: "attachments",
                newName: "FileType");

            migrationBuilder.RenameColumn(
                name: "file_size",
                table: "attachments",
                newName: "FileSize");

            migrationBuilder.RenameColumn(
                name: "file_path",
                table: "attachments",
                newName: "FilePath");

            migrationBuilder.RenameColumn(
                name: "file_name",
                table: "attachments",
                newName: "FileName");

            migrationBuilder.RenameColumn(
                name: "creation_timestamp",
                table: "attachments",
                newName: "CreationTimestamp");

            migrationBuilder.RenameColumn(
                name: "comment_id",
                table: "attachments",
                newName: "CommentId");

            migrationBuilder.RenameColumn(
                name: "attachment_id",
                table: "attachments",
                newName: "AttachmentId");

            migrationBuilder.RenameIndex(
                name: "ix_attachments_uploader_user_id",
                table: "attachments",
                newName: "IX_attachments_UploaderUserId");

            migrationBuilder.RenameIndex(
                name: "ix_attachments_task_id",
                table: "attachments",
                newName: "IX_attachments_TaskId");

            migrationBuilder.RenameIndex(
                name: "ix_attachments_comment_id",
                table: "attachments",
                newName: "IX_attachments_CommentId");

            migrationBuilder.RenameColumn(
                name: "name",
                table: "assettypes",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "description",
                table: "assettypes",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "code",
                table: "assettypes",
                newName: "Code");

            migrationBuilder.RenameColumn(
                name: "updated_at",
                table: "assettypes",
                newName: "UpdatedAt");

            migrationBuilder.RenameColumn(
                name: "parent_id",
                table: "assettypes",
                newName: "ParentId");

            migrationBuilder.RenameColumn(
                name: "is_active",
                table: "assettypes",
                newName: "IsActive");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "assettypes",
                newName: "CreatedAt");

            migrationBuilder.RenameIndex(
                name: "ix_assettypes_parent_id",
                table: "assettypes",
                newName: "IX_assettypes_ParentId");

            migrationBuilder.RenameColumn(
                name: "status",
                table: "assets",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "price",
                table: "assets",
                newName: "Price");

            migrationBuilder.RenameColumn(
                name: "notes",
                table: "assets",
                newName: "Notes");

            migrationBuilder.RenameColumn(
                name: "name",
                table: "assets",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "model",
                table: "assets",
                newName: "Model");

            migrationBuilder.RenameColumn(
                name: "brand",
                table: "assets",
                newName: "Brand");

            migrationBuilder.RenameColumn(
                name: "warranty_expire_date",
                table: "assets",
                newName: "WarrantyExpireDate");

            migrationBuilder.RenameColumn(
                name: "updated_at",
                table: "assets",
                newName: "UpdatedAt");

            migrationBuilder.RenameColumn(
                name: "serial_number",
                table: "assets",
                newName: "SerialNumber");

            migrationBuilder.RenameColumn(
                name: "purchase_date",
                table: "assets",
                newName: "PurchaseDate");

            migrationBuilder.RenameColumn(
                name: "location_id",
                table: "assets",
                newName: "LocationId");

            migrationBuilder.RenameColumn(
                name: "financial_code",
                table: "assets",
                newName: "FinancialCode");

            migrationBuilder.RenameColumn(
                name: "department_id",
                table: "assets",
                newName: "DepartmentId");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "assets",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "asset_type_id",
                table: "assets",
                newName: "AssetTypeId");

            migrationBuilder.RenameColumn(
                name: "asset_code",
                table: "assets",
                newName: "AssetCode");

            migrationBuilder.RenameIndex(
                name: "ix_assets_location_id",
                table: "assets",
                newName: "IX_assets_LocationId");

            migrationBuilder.RenameIndex(
                name: "ix_assets_department_id",
                table: "assets",
                newName: "IX_assets_DepartmentId");

            migrationBuilder.RenameIndex(
                name: "ix_assets_asset_type_id",
                table: "assets",
                newName: "IX_assets_AssetTypeId");

            migrationBuilder.RenameColumn(
                name: "timestamp",
                table: "taskhistory",
                newName: "Timestamp");

            migrationBuilder.RenameColumn(
                name: "description",
                table: "taskhistory",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "user_id",
                table: "taskhistory",
                newName: "UserId");

            migrationBuilder.RenameColumn(
                name: "task_id",
                table: "taskhistory",
                newName: "TaskId");

            migrationBuilder.RenameColumn(
                name: "old_value",
                table: "taskhistory",
                newName: "OldValue");

            migrationBuilder.RenameColumn(
                name: "new_value",
                table: "taskhistory",
                newName: "NewValue");

            migrationBuilder.RenameColumn(
                name: "field_name",
                table: "taskhistory",
                newName: "FieldName");

            migrationBuilder.RenameColumn(
                name: "comment_id",
                table: "taskhistory",
                newName: "CommentId");

            migrationBuilder.RenameColumn(
                name: "attachment_id",
                table: "taskhistory",
                newName: "AttachmentId");

            migrationBuilder.RenameColumn(
                name: "action_type",
                table: "taskhistory",
                newName: "ActionType");

            migrationBuilder.RenameColumn(
                name: "task_history_id",
                table: "taskhistory",
                newName: "TaskHistoryId");

            migrationBuilder.RenameIndex(
                name: "ix_task_histories_user_id",
                table: "taskhistory",
                newName: "IX_taskhistory_UserId");

            migrationBuilder.RenameIndex(
                name: "ix_task_histories_task_id",
                table: "taskhistory",
                newName: "IX_taskhistory_TaskId");

            migrationBuilder.RenameIndex(
                name: "ix_task_histories_comment_id",
                table: "taskhistory",
                newName: "IX_taskhistory_CommentId");

            migrationBuilder.RenameIndex(
                name: "ix_task_histories_attachment_id",
                table: "taskhistory",
                newName: "IX_taskhistory_AttachmentId");

            migrationBuilder.RenameColumn(
                name: "user_id",
                table: "taskassignees",
                newName: "UserId");

            migrationBuilder.RenameColumn(
                name: "task_id",
                table: "taskassignees",
                newName: "TaskId");

            migrationBuilder.RenameColumn(
                name: "assignment_type",
                table: "taskassignees",
                newName: "AssignmentType");

            migrationBuilder.RenameColumn(
                name: "assignment_timestamp",
                table: "taskassignees",
                newName: "AssignmentTimestamp");

            migrationBuilder.RenameColumn(
                name: "assigned_by_user_id",
                table: "taskassignees",
                newName: "AssignedByUserId");

            migrationBuilder.RenameColumn(
                name: "task_assignee_id",
                table: "taskassignees",
                newName: "TaskAssigneeId");

            migrationBuilder.RenameIndex(
                name: "ix_task_assignees_user_id",
                table: "taskassignees",
                newName: "IX_taskassignees_UserId");

            migrationBuilder.RenameIndex(
                name: "ix_task_assignees_task_id",
                table: "taskassignees",
                newName: "IX_taskassignees_TaskId");

            migrationBuilder.RenameIndex(
                name: "ix_task_assignees_assigned_by_user_id",
                table: "taskassignees",
                newName: "IX_taskassignees_AssignedByUserId");

            migrationBuilder.RenameColumn(
                name: "token",
                table: "RefreshTokens",
                newName: "Token");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "RefreshTokens",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "user_id",
                table: "RefreshTokens",
                newName: "UserId");

            migrationBuilder.RenameColumn(
                name: "jwt_id",
                table: "RefreshTokens",
                newName: "JwtId");

            migrationBuilder.RenameColumn(
                name: "is_used",
                table: "RefreshTokens",
                newName: "IsUsed");

            migrationBuilder.RenameColumn(
                name: "is_revoked",
                table: "RefreshTokens",
                newName: "IsRevoked");

            migrationBuilder.RenameColumn(
                name: "expiry_date",
                table: "RefreshTokens",
                newName: "ExpiryDate");

            migrationBuilder.RenameColumn(
                name: "added_date",
                table: "RefreshTokens",
                newName: "AddedDate");

            migrationBuilder.RenameIndex(
                name: "ix_refresh_tokens_user_id",
                table: "RefreshTokens",
                newName: "IX_RefreshTokens_UserId");

            migrationBuilder.RenameIndex(
                name: "ix_periodic_task_schedules_creator_user_id",
                table: "periodictaskschedules",
                newName: "IX_periodictaskschedules_creator_user_id");

            migrationBuilder.RenameColumn(
                name: "task_id",
                table: "pdcaplans",
                newName: "TaskId");

            migrationBuilder.RenameIndex(
                name: "ix_pdca_plans_responsible_person_id",
                table: "pdcaplans",
                newName: "IX_pdcaplans_responsible_person_id");

            migrationBuilder.RenameIndex(
                name: "ix_pdca_plans_creator_user_id",
                table: "pdcaplans",
                newName: "IX_pdcaplans_creator_user_id");

            migrationBuilder.RenameColumn(
                name: "status",
                table: "MaintenanceOrders",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "result",
                table: "MaintenanceOrders",
                newName: "Result");

            migrationBuilder.RenameColumn(
                name: "remarks",
                table: "MaintenanceOrders",
                newName: "Remarks");

            migrationBuilder.RenameColumn(
                name: "description",
                table: "MaintenanceOrders",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "MaintenanceOrders",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "updated_by",
                table: "MaintenanceOrders",
                newName: "UpdatedBy");

            migrationBuilder.RenameColumn(
                name: "updated_at",
                table: "MaintenanceOrders",
                newName: "UpdatedAt");

            migrationBuilder.RenameColumn(
                name: "planned_completion_date",
                table: "MaintenanceOrders",
                newName: "PlannedCompletionDate");

            migrationBuilder.RenameColumn(
                name: "order_number",
                table: "MaintenanceOrders",
                newName: "OrderNumber");

            migrationBuilder.RenameColumn(
                name: "maintenance_type",
                table: "MaintenanceOrders",
                newName: "MaintenanceType");

            migrationBuilder.RenameColumn(
                name: "maintenance_date",
                table: "MaintenanceOrders",
                newName: "MaintenanceDate");

            migrationBuilder.RenameColumn(
                name: "maintenance_cost",
                table: "MaintenanceOrders",
                newName: "MaintenanceCost");

            migrationBuilder.RenameColumn(
                name: "location_id",
                table: "MaintenanceOrders",
                newName: "LocationId");

            migrationBuilder.RenameColumn(
                name: "fault_record_id",
                table: "MaintenanceOrders",
                newName: "FaultRecordId");

            migrationBuilder.RenameColumn(
                name: "created_by",
                table: "MaintenanceOrders",
                newName: "CreatedBy");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "MaintenanceOrders",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "assignee_id",
                table: "MaintenanceOrders",
                newName: "AssigneeId");

            migrationBuilder.RenameColumn(
                name: "asset_id",
                table: "MaintenanceOrders",
                newName: "AssetId");

            migrationBuilder.RenameColumn(
                name: "actual_completion_date",
                table: "MaintenanceOrders",
                newName: "ActualCompletionDate");

            migrationBuilder.RenameIndex(
                name: "ix_maintenance_orders_location_id",
                table: "MaintenanceOrders",
                newName: "IX_MaintenanceOrders_LocationId");

            migrationBuilder.RenameIndex(
                name: "ix_maintenance_orders_fault_record_id",
                table: "MaintenanceOrders",
                newName: "IX_MaintenanceOrders_FaultRecordId");

            migrationBuilder.RenameIndex(
                name: "ix_maintenance_orders_assignee_id",
                table: "MaintenanceOrders",
                newName: "IX_MaintenanceOrders_AssigneeId");

            migrationBuilder.RenameIndex(
                name: "ix_maintenance_orders_asset_id",
                table: "MaintenanceOrders",
                newName: "IX_MaintenanceOrders_AssetId");

            migrationBuilder.RenameColumn(
                name: "name",
                table: "FaultTypes",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "description",
                table: "FaultTypes",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "code",
                table: "FaultTypes",
                newName: "Code");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "FaultTypes",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "updated_at",
                table: "FaultTypes",
                newName: "UpdatedAt");

            migrationBuilder.RenameColumn(
                name: "sort_order",
                table: "FaultTypes",
                newName: "SortOrder");

            migrationBuilder.RenameColumn(
                name: "parent_id",
                table: "FaultTypes",
                newName: "ParentId");

            migrationBuilder.RenameColumn(
                name: "is_active",
                table: "FaultTypes",
                newName: "IsActive");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "FaultTypes",
                newName: "CreatedAt");

            migrationBuilder.RenameIndex(
                name: "ix_fault_types_parent_id",
                table: "FaultTypes",
                newName: "IX_FaultTypes_ParentId");

            migrationBuilder.RenameColumn(
                name: "username",
                table: "AuditLogs",
                newName: "Username");

            migrationBuilder.RenameColumn(
                name: "target",
                table: "AuditLogs",
                newName: "Target");

            migrationBuilder.RenameColumn(
                name: "result",
                table: "AuditLogs",
                newName: "Result");

            migrationBuilder.RenameColumn(
                name: "module",
                table: "AuditLogs",
                newName: "Module");

            migrationBuilder.RenameColumn(
                name: "function",
                table: "AuditLogs",
                newName: "Function");

            migrationBuilder.RenameColumn(
                name: "content",
                table: "AuditLogs",
                newName: "Content");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "AuditLogs",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "user_id",
                table: "AuditLogs",
                newName: "UserId");

            migrationBuilder.RenameColumn(
                name: "updated_at",
                table: "AuditLogs",
                newName: "UpdatedAt");

            migrationBuilder.RenameColumn(
                name: "target_id",
                table: "AuditLogs",
                newName: "TargetId");

            migrationBuilder.RenameColumn(
                name: "ip_address",
                table: "AuditLogs",
                newName: "IPAddress");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "AuditLogs",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "action_type",
                table: "AuditLogs",
                newName: "ActionType");

            migrationBuilder.RenameIndex(
                name: "ix_audit_logs_user_id",
                table: "AuditLogs",
                newName: "IX_AuditLogs_UserId");

            migrationBuilder.RenameColumn(
                name: "status",
                table: "AssetReceives",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "remarks",
                table: "AssetReceives",
                newName: "Remarks");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "AssetReceives",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "updated_by",
                table: "AssetReceives",
                newName: "UpdatedBy");

            migrationBuilder.RenameColumn(
                name: "updated_at",
                table: "AssetReceives",
                newName: "UpdatedAt");

            migrationBuilder.RenameColumn(
                name: "return_to_factory_id",
                table: "AssetReceives",
                newName: "ReturnToFactoryId");

            migrationBuilder.RenameColumn(
                name: "receiver_id",
                table: "AssetReceives",
                newName: "ReceiverId");

            migrationBuilder.RenameColumn(
                name: "receive_type",
                table: "AssetReceives",
                newName: "ReceiveType");

            migrationBuilder.RenameColumn(
                name: "receive_date",
                table: "AssetReceives",
                newName: "ReceiveDate");

            migrationBuilder.RenameColumn(
                name: "purchase_order_id",
                table: "AssetReceives",
                newName: "PurchaseOrderId");

            migrationBuilder.RenameColumn(
                name: "location_id",
                table: "AssetReceives",
                newName: "LocationId");

            migrationBuilder.RenameColumn(
                name: "inspection_result",
                table: "AssetReceives",
                newName: "InspectionResult");

            migrationBuilder.RenameColumn(
                name: "created_by",
                table: "AssetReceives",
                newName: "CreatedBy");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "AssetReceives",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "asset_id",
                table: "AssetReceives",
                newName: "AssetId");

            migrationBuilder.RenameIndex(
                name: "ix_asset_receives_return_to_factory_id",
                table: "AssetReceives",
                newName: "IX_AssetReceives_ReturnToFactoryId");

            migrationBuilder.RenameIndex(
                name: "ix_asset_receives_receiver_id",
                table: "AssetReceives",
                newName: "IX_AssetReceives_ReceiverId");

            migrationBuilder.RenameIndex(
                name: "ix_asset_receives_purchase_order_id",
                table: "AssetReceives",
                newName: "IX_AssetReceives_PurchaseOrderId");

            migrationBuilder.RenameIndex(
                name: "ix_asset_receives_location_id",
                table: "AssetReceives",
                newName: "IX_AssetReceives_LocationId");

            migrationBuilder.RenameIndex(
                name: "ix_asset_receives_asset_id",
                table: "AssetReceives",
                newName: "IX_AssetReceives_AssetId");

            migrationBuilder.RenameColumn(
                name: "description",
                table: "AssetHistories",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "AssetHistories",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "updated_at",
                table: "AssetHistories",
                newName: "UpdatedAt");

            migrationBuilder.RenameColumn(
                name: "operator_id",
                table: "AssetHistories",
                newName: "OperatorId");

            migrationBuilder.RenameColumn(
                name: "operation_type",
                table: "AssetHistories",
                newName: "OperationType");

            migrationBuilder.RenameColumn(
                name: "operation_time",
                table: "AssetHistories",
                newName: "OperationTime");

            migrationBuilder.RenameColumn(
                name: "created_at",
                table: "AssetHistories",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "asset_id",
                table: "AssetHistories",
                newName: "AssetId");

            migrationBuilder.RenameIndex(
                name: "ix_asset_histories_operator_id",
                table: "AssetHistories",
                newName: "IX_AssetHistories_OperatorId");

            migrationBuilder.RenameIndex(
                name: "ix_asset_histories_asset_id",
                table: "AssetHistories",
                newName: "IX_AssetHistories_AssetId");

            migrationBuilder.AddColumn<int>(
                name: "CreatedBy",
                table: "ReturnToFactories",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Reason",
                table: "ReturnToFactories",
                type: "varchar(500)",
                maxLength: 500,
                nullable: false,
                defaultValue: "")
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<DateTime>(
                name: "ReturnDate",
                table: "ReturnToFactories",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<int>(
                name: "UpdatedBy",
                table: "ReturnToFactories",
                type: "int",
                nullable: true);

            migrationBuilder.AlterColumn<bool>(
                name: "is_pinned",
                table: "quick_memos",
                type: "tinyint(1)",
                nullable: false,
                defaultValue: false,
                oldClrType: typeof(bool),
                oldType: "tinyint(1)");

            migrationBuilder.AlterColumn<int>(
                name: "AssetTypeId",
                table: "PurchaseItems",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsAssetGenerated",
                table: "PurchaseItems",
                type: "tinyint(1)",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AlterColumn<int>(
                name: "AssetId",
                table: "FaultRecords",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddPrimaryKey(
                name: "PK_users",
                table: "users",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_userroles",
                table: "userroles",
                columns: new[] { "UserId", "RoleId" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_tasks",
                table: "tasks",
                column: "TaskId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Suppliers",
                table: "Suppliers",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_spare_parts",
                table: "spare_parts",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_spare_part_types",
                table: "spare_part_types",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_spare_part_transactions",
                table: "spare_part_transactions",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_spare_part_locations",
                table: "spare_part_locations",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_roles",
                table: "roles",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_rolepermissions",
                table: "rolepermissions",
                columns: new[] { "RoleId", "PermissionId" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_rolemenus",
                table: "rolemenus",
                columns: new[] { "RoleId", "MenuId" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_ReturnToFactories",
                table: "ReturnToFactories",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_quick_memos",
                table: "quick_memos",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_quick_memo_categories",
                table: "quick_memo_categories",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PurchaseOrders",
                table: "PurchaseOrders",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PurchaseItems",
                table: "PurchaseItems",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_personnel",
                table: "personnel",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_permissions",
                table: "permissions",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Menus",
                table: "Menus",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_locationusers",
                table: "locationusers",
                columns: new[] { "location_id", "personnel_id", "user_type" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_locations",
                table: "locations",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_locationhistories",
                table: "locationhistories",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_FaultRecords",
                table: "FaultRecords",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_departments",
                table: "departments",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_comments",
                table: "comments",
                column: "CommentId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_attachments",
                table: "attachments",
                column: "AttachmentId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_assettypes",
                table: "assettypes",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_assets",
                table: "assets",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_taskhistory",
                table: "taskhistory",
                column: "TaskHistoryId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_taskassignees",
                table: "taskassignees",
                column: "TaskAssigneeId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_RefreshTokens",
                table: "RefreshTokens",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_periodictaskschedules",
                table: "periodictaskschedules",
                column: "periodic_task_schedule_id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_pdcaplans",
                table: "pdcaplans",
                column: "pdca_plan_id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_MaintenanceOrders",
                table: "MaintenanceOrders",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_FaultTypes",
                table: "FaultTypes",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_AuditLogs",
                table: "AuditLogs",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_AssetReceives",
                table: "AssetReceives",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_AssetHistories",
                table: "AssetHistories",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_tasks_PreviousInstanceTaskId",
                table: "tasks",
                column: "PreviousInstanceTaskId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_spare_parts_code",
                table: "spare_parts",
                column: "code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_spare_parts_quantity",
                table: "spare_parts",
                column: "quantity");

            migrationBuilder.CreateIndex(
                name: "IX_spare_part_types_code",
                table: "spare_part_types",
                column: "code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_spare_part_types_path",
                table: "spare_part_types",
                column: "path");

            migrationBuilder.CreateIndex(
                name: "IX_spare_part_transactions_batch_number",
                table: "spare_part_transactions",
                column: "batch_number");

            migrationBuilder.CreateIndex(
                name: "IX_spare_part_transactions_transaction_time",
                table: "spare_part_transactions",
                column: "transaction_time");

            migrationBuilder.CreateIndex(
                name: "IX_spare_part_transactions_type",
                table: "spare_part_transactions",
                column: "type");

            migrationBuilder.CreateIndex(
                name: "IX_spare_part_locations_area",
                table: "spare_part_locations",
                column: "area");

            migrationBuilder.CreateIndex(
                name: "IX_spare_part_locations_code",
                table: "spare_part_locations",
                column: "code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ReturnToFactories_OperatorId",
                table: "ReturnToFactories",
                column: "OperatorId");

            migrationBuilder.CreateIndex(
                name: "ix_quick_memos_user_category",
                table: "quick_memos",
                columns: new[] { "user_id", "category_id" });

            migrationBuilder.CreateIndex(
                name: "ix_quick_memos_user_pinned_updated",
                table: "quick_memos",
                columns: new[] { "user_id", "is_pinned", "updated_at" });

            migrationBuilder.CreateIndex(
                name: "ix_quick_memo_categories_user_name",
                table: "quick_memo_categories",
                columns: new[] { "user_id", "name" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_periodictaskschedules_template_task_id",
                table: "periodictaskschedules",
                column: "template_task_id");

            migrationBuilder.CreateIndex(
                name: "IX_pdcaplans_TaskId",
                table: "pdcaplans",
                column: "TaskId",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_AssetHistories_assets_AssetId",
                table: "AssetHistories",
                column: "AssetId",
                principalTable: "assets",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AssetHistories_users_OperatorId",
                table: "AssetHistories",
                column: "OperatorId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AssetReceives_assets_AssetId",
                table: "AssetReceives",
                column: "AssetId",
                principalTable: "assets",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AssetReceives_locations_LocationId",
                table: "AssetReceives",
                column: "LocationId",
                principalTable: "locations",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AssetReceives_PurchaseOrders_PurchaseOrderId",
                table: "AssetReceives",
                column: "PurchaseOrderId",
                principalTable: "PurchaseOrders",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AssetReceives_ReturnToFactories_ReturnToFactoryId",
                table: "AssetReceives",
                column: "ReturnToFactoryId",
                principalTable: "ReturnToFactories",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AssetReceives_users_ReceiverId",
                table: "AssetReceives",
                column: "ReceiverId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_assets_assettypes_AssetTypeId",
                table: "assets",
                column: "AssetTypeId",
                principalTable: "assettypes",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_assets_departments_DepartmentId",
                table: "assets",
                column: "DepartmentId",
                principalTable: "departments",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_assets_locations_LocationId",
                table: "assets",
                column: "LocationId",
                principalTable: "locations",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_assettypes_assettypes_ParentId",
                table: "assettypes",
                column: "ParentId",
                principalTable: "assettypes",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "FK_attachments_comments_CommentId",
                table: "attachments",
                column: "CommentId",
                principalTable: "comments",
                principalColumn: "CommentId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_attachments_tasks_TaskId",
                table: "attachments",
                column: "TaskId",
                principalTable: "tasks",
                principalColumn: "TaskId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_attachments_users_UploaderUserId",
                table: "attachments",
                column: "UploaderUserId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AuditLogs_users_UserId",
                table: "AuditLogs",
                column: "UserId",
                principalTable: "users",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "FK_comments_comments_ParentCommentId",
                table: "comments",
                column: "ParentCommentId",
                principalTable: "comments",
                principalColumn: "CommentId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_comments_tasks_TaskId",
                table: "comments",
                column: "TaskId",
                principalTable: "tasks",
                principalColumn: "TaskId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_comments_users_UserId",
                table: "comments",
                column: "UserId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_departments_departments_ParentId",
                table: "departments",
                column: "ParentId",
                principalTable: "departments",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "FK_FaultRecords_assets_AssetId",
                table: "FaultRecords",
                column: "AssetId",
                principalTable: "assets",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_FaultRecords_FaultTypes_FaultTypeId",
                table: "FaultRecords",
                column: "FaultTypeId",
                principalTable: "FaultTypes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_FaultRecords_locations_LocationId",
                table: "FaultRecords",
                column: "LocationId",
                principalTable: "locations",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "FK_FaultRecords_users_AssigneeId",
                table: "FaultRecords",
                column: "AssigneeId",
                principalTable: "users",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "FK_FaultRecords_users_ReporterId",
                table: "FaultRecords",
                column: "ReporterId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_FaultTypes_FaultTypes_ParentId",
                table: "FaultTypes",
                column: "ParentId",
                principalTable: "FaultTypes",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_locationhistories_assets_AssetId",
                table: "locationhistories",
                column: "AssetId",
                principalTable: "assets",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_locationhistories_locations_NewLocationId",
                table: "locationhistories",
                column: "NewLocationId",
                principalTable: "locations",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_locationhistories_locations_OldLocationId",
                table: "locationhistories",
                column: "OldLocationId",
                principalTable: "locations",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_locationhistories_users_OperatorId",
                table: "locationhistories",
                column: "OperatorId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_locations_departments_DefaultDepartmentId",
                table: "locations",
                column: "DefaultDepartmentId",
                principalTable: "departments",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_locations_locations_ParentId",
                table: "locations",
                column: "ParentId",
                principalTable: "locations",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_locations_users_DefaultResponsiblePersonId",
                table: "locations",
                column: "DefaultResponsiblePersonId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_locationusers_locations_location_id",
                table: "locationusers",
                column: "location_id",
                principalTable: "locations",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_locationusers_personnel_personnel_id",
                table: "locationusers",
                column: "personnel_id",
                principalTable: "personnel",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_locationusers_users_UserId",
                table: "locationusers",
                column: "UserId",
                principalTable: "users",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "FK_MaintenanceOrders_assets_AssetId",
                table: "MaintenanceOrders",
                column: "AssetId",
                principalTable: "assets",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_MaintenanceOrders_FaultRecords_FaultRecordId",
                table: "MaintenanceOrders",
                column: "FaultRecordId",
                principalTable: "FaultRecords",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_MaintenanceOrders_locations_LocationId",
                table: "MaintenanceOrders",
                column: "LocationId",
                principalTable: "locations",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_MaintenanceOrders_users_AssigneeId",
                table: "MaintenanceOrders",
                column: "AssigneeId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Menus_Menus_ParentId",
                table: "Menus",
                column: "ParentId",
                principalTable: "Menus",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_pdcaplans_tasks_TaskId",
                table: "pdcaplans",
                column: "TaskId",
                principalTable: "tasks",
                principalColumn: "TaskId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_pdcaplans_users_creator_user_id",
                table: "pdcaplans",
                column: "creator_user_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_pdcaplans_users_responsible_person_id",
                table: "pdcaplans",
                column: "responsible_person_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_periodictaskschedules_tasks_template_task_id",
                table: "periodictaskschedules",
                column: "template_task_id",
                principalTable: "tasks",
                principalColumn: "TaskId",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_periodictaskschedules_users_creator_user_id",
                table: "periodictaskschedules",
                column: "creator_user_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_personnel_departments_department_id",
                table: "personnel",
                column: "department_id",
                principalTable: "departments",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "FK_PurchaseItems_assettypes_AssetTypeId",
                table: "PurchaseItems",
                column: "AssetTypeId",
                principalTable: "assettypes",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PurchaseItems_PurchaseOrders_PurchaseOrderId",
                table: "PurchaseItems",
                column: "PurchaseOrderId",
                principalTable: "PurchaseOrders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PurchaseOrders_Suppliers_SupplierId",
                table: "PurchaseOrders",
                column: "SupplierId",
                principalTable: "Suppliers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PurchaseOrders_users_RequesterId",
                table: "PurchaseOrders",
                column: "RequesterId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_quick_memo_categories_users_user_id",
                table: "quick_memo_categories",
                column: "user_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_quick_memos_quick_memo_categories_category_id",
                table: "quick_memos",
                column: "category_id",
                principalTable: "quick_memo_categories",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_quick_memos_users_user_id",
                table: "quick_memos",
                column: "user_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_RefreshTokens_users_UserId",
                table: "RefreshTokens",
                column: "UserId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_ReturnToFactories_assets_AssetId",
                table: "ReturnToFactories",
                column: "AssetId",
                principalTable: "assets",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_ReturnToFactories_Suppliers_SupplierId",
                table: "ReturnToFactories",
                column: "SupplierId",
                principalTable: "Suppliers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_ReturnToFactories_users_OperatorId",
                table: "ReturnToFactories",
                column: "OperatorId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_rolemenus_Menus_MenuId",
                table: "rolemenus",
                column: "MenuId",
                principalTable: "Menus",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_rolemenus_roles_RoleId",
                table: "rolemenus",
                column: "RoleId",
                principalTable: "roles",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_rolepermissions_permissions_PermissionId",
                table: "rolepermissions",
                column: "PermissionId",
                principalTable: "permissions",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_rolepermissions_roles_RoleId",
                table: "rolepermissions",
                column: "RoleId",
                principalTable: "roles",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_spare_part_transactions_spare_part_locations_location_id",
                table: "spare_part_transactions",
                column: "location_id",
                principalTable: "spare_part_locations",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_spare_part_transactions_spare_parts_part_id",
                table: "spare_part_transactions",
                column: "part_id",
                principalTable: "spare_parts",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_spare_part_types_spare_part_types_parent_id",
                table: "spare_part_types",
                column: "parent_id",
                principalTable: "spare_part_types",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_spare_parts_spare_part_locations_location_id",
                table: "spare_parts",
                column: "location_id",
                principalTable: "spare_part_locations",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_spare_parts_spare_part_types_type_id",
                table: "spare_parts",
                column: "type_id",
                principalTable: "spare_part_types",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_taskassignees_tasks_TaskId",
                table: "taskassignees",
                column: "TaskId",
                principalTable: "tasks",
                principalColumn: "TaskId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_taskassignees_users_AssignedByUserId",
                table: "taskassignees",
                column: "AssignedByUserId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_taskassignees_users_UserId",
                table: "taskassignees",
                column: "UserId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_taskhistory_attachments_AttachmentId",
                table: "taskhistory",
                column: "AttachmentId",
                principalTable: "attachments",
                principalColumn: "AttachmentId",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_taskhistory_comments_CommentId",
                table: "taskhistory",
                column: "CommentId",
                principalTable: "comments",
                principalColumn: "CommentId",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_taskhistory_tasks_TaskId",
                table: "taskhistory",
                column: "TaskId",
                principalTable: "tasks",
                principalColumn: "TaskId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_taskhistory_users_UserId",
                table: "taskhistory",
                column: "UserId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_tasks_assets_AssetId",
                table: "tasks",
                column: "AssetId",
                principalTable: "assets",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_tasks_locations_LocationId",
                table: "tasks",
                column: "LocationId",
                principalTable: "locations",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_tasks_tasks_ParentTaskId",
                table: "tasks",
                column: "ParentTaskId",
                principalTable: "tasks",
                principalColumn: "TaskId",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_tasks_tasks_PreviousInstanceTaskId",
                table: "tasks",
                column: "PreviousInstanceTaskId",
                principalTable: "tasks",
                principalColumn: "TaskId",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_tasks_users_AssigneeUserId",
                table: "tasks",
                column: "AssigneeUserId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_tasks_users_CreatorUserId",
                table: "tasks",
                column: "CreatorUserId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_userroles_roles_RoleId",
                table: "userroles",
                column: "RoleId",
                principalTable: "roles",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_userroles_users_UserId",
                table: "userroles",
                column: "UserId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_users_departments_DepartmentId",
                table: "users",
                column: "DepartmentId",
                principalTable: "departments",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "FK_users_roles_DefaultRoleId",
                table: "users",
                column: "DefaultRoleId",
                principalTable: "roles",
                principalColumn: "id");
        }
    }
}
