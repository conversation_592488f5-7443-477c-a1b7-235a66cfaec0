import{_ as e,j as s,r as a,a5 as r,m as l,b as o,e as d,w as t,a as u,u as n,o as w,d as i,A as p,a8 as m}from"./index-C7OOw0MO.js";import{P as c}from"./PageHeader-BkdIizRq.js";const f={class:"change-password"},g=e({__name:"ChangePassword",setup(e){const g=n(),P=s(),h=a(null),_=r({oldPassword:"",newPassword:"",confirmPassword:""}),b={oldPassword:[{required:!0,message:"请输入当前密码",trigger:"blur"},{min:6,message:"密码长度不能少于6个字符",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{validator:(e,s,a)=>{if(""===s)a(new Error("请输入密码"));else if(s.length<8)a(new Error("密码长度不能少于8个字符"));else{let e=0;/\d/.test(s)&&e++,/[a-z]/.test(s)&&e++,/[A-Z]/.test(s)&&e++,/[^a-zA-Z0-9]/.test(s)&&e++,e<3?a(new Error("密码强度不足，请包含数字、大小写字母和特殊字符")):a()}},trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入新密码",trigger:"blur"},{validator:(e,s,a)=>{""===s?a(new Error("请再次输入密码")):s!==_.newPassword?a(new Error("两次输入密码不一致")):a()},trigger:"blur"}]},v=async()=>{h.value&&await h.value.validate((async e=>{if(!e)return!1;try{const e=await P.changePassword({oldPassword:_.oldPassword,newPassword:_.newPassword});e.success?(m.success("密码修改成功，请重新登录"),setTimeout((()=>{P.logout(),g.push("/login")}),1500)):m.error(e.message||"修改密码失败")}catch(s){m.error("修改密码失败: "+(s.message||"未知错误"))}}))},y=()=>{h.value&&h.value.resetFields()};return l((()=>{})),(e,s)=>{const a=u("el-input"),r=u("el-form-item"),l=u("el-button"),n=u("el-form"),m=u("el-card");return w(),o("div",f,[d(c,{title:"修改密码",description:"修改您的账户登录密码"}),d(m,{shadow:"hover",class:"password-card"},{default:t((()=>[d(n,{ref_key:"passwordFormRef",ref:h,model:_,rules:b,"label-width":"120px",class:"password-form"},{default:t((()=>[d(r,{label:"当前密码",prop:"oldPassword"},{default:t((()=>[d(a,{modelValue:_.oldPassword,"onUpdate:modelValue":s[0]||(s[0]=e=>_.oldPassword=e),type:"password",placeholder:"请输入当前密码","show-password":""},null,8,["modelValue"])])),_:1}),d(r,{label:"新密码",prop:"newPassword"},{default:t((()=>[d(a,{modelValue:_.newPassword,"onUpdate:modelValue":s[1]||(s[1]=e=>_.newPassword=e),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])])),_:1}),d(r,{label:"确认新密码",prop:"confirmPassword"},{default:t((()=>[d(a,{modelValue:_.confirmPassword,"onUpdate:modelValue":s[2]||(s[2]=e=>_.confirmPassword=e),type:"password",placeholder:"请再次输入新密码","show-password":""},null,8,["modelValue"])])),_:1}),d(r,null,{default:t((()=>[d(l,{type:"primary",onClick:v},{default:t((()=>s[3]||(s[3]=[p("保存修改")]))),_:1}),d(l,{onClick:y},{default:t((()=>s[4]||(s[4]=[p("重置")]))),_:1})])),_:1})])),_:1},8,["model"]),s[5]||(s[5]=i("div",{class:"password-tips"},[i("h4",null,"密码安全提示："),i("ul",null,[i("li",null,"密码长度应不少于8个字符"),i("li",null,"应包含大小写字母、数字和特殊字符"),i("li",null,"不要使用容易被猜到的信息（如生日、姓名等）"),i("li",null,"定期更换密码以提高账户安全性")])],-1))])),_:1})])}}},[["__scopeId","data-v-938efcdd"]]);export{g as default};
