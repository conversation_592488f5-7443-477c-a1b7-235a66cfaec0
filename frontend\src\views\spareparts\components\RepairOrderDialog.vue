<template>
  <el-dialog
    :model-value="props.visible"
    @update:model-value="emit('update:visible', $event)"
    :title="mode === 'create' ? '创建返厂维修单' : '编辑返厂维修单'"
    width="900px"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <!-- 基本信息 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <span class="section-title">📋 基本信息</span>
        </template>
        
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="维修标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入维修标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="form.priority" placeholder="请选择优先级" style="width: 100%">
                <el-option label="紧急" :value="1" />
                <el-option label="高" :value="2" />
                <el-option label="中" :value="3" />
                <el-option label="低" :value="4" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="维修描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入维修描述" />
        </el-form-item>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="维修供应商" prop="supplierId">
              <el-select v-model="form.supplierId" placeholder="请选择维修供应商" style="width: 100%">
                <el-option v-for="supplier in suppliers" :key="supplier.id" :label="supplier.name" :value="supplier.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关联故障" prop="faultId">
              <el-select v-model="form.faultId" placeholder="选择关联故障（可选）" clearable style="width: 100%">
                <el-option v-for="fault in faults" :key="fault.id" :label="fault.title" :value="fault.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="预估费用">
              <el-input-number v-model="form.estimatedCost" :min="0" :precision="2" placeholder="预估费用" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预估天数">
              <el-input-number v-model="form.estimatedDays" :min="1" placeholder="预估维修天数" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注">
          <el-input v-model="form.notes" type="textarea" :rows="2" placeholder="请输入备注" />
        </el-form-item>
      </el-card>

      <!-- 维修物品选择 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <div class="section-header">
            <span class="section-title">🔧 维修物品</span>
            <el-button type="primary" size="small" @click="showSparePartSelector">选择备件</el-button>
          </div>
        </template>

        <div v-if="form.repairItems.length === 0" class="empty-items">
          <el-empty description="暂无维修物品，请点击上方按钮选择备件" />
        </div>

        <div v-else class="repair-items">
          <div v-for="(item, index) in form.repairItems" :key="index" class="repair-item">
            <el-card class="item-card" shadow="hover">
              <div class="item-header">
                <div class="item-info">
                  <span class="item-name">{{ item.partName }}</span>
                  <span class="item-code">{{ item.partCode }}</span>
                </div>
                <el-button type="danger" size="small" circle @click="removeRepairItem(index)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
              
              <el-row :gutter="16" class="item-details">
                <el-col :span="8">
                  <el-form-item :label="`数量`" :prop="`repairItems.${index}.quantity`">
                    <el-input-number v-model="item.quantity" :min="1" :max="item.availableQuantity" style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="故障描述">
                    <el-input v-model="item.faultDescription" placeholder="故障描述" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="序列号">
                    <el-input v-model="item.serialNumber" placeholder="序列号（可选）" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>
          </div>
        </div>
      </el-card>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">{{ mode === 'create' ? '创建' : '保存' }}</el-button>
      </div>
    </template>

    <!-- 备件选择器对话框 -->
    <SparePartSelector
      v-model:visible="sparePartSelectorVisible"
      :selected-parts="form.repairItems"
      @confirm="handleSparePartsSelected"
    />
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import SparePartSelector from './SparePartSelector.vue'
import {
  createRepairOrder,
  updateRepairOrder,
  getMaintenanceSuppliers
} from '@/api/spareparts'
import { getFaults } from '@/api/faults'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  orderData: {
    type: Object,
    default: () => ({})
  },
  mode: {
    type: String,
    default: 'create' // 'create' | 'edit'
  }
})

// Emits
const emit = defineEmits(['update:visible', 'submit'])

// 响应式数据
const formRef = ref()
const sparePartSelectorVisible = ref(false)
const suppliers = ref([])
const faults = ref([])

const form = reactive({
  id: null,
  title: '',
  description: '',
  priority: 3,
  supplierId: null,
  faultId: null,
  estimatedCost: null,
  estimatedDays: null,
  notes: '',
  repairItems: []
})

const rules = {
  title: [{ required: true, message: '请输入维修标题', trigger: 'blur' }],
  description: [{ required: true, message: '请输入维修描述', trigger: 'blur' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
  supplierId: [{ required: true, message: '请选择维修供应商', trigger: 'change' }]
}

// 计算属性

// 方法
const loadSuppliers = async () => {
  try {
    const response = await getMaintenanceSuppliers()
    if (response.success) {
      suppliers.value = response.data || []
    }
  } catch (error) {
    console.error('加载维修供应商失败:', error)
  }
}

const loadFaults = async () => {
  try {
    // 使用数字状态值：0=待处理, 1=处理中
    const response = await getFaults({ status: 0 })
    if (response.success) {
      faults.value = response.data.items || []
    }
  } catch (error) {
    console.error('加载故障清单失败:', error)
  }
}

const showSparePartSelector = () => {
  sparePartSelectorVisible.value = true
}

const handleSparePartsSelected = (selectedParts) => {
  // 添加新选择的备件，避免重复
  selectedParts.forEach(part => {
    const exists = form.repairItems.find(item => item.partId === part.id)
    if (!exists) {
      form.repairItems.push({
        partId: part.id,
        partCode: part.code,
        partName: part.name,
        availableQuantity: part.stockQuantity,
        quantity: 1,
        faultDescription: '',
        serialNumber: ''
      })
    }
  })
  sparePartSelectorVisible.value = false
}

const removeRepairItem = (index) => {
  form.repairItems.splice(index, 1)
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    if (form.repairItems.length === 0) {
      ElMessage.warning('请至少选择一个维修物品')
      return
    }

    const submitData = {
      ...form,
      repairItems: form.repairItems.map(item => ({
        partId: item.partId,
        quantity: item.quantity,
        faultDescription: item.faultDescription,
        serialNumber: item.serialNumber
      }))
    }

    let response
    if (props.mode === 'create') {
      response = await createRepairOrder(submitData)
    } else {
      response = await updateRepairOrder(form.id, submitData)
    }

    if (response.success) {
      ElMessage.success(props.mode === 'create' ? '维修单创建成功' : '维修单更新成功')
      emit('submit', response.data)
    } else {
      ElMessage.error('操作失败: ' + response.message)
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  }
}

const handleClose = () => {
  emit('update:visible', false)
  resetForm()
}

const resetForm = () => {
  Object.assign(form, {
    id: null,
    title: '',
    description: '',
    priority: 3,
    supplierId: null,
    faultId: null,
    estimatedCost: null,
    estimatedDays: null,
    notes: '',
    repairItems: []
  })
}

// 监听props变化
watch(() => props.visible, (newVisible) => {
  console.log('RepairOrderDialog visible 变化:', newVisible)
  if (newVisible) {
    console.log('对话框打开，模式:', props.mode)
  }
}, { immediate: true })

watch(() => props.orderData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(form, {
      ...newData,
      repairItems: newData.repairItems || []
    })
  }
}, { deep: true, immediate: true })

// 生命周期
onMounted(() => {
  loadSuppliers()
  loadFaults()
})
</script>

<style scoped>
.form-section {
  margin-bottom: 16px;
}

.section-title {
  font-weight: 600;
  font-size: 14px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-items {
  text-align: center;
  padding: 40px 0;
}

.repair-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.repair-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.item-card {
  border: none;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.item-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.item-name {
  font-weight: 600;
  font-size: 14px;
}

.item-code {
  font-size: 12px;
  color: #909399;
}

.item-details {
  margin-top: 12px;
}

.dialog-footer {
  text-align: right;
}
</style>
