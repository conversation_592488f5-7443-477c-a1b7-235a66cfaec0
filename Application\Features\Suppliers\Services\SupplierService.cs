using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Application.Features.Suppliers.Services;
using ItAssetsSystem.Application.Features.Suppliers.Dtos;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models.Entities;
using ItAssetsSystem.Models.Enums;

namespace ItAssetsSystem.Application.Features.Suppliers.Services
{
    /// <summary>
    /// 供应商服务实现
    /// </summary>
    public class SupplierService : ISupplierService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<SupplierService> _logger;

        public SupplierService(
            AppDbContext context,
            ILogger<SupplierService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 获取供应商列表（分页）
        /// </summary>
        public async Task<PaginatedResult<SupplierDto>> GetSuppliersAsync(SupplierQuery query)
        {
            var queryable = _context.Suppliers.AsQueryable();

            // 应用筛选条件
            if (!string.IsNullOrEmpty(query.Name))
            {
                queryable = queryable.Where(s => s.Name.Contains(query.Name));
            }

            if (!string.IsNullOrEmpty(query.Code))
            {
                queryable = queryable.Where(s => s.Code.Contains(query.Code));
            }

            if (!string.IsNullOrEmpty(query.ContactPerson))
            {
                queryable = queryable.Where(s => s.ContactPerson.Contains(query.ContactPerson));
            }

            if (query.SupplierType.HasValue)
            {
                queryable = queryable.Where(s => (s.SupplierType & query.SupplierType.Value) != 0);
            }

            if (query.IsActive.HasValue)
            {
                queryable = queryable.Where(s => s.IsActive == query.IsActive.Value);
            }

            // 计算总数
            var totalCount = await queryable.CountAsync();

            // 应用分页和排序
            var items = await queryable
                .OrderByDescending(s => s.CreatedAt)
                .Skip((query.PageIndex - 1) * query.PageSize)
                .Take(query.PageSize)
                .Select(s => MapToDto(s))
                .ToListAsync();

            return new PaginatedResult<SupplierDto>
            {
                Items = items,
                TotalCount = totalCount,
                PageIndex = query.PageIndex,
                PageSize = query.PageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / query.PageSize)
            };
        }

        /// <summary>
        /// 根据ID获取供应商详情
        /// </summary>
        public async Task<SupplierDto> GetSupplierByIdAsync(int id)
        {
            var supplier = await _context.Suppliers
                .FirstOrDefaultAsync(s => s.Id == id);

            return supplier == null ? null : MapToDto(supplier);
        }

        /// <summary>
        /// 创建供应商
        /// </summary>
        public async Task<SupplierDto> CreateSupplierAsync(CreateSupplierRequest request)
        {
            // 检查编码是否已存在
            if (await IsCodeExistsAsync(request.Code))
            {
                throw new ArgumentException($"供应商编码 {request.Code} 已存在");
            }

            // 检查名称是否已存在
            if (await IsNameExistsAsync(request.Name))
            {
                throw new ArgumentException($"供应商名称 {request.Name} 已存在");
            }

            // 验证邮箱格式（如果提供了邮箱）
            if (!string.IsNullOrWhiteSpace(request.ContactEmail) && !IsValidEmail(request.ContactEmail))
            {
                throw new ArgumentException("邮箱格式不正确");
            }

            var supplier = new Supplier
            {
                Name = request.Name,
                Code = request.Code,
                SupplierType = request.SupplierType,
                ContactPerson = request.ContactPerson,
                ContactPhone = request.ContactPhone,
                ContactEmail = request.ContactEmail,
                Address = request.Address,
                Notes = request.Notes,
                IsActive = true,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            _context.Suppliers.Add(supplier);
            await _context.SaveChangesAsync();

            _logger.LogInformation("创建供应商成功，ID: {SupplierId}, 名称: {SupplierName}", supplier.Id, supplier.Name);

            return MapToDto(supplier);
        }

        /// <summary>
        /// 更新供应商
        /// </summary>
        public async Task<SupplierDto> UpdateSupplierAsync(int id, UpdateSupplierRequest request)
        {
            var supplier = await _context.Suppliers.FirstOrDefaultAsync(s => s.Id == id);
            if (supplier == null)
            {
                return null;
            }

            // 检查编码是否已存在（排除当前记录）
            if (await IsCodeExistsAsync(request.Code, id))
            {
                throw new ArgumentException($"供应商编码 {request.Code} 已存在");
            }

            // 检查名称是否已存在（排除当前记录）
            if (await IsNameExistsAsync(request.Name, id))
            {
                throw new ArgumentException($"供应商名称 {request.Name} 已存在");
            }

            // 验证邮箱格式（如果提供了邮箱）
            if (!string.IsNullOrWhiteSpace(request.ContactEmail) && !IsValidEmail(request.ContactEmail))
            {
                throw new ArgumentException("邮箱格式不正确");
            }

            // 更新字段
            supplier.Name = request.Name;
            supplier.Code = request.Code;
            supplier.SupplierType = request.SupplierType;
            supplier.ContactPerson = request.ContactPerson;
            supplier.ContactPhone = request.ContactPhone;
            supplier.ContactEmail = request.ContactEmail;
            supplier.Address = request.Address;
            supplier.Notes = request.Notes;
            supplier.IsActive = request.IsActive;
            supplier.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();

            _logger.LogInformation("更新供应商成功，ID: {SupplierId}, 名称: {SupplierName}", supplier.Id, supplier.Name);

            return MapToDto(supplier);
        }

        /// <summary>
        /// 删除供应商
        /// </summary>
        public async Task<bool> DeleteSupplierAsync(int id)
        {
            var supplier = await _context.Suppliers.FirstOrDefaultAsync(s => s.Id == id);
            if (supplier == null)
            {
                return false;
            }

            // 检查是否有关联的采购订单
            var hasPurchaseOrders = await _context.PurchaseOrders.AnyAsync(po => po.SupplierId == id);
            if (hasPurchaseOrders)
            {
                throw new InvalidOperationException("该供应商存在关联的采购订单，无法删除");
            }

            _context.Suppliers.Remove(supplier);
            await _context.SaveChangesAsync();

            _logger.LogInformation("删除供应商成功，ID: {SupplierId}, 名称: {SupplierName}", supplier.Id, supplier.Name);

            return true;
        }

        /// <summary>
        /// 获取维修供应商列表
        /// </summary>
        public async Task<List<SupplierSimpleDto>> GetMaintenanceSuppliersAsync()
        {
            return await _context.Suppliers
                .Where(s => s.IsActive && (s.SupplierType & SupplierType.Maintenance) != 0)
                .OrderBy(s => s.Name)
                .Select(s => MapToSimpleDto(s))
                .ToListAsync();
        }

        /// <summary>
        /// 获取采购供应商列表
        /// </summary>
        public async Task<List<SupplierSimpleDto>> GetProcurementSuppliersAsync()
        {
            return await _context.Suppliers
                .Where(s => s.IsActive && (s.SupplierType & SupplierType.Procurement) != 0)
                .OrderBy(s => s.Name)
                .Select(s => MapToSimpleDto(s))
                .ToListAsync();
        }

        /// <summary>
        /// 获取所有激活的供应商
        /// </summary>
        public async Task<List<SupplierSimpleDto>> GetActiveSuppliersAsync()
        {
            return await _context.Suppliers
                .Where(s => s.IsActive)
                .OrderBy(s => s.Name)
                .Select(s => MapToSimpleDto(s))
                .ToListAsync();
        }

        /// <summary>
        /// 检查供应商编码是否存在
        /// </summary>
        public async Task<bool> IsCodeExistsAsync(string code, int? excludeId = null)
        {
            var query = _context.Suppliers.Where(s => s.Code == code);
            if (excludeId.HasValue)
            {
                query = query.Where(s => s.Id != excludeId.Value);
            }
            return await query.AnyAsync();
        }

        /// <summary>
        /// 检查供应商名称是否存在
        /// </summary>
        public async Task<bool> IsNameExistsAsync(string name, int? excludeId = null)
        {
            var query = _context.Suppliers.Where(s => s.Name == name);
            if (excludeId.HasValue)
            {
                query = query.Where(s => s.Id != excludeId.Value);
            }
            return await query.AnyAsync();
        }

        /// <summary>
        /// 验证邮箱格式
        /// </summary>
        private static bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 映射到DTO
        /// </summary>
        private static SupplierDto MapToDto(Supplier supplier)
        {
            return new SupplierDto
            {
                Id = supplier.Id,
                Name = supplier.Name,
                Code = supplier.Code,
                SupplierType = supplier.SupplierType,
                SupplierTypeDisplay = GetSupplierTypeDisplay(supplier.SupplierType),
                ContactPerson = supplier.ContactPerson,
                ContactPhone = supplier.ContactPhone,
                ContactEmail = supplier.ContactEmail,
                Address = supplier.Address,
                Notes = supplier.Notes,
                IsActive = supplier.IsActive,
                CreatedAt = supplier.CreatedAt,
                UpdatedAt = supplier.UpdatedAt
            };
        }

        /// <summary>
        /// 映射到简化DTO
        /// </summary>
        private static SupplierSimpleDto MapToSimpleDto(Supplier supplier)
        {
            return new SupplierSimpleDto
            {
                Id = supplier.Id,
                Name = supplier.Name,
                Code = supplier.Code,
                SupplierType = supplier.SupplierType,
                ContactPerson = supplier.ContactPerson,
                ContactPhone = supplier.ContactPhone
            };
        }

        /// <summary>
        /// 获取供应商类型显示名称
        /// </summary>
        private static string GetSupplierTypeDisplay(SupplierType supplierType)
        {
            return supplierType switch
            {
                SupplierType.Procurement => "采购供应商",
                SupplierType.Maintenance => "维修供应商",
                SupplierType.Both => "采购+维修供应商",
                _ => "未知类型"
            };
        }
    }
}
