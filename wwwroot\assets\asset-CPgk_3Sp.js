import{aj as e,ae as t}from"./index-C7OOw0MO.js";import{s}from"./system-9jEcQzSp.js";function n(t){const s={...t};return void 0!==s.pageIndex&&"number"!=typeof s.pageIndex&&(s.pageIndex=parseInt(s.pageIndex)||1),void 0!==s.pageSize&&"number"!=typeof s.pageSize&&(s.pageSize=parseInt(s.pageSize)||20),Object.keys(s).forEach((e=>{""===s[e]&&delete s[e]})),e.get("/Asset",{params:s}).then((e=>e)).catch((e=>{throw e}))}function r(t){return t?e.get(`/Asset/${t}`).then((e=>e)).catch((e=>{throw e})):Promise.reject(new Error("资产ID不能为空"))}function a(t){return e.post("/Asset",t)}function o(t,s){return e.put(`/Asset/${t}`,s)}function p(t){return e.delete(`/Asset/${t}`)}function i(t,s){return e.get(`/Asset/${t}/history`,{params:s})}function c(e={}){const n=t(),r=s.apiBaseUrl,a=new URLSearchParams;Object.keys(e).forEach((t=>{void 0!==e[t]&&""!==e[t]&&a.append(t,e[t])})),a.append("exportAll","true");const o=`${r}/Asset/export?${a.toString()}`,p=document.createElement("a");p.href=o,n&&(p.href=`${o}&access_token=${encodeURIComponent(n)}`);const i=new Date,c=`资产导出_${i.getFullYear()}-${String(i.getMonth()+1).padStart(2,"0")}-${String(i.getDate()).padStart(2,"0")}.xlsx`;return p.download=c,document.body.appendChild(p),p.click(),document.body.removeChild(p),Promise.resolve({success:!0,message:"导出请求已发送"})}const u={getAssetTypes:function(t){return e.get("/AssetType",{params:t})},createAssetType:function(t){return e.post("/AssetType",t)},updateAssetType:function(t,s){return e.put(`/AssetType/${t}`,s)},deleteAssetType:function(t){return e.delete(`/AssetType/${t}`)},getAssets:n,getAssetById:r,createAsset:a,updateAsset:o,deleteAsset:p,changeAssetLocation:function(t,s){const n={...s,reason:s.reason||"系统自动变更",notes:s.notes||""};return e.post(`/Asset/${t}/change-location`,n)},getAssetHistory:i,getLocationAssetHistory:function(t,s){return e.get(`/Asset/location/${t}/history`,{params:s})},exportAssets:c,getAssetImportTemplate:function(){return e.get("/import/template",{params:{entityType:"Assets",format:"excel"},responseType:"blob"})},importAssets:function(t){return t.has("entityType")||t.append("entityType","Assets"),e.post("/import/data",t,{headers:{"Content-Type":"multipart/form-data"}})},printAssetLabels:function(t){return e.post("/asset/print-labels",{ids:t})},getAssetStatistics:function(t){return e.get("/asset/statistics",{params:t})}};export{r as a,i as b,a as c,p as d,c as e,u as f,n as g,o as u};
