/**
 * 资产API接口 V1.1 - 新接口化版本
 * 文件路径: src/api/v1.1/asset.js
 * 功能描述: 基于新的统一服务接口的资产管理API
 */

import request from '@/utils/request'

/**
 * 资产API V1.1
 */
const assetApiV1_1 = {
  /**
   * 获取资产列表 (V1.1)
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  getAssetList(params) {
    const apiParams = {
      pageIndex: params.pageIndex || params.pageNumber || 1,
      pageSize: params.pageSize || 20,
      keyword: params.keyword || params.searchTerm || '',
      category: params.category || '',
      location: params.location || '',
      department: params.department || '',
      status: params.status || '',
      assetType: params.assetType || '',
      sortBy: params.sortBy || 'CreatedAt',
      sortDirection: params.sortDirection || 'desc'
    }
    
    console.log('V1.1 获取资产列表，参数:', apiParams)
    return request.get('/api/v1.1/asset', { params: apiParams })
  },

  /**
   * 获取资产详情 (V1.1)
   * @param {number|string} id 资产ID
   * @returns {Promise}
   */
  getAssetById(id) {
    console.log('V1.1 获取资产详情，ID:', id)
    return request.get(`/api/v1.1/asset/${id}`)
  },

  /**
   * 创建资产 (V1.1)
   * @param {Object} data 资产数据
   * @returns {Promise}
   */
  createAsset(data) {
    const apiData = {
      name: data.name,
      assetNumber: data.assetNumber || data.code,
      category: data.category,
      assetType: data.assetType || data.type,
      brand: data.brand,
      model: data.model,
      serialNumber: data.serialNumber,
      purchaseDate: data.purchaseDate,
      purchasePrice: data.purchasePrice,
      locationId: data.locationId,
      departmentId: data.departmentId,
      responsibleUserId: data.responsibleUserId || data.userId,
      status: data.status || 'Active',
      description: data.description || data.notes,
      specifications: data.specifications
    }
    
    console.log('V1.1 创建资产，数据:', apiData)
    return request.post('/api/v1.1/asset', apiData)
  },

  /**
   * 更新资产 (V1.1)
   * @param {number|string} id 资产ID
   * @param {Object} data 资产数据
   * @returns {Promise}
   */
  updateAsset(id, data) {
    const apiData = {
      name: data.name,
      assetNumber: data.assetNumber || data.code,
      category: data.category,
      assetType: data.assetType || data.type,
      brand: data.brand,
      model: data.model,
      serialNumber: data.serialNumber,
      purchaseDate: data.purchaseDate,
      purchasePrice: data.purchasePrice,
      locationId: data.locationId,
      departmentId: data.departmentId,
      responsibleUserId: data.responsibleUserId || data.userId,
      status: data.status,
      description: data.description || data.notes,
      specifications: data.specifications
    }
    
    console.log('V1.1 更新资产，ID:', id, '数据:', apiData)
    return request.put(`/api/v1.1/asset/${id}`, apiData)
  },

  /**
   * 删除资产 (V1.1)
   * @param {number|string} id 资产ID
   * @returns {Promise}
   */
  deleteAsset(id) {
    console.log('V1.1 删除资产，ID:', id)
    return request.delete(`/api/v1.1/asset/${id}`)
  },

  /**
   * 变更资产位置 (V1.1)
   * @param {number|string} id 资产ID
   * @param {Object} data 位置变更数据
   * @returns {Promise}
   */
  changeAssetLocation(id, data) {
    const apiData = {
      newLocationId: data.newLocationId,
      reason: data.reason || '系统变更',
      notes: data.notes || ''
    }
    
    console.log('V1.1 变更资产位置，ID:', id, '数据:', apiData)
    return request.post(`/api/v1.1/asset/${id}/change-location`, apiData)
  },

  /**
   * 获取资产历史记录 (V1.1)
   * @param {number|string} id 资产ID
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  getAssetHistory(id, params = {}) {
    console.log('V1.1 获取资产历史记录，ID:', id, '参数:', params)
    return request.get(`/api/v1.1/asset/${id}/history`, { params })
  },

  /**
   * 获取资产统计信息 (V1.1)
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  getAssetStatistics(params = {}) {
    console.log('V1.1 获取资产统计信息，参数:', params)
    return request.get('/api/v1.1/asset/statistics', { params })
  },

  /**
   * 搜索资产 (V1.1)
   * @param {Object} params 搜索参数
   * @returns {Promise}
   */
  searchAssets(params) {
    const apiParams = {
      keyword: params.keyword || params.search || '',
      category: params.category || '',
      location: params.location || '',
      department: params.department || '',
      limit: params.limit || 10
    }
    
    console.log('V1.1 搜索资产，参数:', apiParams)
    return request.get('/api/v1.1/asset/search', { params: apiParams })
  },

  /**
   * 批量获取资产信息 (V1.1)
   * @param {Array} assetIds 资产ID数组
   * @returns {Promise}
   */
  getAssetsByIds(assetIds) {
    console.log('V1.1 批量获取资产信息，IDs:', assetIds)
    return request.post('/api/v1.1/asset/batch', { assetIds })
  },

  /**
   * 导出资产数据 (V1.1)
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  exportAssets(params = {}) {
    console.log('V1.1 导出资产数据，参数:', params)
    return request.post('/api/v1.1/asset/export', params, {
      responseType: 'blob'
    })
  },

  /**
   * 导入资产数据 (V1.1)
   * @param {FormData} formData 包含Excel文件的表单数据
   * @returns {Promise}
   */
  importAssets(formData) {
    console.log('V1.1 导入资产数据')
    return request.post('/api/v1.1/asset/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 获取资产类型列表 (V1.1)
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  getAssetTypes(params = {}) {
    console.log('V1.1 获取资产类型列表，参数:', params)
    return request.get('/api/v1.1/asset/types', { params })
  },

  /**
   * 获取资产分类列表 (V1.1)
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  getAssetCategories(params = {}) {
    console.log('V1.1 获取资产分类列表，参数:', params)
    return request.get('/api/v1.1/asset/categories', { params })
  },

  /**
   * 获取位置资产历史记录 (V1.1)
   * @param {number|string} locationId 位置ID
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  getLocationAssetHistory(locationId, params = {}) {
    console.log('V1.1 获取位置资产历史记录，位置ID:', locationId, '参数:', params)
    return request.get(`/api/v1.1/asset/location/${locationId}/history`, { params })
  },

  /**
   * 资产标签打印 (V1.1)
   * @param {Array} ids 资产ID数组
   * @returns {Promise}
   */
  printAssetLabels(ids) {
    console.log('V1.1 打印资产标签，IDs:', ids)
    return request.post('/api/v1.1/asset/print-labels', { ids })
  },

  /**
   * 健康检查 (V1.1)
   * @returns {Promise}
   */
  healthCheck() {
    return request.get('/api/v1.1/asset/health')
  }
}

// 兼容性方法 - 保持与V1 API相同的方法名
assetApiV1_1.getAssets = assetApiV1_1.getAssetList
assetApiV1_1.getAssetDetail = assetApiV1_1.getAssetById

export default assetApiV1_1
