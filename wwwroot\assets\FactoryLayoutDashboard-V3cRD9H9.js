import{_ as e,r as t,c as a,C as l,ag as o,ah as n,ai as s,p as i,a as r,Y as c,o as u,w as d,d as v,b as m,a9 as f,e as p,A as h,Z as g,t as y,f as w,aj as b,ak as x,al as C,am as k,an as z,ao as _,a8 as M,ap as $,m as N,q as I,aq as E,i as D,F as R,h as S,X as L,ar as T,n as j,as as V,ae as W,v as A,at as B,au as q,G as O,R as P,av as U,af as F,J as H,ab as G,aw as X}from"./index-CkwLz8y6.js";const Y={class:"location-details-content"},J={class:"basic-info mb-6"},Q={class:"flex items-center justify-between mb-4"},Z={class:"grid grid-cols-2 gap-4"},K={class:"info-item"},ee={class:"info-value"},te={class:"info-item"},ae={class:"info-value"},le={class:"info-item"},oe={class:"info-value"},ne={class:"info-item"},se={class:"info-value"},ie={key:0,class:"monitoring-data mb-6"},re={class:"grid grid-cols-2 lg:grid-cols-4 gap-3"},ce={class:"monitoring-card"},ue={class:"monitoring-icon temperature"},de={class:"monitoring-info"},ve={class:"monitoring-value"},me={class:"monitoring-card"},fe={class:"monitoring-icon pressure"},pe={class:"monitoring-info"},he={class:"monitoring-value"},ge={class:"monitoring-card"},ye={class:"monitoring-icon uptime"},we={class:"monitoring-info"},be={class:"monitoring-value"},xe={class:"monitoring-card"},Ce={class:"monitoring-icon maintenance"},ke={class:"monitoring-info"},ze={class:"monitoring-value"},_e={key:1,class:"alerts mb-6"},Me={key:2,class:"assets-list mb-6"},$e={class:"max-h-48 overflow-y-auto"},Ne={class:"actions"},Ie={class:"flex justify-between"},Ee={class:"flex gap-2"},De=e({__name:"LocationDetailsModal",props:{location:{type:Object,required:!0}},emits:["close"],setup(e,{emit:$}){const N=e,I=$,E=t(!0),D={operational:{type:"success",text:"正常运行",icon:s},warning:{type:"warning",text:"警告状态",icon:n},error:{type:"danger",text:"故障状态",icon:o},idle:{type:"info",text:"空闲状态",icon:l}},R=a((()=>{var e;return(null==(e=D[N.location.status])?void 0:e.type)||"info"})),S=a((()=>{var e;return(null==(e=D[N.location.status])?void 0:e.text)||"未知状态"})),L=a((()=>{var e;return(null==(e=D[N.location.status])?void 0:e.icon)||l})),T=()=>{E.value=!1,setTimeout((()=>{I("close")}),300)},j=()=>{M.success("诊断已启动，请等待结果")},V=()=>{M.info("历史记录功能开发中")};return i((()=>N.location),(e=>{e&&(E.value=!0)}),{immediate:!0}),(t,a)=>{const l=r("el-icon"),o=r("el-tag"),n=r("el-alert"),s=r("el-table-column"),i=r("el-button"),$=r("el-table"),N=r("el-dialog");return u(),c(N,{modelValue:E.value,"onUpdate:modelValue":a[0]||(a[0]=e=>E.value=e),title:`位置详情 - ${e.location.locationName}`,width:"600px","before-close":T,class:"location-details-modal","modal-class":"custom-modal-backdrop"},{default:d((()=>{var t,r;return[v("div",Y,[v("div",J,[v("div",Q,[a[1]||(a[1]=v("h3",{class:"text-lg font-semibold text-gray-800"},"基本信息",-1)),p(o,{type:R.value,size:"large"},{default:d((()=>[p(l,{class:"mr-1"},{default:d((()=>[(u(),c(g(L.value)))])),_:1}),h(" "+y(S.value),1)])),_:1},8,["type"])]),v("div",Z,[v("div",K,[a[2]||(a[2]=v("label",{class:"info-label"},"位置路径",-1)),v("div",ee,y(e.location.locationPath),1)]),v("div",te,[a[3]||(a[3]=v("label",{class:"info-label"},"有效部门",-1)),v("div",ae,y(e.location.effectiveDepartmentName||"未分配"),1)]),v("div",le,[a[4]||(a[4]=v("label",{class:"info-label"},"直接部门",-1)),v("div",oe,y(e.location.directDepartmentName||"继承自上级"),1)]),v("div",ne,[a[5]||(a[5]=v("label",{class:"info-label"},"设备数量",-1)),v("div",se,y(e.location.assetCount)+" 台",1)])])]),e.location.details?(u(),m("div",ie,[a[10]||(a[10]=v("h3",{class:"text-lg font-semibold text-gray-800 mb-4"},"设备监控数据",-1)),v("div",re,[v("div",ce,[v("div",ue,[p(l,{size:"20"},{default:d((()=>[p(w(b))])),_:1})]),v("div",de,[a[6]||(a[6]=v("div",{class:"monitoring-label"},"温度",-1)),v("div",ve,y(e.location.details.temperature),1)])]),v("div",me,[v("div",fe,[p(l,{size:"20"},{default:d((()=>[p(w(x))])),_:1})]),v("div",pe,[a[7]||(a[7]=v("div",{class:"monitoring-label"},"压力",-1)),v("div",he,y(e.location.details.pressure),1)])]),v("div",ge,[v("div",ye,[p(l,{size:"20"},{default:d((()=>[p(w(C))])),_:1})]),v("div",we,[a[8]||(a[8]=v("div",{class:"monitoring-label"},"运行时间",-1)),v("div",be,y(e.location.details.uptime),1)])]),v("div",xe,[v("div",Ce,[p(l,{size:"20"},{default:d((()=>[p(w(k))])),_:1})]),v("div",ke,[a[9]||(a[9]=v("div",{class:"monitoring-label"},"最后维护",-1)),v("div",ze,y(e.location.details.lastMaintenance),1)])])])])):f("",!0),(null==(t=e.location.details)?void 0:t.errorCode)||(null==(r=e.location.details)?void 0:r.warningMessage)?(u(),m("div",_e,[a[11]||(a[11]=v("h3",{class:"text-lg font-semibold text-gray-800 mb-4"},"告警信息",-1)),e.location.details.errorCode?(u(),c(n,{key:0,title:"错误警告",type:"error",description:`错误代码: ${e.location.details.errorCode} - 请立即检查设备`,"show-icon":"",class:"mb-3"},null,8,["description"])):f("",!0),e.location.details.warningMessage?(u(),c(n,{key:1,title:"设备警告",type:"warning",description:e.location.details.warningMessage,"show-icon":""},null,8,["description"])):f("",!0)])):f("",!0),e.location.assets&&e.location.assets.length>0?(u(),m("div",Me,[a[13]||(a[13]=v("h3",{class:"text-lg font-semibold text-gray-800 mb-4"},"设备列表",-1)),v("div",$e,[p($,{data:e.location.assets,size:"small",stripe:""},{default:d((()=>[p(s,{prop:"assetCode",label:"设备编码",width:"120"}),p(s,{prop:"assetName",label:"设备名称"}),p(s,{prop:"assetTypeName",label:"设备类型",width:"120"}),p(s,{label:"操作",width:"100",align:"center"},{default:d((({row:e})=>[p(i,{type:"primary",size:"small",text:"",onClick:t=>{return a=e.assetId,void M.info(`查看设备 ${a} 详情`);var a}},{default:d((()=>a[12]||(a[12]=[h(" 查看 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])])])):f("",!0),v("div",Ne,[v("div",Ie,[v("div",Ee,[p(i,{type:"primary",onClick:j},{default:d((()=>[p(l,{class:"mr-1"},{default:d((()=>[p(w(z))])),_:1}),a[14]||(a[14]=h(" 启动诊断 "))])),_:1}),p(i,{onClick:V},{default:d((()=>[p(l,{class:"mr-1"},{default:d((()=>[p(w(_))])),_:1}),a[15]||(a[15]=h(" 查看历史 "))])),_:1})]),p(i,{onClick:T},{default:d((()=>a[16]||(a[16]=[h("关闭")]))),_:1})])])])]})),_:1},8,["modelValue","title"])}}},[["__scopeId","data-v-071d96de"]]),Re={class:"modern-factory-dashboard"},Se={class:"dashboard-header"},Le={class:"header-container"},Te={class:"brand-section"},je={class:"brand-icon"},Ve={class:"brand-content"},We={class:"brand-subtitle"},Ae={class:"header-actions"},Be={class:"search-container"},qe={key:0,class:"search-results"},Oe=["onClick"],Pe={class:"result-content"},Ue={class:"result-name"},Fe={class:"result-info"},He={class:"action-buttons"},Ge={class:"filter-content"},Xe={class:"filter-section"},Ye={class:"filter-section"},Je={class:"range-display"},Qe={class:"filter-actions"},Ze={class:"dashboard-main"},Ke={key:0,class:"loading-container"},et={key:1,class:"error-container"},tt={class:"dashboard-container"},at={class:"stats-panel"},lt={class:"stats-card overview-card"},ot={class:"card-header"},nt={class:"update-indicator"},st={class:"update-time"},it={class:"stats-grid"},rt=["onClick"],ct={class:"stat-visual"},ut={class:"stat-progress"},dt={class:"stat-data"},vt={class:"stat-value"},mt={class:"stat-label"},ft={key:0,class:"stat-percent"},pt={class:"system-metrics"},ht={class:"metric-item"},gt={class:"metric-value"},yt={class:"metric-item"},wt={class:"metric-value"},bt={class:"stats-card zone-card"},xt={class:"zone-list"},Ct={class:"zone-info"},kt={class:"zone-name"},zt={class:"zone-metrics"},_t={class:"zone-total"},Mt={class:"zone-efficiency"},$t={class:"zone-status"},Nt={class:"status-indicators"},It={key:0,class:"status-dot operational"},Et={key:1,class:"status-dot warning"},Dt={key:2,class:"status-dot error"},Rt={class:"factory-layout"},St=["viewBox"],Lt=["data-zone"],Tt={class:"zone-info-label"},jt={class:"zone-title"},Vt={key:0,class:"zone-debug"},Wt=["onClick","onMouseenter"],At={key:0,class:"selected-border"},Bt={key:1,class:"factory-list"},qt={key:0,class:"fault-count-text"},Ot={key:1,class:"no-fault"},Pt={class:"tooltip-header"},Ut={class:"tooltip-title"},Ft={class:"tooltip-code"},Ht={class:"tooltip-content"},Gt={class:"tooltip-section"},Xt={class:"status-info"},Yt={class:"efficiency-text"},Jt={key:0,class:"tooltip-section"},Qt={class:"department-name"},Zt={class:"tooltip-section"},Kt={class:"asset-info"},ea=e({__name:"FactoryLayoutDashboard",setup(e){const o={frameCount:0,lastTime:performance.now(),fps:0,raf:null,lastDegradation:0,updateCounter:0,backgroundLoadCount:0,workstationRenderCount:0,componentRerenderCount:0,start(){this.frameCount=0,this.lastTime=performance.now(),this.raf=requestAnimationFrame(this.tick.bind(this)),this.monitorBackgroundLoading()},tick(){this.frameCount++,this.updateCounter++;const e=performance.now(),t=e-this.lastTime;t>=2e3&&(this.fps=Math.round(1e3*this.frameCount/t),this.frameCount=0,this.lastTime=e,this.updateCounter%4==0&&this.updateUI(),this.updateCounter%8==0&&this.fps<50&&this.handlePerformanceDegradation()),this.raf=requestAnimationFrame(this.tick.bind(this))},updateUI(){const e=document.getElementById("fps-counter");e&&(e.textContent=`FPS: ${this.fps}`,e.className="fps-counter "+(this.fps>55?"fps-good":this.fps>45?"fps-medium":"fps-poor")),this.updateDebugInfo()},updateDebugInfo(){const e=document.getElementById("bg-load-count"),t=document.getElementById("workstation-render-count"),a=document.getElementById("component-rerender-count");e&&(e.textContent=this.backgroundLoadCount),t&&(t.textContent=this.workstationRenderCount),a&&(a.textContent=this.componentRerenderCount)},monitorBackgroundLoading(){const e=new MutationObserver((e=>{e.forEach((e=>{"attributes"!==e.type||"style"!==e.attributeName&&"class"!==e.attributeName||this.backgroundLoadCount++}))}));[".modern-factory-dashboard",".factory-layout",".factory-floor",".cell-background"].forEach((t=>{document.querySelectorAll(t).forEach((t=>{e.observe(t,{attributes:!0,attributeFilter:["style","class"]})}))}))},incrementBackgroundLoad(){this.backgroundLoadCount++},incrementWorkstationRender(){this.workstationRenderCount++},incrementComponentRerender(){this.componentRerenderCount++},handlePerformanceDegradation(){performance.now()-this.lastDegradation>1e4&&(document.documentElement.classList.add("low-fps-mode"),this.lastDegradation=performance.now())},stop(){this.raf&&(cancelAnimationFrame(this.raf),this.raf=null)}},n=(e,t)=>{let a;const l=(...l)=>{clearTimeout(a),a=setTimeout((()=>e.apply(this,l)),t)};return l.cancel=()=>clearTimeout(a),l};let s=0;const i=()=>{s++,s%5==1&&V((()=>{const e=document.getElementById("bg-load-count"),t=document.getElementById("workstation-render-count"),a=document.getElementById("component-rerender-count");e&&(e.textContent=ge.backgroundLoadCount),t&&(t.textContent=ge.workstationRenderCount),a&&(a.textContent=ge.componentRerenderCount)}))},b=$([]),x=t(!1),C=t(!1),k=t("custom"),z=t("factory-layout.json"),_=t(null),Y=t(new Date),J=t(""),Q=t(null);t(null);const Z=t("layout"),K=t(!1),ee=t(1),te=t(null),ae=t(null),le=t(!1),oe=t(["operational","warning","error","idle"]),ne=t(""),se=t([0,100]),ie=t([]),re=t(!1),ce=t(0),ue=t(0),de=t(null),ve=t(!1),me=t(null),fe=t(!1),pe=t(new Set),he=t(null),ge={backgroundLoadCount:0,workstationRenderCount:0,componentRerenderCount:0,lastWorkstationRender:null},ye=t("operational"),we=a((()=>({operational:"正常运行",warning:"警告状态",offline:"离线状态"}[ye.value]||"未知状态"))),be={operational:{icon:G,label:"运行正常"},warning:{icon:H,label:"警告状态"},error:{icon:F,label:"故障状态"},idle:{icon:l,label:"空闲工位"}},xe=async()=>{try{x.value=!0,me.value=null;const t="/analyresport/factory-layout.json",a=await fetch(t);if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const l=await a.text();let o;try{o=JSON.parse(l)}catch(e){throw new Error(`JSON解析失败: ${e.message}`)}if(o.factoryLayout)_.value=o.factoryLayout;else{if(!o.zones||!Array.isArray(o.zones))throw new Error("未识别的配置文件格式");_.value=Ce(o)}return ve.value=!0,_.value}catch(t){return me.value=t.message,M.error(`加载布局配置失败: ${t.message}，使用默认配置`),_.value=ke(),ve.value=!0,_.value}finally{x.value=!1}},Ce=e=>{const t=e.zones.map((e=>({...e,position:{x:e.x,y:e.y,width:e.width,height:e.height},layout:{rows:e.rows,cols:e.cols}}))),a=ea(t);let l=0,o=0;e.zones.forEach((e=>{const t=e.x+e.width,a=e.y+e.height;l=Math.max(l,t),o=Math.max(o,a)}));const n=l+50,s=o+50,i={name:"自定义工厂布局",description:`包含${e.zones.length}个区域的工厂布局`,version:e.version||"1.0",canvas:{width:n,height:s,backgroundColor:"#0f172a",gridSize:20},zones:[],statusDistribution:{operational:.7,warning:.15,error:.1,idle:.05},defaultMetrics:{efficiency:{min:70,max:100},uptime:{min:80,max:100},assetCount:{min:2,max:7},taskCount:{min:1,max:9}}};return a.forEach((e=>{const t=aa(e),a=ta(e),l=t.rows*t.cols,o=[],n=a.width,s=a.height,r=e.gapX||2,c=e.gapY||2,u=(n-(t.cols-1)*r)/t.cols,d=(s-(t.rows-1)*c)/t.rows;for(let i=0;i<t.rows;i++)for(let a=0;a<t.cols;a++){const l=e.startWorkstation+i*t.cols+a,n=a*(u+r),s=i*(d+c);o.push({id:l,x:n,y:s,width:u,height:d,name:`${e.name}-工位${l.toString().padStart(3,"0")}`})}const v=a.x,m=a.y,f=e.adjustedWidth||a.width,p=e.adjustedHeight||a.height;i.zones.push({id:`zone${e.id}`,name:e.name,uniqueName:`${e.name}-${e.id}`,description:`${e.name}生产区域`,color:e.color,position:{x:v,y:m,width:f,height:p},layout:{type:"grid",rows:t.rows,cols:t.cols},workstations:{startId:e.startWorkstation,count:l,pattern:"grid",positions:o},adjustedWidth:e.adjustedWidth,adjustedHeight:e.adjustedHeight})})),i},ke=()=>({name:"默认工厂布局",description:"包含1个区域的默认工厂布局",version:"1.0",canvas:{width:1e3,height:600,backgroundColor:"#0f172a",gridSize:20},zones:[{id:"zone1",name:"默认区域",uniqueName:"默认区域-1",description:"默认区域生产区域",color:"#4A90E2",position:{x:100,y:100,width:300,height:270},layout:{type:"grid",rows:3,cols:3},workstations:{startId:1,count:9,pattern:"grid",positions:Array.from({length:9},((e,t)=>({id:t+1,x:t%3*100,y:90*Math.floor(t/3),name:`默认区域-工位${(t+1).toString().padStart(3,"0")}`})))}}],statusDistribution:{operational:.7,warning:.15,error:.1,idle:.05},defaultMetrics:{efficiency:{min:70,max:100},uptime:{min:80,max:100},assetCount:{min:2,max:7},taskCount:{min:1,max:9}}}),ze=()=>{if(!_.value)return((e="unknown",t=1)=>{ge.workstationRenderCount+=t,ge.lastWorkstationRender=(new Date).toLocaleTimeString(),ge.workstationRenderCount,i()})("配置为空，返回空数组",0),[];const e=[],t=["operational","warning","error","idle"],a=_.value.statusDistribution,l=[a.operational,a.warning,a.error,a.idle];return _.value.zones.forEach((a=>{var o;const n=aa(a),{rows:s,cols:i}=n,r=(null==(o=a.workstations)?void 0:o.startId)||a.startWorkstation||1;for(let c=0;c<s;c++)for(let o=0;o<i;o++){const n=r+c*i+o,u=Math.random();let d="operational",v=0;for(let e=0;e<l.length;e++)if(v+=l[e],u<v){d=t[e];break}const m=_.value.defaultMetrics||{efficiency:{min:70,max:100},uptime:{min:80,max:100},assetCount:{min:2,max:7},taskCount:{min:1,max:9}},f=`${a.name}-${a.id}`;e.push({locationId:n,locationName:`${a.name}-工位${n.toString().padStart(3,"0")}`,locationCode:`WS${n.toString().padStart(3,"0")}`,departmentName:f,originalDepartmentName:a.name,status:d,efficiency:Math.floor(Math.random()*(m.efficiency.max-m.efficiency.min+1))+m.efficiency.min,uptime:Math.floor(Math.random()*(m.uptime.max-m.uptime.min+1))+m.uptime.min,assetCount:Math.floor(Math.random()*(m.assetCount.max-m.assetCount.min+1))+m.assetCount.min,taskCount:Math.floor(Math.random()*(m.taskCount.max-m.taskCount.min+1))+m.taskCount.min,faultCount:"error"===d?Math.floor(3*Math.random())+1:"warning"===d?Math.floor(2*Math.random()):0,lastUpdate:new Date,zoneColor:a.color,zoneId:a.id,isHighlighted:!1,relativeRow:c,relativeCol:o,gridPosition:`${c+1}-${o+1}`,totalCols:i,totalRows:s})}})),_.value.zones.forEach((t=>{var a;const l=aa(t),o=(null==(a=t.workstations)?void 0:a.startId)||t.startWorkstation||1,n=`${t.name}-${t.id}`,s=e.filter((e=>e.departmentName===n)),i=(s.map((e=>e.locationId)).sort(((e,t)=>e-t)),l.rows,l.cols,{}),r={};s.forEach((e=>{i[e.relativeRow]=(i[e.relativeRow]||0)+1,r[e.relativeCol]=(r[e.relativeCol]||0)+1}))})),e};let _e=new Map,Me=0,$e=0;const Ne=a((()=>{var e,t;const a=b.value.length,l=(null==(e=_.value)?void 0:e.version)||0;if(a===Me&&l===$e&&_e.size>0)return _e;const o=new Map;return(null==(t=_.value)?void 0:t.zones)?(_.value.zones.forEach((e=>{const t=`${e.name}-${e.id}`,a=b.value.filter((e=>e.departmentName===t)).sort(((e,t)=>e.relativeRow!==t.relativeRow?e.relativeRow-t.relativeRow:e.relativeCol-t.relativeCol));o.set(t,a)})),_e=o,Me=a,$e=l,o):(_e=o,o)})),Ie=e=>{const t=`${e.name}-${e.id}`;return Ne.value.get(t)||[]},Ee=e=>[`status-${e.status}`,{highlighted:e.isHighlighted}],ea=e=>{if(!e||0===e.length)return e;return e.map((e=>{const t=ta(e);aa(e);return{...e,adjustedWidth:t.width,adjustedHeight:t.height,newX:t.x,newY:t.y,originalX:t.x,originalY:t.y}}))},ta=e=>e.position?{x:e.position.x,y:e.position.y,width:e.position.width,height:e.position.height}:{x:e.x||0,y:e.y||0,width:e.width||100,height:e.height||100},aa=e=>e.layout?{rows:e.layout.rows||1,cols:e.layout.cols||1}:{rows:e.rows||1,cols:e.cols||1},la=e=>{const t=ta(e);return{position:"absolute",left:`${t.x}px`,top:`${t.y}px`,width:`${t.width}px`,height:`${t.height}px`,border:`2px dashed ${e.color}`,borderRadius:"8px",backgroundColor:`${e.color}15`}},oa=e=>{const t=aa(e);if(!t||1===t.rows&&1===t.cols)return{position:"relative",height:"100%",width:"100%",padding:"0",boxSizing:"border-box"};const{rows:a,cols:l}=t,o=e.gapX||2,n=e.gapY||2,s=ta(e),i=(s.width-(l-1)*o)/l,r=(s.height-(a-1)*n)/a,c=Array(l).fill(`${i}px`).join(" ");return{display:"grid",gridTemplateRows:Array(a).fill(`${r}px`).join(" "),gridTemplateColumns:c,gap:`${n}px ${o}px`,height:"100%",width:"100%",padding:"0",boxSizing:"border-box",justifyContent:"stretch",alignContent:"stretch",overflow:"hidden"}};let na=[],sa={term:"",statuses:[],minEff:0,maxEff:100,locationsLength:0};const ia=a((()=>{const e=ne.value.toLowerCase(),t=oe.value,[a,l]=se.value,o=b.value,n={term:e,statuses:JSON.stringify(t),minEff:a,maxEff:l,locationsLength:o.length};if(!(JSON.stringify(n)!==JSON.stringify(sa))&&na.length>0)return na;const s=o.filter((o=>!(t.length<4&&!t.includes(o.status))&&(!(o.efficiency<a||o.efficiency>l)&&!(e&&!o.locationName.toLowerCase().includes(e)&&!o.locationCode.toLowerCase().includes(e)&&!o.locationId.toString().includes(e)))));return na=s,sa=n,s})),ra=a((()=>oe.value.length<4||ne.value.length>0||se.value[0]>0||se.value[1]<100)),ca=a((()=>{let e=0;return oe.value.length<4&&e++,ne.value.length>0&&e++,(se.value[0]>0||se.value[1]<100)&&e++,e}));a((()=>b.value.filter((e=>"error"===e.status||"warning"===e.status||e.efficiency<70)).sort(((e,t)=>"error"===e.status&&"error"!==t.status?-1:"error"===t.status&&"error"!==e.status?1:"warning"===e.status&&"warning"!==t.status?-1:"warning"===t.status&&"warning"!==e.status?1:e.efficiency-t.efficiency))));const ua=a((()=>{var e;const t={};return(null==(e=_.value)?void 0:e.zones)&&_.value.zones.forEach((e=>{const a=b.value.filter((t=>t.zoneId===e.id||t.zoneName===e.name));t[e.id]={total:a.length,operational:a.filter((e=>"operational"===e.status)).length,warning:a.filter((e=>"warning"===e.status)).length,error:a.filter((e=>"error"===e.status)).length,idle:a.filter((e=>"idle"===e.status)).length,efficiency:a.length>0?Math.round(a.reduce(((e,t)=>e+t.efficiency),0)/a.length):0}})),t})),da=a((()=>{if(0===b.value.length)return 0;const e=b.value.reduce(((e,t)=>e+t.efficiency),0);return Math.round(e/b.value.length)})),va=a((()=>b.value.reduce(((e,t)=>e+(t.assetCount||0)),0))),ma=a((()=>{const e=b.value.length,t=b.value.filter((e=>"operational"===e.status)).length;return{total:e,operational:t,warning:b.value.filter((e=>"warning"===e.status)).length,error:b.value.filter((e=>"error"===e.status)).length,idle:b.value.filter((e=>"idle"===e.status)).length,operationalPercent:e>0?Math.round(t/e*100):0}}));a((()=>b.value.length)),a((()=>{if(0===b.value.length)return 0;return b.value.reduce(((e,t)=>e+t.efficiency),0)/b.value.length}));const fa=a((()=>Y.value.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",second:"2-digit"}))),pa=a((()=>{if(_.value&&_.value.canvas){const{width:e,height:t}=_.value.canvas;return`0 0 ${Math.max(e,1200)} ${Math.max(t,600)}`}return"0 0 1200 600"})),ha=a((()=>{if(!_.value||!_.value.canvas)return{};let{width:e,height:t}=_.value.canvas;if(_.value.zones){let a=0,l=0;_.value.zones.forEach((e=>{const t=ta(e),o=t.x+t.width,n=t.y+t.height;a=Math.max(a,o),l=Math.max(l,n)}));const o=50;e=Math.max(e,a+o),t=Math.max(t,l+o)}const a=1200/e,l=600/t;return{width:`${e}px`,height:`${t}px`,transform:`scale(${Math.min(a,l,.9)*ee.value})`,transformOrigin:"center center",transition:"transform 0.3s ease"}})),ga=e=>ia.value.includes(e),ya=e=>({operational:"正常运行",warning:"警告状态",error:"故障状态",idle:"空闲状态"}[e]||"未知状态"),wa=e=>{const t=ma.value.total;return t>0?ma.value[e]/t*100:0},ba=e=>({zone1:"区域1",zone2:"区域2",zone3:"区域3",zone4:"区域4",zone5:"区域5",zone6:"区域6",zone7:"区域7"}[e]||"未知区域"),xa=e=>{if(!e)return void(ie.value=[]);const t=e.toLowerCase();ie.value=b.value.filter((e=>e.locationName.toLowerCase().includes(t)||e.locationCode.toLowerCase().includes(t)||e.locationId.toString().includes(t))).slice(0,10)},Ca=e=>{Z.value=e},ka=()=>{},za=()=>{oe.value=["operational","warning","error","idle"],ne.value="",se.value=[0,100]},_a=n(((e,t)=>{((e,t)=>{de.value=e,ce.value=t.clientX+10,ue.value=t.clientY-50,re.value=!0})(e,t)}),150),Ma=()=>{var e;null==(e=_a.cancel)||e.call(_a),re.value=!1,de.value=null},$a=e=>{te.value=e;const t=b.value.find((t=>t.locationId===e));t&&(ae.value={...t,locationPath:`${t.originalDepartmentName||t.departmentName} / ${t.locationName}`,effectiveDepartmentName:t.originalDepartmentmentName||t.departmentName,directDepartmentName:t.departmentName,details:{temperature:Math.round(20*Math.random()+20)+"°C",pressure:Math.round(10*Math.random()+95)+"kPa",uptime:Math.round(20*Math.random()+80)+"%",lastMaintenance:"2天前",errorCode:"error"===t.status?"E001":null,warningMessage:"warning"===t.status?"温度偏高，请注意监控":null},assets:[{assetId:1,assetCode:"NC001",assetName:"数控机床",assetTypeName:"加工设备"},{assetId:2,assetCode:"QI002",assetName:"质检仪",assetTypeName:"检测设备"}]},le.value=!0)},Na=e=>{$a(e.locationId)},Ia=()=>{ae.value=null,te.value=null,le.value=!1},Ea=n((async()=>{C.value=!0;try{await xe(),b.value=ze(),Y.value=new Date,M.success("数据已刷新")}catch(e){M.error("刷新失败")}finally{C.value=!1}}),500),Da=()=>{Ea()},Ra=()=>{var e,t,a;K.value=!K.value,K.value?null==(t=(e=document.documentElement).requestFullscreen)||t.call(e):null==(a=document.exitFullscreen)||a.call(document)},Sa=async()=>{try{if(ve.value)return;if(x.value=!0,me.value=null,"import"===k.value&&await(async()=>{try{const e=await fetch(`/analyresport/${z.value}`);if(e.ok){const t=await e.json();return J.value=z.value,Q.value=t,M.success(`自动加载布局文件: ${z.value}`),!0}}catch(e){}return!1})(),await xe(),!_.value)throw new Error("布局配置加载失败");b.value=ze(),ve.value=!0,window.factoryDashboardTimer||(window.factoryDashboardTimer=setInterval((()=>{Y.value=new Date}),5e3))}catch(e){me.value=e.message,M.error("初始化失败，请刷新页面重试")}finally{x.value=!1}};a((()=>{var e;const t=new Map;return(null==(e=_.value)?void 0:e.zones)?(_.value.zones.forEach((e=>{const a=Ne.value.get(e.uniqueName)||[],l=a.length;let o=0;if(l>0){const e=a.reduce(((e,t)=>e+(t.efficiency||0)),0);o=Math.round(e/l)}t.set(e.uniqueName,{count:l,efficiency:o})})),t):t}));return N((async()=>{o.start(),he.value&&he.value.disconnect(),he.value=new IntersectionObserver((e=>{e.forEach((e=>{const t=e.target.dataset.workstationId;e.isIntersecting?pe.value.add(t):pe.value.delete(t)}))}),{rootMargin:"50px",threshold:.1}),ve.value||(_.value=ke(),b.value=ze()),await Sa()})),I((()=>{o.stop(),he.value&&(he.value.disconnect(),he.value=null),window.factoryDashboardTimer&&(clearInterval(window.factoryDashboardTimer),window.factoryDashboardTimer=null)})),(e,t)=>{var a,l,o,n,s,i,b,k;const z=r("el-icon"),M=r("el-input"),$=r("el-badge"),N=r("el-button"),I=r("el-checkbox"),V=r("el-checkbox-group"),G=r("el-slider"),Y=r("el-popover"),J=r("el-button-group"),Q=r("el-progress"),ee=r("el-table-column"),pe=r("el-tag"),he=r("el-table"),ge=r("el-drawer");return u(),m("div",Re,[v("header",Se,[v("div",Le,[v("div",Te,[v("div",je,[p(z,{size:"32"},{default:d((()=>[p(w(W))])),_:1}),t[6]||(t[6]=v("div",{class:"icon-glow"},null,-1))]),v("div",Ve,[t[7]||(t[7]=v("h1",{class:"brand-title"},"智能制造监控系统",-1)),v("p",We,"实时工厂状态监控 • "+y(ma.value.total)+"个工位",1)]),v("div",{class:D(["status-badge",ye.value])},[t[8]||(t[8]=v("div",{class:"status-indicator"},null,-1)),v("span",null,"系统"+y(we.value),1)],2)]),v("div",Ae,[v("div",Be,[p(M,{modelValue:ne.value,"onUpdate:modelValue":t[0]||(t[0]=e=>ne.value=e),placeholder:"搜索工位编号或设备名称...",class:"smart-search",clearable:"",onInput:xa},{prefix:d((()=>[p(z,{class:"search-icon"},{default:d((()=>[p(w(A))])),_:1})])),_:1},8,["modelValue"]),ie.value.length>0&&ne.value?(u(),m("div",qe,[(u(!0),m(R,null,S(ie.value.slice(0,5),(e=>(u(),m("div",{key:e.locationId,class:"search-result-item",onClick:t=>(e=>{ne.value="",ie.value=[],$a(e.locationId)})(e)},[v("div",{class:D(["result-status",e.status])},null,2),v("div",Pe,[v("span",Ue,y(e.locationName),1),v("span",Fe,y(e.efficiency)+"% 效率",1)])],8,Oe)))),128))])):f("",!0)]),v("div",He,[p(Y,{placement:"bottom",trigger:"click",width:"350","popper-class":"filter-popover"},{reference:d((()=>[p(N,{class:D(["action-btn filter-btn",{active:ra.value}])},{default:d((()=>[p(z,null,{default:d((()=>[p(w(B))])),_:1}),t[9]||(t[9]=v("span",null,"筛选",-1)),ca.value>0?(u(),c($,{key:0,value:ca.value,class:"filter-badge"},null,8,["value"])):f("",!0)])),_:1},8,["class"])])),default:d((()=>[v("div",Ge,[v("div",Xe,[t[14]||(t[14]=v("label",null,"状态筛选",-1)),p(V,{modelValue:oe.value,"onUpdate:modelValue":t[1]||(t[1]=e=>oe.value=e),onChange:ka},{default:d((()=>[p(I,{label:"operational"},{default:d((()=>t[10]||(t[10]=[h("正常运行")]))),_:1}),p(I,{label:"warning"},{default:d((()=>t[11]||(t[11]=[h("警告状态")]))),_:1}),p(I,{label:"error"},{default:d((()=>t[12]||(t[12]=[h("故障状态")]))),_:1}),p(I,{label:"idle"},{default:d((()=>t[13]||(t[13]=[h("空闲工位")]))),_:1})])),_:1},8,["modelValue"])]),v("div",Ye,[t[15]||(t[15]=v("label",null,"效率范围",-1)),p(G,{modelValue:se.value,"onUpdate:modelValue":t[2]||(t[2]=e=>se.value=e),range:"",min:0,max:100,step:5,onChange:ka},null,8,["modelValue"]),v("div",Je,y(se.value[0])+"% - "+y(se.value[1])+"%",1)]),v("div",Qe,[p(N,{size:"small",onClick:za},{default:d((()=>t[16]||(t[16]=[h("重置")]))),_:1}),p(N,{size:"small",type:"primary",onClick:ka},{default:d((()=>t[17]||(t[17]=[h("应用")]))),_:1})])])])),_:1}),p(J,{class:"view-toggle"},{default:d((()=>[p(N,{class:D([{active:"layout"===Z.value},"view-btn"]),onClick:t[3]||(t[3]=e=>Ca("layout")),title:"布局视图"},{default:d((()=>[p(z,null,{default:d((()=>[p(w(q))])),_:1})])),_:1},8,["class"]),p(N,{class:D([{active:"list"===Z.value},"view-btn"]),onClick:t[4]||(t[4]=e=>Ca("list")),title:"列表视图"},{default:d((()=>[p(z,null,{default:d((()=>[p(w(O))])),_:1})])),_:1},8,["class"])])),_:1}),p(N,{onClick:Da,loading:C.value,class:D(["action-btn refresh-btn",{refreshing:C.value}]),title:"刷新数据"},{default:d((()=>[p(z,{class:"refresh-icon"},{default:d((()=>[p(w(P))])),_:1})])),_:1},8,["loading","class"]),p(N,{onClick:Ra,class:D(["action-btn fullscreen-btn",{active:K.value}]),title:K.value?"退出全屏":"全屏"},{default:d((()=>[p(z,null,{default:d((()=>[K.value?(u(),c(w(F),{key:1})):(u(),c(w(U),{key:0}))])),_:1})])),_:1},8,["class","title"])])])])]),v("main",Ze,[x.value&&!ve.value?(u(),m("div",Ke,[p(z,{class:"loading-icon",size:"48"},{default:d((()=>[p(w(W))])),_:1}),t[18]||(t[18]=v("h3",null,"正在加载工厂布局...",-1)),t[19]||(t[19]=v("p",null,"请稍候，系统正在初始化数据",-1)),p(Q,{percentage:50,"show-text":!1,"stroke-width":4,color:"#3d8fd1",style:{width:"200px","margin-top":"20px"}})])):me.value?(u(),m("div",et,[p(z,{class:"error-icon",size:"48"},{default:d((()=>[p(w(H))])),_:1}),t[21]||(t[21]=v("h3",null,"加载失败",-1)),v("p",null,y(me.value),1),p(N,{type:"primary",onClick:Sa},{default:d((()=>t[20]||(t[20]=[h("重新加载")]))),_:1})])):f("",!0),p(L,{name:"fade-in",appear:""},{default:d((()=>{var e;return[E(v("div",tt,[v("div",at,[v("div",lt,[v("div",ot,[t[23]||(t[23]=v("h3",{class:"card-title"},"实时概览",-1)),v("div",nt,[t[22]||(t[22]=v("div",{class:"pulse-dot"},null,-1)),v("span",st,y(fa.value),1)])]),v("div",it,[(u(),m(R,null,S(be,((e,t)=>v("div",{key:t,class:D(["modern-stat-card",t]),onClick:e=>(e=>{oe.value=[e],ka()})(t)},[v("div",ct,[v("div",{class:D(["stat-icon-container",t])},[p(z,{class:"stat-icon"},{default:d((()=>[(u(),c(g(e.icon)))])),_:2},1024),v("div",{class:D(["icon-glow",t])},null,2)],2),v("div",ut,[v("div",{class:D(["progress-bar",t]),style:j({width:wa(t)+"%"})},null,6)])]),v("div",dt,[v("div",vt,y(ma.value[t]),1),v("div",mt,y(e.label),1),"operational"===t?(u(),m("div",ft,y(ma.value.operationalPercent)+"% ",1)):f("",!0)])],10,rt))),64))]),v("div",pt,[v("div",ht,[v("div",gt,y(da.value)+"%",1),t[24]||(t[24]=v("div",{class:"metric-label"},"平均效率",-1))]),v("div",yt,[v("div",wt,y(va.value),1),t[25]||(t[25]=v("div",{class:"metric-label"},"总设备数",-1))])])]),v("div",bt,[t[26]||(t[26]=v("div",{class:"card-header"},[v("h3",{class:"card-title"},"区域统计")],-1)),v("div",xt,[(u(!0),m(R,null,S(ua.value,((e,t)=>(u(),m("div",{key:t,class:"zone-item"},[v("div",Ct,[v("div",kt,y(ba(t)),1),v("div",zt,[v("span",_t,y(e.total)+"个工位",1),v("span",Mt,y(e.efficiency)+"%效率",1)])]),v("div",$t,[v("div",Nt,[e.operational>0?(u(),m("span",It,y(e.operational),1)):f("",!0),e.warning>0?(u(),m("span",Et,y(e.warning),1)):f("",!0),e.error>0?(u(),m("span",Dt,y(e.error),1)):f("",!0)])])])))),128))])])]),v("div",Rt,["layout"===Z.value?(u(),m("div",{key:0,class:D(["factory-floor",{fullscreen:K.value}])},[(u(),m("svg",{class:"factory-grid",viewBox:pa.value},t[27]||(t[27]=[v("defs",null,[v("pattern",{id:"grid",width:"20",height:"20",patternUnits:"userSpaceOnUse"},[v("path",{d:"M 20 0 L 0 0 0 20",fill:"none",stroke:"rgba(107, 148, 214, 0.08)","stroke-width":"1"})]),v("linearGradient",{id:"operationalGradient",x1:"0%",y1:"0%",x2:"100%",y2:"100%"},[v("stop",{offset:"0%",style:{"stop-color":"#22c55e","stop-opacity":"1"}}),v("stop",{offset:"100%",style:{"stop-color":"#16a34a","stop-opacity":"1"}})]),v("linearGradient",{id:"warningGradient",x1:"0%",y1:"0%",x2:"100%",y2:"100%"},[v("stop",{offset:"0%",style:{"stop-color":"#eab308","stop-opacity":"1"}}),v("stop",{offset:"100%",style:{"stop-color":"#ca8a04","stop-opacity":"1"}})]),v("linearGradient",{id:"errorGradient",x1:"0%",y1:"0%",x2:"100%",y2:"100%"},[v("stop",{offset:"0%",style:{"stop-color":"#ef4444","stop-opacity":"1"}}),v("stop",{offset:"100%",style:{"stop-color":"#dc2626","stop-opacity":"1"}})])],-1),v("rect",{width:"100%",height:"100%",fill:"url(#grid)"},null,-1)]),8,St)),v("div",{class:"zone-containers",style:j(ha.value)},[(u(!0),m(R,null,S((null==(e=_.value)?void 0:e.zones)||[],(e=>(u(),m("div",{key:e.id,class:D(["zone-container modern-zone",`zone-${e.id}`]),"data-zone":e.name,style:j(la(e))},[v("div",Tt,[v("div",jt,y(e.name),1),fe.value?(u(),m("div",Vt,y(aa(e).rows)+"x"+y(aa(e).cols)+" 位置:("+y(ta(e).x)+","+y(ta(e).y)+") 尺寸:"+y(ta(e).width)+"x"+y(ta(e).height)+" 间距:"+y(e.gapX||2)+"x"+y(e.gapY||2),1)):f("",!0)]),v("div",{class:"zone-workstations modern-grid",style:j(oa(e))},[(u(!0),m(R,null,S(Ie(e),(e=>E((u(),m("div",{key:e.locationId,class:D(["workstation-cell modern-workstation",Ee(e)]),style:j({width:"100%",height:"100%",minWidth:"24px",minHeight:"24px",aspectRatio:"1",display:"flex",alignItems:"center",justifyContent:"center",overflow:"hidden",gridArea:"auto",boxSizing:"border-box"}),onClick:t=>(e=>{$a(e.locationId)})(e),onMouseenter:t=>((e,t)=>{_a(e,t)})(e,t),onMouseleave:Ma},[t[28]||(t[28]=v("div",{class:"cell-content"},null,-1)),te.value===e.locationId?(u(),m("div",At)):f("",!0)],46,Wt)),[[T,ga(e)]]))),128))],4)],14,Lt)))),128))],4)],2)):(u(),m("div",Bt,[p(he,{data:ia.value,height:"100%",onRowClick:Na,"highlight-current-row":""},{default:d((()=>[p(ee,{prop:"locationCode",label:"工位编号",width:"100"}),p(ee,{prop:"locationName",label:"工位名称","min-width":"120"}),p(ee,{prop:"departmentName",label:"所属部门","min-width":"120"}),p(ee,{prop:"efficiency",label:"效率",width:"80",align:"center"},{default:d((({row:e})=>{return[v("span",{class:D((t=e.efficiency,t>=80?"efficiency-high":t>=60?"efficiency-medium":"efficiency-low"))},y(e.efficiency)+"%",3)];var t})),_:1}),p(ee,{prop:"status",label:"状态",width:"100",align:"center"},{default:d((({row:e})=>{return[p(pe,{type:(t=e.status,{operational:"success",warning:"warning",error:"danger",idle:"info"}[t]||"info"),size:"small"},{default:d((()=>[h(y(ya(e.status)),1)])),_:2},1032,["type"])];var t})),_:1}),p(ee,{prop:"assetCount",label:"设备数",width:"80",align:"center"}),p(ee,{prop:"taskCount",label:"任务数",width:"80",align:"center"}),p(ee,{prop:"faultCount",label:"故障数",width:"80",align:"center"},{default:d((({row:e})=>[e.faultCount>0?(u(),m("span",qt,y(e.faultCount),1)):(u(),m("span",Ot,"0"))])),_:1}),p(ee,{label:"操作",width:"100",align:"center"},{default:d((({row:e})=>[p(N,{size:"small",onClick:X((t=>$a(e.locationId)),["stop"])},{default:d((()=>t[29]||(t[29]=[h(" 详情 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])]))])],512),[[T,ve.value]])]})),_:1})]),E(v("div",{class:"location-tooltip",style:j({left:ce.value+"px",top:ue.value+"px"})},[v("div",Pt,[v("div",Ut,y(null==(a=de.value)?void 0:a.locationName),1),v("div",Ft,y(null==(l=de.value)?void 0:l.locationCode),1)]),v("div",Ht,[v("div",Gt,[t[30]||(t[30]=v("div",{class:"section-title"},"状态信息",-1)),v("div",Xt,[v("span",{class:D(["status-badge",null==(o=de.value)?void 0:o.status])},y(ya(null==(n=de.value)?void 0:n.status)),3),v("span",Yt,"效率: "+y(null==(s=de.value)?void 0:s.efficiency)+"%",1)])]),(null==(i=de.value)?void 0:i.departmentName)?(u(),m("div",Jt,[t[31]||(t[31]=v("div",{class:"section-title"},"所属区域",-1)),v("div",Qt,y(de.value.originalDepartmentName||de.value.departmentName),1)])):f("",!0),v("div",Zt,[t[32]||(t[32]=v("div",{class:"section-title"},"设备信息",-1)),v("div",Kt,[v("span",null,"设备数量: "+y((null==(b=de.value)?void 0:b.assetCount)||0),1),v("span",null,"任务数量: "+y((null==(k=de.value)?void 0:k.taskCount)||0),1)])])])],4),[[T,re.value]]),p(ge,{modelValue:le.value,"onUpdate:modelValue":t[5]||(t[5]=e=>le.value=e),title:"工位详情",size:"450px",direction:"rtl"},{default:d((()=>[ae.value?(u(),c(De,{key:0,location:ae.value,onClose:Ia},null,8,["location"])):f("",!0)])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-45d3738e"]]);export{ea as default};
