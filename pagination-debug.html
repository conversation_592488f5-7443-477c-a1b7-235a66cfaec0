<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分页异常调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 分页异常调试工具</h1>
        <p>检查为什么第2页显示200条记录而不是剩余的42条记录</p>

        <div class="test-section">
            <h3>1. 基础分页测试</h3>
            <button class="btn" onclick="testBasicPagination()">测试基础分页</button>
            <div id="basicResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. 大页面分页测试 (200条/页)</h3>
            <button class="btn" onclick="testLargePagination()">测试大页面分页</button>
            <div id="largeResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. 边界条件测试</h3>
            <button class="btn" onclick="testBoundaryConditions()">测试边界条件</button>
            <div id="boundaryResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. 前端API vs 直接API对比</h3>
            <button class="btn" onclick="compareFrontendVsDirect()">对比测试</button>
            <div id="compareResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>5. 分页数据分析</h3>
            <div id="analysisTable"></div>
        </div>
    </div>

    <script>
        // 测试基础分页
        async function testBasicPagination() {
            const resultDiv = document.getElementById('basicResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '正在测试基础分页...';

            try {
                const tests = [
                    { pageIndex: 1, pageSize: 5 },
                    { pageIndex: 2, pageSize: 5 },
                    { pageIndex: 3, pageSize: 5 }
                ];

                let results = '基础分页测试结果:\n\n';
                
                for (const test of tests) {
                    const url = `http://localhost:5001/api/Asset?pageIndex=${test.pageIndex}&pageSize=${test.pageSize}`;
                    const response = await fetch(url);
                    const data = await response.json();
                    
                    if (data.success && data.data) {
                        results += `第${test.pageIndex}页 (每页${test.pageSize}条):\n`;
                        results += `  实际返回: ${data.data.items.length}条\n`;
                        results += `  总记录数: ${data.data.total}\n`;
                        results += `  总页数: ${data.data.totalPages}\n`;
                        if (data.data.items.length > 0) {
                            results += `  首条ID: ${data.data.items[0].id}\n`;
                            results += `  末条ID: ${data.data.items[data.data.items.length - 1].id}\n`;
                        }
                        results += '\n';
                    } else {
                        results += `第${test.pageIndex}页测试失败: ${data.message}\n\n`;
                    }
                }

                resultDiv.textContent = results;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `测试失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 测试大页面分页
        async function testLargePagination() {
            const resultDiv = document.getElementById('largeResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '正在测试大页面分页...';

            try {
                const tests = [
                    { pageIndex: 1, pageSize: 200 },
                    { pageIndex: 2, pageSize: 200 }
                ];

                let results = '大页面分页测试结果:\n\n';
                
                for (const test of tests) {
                    const url = `http://localhost:5001/api/Asset?pageIndex=${test.pageIndex}&pageSize=${test.pageSize}`;
                    const response = await fetch(url);
                    const data = await response.json();
                    
                    if (data.success && data.data) {
                        results += `第${test.pageIndex}页 (每页${test.pageSize}条):\n`;
                        results += `  实际返回: ${data.data.items.length}条\n`;
                        results += `  期望返回: ${test.pageIndex === 1 ? Math.min(test.pageSize, data.data.total) : Math.max(0, data.data.total - (test.pageIndex - 1) * test.pageSize)}条\n`;
                        results += `  总记录数: ${data.data.total}\n`;
                        results += `  总页数: ${data.data.totalPages}\n`;
                        
                        // 检查是否异常
                        const expectedCount = test.pageIndex === 1 ? 
                            Math.min(test.pageSize, data.data.total) : 
                            Math.max(0, data.data.total - (test.pageIndex - 1) * test.pageSize);
                        
                        if (data.data.items.length !== expectedCount) {
                            results += `  ⚠️ 异常: 期望${expectedCount}条，实际${data.data.items.length}条\n`;
                        } else {
                            results += `  ✅ 正常: 数据量符合预期\n`;
                        }
                        
                        if (data.data.items.length > 0) {
                            results += `  首条ID: ${data.data.items[0].id}\n`;
                            results += `  末条ID: ${data.data.items[data.data.items.length - 1].id}\n`;
                        }
                        results += '\n';
                    } else {
                        results += `第${test.pageIndex}页测试失败: ${data.message}\n\n`;
                    }
                }

                resultDiv.textContent = results;
                resultDiv.className = 'result ' + (results.includes('⚠️ 异常') ? 'error' : 'success');
            } catch (error) {
                resultDiv.textContent = `测试失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 测试边界条件
        async function testBoundaryConditions() {
            const resultDiv = document.getElementById('boundaryResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '正在测试边界条件...';

            try {
                // 先获取总数
                const firstResponse = await fetch('http://localhost:5001/api/Asset?pageIndex=1&pageSize=1');
                const firstData = await firstResponse.json();
                const totalCount = firstData.data.total;

                const tests = [
                    { pageIndex: 1, pageSize: totalCount }, // 一页显示全部
                    { pageIndex: 2, pageSize: totalCount }, // 第二页应该为空
                    { pageIndex: 1, pageSize: totalCount + 100 }, // 超大页面
                    { pageIndex: Math.ceil(totalCount / 10), pageSize: 10 }, // 最后一页
                    { pageIndex: Math.ceil(totalCount / 10) + 1, pageSize: 10 } // 超出范围
                ];

                let results = `边界条件测试结果 (总记录数: ${totalCount}):\n\n`;
                
                for (const test of tests) {
                    const url = `http://localhost:5001/api/Asset?pageIndex=${test.pageIndex}&pageSize=${test.pageSize}`;
                    const response = await fetch(url);
                    const data = await response.json();
                    
                    if (data.success && data.data) {
                        results += `页码${test.pageIndex}, 页大小${test.pageSize}:\n`;
                        results += `  实际返回: ${data.data.items.length}条\n`;
                        
                        // 计算期望值
                        const startIndex = (test.pageIndex - 1) * test.pageSize;
                        const expectedCount = Math.max(0, Math.min(test.pageSize, totalCount - startIndex));
                        
                        results += `  期望返回: ${expectedCount}条\n`;
                        
                        if (data.data.items.length !== expectedCount) {
                            results += `  ⚠️ 异常: 期望${expectedCount}条，实际${data.data.items.length}条\n`;
                        } else {
                            results += `  ✅ 正常\n`;
                        }
                        results += '\n';
                    } else {
                        results += `测试失败: ${data.message}\n\n`;
                    }
                }

                resultDiv.textContent = results;
                resultDiv.className = 'result ' + (results.includes('⚠️ 异常') ? 'error' : 'success');
            } catch (error) {
                resultDiv.textContent = `测试失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 对比前端API和直接API
        async function compareFrontendVsDirect() {
            const resultDiv = document.getElementById('compareResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '正在对比前端API和直接API...';

            try {
                const tests = [
                    { pageIndex: 1, pageSize: 200 },
                    { pageIndex: 2, pageSize: 200 }
                ];

                let results = '前端API vs 直接API对比:\n\n';
                
                for (const test of tests) {
                    results += `第${test.pageIndex}页 (每页${test.pageSize}条):\n`;
                    
                    // 测试直接API
                    const directUrl = `http://localhost:5001/api/Asset?pageIndex=${test.pageIndex}&pageSize=${test.pageSize}`;
                    const directResponse = await fetch(directUrl);
                    const directData = await directResponse.json();
                    
                    results += `  直接API: ${directData.success ? directData.data.items.length + '条' : '失败'}\n`;
                    
                    // 测试前端API (通过代理)
                    try {
                        const frontendUrl = `http://localhost:5173/api/Asset?pageIndex=${test.pageIndex}&pageSize=${test.pageSize}`;
                        const frontendResponse = await fetch(frontendUrl);
                        const frontendData = await frontendResponse.json();
                        
                        results += `  前端API: ${frontendData.success ? frontendData.data.items.length + '条' : '失败'}\n`;
                        
                        // 对比结果
                        if (directData.success && frontendData.success) {
                            if (directData.data.items.length === frontendData.data.items.length) {
                                results += `  ✅ 一致\n`;
                            } else {
                                results += `  ⚠️ 不一致: 直接${directData.data.items.length}条 vs 前端${frontendData.data.items.length}条\n`;
                            }
                        }
                    } catch (frontendError) {
                        results += `  前端API: 连接失败 (${frontendError.message})\n`;
                        results += `  ℹ️ 前端服务可能未启动\n`;
                    }
                    
                    results += '\n';
                }

                resultDiv.textContent = results;
                resultDiv.className = 'result ' + (results.includes('⚠️') ? 'warning' : 'success');
            } catch (error) {
                resultDiv.textContent = `测试失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 页面加载时自动运行分析
        window.addEventListener('DOMContentLoaded', async function() {
            // 自动运行大页面分页测试
            await testLargePagination();
            
            // 创建分析表格
            await createAnalysisTable();
        });

        // 创建分析表格
        async function createAnalysisTable() {
            const tableDiv = document.getElementById('analysisTable');
            
            try {
                const response = await fetch('http://localhost:5001/api/Asset?pageIndex=1&pageSize=1');
                const data = await response.json();
                const totalCount = data.data.total;
                
                let html = '<h4>分页数据分析</h4>';
                html += '<table>';
                html += '<tr><th>页码</th><th>页大小</th><th>期望数据量</th><th>起始索引</th><th>结束索引</th></tr>';
                
                const pageSize = 200;
                const totalPages = Math.ceil(totalCount / pageSize);
                
                for (let page = 1; page <= Math.min(totalPages + 1, 5); page++) {
                    const startIndex = (page - 1) * pageSize + 1;
                    const endIndex = Math.min(page * pageSize, totalCount);
                    const expectedCount = Math.max(0, endIndex - startIndex + 1);
                    
                    html += '<tr>';
                    html += `<td>${page}</td>`;
                    html += `<td>${pageSize}</td>`;
                    html += `<td>${expectedCount}</td>`;
                    html += `<td>${startIndex}</td>`;
                    html += `<td>${endIndex}</td>`;
                    html += '</tr>';
                }
                
                html += '</table>';
                html += `<p><strong>总记录数:</strong> ${totalCount}</p>`;
                html += `<p><strong>总页数:</strong> ${totalPages}</p>`;
                
                tableDiv.innerHTML = html;
            } catch (error) {
                tableDiv.innerHTML = `<p class="error">无法创建分析表格: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
