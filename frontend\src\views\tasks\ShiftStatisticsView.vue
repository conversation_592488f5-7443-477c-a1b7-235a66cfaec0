<template>
  <div class="shift-statistics-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <el-icon><DataAnalysis /></el-icon>
            班次任务统计
          </h1>
          <p class="page-subtitle">实时监控各班次任务完成情况</p>
        </div>
        <div class="action-section">
          <el-date-picker
            v-model="selectedDate"
            type="date"
            placeholder="选择日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="loadStatistics"
            class="date-picker"
          />
          <el-button 
            type="primary" 
            :icon="Refresh" 
            @click="loadStatistics"
            :loading="loading"
          >
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 当前班次信息 -->
    <div class="current-shift-card" v-if="currentShift">
      <el-card shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><Clock /></el-icon>
            <span>当前班次</span>
          </div>
        </template>
        <div class="shift-info">
          <div class="shift-detail">
            <h3>{{ currentShift.shiftName }}</h3>
            <p>{{ currentShift.shiftCode }} | {{ currentShift.startTime }} - {{ currentShift.endTime }}</p>
            <p>任务领取时间: {{ currentShift.taskClaimTime }}</p>
          </div>
          <div class="shift-actions">
            <el-button 
              type="success" 
              :icon="Plus" 
              @click="showTaskClaimDialog"
              :disabled="!canClaimTasks"
            >
              领取任务
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-grid">
      <div 
        v-for="stat in statistics" 
        :key="`${stat.userId}-${stat.shiftId}`"
        class="stat-card"
      >
        <el-card shadow="hover" @click="viewUserTasks(stat)">
          <template #header>
            <div class="stat-header">
              <div class="user-info">
                <el-avatar 
                  :size="32" 
                  :style="{ backgroundColor: getUserColor(stat.userId) }"
                >
                  {{ stat.userName.charAt(0) }}
                </el-avatar>
                <div class="user-details">
                  <h4>{{ stat.userName }}</h4>
                  <span class="shift-name">{{ stat.shiftName }}</span>
                </div>
              </div>
              <el-tag 
                :type="getCompletionRateType(stat.completionRate)"
                size="small"
              >
                {{ stat.completionRate.toFixed(1) }}%
              </el-tag>
            </div>
          </template>
          
          <div class="stat-content">
            <div class="stat-row">
              <div class="stat-item">
                <span class="stat-number primary">{{ stat.claimedTasksCount }}</span>
                <span class="stat-label">已领取</span>
              </div>
              <div class="stat-item">
                <span class="stat-number success">{{ stat.completedTasksCount }}</span>
                <span class="stat-label">已完成</span>
              </div>
            </div>
            <div class="stat-row">
              <div class="stat-item">
                <span class="stat-number warning">{{ stat.startedTasksCount }}</span>
                <span class="stat-label">进行中</span>
              </div>
              <div class="stat-item">
                <span class="stat-number danger">{{ stat.unclaimedTasksCount }}</span>
                <span class="stat-label">未领取</span>
              </div>
            </div>
            
            <!-- 进度条 -->
            <div class="progress-section">
              <el-progress 
                :percentage="stat.completionRate" 
                :color="getProgressColor(stat.completionRate)"
                :stroke-width="8"
                :show-text="false"
              />
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && statistics.length === 0" class="empty-state">
      <el-empty description="暂无班次统计数据">
        <el-button type="primary" @click="loadStatistics">重新加载</el-button>
      </el-empty>
    </div>

    <!-- 任务领取对话框 -->
    <TaskClaimDialog 
      v-model="showClaimDialog"
      @success="handleClaimSuccess"
    />

    <!-- 用户任务详情对话框 -->
    <UserTasksDialog 
      v-model="showUserTasksDialog"
      :user-stat="selectedUserStat"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { DataAnalysis, Refresh, Clock, Plus } from '@element-plus/icons-vue'
import workShiftApi from '@/api/workShift'
import TaskClaimDialog from '@/components/Tasks/TaskClaimDialog.vue'
import UserTasksDialog from '@/components/Tasks/UserTasksDialog.vue'

// 响应式数据
const loading = ref(false)
const selectedDate = ref(new Date().toISOString().split('T')[0])
const statistics = ref([])
const currentShift = ref(null)
const showClaimDialog = ref(false)
const showUserTasksDialog = ref(false)
const selectedUserStat = ref(null)

// 用户颜色映射
const userColors = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD',
  '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9', '#F8C471', '#82E0AA'
]

// 计算属性
const canClaimTasks = computed(() => {
  if (!currentShift.value) return false
  const now = new Date()
  const currentTime = now.toTimeString().slice(0, 5)
  const claimTime = currentShift.value.taskClaimTime
  // 简化判断：在任务领取时间前后30分钟内可以领取
  return Math.abs(timeToMinutes(currentTime) - timeToMinutes(claimTime)) <= 30
})

// 方法
const loadStatistics = async () => {
  loading.value = true
  try {
    const [statsResponse, shiftResponse] = await Promise.all([
      workShiftApi.getTodayShiftStatistics({ statisticsDate: selectedDate.value }),
      workShiftApi.getUserCurrentShift()
    ])
    
    if (statsResponse.success) {
      statistics.value = statsResponse.data || []
    }
    
    if (shiftResponse.success) {
      currentShift.value = shiftResponse.data
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  } finally {
    loading.value = false
  }
}

const getUserColor = (userId) => {
  return userColors[userId % userColors.length]
}

const getCompletionRateType = (rate) => {
  if (rate >= 80) return 'success'
  if (rate >= 60) return 'warning'
  return 'danger'
}

const getProgressColor = (rate) => {
  if (rate >= 80) return '#67C23A'
  if (rate >= 60) return '#E6A23C'
  return '#F56C6C'
}

const timeToMinutes = (timeStr) => {
  const [hours, minutes] = timeStr.split(':').map(Number)
  return hours * 60 + minutes
}

const showTaskClaimDialog = () => {
  showClaimDialog.value = true
}

const viewUserTasks = (stat) => {
  selectedUserStat.value = stat
  showUserTasksDialog.value = true
}

const handleClaimSuccess = () => {
  ElMessage.success('任务领取成功')
  loadStatistics()
}

// 自动刷新
let refreshTimer = null
const startAutoRefresh = () => {
  refreshTimer = setInterval(() => {
    loadStatistics()
  }, 30000) // 30秒刷新一次
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 生命周期
onMounted(() => {
  loadStatistics()
  startAutoRefresh()
})

// 组件卸载时清理定时器
import { onUnmounted } from 'vue'
onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.shift-statistics-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.title-section {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-subtitle {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.action-section {
  display: flex;
  gap: 12px;
  align-items: center;
}

.current-shift-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.shift-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.shift-detail h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.shift-detail p {
  margin: 4px 0;
  color: #606266;
  font-size: 14px;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-details h4 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.shift-name {
  font-size: 12px;
  color: #909399;
}

.stat-content {
  padding-top: 16px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-number.primary { color: #409EFF; }
.stat-number.success { color: #67C23A; }
.stat-number.warning { color: #E6A23C; }
.stat-number.danger { color: #F56C6C; }

.stat-label {
  font-size: 12px;
  color: #909399;
}

.progress-section {
  margin-top: 16px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
  }
  
  .action-section {
    width: 100%;
    justify-content: flex-end;
  }
  
  .statistics-grid {
    grid-template-columns: 1fr;
  }
  
  .shift-info {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
}
</style>
