import{_ as s,r as a,j as e,c as t,m as l,b as n,d as u,t as i,e as o,w as r,f as c,aO as v,F as d,h as g,aI as p,o as m,A as y,a8 as f}from"./index-CkwLz8y6.js";import{a as h}from"./assetStatistics-Cx-K-pLX.js";const w={class:"debug-container"},S={class:"section"},R={class:"section"},T={class:"section"},A={class:"result-display"},D={key:0},I={key:1},$={class:"section"},E={class:"logs"},O=s({__name:"AssetStatisticsDebug",setup(s){const O=a(!1),_=a({}),k=a([]),P=a([]),b=a([]),G=e(),j=t((()=>!!p())),B=t((()=>G.userInfo)),C=(s,a,e,t,l)=>{b.value.unshift({method:s,url:a,status:e,time:t,data:l,timestamp:(new Date).toLocaleTimeString()}),b.value.length>10&&b.value.pop()},J=async()=>{var s,a;O.value=!0,P.value=[];try{const s=Date.now(),a=await h.getOverallStatistics(),e=Date.now();_.value=a,C("GET","/v2/asset-statistics/overall",200,e-s,a),f.success("总体统计获取成功")}catch(e){P.value.push(`总体统计失败: ${e.message}`),C("GET","/v2/asset-statistics/overall",(null==(s=e.response)?void 0:s.status)||"ERROR",0,{error:e.message,response:null==(a=e.response)?void 0:a.data}),f.error("总体统计获取失败")}finally{O.value=!1}},N=async()=>{var s,a;O.value=!0,P.value=[];try{const s=Date.now(),a=await h.getStatisticsByType(),e=Date.now();k.value=a,C("GET","/v2/asset-statistics/by-type",200,e-s,a),f.success("类型统计获取成功")}catch(e){P.value.push(`类型统计失败: ${e.message}`),C("GET","/v2/asset-statistics/by-type",(null==(s=e.response)?void 0:s.status)||"ERROR",0,{error:e.message,response:null==(a=e.response)?void 0:a.data}),f.error("类型统计获取失败")}finally{O.value=!1}},x=async()=>{var s,a;O.value=!0,P.value=[];const e=[{name:"总体统计",fn:()=>h.getOverallStatistics()},{name:"类型统计",fn:()=>h.getStatisticsByType()},{name:"区域统计",fn:()=>h.getStatisticsByRegion()},{name:"部门统计",fn:()=>h.getStatisticsByDepartment()},{name:"资产类型列表",fn:()=>h.getAssetTypes()},{name:"区域列表",fn:()=>h.getRegions()},{name:"部门列表",fn:()=>h.getDepartments()}];let t=0;for(const n of e)try{const s=Date.now(),a=await n.fn(),e=Date.now();t++,C("GET",`API-${n.name}`,200,e-s,a)}catch(l){P.value.push(`${n.name}失败: ${l.message}`),C("GET",`API-${n.name}`,(null==(s=l.response)?void 0:s.status)||"ERROR",0,{error:l.message,response:null==(a=l.response)?void 0:a.data})}O.value=!1,t===e.length?f.success(`所有API测试完成，${t}/${e.length} 成功`):f.warning(`API测试完成，${t}/${e.length} 成功`)};return l((()=>{})),(s,a)=>(m(),n("div",w,[a[11]||(a[11]=u("h1",null,"资产统计API调试页面",-1)),u("div",S,[a[0]||(a[0]=u("h2",null,"🔐 认证状态",-1)),u("p",null,"Token存在: "+i(j.value?"是":"否"),1),u("p",null,"用户信息: "+i(B.value),1)]),u("div",R,[a[4]||(a[4]=u("h2",null,"📊 API调用测试",-1)),o(c(v),{onClick:J,loading:O.value},{default:r((()=>a[1]||(a[1]=[y("测试总体统计")]))),_:1},8,["loading"]),o(c(v),{onClick:N,loading:O.value},{default:r((()=>a[2]||(a[2]=[y("测试类型统计")]))),_:1},8,["loading"]),o(c(v),{onClick:x,loading:O.value},{default:r((()=>a[3]||(a[3]=[y("测试所有API")]))),_:1},8,["loading"])]),u("div",T,[a[8]||(a[8]=u("h2",null,"📋 调用结果",-1)),u("div",A,[a[5]||(a[5]=u("h3",null,"总体统计数据：",-1)),u("pre",null,i(JSON.stringify(_.value,null,2)),1),a[6]||(a[6]=u("h3",null,"类型统计数据：",-1)),u("pre",null,i(JSON.stringify(k.value,null,2)),1),a[7]||(a[7]=u("h3",null,"错误信息：",-1)),P.value.length?(m(),n("pre",D,i(P.value.join("\n")),1)):(m(),n("p",I,"无错误"))])]),u("div",$,[a[10]||(a[10]=u("h2",null,"🌐 网络请求日志",-1)),u("div",E,[(m(!0),n(d,null,g(b.value,((s,e)=>(m(),n("div",{key:e,class:"log-item"},[u("strong",null,i(s.method)+" "+i(s.url),1),u("p",null,"状态: "+i(s.status),1),u("p",null,"响应时间: "+i(s.time)+"ms",1),u("details",null,[a[9]||(a[9]=u("summary",null,"详细信息",-1)),u("pre",null,i(JSON.stringify(s.data,null,2)),1)])])))),128))])])]))}},[["__scopeId","data-v-83117b1c"]]);export{O as default};
