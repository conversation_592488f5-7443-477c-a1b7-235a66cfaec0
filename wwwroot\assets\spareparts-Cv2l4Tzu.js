import{aK as t}from"./index-CkwLz8y6.js";function r(r){return t({url:"/v2/spareparttype",method:"get",params:r})}function a(r){return t({url:"/v2/spareparttype/tree",method:"get",params:r})}function e(r){return t({url:"/v2/spareparttype",method:"post",data:r})}function n(r,a){return t({url:`/v2/spareparttype/${r}`,method:"put",data:a})}function s(r){return t({url:`/v2/spareparttype/${r}`,method:"delete"})}function u(r){return t({url:"/v2/sparepartlocation",method:"get",params:r})}function p(r){return t({url:"/v2/sparepartlocation",method:"post",data:r})}function o(r,a){return t({url:`/v2/sparepartlocation/${r}`,method:"put",data:a})}function d(r){return t({url:`/v2/sparepartlocation/${r}`,method:"delete"})}function i(r={}){return t({url:"/v2/spare-parts",method:"get",params:r})}function m(r){return t({url:`/v2/spare-parts/${r}`,method:"get"})}function l(r){return t({url:"/v2/spare-parts",method:"post",data:r})}function c(r,a){return t({url:`/v2/spare-parts/${r}`,method:"put",data:a})}function f(r){return t({url:`/v2/spare-parts/${r}`,method:"delete"})}function h(r=""){return t({url:"/v2/spare-parts/simple",method:"get",params:{keyword:r}})}function v(r={}){return t({url:"/v2/spare-part-transactions",method:"get",params:r})}function g(r){return t({url:"/v2/spare-parts/transactions/in",method:"post",data:r})}function $(r){return t({url:"/v2/spare-parts/transactions/out",method:"post",data:r})}function y(){return t({url:"/v2/spare-parts/area-stats",method:"get"})}export{h as a,$ as b,l as c,v as d,f as e,m as f,i as g,d as h,o as i,p as j,y as k,u as l,s as m,n,e as o,a as p,r as q,g as s,c as u};
