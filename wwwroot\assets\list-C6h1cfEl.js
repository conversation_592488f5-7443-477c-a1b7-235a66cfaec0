import{_ as e,r as a,a5 as l,ae as t,m as s,a8 as o,b as r,d as n,e as u,w as i,af as d,ag as c,Y as p,a as m,o as v,A as f,t as y,F as g,h,a9 as w,f as b,ad as _,ah as C,V,ai as N,a1 as T}from"./index-C7OOw0MO.js";import{g as I,d as k,e as A,c as x,u as S,a as j,b as O}from"./asset-CPgk_3Sp.js";import{g as U}from"./assetType-D2p4Ng_M.js";import{l as $}from"./location-CmcDmm4Y.js";import{s as z}from"./system-9jEcQzSp.js";import{f as E}from"./format-DfhXadVZ.js";const M={class:"asset-list"},R={class:"sticky-header"},L={class:"page-header"},D={class:"header-actions"},B={class:"content-area"},q={class:"pagination-container"},F={key:0,class:"asset-title"},P={class:"history-table"},H={key:0,class:"empty-history"},J={key:0,class:"changes-list"},W={class:"field-name"},Y={class:"change-values"},G={class:"change-value-old"},X={class:"change-value-new"},K={key:1,class:"unknown-change"},Q={key:1,class:"pagination-container"},Z={class:"import-dialog-content"},ee={class:"import-steps"},ae={class:"step"},le={class:"step"},te={class:"step"},se={class:"template-download"},oe={key:0,class:"import-progress"},re={key:1,class:"import-result"},ne={class:"result-info"},ue={key:0,class:"error-messages"},ie={class:"message-list"},de={key:1,class:"warning-messages"},ce={class:"message-list"},pe={class:"dialog-footer"},me=e({__name:"list",setup(e){const me=a(!1),ve=a([]),fe=a([]),ye=a([]),ge=a(0),he=a(1),we=a(20),be=l({assetCode:"",name:"",assetTypeName:"",statusName:""}),_e=a(!1),Ce=a({}),Ve=a(!1),Ne=a(!1),Te=a(null),Ie=l({id:"",assetCode:"",financialCode:"",name:"",assetTypeId:void 0,model:"",brand:"",locationId:void 0,status:1,notes:""}),ke={assetCode:[{required:!0,message:"请输入资产编号",trigger:"blur"}],name:[{required:!0,message:"请输入资产名称",trigger:"blur"}],assetTypeId:[{required:!0,message:"请选择资产类型",trigger:"change"}],model:[{required:!0,message:"请输入规格型号",trigger:"blur"}],brand:[{required:!0,message:"请输入品牌",trigger:"blur"}]},Ae=a(!1),xe=a(!1),Se=a([]),je=a(1),Oe=a(10),Ue=a(0),$e=a(!1),ze=a(null),Ee=a(null),Me=a(!1),Re=a(0),Le=a(null);t();const De=a([]),Be=async()=>{try{const e=await U();if(e&&e.success)if(Array.isArray(e.data))fe.value=e.data;else if(e.data&&"object"==typeof e.data){let a=!1;for(const l in e.data)if(Array.isArray(e.data[l])){fe.value=e.data[l],a=!0;break}a||(fe.value=[],o.warning("获取资产类型数据格式错误"))}else fe.value=[],o.warning("获取资产类型失败：数据格式错误");else fe.value=[],o.warning("获取资产类型列表失败")}catch(e){fe.value=[],o.warning("获取资产类型列表失败："+(e.message||"未知错误"))}return fe.value},qe=async()=>{try{const e=await $.getLocationsForDropdown({includeAll:!0});e.success&&(ye.value=e.data||[])}catch(e){}},Fe=async()=>{me.value=!0,ve.value=[],ge.value=0;try{const e=await I({pageIndex:he.value,pageSize:we.value,...be});if(e&&e.success){if(e.data&&(e.data.items&&Array.isArray(e.data.items)?(ve.value=e.data.items,ge.value=e.data.totalCount||e.data.total||e.data.items.length):Array.isArray(e.data)?(ve.value=e.data,ge.value=e.data.length):e.items&&Array.isArray(e.items)?(ve.value=e.items,ge.value=e.total||e.items.length):Object.keys(e).forEach((a=>{Array.isArray(e[a])&&e[a].length>0&&(ve.value=e[a],ge.value=e[a].length),"object"==typeof e[a]&&null!==e[a]&&Object.keys(e[a]).forEach((l=>{Array.isArray(e[a][l])&&e[a][l].length>0&&(ve.value=e[a][l],e[a].totalCount||e[a].total?ge.value=e[a].totalCount||e[a].total:ge.value=e[a][l].length)}))}))),void 0!==e.total?ge.value=e.total:void 0!==e.totalCount&&(ge.value=e.totalCount),0===ge.value&&e.data&&Array.isArray(e.data.items))for(const a in e)if("total"===a&&"number"==typeof e[a]){ge.value=e[a];break}ve.value.length}else o.error((null==e?void 0:e.message)||"获取资产列表失败")}catch(e){o.error("获取资产列表失败: "+(e.message||"未知错误"))}finally{me.value=!1}},Pe=async e=>{if(!e)return o.error("获取资产详情失败: 资产ID无效"),null;try{const a=await j(e);return a&&a.success?a.data:(o.error((null==a?void 0:a.message)||"获取资产详情失败"),null)}catch(a){return o.error("获取资产详情失败: "+(a.message||"网络错误")),null}},He=async e=>{var a;xe.value=!0;try{const l={page:je.value,pageSize:Oe.value},t=await O(e,l);!0===t.success&&t.data?t.data.history?(Se.value=t.data.history.map((e=>ya(e)))||[],Ue.value=(null==(a=t.data.history)?void 0:a.length)||0,t.data.asset&&(Ce.value={...Ce.value,...t.data.asset})):t.data.items?(Se.value=t.data.items.map((e=>ya(e)))||[],Ue.value=t.data.total||0):(Se.value=Array.isArray(t.data)?t.data.map((e=>ya(e))):[],Ue.value=Array.isArray(t.data)?t.data.length:0):(o.error(t.message||"获取资产历史记录失败"),Se.value=[],Ue.value=0)}catch(l){o.error("获取资产历史记录失败: "+(l.message||"未知错误")),Se.value=[],Ue.value=0}finally{xe.value=!1}},Je=e=>({0:"info",1:"success",2:"warning",3:"danger"}[e]||"info"),We=e=>({0:"闲置",1:"在用",2:"维修",3:"报废"}[e]||"未知"),Ye=e=>({1:"创建",2:"修改",3:"删除",4:"位置变更",5:"状态变更"}[e]||"未知操作"),Ge=()=>{he.value=1,Fe()},Xe=()=>{be.assetCode="",be.name="",be.assetTypeName="",be.statusName="",he.value=1,Fe()},Ke=async e=>{we.value=e,he.value=1,await Fe()},Qe=async e=>{he.value=e,await Fe()},Ze=e=>(he.value-1)*we.value+e+1,ea=e=>{Oe.value=e,He(Ce.value.id)},aa=e=>{je.value=e,He(Ce.value.id)},la=()=>{Ne.value=!0,Object.keys(Ie).forEach((e=>{Ie[e]=""})),Ie.status=1,Ve.value=!0},ta=async()=>{if(Te.value)try{await Te.value.validate();const a=sa();let l={};if(!Ne.value&&Ce.value){if(["assetCode","financialCode","name","assetTypeId","model","brand","locationId","status","notes"].forEach((e=>{if(void 0===a[e])return;let t=Ce.value[e],s=a[e];if("assetTypeId"!==e&&"locationId"!==e&&"status"!==e||(t=null!=t?Number(t):null,s=null!=s?Number(s):null),"string"==typeof t||"string"==typeof s){const a=(t||"").toString().trim(),o=(s||"").toString().trim();a!==o&&(l[e]={oldValue:a||"无",newValue:o||"无"})}else t!==s&&(null==t&&null==s||(l[e]={oldValue:null==t?"无":t,newValue:null==s?"无":s}))})),0===Object.keys(l).length)return void o.info("没有修改任何内容，无需保存");if(l.assetTypeId){const e=fe.value.find((e=>e.id===Number(Ce.value.assetTypeId))),t=fe.value.find((e=>e.id===Number(a.assetTypeId)));l["资产类型"]={oldValue:e?e.name:"未知",newValue:t?t.name:"未知"}}if(l.locationId){const e=ye.value.find((e=>e.id===Number(Ce.value.locationId))),t=ye.value.find((e=>e.id===Number(a.locationId)));l["位置"]={oldValue:e?e.fullName||e.name:"未分配",newValue:t?t.fullName||t.name:"未分配"}}l.status&&(l["状态"]={oldValue:We(Ce.value.status),newValue:We(a.status)})}me.value=!0;try{if(Ne.value){const e=await x(a);e.success?(o.success("创建资产成功"),Ve.value=!1,Fe()):o.error(e.message||"创建资产失败")}else{const e={asset:a,changes:l},t=await S(Ie.id,e);t.success?(o.success("更新资产成功"),Ve.value=!1,Fe()):o.error(t.message||"更新资产失败")}}catch(e){o.error(Ne.value?"创建资产失败":"更新资产失败")}finally{me.value=!1}}catch(e){o.error("请完成必填项")}},sa=()=>{const e={...Ie};return void 0!==e.assetTypeId&&null!==e.assetTypeId&&(e.assetTypeId=Number(e.assetTypeId)),void 0!==e.locationId&&null!==e.locationId&&(e.locationId=Number(e.locationId)),void 0!==e.status&&(e.status=Number(e.status)),e},oa=()=>{$e.value=!0,Ee.value=null,Me.value=!1,Re.value=0,Le.value=null},ra=async()=>{try{const e=`${z.apiBaseUrl}/import/template?entityType=Assets&format=excel`,a=t(),l=await fetch(e,{method:"GET",headers:{Accept:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",Authorization:a?`Bearer ${a}`:""}});if(!l.ok){await l.text();throw new Error(`服务器返回错误: ${l.status} ${l.statusText}`)}const s=await l.blob(),r=`资产导入模板_${(new Date).toISOString().substring(0,10)}.xlsx`;if(window.navigator.msSaveOrOpenBlob)window.navigator.msSaveBlob(s,r);else{const e=URL.createObjectURL(s),a=document.createElement("a");a.href=e,a.download=r,a.style.display="none",document.body.appendChild(a),a.click(),URL.revokeObjectURL(e),document.body.removeChild(a)}o.success("模板下载成功")}catch(e){o.error(`模板下载失败: ${e.message}`)}},na=e=>{const a="application/vnd.ms-excel"===e.type||"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"===e.type||e.name.endsWith(".xlsx")||e.name.endsWith(".xls"),l="text/csv"===e.type||e.name.endsWith(".csv"),t=a||l,s=e.size/1024/1024<10;return t?s?(Ee.value=e,!0):(o.error("文件大小不能超过10MB!"),!1):(o.error("请上传Excel或CSV格式的文件!"),!1)},ua=e=>{e&&(Ee.value=e.raw||e)},ia=e=>{if(!e.file)return void o.warning("没有选择文件");const a=new FormData;a.append("file",e.file),a.append("entityType","Assets"),Me.value=!0,Re.value=10;const l=new XMLHttpRequest;l.open("POST",`${z.apiBaseUrl}/import/data`,!0);const s=t();s&&l.setRequestHeader("Authorization",`Bearer ${s}`),l.upload.addEventListener("progress",(a=>{if(a.lengthComputable){const l=Math.round(100*a.loaded/a.total);Re.value=l,e.onProgress&&e.onProgress({percent:l})}})),l.onload=()=>{if(l.status>=200&&l.status<300){let t;try{t=JSON.parse(l.responseText)}catch(a){t={success:!1,message:"无法解析服务器响应"}}e.onSuccess&&e.onSuccess(t),pa(t)}else{let t="上传失败",s=null;try{s=JSON.parse(l.responseText),t=s.message||`服务器返回错误: ${l.status}`}catch(a){t=`上传失败: ${l.status} ${l.statusText}`}e.onError&&e.onError(new Error(t)),ma({message:t,response:{data:s}})}},l.onerror=()=>{const a="网络错误，上传失败";e.onError&&e.onError(new Error(a)),ma({message:a})},l.send(a)},da=()=>{if(Ee.value)if(Me.value=!0,Re.value=0,Le.value=null,ze.value&&ze.value.uploadFiles&&ze.value.uploadFiles.length>0)ze.value.submit();else{const e=new FormData;e.append("file",Ee.value),e.append("entityType","Assets"),ia({file:Ee.value,onProgress:e=>ca(e),onSuccess:e=>pa(e),onError:e=>ma(e)})}else o.warning("请先选择要导入的文件")},ca=e=>{Re.value=Math.round(e.percent)},pa=e=>{if(Me.value=!1,Re.value=100,e.success){if(o.success("数据导入成功"),Le.value={success:!0,message:e.message||"导入成功",data:{successCount:e.successCount||0,errorCount:e.errorCount||0,totalRows:e.totalRows||0,errorMessages:[]}},e.errors&&Array.isArray(e.errors))Le.value.data.errorMessages=e.errors;else if(e.errors&&"object"==typeof e.errors){const a=[];for(const l in e.errors)a.push(`行 ${l}: ${e.errors[l]}`);Le.value.data.errorMessages=a}if(e.warnings&&"object"==typeof e.warnings){const a=[];for(const l in e.warnings){const t=e.warnings[l];Array.isArray(t)?t.forEach((e=>{a.push(`行 ${l} 警告: ${e}`)})):a.push(`行 ${l} 警告: ${t}`)}Le.value.data.warningMessages=a}}else if(o.error(e.message||"导入失败"),Le.value={success:!1,message:e.message||"导入失败",data:{successCount:e.successCount||0,errorCount:e.errorCount||0,totalRows:e.totalRows||0,errorMessages:[]}},e.errors&&Array.isArray(e.errors))Le.value.data.errorMessages=e.errors;else if(e.errors&&"object"==typeof e.errors){const a=[];for(const l in e.errors)a.push(`行 ${l}: ${e.errors[l]}`);Le.value.data.errorMessages=a}},ma=e=>{Me.value=!1;let a=e.message||"导入失败",l=[];if(e.response&&e.response.data){const t=e.response.data;if(t.message&&(a=t.message),t.errors&&Array.isArray(t.errors))l=t.errors;else if(t.errors&&"object"==typeof t.errors)for(const e in t.errors)l.push(`${e}: ${t.errors[e]}`)}o.error(a),Le.value={success:!1,message:a,data:{successCount:0,errorCount:0,errorMessages:l}}},va=()=>{$e.value=!1,Fe()},fa=async()=>{try{me.value=!0;const e={assetCode:be.assetCode||"",name:be.name||"",assetTypeName:be.assetTypeName||"",statusName:be.statusName||""};await A(e),o.success("导出成功")}catch(e){o.error("导出失败："+(e.message||"未知错误"))}finally{me.value=!1}},ya=e=>{const a={...e};if(void 0!==e.operationType||e.description){if(a.isAssetHistory=!0,a.operationTypeName=Ye(e.operationType),e.description&&"string"==typeof e.description)try{const l=JSON.parse(e.description),t={};Object.keys(l).forEach((e=>{const a=l[e];t[e]="object"==typeof a?"oldValue"in a&&"newValue"in a?{oldValue:null===a.oldValue||void 0===a.oldValue?"无":String(a.oldValue),newValue:null===a.newValue||void 0===a.newValue?"无":String(a.newValue)}:"Old"in a&&"New"in a?{oldValue:null===a.Old||void 0===a.Old?"无":String(a.Old),newValue:null===a.New||void 0===a.New?"无":String(a.New)}:"old"in a&&"new"in a?{oldValue:null===a.old||void 0===a.old?"无":String(a.old),newValue:null===a.new||void 0===a.new?"无":String(a.new)}:a:a})),a.parsedChanges=t}catch(l){a.parsedChanges=null}}else void 0===e.changeType&&void 0===e.oldLocationId||(a.isLocationHistory=!0,a.operationType=4,a.operationTypeName="位置变更",a.operationTime=e.changeTime,a.parsedChanges={"位置":{oldValue:e.oldLocationName||"未知位置",newValue:e.newLocationName||"未知位置"}},e.notes&&(a.parsedChanges["备注"]=e.notes));return a},ga=e=>{if(!e)return"无";if("object"==typeof e){if("oldValue"in e)return null===e.oldValue||void 0===e.oldValue||""===e.oldValue?"无":String(e.oldValue);if("Old"in e)return null===e.Old||void 0===e.Old||""===e.Old?"无":String(e.Old);if("old"in e)return null===e.old||void 0===e.old||""===e.old?"无":String(e.old)}return"无"},ha=e=>{if(!e)return"无";if("object"==typeof e){if("newValue"in e)return null===e.newValue||void 0===e.newValue||""===e.newValue?"无":String(e.newValue);if("New"in e)return null===e.New||void 0===e.New||""===e.New?"无":String(e.New);if("new"in e)return null===e.new||void 0===e.new||""===e.new?"无":String(e.new)}return String(e)};s((async()=>{try{await Promise.all([Be(),qe()]),Fe()}catch(e){o.error("初始化数据失败，请刷新页面重试")}}));const wa=e=>{De.value=e},ba=()=>{0!==De.value.length?(De.value.map((e=>e.name||e.assetCode)).join(", "),T.confirm(`确定要删除选中的 ${De.value.length} 个资产吗？删除后不可恢复！`,"警告",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}).then((async()=>{me.value=!0;try{const e=De.value.map((e=>k(e.id))),a=await Promise.allSettled(e),l=a.filter((e=>{var a;return"fulfilled"===e.status&&(null==(a=e.value)?void 0:a.success)})).length,t=a.length-l;0===t?o.success(`成功删除 ${l} 个资产`):0===l?o.error("删除失败，请重试"):o.warning(`部分资产删除成功，${l} 成功，${t} 失败`),Fe()}catch(e){o.error("批量删除资产失败:"+(e.message||"未知错误"))}finally{me.value=!1,De.value=[]}})).catch((()=>{}))):o.warning("请选择要删除的资产")},_a=e=>({assetCode:"资产编号",financialCode:"财务编号",name:"资产名称",assetTypeId:"资产类型ID",locationId:"位置ID",model:"型号",brand:"品牌",status:"状态",notes:"备注","资产编码":"资产编号","财务编码":"财务编号","名称":"资产名称","资产类型":"资产类型","序列号":"序列号","型号":"型号","品牌":"品牌","购买日期":"购买日期","保修到期日":"保修到期日","价格":"价格","位置":"位置","状态":"状态","备注":"备注",id:"ID"}[e]||e),Ca=e=>{if(!e)return{};const a={},l=["id","assetTypeId","locationId","ID"];return Object.keys(e).forEach((t=>{if(!l.includes(t)&&!t.endsWith("Id")&&!t.includes("ID")){const l=e[t];ga(l)!==ha(l)&&(a[t]=e[t])}})),a};return(e,a)=>{const l=m("el-button"),t=m("el-input"),s=m("el-form-item"),I=m("el-form"),A=m("el-table-column"),x=m("el-tooltip"),S=m("el-tag"),j=m("el-table"),O=m("el-pagination"),U=m("el-descriptions-item"),z=m("el-descriptions"),qe=m("el-dialog"),sa=m("el-option"),ya=m("el-select"),Va=m("el-icon"),Na=m("el-empty"),Ta=m("el-divider"),Ia=m("el-upload"),ka=m("el-progress"),Aa=m("el-scrollbar"),xa=m("el-alert"),Sa=c("loading");return v(),r("div",M,[n("div",R,[n("div",L,[a[26]||(a[26]=n("h2",null,"资产列表",-1)),n("div",D,[u(l,{type:"primary",onClick:la},{default:i((()=>a[22]||(a[22]=[f("新增资产")]))),_:1}),u(l,{type:"danger",onClick:ba,disabled:0===De.value.length},{default:i((()=>a[23]||(a[23]=[f("批量删除")]))),_:1},8,["disabled"]),u(l,{onClick:oa},{default:i((()=>a[24]||(a[24]=[f("导入")]))),_:1}),u(l,{onClick:fa},{default:i((()=>a[25]||(a[25]=[f("导出")]))),_:1})])]),u(I,{inline:!0,model:be,class:"search-form"},{default:i((()=>[u(s,{label:"资产编号"},{default:i((()=>[u(t,{modelValue:be.assetCode,"onUpdate:modelValue":a[0]||(a[0]=e=>be.assetCode=e),placeholder:"请输入资产编号"},null,8,["modelValue"])])),_:1}),u(s,{label:"资产名称"},{default:i((()=>[u(t,{modelValue:be.name,"onUpdate:modelValue":a[1]||(a[1]=e=>be.name=e),placeholder:"请输入资产名称"},null,8,["modelValue"])])),_:1}),u(s,{label:"资产类型"},{default:i((()=>[u(t,{modelValue:be.assetTypeName,"onUpdate:modelValue":a[2]||(a[2]=e=>be.assetTypeName=e),placeholder:"资产类型",disabled:""},null,8,["modelValue"])])),_:1}),u(s,{label:"使用状态"},{default:i((()=>[u(t,{modelValue:be.statusName,"onUpdate:modelValue":a[3]||(a[3]=e=>be.statusName=e),placeholder:"使用状态",disabled:""},null,8,["modelValue"])])),_:1}),u(s,null,{default:i((()=>[u(l,{type:"primary",onClick:Ge},{default:i((()=>a[27]||(a[27]=[f("查询")]))),_:1}),u(l,{onClick:Xe},{default:i((()=>a[28]||(a[28]=[f("重置")]))),_:1})])),_:1})])),_:1},8,["model"])]),n("div",B,[d((v(),p(j,{ref:"multipleTableRef","element-loading-text":"加载资产数据中...",data:ve.value,style:{width:"100%","margin-top":"15px","border-radius":"5px"},"row-key":"id","highlight-current-row":!0,onSelectionChange:wa},{default:i((()=>[u(A,{type:"selection",width:"55"}),u(A,{type:"index",label:"序号",width:"60",align:"center",index:Ze}),u(A,{prop:"assetCode",label:"资产编号","min-width":"120","show-overflow-tooltip":""},{default:i((({row:e})=>[u(x,{content:e.assetCode,placement:"top","show-after":500},{default:i((()=>[n("span",null,y(e.assetCode),1)])),_:2},1032,["content"])])),_:1}),u(A,{prop:"financialCode",label:"财务编号",width:"120"},{default:i((({row:e})=>[f(y(e.financialCode||"-"),1)])),_:1}),u(A,{prop:"name",label:"资产名称",width:"150"},{default:i((({row:e})=>[u(x,{content:e.name,placement:"top","show-after":500},{default:i((()=>[n("span",null,y(e.name),1)])),_:2},1032,["content"])])),_:1}),u(A,{label:"资产类型",width:"120"},{default:i((({row:e})=>[f(y(e.assetTypeName||"-"),1)])),_:1}),u(A,{prop:"model",label:"规格型号",width:"120"},{default:i((({row:e})=>[f(y(e.model||"-"),1)])),_:1}),u(A,{prop:"brand",label:"品牌",width:"100"},{default:i((({row:e})=>[f(y(e.brand||"-"),1)])),_:1}),u(A,{prop:"locationName",label:"位置",width:"150","show-overflow-tooltip":""},{default:i((({row:e})=>[f(y(e.locationName||"-"),1)])),_:1}),u(A,{label:"状态",width:"80"},{default:i((({row:e})=>[u(S,{type:Je(e.status)},{default:i((()=>[f(y(We(e.status)),1)])),_:2},1032,["type"])])),_:1}),u(A,{label:"操作",width:"200",fixed:"right"},{default:i((({row:e})=>[u(l,{type:"text",onClick:a=>(async e=>{Ne.value=!1,Object.keys(Ie).forEach((e=>{Ie[e]="status"===e?1:""}));try{if(0===fe.value.length)try{await Be()}catch(a){o.warning("资产类型加载失败，可能影响编辑功能")}if(0===ye.value.length){const e=await $.getLocationsForDropdown({includeAll:!0});e&&e.success?ye.value=e.data||[]:o.warning("位置列表加载失败，可能影响编辑功能")}const l=await Pe(e.id);if(!l)return void o.error("无法获取资产详情，编辑失败");if(Ie.id=l.id||e.id,Ie.assetCode=l.assetCode||e.assetCode||"",Ie.financialCode=l.financialCode||e.financialCode||"",Ie.name=l.name||e.name||"",Ie.model=l.model||e.model||"",Ie.brand=l.brand||e.brand||"",Ie.notes=l.notes||e.notes||"",Ie.status=void 0!==l.status?Number(l.status):void 0!==e.status?Number(e.status):1,l.assetTypeId)if(Ie.assetTypeId=Number(l.assetTypeId),Array.isArray(fe.value)){if(fe.value.find((e=>e.id===Ie.assetTypeId)));else if(l.assetTypeName&&Array.isArray(fe.value)){const e=fe.value.find((e=>e.name===l.assetTypeName));e&&(Ie.assetTypeId=e.id)}}else o.warning("资产类型数据格式错误，可能影响显示");else if(l.assetTypeName&&Array.isArray(fe.value)){const e=fe.value.find((e=>e.name===l.assetTypeName));e&&(Ie.assetTypeId=e.id)}if(l.locationId)if(Ie.locationId=Number(l.locationId),Array.isArray(ye.value)){if(ye.value.find((e=>e.id===Ie.locationId)));else if(l.locationName&&Array.isArray(ye.value)){const e=ye.value.find((e=>e.name===l.locationName||e.fullName===l.locationName));e&&(Ie.locationId=e.id)}}else o.warning("位置数据格式错误，可能影响显示");else if(l.locationName&&Array.isArray(ye.value)){const e=ye.value.find((e=>e.name===l.locationName||e.fullName===l.locationName));e&&(Ie.locationId=e.id)}Ce.value={...l},Ve.value=!0}catch(l){o.error("准备编辑对话框失败: "+(l.message||"未知错误"))}})(e)},{default:i((()=>a[29]||(a[29]=[f("编辑")]))),_:2},1032,["onClick"]),u(l,{type:"text",onClick:a=>(async e=>{try{const a=await Pe(e.id);if(a){if(Ce.value={...e,...a},!Ce.value.assetTypeName&&Ce.value.assetTypeId){const e=fe.value.find((e=>e.id===Ce.value.assetTypeId));e&&(Ce.value.assetTypeName=e.name)}if(!Ce.value.locationName&&Ce.value.locationId){const e=ye.value.find((e=>e.id===Ce.value.locationId));e&&(Ce.value.locationName=e.fullName||e.name)}_e.value=!0}}catch(a){o.error("获取资产详情失败")}})(e)},{default:i((()=>a[30]||(a[30]=[f("查看")]))),_:2},1032,["onClick"]),u(l,{type:"text",onClick:a=>(async e=>{Ce.value=e,je.value=1,await He(e.id),Ae.value=!0})(e)},{default:i((()=>a[31]||(a[31]=[f("历史")]))),_:2},1032,["onClick"]),u(l,{type:"text",class:"delete-btn",onClick:a=>(e=>{T.confirm("确认删除该资产吗?","提示",{type:"warning"}).then((async()=>{try{const a=await k(e.id);!0===a.success?(o.success("删除成功"),Fe()):o.error(a.message||"删除失败")}catch(a){o.error("删除失败")}}))})(e)},{default:i((()=>a[32]||(a[32]=[f("删除")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[Sa,me.value]]),n("div",q,[u(O,{"current-page":he.value,"onUpdate:currentPage":a[4]||(a[4]=e=>he.value=e),"page-size":we.value,"onUpdate:pageSize":a[5]||(a[5]=e=>we.value=e),"page-sizes":[10,20,50,100,200],total:ge.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ke,onCurrentChange:Qe},null,8,["current-page","page-size","total"])])]),u(qe,{modelValue:_e.value,"onUpdate:modelValue":a[6]||(a[6]=e=>_e.value=e),title:"资产详情",width:"650px","destroy-on-close":""},{default:i((()=>[u(z,{column:2,border:""},{default:i((()=>[u(U,{label:"资产编号"},{default:i((()=>[f(y(Ce.value.assetCode),1)])),_:1}),u(U,{label:"财务编号"},{default:i((()=>[f(y(Ce.value.financialCode||"-"),1)])),_:1}),u(U,{label:"资产名称"},{default:i((()=>[f(y(Ce.value.name),1)])),_:1}),u(U,{label:"资产类型"},{default:i((()=>[f(y(Ce.value.assetTypeName),1)])),_:1}),u(U,{label:"规格型号"},{default:i((()=>[f(y(Ce.value.model),1)])),_:1}),u(U,{label:"品牌"},{default:i((()=>[f(y(Ce.value.brand),1)])),_:1}),u(U,{label:"位置"},{default:i((()=>[f(y(Ce.value.locationName),1)])),_:1}),u(U,{label:"状态"},{default:i((()=>[u(S,{type:Je(Ce.value.status)},{default:i((()=>[f(y(We(Ce.value.status)),1)])),_:1},8,["type"])])),_:1})])),_:1})])),_:1},8,["modelValue"]),u(qe,{modelValue:Ve.value,"onUpdate:modelValue":a[16]||(a[16]=e=>Ve.value=e),title:Ne.value?"新增资产":"编辑资产",width:"500px","destroy-on-close":""},{footer:i((()=>[u(l,{onClick:a[15]||(a[15]=e=>Ve.value=!1)},{default:i((()=>a[33]||(a[33]=[f("取消")]))),_:1}),u(l,{type:"primary",onClick:ta},{default:i((()=>a[34]||(a[34]=[f("确定")]))),_:1})])),default:i((()=>[u(I,{ref_key:"assetFormRef",ref:Te,model:Ie,rules:ke,"label-width":"100px",style:{"max-height":"500px","overflow-y":"auto"}},{default:i((()=>[u(s,{label:"资产编号",prop:"assetCode"},{default:i((()=>[u(t,{modelValue:Ie.assetCode,"onUpdate:modelValue":a[7]||(a[7]=e=>Ie.assetCode=e),placeholder:"请输入资产编号"},null,8,["modelValue"])])),_:1}),u(s,{label:"财务编号",prop:"financialCode"},{default:i((()=>[u(t,{modelValue:Ie.financialCode,"onUpdate:modelValue":a[8]||(a[8]=e=>Ie.financialCode=e),placeholder:"请输入财务编号"},null,8,["modelValue"])])),_:1}),u(s,{label:"资产名称",prop:"name"},{default:i((()=>[u(t,{modelValue:Ie.name,"onUpdate:modelValue":a[9]||(a[9]=e=>Ie.name=e),placeholder:"请输入资产名称"},null,8,["modelValue"])])),_:1}),u(s,{label:"资产类型",prop:"assetTypeId"},{default:i((()=>[u(ya,{modelValue:Ie.assetTypeId,"onUpdate:modelValue":a[10]||(a[10]=e=>Ie.assetTypeId=e),placeholder:"请选择资产类型",style:{width:"100%"}},{default:i((()=>[(v(!0),r(g,null,h(fe.value,(e=>(v(),p(sa,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),u(s,{label:"规格型号",prop:"model"},{default:i((()=>[u(t,{modelValue:Ie.model,"onUpdate:modelValue":a[11]||(a[11]=e=>Ie.model=e),placeholder:"请输入规格型号"},null,8,["modelValue"])])),_:1}),u(s,{label:"品牌",prop:"brand"},{default:i((()=>[u(t,{modelValue:Ie.brand,"onUpdate:modelValue":a[12]||(a[12]=e=>Ie.brand=e),placeholder:"请输入品牌"},null,8,["modelValue"])])),_:1}),u(s,{label:"位置",prop:"locationId"},{default:i((()=>[u(ya,{modelValue:Ie.locationId,"onUpdate:modelValue":a[13]||(a[13]=e=>Ie.locationId=e),filterable:"",placeholder:"请选择位置",style:{width:"100%"}},{default:i((()=>[(v(!0),r(g,null,h(ye.value,(e=>(v(),p(sa,{key:e.id,label:e.fullName||e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),u(s,{label:"状态",prop:"status"},{default:i((()=>[u(ya,{modelValue:Ie.status,"onUpdate:modelValue":a[14]||(a[14]=e=>Ie.status=e),placeholder:"请选择状态"},{default:i((()=>[u(sa,{label:"闲置",value:0}),u(sa,{label:"在用",value:1}),u(sa,{label:"维修",value:2}),u(sa,{label:"报废",value:3})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"]),u(qe,{modelValue:Ae.value,"onUpdate:modelValue":a[19]||(a[19]=e=>Ae.value=e),title:"资产历史记录",width:"800px","destroy-on-close":"",class:"history-dialog"},{default:i((()=>[Ce.value.id?(v(),r("h3",F,[u(Va,null,{default:i((()=>[u(b(_))])),_:1}),f(" 资产: "+y(Ce.value.name)+" ",1),u(S,{type:"info",size:"small"},{default:i((()=>[f(y(Ce.value.assetCode),1)])),_:1})])):w("",!0),d((v(),r("div",P,[0!==Se.value.length||xe.value?(v(),p(j,{key:1,ref:"historyTable",data:Se.value,style:{width:"100%"},border:"",stripe:"",size:"default"},{default:i((()=>[u(A,{prop:"operationTime",label:"变更时间",width:"170",sortable:""},{default:i((e=>[f(y(b(E)(e.row.changeTime||e.row.operationTime)),1)])),_:1}),u(A,{prop:"operationTypeName",label:"操作类型",width:"100"},{default:i((e=>{return[u(S,{type:(a=e.row.operationType,{1:"success",2:"primary",3:"danger",4:"warning",5:"info"}[a]||"info")},{default:i((()=>[f(y(e.row.operationTypeName||Ye(e.row.operationType)),1)])),_:2},1032,["type"])];var a})),_:1}),u(A,{label:"变更内容","min-width":"450"},{default:i((e=>[e.row.parsedChanges?(v(),r("div",J,[(v(!0),r(g,null,h(Ca(e.row.parsedChanges),((e,a)=>(v(),r("div",{key:a,class:"change-item"},[n("div",W,y(_a(a)),1),n("div",Y,[n("span",G,y(ga(e)),1),u(Va,{class:"change-arrow"},{default:i((()=>[u(b(C))])),_:1}),n("span",X,y(ha(e)),1)])])))),128))])):(v(),r("div",K,[u(l,{size:"small",type:"primary",onClick:a=>(e=>{if(e.description)try{const a=JSON.parse(e.description);e.parsedChanges=a}catch(a){o.error("无法解析变更详情")}})(e.row)},{default:i((()=>a[35]||(a[35]=[f("解析变更")]))),_:2},1032,["onClick"])]))])),_:1}),u(A,{prop:"operatorName",label:"操作人",width:"150"})])),_:1},8,["data"])):(v(),r("div",H,[u(Na,{description:"暂无历史记录"})]))])),[[Sa,xe.value]]),Se.value.length>0?(v(),r("div",Q,[u(O,{"current-page":je.value,"onUpdate:currentPage":a[17]||(a[17]=e=>je.value=e),"page-size":Oe.value,"onUpdate:pageSize":a[18]||(a[18]=e=>Oe.value=e),total:Ue.value,"page-sizes":[10,20,50],layout:"total, sizes, prev, pager, next",onSizeChange:ea,onCurrentChange:aa},null,8,["current-page","page-size","total"])])):w("",!0)])),_:1},8,["modelValue"]),u(qe,{modelValue:$e.value,"onUpdate:modelValue":a[21]||(a[21]=e=>$e.value=e),title:"资产数据导入",width:"500px","destroy-on-close":""},{footer:i((()=>[n("span",pe,[u(l,{onClick:a[20]||(a[20]=e=>$e.value=!1)},{default:i((()=>a[52]||(a[52]=[f("关闭")]))),_:1}),Le.value&&Le.value.success?(v(),p(l,{key:0,type:"primary",onClick:va},{default:i((()=>a[53]||(a[53]=[f(" 完成并刷新 ")]))),_:1})):w("",!0)])])),default:i((()=>[n("div",Z,[n("div",ee,[n("p",ae,[u(S,{size:"small"},{default:i((()=>a[36]||(a[36]=[f("步骤 1")]))),_:1}),a[37]||(a[37]=f(" 下载导入模板"))]),n("p",le,[u(S,{size:"small"},{default:i((()=>a[38]||(a[38]=[f("步骤 2")]))),_:1}),a[39]||(a[39]=f(" 按照模板格式填写数据"))]),n("p",te,[u(S,{size:"small"},{default:i((()=>a[40]||(a[40]=[f("步骤 3")]))),_:1}),a[41]||(a[41]=f(" 上传填写好的文件"))])]),n("div",se,[u(l,{type:"primary",plain:"",onClick:ra},{default:i((()=>[u(Va,null,{default:i((()=>[u(b(V))])),_:1}),a[42]||(a[42]=f(" 下载导入模板 "))])),_:1}),a[43]||(a[43]=n("p",{class:"template-tip"},"请确保按照模板格式填写数据，否则可能导致导入失败",-1))]),u(Ta),u(Ia,{ref_key:"uploadRef",ref:ze,class:"import-upload",action:"",multiple:!1,"auto-upload":!1,"http-request":ia,"on-change":ua,"before-upload":na,"on-success":pa,"on-error":ma,"on-progress":ca,accept:".csv,.xlsx,.xls",limit:1},{trigger:i((()=>[u(l,{type:"primary"},{default:i((()=>[u(Va,null,{default:i((()=>[u(b(N))])),_:1}),a[44]||(a[44]=f(" 选择文件 "))])),_:1})])),tip:i((()=>a[45]||(a[45]=[n("div",{class:"el-upload__tip"}," 仅支持 xlsx, xls 或 csv 格式文件，文件大小不超过10MB ",-1)]))),default:i((()=>[u(l,{class:"ml-3",type:"success",onClick:da,loading:Me.value,disabled:null===Ee.value},{default:i((()=>a[46]||(a[46]=[f(" 开始导入 ")]))),_:1},8,["loading","disabled"])])),_:1},512),Me.value?(v(),r("div",oe,[u(ka,{percentage:Re.value,status:"success"},null,8,["percentage"]),a[47]||(a[47]=n("p",{class:"progress-text"},"正在导入，请勿关闭窗口...",-1))])):w("",!0),Le.value?(v(),r("div",re,[u(xa,{title:Le.value.success?"导入成功":"导入失败",type:Le.value.success?"success":"error","show-icon":""},{default:i((()=>{var e,l,t,s,o;return[n("div",ne,[n("p",null," 总计 "+y((null==(e=Le.value.data)?void 0:e.totalRows)||0)+" 条记录， 成功导入 "+y((null==(l=Le.value.data)?void 0:l.successCount)||0)+" 条， 失败 "+y((null==(t=Le.value.data)?void 0:t.errorCount)||0)+" 条 ",1),(null==(s=Le.value.data)?void 0:s.errorMessages)&&Le.value.data.errorMessages.length>0?(v(),r("div",ue,[a[49]||(a[49]=n("p",{class:"message-title"},"错误信息:",-1)),u(Aa,{height:"150px"},{default:i((()=>[n("ul",ie,[(v(!0),r(g,null,h(Le.value.data.errorMessages,((e,l)=>(v(),r("li",{key:"error-"+l},[u(S,{type:"danger",size:"small"},{default:i((()=>a[48]||(a[48]=[f("错误")]))),_:1}),f(" "+y(e),1)])))),128))])])),_:1})])):w("",!0),(null==(o=Le.value.data)?void 0:o.warningMessages)&&Le.value.data.warningMessages.length>0?(v(),r("div",de,[a[51]||(a[51]=n("p",{class:"message-title"},"警告信息:",-1)),u(Aa,{height:"150px"},{default:i((()=>[n("ul",ce,[(v(!0),r(g,null,h(Le.value.data.warningMessages,((e,l)=>(v(),r("li",{key:"warning-"+l},[u(S,{type:"warning",size:"small"},{default:i((()=>a[50]||(a[50]=[f("警告")]))),_:1}),f(" "+y(e),1)])))),128))])])),_:1})])):w("",!0)])]})),_:1},8,["title","type"])])):w("",!0)])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-fd382baf"]]);export{me as default};
