namespace ItAssetsSystem.Models.DTOs.Gamification
{
    /// <summary>
    /// 游戏化用户统计DTO
    /// </summary>
    public class GamificationUserStatsDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 当前经验值
        /// </summary>
        public int CurrentXP { get; set; }

        /// <summary>
        /// 当前等级
        /// </summary>
        public int CurrentLevel { get; set; }

        /// <summary>
        /// 当前等级所需经验值
        /// </summary>
        public int CurrentLevelXP { get; set; }

        /// <summary>
        /// 下一等级所需经验值
        /// </summary>
        public int NextLevelXP { get; set; }

        /// <summary>
        /// 当前等级进度百分比
        /// </summary>
        public decimal LevelProgress { get; set; }

        /// <summary>
        /// 当前可用积分
        /// </summary>
        public int PointsBalance { get; set; }

        /// <summary>
        /// 累计完成任务数
        /// </summary>
        public int CompletedTasksCount { get; set; }

        /// <summary>
        /// 累计按时完成任务数
        /// </summary>
        public int OnTimeTasksCount { get; set; }

        /// <summary>
        /// 按时完成率
        /// </summary>
        public decimal OnTimeRate { get; set; }

        /// <summary>
        /// 当前连续天数
        /// </summary>
        public int StreakCount { get; set; }

        /// <summary>
        /// 最后活跃时间
        /// </summary>
        public DateTime? LastActivityTimestamp { get; set; }
    }

    /// <summary>
    /// 游戏化奖励DTO
    /// </summary>
    public class GamificationRewardDto
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 获得的经验值
        /// </summary>
        public int XPGained { get; set; }

        /// <summary>
        /// 获得的积分
        /// </summary>
        public int PointsGained { get; set; }

        /// <summary>
        /// 是否升级
        /// </summary>
        public bool LeveledUp { get; set; }

        /// <summary>
        /// 新等级
        /// </summary>
        public int? NewLevel { get; set; }

        /// <summary>
        /// 获得的徽章
        /// </summary>
        public List<string> BadgesEarned { get; set; } = new();

        /// <summary>
        /// 奖励详情
        /// </summary>
        public string RewardDetails { get; set; } = string.Empty;
    }

    /// <summary>
    /// 每日任务统计DTO
    /// </summary>
    public class DailyTaskStatsDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 日期
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// 领取任务数
        /// </summary>
        public int ClaimedTasksCount { get; set; }

        /// <summary>
        /// 完成任务数
        /// </summary>
        public int CompletedTasksCount { get; set; }

        /// <summary>
        /// 按时完成任务数
        /// </summary>
        public int OnTimeTasksCount { get; set; }

        /// <summary>
        /// 获得的总经验值
        /// </summary>
        public int TotalXPGained { get; set; }

        /// <summary>
        /// 获得的总积分
        /// </summary>
        public int TotalPointsGained { get; set; }

        /// <summary>
        /// 完成率
        /// </summary>
        public decimal CompletionRate { get; set; }

        /// <summary>
        /// 按时完成率
        /// </summary>
        public decimal OnTimeRate { get; set; }
    }

    /// <summary>
    /// 用户排行榜DTO
    /// </summary>
    public class UserLeaderboardDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 头像URL
        /// </summary>
        public string? AvatarUrl { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        public string? Department { get; set; }

        /// <summary>
        /// 积分
        /// </summary>
        public int Points { get; set; }

        /// <summary>
        /// 排名
        /// </summary>
        public int Rank { get; set; }

        /// <summary>
        /// 等级
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 完成任务数
        /// </summary>
        public int CompletedTasksCount { get; set; }

        /// <summary>
        /// 连续天数
        /// </summary>
        public int StreakCount { get; set; }

        /// <summary>
        /// 排行榜类型
        /// </summary>
        public string LeaderboardType { get; set; } = string.Empty;

        /// <summary>
        /// 排行榜周期
        /// </summary>
        public string LeaderboardPeriod { get; set; } = string.Empty;
    }

    /// <summary>
    /// 任务领取请求DTO
    /// </summary>
    public class TaskClaimRequestDto
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public long TaskId { get; set; }

        /// <summary>
        /// 班次ID
        /// </summary>
        public long ShiftId { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Notes { get; set; }
    }

    /// <summary>
    /// 任务领取响应DTO
    /// </summary>
    public class TaskClaimResponseDto
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 领取记录ID
        /// </summary>
        public long? ClaimId { get; set; }

        /// <summary>
        /// 奖励信息
        /// </summary>
        public GamificationRewardDto? Reward { get; set; }
    }
}
