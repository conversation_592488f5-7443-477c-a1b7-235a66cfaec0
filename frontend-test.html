<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端资产列表测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .controls {
            padding: 30px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }

        .control-group {
            display: flex;
            gap: 20px;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            padding: 20px 30px;
            background: #f1f5f9;
            border-left: 4px solid #3b82f6;
            margin: 20px 30px;
            border-radius: 0 8px 8px 0;
        }

        .pagination-controls {
            padding: 20px 30px;
            display: flex;
            gap: 10px;
            align-items: center;
            justify-content: center;
        }

        .pagination-controls input,
        .pagination-controls select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
        }

        .data-display {
            padding: 30px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .data-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
        }

        .data-table tr:hover {
            background: #f9fafb;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .log {
            background: #1e293b;
            color: #e2e8f0;
            padding: 20px;
            margin: 20px 30px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-entry.success {
            color: #10b981;
        }

        .log-entry.error {
            color: #ef4444;
        }

        .log-entry.info {
            color: #3b82f6;
        }

        .log-entry.warning {
            color: #f59e0b;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 前端资产列表测试</h1>
            <p>模拟前端 http://localhost:5173/main/asset/list 页面的API调用</p>
        </div>

        <div class="controls">
            <div class="control-group">
                <button class="btn" onclick="testFrontendAPI()">
                    <span id="test-loading" style="display: none;" class="loading"></span>
                    测试前端API调用
                </button>
                <button class="btn" onclick="testDirectAPI()">
                    <span id="direct-loading" style="display: none;" class="loading"></span>
                    测试直接API调用
                </button>
                <button class="btn" onclick="clearResults()">清除结果</button>
            </div>
        </div>

        <div class="pagination-controls">
            <label>页码:</label>
            <input type="number" id="pageIndex" value="1" min="1">
            
            <label>每页大小:</label>
            <select id="pageSize">
                <option value="5">5条/页</option>
                <option value="10">10条/页</option>
                <option value="20" selected>20条/页</option>
                <option value="50">50条/页</option>
            </select>
            
            <button class="btn" onclick="loadSpecificPage()">
                <span id="load-loading" style="display: none;" class="loading"></span>
                加载指定页
            </button>
        </div>

        <div class="status" id="status">
            <strong>状态:</strong> 准备就绪，点击按钮开始测试
        </div>

        <div class="data-display">
            <h3>数据展示</h3>
            <table class="data-table" id="dataTable">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>资产ID</th>
                        <th>资产编号</th>
                        <th>资产名称</th>
                        <th>状态</th>
                        <th>位置</th>
                    </tr>
                </thead>
                <tbody id="dataTableBody">
                    <tr>
                        <td colspan="6" style="text-align: center; color: #6b7280;">暂无数据</td>
                    </tr>
                </tbody>
            </table>
            
            <div id="paginationInfo" style="text-align: center; color: #6b7280;">
                分页信息将在这里显示
            </div>
        </div>

        <div class="log" id="log">
            <div class="log-entry info">[INFO] 前端测试系统已初始化</div>
            <div class="log-entry info">[INFO] 模拟前端页面: http://localhost:5173/main/asset/list</div>
            <div class="log-entry info">[INFO] 准备开始测试...</div>
        </div>
    </div>

    <script>
        // 日志函数
        function addLog(message, type = 'info') {
            const log = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        // 更新状态
        function updateStatus(message) {
            document.getElementById('status').innerHTML = `<strong>状态:</strong> ${message}`;
        }

        // 测试前端API调用（通过代理）
        async function testFrontendAPI() {
            const loading = document.getElementById('test-loading');
            loading.style.display = 'inline-block';
            updateStatus('正在测试前端API调用（通过5173代理）...');

            try {
                addLog('开始测试前端API调用', 'info');
                
                // 模拟前端的API调用方式
                const pageIndex = parseInt(document.getElementById('pageIndex').value) || 1;
                const pageSize = parseInt(document.getElementById('pageSize').value) || 20;
                
                // 前端通过代理调用API
                const frontendUrl = `http://localhost:5173/api/Asset?pageIndex=${pageIndex}&pageSize=${pageSize}`;
                addLog(`前端代理URL: ${frontendUrl}`, 'info');
                
                const response = await fetch(frontendUrl);
                const data = await response.json();
                
                addLog(`前端API响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (data.success && data.data) {
                    displayData(data.data, pageIndex, pageSize, '前端API');
                    updateStatus('前端API调用成功');
                    addLog(`前端API返回${data.data.items.length}条记录`, 'success');
                } else {
                    addLog('前端API返回失败: ' + (data.message || '未知错误'), 'error');
                    updateStatus('前端API调用失败');
                }
            } catch (error) {
                addLog(`前端API调用失败: ${error.message}`, 'error');
                updateStatus('前端API调用失败');
                
                // 如果前端代理失败，提示可能的原因
                if (error.message.includes('Failed to fetch')) {
                    addLog('可能原因: 前端服务未启动或代理配置错误', 'warning');
                    addLog('请确保前端服务在 http://localhost:5173 运行', 'warning');
                }
            } finally {
                loading.style.display = 'none';
            }
        }

        // 测试直接API调用
        async function testDirectAPI() {
            const loading = document.getElementById('direct-loading');
            loading.style.display = 'inline-block';
            updateStatus('正在测试直接API调用...');

            try {
                addLog('开始测试直接API调用', 'info');
                
                const pageIndex = parseInt(document.getElementById('pageIndex').value) || 1;
                const pageSize = parseInt(document.getElementById('pageSize').value) || 20;
                
                // 直接调用后端API
                const directUrl = `http://localhost:5001/api/Asset?pageIndex=${pageIndex}&pageSize=${pageSize}`;
                addLog(`直接API URL: ${directUrl}`, 'info');
                
                const response = await fetch(directUrl);
                const data = await response.json();
                
                addLog(`直接API响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (data.success && data.data) {
                    displayData(data.data, pageIndex, pageSize, '直接API');
                    updateStatus('直接API调用成功');
                    addLog(`直接API返回${data.data.items.length}条记录`, 'success');
                } else {
                    addLog('直接API返回失败: ' + (data.message || '未知错误'), 'error');
                    updateStatus('直接API调用失败');
                }
            } catch (error) {
                addLog(`直接API调用失败: ${error.message}`, 'error');
                updateStatus('直接API调用失败');
            } finally {
                loading.style.display = 'none';
            }
        }

        // 加载指定页面
        async function loadSpecificPage() {
            const loading = document.getElementById('load-loading');
            loading.style.display = 'inline-block';
            
            const pageIndex = parseInt(document.getElementById('pageIndex').value) || 1;
            const pageSize = parseInt(document.getElementById('pageSize').value) || 20;
            
            updateStatus(`正在加载第${pageIndex}页，每页${pageSize}条...`);

            try {
                // 先尝试前端API，再尝试直接API
                let success = false;
                
                // 尝试前端API
                try {
                    const frontendUrl = `http://localhost:5173/api/Asset?pageIndex=${pageIndex}&pageSize=${pageSize}`;
                    const response = await fetch(frontendUrl);
                    const data = await response.json();
                    
                    if (data.success && data.data) {
                        displayData(data.data, pageIndex, pageSize, '前端API');
                        addLog(`通过前端API成功加载第${pageIndex}页`, 'success');
                        success = true;
                    }
                } catch (frontendError) {
                    addLog('前端API不可用，尝试直接API', 'warning');
                }
                
                // 如果前端API失败，尝试直接API
                if (!success) {
                    const directUrl = `http://localhost:5001/api/Asset?pageIndex=${pageIndex}&pageSize=${pageSize}`;
                    const response = await fetch(directUrl);
                    const data = await response.json();
                    
                    if (data.success && data.data) {
                        displayData(data.data, pageIndex, pageSize, '直接API');
                        addLog(`通过直接API成功加载第${pageIndex}页`, 'success');
                        success = true;
                    }
                }
                
                if (success) {
                    updateStatus(`成功加载第${pageIndex}页数据`);
                } else {
                    updateStatus('加载数据失败');
                    addLog('所有API调用都失败了', 'error');
                }
                
            } catch (error) {
                addLog(`加载页面失败: ${error.message}`, 'error');
                updateStatus('加载数据失败');
            } finally {
                loading.style.display = 'none';
            }
        }

        // 显示数据
        function displayData(data, pageIndex, pageSize, source) {
            const tbody = document.getElementById('dataTableBody');
            const paginationInfo = document.getElementById('paginationInfo');
            
            if (data.items && data.items.length > 0) {
                tbody.innerHTML = '';
                
                data.items.forEach((item, index) => {
                    const row = document.createElement('tr');
                    const serialNumber = (pageIndex - 1) * pageSize + index + 1;
                    
                    row.innerHTML = `
                        <td>${serialNumber}</td>
                        <td>${item.id}</td>
                        <td>${item.assetCode || '-'}</td>
                        <td>${item.name || '-'}</td>
                        <td>${item.statusName || item.status || '-'}</td>
                        <td>${item.locationName || '-'}</td>
                    `;
                    tbody.appendChild(row);
                });
                
                paginationInfo.innerHTML = `
                    <strong>数据源: ${source}</strong> | 
                    当前页: ${data.pageIndex || pageIndex} | 
                    每页大小: ${data.pageSize || pageSize} | 
                    总记录数: ${data.total || data.totalCount || 0} | 
                    总页数: ${data.totalPages || Math.ceil((data.total || data.totalCount || 0) / pageSize)}
                `;
                
                addLog(`显示了${data.items.length}条记录 (来源: ${source})`, 'success');
            } else {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: #6b7280;">暂无数据</td></tr>';
                paginationInfo.innerHTML = '暂无数据';
                addLog('没有数据返回', 'info');
            }
        }

        // 清除结果
        function clearResults() {
            document.getElementById('dataTableBody').innerHTML = 
                '<tr><td colspan="6" style="text-align: center; color: #6b7280;">暂无数据</td></tr>';
            document.getElementById('paginationInfo').innerHTML = '分页信息将在这里显示';
            document.getElementById('log').innerHTML = `
                <div class="log-entry info">[INFO] 结果已清除</div>
                <div class="log-entry info">[INFO] 准备开始新的测试...</div>
            `;
            updateStatus('结果已清除，准备开始新的测试');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('前端测试页面已加载', 'success');
            updateStatus('准备就绪，点击按钮开始测试');
            
            // 自动检测前端服务状态
            checkFrontendService();
        });

        // 检测前端服务状态
        async function checkFrontendService() {
            try {
                const response = await fetch('http://localhost:5173/', { method: 'HEAD' });
                if (response.ok) {
                    addLog('前端服务 (5173) 运行正常', 'success');
                } else {
                    addLog('前端服务 (5173) 响应异常', 'warning');
                }
            } catch (error) {
                addLog('前端服务 (5173) 不可用', 'error');
                addLog('建议: 启动前端服务 npm run dev', 'warning');
            }
            
            // 检测后端服务状态
            try {
                const response = await fetch('http://localhost:5001/api/Asset?pageIndex=1&pageSize=1');
                if (response.ok) {
                    addLog('后端服务 (5001) 运行正常', 'success');
                } else {
                    addLog('后端服务 (5001) 响应异常', 'warning');
                }
            } catch (error) {
                addLog('后端服务 (5001) 不可用', 'error');
            }
        }
    </script>
</body>
</html>
