import{_ as e,b as a,e as l,w as t,r as s,a5 as r,m as i,a as o,aP as n,a8 as u,u as d,ag as m,o as g,d as p,F as c,h as f,Y as k,A as b,af as w,t as y,a9 as h,as as _}from"./index-C7OOw0MO.js";import{t as v}from"./task-BTGSy_AK.js";const V={class:"team-tasks-container"},C={class:"card-header"},F={class:"header-actions"},I={class:"filter-container"},S={key:0,class:"loading-container"},T={key:1,class:"empty-data"},U={key:2},x={key:1},D={class:"pagination-container"},z={class:"dialog-footer"},P={class:"dialog-footer"};const A=e({name:"TeamTasksView",setup(){const e=d(),a=s([]),l=s(!0),t=s(!1),o=s(0),m=s(1),g=s(10),p=s([]),c=r({status:"",priority:"",assigneeUserId:null}),f=s(!1),k=r({taskId:null,taskTitle:"",assigneeUserId:null,remarks:""}),b=s(!1),w=r({taskId:null,taskTitle:"",currentStatus:"",newStatus:"",progress:100,remarks:""}),y=s(!1),h=async()=>{l.value=!0;try{const e={pageIndex:m.value,pageSize:g.value,teamView:!0};c.status&&(e.status=c.status),c.priority&&(e.priority=c.priority),c.assigneeUserId&&(e.assigneeUserId=c.assigneeUserId);const l=await v.getTasks(e);l.success?(a.value=l.data.items,o.value=l.data.totalCount):u.error(l.message||"加载任务数据失败")}catch(e){u.error("加载任务数据时发生错误")}finally{l.value=!1}},_=a=>{e.push({name:"TaskDetail",params:{id:a.taskId}})};return i((()=>{(async()=>{try{const e=await n.getTeamMembers();e.success?p.value=e.data:u.warning(e.message||"加载团队成员失败")}catch(e){u.warning("加载团队成员时发生错误")}})(),h()})),{tasks:a,loading:l,tableLoading:t,total:o,currentPage:m,pageSize:g,teamMembers:p,filterForm:c,assignDialogVisible:f,assignForm:k,statusDialogVisible:b,statusForm:w,submitting:y,refreshData:()=>{h()},handleFilter:()=>{m.value=1,h()},resetFilter:()=>{c.status="",c.priority="",c.assigneeUserId=null,m.value=1,h()},handleSizeChange:e=>{g.value=e,h()},handleCurrentChange:e=>{m.value=e,h()},getStatusType:e=>({pending:"info",in_progress:"warning",completed:"success",cancelled:"danger"}[e]||"info"),getStatusText:e=>({pending:"待处理",in_progress:"进行中",completed:"已完成",cancelled:"已取消"}[e]||e),getPriorityType:e=>({low:"info",medium:"success",high:"warning",urgent:"danger"}[e]||"info"),getPriorityText:e=>({low:"低",medium:"中",high:"高",urgent:"紧急"}[e]||e),getProgressStatus:e=>{if("completed"===e.status)return"success";if("cancelled"===e.status)return"exception";if(e.dueDate){if(new Date>new Date(e.dueDate)&&e.progress<100)return"exception"}return""},handleRowClick:e=>{_(e)},viewTaskDetail:_,createTask:()=>{e.push({name:"TaskCreate"})},assignTask:e=>{k.taskId=e.taskId,k.taskTitle=e.title,k.assigneeUserId=e.assigneeUserId,k.remarks="",f.value=!0},submitAssign:async()=>{if(k.taskId&&k.assigneeUserId){y.value=!0;try{const e=await v.assignTask(k.taskId,{assigneeUserId:k.assigneeUserId,remarks:k.remarks});e.success?(u.success("任务分配成功"),f.value=!1,h()):u.error(e.message||"任务分配失败")}catch(e){u.error("任务分配时发生错误")}finally{y.value=!1}}else u.warning("请选择负责人")},updateStatus:e=>{w.taskId=e.taskId,w.taskTitle=e.title,w.currentStatus=e.status,w.newStatus=e.status,w.progress=e.progress||0,w.remarks="",b.value=!0},submitStatusUpdate:async()=>{if(w.taskId&&w.newStatus){y.value=!0;try{const e=await v.updateTaskStatus(w.taskId,{status:w.newStatus,remarks:w.remarks});e.success?("completed"===w.newStatus&&await v.updateTaskProgress(w.taskId,{progress:100,remarks:"任务已完成，进度自动更新为100%"}),u.success("任务状态更新成功"),b.value=!1,h()):u.error(e.message||"任务状态更新失败")}catch(e){u.error("任务状态更新时发生错误")}finally{y.value=!1}}else u.warning("请选择新状态")},getAllAssignees:getAllAssignees}}},[["render",function(e,s,r,i,n,u){const d=o("Refresh"),v=o("el-icon"),A=o("el-button"),R=o("Plus"),M=o("el-option"),N=o("el-select"),j=o("el-form-item"),L=o("el-form"),Y=o("el-skeleton"),q=o("el-empty"),B=o("el-table-column"),E=o("el-tag"),G=o("el-progress"),H=o("el-table"),J=o("el-pagination"),K=o("el-card"),O=o("el-input"),Q=o("el-dialog"),W=o("el-input-number"),X=m("loading");return g(),a("div",V,[l(K,{class:"task-card"},{header:t((()=>[p("div",C,[s[14]||(s[14]=p("h2",null,"团队任务",-1)),p("div",F,[l(A,{type:"primary",onClick:i.refreshData},{default:t((()=>[l(v,null,{default:t((()=>[l(d)])),_:1}),s[12]||(s[12]=b(" 刷新 "))])),_:1},8,["onClick"]),l(A,{type:"success",onClick:i.createTask},{default:t((()=>[l(v,null,{default:t((()=>[l(R)])),_:1}),s[13]||(s[13]=b(" 新建任务 "))])),_:1},8,["onClick"])])])])),default:t((()=>[p("div",I,[l(L,{inline:!0,model:i.filterForm,class:"filter-form"},{default:t((()=>[l(j,{label:"状态"},{default:t((()=>[l(N,{modelValue:i.filterForm.status,"onUpdate:modelValue":s[0]||(s[0]=e=>i.filterForm.status=e),placeholder:"任务状态",clearable:""},{default:t((()=>[l(M,{label:"待处理",value:"pending"}),l(M,{label:"进行中",value:"in_progress"}),l(M,{label:"已完成",value:"completed"}),l(M,{label:"已取消",value:"cancelled"})])),_:1},8,["modelValue"])])),_:1}),l(j,{label:"优先级"},{default:t((()=>[l(N,{modelValue:i.filterForm.priority,"onUpdate:modelValue":s[1]||(s[1]=e=>i.filterForm.priority=e),placeholder:"优先级",clearable:""},{default:t((()=>[l(M,{label:"低",value:"low"}),l(M,{label:"中",value:"medium"}),l(M,{label:"高",value:"high"}),l(M,{label:"紧急",value:"urgent"})])),_:1},8,["modelValue"])])),_:1}),l(j,{label:"负责人"},{default:t((()=>[l(N,{modelValue:i.filterForm.assigneeUserId,"onUpdate:modelValue":s[2]||(s[2]=e=>i.filterForm.assigneeUserId=e),placeholder:"选择负责人",clearable:"",filterable:""},{default:t((()=>[(g(!0),a(c,null,f(i.teamMembers,(e=>(g(),k(M,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),l(j,null,{default:t((()=>[l(A,{type:"primary",onClick:i.handleFilter},{default:t((()=>s[15]||(s[15]=[b("筛选")]))),_:1},8,["onClick"]),l(A,{onClick:i.resetFilter},{default:t((()=>s[16]||(s[16]=[b("重置")]))),_:1},8,["onClick"])])),_:1})])),_:1},8,["model"])]),i.loading?(g(),a("div",S,[l(Y,{rows:10,animated:""})])):0===i.tasks.length?(g(),a("div",T,[l(q,{description:"暂无任务数据"})])):(g(),a("div",U,[w((g(),k(H,{data:i.tasks,style:{width:"100%"},onRowClick:i.handleRowClick},{default:t((()=>[l(B,{prop:"taskId",label:"ID",width:"70"}),l(B,{prop:"title",label:"任务名称","min-width":"200","show-overflow-tooltip":""}),l(B,{prop:"status",label:"状态",width:"100"},{default:t((e=>[l(E,{type:i.getStatusType(e.row.status)},{default:t((()=>[b(y(i.getStatusText(e.row.status)),1)])),_:2},1032,["type"])])),_:1}),l(B,{prop:"priority",label:"优先级",width:"100"},{default:t((e=>[l(E,{type:i.getPriorityType(e.row.priority),effect:"dark"},{default:t((()=>[b(y(i.getPriorityText(e.row.priority)),1)])),_:2},1032,["type"])])),_:1}),l(B,{prop:"dueDate",label:"截止日期",width:"120"}),l(B,{prop:"progress",label:"进度",width:"180"},{default:t((e=>[l(G,{percentage:e.row.progress||0,status:i.getProgressStatus(e.row)},null,8,["percentage","status"])])),_:1}),l(B,{prop:"assigneeName",label:"负责人",width:"120"},{default:t((e=>[e.row.assigneeName?(g(),a("span",x,y(e.row.assigneeName),1)):(g(),k(E,{key:0,type:"info"},{default:t((()=>s[17]||(s[17]=[b("未分配")]))),_:1}))])),_:1}),l(B,{label:"操作",width:"220",fixed:"right"},{default:t((e=>[l(A,{size:"small",type:"primary",onClick:_((a=>i.viewTaskDetail(e.row)),["stop"])},{default:t((()=>s[18]||(s[18]=[b(" 详情 ")]))),_:2},1032,["onClick"]),e.row.assigneeUserId?h("",!0):(g(),k(A,{key:0,size:"small",type:"success",onClick:_((a=>i.assignTask(e.row)),["stop"])},{default:t((()=>s[19]||(s[19]=[b(" 分配 ")]))),_:2},1032,["onClick"])),"completed"!==e.row.status&&"cancelled"!==e.row.status?(g(),k(A,{key:1,size:"small",type:"warning",onClick:_((a=>i.updateStatus(e.row)),["stop"])},{default:t((()=>s[20]||(s[20]=[b(" 更新状态 ")]))),_:2},1032,["onClick"])):h("",!0)])),_:1})])),_:1},8,["data","onRowClick"])),[[X,i.tableLoading]]),p("div",D,[l(J,{background:"",layout:"total, sizes, prev, pager, next","page-sizes":[10,20,50,100],total:i.total,"page-size":i.pageSize,"current-page":i.currentPage,onSizeChange:i.handleSizeChange,onCurrentChange:i.handleCurrentChange},null,8,["total","page-size","current-page","onSizeChange","onCurrentChange"])])]))])),_:1}),l(Q,{modelValue:i.assignDialogVisible,"onUpdate:modelValue":s[6]||(s[6]=e=>i.assignDialogVisible=e),title:"分配任务",width:"500px","close-on-click-modal":!1},{footer:t((()=>[p("span",z,[l(A,{onClick:s[5]||(s[5]=e=>i.assignDialogVisible=!1)},{default:t((()=>s[21]||(s[21]=[b("取消")]))),_:1}),l(A,{type:"primary",onClick:i.submitAssign,loading:i.submitting},{default:t((()=>s[22]||(s[22]=[b(" 确认 ")]))),_:1},8,["onClick","loading"])])])),default:t((()=>[l(L,{model:i.assignForm,"label-width":"100px"},{default:t((()=>[l(j,{label:"任务名称"},{default:t((()=>[p("span",null,y(i.assignForm.taskTitle),1)])),_:1}),l(j,{label:"负责人"},{default:t((()=>[l(N,{modelValue:i.assignForm.assigneeUserId,"onUpdate:modelValue":s[3]||(s[3]=e=>i.assignForm.assigneeUserId=e),placeholder:"选择负责人",filterable:""},{default:t((()=>[(g(!0),a(c,null,f(i.teamMembers,(e=>(g(),k(M,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),l(j,{label:"备注"},{default:t((()=>[l(O,{modelValue:i.assignForm.remarks,"onUpdate:modelValue":s[4]||(s[4]=e=>i.assignForm.remarks=e),type:"textarea",rows:3,placeholder:"请输入分配备注"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),l(Q,{modelValue:i.statusDialogVisible,"onUpdate:modelValue":s[11]||(s[11]=e=>i.statusDialogVisible=e),title:"更新任务状态",width:"500px","close-on-click-modal":!1},{footer:t((()=>[p("span",P,[l(A,{onClick:s[10]||(s[10]=e=>i.statusDialogVisible=!1)},{default:t((()=>s[24]||(s[24]=[b("取消")]))),_:1}),l(A,{type:"primary",onClick:i.submitStatusUpdate,loading:i.submitting},{default:t((()=>s[25]||(s[25]=[b(" 确认 ")]))),_:1},8,["onClick","loading"])])])),default:t((()=>[l(L,{model:i.statusForm,"label-width":"100px"},{default:t((()=>[l(j,{label:"任务名称"},{default:t((()=>[p("span",null,y(i.statusForm.taskTitle),1)])),_:1}),l(j,{label:"当前状态"},{default:t((()=>[l(E,{type:i.getStatusType(i.statusForm.currentStatus)},{default:t((()=>[b(y(i.getStatusText(i.statusForm.currentStatus)),1)])),_:1},8,["type"])])),_:1}),l(j,{label:"新状态"},{default:t((()=>[l(N,{modelValue:i.statusForm.newStatus,"onUpdate:modelValue":s[7]||(s[7]=e=>i.statusForm.newStatus=e),placeholder:"选择新状态"},{default:t((()=>[l(M,{label:"待处理",value:"pending"}),l(M,{label:"进行中",value:"in_progress"}),l(M,{label:"已完成",value:"completed"}),l(M,{label:"已取消",value:"cancelled"})])),_:1},8,["modelValue"])])),_:1}),"completed"===i.statusForm.newStatus?(g(),k(j,{key:0,label:"进度"},{default:t((()=>[l(W,{modelValue:i.statusForm.progress,"onUpdate:modelValue":s[8]||(s[8]=e=>i.statusForm.progress=e),min:0,max:100,step:5},null,8,["modelValue"]),s[23]||(s[23]=p("span",{class:"progress-unit"},"%",-1))])),_:1})):h("",!0),l(j,{label:"备注"},{default:t((()=>[l(O,{modelValue:i.statusForm.remarks,"onUpdate:modelValue":s[9]||(s[9]=e=>i.statusForm.remarks=e),type:"textarea",rows:3,placeholder:"请输入状态更新备注"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}],["__scopeId","data-v-5681f9ba"]]);export{A as default};
