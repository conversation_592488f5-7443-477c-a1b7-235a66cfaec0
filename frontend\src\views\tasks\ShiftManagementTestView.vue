<template>
  <div class="test-container">
    <h2>班次管理API测试页面</h2>
    
    <div class="test-section">
      <h3>1. 获取所有班次</h3>
      <el-button @click="testGetAllShifts" :loading="loading.shifts">测试获取班次</el-button>
      <pre v-if="results.shifts">{{ JSON.stringify(results.shifts, null, 2) }}</pre>
    </div>

    <div class="test-section">
      <h3>2. 获取用户当前班次</h3>
      <el-button @click="testGetCurrentShift" :loading="loading.currentShift">测试获取当前班次</el-button>
      <pre v-if="results.currentShift">{{ JSON.stringify(results.currentShift, null, 2) }}</pre>
    </div>

    <div class="test-section">
      <h3>3. 获取今日班次统计</h3>
      <el-button @click="testGetStatistics" :loading="loading.statistics">测试获取统计</el-button>
      <pre v-if="results.statistics">{{ JSON.stringify(results.statistics, null, 2) }}</pre>
    </div>

    <div class="test-section">
      <h3>4. 获取可领取任务</h3>
      <el-button @click="testGetAvailableTasks" :loading="loading.tasks">测试获取任务</el-button>
      <pre v-if="results.tasks">{{ JSON.stringify(results.tasks, null, 2) }}</pre>
    </div>

    <div class="test-section">
      <h3>5. 创建测试班次</h3>
      <el-button @click="testCreateShift" :loading="loading.createShift">创建测试班次</el-button>
      <pre v-if="results.createShift">{{ JSON.stringify(results.createShift, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import workShiftApi from '@/api/workShift'

const loading = reactive({
  shifts: false,
  currentShift: false,
  statistics: false,
  tasks: false,
  createShift: false
})

const results = reactive({
  shifts: null,
  currentShift: null,
  statistics: null,
  tasks: null,
  createShift: null
})

const testGetAllShifts = async () => {
  loading.shifts = true
  try {
    const response = await workShiftApi.getAllShifts()
    results.shifts = response
    ElMessage.success('获取班次成功')
  } catch (error) {
    console.error('获取班次失败:', error)
    results.shifts = { error: error.message }
    ElMessage.error('获取班次失败')
  } finally {
    loading.shifts = false
  }
}

const testGetCurrentShift = async () => {
  loading.currentShift = true
  try {
    const response = await workShiftApi.getUserCurrentShift()
    results.currentShift = response
    ElMessage.success('获取当前班次成功')
  } catch (error) {
    console.error('获取当前班次失败:', error)
    results.currentShift = { error: error.message }
    ElMessage.error('获取当前班次失败')
  } finally {
    loading.currentShift = false
  }
}

const testGetStatistics = async () => {
  loading.statistics = true
  try {
    const response = await workShiftApi.getTodayShiftStatistics()
    results.statistics = response
    ElMessage.success('获取统计成功')
  } catch (error) {
    console.error('获取统计失败:', error)
    results.statistics = { error: error.message }
    ElMessage.error('获取统计失败')
  } finally {
    loading.statistics = false
  }
}

const testGetAvailableTasks = async () => {
  loading.tasks = true
  try {
    const response = await workShiftApi.getAvailableTasks()
    results.tasks = response
    ElMessage.success('获取任务成功')
  } catch (error) {
    console.error('获取任务失败:', error)
    results.tasks = { error: error.message }
    ElMessage.error('获取任务失败')
  } finally {
    loading.tasks = false
  }
}

const testCreateShift = async () => {
  loading.createShift = true
  try {
    const testShiftData = {
      shiftName: '测试班次',
      shiftCode: 'TEST',
      shiftType: 'Day',
      startTime: '09:00:00',
      endTime: '17:00:00',
      taskClaimTime: '09:00:00',
      isOvernight: false,
      description: '这是一个测试班次'
    }
    
    const response = await workShiftApi.createShift(testShiftData)
    results.createShift = response
    ElMessage.success('创建班次成功')
  } catch (error) {
    console.error('创建班次失败:', error)
    results.createShift = { error: error.message }
    ElMessage.error('创建班次失败')
  } finally {
    loading.createShift = false
  }
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.test-section h3 {
  margin-top: 0;
  color: #333;
}

pre {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 300px;
  margin-top: 10px;
}
</style>
