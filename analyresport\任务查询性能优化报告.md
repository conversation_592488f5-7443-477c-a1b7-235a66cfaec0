# 任务查询性能优化报告

## 问题诊断

根据日志分析，发现任务查询接口 `/api/v2/tasks` 存在严重的性能问题：

### 主要问题
1. **N+1查询问题**：每个任务都单独查询用户信息，导致大量重复的数据库查询
2. **重复查询**：同一个用户可能被多次查询
3. **统计信息单独查询**：子任务数、评论数、附件数每个任务单独查询
4. **缺少批量优化**：没有使用批量查询和预加载

### 性能表现
- 原始耗时：约1.9秒（处理少量任务）
- 查询次数：每个任务产生4-6次额外查询
- 前端体验：总响应时间约6秒

## 优化方案

### 1. 批量查询优化
创建了 `MapTasksToDtosOptimizedAsync` 方法，实现以下优化：

#### 数据收集阶段
```csharp
// 收集所有需要查询的ID
var userIds = new HashSet<int>();
var assetIds = new HashSet<int>();
var locationIds = new HashSet<int>();
var parentTaskIds = new HashSet<long>();
```

#### 并行批量查询
```csharp
// 批量查询所有相关数据
var usersTask = _coreDataQueryService.GetUsersAsync(userIds.ToList());
var assetsTask = _coreDataQueryService.GetAssetsAsync(assetIds.ToList());
var locationsTask = _coreDataQueryService.GetLocationsAsync(locationIds.ToList());
var parentTasksTask = _taskRepository.GetTasksByIdsAsync(parentTaskIds.ToList());

// 批量查询统计信息
var subTaskCountsTask = _taskRepository.GetSubTaskCountsBatchAsync(taskIds);
var commentCountsTask = _taskRepository.GetCommentCountsBatchAsync(taskIds);
var attachmentCountsTask = _taskRepository.GetAttachmentCountsBatchAsync(taskIds);

// 并行等待所有查询完成
await Task.WhenAll(usersTask, assetsTask, locationsTask, parentTasksTask, 
                   subTaskCountsTask, commentCountsTask, attachmentCountsTask);
```

### 2. Repository层优化

#### 新增批量统计方法
在 `ITaskRepository` 接口中添加：
- `GetSubTaskCountsBatchAsync` - 批量获取子任务数量
- `GetCommentCountsBatchAsync` - 批量获取评论数量  
- `GetAttachmentCountsBatchAsync` - 批量获取附件数量
- `GetTasksByIdsAsync` - 批量获取任务实体

#### 实现示例
```csharp
public async Task<Dictionary<long, int>> GetCommentCountsBatchAsync(List<long> taskIds)
{
    var counts = await _dbContext.Comments
        .Where(c => taskIds.Contains(c.TaskId))
        .GroupBy(c => c.TaskId)
        .Select(g => new { TaskId = g.Key, Count = g.Count() })
        .AsNoTracking()
        .ToListAsync();

    var result = taskIds.ToDictionary(id => id, id => 0);
    foreach (var count in counts)
    {
        result[count.TaskId] = count.Count;
    }
    return result;
}
```

### 3. 数据映射优化

#### 缓存查询结果
```csharp
var users = (await usersTask).ToDictionary(u => u.Id, u => u);
var assets = (await assetsTask).ToDictionary(a => a.Id, a => a);
var locations = (await locationsTask).ToDictionary(l => l.Id, l => l);
```

#### 高效映射
创建 `MapTaskToDtoWithCachedData` 方法，使用预加载的数据进行映射，避免重复查询。

## 优化效果预期

### 查询次数减少
- **原来**：N个任务 × 4-6次查询 = 4N-6N次查询
- **优化后**：7次批量查询（固定次数，与任务数量无关）

### 性能提升预期
- **查询时间**：从1.9秒减少到200-500毫秒
- **数据库负载**：查询次数减少80-90%
- **用户体验**：总响应时间从6秒减少到2-3秒

### 可扩展性
- 任务数量增加时，性能衰减较小
- 批量查询具有更好的缓存命中率
- 数据库连接使用更高效

## 修改文件清单

| 文件路径 | 修改类型 | 说明 |
|---------|---------|------|
| `Application/Features/Tasks/Services/TaskService.cs` | 修改 | 添加批量映射方法，优化GetTasksAsync |
| `Core/Abstractions/ITaskRepository.cs` | 新增 | 添加批量查询接口方法 |
| `Infrastructure/Data/Repositories/TaskRepository.cs` | 新增 | 实现批量查询方法 |

## 后续建议

### 1. 进一步优化
- 考虑添加Redis缓存用户信息
- 实现查询结果的内存缓存
- 添加数据库索引优化

### 2. 监控指标
- 监控API响应时间
- 跟踪数据库查询次数
- 观察用户满意度变化

### 3. 测试验证
- 进行负载测试验证性能提升
- 测试并发场景下的表现
- 验证内存使用情况

## 注意事项

1. **数据一致性**：批量查询保持了与原方法相同的数据一致性
2. **错误处理**：保持了原有的错误处理逻辑
3. **向后兼容**：不影响现有API接口和前端调用
4. **类型安全**：使用强类型Dictionary替代动态类型