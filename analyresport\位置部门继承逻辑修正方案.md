# 位置部门继承逻辑修正方案

## 问题分析

你提出了一个非常重要的业务逻辑问题：**位置的使用部门继承机制**

### 当前问题
原有的SQL查询：
```sql
LEFT JOIN departments d ON l.DefaultDepartmentId = d.Id
```

这种方式只能获取**当前位置直接设置的部门**，无法处理以下场景：
- 层级5位置（设备位置）没有设置部门
- 需要向上查找父级位置的部门
- 直到找到设置了部门的祖先位置

### 正确的业务逻辑
```
位置层级示例：
工厂1 (部门：总公司)
├── 产线A (部门：生产部) ✅ 设置了部门
    ├── 工序1 (部门：未设置) ❌ 应继承"生产部"
        ├── 工位1 (部门：未设置) ❌ 应继承"生产部"  
            ├── 设备位置1 (部门：未设置) ❌ 应继承"生产部"
```

## 解决方案

### 方案1：MySQL递归CTE查询 (推荐)

```sql
-- 使用递归CTE获取位置及其继承的部门
WITH RECURSIVE location_department_hierarchy AS (
    -- 基础查询：获取所有位置
    SELECT 
        l.Id as location_id,
        l.Name as location_name,
        l.Code as location_code,
        l.Type as location_type,
        l.ParentId as parent_id,
        l.Path as location_path,
        l.DefaultDepartmentId as current_dept_id,
        -- 如果当前位置有部门，使用当前部门；否则准备向上查找
        CASE 
            WHEN l.DefaultDepartmentId IS NOT NULL THEN l.DefaultDepartmentId
            ELSE NULL 
        END as effective_dept_id,
        0 as level_up  -- 层级计数器
    FROM locations l
    
    UNION ALL
    
    -- 递归查询：向上查找有部门的父级位置
    SELECT 
        ldh.location_id,
        ldh.location_name,
        ldh.location_code,
        ldh.location_type,
        p.ParentId as parent_id,
        ldh.location_path,
        ldh.current_dept_id,
        -- 如果还没找到部门且父级有部门，使用父级部门
        CASE 
            WHEN ldh.effective_dept_id IS NULL AND p.DefaultDepartmentId IS NOT NULL 
            THEN p.DefaultDepartmentId
            ELSE ldh.effective_dept_id 
        END as effective_dept_id,
        ldh.level_up + 1
    FROM location_department_hierarchy ldh
    JOIN locations p ON ldh.parent_id = p.Id
    WHERE ldh.effective_dept_id IS NULL  -- 只有还没找到部门的才继续向上查找
        AND ldh.level_up < 10            -- 防止无限递归
)
-- 主查询：关联资产数据
SELECT 
    a.Id as id, 
    a.AssetCode as assetCode, 
    a.Name as name, 
    a.AssetTypeId as assetTypeId,
    at.Name AS assetTypeName,
    a.SerialNumber as serialNumber,
    a.Model as model,
    a.Brand as brand,
    a.PurchaseDate as purchaseDate,
    a.WarrantyExpireDate as warrantyExpireDate,
    a.Price as price,
    a.LocationId as locationId,
    ldh.location_name AS locationName,
    ldh.location_path AS locationPath,
    -- 使用继承后的有效部门
    d.Name AS departmentName,
    d.Id AS departmentId,
    a.Status as status,
    a.Notes as notes,
    a.FinancialCode as financialCode,
    a.CreatedAt as createdAt,
    a.UpdatedAt as updatedAt
FROM assets a
    LEFT JOIN assettypes at ON a.AssetTypeId = at.Id
    LEFT JOIN (
        -- 获取每个位置的最终有效部门（去重）
        SELECT DISTINCT 
            location_id, 
            location_name, 
            location_code,
            location_type,
            location_path,
            FIRST_VALUE(effective_dept_id) OVER (
                PARTITION BY location_id 
                ORDER BY CASE WHEN effective_dept_id IS NOT NULL THEN 0 ELSE 1 END, level_up
            ) as final_dept_id
        FROM location_department_hierarchy
    ) ldh ON a.LocationId = ldh.location_id
    LEFT JOIN departments d ON ldh.final_dept_id = d.Id
WHERE 1=1;
```

### 方案2：应用层递归查询 (适用于复杂业务逻辑)

```csharp
/// <summary>
/// 获取位置的有效部门ID（支持继承）
/// </summary>
/// <param name="locationId">位置ID</param>
/// <returns>有效的部门ID</returns>
private async Task<int?> GetEffectiveDepartmentId(int locationId)
{
    var location = await _context.Locations
        .FirstOrDefaultAsync(l => l.Id == locationId);
    
    if (location == null) return null;
    
    // 如果当前位置有部门，直接返回
    if (location.DefaultDepartmentId.HasValue)
    {
        return location.DefaultDepartmentId;
    }
    
    // 如果没有部门且有父级，递归查找父级
    if (location.ParentId.HasValue)
    {
        return await GetEffectiveDepartmentId(location.ParentId.Value);
    }
    
    // 没有父级且没有部门，返回null
    return null;
}

/// <summary>
/// 批量获取多个位置的有效部门（优化版本）
/// </summary>
/// <param name="locationIds">位置ID列表</param>
/// <returns>位置ID到部门ID的映射</returns>
private async Task<Dictionary<int, int?>> GetEffectiveDepartmentIds(List<int> locationIds)
{
    var result = new Dictionary<int, int?>();
    
    // 获取所有相关位置（包括祖先位置）
    var allLocations = await _context.Locations
        .Where(l => locationIds.Any(id => l.Path.Contains(id.ToString())))
        .OrderBy(l => l.Path.Length) // 从根到叶子排序
        .ToListAsync();
    
    foreach (var locationId in locationIds)
    {
        result[locationId] = await GetEffectiveDepartmentIdFromCache(locationId, allLocations);
    }
    
    return result;
}

private async Task<int?> GetEffectiveDepartmentIdFromCache(int locationId, List<Location> allLocations)
{
    var location = allLocations.FirstOrDefault(l => l.Id == locationId);
    if (location == null) return null;
    
    // 如果当前位置有部门，直接返回
    if (location.DefaultDepartmentId.HasValue)
    {
        return location.DefaultDepartmentId;
    }
    
    // 通过Path向上查找有部门的祖先位置
    if (!string.IsNullOrEmpty(location.Path))
    {
        var pathIds = location.Path.Split(',')
            .Where(p => !string.IsNullOrEmpty(p))
            .Select(int.Parse)
            .Reverse() // 从当前位置向根查找
            .ToList();
        
        foreach (var pathId in pathIds)
        {
            if (pathId == locationId) continue; // 跳过自己
            
            var ancestorLocation = allLocations.FirstOrDefault(l => l.Id == pathId);
            if (ancestorLocation?.DefaultDepartmentId.HasValue == true)
            {
                return ancestorLocation.DefaultDepartmentId;
            }
        }
    }
    
    return null;
}
```

### 方案3：数据库存储过程 (性能最优)

```sql
DELIMITER //

CREATE FUNCTION GetEffectiveDepartmentId(input_location_id INT) 
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE current_id INT DEFAULT input_location_id;
    DECLARE dept_id INT DEFAULT NULL;
    DECLARE parent_id INT DEFAULT NULL;
    DECLARE max_iterations INT DEFAULT 10;
    DECLARE current_iteration INT DEFAULT 0;
    
    -- 循环向上查找有部门的位置
    WHILE current_id IS NOT NULL AND current_iteration < max_iterations DO
        -- 获取当前位置的部门和父级ID
        SELECT DefaultDepartmentId, ParentId 
        INTO dept_id, parent_id
        FROM locations 
        WHERE Id = current_id 
        LIMIT 1;
        
        -- 如果找到部门，直接返回
        IF dept_id IS NOT NULL THEN
            RETURN dept_id;
        END IF;
        
        -- 移动到父级位置
        SET current_id = parent_id;
        SET current_iteration = current_iteration + 1;
    END WHILE;
    
    -- 没有找到部门，返回NULL
    RETURN NULL;
END //

DELIMITER ;

-- 使用存储过程的查询
SELECT 
    a.Id as id, 
    a.AssetCode as assetCode, 
    a.Name as name, 
    a.AssetTypeId as assetTypeId,
    at.Name AS assetTypeName,
    a.Model as model,
    a.Brand as brand,
    a.LocationId as locationId,
    l.Name AS locationName,
    l.Path AS locationPath,
    -- 使用存储过程获取继承的部门
    d.Name AS departmentName,
    d.Id AS departmentId,
    a.Status as status,
    a.FinancialCode as financialCode,
    a.CreatedAt as createdAt,
    a.UpdatedAt as updatedAt
FROM assets a
    LEFT JOIN assettypes at ON a.AssetTypeId = at.Id
    LEFT JOIN locations l ON a.LocationId = l.Id
    LEFT JOIN departments d ON GetEffectiveDepartmentId(l.Id) = d.Id
WHERE 1=1;
```

## 修正后的统计查询

### 按位置统计（考虑部门继承）

```sql
-- 层级5位置的资产统计（正确处理部门继承）
WITH RECURSIVE location_dept_inheritance AS (
    SELECT 
        Id as location_id,
        Name as location_name,
        Code as location_code,
        Type as location_type,
        DefaultDepartmentId as dept_id,
        CASE WHEN DefaultDepartmentId IS NOT NULL THEN DefaultDepartmentId ELSE NULL END as effective_dept_id,
        ParentId,
        0 as level_up
    FROM locations 
    WHERE Type = 4  -- 只处理层级5位置
    
    UNION ALL
    
    SELECT 
        ldi.location_id,
        ldi.location_name,
        ldi.location_code,
        ldi.location_type,
        ldi.dept_id,
        CASE 
            WHEN ldi.effective_dept_id IS NULL AND p.DefaultDepartmentId IS NOT NULL 
            THEN p.DefaultDepartmentId
            ELSE ldi.effective_dept_id 
        END,
        p.ParentId,
        ldi.level_up + 1
    FROM location_dept_inheritance ldi
    JOIN locations p ON ldi.ParentId = p.Id
    WHERE ldi.effective_dept_id IS NULL AND ldi.level_up < 10
)
SELECT 
    ldi.location_id,
    ldi.location_name,
    ldi.location_code,
    d.Id as department_id,
    d.Name as department_name,
    at.Id as asset_type_id,
    at.Name as asset_type_name,
    COUNT(*) as asset_count
FROM (
    SELECT DISTINCT 
        location_id, 
        location_name, 
        location_code,
        FIRST_VALUE(effective_dept_id) OVER (
            PARTITION BY location_id 
            ORDER BY CASE WHEN effective_dept_id IS NOT NULL THEN 0 ELSE 1 END, level_up
        ) as final_dept_id
    FROM location_dept_inheritance
) ldi
LEFT JOIN departments d ON ldi.final_dept_id = d.Id
LEFT JOIN assets a ON a.LocationId = ldi.location_id
LEFT JOIN assettypes at ON a.AssetTypeId = at.Id
WHERE a.Status != 3  -- 排除报废资产
GROUP BY ldi.location_id, d.Id, at.Id
ORDER BY ldi.location_name, d.Name, at.Name;
```

## 实施建议

### 立即可行方案
1. **修改AssetController中的SQL查询**，使用方案1的递归CTE
2. **添加GetEffectiveDepartmentId方法**，作为备用的应用层解决方案
3. **更新前端统计API调用**，确保正确显示继承的部门信息

### 长期优化方案
1. **考虑添加缓存机制**，避免重复计算位置部门继承
2. **数据库视图**：创建一个包含有效部门的位置视图
3. **预计算字段**：在位置表中添加`EffectiveDepartmentId`字段并定期更新

## 测试验证

```sql
-- 测试查询：验证部门继承逻辑
SELECT 
    l.Id,
    l.Name,
    l.Type,
    l.DefaultDepartmentId as direct_dept,
    GetEffectiveDepartmentId(l.Id) as effective_dept,
    d1.Name as direct_dept_name,
    d2.Name as effective_dept_name
FROM locations l
    LEFT JOIN departments d1 ON l.DefaultDepartmentId = d1.Id
    LEFT JOIN departments d2 ON GetEffectiveDepartmentId(l.Id) = d2.Id
WHERE l.Type = 4  -- 层级5位置
ORDER BY l.Name;
```

这样就能正确处理位置部门的继承逻辑，确保统计数据的准确性！