import{_ as e,b as a,e as s,w as t,r as l,a5 as r,m as o,a as i,a8 as n,u,aJ as d,o as p,d as g,A as c,aq as m,Y as f,t as k,a9 as h,aw as y}from"./index-CkwLz8y6.js";import{t as v}from"./task-Uzj9rZkj.js";import{U as w}from"./UserAvatarStack-DuXt8B2E.js";const b={class:"my-tasks-container"},_={class:"card-header"},C={class:"header-actions"},V={class:"filter-container"},T={key:0,class:"loading-container"},P={key:1,class:"empty-data"},U={key:2},F={class:"pagination-container"},S={class:"dialog-footer"};const I=e({name:"MyTasksView",components:{UserAvatarStack:w},setup(){const e=u(),a=l([]),s=l(!0),t=l(!1),i=l(0),d=l(1),p=l(10),g=r({status:"",priority:""}),c=l(!1),m=r({taskId:null,progress:0,status:"",remarks:""}),f=l(!1),k=async()=>{s.value=!0;try{const e={pageIndex:d.value,pageSize:p.value,assignedToMe:!0};g.status&&(e.status=g.status),g.priority&&(e.priority=g.priority);const s=await v.getTasks(e);s.success?(a.value=s.data.items,i.value=s.data.totalCount):n.error(s.message||"加载任务数据失败")}catch(e){n.error("加载任务数据时发生错误")}finally{s.value=!1}},h=e=>({pending:"待处理",in_progress:"进行中",completed:"已完成",cancelled:"已取消"}[e]||e),y=a=>{e.push({name:"TaskDetail",params:{id:a.taskId}})};return o((()=>{k()})),{tasks:a,loading:s,tableLoading:t,total:i,currentPage:d,pageSize:p,filterForm:g,progressDialogVisible:c,progressForm:m,submitting:f,refreshData:()=>{k()},handleFilter:()=>{d.value=1,k()},resetFilter:()=>{g.status="",g.priority="",d.value=1,k()},handleSizeChange:e=>{p.value=e,k()},handleCurrentChange:e=>{d.value=e,k()},getStatusType:e=>({pending:"info",in_progress:"warning",completed:"success",cancelled:"danger"}[e]||"info"),getStatusText:h,getPriorityType:e=>({low:"info",medium:"success",high:"warning",urgent:"danger"}[e]||"info"),getPriorityText:e=>({low:"低",medium:"中",high:"高",urgent:"紧急"}[e]||e),getProgressStatus:e=>{if("completed"===e.status)return"success";if("cancelled"===e.status)return"exception";if(e.dueDate){if(new Date>new Date(e.dueDate)&&e.progress<100)return"exception"}return""},formatProgressTooltip:e=>`${e}%`,handleRowClick:e=>{y(e)},viewTaskDetail:y,createTask:()=>{e.push({name:"TaskCreate"})},updateProgress:e=>{m.taskId=e.taskId,m.progress=e.progress||0,m.status=e.status,m.remarks="",c.value=!0},submitProgressUpdate:async()=>{if(m.taskId){f.value=!0;try{const e=await v.updateTaskProgress(m.taskId,{progress:m.progress,remarks:m.remarks});e.success?(n.success("任务进度更新成功"),c.value=!1,m.status!==e.data.status&&await v.updateTaskStatus(m.taskId,{status:m.status,remarks:`状态更新为: ${h(m.status)}`}),k()):n.error(e.message||"更新任务进度失败")}catch(e){n.error("更新任务进度时发生错误")}finally{f.value=!1}}},getAllAssignees:e=>{if(!e)return[];const a=[];return e.assigneeUserId&&a.push({id:e.assigneeUserId,name:e.assigneeName||"未知用户",avatarUrl:e.assigneeAvatarUrl||"",role:"Primary",isPrimary:!0}),e.assignees&&Array.isArray(e.assignees)&&e.assignees.length>0&&e.assignees.forEach((s=>{s.userId!==e.assigneeUserId&&a.push({id:s.userId,name:s.userName||"未知用户",avatarUrl:s.avatarUrl||"",role:"Collaborator",isPrimary:!1})})),a}}}},[["render",function(e,l,r,o,n,u){const v=i("Refresh"),w=i("el-icon"),I=i("el-button"),x=i("Plus"),D=i("el-option"),z=i("el-select"),A=i("el-form-item"),R=i("el-form"),j=i("el-skeleton"),N=i("el-empty"),L=i("el-table-column"),M=i("el-tag"),$=i("el-progress"),q=i("UserAvatarStack"),E=i("el-table"),J=i("el-pagination"),Y=i("el-card"),B=i("el-slider"),G=i("el-input"),H=i("el-dialog"),K=d("loading");return p(),a("div",b,[s(Y,{class:"task-card"},{header:t((()=>[g("div",_,[l[9]||(l[9]=g("h2",null,"我的任务",-1)),g("div",C,[s(I,{type:"primary",onClick:o.refreshData},{default:t((()=>[s(w,null,{default:t((()=>[s(v)])),_:1}),l[7]||(l[7]=c(" 刷新 "))])),_:1},8,["onClick"]),s(I,{type:"success",onClick:o.createTask},{default:t((()=>[s(w,null,{default:t((()=>[s(x)])),_:1}),l[8]||(l[8]=c(" 新建任务 "))])),_:1},8,["onClick"])])])])),default:t((()=>[g("div",V,[s(R,{inline:!0,model:o.filterForm,class:"filter-form"},{default:t((()=>[s(A,{label:"状态"},{default:t((()=>[s(z,{modelValue:o.filterForm.status,"onUpdate:modelValue":l[0]||(l[0]=e=>o.filterForm.status=e),placeholder:"任务状态",clearable:""},{default:t((()=>[s(D,{label:"待处理",value:"pending"}),s(D,{label:"进行中",value:"in_progress"}),s(D,{label:"已完成",value:"completed"}),s(D,{label:"已取消",value:"cancelled"})])),_:1},8,["modelValue"])])),_:1}),s(A,{label:"优先级"},{default:t((()=>[s(z,{modelValue:o.filterForm.priority,"onUpdate:modelValue":l[1]||(l[1]=e=>o.filterForm.priority=e),placeholder:"优先级",clearable:""},{default:t((()=>[s(D,{label:"低",value:"low"}),s(D,{label:"中",value:"medium"}),s(D,{label:"高",value:"high"}),s(D,{label:"紧急",value:"urgent"})])),_:1},8,["modelValue"])])),_:1}),s(A,null,{default:t((()=>[s(I,{type:"primary",onClick:o.handleFilter},{default:t((()=>l[10]||(l[10]=[c("筛选")]))),_:1},8,["onClick"]),s(I,{onClick:o.resetFilter},{default:t((()=>l[11]||(l[11]=[c("重置")]))),_:1},8,["onClick"])])),_:1})])),_:1},8,["model"])]),o.loading?(p(),a("div",T,[s(j,{rows:10,animated:""})])):0===o.tasks.length?(p(),a("div",P,[s(N,{description:"暂无任务数据"})])):(p(),a("div",U,[m((p(),f(E,{data:o.tasks,style:{width:"100%"},onRowClick:o.handleRowClick},{default:t((()=>[s(L,{prop:"taskId",label:"ID",width:"70"}),s(L,{prop:"title",label:"任务名称","min-width":"200","show-overflow-tooltip":""}),s(L,{prop:"status",label:"状态",width:"100"},{default:t((e=>[s(M,{type:o.getStatusType(e.row.status)},{default:t((()=>[c(k(o.getStatusText(e.row.status)),1)])),_:2},1032,["type"])])),_:1}),s(L,{prop:"priority",label:"优先级",width:"100"},{default:t((e=>[s(M,{type:o.getPriorityType(e.row.priority),effect:"dark"},{default:t((()=>[c(k(o.getPriorityText(e.row.priority)),1)])),_:2},1032,["type"])])),_:1}),s(L,{prop:"dueDate",label:"截止日期",width:"120"}),s(L,{prop:"progress",label:"进度",width:"180"},{default:t((e=>[s($,{percentage:e.row.progress||0,status:o.getProgressStatus(e.row)},null,8,["percentage","status"])])),_:1}),s(L,{label:"负责人",width:"160"},{default:t((e=>[s(q,{users:o.getAllAssignees(e.row),"is-main-user-primary":!0,"max-users":4,"avatar-size":"18",overlap:6,class:"small"},null,8,["users"])])),_:1}),s(L,{prop:"creatorName",label:"创建人",width:"120"}),s(L,{label:"操作",width:"180",fixed:"right"},{default:t((e=>[s(I,{size:"small",type:"primary",onClick:y((a=>o.viewTaskDetail(e.row)),["stop"])},{default:t((()=>l[12]||(l[12]=[c(" 详情 ")]))),_:2},1032,["onClick"]),"completed"!==e.row.status?(p(),f(I,{key:0,size:"small",type:"success",onClick:y((a=>o.updateProgress(e.row)),["stop"])},{default:t((()=>l[13]||(l[13]=[c(" 更新进度 ")]))),_:2},1032,["onClick"])):h("",!0)])),_:1})])),_:1},8,["data","onRowClick"])),[[K,o.tableLoading]]),g("div",F,[s(J,{background:"",layout:"total, sizes, prev, pager, next","page-sizes":[10,20,50,100],total:o.total,"page-size":o.pageSize,"current-page":o.currentPage,onSizeChange:o.handleSizeChange,onCurrentChange:o.handleCurrentChange},null,8,["total","page-size","current-page","onSizeChange","onCurrentChange"])])]))])),_:1}),s(H,{modelValue:o.progressDialogVisible,"onUpdate:modelValue":l[6]||(l[6]=e=>o.progressDialogVisible=e),title:"更新任务进度",width:"500px","close-on-click-modal":!1},{footer:t((()=>[g("span",S,[s(I,{onClick:l[5]||(l[5]=e=>o.progressDialogVisible=!1)},{default:t((()=>l[14]||(l[14]=[c("取消")]))),_:1}),s(I,{type:"primary",onClick:o.submitProgressUpdate,loading:o.submitting},{default:t((()=>l[15]||(l[15]=[c(" 确认 ")]))),_:1},8,["onClick","loading"])])])),default:t((()=>[s(R,{model:o.progressForm,"label-width":"100px"},{default:t((()=>[s(A,{label:"当前进度"},{default:t((()=>[s(B,{modelValue:o.progressForm.progress,"onUpdate:modelValue":l[2]||(l[2]=e=>o.progressForm.progress=e),"format-tooltip":o.formatProgressTooltip,step:5,"show-stops":""},null,8,["modelValue","format-tooltip"])])),_:1}),s(A,{label:"状态"},{default:t((()=>[s(z,{modelValue:o.progressForm.status,"onUpdate:modelValue":l[3]||(l[3]=e=>o.progressForm.status=e),placeholder:"选择状态"},{default:t((()=>[s(D,{label:"待处理",value:"pending"}),s(D,{label:"进行中",value:"in_progress"}),s(D,{label:"已完成",value:"completed"})])),_:1},8,["modelValue"])])),_:1}),s(A,{label:"备注"},{default:t((()=>[s(G,{modelValue:o.progressForm.remarks,"onUpdate:modelValue":l[4]||(l[4]=e=>o.progressForm.remarks=e),type:"textarea",rows:3,placeholder:"请输入进度更新备注"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}],["__scopeId","data-v-ea0fc743"]]);export{I as default};
