import{_ as a,r as e,j as s,m as l,q as t,b as n,e as i,w as o,ba as c,a8 as u,a as r,o as v,d,A as g,t as f,a9 as y,F as p,h as m,i as h,Y as w}from"./index-C7OOw0MO.js";import{n as _}from"./notification-service-BchTNjTa.js";const S={class:"notification-test-page"},C={class:"card-header"},$={class:"status-section"},k={class:"status-info"},N={class:"test-section"},b={class:"test-controls"},I={class:"log-section"},T={class:"log-header"},R={class:"log-content"},D={class:"log-time"},j={class:"log-type"},J={class:"log-message"},O={key:0,class:"empty-log"},x=a({__name:"NotificationTestPage",setup(a){const x=e(!1),z=e({}),P=e([]),q=e(!1),A=e(!1),E=s(),F=e(0),H=async()=>{try{q.value=!0,V("info","发送测试通知请求...");const a=await c.sendTestNotification();V("success",`测试通知发送成功: ${JSON.stringify(a)}`),u.success("测试通知已发送")}catch(a){V("error",`测试通知发送失败: ${a.message||"未知错误"}`),u.error("发送测试通知失败")}finally{q.value=!1}},L=async()=>{try{q.value=!0,V("info","发送测试事件请求...");const a=await c.sendTestEvent();V("success",`测试事件发送成功: ${JSON.stringify(a)}`),u.success("测试事件已发送")}catch(a){V("error",`测试事件发送失败: ${a.message||"未知错误"}`),u.error("发送测试事件失败")}finally{q.value=!1}},U=async()=>{try{A.value=!0,V("info","检查连接状态...");const e=await c.getConnectionStatus();if(e&&e.success){z.value=e.data,V("success",`连接状态检查成功: ${JSON.stringify(e.data)}`);try{await _.sendTestNotification(F.value),V("info","前端通知服务测试成功")}catch(a){V("warning",`前端通知服务测试失败: ${a.message||"未知错误"}`)}}else V("error",`连接状态检查失败: ${(null==e?void 0:e.message)||"未知错误"}`)}catch(a){z.value={},V("error",`连接状态检查出错: ${a.message||"未知错误"}`),u.error("获取连接状态失败")}finally{A.value=!1}},V=(a,e)=>{P.value.unshift({type:a,message:e,time:new Date}),P.value.length>100&&(P.value=P.value.slice(0,100))},Y=()=>{P.value=[],V("info","日志已清空")};return l((async()=>{V("info","通知测试页面已加载"),F.value=E.userId,V("info",`当前用户ID: ${F.value}`),x.value=_.isConnected||!1,V("info","当前SignalR连接状态: "+(x.value?"已连接":"未连接")),await U(),!x.value&&F.value&&await(async()=>{try{V("info","尝试重新连接SignalR..."),await _.disconnect(),await _.initConnection(F.value),x.value=!0,V("success","SignalR重新连接成功")}catch(a){x.value=!1,V("error",`SignalR重新连接失败: ${a.message||"未知错误"}`)}})();const a=setInterval((()=>{x.value=_.isConnected||!1,void 0===x.value&&(x.value=!1)}),5e3);t((()=>{clearInterval(a)}))})),(a,e)=>{const s=r("el-tag"),l=r("el-divider"),t=r("el-button"),c=r("el-card");return v(),n("div",S,[i(c,{class:"test-card"},{header:o((()=>[d("div",C,[e[2]||(e[2]=d("h2",null,"通知系统测试面板",-1)),x.value?(v(),w(s,{key:0,type:"success"},{default:o((()=>e[0]||(e[0]=[g("连接状态: 正常")]))),_:1})):(v(),w(s,{key:1,type:"danger"},{default:o((()=>e[1]||(e[1]=[g("连接状态: 断开")]))),_:1}))])])),default:o((()=>[d("div",$,[e[8]||(e[8]=d("h3",null,"连接信息",-1)),d("div",k,[d("p",null,[e[3]||(e[3]=d("strong",null,"SignalR状态:",-1)),g(" "+f(x.value?"已连接":"未连接"),1)]),d("p",null,[e[4]||(e[4]=d("strong",null,"服务器时间:",-1)),g(" "+f(z.value.serverTime||"未知"),1)]),d("p",null,[e[5]||(e[5]=d("strong",null,"Hub路径:",-1)),g(" "+f(z.value.hubPath||"/hubs/notification"),1)]),d("p",null,[e[6]||(e[6]=d("strong",null,"消息:",-1)),g(" "+f(z.value.message||"未知"),1)]),d("p",null,[e[7]||(e[7]=d("strong",null,"用户ID:",-1)),g(" "+f(F.value),1)])])]),i(l),d("div",N,[e[12]||(e[12]=d("h3",null,"测试发送通知",-1)),d("div",b,[i(t,{type:"primary",onClick:H,loading:q.value},{default:o((()=>e[9]||(e[9]=[g(" 发送测试通知 ")]))),_:1},8,["loading"]),i(t,{type:"warning",onClick:L,loading:q.value},{default:o((()=>e[10]||(e[10]=[g(" 发送测试事件 ")]))),_:1},8,["loading"]),i(t,{type:"info",onClick:U,loading:A.value},{default:o((()=>e[11]||(e[11]=[g(" 检查连接状态 ")]))),_:1},8,["loading"])])]),i(l),d("div",I,[d("div",T,[e[14]||(e[14]=d("h3",null,"事件日志",-1)),i(t,{type:"default",size:"small",onClick:Y},{default:o((()=>e[13]||(e[13]=[g("清空日志")]))),_:1})]),d("div",R,[(v(!0),n(p,null,m(P.value,((a,e)=>{return v(),n("div",{key:e,class:h(["log-item",a.type])},[d("span",D,f((s=a.time,s.toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit",fractionalSecondDigits:3}))),1),d("span",j,"["+f(a.type.toUpperCase())+"]",1),d("span",J,f(a.message),1)],2);var s})),128)),0===P.value.length?(v(),n("div",O," 暂无日志记录 ")):y("",!0)])])])),_:1})])}}},[["__scopeId","data-v-466c2adf"]]);export{x as default};
