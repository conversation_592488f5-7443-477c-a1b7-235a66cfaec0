<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="智创科技 - 标杆级任务管理平台 v7，集成批量操作、ECharts报表与AI辅助，打造高效协作与极致体验。">
    <title>智创科技 - 任务管理平台 v7</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-plus@2.7.0/dist/index.min.css">
    <script src="https://cdn.jsdelivr.net/npm/vue@3.4.27/dist/vue.global.prod.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-plus@2.7.0/dist/index.full.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-plus@2.7.0/dist/locale/zh-cn.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@element-plus/icons-vue@2.3.1/dist/index.global.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue-draggable-plus@0.4.0/dist/vue-draggable-plus.global.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.5.0/dist/echarts.min.js"></script>
    <script>
        // Fallback for VueDraggablePlus if CDN fails
        if (typeof VueDraggablePlus === 'undefined') {
            console.warn('VueDraggablePlus not loaded from CDN, using fallback stub.');
            window.VueDraggablePlus = {
                name: 'VueDraggableStub',
                render(h) { return h('div', this.$slots.default ? this.$slots.default() : []); }
            };
        }
    </script>
    <style>
        :root {
            --primary-color: #2563eb; /* 更鲜明的蓝色 */
            --primary-color-light: #60a5fa;
            --primary-color-darker: #1d4ed8;
            --success-color: #16a34a;
            --warning-color: #f59e0b;
            --danger-color: #dc2626;
            --info-color: #6b7280;

            --text-primary-light: #111827;
            --text-secondary-light: #4b5563;
            --bg-light: #f3f4f6; /* 更柔和的浅灰 */
            --card-bg-light: #ffffff;
            --border-light: #e5e7eb;
            --hover-bg-light: #e0e7ff; /* 柔和的靛蓝悬停 */

            --text-primary-dark: #f3f4f6;
            --text-secondary-dark: #9ca3af;
            --bg-dark: #111827; /* 深邃的暗色 */
            --card-bg-dark: #1f2937; /* 卡片暗色 */
            --border-dark: #374151;
            --hover-bg-dark: #312e81; /* 较深的靛蓝悬停 */
        }

        .dark {
            --text-primary: var(--text-primary-dark);
            --text-secondary: var(--text-secondary-dark);
            --bg-app: var(--bg-dark);
            --bg-card: var(--card-bg-dark);
            --border-color: var(--border-dark);
            --hover-bg-color: var(--hover-bg-dark);
        }

        html { scroll-behavior: smooth; font-size: 15px; } /* 略微减小基础字号 */
        body {
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
            background-color: var(--bg-app, var(--bg-light));
            color: var(--text-primary, var(--text-primary-light));
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        [v-cloak] { display: none; }

        #app { @apply min-h-screen flex flex-col; }

        .header {
            @apply bg-gray-900 dark:bg-opacity-80 dark:backdrop-blur-md px-4 sm:px-6 py-3 flex items-center justify-between shadow-xl fixed top-0 left-0 right-0 z-50;
        }
        .logo { @apply text-white text-2xl font-bold hover:opacity-90 transition-opacity flex items-center; }
        .logo .el-icon { @apply mr-2 text-3xl; }

        .sidebar {
            @apply w-60 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex-shrink-0 p-4 transition-all duration-300 ease-in-out fixed top-16 left-0 h-[calc(100vh-4rem)] overflow-y-auto;
        }
        .sidebar.collapsed { @apply -translate-x-full md:w-16 md:translate-x-0; }
        .sidebar.collapsed .menu-text { @apply hidden md:hidden; }
        .sidebar.collapsed .menu-item { @apply justify-center; }


        .main-content {
            @apply flex-1 p-4 sm:p-6 overflow-y-auto transition-all duration-300 ease-in-out mt-16;
            margin-left: 15rem; /* sidebar width */
        }
        .main-content.sidebar-collapsed {
            margin-left: 0rem; /* Adjust when sidebar is collapsed for mobile, or 4rem for desktop collapsed */
        }
        @screen md {
            .main-content.sidebar-collapsed {
                 margin-left: 4rem; /* desktop collapsed sidebar width */
            }
        }


        .menu-item { @apply flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer hover:bg-indigo-100 dark:hover:bg-indigo-700/30 transition-colors text-gray-700 dark:text-gray-300; }
        .menu-item .el-icon { @apply text-xl; }
        .menu-item.active { @apply bg-indigo-500 text-white dark:bg-indigo-600; }
        .menu-item.active .el-icon { @apply text-white; }

        .kanban-board { @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-5; }
        .task-column {
            @apply bg-gray-100 dark:bg-gray-700/60 rounded-xl p-4 border border-gray-200 dark:border-gray-600/50 min-h-[400px] flex flex-col shadow-sm;
            transition: var(--transition);
        }
        .task-column-header { @apply flex justify-between items-center mb-4 pb-2 border-b border-gray-300 dark:border-gray-500; }
        .task-column-title { @apply text-lg font-semibold text-gray-800 dark:text-gray-100; }
        .task-column .el-icon { @apply mr-1; }

        .task-card {
            @apply bg-white dark:bg-gray-800 rounded-lg p-3.5 mb-3 shadow-md hover:shadow-lg transition-all cursor-grab relative;
            border-left-width: 5px;
        }
        .task-card:hover { transform: translateY(-2px) scale(1.01); }
        .task-card.priority-high { border-left-color: var(--danger-color); }
        .task-card.priority-medium { border-left-color: var(--warning-color); }
        .task-card.priority-low { border-left-color: var(--success-color); }
        .task-card.selected { @apply ring-2 ring-offset-1 ring-[var(--primary-color)] dark:ring-offset-gray-800; }
        .task-card-checkbox { @apply absolute top-2 right-2 z-10; }

        .task-preview-popup {
            @apply fixed bg-white dark:bg-gray-800 rounded-xl p-5 shadow-2xl w-96 z-[60] border border-gray-200 dark:border-gray-600;
            animation: fadeIn 0.15s ease-out;
        }
        .notification-center-panel {
            @apply fixed top-20 right-4 w-96 max-h-[calc(100vh-6rem)] overflow-y-auto z-[70] bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-600;
            animation: slideInRight 0.3s ease-out;
        }
        .notification-item { @apply p-3 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors cursor-pointer; }
        .notification-item.unread { @apply border-l-4 border-[var(--primary-color)]; }

        .mention-suggestions-list { @apply absolute bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md max-h-48 overflow-y-auto z-[100] w-auto min-w-[200px] shadow-lg; }
        .mention-suggestion-item { @apply flex items-center gap-2 p-2 hover:bg-indigo-50 dark:hover:bg-indigo-700/30 cursor-pointer text-sm; }

        .ai-suggestion-btn { @apply text-xs ml-2 px-1.5 py-0.5 rounded border border-purple-400 text-purple-600 dark:text-purple-300 dark:border-purple-500 hover:bg-purple-50 dark:hover:bg-purple-700/30; }
        .ai-suggestion-btn .el-icon { @apply mr-1; }

        .batch-actions-bar {
            @apply fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 p-3 shadow-[0_-2px_10px_rgba(0,0,0,0.1)] z-40 flex items-center justify-between transition-transform duration-300 ease-out;
            transform: translateY(100%);
        }
        .batch-actions-bar.visible { transform: translateY(0%); }

        /* Chart container */
        .chart-container { @apply bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-xl shadow-lg; min-height: 350px; }

        @keyframes fadeIn { from { opacity: 0; transform: scale(0.98); } to { opacity: 1; transform: scale(1); } }
        @keyframes slideInRight { from { opacity: 0; transform: translateX(20px); } to { opacity: 1; transform: translateX(0); } }

        /* Element Plus overrides for dark theme consistency */
        .dark .el-dialog, .dark .el-message-box, .dark .el-drawer {
            --el-dialog-bg-color: var(--card-bg-dark);
            --el-text-color-primary: var(--text-primary-dark);
            --el-text-color-regular: var(--text-secondary-dark);
            --el-border-color-lighter: var(--border-dark);
        }
        .dark .el-input__inner, .dark .el-textarea__inner, .dark .el-select .el-input__inner {
            background-color: var(--card-bg-dark) !important;
            color: var(--text-primary-dark) !important;
            border-color: var(--border-dark) !important;
        }
        .dark .el-input__wrapper.is-focus, .dark .el-textarea__inner:focus, .dark .el-select .el-input.is-focus .el-input__wrapper {
            box-shadow: 0 0 0 1px var(--primary-color) inset !important;
        }
        .dark .el-button.is-text { color: var(--primary-color-light); }
        .dark .el-button.is-text:hover { color: var(--primary-color); }
        .dark .el-tabs__item { color: var(--text-secondary-dark); }
        .dark .el-tabs__item.is-active { color: var(--primary-color); }
        .dark .el-tabs__nav-wrap::after { background-color: var(--border-dark); }
        .dark .el-progress-bar__inner { background-color: var(--primary-color) !important; }
        .dark .el-loading-mask { background-color: rgba(31, 41, 55, 0.8); }
        .dark .el-skeleton__item { background: #4b5563; }

        /* Custom scrollbar for draggable area */
        .task-column .el-scrollbar__wrap { padding-right: 5px; /* Make space for scrollbar */ }
        .task-column ::-webkit-scrollbar { width: 6px; }
        .task-column ::-webkit-scrollbar-thumb { background: #a0aec0; border-radius: 3px; }
        .dark .task-column ::-webkit-scrollbar-thumb { background: #4a5568; }

    </style>
</head>
<body>
<div id="app" v-cloak :class="{ dark: isDarkTheme }">
    <header class="header">
        <div class="flex items-center">
            <el-button text circle @click="toggleSidebar" class="mr-2 text-white hidden md:inline-flex text-xl"><el-icon><Operation /></el-icon></el-button>
            <a href="#" class="logo"><el-icon><OfficeBuilding /></el-icon>智创科技</a>
        </div>
        <div class="flex items-center gap-3 sm:gap-4">
            <el-tooltip :content="isDarkTheme ? '切换明亮模式' : '切换暗黑模式'" placement="bottom">
                <el-button circle @click="toggleTheme" class="!text-xl !p-2">
                    <el-icon v-if="isDarkTheme"><Sunny /></el-icon>
                    <el-icon v-else><Moon /></el-icon>
                </el-button>
            </el-tooltip>
            <el-dropdown trigger="click" @command="handleUserCommand">
                <span class="flex items-center cursor-pointer">
                    <img :src="currentUser.avatar" class="w-8 h-8 rounded-full border-2 border-gray-300 dark:border-gray-600" :alt="currentUser.name" @error="onAvatarError">
                    <span class="text-white ml-2 hidden sm:inline">{{ currentUser.name }}</span>
                    <el-icon class="el-icon--right text-white hidden sm:inline"><arrow-down /></el-icon>
                </span>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item command="profile"><el-icon><User /></el-icon>个人资料</el-dropdown-item>
                        <el-dropdown-item command="settings"><el-icon><Setting /></el-icon>系统设置</el-dropdown-item>
                        <el-dropdown-item command="logout" divided><el-icon><SwitchButton /></el-icon>退出登录</el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
             <el-tooltip content="通知中心" placement="bottom">
                <el-badge :value="unreadNotificationsCount" :max="99" class="item" :hidden="unreadNotificationsCount === 0">
                    <el-button circle @click="showNotificationCenter = !showNotificationCenter" class="!text-xl !p-2"><el-icon><Bell /></el-icon></el-button>
                </el-badge>
            </el-tooltip>
        </div>
    </header>

    <div class="flex flex-1 pt-16"> <aside class="sidebar" :class="{ collapsed: isSidebarCollapsed }">
            <div class="space-y-2 mt-2">
                <div class="menu-item" :class="{ active: activeMenu === 'dashboard' }" @click="navigateTo('dashboard')" :title="isSidebarCollapsed ? '任务看板' : ''">
                    <el-icon><DataBoard /></el-icon><span class="menu-text">任务看板</span>
                </div>
                <div class="menu-item" :class="{ active: activeMenu === 'analytics' }" @click="navigateTo('analytics')" :title="isSidebarCollapsed ? '数据分析' : ''">
                    <el-icon><TrendCharts /></el-icon><span class="menu-text">数据分析</span>
                </div>
                <div class="menu-item" :class="{ active: activeMenu === 'my-tasks' }" @click="navigateTo('my-tasks')" :title="isSidebarCollapsed ? '我的任务' : ''">
                    <el-icon><Tickets /></el-icon><span class="menu-text">我的任务</span>
                </div>
                 <div class="menu-item" :class="{ active: activeMenu === 'team' }" @click="navigateTo('team')" :title="isSidebarCollapsed ? '团队管理' : ''">
                    <el-icon><Avatar /></el-icon><span class="menu-text">团队管理</span>
                </div>
                <div class="menu-item" :class="{ active: activeMenu === 'settings' }" @click="navigateTo('settings')" :title="isSidebarCollapsed ? '系统设置' : ''">
                    <el-icon><Setting /></el-icon><span class="menu-text">系统设置</span>
                </div>
            </div>
        </aside>

        <main class="main-content" :class="{ 'sidebar-collapsed': isSidebarCollapsed }">
            <div class="mb-4 text-sm text-gray-500 dark:text-gray-400 flex items-center">
                <span class="hover:text-[var(--primary-color)] cursor-pointer" @click="navigateTo('dashboard')">部门管理</span>
                <el-icon class="mx-1"><ArrowRightBold /></el-icon>
                <strong class="text-gray-700 dark:text-gray-200">{{ currentViewTitle }}</strong>
            </div>

            <div v-if="activeMenu === 'dashboard'" class="animate-fadeIn">
                <div class="bg-white dark:bg-gray-800 rounded-xl p-4 sm:p-6 mb-6 shadow-lg">
                    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                        <div>
                            <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">项目任务看板</h1>
                            <p class="text-gray-600 dark:text-gray-400 mt-1">拖拽任务卡片以更新状态，高效协作管理您的项目。</p>
                        </div>
                        <el-button type="primary" size="large" @click="openCreateDialog(null)" class="mt-4 sm:mt-0"><el-icon><Plus /></el-icon> 创建新任务</el-button>
                    </div>
                </div>
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mb-6 p-4 bg-white dark:bg-gray-800 rounded-xl shadow-md">
                    <el-input v-model="searchQuery" placeholder="搜索任务标题/描述..." clearable @input="debouncedFilterTasks" size="default">
                        <template #prefix><el-icon><Search /></el-icon></template>
                    </el-input>
                    <el-select v-model="filter.status" placeholder="任务状态" clearable @change="filterTasks" size="default" class="w-full">
                        <el-option label="所有状态" value=""></el-option>
                        <el-option v-for="status in taskStatusOptions" :key="status" :label="status" :value="status"></el-option>
                    </el-select>
                    <el-select v-model="filter.assigneeId" placeholder="负责人" clearable @change="filterTasks" filterable size="default" class="w-full">
                        <el-option label="所有负责人" value=""></el-option>
                        <el-option v-for="member in teamMembers" :key="member.id" :label="member.name" :value="member.id"></el-option>
                    </el-select>
                    <el-select v-model="filter.priority" placeholder="优先级" clearable @change="filterTasks" size="default" class="w-full">
                        <el-option label="所有优先级" value=""></el-option>
                        <el-option label="高" value="high"></el-option><el-option label="中" value="medium"></el-option><el-option label="低" value="low"></el-option>
                    </el-select>
                </div>

                <div v-if="isLoading" class="animate-pulse"><el-skeleton :rows="12" animated /></div>
                <div class="kanban-board" v-else>
                    <div v-for="column in taskColumns" :key="column.status" class="task-column">
                        <div class="task-column-header">
                            <h4 class="task-column-title"><el-icon><component :is="column.icon" /></el-icon>{{ column.title }}</h4>
                            <span class="bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300 text-xs font-semibold px-2 py-1 rounded-full">{{ column.tasks.length }}</span>
                        </div>
                        <el-scrollbar height="calc(100vh - 25rem)" class="flex-grow">
                            <VueDraggable v-model="column.tasks" :animation="250" group="tasks" item-key="id" class="space-y-3 min-h-[50px] py-1" ghost-class="opacity-50" drag-class="ring-2 ring-[var(--primary-color)]" @start="onDragStart" @end="onDragEnd($event, column.status)">
                                <div v-for="task in column.tasks" :key="task.id" class="task-card" :class="[`priority-${task.priority}`, { selected: selectedTaskIds.includes(task.id) }]" @mouseenter="showPreview(task, $event)" @mouseleave="hidePreview" @click.stop="toggleTaskSelection(task.id, $event)">
                                    <el-checkbox :model-value="selectedTaskIds.includes(task.id)" @change.stop="toggleTaskSelection(task.id, $event)" class="task-card-checkbox" size="small"></el-checkbox>
                                    <div @click.stop="showTaskDetail(task)" class="cursor-pointer">
                                        <div class="font-semibold text-gray-800 dark:text-gray-100 mb-1.5 text-base line-clamp-2">{{ task.title }}</div>
                                        <div class="flex justify-between items-center text-xs text-gray-500 dark:text-gray-400 mb-2">
                                            <div class="flex items-center -space-x-2">
                                                <img v-for="id in task.assigneeIds.slice(0, 3)" :key="id" :src="getAvatarUrl(id)" class="w-6 h-6 rounded-full border-2 border-white dark:border-gray-800" :alt="getMemberNameById(id)" @error="onAvatarError" :title="getMemberNameById(id)">
                                                <span v-if="task.assigneeIds.length > 3" class="flex items-center justify-center w-6 h-6 text-xs bg-gray-200 dark:bg-gray-600 rounded-full border-2 border-white dark:border-gray-800">+{{ task.assigneeIds.length - 3 }}</span>
                                            </div>
                                            <span :class="['px-1.5 py-0.5 rounded-full font-medium', `priority-tag-${task.priority}`]">{{ priorityLabel(task.priority) }}</span>
                                        </div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400 mb-2 flex items-center">
                                            <el-icon class="mr-1"><Clock /></el-icon>
                                            <span :class="{ 'text-red-500 dark:text-red-400 font-semibold': task.isOverdue && task.status !== '已完成' }">
                                                {{ task.status === '已完成' ? '完成于' : task.isOverdue ? '已逾期' : '截止于' }}: {{ formatDate(task.status === '已完成' ? task.completedDate : task.endDate, false) }}
                                            </span>
                                        </div>
                                        <el-progress v-if="task.status !== '待办'" :percentage="task.progress" :color="getTaskProgressColor(task)" :status="getTaskProgressStatus(task)" :stroke-width="6" class="mt-1" />
                                    </div>
                                </div>
                            </VueDraggable>
                             <div v-if="!column.tasks.length" class="text-center text-gray-400 dark:text-gray-500 mt-10 py-6">
                                <el-icon :size="32" class="opacity-50"><Files /></el-icon>
                                <p class="mt-2 text-sm">暂无任务</p>
                            </div>
                        </el-scrollbar>
                    </div>
                </div>
            </div>

            <div v-if="activeMenu === 'analytics'" class="animate-fadeIn space-y-6">
                <div class="bg-white dark:bg-gray-800 rounded-xl p-4 sm:p-6 shadow-lg">
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">数据分析中心</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">洞察任务趋势，优化团队效能。</p>
                </div>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="chart-container">
                        <h3 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-100">任务状态分布</h3>
                        <div ref="taskStatusChart" style="width: 100%; height: 300px;"></div>
                    </div>
                    <div class="chart-container">
                        <h3 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-100">任务优先级分布</h3>
                         <div ref="taskPriorityChart" style="width: 100%; height: 300px;"></div>
                    </div>
                </div>
                <div class="chart-container">
                    <h3 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-100">任务趋势 (近7日创建与完成)</h3>
                    <div ref="taskTrendChart" style="width: 100%; height: 320px;"></div>
                </div>
                 <div class="chart-container">
                    <h3 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-100">成员任务负载 (进行中)</h3>
                    <div ref="memberLoadChart" style="width: 100%; height: 320px;"></div>
                </div>
            </div>
            
            <div v-if="activeMenu !== 'dashboard' && activeMenu !== 'analytics'" class="animate-fadeIn bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg text-center">
                 <el-empty :description="`“${currentViewTitle}” 页面正在加速建设中...`" :image-size="150"></el-empty>
            </div>


            <div v-if="previewTask" class="task-preview-popup" :style="{ top: previewPosition.top + 'px', left: previewPosition.left + 'px' }">
                <div class="flex justify-between items-center mb-2">
                    <h4 class="font-semibold text-gray-900 dark:text-white text-lg">{{ previewTask.title }}</h4>
                    <span :class="['px-2 py-0.5 rounded-full text-xs font-medium', `priority-tag-${previewTask.priority}`]">{{ priorityLabel(previewTask.priority) }}</span>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-300 line-clamp-3 mb-3">{{ previewTask.description || '暂无描述信息。' }}</p>
                <div class="text-xs text-gray-500 dark:text-gray-400 space-y-1">
                    <p><el-icon class="mr-1"><User /></el-icon>负责人: {{ previewTask.assigneeIds.map(id => getMemberNameById(id)).join(', ') || '未分配' }}</p>
                    <p><el-icon class="mr-1"><Clock /></el-icon>截止: {{ formatDate(previewTask.endDate, false) }}</p>
                    <p><el-icon class="mr-1"><CollectionTag /></el-icon>状态: {{ previewTask.status }}</p>
                </div>
                <div class="flex gap-2 mt-4">
                    <el-button size="small" type="primary" @click="showTaskDetail(previewTask); hidePreview();">查看详情</el-button>
                    <el-button size="small" @click="editTask(previewTask); hidePreview();">快速编辑</el-button>
                </div>
            </div>
        </main>
    </div>

    <transition name="el-fade-in">
        <div v-if="showNotificationCenter" class="notification-center-panel">
            <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                <h4 class="text-lg font-semibold text-gray-800 dark:text-gray-100">通知中心</h4>
                <el-button link type="primary" @click="markAllNotificationsRead" v-if="unreadNotificationsCount > 0">全部已读</el-button>
            </div>
            <el-scrollbar height="calc(100% - 57px)">
                <div v-if="notifications.length === 0" class="text-center p-6 text-gray-400 dark:text-gray-500">
                    <el-icon :size="40" class="opacity-50"><Bell /></el-icon>
                    <p class="mt-2">暂无通知</p>
                </div>
                <div v-else>
                    <div v-for="notification in notifications" :key="notification.id" class="notification-item" :class="{ unread: !notification.read }" @click="handleNotificationClick(notification)">
                        <div class="flex items-start gap-2">
                            <el-icon :size="18" class="mt-0.5" :class="notification.type === 'mention' ? 'text-purple-500' : 'text-[var(--primary-color)]'">
                                <ChatDotRound v-if="notification.type === 'comment'" />
                                <UserFilled v-else-if="notification.type === 'mention'" />
                                <WarningFilled v-else-if="notification.type === 'overdue'" class="text-red-500" />
                                <SuccessFilled v-else-if="notification.type === 'completed'" class="text-green-500" />
                                <InfoFilled v-else />
                            </el-icon>
                            <div>
                                <p class="font-medium text-sm text-gray-800 dark:text-gray-100">{{ notification.title }}</p>
                                <p class="text-xs text-gray-600 dark:text-gray-300 mt-0.5 line-clamp-2">{{ notification.message }}</p>
                                <small class="text-xs text-gray-400 dark:text-gray-500">{{ timeAgo(notification.createdAt) }}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </el-scrollbar>
        </div>
    </transition>

    <div class="batch-actions-bar" :class="{ visible: selectedTaskIds.length > 0 }">
        <span class="text-sm font-medium text-gray-700 dark:text-gray-200">已选择 {{ selectedTaskIds.length }} 项任务</span>
        <div class="flex items-center gap-2">
            <el-button size="small" @click="openBatchAssignDialog"><el-icon><User /></el-icon>批量分配</el-button>
            <el-button size="small" @click="openBatchStatusDialog"><el-icon><Switch /></el-icon>批量改状态</el-button>
            <el-button size="small" type="danger" @click="confirmBatchDeleteTasks"><el-icon><Delete /></el-icon>批量删除</el-button>
            <el-button size="small" @click="clearSelection">取消选择</el-button>
        </div>
    </div>

    <TaskFormDialog 
        :visible="showCreateDialog" 
        :edit-mode="editMode" 
        :task-data="taskForm"
        :team-members="teamMembers"
        :current-user-id="currentUser.id"
        @close="showCreateDialog = false"
        @save="saveTask"
    />
    <TaskDetailDialog
        :visible="showDetailDialog"
        :task="selectedTask"
        :team-members="teamMembers"
        :current-user="currentUser"
        @close="showDetailDialog = false"
        @edit="editTask"
        @delete="confirmDeleteTask"
        @comment="submitComment"
        @update-task="updateTaskFromDetail"
    />
    <BatchAssignDialog
        :visible="showBatchAssignDialog"
        :team-members="teamMembers"
        @close="showBatchAssignDialog = false"
        @assign="handleBatchAssign"
    />
     <BatchStatusDialog
        :visible="showBatchStatusDialog"
        :task-status-options="taskStatusOptions"
        @close="showBatchStatusDialog = false"
        @update="handleBatchUpdateStatus"
    />

</div>

<script>
// Helper function for debounce, outside Vue app
function debounce(fn, wait) {
    let timeout;
    return function(...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => fn.apply(this, args), wait);
    };
}
// Helper function for timeAgo
function timeAgo(timestamp) {
    const now = new Date();
    const past = new Date(timestamp);
    const diffInSeconds = Math.floor((now - past) / 1000);

    if (diffInSeconds < 60) return `${diffInSeconds} 秒前`;
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) return `${diffInMinutes} 分钟前`;
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} 小时前`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} 天前`;
    return past.toLocaleDateString('zh-CN', { year: 'numeric', month: 'short', day: 'numeric' });
}


document.addEventListener('DOMContentLoaded', function() {
    const { createApp, reactive, ref, computed, onMounted, watch, nextTick, toRefs } = Vue;
    const { ElMessage, ElMessageBox, ElNotification } = ElementPlus;

    // --- Task Form Dialog Component ---
    const TaskFormDialog = {
        props: ['visible', 'editMode', 'taskData', 'teamMembers', 'currentUserId'],
        emits: ['close', 'save'],
        setup(props, { emit }) {
            const formRef = ref(null);
            const localTaskData = ref({});
            const saving = ref(false);
            const { theme } = Vue.inject('theme', { theme: ref('light') }); // Inject theme

            const taskStatusOptions = ['待办', '进行中', '已完成', '已暂停']; // Exclude '已逾期' from manual selection

            watch(() => props.visible, (newVal) => {
                if (newVal) {
                    localTaskData.value = JSON.parse(JSON.stringify(props.taskData)); // Deep copy
                     // Ensure assigneeIds is an array
                    if (!Array.isArray(localTaskData.value.assigneeIds)) {
                        localTaskData.value.assigneeIds = localTaskData.value.assigneeIds ? [localTaskData.value.assigneeIds] : [];
                    }
                }
            });
            
            const rules = {
                title: [{ required: true, message: '请输入任务标题', trigger: 'blur' }, { min: 3, max: 100, message: '长度在 3 到 100 个字符', trigger: 'blur' }],
                assigneeIds: [{ type: 'array', required: true, message: '请至少选择一个负责人', trigger: 'change' }],
                priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
                endDate: [{ required: true, message: '请选择截止日期', trigger: 'change' },
                          { validator: (rule, value, callback) => {
                              if (localTaskData.value.startDate && new Date(value) < new Date(localTaskData.value.startDate)) {
                                  callback(new Error('截止日期不能早于开始日期'));
                              } else {
                                  callback();
                              }
                            }, trigger: 'change'
                          }],
                status: [{ required: true, message: '请选择任务状态', trigger: 'change' }],
            };

            const aiGenerateDescription = () => {
                // Simulate AI generating description
                const keywords = localTaskData.value.title.split(' ').slice(0, 3).join(', ');
                localTaskData.value.description = `针对任务"${localTaskData.value.title}"，我们计划通过以下步骤完成：\n1. 详细分析需求 (${keywords})。\n2. 设计初步方案并进行评审。\n3. 开发和测试。\n4. 最终交付。请 @相关负责人 确认。`;
                ElMessage.success({ message: 'AI已生成描述建议!', customClass: theme.value === 'dark' ? 'el-message--dark' : '' });
            };

            const aiSuggestPriority = () => {
                // Simulate AI suggesting priority based on title
                const title = localTaskData.value.title.toLowerCase();
                if (title.includes('紧急') || title.includes('重要') || title.includes('立即')) {
                    localTaskData.value.priority = 'high';
                } else if (title.includes('修复') || title.includes('bug')) {
                    localTaskData.value.priority = 'medium';
                } else {
                    localTaskData.value.priority = 'low';
                }
                ElMessage.info({ message: `AI建议优先级: ${props.editMode ? localTaskData.value.priority : 'low'}`, customClass: theme.value === 'dark' ? 'el-message--dark' : '' });
            };
             const aiSuggestWorkload = () => {
                const complexityFactor = (localTaskData.value.description?.length || 0) > 100 ? 1.5 : 1;
                const titleFactor = localTaskData.value.title?.length > 20 ? 1.2 : 1;
                const suggestedHours = Math.round((Math.random() * 10 + 5) * complexityFactor * titleFactor);
                localTaskData.value.estimatedWorkload = suggestedHours;
                ElMessage.info({ message: `AI预估工时: ${suggestedHours}小时`, customClass: theme.value === 'dark' ? 'el-message--dark' : '' });
            };


            const handleSave = async () => {
                try {
                    await formRef.value.validate();
                    saving.value = true;
                    // Simulate save
                    setTimeout(() => {
                        emit('save', { ...localTaskData.value, creatorId: props.editMode ? localTaskData.value.creatorId : props.currentUserId });
                        saving.value = false;
                        emit('close');
                    }, 500);
                } catch (error) {
                    console.log('Form validation failed:', error);
                }
            };

            return { formRef, localTaskData, rules, handleSave, saving, aiGenerateDescription, aiSuggestPriority, aiSuggestWorkload, taskStatusOptions, emit };
        },
        template: `
            <el-dialog :model-value="visible" :title="editMode ? '编辑任务' : '创建新任务'" width="clamp(300px, 90vw, 700px)" top="5vh" :before-close="() => emit('close')" destroy-on-close draggable append-to-body>
                <el-form :model="localTaskData" :rules="rules" ref="formRef" label-width="100px" label-position="right">
                    <el-form-item label="任务标题" prop="title">
                        <el-input v-model="localTaskData.title" placeholder="请输入任务标题" maxlength="100" show-word-limit clearable />
                    </el-form-item>
                    <el-form-item label="任务描述" prop="description">
                        <el-input v-model="localTaskData.description" type="textarea" :rows="4" placeholder="请输入详细描述，支持 @用户" maxlength="1000" show-word-limit />
                        <el-button text type="primary" size="small" @click="aiGenerateDescription" class="ai-suggestion-btn mt-1"><el-icon><MagicStick /></el-icon>AI生成描述</el-button>
                    </el-form-item>
                    <el-row :gutter="20">
                        <el-col :xs="24" :sm="12">
                            <el-form-item label="优先级" prop="priority">
                                <el-select v-model="localTaskData.priority" placeholder="选择优先级" class="w-full">
                                    <el-option label="高" value="high"></el-option><el-option label="中" value="medium"></el-option><el-option label="低" value="low"></el-option>
                                </el-select>
                                <el-button text type="primary" size="small" @click="aiSuggestPriority" class="ai-suggestion-btn"><el-icon><Opportunity /></el-icon>AI建议</el-button>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12">
                            <el-form-item label="负责人" prop="assigneeIds">
                                <el-select v-model="localTaskData.assigneeIds" multiple filterable placeholder="选择负责人" class="w-full">
                                    <el-option v-for="member in teamMembers" :key="member.id" :label="member.name" :value="member.id">
                                        <div class="flex items-center gap-2"><img :src="member.avatar" class="w-5 h-5 rounded-full" /> {{ member.name }}</div>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                         <el-col :xs="24" :sm="12">
                            <el-form-item label="开始日期" prop="startDate">
                                <el-date-picker v-model="localTaskData.startDate" type="datetime" placeholder="选择开始日期" class="w-full" format="YYYY-MM-DD HH:mm" value-format="YYYY-MM-DDTHH:mm:ss" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12">
                            <el-form-item label="截止日期" prop="endDate">
                                <el-date-picker v-model="localTaskData.endDate" type="datetime" placeholder="选择截止日期" class="w-full" format="YYYY-MM-DD HH:mm" value-format="YYYY-MM-DDTHH:mm:ss" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                     <el-row :gutter="20">
                        <el-col :xs="24" :sm="12">
                            <el-form-item label="任务状态" prop="status">
                                <el-select v-model="localTaskData.status" placeholder="选择任务状态" class="w-full">
                                    <el-option v-for="s in taskStatusOptions" :key="s" :label="s" :value="s"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12">
                            <el-form-item label="预估工时(h)">
                                <el-input-number v-model="localTaskData.estimatedWorkload" :min="0" :step="0.5" placeholder="小时" class="w-full"/>
                                <el-button text type="primary" size="small" @click="aiSuggestWorkload" class="ai-suggestion-btn"><el-icon><Timer /></el-icon>AI预估</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-form-item label="当前进度 (%)" prop="progress">
                        <el-slider v-model="localTaskData.progress" :min="0" :max="100" show-input />
                    </el-form-item>
                </el-form>
                <template #footer>
                    <el-button @click="() => emit('close')">取消</el-button>
                    <el-button type="primary" @click="handleSave" :loading="saving">{{ saving ? '保存中...' : (editMode ? '更新任务' : '创建任务') }}</el-button>
                </template>
            </el-dialog>
        `
    };

    // --- Task Detail Dialog Component ---
    const TaskDetailDialog = {
        props: ['visible', 'task', 'teamMembers', 'currentUser'],
        emits: ['close', 'edit', 'delete', 'comment', 'updateTask'], // Added updateTask
        setup(props, { emit }) {
            const activeTab = ref('basic');
            const newCommentText = ref('');
            const commentInputRef = ref(null);
            const showMentionSuggestions = ref(false);
            const mentionQuery = ref('');
            const mentionPosition = reactive({ top: 0, left: 0 });
            const { theme } = Vue.inject('theme', { theme: ref('light') });

            const getMember = (id) => props.teamMembers.find(m => m.id === id) || { name: '未知用户', avatar: 'https://placehold.co/24x24/cccccc/ffffff?text=?' };
            
            const renderMentionedText = (text) => {
                if (!text) return '';
                return text.replace(/@\[([^\]]+)\]\(user:([^\)]+)\)/g, (match, name, id) => {
                    return `<span class="font-semibold text-[var(--primary-color)] cursor-pointer hover:underline" title="用户ID: ${id}">@${name}</span>`;
                });
            };

            const handleCommentInput = (event) => {
                const textarea = event.target;
                const text = newCommentText.value;
                const cursorPos = textarea.selectionStart;
                const textBeforeCursor = text.substring(0, cursorPos);
                const lastAtSymbolIndex = textBeforeCursor.lastIndexOf('@');

                if (lastAtSymbolIndex !== -1 && (cursorPos === lastAtSymbolIndex + 1 || textBeforeCursor.charAt(lastAtSymbolIndex + 1) !== '[')) {
                    const query = textBeforeCursor.substring(lastAtSymbolIndex + 1);
                    if (!query.includes(' ') && query.length <= 15) { // Limit query length
                        mentionQuery.value = query;
                        showMentionSuggestions.value = true;
                        
                        // Calculate position for suggestions
                        const rect = textarea.getBoundingClientRect();
                        // This is a simplified position calculation, a more robust solution might involve a library or more complex logic
                        const tempDiv = document.createElement('div');
                        tempDiv.style.visibility = 'hidden';
                        tempDiv.style.position = 'absolute';
                        tempDiv.style.whiteSpace = 'pre-wrap';
                        tempDiv.style.font = window.getComputedStyle(textarea).font;
                        tempDiv.style.width = textarea.clientWidth + 'px';
                        tempDiv.innerHTML = textBeforeCursor.substring(0, lastAtSymbolIndex).replace(/\n/g, '<br/>') + '<span>@</span>';
                        document.body.appendChild(tempDiv);
                        const span = tempDiv.querySelector('span');
                        const atRect = span.getBoundingClientRect();
                        document.body.removeChild(tempDiv);

                        mentionPosition.top = rect.top + (atRect.bottom - rect.top) + 5; // Position below the line with @
                        mentionPosition.left = rect.left + (atRect.left - rect.left);

                    } else {
                        showMentionSuggestions.value = false;
                    }
                } else {
                    showMentionSuggestions.value = false;
                }
            };


            const selectMention = (user) => {
                const textarea = commentInputRef.value?.$el?.querySelector('textarea');
                if (!textarea) return;

                const text = newCommentText.value;
                const cursorPos = textarea.selectionStart;
                const textBeforeCursor = text.substring(0, cursorPos);
                const lastAtSymbolIndex = textBeforeCursor.lastIndexOf('@');
                
                if (lastAtSymbolIndex !== -1) {
                    const beforeAt = text.substring(0, lastAtSymbolIndex);
                    const afterCursor = text.substring(cursorPos);
                    const mentionString = `@[${user.name}](user:${user.id}) `;
                    
                    newCommentText.value = beforeAt + mentionString + afterCursor;
                    
                    nextTick(() => {
                        textarea.focus();
                        textarea.selectionStart = textarea.selectionEnd = beforeAt.length + mentionString.length;
                    });
                }
                showMentionSuggestions.value = false;
            };

            const filteredMentionSuggestions = computed(() => {
                if (!mentionQuery.value) return props.teamMembers.filter(m => m.id !== props.currentUser.id).slice(0,5); // Show some default if no query
                return props.teamMembers.filter(m => 
                    m.id !== props.currentUser.id &&
                    m.name.toLowerCase().includes(mentionQuery.value.toLowerCase())
                ).slice(0,5);
            });

            const submitNewComment = () => {
                if (!newCommentText.value.trim()) {
                     ElMessage.warning({ message: '评论内容不能为空', customClass: theme.value === 'dark' ? 'el-message--dark' : '' });
                    return;
                }
                emit('comment', props.task.id, newCommentText.value);
                newCommentText.value = '';
            };
            
            const updateField = (field, value) => {
                const updatedTask = { ...props.task, [field]: value, updatedAt: new Date().toISOString() };
                // Add to history
                if (!updatedTask.history) updatedTask.history = [];
                updatedTask.history.push({
                    userId: props.currentUser.id,
                    action: `更新 ${field} 为 ${value}`,
                    timestamp: new Date().toISOString()
                });
                emit('updateTask', updatedTask);
            };


            return { 
                activeTab, newCommentText, getMember, renderMentionedText, submitNewComment, 
                commentInputRef, handleCommentInput, showMentionSuggestions, selectMention, filteredMentionSuggestions, mentionPosition,
                updateField, emit
            };
        },
        template: `
            <el-dialog :model-value="visible" :title="'任务详情: ' + task?.title" width="clamp(350px, 90vw, 800px)" top="5vh" :before-close="() => emit('close')" destroy-on-close draggable append-to-body>
                <div v-if="task" class="space-y-3 max-h-[75vh] overflow-y-auto pr-2">
                    <el-descriptions :column="2" border size="small">
                        <el-descriptions-item label="状态">
                            <el-select :model-value="task.status" size="small" @update:modelValue="val => updateField('status', val)" class="w-32">
                                <el-option v-for="s in ['待办', '进行中', '已完成', '已暂停']" :key="s" :label="s" :value="s"></el-option>
                            </el-select>
                        </el-descriptions-item>
                        <el-descriptions-item label="优先级">
                             <el-select :model-value="task.priority" size="small" @update:modelValue="val => updateField('priority', val)" class="w-24">
                                <el-option label="高" value="high"></el-option><el-option label="中" value="medium"></el-option><el-option label="低" value="low"></el-option>
                            </el-select>
                        </el-descriptions-item>
                        <el-descriptions-item label="负责人" :span="2">
                            <el-select :model-value="task.assigneeIds" multiple filterable size="small" @update:modelValue="val => updateField('assigneeIds', val)" class="w-full">
                                <el-option v-for="member in teamMembers" :key="member.id" :label="member.name" :value="member.id">
                                   <div class="flex items-center gap-2"><img :src="member.avatar" class="w-5 h-5 rounded-full" /> {{ member.name }}</div>
                                </el-option>
                            </el-select>
                        </el-descriptions-item>
                        <el-descriptions-item label="创建者">{{ getMember(task.creatorId).name }}</el-descriptions-item>
                        <el-descriptions-item label="创建时间">{{ timeAgo(task.createdAt) }}</el-descriptions-item>
                        <el-descriptions-item label="开始日期">{{ task.startDate ? formatDate(task.startDate, false) : '-' }}</el-descriptions-item>
                        <el-descriptions-item label="截止日期" :class="{'text-red-500 font-semibold': task.isOverdue && task.status !== '已完成'}">
                            {{ task.endDate ? formatDate(task.endDate, false) : '-' }}
                            <el-tag v-if="task.isOverdue && task.status !== '已完成'" type="danger" size="small" class="ml-1">已逾期</el-tag>
                        </el-descriptions-item>
                         <el-descriptions-item label="预估工时(h)">{{ task.estimatedWorkload || '-' }}</el-descriptions-item>
                         <el-descriptions-item label="完成日期">{{ task.status === '已完成' && task.completedDate ? formatDate(task.completedDate, false) : '-' }}</el-descriptions-item>
                    </el-descriptions>
                    
                    <div class="mt-3">
                        <h4 class="text-sm font-semibold mb-1 text-gray-700 dark:text-gray-300">任务描述</h4>
                        <div class="p-3 rounded-md bg-gray-50 dark:bg-gray-700/50 text-sm min-h-[60px]" v-html="renderMentionedText(task.description || '暂无描述')"></div>
                    </div>

                    <div class="mt-3">
                         <h4 class="text-sm font-semibold mb-1 text-gray-700 dark:text-gray-300">当前进度 ({{task.progress}}%)</h4>
                         <el-slider :model-value="task.progress" @update:modelValue="val => updateField('progress', val)" :min="0" :max="100" show-input />
                    </div>

                    <el-tabs v-model="activeTab" class="mt-4">
                        <el-tab-pane label="讨论与动态" name="basic">
                            <div class="space-y-3">
                                <div class="max-h-60 overflow-y-auto pr-1 space-y-3">
                                    <template v-for="item in [...(task.history || []), ...(task.comments || [])].sort((a,b) => new Date(a.timestamp || a.date) - new Date(b.timestamp || b.date))" :key="item.id || item.timestamp">
                                        <div v-if="item.text" class="flex items-start gap-2">
                                            <img :src="getMember(item.userId || item.authorId).avatar" class="w-7 h-7 rounded-full mt-0.5" />
                                            <div class="p-2.5 rounded-lg text-sm w-full" :class="item.userId === currentUser.id ? 'bg-indigo-50 dark:bg-indigo-700/30' : 'bg-gray-100 dark:bg-gray-700/40'">
                                                <div class="flex justify-between items-center mb-0.5">
                                                    <span class="font-semibold">{{ getMember(item.userId || item.authorId).name }}</span>
                                                    <small class="text-gray-400 dark:text-gray-500">{{ timeAgo(item.timestamp || item.date) }}</small>
                                                </div>
                                                <p class="whitespace-pre-wrap" v-html="renderMentionedText(item.text)"></p>
                                            </div>
                                        </div>
                                        <div v-else-if="item.action" class="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1.5 py-1 border-b border-dashed border-gray-200 dark:border-gray-700 last:border-b-0">
                                            <el-icon><RefreshRight /></el-icon>
                                            <span>{{ getMember(item.userId).name }} {{ item.action }} - {{ timeAgo(item.timestamp) }}</span>
                                        </div>
                                    </template>
                                    <el-empty v-if="(!task.history || task.history.length === 0) && (!task.comments || task.comments.length === 0)" description="暂无动态或评论" :image-size="60" />
                                </div>
                                <div class="mt-3 relative">
                                    <el-input v-model="newCommentText" type="textarea" :rows="3" placeholder="输入评论，使用 @ 提及用户..." ref="commentInputRef" @input="handleCommentInput" @blur="() => setTimeout(() => showMentionSuggestions = false, 150)" />
                                    <div v-if="showMentionSuggestions && filteredMentionSuggestions.length > 0" class="mention-suggestions-list" :style="{ top: mentionPosition.top + 'px', left: mentionPosition.left + 'px' }">
                                        <div v-for="member in filteredMentionSuggestions" :key="member.id" class="mention-suggestion-item" @mousedown.prevent="selectMention(member)">
                                            <img :src="member.avatar" class="w-5 h-5 rounded-full" /> {{ member.name }}
                                        </div>
                                    </div>
                                    <el-button type="primary" size="small" @click="submitNewComment" :disabled="!newCommentText.trim()" class="mt-2">发表评论</el-button>
                                </div>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane label="附件" name="attachments">
                            <p class="text-sm text-gray-500">附件功能待实现。</p>
                        </el-tab-pane>
                    </el-tabs>
                </div>
                <el-skeleton :rows="10" animated v-else />
                <template #footer>
                    <el-button @click="() => emit('edit', task)" type="primary" plain><el-icon><EditPen /></el-icon> 编辑任务</el-button>
                    <el-button @click="() => emit('delete', task.id)" type="danger" plain v-if="currentUser.role === 'admin'"><el-icon><Delete /></el-icon> 删除任务</el-button>
                    <el-button @click="() => emit('close')">关闭</el-button>
                </template>
            </el-dialog>
        `
    };
    
    // --- Batch Assign Dialog ---
    const BatchAssignDialog = {
        props: ['visible', 'teamMembers'],
        emits: ['close', 'assign'],
        setup(props, { emit }) {
            const selectedAssigneeIds = ref([]);
            const handleAssign = () => {
                if (selectedAssigneeIds.value.length === 0) {
                    ElMessage.warning('请至少选择一个负责人');
                    return;
                }
                emit('assign', selectedAssigneeIds.value);
                emit('close');
                selectedAssigneeIds.value = [];
            };
            return { selectedAssigneeIds, handleAssign, emit };
        },
        template: `
            <el-dialog :model-value="visible" title="批量分配负责人" width="400px" @close="() => emit('close')" append-to-body>
                <el-select v-model="selectedAssigneeIds" multiple filterable placeholder="选择负责人" class="w-full">
                    <el-option v-for="member in teamMembers" :key="member.id" :label="member.name" :value="member.id">
                        <div class="flex items-center gap-2"><img :src="member.avatar" class="w-5 h-5 rounded-full" /> {{ member.name }}</div>
                    </el-option>
                </el-select>
                <template #footer>
                    <el-button @click="() => emit('close')">取消</el-button>
                    <el-button type="primary" @click="handleAssign">确认分配</el-button>
                </template>
            </el-dialog>
        `
    };

    // --- Batch Status Dialog ---
    const BatchStatusDialog = {
        props: ['visible', 'taskStatusOptions'],
        emits: ['close', 'update'],
        setup(props, { emit }) {
            const selectedStatus = ref('');
            const handleUpdate = () => {
                if (!selectedStatus.value) {
                    ElMessage.warning('请选择目标状态');
                    return;
                }
                emit('update', selectedStatus.value);
                emit('close');
                selectedStatus.value = '';
            };
            return { selectedStatus, handleUpdate, emit };
        },
        template: `
            <el-dialog :model-value="visible" title="批量修改状态" width="400px" @close="() => emit('close')" append-to-body>
                <el-select v-model="selectedStatus" placeholder="选择新的任务状态" class="w-full">
                     <el-option v-for="s in taskStatusOptions" :key="s" :label="s" :value="s"></el-option>
                </el-select>
                <template #footer>
                    <el-button @click="() => emit('close')">取消</el-button>
                    <el-button type="primary" @click="handleUpdate">确认修改</el-button>
                </template>
            </el-dialog>
        `
    };


    const app = createApp({
        components: { TaskFormDialog, TaskDetailDialog, BatchAssignDialog, BatchStatusDialog },
        setup() {
            const tasks = reactive([]);
            const showCreateDialog = ref(false);
            const showDetailDialog = ref(false);
            const showNotificationCenter = ref(false);
            const selectedTask = ref(null);
            const newComment = ref('');
            const editMode = ref(false);
            const isLoading = ref(true);
            const searchQuery = ref('');
            const filter = reactive({ status: '', assigneeId: '', priority: '' });
            const isDarkTheme = ref(localStorage.getItem('智创科技任务平台主题_v7') === 'dark');
            const activeMenu = ref(localStorage.getItem('智创科技任务平台菜单_v7') || 'dashboard');
            const notifications = reactive(JSON.parse(localStorage.getItem('智创科技任务平台通知_v7') || '[]'));
            const previewTask = ref(null);
            const previewPosition = reactive({ top: 0, left: 0 });
            const isSidebarCollapsed = ref(localStorage.getItem('智创科技任务平台侧边栏状态_v7') === 'true');

            // Batch operations
            const selectedTaskIds = ref([]);
            const showBatchAssignDialog = ref(false);
            const showBatchStatusDialog = ref(false);

            // ECharts instances
            const taskStatusChart = ref(null);
            const taskPriorityChart = ref(null);
            const taskTrendChart = ref(null);
            const memberLoadChart = ref(null);


            const teamMembers = [
                { id: 'user-admin', name: '项目管理员', avatar: 'https://placehold.co/40x40/FF8C00/FFF?text=管', role: 'admin' },
                { id: 'user-dev1', name: '开发者-小明', avatar: 'https://placehold.co/40x40/4682B4/FFF?text=明', role: 'developer' },
                { id: 'user-dev2', name: '开发者-小红', avatar: 'https://placehold.co/40x40/32CD32/FFF?text=红', role: 'developer' },
                { id: 'user-design1', name: '设计师-丽丽', avatar: 'https://placehold.co/40x40/FF69B4/FFF?text=丽', role: 'designer' },
                { id: 'user-test1', name: '测试员-阿强', avatar: 'https://placehold.co/40x40/8A2BE2/FFF?text=强', role: 'tester' },
            ];
            const currentUser = ref(teamMembers[0]); // Admin by default

            const taskStatusOptions = ['待办', '进行中', '已完成', '已暂停']; // Exclude '已逾期'

            const defaultTaskForm = () => ({
                id: null, title: '', priority: 'medium', assigneeIds: [], projectType: 'internal',
                startDate: new Date().toISOString().substring(0,10) + "T09:00:00", 
                endDate: new Date(Date.now() + 7 * 86400000).toISOString().substring(0,10) + "T18:00:00",
                progress: 0, status: '待办', description: '', remarks: '', estimatedWorkload: null,
                isOverdue: false, comments: [], history: [], createdAt: new Date().toISOString(), creatorId: currentUser.value.id
            });
            const taskForm = ref(defaultTaskForm());

            const taskColumns = computed(() => {
                const baseColumns = [
                    { status: '待办', title: '待办任务', tasks: [], icon: 'Memo' },
                    { status: '进行中', title: '进行中', tasks: [], icon: 'Loading' },
                    { status: '已完成', title: '已完成', tasks: [], icon: 'SuccessFilled' },
                    { status: '已逾期', title: '已逾期', tasks: [], icon: 'WarningFilled' }
                ];
                return baseColumns.map(col => ({
                    ...col,
                    tasks: filteredTasks.value.filter(t => t.status === col.status)
                                     .sort((a, b) => priorityOrder(b.priority) - priorityOrder(a.priority) || new Date(a.endDate) - new Date(b.endDate))
                }));
            });
            
            const priorityOrder = (priority) => ({ high: 3, medium: 2, low: 1 }[priority] || 0);

            const unreadNotificationsCount = computed(() => notifications.filter(n => !n.read).length);

            // --- LocalStorage Sync ---
            const saveData = () => {
                localStorage.setItem('智创科技任务平台任务_v7', JSON.stringify(tasks));
                localStorage.setItem('智创科技任务平台通知_v7', JSON.stringify(notifications));
            };
            const loadData = () => {
                isLoading.value = true;
                setTimeout(() => {
                    const savedTasks = localStorage.getItem('智创科技任务平台任务_v7');
                    tasks.length = 0; // Clear current tasks before loading
                    if (savedTasks) {
                        tasks.push(...JSON.parse(savedTasks).map(processLoadedTask));
                    } else {
                        loadSampleData(); // Load sample if nothing saved
                    }
                    updateAllOverdueStatus(false); // Initial check without notification
                    isLoading.value = false;
                    initCharts(); // Initialize charts after data is loaded
                }, 500); // Simulate loading delay
            };
            const processLoadedTask = (task) => { // Process task from storage
                const defaults = defaultTaskForm();
                return {
                    ...defaults, ...task,
                    assigneeIds: Array.isArray(task.assigneeIds) ? task.assigneeIds : (task.assigneeId ? [task.assigneeId] : []),
                    comments: Array.isArray(task.comments) ? task.comments : [],
                    history: Array.isArray(task.history) ? task.history : [],
                    isOverdue: task.endDate && task.status !== '已完成' && new Date(task.endDate) < new Date()
                };
            };
            const loadSampleData = () => {
                 tasks.push(...[
                    { id: 'sample-1', title: '设计新版官网首页', description: '根据最新品牌形象，设计一个现代、响应式的官网首页。@[设计师-丽丽](user:user-design1) 请主导设计。', assigneeIds: ['user-design1', 'user-admin'], startDate: '2025-05-20T09:00:00Z', endDate: '2025-06-05T18:00:00Z', progress: 30, status: '进行中', priority: 'high', projectType: 'internal', comments: [{userId: 'user-admin', text:'初稿何时可以评审？', timestamp: new Date(Date.now() - 86400000).toISOString()}], history: [{userId: 'user-admin', action: '创建任务', timestamp: new Date(Date.now() - 2*86400000).toISOString()}], createdAt: new Date(Date.now() - 2*86400000).toISOString(), creatorId: 'user-admin', estimatedWorkload: 40 },
                    { id: 'sample-2', title: '开发用户登录API', description: '实现安全的JWT登录认证接口。@[开发者-小明](user:user-dev1) 负责后端。', assigneeIds: ['user-dev1'], startDate: '2025-05-25T09:00:00Z', endDate: '2025-06-10T18:00:00Z', progress: 0, status: '待办', priority: 'high', projectType: 'research', comments: [], history: [{userId: 'user-admin', action: '创建任务', timestamp: new Date(Date.now() - 86400000).toISOString()}], createdAt: new Date(Date.now() - 86400000).toISOString(), creatorId: 'user-admin', estimatedWorkload: 24 },
                    { id: 'sample-3', title: '撰写产品发布会演讲稿', description: '为下月产品发布会准备演讲稿，突出新功能亮点。@[项目管理员](user:user-admin) 审核。', assigneeIds: ['user-admin'], startDate: '2025-06-01T09:00:00Z', endDate: '2025-06-15T18:00:00Z', progress: 75, status: '进行中', priority: 'medium', projectType: 'client', comments: [], history: [{userId: 'user-admin', action: '创建任务', timestamp: new Date().toISOString()}], createdAt: new Date().toISOString(), creatorId: 'user-admin', estimatedWorkload: 16 },
                    { id: 'sample-4', title: '测试支付模块兼容性', description: '在主流浏览器和设备上测试支付模块的兼容性。@[测试员-阿强](user:user-test1) 请执行测试用例。', assigneeIds: ['user-test1', 'user-dev2'], startDate: '2025-05-10T09:00:00Z', endDate: '2025-05-18T18:00:00Z', progress: 100, status: '已完成', priority: 'medium', projectType: 'internal', comments: [], history: [], createdAt: new Date(Date.now() - 5*86400000).toISOString(), creatorId: 'user-admin', completedDate: '2025-05-18T17:00:00Z', estimatedWorkload: 32 },
                     { id: 'sample-5', title: '优化数据库查询性能', description: '针对用户列表页的查询进行性能分析和优化。@[开发者-小红](user:user-dev2)', assigneeIds: ['user-dev2'], startDate: '2025-06-01T09:00:00Z', endDate: '2025-06-03T18:00:00Z', progress: 0, status: '待办', priority: 'low', projectType: 'internal', comments: [], history: [], createdAt: new Date().toISOString(), creatorId: 'user-admin', estimatedWorkload: 8 },
                ].map(processLoadedTask));
                saveData();
            };
            
            // --- Notifications ---
            const addNotification = ({ type, title, message, taskId = null, userIdsToNotify = [], isGlobal = false }) => {
                const newNotif = {
                    id: `notif-${Date.now()}-${Math.random().toString(16).slice(2, 8)}`,
                    type, title, message, taskId, userIdsToNotify, createdAt: new Date().toISOString(), read: false
                };
                notifications.unshift(newNotif);
                if (notifications.length > 50) notifications.pop(); // Keep max 50

                // Show ElNotification if it's a global message or current user is in userIdsToNotify
                if (isGlobal || userIdsToNotify.includes(currentUser.value.id)) {
                    let elNotifType = 'info';
                    if (type === 'overdue' || type === 'error') elNotifType = 'error';
                    if (type === 'completed' || type === 'success') elNotifType = 'success';
                    if (type === 'mention') elNotifType = 'warning';
                    
                    ElNotification({
                        title: title,
                        message: message,
                        type: elNotifType,
                        duration: 4500,
                        onClick: () => { if (taskId) handleNotificationClick(newNotif); }
                    });
                }
                saveData();
            };
            const handleNotificationClick = (notification) => {
                const notifInArray = notifications.find(n => n.id === notification.id);
                if (notifInArray) notifInArray.read = true;
                saveData();
                if (notification.taskId) {
                    const taskToView = tasks.find(t => t.id === notification.taskId);
                    if (taskToView) showTaskDetail(taskToView);
                }
                showNotificationCenter.value = false;
            };
            const markAllNotificationsRead = () => {
                notifications.forEach(n => n.read = true);
                saveData();
            };


            // --- Task Helpers ---
            const getMemberNameById = (id) => teamMembers.find(m => m.id === id)?.name || '未知用户';
            const getAvatarUrl = (id) => teamMembers.find(m => m.id === id)?.avatar || 'https://placehold.co/24x24/cccccc/ffffff?text=?';
            const onAvatarError = (e) => { e.target.src = 'https://placehold.co/24x24/cccccc/ffffff?text=?'; };
            const formatDate = (dateStr, includeTime = true) => {
                if (!dateStr) return '-';
                const date = new Date(dateStr);
                if (isNaN(date.getTime())) return 'N/A';
                const options = { year: 'numeric', month: '2-digit', day: '2-digit'};
                if (includeTime) {
                    options.hour = '2-digit';
                    options.minute = '2-digit';
                }
                return date.toLocaleString('zh-CN', options).replace(/\//g, '-');
            };
            const priorityLabel = (priority) => ({ low: '低', medium: '中', high: '高' }[priority] || '未知');
            const getTaskProgressColor = (task) => {
                if (task.status === '已完成') return 'var(--success-color)';
                if (task.isOverdue) return 'var(--danger-color)';
                return { high: 'var(--warning-color)', medium: 'var(--primary-color)', low: 'var(--info-color)' }[task.priority] || 'var(--info-color)';
            };
            const getTaskProgressStatus = (task) => {
                if (task.status === '已完成') return 'success';
                if (task.isOverdue) return 'exception';
                return undefined; // Normal progress bar
            };

            // --- Filtering ---
            const filteredTasks = computed(() => {
                return tasks.filter(task => {
                    const query = searchQuery.value.toLowerCase();
                    const matchesQuery = !query || 
                                         task.title.toLowerCase().includes(query) || 
                                         (task.description && task.description.toLowerCase().includes(query));
                    const matchesStatus = !filter.status || task.status === filter.status;
                    const matchesAssignee = !filter.assigneeId || task.assigneeIds.includes(filter.assigneeId);
                    const matchesPriority = !filter.priority || task.priority === filter.priority;
                    return matchesQuery && matchesStatus && matchesAssignee && matchesPriority;
                });
            });
            const debouncedFilterTasks = debounce(() => { /* Computed property handles this */ }, 300);
            const filterTasks = () => { /* Computed property handles this */ };

            // --- Overdue Status ---
            const updateAllOverdueStatus = (notify = true) => {
                let changed = false;
                tasks.forEach(task => {
                    const isCurrentlyOverdue = task.endDate && task.status !== '已完成' && new Date(task.endDate) < new Date();
                    if (isCurrentlyOverdue && task.status !== '已逾期') {
                        task.status = '已逾期';
                        task.isOverdue = true;
                        if (notify) {
                            addNotification({ type: 'overdue', title: '任务逾期提醒', message: `"${task.title}" 已到期且未完成！`, taskId: task.id, userIdsToNotify: task.assigneeIds, isGlobal: false });
                        }
                        changed = true;
                    } else if (!isCurrentlyOverdue && task.status === '已逾期') { // If no longer overdue (e.g. date changed or completed)
                        task.status = task.progress === 100 ? '已完成' : (task.progress > 0 ? '进行中' : '待办'); // Revert to a sensible status
                        task.isOverdue = false;
                        changed = true;
                    }
                    task.isOverdue = isCurrentlyOverdue; // Ensure isOverdue flag is always correct
                });
                if (changed) saveData();
            };


            // --- Drag & Drop ---
            const onDragStart = (event) => { /* Optional: visual feedback */ };
            const onDragEnd = (event, targetStatus) => {
                if (typeof VueDraggablePlus === 'undefined' || VueDraggablePlus.name === 'VueDraggableStub') {
                    ElMessage.warning({ message: '拖拽功能当前不可用。', customClass: isDarkTheme.value ? 'el-message--dark' : '' });
                    loadData(); // Revert to original state
                    return;
                }
                const taskId = event.item.getAttribute('data-id') || event.item.__draggable_id; // Try to get ID
                if (!taskId) {
                    console.error("Draggable item ID not found.", event.item);
                    loadData(); return;
                }

                const taskIndex = tasks.findIndex(t => t.id === taskId);
                if (taskIndex === -1) { console.error("Task not found for ID:", taskId); loadData(); return;}

                const task = tasks[taskIndex];
                const oldStatus = task.status;

                if (task && oldStatus !== targetStatus && targetStatus !== '已逾期') { // Cannot drag to '已逾期'
                    task.status = targetStatus;
                    task.progress = targetStatus === '已完成' ? 100 : (targetStatus === '待办' ? 0 : (task.progress || 10));
                    if (targetStatus === '已完成') task.completedDate = new Date().toISOString();
                    else task.completedDate = null;
                    task.isOverdue = false; // Reset overdue if manually moved from there
                    task.updatedAt = new Date().toISOString();
                    if(!task.history) task.history = [];
                    task.history.push({ userId: currentUser.value.id, action: `状态从 ${oldStatus} 更新为 ${targetStatus}`, timestamp: new Date().toISOString() });


                    addNotification({ type: 'update', title: '任务状态更新', message: `"${task.title}" 状态变更为 ${targetStatus}`, taskId: task.id, userIdsToNotify: task.assigneeIds });
                    saveData();
                    ElMessage.success({ message: `任务 "${task.title}" 已移至 ${targetStatus}`, customClass: isDarkTheme.value ? 'el-message--dark' : '' });
                } else if (targetStatus === '已逾期' && oldStatus !== '已逾期') {
                     // Revert drag if attempted to move to "已逾期" manually
                    ElMessage.warning({ message: '不能手动将任务拖拽到“已逾期”列。', customClass: isDarkTheme.value ? 'el-message--dark' : '' });
                    loadData(); // This is a bit heavy, ideally just revert the specific task's column
                }
            };

            // --- Task CRUD ---
            const openCreateDialog = (initialData = null) => {
                editMode.value = !!initialData;
                taskForm.value = initialData ? { ...defaultTaskForm(), ...JSON.parse(JSON.stringify(initialData)) } : defaultTaskForm();
                showCreateDialog.value = true;
            };
            const editTask = (task) => openCreateDialog(task);

            const saveTask = (taskDataFromForm) => {
                const isNewTask = !taskDataFromForm.id;
                const taskData = {
                    ...taskDataFromForm,
                    id: isNewTask ? `task-${Date.now()}-${Math.random().toString(16).slice(2, 8)}` : taskDataFromForm.id,
                    updatedAt: new Date().toISOString(),
                };
                if (taskData.status === '已完成' && taskData.progress !== 100) taskData.progress = 100;
                if (taskData.status === '已完成' && !taskData.completedDate) taskData.completedDate = new Date().toISOString();
                if (taskData.status !== '已完成') taskData.completedDate = null;


                if (isNewTask) {
                    tasks.unshift(processLoadedTask(taskData)); // Add to start of array
                    addNotification({ type: 'create', title: '新任务已创建', message: `"${taskData.title}"`, taskId: taskData.id, userIdsToNotify: taskData.assigneeIds, isGlobal: true });
                } else {
                    const index = tasks.findIndex(t => t.id === taskData.id);
                    if (index !== -1) {
                        tasks[index] = processLoadedTask({ ...tasks[index], ...taskData });
                        addNotification({ type: 'update', title: '任务信息已更新', message: `"${taskData.title}"`, taskId: taskData.id, userIdsToNotify: taskData.assigneeIds });
                    }
                }
                updateAllOverdueStatus(false); // Recalculate overdue after save
                saveData();
                ElMessage.success({ message: isNewTask ? '任务创建成功!' : '任务更新成功!', customClass: isDarkTheme.value ? 'el-message--dark' : '' });
                initCharts(); // Re-init charts if data changes
            };

            const confirmDeleteTask = (taskId) => {
                const task = tasks.find(t => t.id === taskId);
                if (!task) return;
                ElMessageBox.confirm(`您确定要永久删除任务 "${task.title}" 吗？此操作无法撤销。`, '确认删除', {
                    confirmButtonText: '确认删除', cancelButtonText: '取消', type: 'warning',
                    customClass: isDarkTheme.value ? 'el-message-box--dark' : ''
                }).then(() => {
                    tasks.splice(tasks.findIndex(t => t.id === taskId), 1);
                    saveData();
                    showDetailDialog.value = false; // Close detail if it was open for this task
                    ElMessage.success({ message: '任务已删除。', customClass: isDarkTheme.value ? 'el-message--dark' : '' });
                    addNotification({ type: 'delete', title: '任务已删除', message: `"${task.title}"`, isGlobal: true });
                    initCharts();
                }).catch(() => {});
            };
            
            const updateTaskFromDetail = (updatedTask) => { // Called from TaskDetailDialog
                const index = tasks.findIndex(t => t.id === updatedTask.id);
                if (index !== -1) {
                    tasks[index] = processLoadedTask(updatedTask); // Ensure it's processed
                     if (tasks[index].status === '已完成' && tasks[index].progress !== 100) tasks[index].progress = 100;
                     if (tasks[index].status === '已完成' && !tasks[index].completedDate) tasks[index].completedDate = new Date().toISOString();
                     if (tasks[index].status !== '已完成') tasks[index].completedDate = null;
                    saveData();
                    updateAllOverdueStatus(false);
                    if(selectedTask.value && selectedTask.value.id === updatedTask.id) {
                        selectedTask.value = tasks[index]; // Update the ref for detail dialog
                    }
                    initCharts();
                }
            };


            // --- Task Detail & Preview ---
            const showTaskDetail = (task) => {
                selectedTask.value = tasks.find(t => t.id === task.id); // Ensure we get the reactive task object
                showDetailDialog.value = true;
            };
            const showPreview = (task, event) => {
                // Debounce or delay this if it causes performance issues on rapid mouse movements
                previewTask.value = task;
                // Position calculation needs to be more robust, considering viewport edges
                const cardRect = event.currentTarget.getBoundingClientRect();
                let top = cardRect.bottom + 8;
                let left = cardRect.left;
                // Basic boundary check (rough)
                if (top + 200 > window.innerHeight) top = cardRect.top - 208; // Show above if not enough space below
                if (left + 320 > window.innerWidth) left = window.innerWidth - 328; // Adjust left if overflows right

                previewPosition.top = top;
                previewPosition.left = left;
            };
            const hidePreview = () => { previewTask.value = null; };

            // --- Comments ---
            const submitComment = (taskId, commentText) => {
                const task = tasks.find(t => t.id === taskId);
                if (!task) return;
                const newComment = {
                    id: `comment-${Date.now()}`,
                    userId: currentUser.value.id,
                    text: commentText,
                    timestamp: new Date().toISOString(),
                };
                if (!task.comments) task.comments = [];
                task.comments.push(newComment);
                if(!task.history) task.history = [];
                task.history.push({ userId: currentUser.value.id, action: `发表了评论`, timestamp: new Date().toISOString() });
                task.updatedAt = new Date().toISOString();

                // Notify mentioned users and assignees
                const mentionedUserIds = (commentText.match(/@\[([^\]]+)\]\(user:([^\)]+)\)/g) || [])
                    .map(match => match.match(/user:([^\)]+)/)[1])
                    .filter(id => id !== currentUser.value.id);
                
                const userIdsToNotify = [...new Set([...task.assigneeIds, ...mentionedUserIds])].filter(id => id !== currentUser.value.id);

                if (userIdsToNotify.length > 0) {
                     addNotification({
                        type: 'comment',
                        title: '任务有新评论',
                        message: `${currentUser.value.name} 在 "${task.title}" 中发表了评论。`,
                        taskId: task.id,
                        userIdsToNotify: userIdsToNotify
                    });
                }
                 // Specific notification for mentions
                mentionedUserIds.forEach(mentionedId => {
                    addNotification({
                        type: 'mention',
                        title: '您被提及了',
                        message: `${currentUser.value.name} 在任务 "${task.title}" 的评论中提及了您。`,
                        taskId: task.id,
                        userIdsToNotify: [mentionedId] // Only notify the mentioned user specifically for this
                    });
                });


                saveData();
                ElMessage.success({ message: '评论已发表', customClass: isDarkTheme.value ? 'el-message--dark' : '' });
            };
            
            // --- Batch Operations ---
            const toggleTaskSelection = (taskId, event) => {
                // event.stopPropagation(); // Prevent opening detail view when clicking checkbox
                const index = selectedTaskIds.value.indexOf(taskId);
                if (index > -1) {
                    selectedTaskIds.value.splice(index, 1);
                } else {
                    selectedTaskIds.value.push(taskId);
                }
            };
            const clearSelection = () => selectedTaskIds.value = [];

            const openBatchAssignDialog = () => { if(selectedTaskIds.value.length > 0) showBatchAssignDialog.value = true; };
            const handleBatchAssign = (assigneeIds) => {
                tasks.forEach(task => {
                    if (selectedTaskIds.value.includes(task.id)) {
                        task.assigneeIds = [...new Set([...task.assigneeIds, ...assigneeIds])]; // Add, avoid duplicates
                        task.updatedAt = new Date().toISOString();
                        if(!task.history) task.history = [];
                        task.history.push({ userId: currentUser.value.id, action: `批量分配负责人`, timestamp: new Date().toISOString() });
                        addNotification({ type: 'update', title: '任务已分配', message: `"${task.title}" 已分配给新负责人。`, taskId: task.id, userIdsToNotify: task.assigneeIds });
                    }
                });
                saveData();
                ElMessage.success({ message: `${selectedTaskIds.value.length} 个任务已分配负责人。`, customClass: isDarkTheme.value ? 'el-message--dark' : '' });
                clearSelection();
            };

            const openBatchStatusDialog = () => { if(selectedTaskIds.value.length > 0) showBatchStatusDialog.value = true; };
            const handleBatchUpdateStatus = (newStatus) => {
                 tasks.forEach(task => {
                    if (selectedTaskIds.value.includes(task.id)) {
                        const oldStatus = task.status;
                        task.status = newStatus;
                        task.progress = newStatus === '已完成' ? 100 : (newStatus === '待办' ? 0 : (task.progress || 10));
                        if (newStatus === '已完成') task.completedDate = new Date().toISOString();
                        else task.completedDate = null;
                        task.isOverdue = false; // Reset overdue if manually changed
                        task.updatedAt = new Date().toISOString();
                        if(!task.history) task.history = [];
                        task.history.push({ userId: currentUser.value.id, action: `批量更新状态为 ${newStatus}`, timestamp: new Date().toISOString() });
                        addNotification({ type: 'update', title: '任务状态批量更新', message: `"${task.title}" 状态更新为 ${newStatus}`, taskId: task.id, userIdsToNotify: task.assigneeIds });
                    }
                });
                saveData();
                ElMessage.success({ message: `${selectedTaskIds.value.length} 个任务状态已更新。`, customClass: isDarkTheme.value ? 'el-message--dark' : '' });
                clearSelection();
                updateAllOverdueStatus(false);
            };

            const confirmBatchDeleteTasks = () => {
                if (selectedTaskIds.value.length === 0) return;
                ElMessageBox.confirm(`您确定要永久删除选中的 ${selectedTaskIds.value.length} 个任务吗？此操作无法撤销。`, '确认批量删除', {
                    confirmButtonText: '确认删除', cancelButtonText: '取消', type: 'warning',
                    customClass: isDarkTheme.value ? 'el-message-box--dark' : ''
                }).then(() => {
                    const deletedTaskTitles = [];
                    tasks.forEach(task => {
                        if(selectedTaskIds.value.includes(task.id)) deletedTaskTitles.push(task.title);
                    });
                    tasks.splice(0, tasks.length, ...tasks.filter(t => !selectedTaskIds.value.includes(t.id)));
                    saveData();
                    ElMessage.success({ message: `${selectedTaskIds.value.length} 个任务已删除。`, customClass: isDarkTheme.value ? 'el-message--dark' : '' });
                    addNotification({ type: 'delete', title: '任务批量删除', message: `删除了 ${deletedTaskTitles.length} 个任务，包括 "${deletedTaskTitles.slice(0,2).join('", "')}${deletedTaskTitles.length > 2 ? '...' : ''}"`, isGlobal: true });
                    clearSelection();
                    initCharts();
                }).catch(() => {});
            };


            // --- Theme & Sidebar ---
            const toggleTheme = () => {
                isDarkTheme.value = !isDarkTheme.value;
                localStorage.setItem('智创科技任务平台主题_v7', isDarkTheme.value ? 'dark' : 'light');
                document.documentElement.classList.toggle('dark', isDarkTheme.value);
                // Re-init charts on theme change for ECharts theme compatibility
                nextTick(() => initCharts());
            };
            const toggleSidebar = () => {
                isSidebarCollapsed.value = !isSidebarCollapsed.value;
                localStorage.setItem('智创科技任务平台侧边栏状态_v7', isSidebarCollapsed.value);
                 nextTick(() => { // Allow DOM to update before resizing charts
                    resizeAllCharts();
                });
            };
            
            // --- Navigation & View Title ---
            const currentViewTitle = computed(() => {
                switch(activeMenu.value) {
                    case 'dashboard': return '任务看板';
                    case 'analytics': return '数据分析';
                    case 'my-tasks': return '我的任务';
                    case 'team': return '团队管理';
                    case 'settings': return '系统设置';
                    default: return '任务看板';
                }
            });
            const navigateTo = (menuKey) => {
                activeMenu.value = menuKey;
                localStorage.setItem('智创科技任务平台菜单_v7', menuKey);
                if (menuKey === 'analytics') {
                    nextTick(() => initCharts()); // Ensure charts are initialized when view becomes visible
                }
            };
            
            // --- ECharts ---
            let statusChartInstance, priorityChartInstance, trendChartInstance, memberLoadChartInstance;

            const initCharts = () => {
                if (activeMenu.value !== 'analytics') return; // Only init if analytics view is active
                
                // Destroy existing instances before re-initializing
                if (statusChartInstance) statusChartInstance.dispose();
                if (priorityChartInstance) priorityChartInstance.dispose();
                if (trendChartInstance) trendChartInstance.dispose();
                if (memberLoadChartInstance) memberLoadChartInstance.dispose();

                const chartTheme = isDarkTheme.value ? 'dark' : null;

                // 1. Task Status Distribution (Pie Chart)
                if (taskStatusChart.value) {
                    statusChartInstance = echarts.init(taskStatusChart.value, chartTheme);
                    const statusCounts = tasks.reduce((acc, task) => {
                        acc[task.status] = (acc[task.status] || 0) + 1;
                        return acc;
                    }, {});
                    statusChartInstance.setOption({
                        tooltip: { trigger: 'item' },
                        legend: { orient: 'vertical', left: 'left', top: 'center', textStyle: { color: isDarkTheme.value ? '#ccc' : '#333'} },
                        series: [{
                            name: '任务状态', type: 'pie', radius: ['45%', '70%'], avoidLabelOverlap: false, itemStyle: { borderRadius: 8, borderColor: isDarkTheme.value ? '#1f2937' : '#fff', borderWidth: 2 },
                            label: { show: false, position: 'center' }, emphasis: { label: { show: true, fontSize: '20', fontWeight: 'bold' } }, labelLine: { show: false },
                            data: Object.entries(statusCounts).map(([name, value]) => ({ value, name }))
                        }]
                    });
                }

                // 2. Task Priority Distribution (Bar Chart)
                if (taskPriorityChart.value) {
                    priorityChartInstance = echarts.init(taskPriorityChart.value, chartTheme);
                    const priorityCounts = tasks.reduce((acc, task) => {
                        acc[priorityLabel(task.priority)] = (acc[priorityLabel(task.priority)] || 0) + 1;
                        return acc;
                    }, {});
                     priorityChartInstance.setOption({
                        tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
                        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
                        xAxis: { type: 'category', data: Object.keys(priorityCounts), axisLabel: { color: isDarkTheme.value ? '#ccc' : '#333'} },
                        yAxis: { type: 'value', axisLabel: { color: isDarkTheme.value ? '#ccc' : '#333'} },
                        series: [{
                            name: '任务数量', type: 'bar', barWidth: '50%', data: Object.values(priorityCounts),
                            itemStyle: { color: params => ({'高':'#ef4444', '中':'#f59e0b', '低':'#22c55e'}[params.name] || '#6b7280') }
                        }]
                    });
                }
                
                // 3. Task Trend (Created vs Completed - Line Chart for last 7 days)
                if (taskTrendChart.value) {
                    trendChartInstance = echarts.init(taskTrendChart.value, chartTheme);
                    const last7Days = Array.from({length: 7}, (_, i) => {
                        const d = new Date();
                        d.setDate(d.getDate() - i);
                        return d.toISOString().split('T')[0];
                    }).reverse();
                    
                    const createdPerDay = last7Days.map(day => tasks.filter(t => t.createdAt && t.createdAt.startsWith(day)).length);
                    const completedPerDay = last7Days.map(day => tasks.filter(t => t.completedDate && t.completedDate.startsWith(day)).length);

                    trendChartInstance.setOption({
                        tooltip: { trigger: 'axis' },
                        legend: { data: ['创建任务数', '完成任务数'], top: 'bottom', textStyle: { color: isDarkTheme.value ? '#ccc' : '#333'} },
                        grid: { left: '3%', right: '4%', bottom: '10%', containLabel: true },
                        xAxis: { type: 'category', boundaryGap: false, data: last7Days.map(d => d.slice(5)), axisLabel: { color: isDarkTheme.value ? '#ccc' : '#333'} },
                        yAxis: { type: 'value', axisLabel: { color: isDarkTheme.value ? '#ccc' : '#333'} },
                        series: [
                            { name: '创建任务数', type: 'line', smooth: true, data: createdPerDay, areaStyle: {}, itemStyle: { color: '#3b82f6' } },
                            { name: '完成任务数', type: 'line', smooth: true, data: completedPerDay, areaStyle: {}, itemStyle: { color: '#16a34a' } }
                        ]
                    });
                }

                // 4. Member Load (Active Tasks - Horizontal Bar Chart)
                if (memberLoadChart.value) {
                    memberLoadChartInstance = echarts.init(memberLoadChart.value, chartTheme);
                    const memberLoad = teamMembers.map(member => {
                        const count = tasks.filter(task => task.status === '进行中' && task.assigneeIds.includes(member.id)).length;
                        return { name: member.name, value: count };
                    }).filter(m => m.value > 0).sort((a,b) => a.value - b.value);

                    memberLoadChartInstance.setOption({
                        tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
                        grid: { left: '3%', right: '4%', bottom: '3%', top: '5%', containLabel: true },
                        xAxis: { type: 'value', boundaryGap: [0, 0.01], axisLabel: { color: isDarkTheme.value ? '#ccc' : '#333'} },
                        yAxis: { type: 'category', data: memberLoad.map(m => m.name), axisLabel: { color: isDarkTheme.value ? '#ccc' : '#333'} },
                        series: [{
                            name: '进行中任务数', type: 'bar', data: memberLoad.map(m => m.value),
                            itemStyle: { color: '#6366f1' } // Indigo
                        }]
                    });
                }
            };
            
            const resizeAllCharts = () => {
                if (statusChartInstance) statusChartInstance.resize();
                if (priorityChartInstance) priorityChartInstance.resize();
                if (trendChartInstance) trendChartInstance.resize();
                if (memberLoadChartInstance) memberLoadChartInstance.resize();
            };

            // --- Lifecycle & Watchers ---
            onMounted(() => {
                loadData(); // This will also call initCharts
                // Periodically check for overdue tasks
                setInterval(() => updateAllOverdueStatus(true), 5 * 60 * 1000); // Check every 5 minutes
                document.documentElement.classList.toggle('dark', isDarkTheme.value);
                window.addEventListener('resize', resizeAllCharts);
            });
            
            watch(isDarkTheme, () => { // Re-init charts on theme change
                nextTick(() => initCharts());
            });
            
            // Provide theme for child components (like dialogs)
            Vue.provide('theme', { theme: isDarkTheme });


            return {
                tasks, showCreateDialog, showDetailDialog, showNotificationCenter, selectedTask, newComment, editMode, isLoading, searchQuery, filter, isDarkTheme, activeMenu, notifications, previewTask, previewPosition, isSidebarCollapsed,
                teamMembers, currentUser, taskColumns, unreadNotificationsCount, taskForm, taskStatusOptions,
                saveData, loadData, updateAllOverdueStatus, getMemberNameById, getAvatarUrl, onAvatarError, formatDate, priorityLabel, getTaskProgressColor, getTaskProgressStatus, filteredTasks, debouncedFilterTasks, filterTasks, onDragStart, onDragEnd, 
                onDrop: onDragEnd, // Corrected: onDrop should be onDragEnd for vue-draggable-plus in this context
                openCreateDialog, editTask, saveTask, confirmDeleteTask, updateTaskFromDetail,
                showTaskDetail, showPreview, hidePreview, submitComment,
                toggleTheme, toggleSidebar, currentViewTitle, navigateTo, addNotification, handleNotificationClick, markAllNotificationsRead, timeAgo,
                // Batch Ops
                selectedTaskIds, toggleTaskSelection, clearSelection,
                showBatchAssignDialog, openBatchAssignDialog, handleBatchAssign,
                showBatchStatusDialog, openBatchStatusDialog, handleBatchUpdateStatus,
                confirmBatchDeleteTasks,
                // ECharts refs
                taskStatusChart, taskPriorityChart, taskTrendChart, memberLoadChart,
                // User dropdown
                handleUserCommand: (command) => {
                    if (command === 'logout') ElMessage.info({ message: '模拟退出登录', customClass: isDarkTheme.value ? 'el-message--dark' : '' });
                    else ElMessage.info({ message: `点击了: ${command}`, customClass: isDarkTheme.value ? 'el-message--dark' : '' });
                }
            };
        }
    });

    // Register Element Plus Icons globally
    if (window.ElementPlusIconsVue) {
        for (const [key, component] of Object.entries(window.ElementPlusIconsVue)) {
            app.component(key, component);
        }
    }
    // Register VueDraggablePlus or stub
    if (typeof VueDraggablePlus !== 'undefined' && VueDraggablePlus.name !== 'VueDraggableStub') {
        app.component('VueDraggable', VueDraggablePlus.VueDraggable); // Correct way to register VueDraggablePlus v0.4.0
    } else {
        app.component('VueDraggable', { // Stub component
            props: ['modelValue', 'itemKey', 'group', 'animation', 'ghostClass', 'dragClass'],
            emits: ['update:modelValue', 'start', 'end'],
            template: `<div><slot name="header"></slot><div v-for="element in modelValue" :key="element[itemKey] || element.id"><slot name="item" :element="element"></slot></div><slot name="footer"></slot></div>`
        });
    }

    app.use(ElementPlus, { locale: ElementPlusLocalesZhCn }); // Using the correct global variable name for locale
    app.mount('#app');
});
</script>
</body>
</html>
