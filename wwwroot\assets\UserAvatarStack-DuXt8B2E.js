import{_ as a,c as e,r,m as s,b as t,d as l,a9 as u,e as i,w as n,t as o,i as m,a as v,o as d,p,Y as c,F as f,h as U,n as y,A as h}from"./index-CkwLz8y6.js";const g={name:"UserAvatar",props:{user:{type:Object,default:()=>({})},userId:{type:[Number,String],default:null},userName:{type:String,default:""},avatarUrl:{type:String,default:""},size:{type:String,default:"default",validator:a=>["mini","small","default","medium","large"].includes(a)},showName:{type:Boolean,default:!1}},setup(a){const t=e((()=>`size-${a.size}`)),l=r(!1),u=e((()=>({mini:24,small:32,default:40,medium:56,large:72}[a.size]||40))),i=e((()=>{if(a.userName)return a.userName;const e=a.user||{};return e.name||e.userName||e.username||"未知用户"})),n=e((()=>{if(l.value)return"";let e=a.avatarUrl||"";if(!e&&a.user){const r=a.user;e=r.avatarUrl||r.avatar||r.userAvatarUrl||r.userAvatar||r.picture||r.imageUrl||"",!e&&r.user&&(e=r.user.avatarUrl||r.user.avatar||r.user.userAvatarUrl||r.user.picture||"")}if(!e)return"";if(e.startsWith("http"))return e;return""+(e.startsWith("/")?e.substring(1):e)}));return s((()=>{})),{sizeClass:t,avatarSize:u,userName:i,getInitials:a=>{var e;if(!a)return"?";if(/[\u4e00-\u9fa5]/.test(a))return a.slice(-1);const r=a.split(" ");return r.length>=2?(r[0][0]+r[1][0]).toUpperCase():(null==(e=a[0])?void 0:e.toUpperCase())||"?"},fullAvatarUrl:n,handleAvatarError:a=>{l.value=!0}}}},z={class:"avatar-container"},N={class:"avatar-text"},x={key:0,class:"user-name"};const A=a(g,[["render",function(a,e,r,s,p,c){const f=v("el-avatar");return d(),t("div",{class:m(["user-avatar",[s.sizeClass,{"show-name":r.showName}]])},[l("div",z,[i(f,{size:s.avatarSize,src:s.fullAvatarUrl,alt:s.userName,class:"avatar",onError:s.handleAvatarError},{default:n((()=>[l("span",N,o(s.getInitials(s.userName)),1)])),_:1},8,["size","src","alt","onError"])]),r.showName?(d(),t("span",x,o(s.userName),1)):u("",!0)],2)}],["__scopeId","data-v-9adde081"]]),S={name:"UserAvatarStack",components:{UserAvatar:A},props:{users:{type:Array,default:()=>[]},maxUsers:{type:Number,default:3},size:{type:String,default:"default"},isMainUserPrimary:{type:Boolean,default:!1},showNames:{type:Boolean,default:!0},showPopover:{type:Boolean,default:!1},showDetails:{type:Boolean,default:!1},overlap:{type:Number,default:8},avatarSize:{type:[String,Number],default:24}},setup(a){const r=e((()=>a.isMainUserPrimary&&a.users.length?a.users.find((a=>!0===a.isPrimary||"Primary"===a.role)):null)),t=e((()=>a.users.length?a.users.filter((a=>!0!==a.isPrimary&&"Primary"!==a.role)):[])),l=e((()=>a.maxUsers)),u=e((()=>{let e=[];return e=r.value&&a.isMainUserPrimary?t.value.slice(0,l.value-1):a.users.slice(0,l.value),e.map((a=>({...a,avatarUrl:a.avatarUrl||""})))})),i=e((()=>Math.max(0,a.users.length-a.maxUsers))),n=e((()=>i.value)),o=e((()=>t.value.map((a=>a.name)).join(", "))),m=()=>{a.users&&a.users.length>0&&a.users.forEach(((a,e)=>{}))};return s((()=>{m()})),p((()=>a.users),(()=>{m()}),{deep:!0}),{mainUser:r,otherUsers:t,visibleUsers:u,hiddenCount:i,extraCount:n,getUserName:a=>a&&(a.name||a.userName||a.username)||"未知用户",formatOtherUsers:o}}},b={class:"avatar-wrapper"},w={key:0,class:"avatar-details"},P={key:0,class:"user-info"},k={class:"user-name"},C={key:1,class:"other-users"};const _=a(S,[["render",function(a,e,r,s,i,p){const g=v("el-avatar");return d(),t("div",{class:m(["user-avatar-stack",{clickable:r.showPopover}])},[l("div",b,[s.mainUser&&r.isMainUserPrimary?(d(),c(g,{key:0,src:s.mainUser.avatarUrl,size:r.avatarSize,class:"avatar primary"},{default:n((()=>[h(o(s.mainUser.name?s.mainUser.name.charAt(0).toUpperCase():"?"),1)])),_:1},8,["src","size"])):u("",!0),(d(!0),t(f,null,U(s.visibleUsers,((a,e)=>(d(),c(g,{key:`user-${a.id||a.userId||e}`,src:a.avatarUrl,size:r.avatarSize,class:"avatar",style:y({zIndex:s.visibleUsers.length-e,marginLeft:e>0?`-${r.overlap}px`:"0"})},{default:n((()=>[h(o(a.name?a.name.charAt(0).toUpperCase():"?"),1)])),_:2},1032,["src","size","style"])))),128)),s.extraCount>0?(d(),c(g,{key:1,size:r.avatarSize,class:"avatar extra-count",style:y({zIndex:0,marginLeft:`-${r.overlap}px`})},{default:n((()=>[h(" +"+o(s.extraCount),1)])),_:1},8,["size","style"])):u("",!0)]),r.showDetails&&r.users.length>0?(d(),t("div",w,[s.mainUser&&r.isMainUserPrimary?(d(),t("div",P,[l("span",k,o(s.mainUser.name),1),e[0]||(e[0]=l("span",{class:"user-role"},"(负责人)",-1))])):u("",!0),s.otherUsers.length>0?(d(),t("div",C,[l("span",null,o(s.formatOtherUsers),1)])):u("",!0)])):u("",!0)],2)}],["__scopeId","data-v-3711b20f"]]);export{_ as U,A as a};
