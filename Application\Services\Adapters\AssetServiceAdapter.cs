using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Core.Interfaces.Services;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models.Entities;
using ItAssetsSystem.Core.Monitoring;

namespace ItAssetsSystem.Application.Services.Adapters
{
    /// <summary>
    /// 资产服务适配器 - 基础解耦
    /// 包装现有的资产业务逻辑，提供统一的服务接口
    /// </summary>
    public class AssetServiceAdapter : IAssetService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<AssetServiceAdapter> _logger;
        private readonly IPerformanceMonitor _performanceMonitor;

        public AssetServiceAdapter(
            AppDbContext context,
            ILogger<AssetServiceAdapter> logger,
            IPerformanceMonitor performanceMonitor = null)
        {
            _context = context;
            _logger = logger;
            _performanceMonitor = performanceMonitor;
        }

        /// <summary>
        /// 根据ID获取资产信息
        /// </summary>
        public async Task<AssetDto> GetByIdAsync(int id)
        {
            if (_performanceMonitor != null)
            {
                return await _performanceMonitor.MonitorAndCompareAsync("AssetGetById", 
                    () => GetByIdInternalAsync(id), "v1.1");
            }
            
            return await GetByIdInternalAsync(id);
        }

        /// <summary>
        /// 内部获取资产信息实现
        /// </summary>
        private async Task<AssetDto> GetByIdInternalAsync(int id)
        {
            try
            {
                var asset = await _context.Assets
                    .Include(a => a.AssetType)
                    .Include(a => a.Location)
                        .ThenInclude(l => l.Department)
                    .FirstOrDefaultAsync(a => a.Id == id);

                return asset != null ? MapToAssetDto(asset) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取资产信息失败: {AssetId}", id);
                return null;
            }
        }

        /// <summary>
        /// 分页获取资产列表
        /// </summary>
        public async Task<PagedResult<AssetDto>> GetPagedAsync(AssetQueryDto query)
        {
            if (_performanceMonitor != null)
            {
                return await _performanceMonitor.MonitorAndCompareAsync("AssetGetPaged", 
                    () => GetPagedInternalAsync(query), "v1.1");
            }
            
            return await GetPagedInternalAsync(query);
        }

        /// <summary>
        /// 内部分页查询实现
        /// </summary>
        private async Task<PagedResult<AssetDto>> GetPagedInternalAsync(AssetQueryDto query)
        {
            try
            {
                var queryable = _context.Assets
                    .Include(a => a.AssetType)
                    .Include(a => a.Location)
                        .ThenInclude(l => l.Department)
                    .AsQueryable();

                // 应用筛选条件
                if (!string.IsNullOrEmpty(query.Keyword))
                {
                    queryable = queryable.Where(a => 
                        a.Name.Contains(query.Keyword) || 
                        a.AssetCode.Contains(query.Keyword) ||
                        a.SerialNumber.Contains(query.Keyword));
                }

                // AssetQueryDto中没有AssetCode字段，使用Keyword代替
                // if (!string.IsNullOrEmpty(query.AssetCode))
                // {
                //     queryable = queryable.Where(a => a.AssetCode.Contains(query.AssetCode));
                // }

                if (query.AssetTypeId.HasValue)
                {
                    queryable = queryable.Where(a => a.AssetTypeId == query.AssetTypeId.Value);
                }

                if (query.LocationId.HasValue)
                {
                    queryable = queryable.Where(a => a.LocationId == query.LocationId.Value);
                }

                if (!string.IsNullOrEmpty(query.Status))
                {
                    // 将状态名称转换为状态值
                    var statusValue = GetStatusValue(query.Status);
                    if (statusValue.HasValue)
                    {
                        queryable = queryable.Where(a => a.Status == statusValue.Value);
                    }
                }

                // 获取总数
                var totalCount = await queryable.CountAsync();

                // 分页查询
                var assets = await queryable
                    .OrderBy(a => a.Id)
                    .Skip((query.PageIndex - 1) * query.PageSize)
                    .Take(query.PageSize)
                    .ToListAsync();

                var assetDtos = assets.Select(MapToAssetDto).ToList();

                return new PagedResult<AssetDto>
                {
                    Items = assetDtos,
                    TotalCount = totalCount,
                    PageIndex = query.PageIndex,
                    PageSize = query.PageSize
                    // TotalPages是计算属性，不需要手动设置
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分页获取资产列表失败");
                return new PagedResult<AssetDto>
                {
                    Items = new List<AssetDto>(),
                    TotalCount = 0,
                    PageIndex = query.PageIndex,
                    PageSize = query.PageSize
                    // TotalPages是计算属性，不需要手动设置
                };
            }
        }

        /// <summary>
        /// 创建资产
        /// </summary>
        public async Task<AssetDto> CreateAsync(CreateAssetDto dto)
        {
            if (_performanceMonitor != null)
            {
                return await _performanceMonitor.MonitorAndCompareAsync("AssetCreate", 
                    () => CreateInternalAsync(dto), "v1.1");
            }
            
            return await CreateInternalAsync(dto);
        }

        /// <summary>
        /// 内部创建资产实现
        /// </summary>
        private async Task<AssetDto> CreateInternalAsync(CreateAssetDto dto)
        {
            try
            {
                // 验证资产编码唯一性
                if (await _context.Assets.AnyAsync(a => a.AssetCode == dto.AssetCode))
                {
                    throw new InvalidOperationException($"资产编码 {dto.AssetCode} 已存在");
                }

                // 验证资产类型存在
                if (!await _context.AssetTypes.AnyAsync(at => at.Id == dto.AssetTypeId))
                {
                    throw new InvalidOperationException($"资产类型 {dto.AssetTypeId} 不存在");
                }

                // 验证位置存在（如果指定）
                if (dto.LocationId.HasValue && 
                    !await _context.Locations.AnyAsync(l => l.Id == dto.LocationId.Value))
                {
                    throw new InvalidOperationException($"位置 {dto.LocationId} 不存在");
                }

                var asset = new Asset
                {
                    AssetCode = dto.AssetCode,
                    Name = dto.Name,
                    AssetTypeId = dto.AssetTypeId,
                    SerialNumber = "", // 接口DTO中没有SerialNumber
                    Model = "", // 接口DTO中没有Model
                    Brand = "", // 接口DTO中没有Brand
                    PurchaseDate = dto.PurchaseDate,
                    WarrantyExpireDate = null, // 接口DTO中没有WarrantyExpireDate
                    Price = dto.Price,
                    LocationId = dto.LocationId,
                    Status = GetStatusValue(dto.Status) ?? 0, // 默认闲置状态
                    Notes = dto.Notes,
                    FinancialCode = "", // 接口DTO中没有FinancialCode
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                _context.Assets.Add(asset);
                await _context.SaveChangesAsync();

                _logger.LogInformation("资产创建成功: {AssetCode}", asset.AssetCode);

                // 重新查询以获取关联数据
                return await GetByIdAsync(asset.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建资产失败: {AssetCode}", dto.AssetCode);
                throw;
            }
        }

        /// <summary>
        /// 更新资产
        /// </summary>
        public async Task<AssetDto> UpdateAsync(int id, UpdateAssetDto dto)
        {
            if (_performanceMonitor != null)
            {
                return await _performanceMonitor.MonitorAndCompareAsync("AssetUpdate", 
                    () => UpdateInternalAsync(id, dto), "v1.1");
            }
            
            return await UpdateInternalAsync(id, dto);
        }

        /// <summary>
        /// 内部更新资产实现
        /// </summary>
        private async Task<AssetDto> UpdateInternalAsync(int id, UpdateAssetDto dto)
        {
            try
            {
                var asset = await _context.Assets.FindAsync(id);
                if (asset == null)
                {
                    throw new InvalidOperationException($"资产 {id} 不存在");
                }

                // UpdateAssetDto中没有AssetCode字段，不需要验证编码唯一性

                // 更新资产信息
                asset.Name = dto.Name;
                if (dto.AssetTypeId.HasValue)
                    asset.AssetTypeId = dto.AssetTypeId.Value;
                asset.PurchaseDate = dto.PurchaseDate;
                asset.Price = dto.Price;
                asset.LocationId = dto.LocationId;
                if (!string.IsNullOrEmpty(dto.Status))
                    asset.Status = GetStatusValue(dto.Status) ?? asset.Status;
                asset.Notes = dto.Notes;
                asset.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                _logger.LogInformation("资产更新成功: {AssetCode}", asset.AssetCode);

                return await GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新资产失败: {AssetId}", id);
                throw;
            }
        }

        /// <summary>
        /// 删除资产
        /// </summary>
        public async Task<bool> DeleteAsync(int id)
        {
            if (_performanceMonitor != null)
            {
                return await _performanceMonitor.MonitorAndCompareAsync("AssetDelete",
                    () => DeleteInternalAsync(id), "v1.1");
            }

            return await DeleteInternalAsync(id);
        }

        /// <summary>
        /// 根据资产编码获取资产
        /// </summary>
        public async Task<AssetDto> GetByCodeAsync(string assetCode)
        {
            if (_performanceMonitor != null)
            {
                return await _performanceMonitor.MonitorAndCompareAsync("AssetGetByCode",
                    () => GetByCodeInternalAsync(assetCode), "v1.1");
            }

            return await GetByCodeInternalAsync(assetCode);
        }

        /// <summary>
        /// 检查资产是否存在
        /// </summary>
        public async Task<bool> ExistsAsync(int id)
        {
            if (_performanceMonitor != null)
            {
                return await _performanceMonitor.MonitorAndCompareAsync("AssetExists",
                    () => ExistsInternalAsync(id), "v1.1");
            }

            return await ExistsInternalAsync(id);
        }

        /// <summary>
        /// 内部根据编码获取资产实现
        /// </summary>
        private async Task<AssetDto> GetByCodeInternalAsync(string assetCode)
        {
            try
            {
                var asset = await _context.Assets
                    .Include(a => a.AssetType)
                    .Include(a => a.Location)
                        .ThenInclude(l => l.Department)
                    .FirstOrDefaultAsync(a => a.AssetCode == assetCode);

                return asset != null ? MapToAssetDto(asset) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据编码获取资产失败: {AssetCode}", assetCode);
                return null;
            }
        }

        /// <summary>
        /// 内部检查资产存在实现
        /// </summary>
        private async Task<bool> ExistsInternalAsync(int id)
        {
            try
            {
                return await _context.Assets.AnyAsync(a => a.Id == id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查资产存在失败: {AssetId}", id);
                return false;
            }
        }

        /// <summary>
        /// 内部删除资产实现
        /// </summary>
        private async Task<bool> DeleteInternalAsync(int id)
        {
            try
            {
                var asset = await _context.Assets.FindAsync(id);
                if (asset == null)
                {
                    return false;
                }

                _context.Assets.Remove(asset);
                await _context.SaveChangesAsync();

                _logger.LogInformation("资产删除成功: {AssetId}", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除资产失败: {AssetId}", id);
                return false;
            }
        }

        /// <summary>
        /// 映射资产实体到DTO
        /// </summary>
        private AssetDto MapToAssetDto(Asset asset)
        {
            return new AssetDto
            {
                Id = asset.Id,
                AssetCode = asset.AssetCode,
                Name = asset.Name,
                Description = asset.Notes, // 使用Notes作为Description
                AssetTypeId = asset.AssetTypeId,
                AssetTypeName = asset.AssetType?.Name,
                LocationId = asset.LocationId,
                LocationName = asset.Location?.Name,
                DepartmentId = asset.Location?.Department?.Id,
                DepartmentName = asset.Location?.Department?.Name,
                Price = asset.Price,
                PurchaseDate = asset.PurchaseDate,
                Status = GetStatusName(asset.Status), // 接口中Status是string类型
                Notes = asset.Notes,
                CreatedAt = asset.CreatedAt,
                UpdatedAt = asset.UpdatedAt,
                CreatedBy = null, // 当前实体中没有CreatedBy字段
                CreatedByName = null
            };
        }

        /// <summary>
        /// 获取状态名称
        /// </summary>
        private string GetStatusName(int status)
        {
            return status switch
            {
                0 => "闲置",
                1 => "使用中",
                2 => "维修中",
                3 => "报废",
                4 => "借出",
                _ => "未知"
            };
        }

        /// <summary>
        /// 根据状态名称获取状态值
        /// </summary>
        private int? GetStatusValue(string statusName)
        {
            return statusName switch
            {
                "闲置" => 0,
                "使用中" => 1,
                "维修中" => 2,
                "报废" => 3,
                "借出" => 4,
                _ => null
            };
        }
    }
}
