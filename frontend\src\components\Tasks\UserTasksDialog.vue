<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${userStat?.userName || '用户'} - 今日任务详情`"
    width="800px"
    :before-close="handleClose"
  >
    <div class="user-tasks-content" v-if="userStat">
      <!-- 用户信息 -->
      <div class="user-info-section">
        <div class="user-header">
          <el-avatar 
            :size="48" 
            :style="{ backgroundColor: getUserColor(userStat.userId) }"
          >
            {{ userStat.userName.charAt(0) }}
          </el-avatar>
          <div class="user-details">
            <h3>{{ userStat.userName }}</h3>
            <p>{{ userStat.shiftName }} | 完成率: {{ userStat.completionRate.toFixed(1) }}%</p>
          </div>
        </div>
        
        <!-- 统计概览 -->
        <div class="stats-overview">
          <div class="stat-item">
            <span class="stat-number primary">{{ userStat.claimedTasksCount }}</span>
            <span class="stat-label">已领取</span>
          </div>
          <div class="stat-item">
            <span class="stat-number success">{{ userStat.completedTasksCount }}</span>
            <span class="stat-label">已完成</span>
          </div>
          <div class="stat-item">
            <span class="stat-number warning">{{ userStat.startedTasksCount }}</span>
            <span class="stat-label">进行中</span>
          </div>
          <div class="stat-item">
            <span class="stat-number danger">{{ userStat.unclaimedTasksCount }}</span>
            <span class="stat-label">未领取</span>
          </div>
        </div>
      </div>

      <!-- 任务列表 -->
      <div class="tasks-section">
        <div class="section-header">
          <h4>任务详情</h4>
          <el-button 
            size="small" 
            :icon="Refresh" 
            @click="loadUserTasks"
            :loading="loading"
          >
            刷新
          </el-button>
        </div>

        <el-table 
          :data="userTasks" 
          v-loading="loading"
          stripe
          style="width: 100%"
          :empty-text="loading ? '加载中...' : '暂无任务数据'"
        >
          <el-table-column prop="taskName" label="任务名称" min-width="200">
            <template #default="{ row }">
              <div class="task-name-cell">
                <span class="task-name">{{ row.taskName || '未知任务' }}</span>
                <div class="task-watermark" v-if="row.claimStatus === 'Completed'">
                  <span 
                    class="watermark-text"
                    :style="{ color: getUserColor(userStat.userId) }"
                  >
                    ✓ {{ userStat.userName }}
                  </span>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="claimStatus" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.claimStatus)" size="small">
                {{ getStatusText(row.claimStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="claimedAt" label="领取时间" width="150">
            <template #default="{ row }">
              {{ formatDateTime(row.claimedAt) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="startedAt" label="开始时间" width="150">
            <template #default="{ row }">
              {{ row.startedAt ? formatDateTime(row.startedAt) : '-' }}
            </template>
          </el-table-column>
          
          <el-table-column prop="completedAt" label="完成时间" width="150">
            <template #default="{ row }">
              {{ row.completedAt ? formatDateTime(row.completedAt) : '-' }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button 
                v-if="row.claimStatus === 'Claimed'"
                type="primary" 
                size="small"
                @click="updateTaskStatus(row, 'Started')"
              >
                开始
              </el-button>
              <el-button 
                v-else-if="row.claimStatus === 'Started'"
                type="success" 
                size="small"
                @click="updateTaskStatus(row, 'Completed')"
              >
                完成
              </el-button>
              <span v-else class="status-text">{{ getStatusText(row.claimStatus) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import workShiftApi from '@/api/workShift'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  userStat: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const userTasks = ref([])

// 用户颜色映射
const userColors = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD',
  '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9', '#F8C471', '#82E0AA'
]

// 方法
const loadUserTasks = async () => {
  if (!props.userStat) return
  
  loading.value = true
  try {
    const response = await workShiftApi.getUserTodayClaimsById(props.userStat.userId)
    if (response.success) {
      userTasks.value = response.data || []
    } else {
      ElMessage.error('获取用户任务失败')
    }
  } catch (error) {
    console.error('获取用户任务失败:', error)
    ElMessage.error('获取用户任务失败')
  } finally {
    loading.value = false
  }
}

const updateTaskStatus = async (task, newStatus) => {
  try {
    const response = await workShiftApi.updateClaimStatus(task.claimId, {
      claimStatus: newStatus,
      notes: `状态更新为: ${getStatusText(newStatus)}`
    })

    if (response.success) {
      ElMessage.success('状态更新成功')
      loadUserTasks() // 重新加载任务列表
    } else {
      ElMessage.error(response.message || '状态更新失败')
    }
  } catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
  }
}

const getUserColor = (userId) => {
  return userColors[userId % userColors.length]
}

const getStatusType = (status) => {
  const typeMap = {
    'Claimed': 'primary',
    'Started': 'warning',
    'Completed': 'success',
    'Cancelled': 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    'Claimed': '已领取',
    'Started': '进行中',
    'Completed': '已完成',
    'Cancelled': '已取消'
  }
  return textMap[status] || status
}

const formatDateTime = (dateStr) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const handleClose = () => {
  dialogVisible.value = false
}

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible && props.userStat) {
    loadUserTasks()
  }
})
</script>

<style scoped>
.user-tasks-content {
  padding: 0 4px;
}

.user-info-section {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.user-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.user-details h3 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 18px;
}

.user-details p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-overview {
  display: flex;
  justify-content: space-around;
  gap: 20px;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-number.primary { color: #409EFF; }
.stat-number.success { color: #67C23A; }
.stat-number.warning { color: #E6A23C; }
.stat-number.danger { color: #F56C6C; }

.stat-label {
  font-size: 12px;
  color: #909399;
}

.tasks-section {
  margin-top: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  margin: 0;
  color: #303133;
}

.task-name-cell {
  position: relative;
}

.task-name {
  display: block;
  color: #303133;
  font-weight: 500;
}

.task-watermark {
  margin-top: 4px;
}

.watermark-text {
  font-size: 12px;
  font-weight: 600;
  opacity: 0.8;
}

.status-text {
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .stats-overview {
    flex-wrap: wrap;
    gap: 16px;
  }
  
  .stat-item {
    min-width: calc(50% - 8px);
  }
}
</style>
