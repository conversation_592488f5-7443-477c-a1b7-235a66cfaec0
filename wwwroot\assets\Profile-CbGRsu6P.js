import{_ as e,j as a,r as l,c as r,a5 as s,m as t,b as o,e as u,w as i,a as n,o as d,d as m,f as p,b0 as c,A as f,t as v,a8 as _}from"./index-CkwLz8y6.js";import{P as g}from"./PageHeader-BGBjKbIR.js";import{s as h}from"./system-9jEcQzSp.js";const b={class:"user-profile"},y={class:"card-header"},V={class:"profile-content"},w={class:"avatar-container"},j={class:"avatar-uploader"},U={class:"avatar-uploader-icon"},k={class:"info-container"},x=e({__name:"Profile",setup(e){const x=a(),A=l(!1),P=l(null);h.defaultAvatar;const z=`${h.apiBaseUrl}/v2/profile/avatar`,B=r((()=>({Authorization:`Bearer ${x.token}`}))),C=r((()=>x.userInfo||{})),L=s({name:"",email:"",phone:""}),$={name:[{required:!0,message:"请输入姓名",trigger:"blur"},{min:2,max:20,message:"长度在 2 到 20 个字符",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}]},q=()=>{L.name=C.value.name||"",L.email=C.value.email||"",L.phone=C.value.phone||"",A.value=!0},I=e=>{if(e.success){const a=e.data.avatarUrl;a?(x.setAvatar(a),_.success("头像上传成功")):_.warning("头像上传成功，但未获取到URL")}else _.error(e.message||"头像上传失败")},R=e=>{const a=e.type.startsWith("image/"),l=e.size/1024/1024<5;return a?!!l||(_.error("头像大小不能超过 5MB!"),!1):(_.error("头像必须是图片格式!"),!1)},D=e=>{_.error("头像上传失败，请重试")},F=async()=>{P.value&&await P.value.validate((async e=>{if(e)try{const e=await x.updateProfile({name:L.name,email:L.email,phone:L.phone});e.success?(_.success("个人信息更新成功"),A.value=!1):_.error(e.message||"更新个人信息失败")}catch(a){_.error("更新个人信息失败: "+(a.message||"未知错误"))}}))};return t((()=>{})),(e,a)=>{const l=n("el-button"),r=n("el-avatar"),s=n("el-icon"),t=n("el-upload"),_=n("el-descriptions-item"),h=n("el-descriptions"),x=n("el-card"),H=n("el-input"),M=n("el-form-item"),S=n("el-form"),W=n("el-dialog");return d(),o("div",b,[u(g,{title:"个人信息",description:"查看和修改您的个人账户信息"}),u(x,{shadow:"hover",class:"profile-card"},{header:i((()=>[m("div",y,[a[6]||(a[6]=m("h3",null,"个人信息",-1)),u(l,{type:"primary",onClick:q},{default:i((()=>a[5]||(a[5]=[f("编辑")]))),_:1})])])),default:i((()=>[m("div",V,[m("div",w,[m("div",j,[u(t,{class:"avatar-uploader",action:z,headers:B.value,"show-file-list":!1,"on-success":I,"before-upload":R,"on-error":D},{default:i((()=>[u(r,{size:100,src:"http://localhost/files/uploads/avatars/user1_1747191994946.jpg",class:"profile-avatar"}),m("div",U,[u(s,null,{default:i((()=>[u(p(c))])),_:1}),a[7]||(a[7]=m("span",null,"点击更换",-1))])])),_:1},8,["headers"])])]),m("div",k,[u(h,{column:2,border:""},{default:i((()=>[u(_,{label:"用户名"},{default:i((()=>[f(v(C.value.username),1)])),_:1}),u(_,{label:"姓名"},{default:i((()=>[f(v(C.value.name),1)])),_:1}),u(_,{label:"邮箱"},{default:i((()=>[f(v(C.value.email),1)])),_:1}),u(_,{label:"电话"},{default:i((()=>[f(v(C.value.phone),1)])),_:1}),u(_,{label:"部门"},{default:i((()=>[f(v(C.value.department),1)])),_:1}),u(_,{label:"职位"},{default:i((()=>[f(v(C.value.position),1)])),_:1}),u(_,{label:"角色"},{default:i((()=>{var e;return[f(v((null==(e=C.value.roles)?void 0:e.join(", "))||"无"),1)]})),_:1}),u(_,{label:"最后登录"},{default:i((()=>{return[f(v((e=C.value.lastLogin,e?new Date(e).toLocaleString():"未登录")),1)];var e})),_:1})])),_:1})])])])),_:1}),u(W,{modelValue:A.value,"onUpdate:modelValue":a[4]||(a[4]=e=>A.value=e),title:"编辑个人信息",width:"500px","destroy-on-close":""},{footer:i((()=>[u(l,{onClick:a[3]||(a[3]=e=>A.value=!1)},{default:i((()=>a[8]||(a[8]=[f("取消")]))),_:1}),u(l,{type:"primary",onClick:F},{default:i((()=>a[9]||(a[9]=[f("保存")]))),_:1})])),default:i((()=>[u(S,{ref_key:"editFormRef",ref:P,model:L,rules:$,"label-width":"100px"},{default:i((()=>[u(M,{label:"姓名",prop:"name"},{default:i((()=>[u(H,{modelValue:L.name,"onUpdate:modelValue":a[0]||(a[0]=e=>L.name=e)},null,8,["modelValue"])])),_:1}),u(M,{label:"邮箱",prop:"email"},{default:i((()=>[u(H,{modelValue:L.email,"onUpdate:modelValue":a[1]||(a[1]=e=>L.email=e)},null,8,["modelValue"])])),_:1}),u(M,{label:"电话",prop:"phone"},{default:i((()=>[u(H,{modelValue:L.phone,"onUpdate:modelValue":a[2]||(a[2]=e=>L.phone=e)},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-85e50b91"]]);export{x as default};
