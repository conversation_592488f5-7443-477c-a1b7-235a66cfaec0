using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Domain.Entities
{
    /// <summary>
    /// 备件库存明细实体（支持状态区分）
    /// </summary>
    [Table("spare_part_inventories")]
    public class SparePartInventory
    {
        /// <summary>
        /// 库存明细ID
        /// </summary>
        [Key]
        [Column("id")]
        public long Id { get; set; }
        
        /// <summary>
        /// 备件ID
        /// </summary>
        [Column("part_id")]
        public long PartId { get; set; }
        
        /// <summary>
        /// 备件导航属性
        /// </summary>
        [ForeignKey("PartId")]
        public virtual SparePart Part { get; set; }
        
        /// <summary>
        /// 库位ID
        /// </summary>
        [Column("location_id")]
        public long LocationId { get; set; }
        
        /// <summary>
        /// 库位导航属性
        /// </summary>
        [ForeignKey("LocationId")]
        public virtual SparePartLocation Location { get; set; }
        
        /// <summary>
        /// 状态ID
        /// </summary>
        [Column("status_id")]
        public int StatusId { get; set; }
        
        /// <summary>
        /// 状态导航属性
        /// </summary>
        [ForeignKey("StatusId")]
        public virtual SparePartStatusType Status { get; set; }
        
        /// <summary>
        /// 数量
        /// </summary>
        [Column("quantity")]
        public int Quantity { get; set; }
        
        /// <summary>
        /// 批次号
        /// </summary>
        [Column("batch_number")]
        [StringLength(50)]
        public string BatchNumber { get; set; }
        
        /// <summary>
        /// 序列号列表（JSON格式）
        /// </summary>
        [Column("serial_numbers")]
        public string SerialNumbers { get; set; }
        
        /// <summary>
        /// 采购日期
        /// </summary>
        [Column("purchase_date")]
        public DateTime? PurchaseDate { get; set; }
        
        /// <summary>
        /// 保修到期日期
        /// </summary>
        [Column("warranty_expire_date")]
        public DateTime? WarrantyExpireDate { get; set; }
        
        /// <summary>
        /// 供应商ID
        /// </summary>
        [Column("supplier_id")]
        public int? SupplierId { get; set; }
        
        /// <summary>
        /// 单位成本
        /// </summary>
        [Column("unit_cost")]
        public decimal? UnitCost { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        [Column("notes")]
        [StringLength(500)]
        public string Notes { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        /// <summary>
        /// 更新时间
        /// </summary>
        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
        
        /// <summary>
        /// 状态变更历史导航属性
        /// </summary>
        public virtual ICollection<SparePartStatusHistory> StatusHistories { get; set; } = new List<SparePartStatusHistory>();
        
        /// <summary>
        /// 交易记录导航属性
        /// </summary>
        public virtual ICollection<SparePartTransaction> Transactions { get; set; } = new List<SparePartTransaction>();
    }
}
