import request from '@/utils/request'

/**
 * 游戏化系统API
 */

/**
 * 获取当前用户游戏化统计信息
 */
export function getUserStats() {
  return request({
    url: '/api/v2/gamification/stats',
    method: 'get'
  })
}

/**
 * 获取指定用户游戏化统计信息
 * @param {number} userId - 用户ID
 */
export function getUserStatsById(userId) {
  return request({
    url: `/api/v2/gamification/stats/${userId}`,
    method: 'get'
  })
}

/**
 * 获取用户每日任务统计
 * @param {string} date - 日期 (可选，格式: YYYY-MM-DD)
 */
export function getDailyTaskStats(date = null) {
  const params = {}
  if (date) {
    params.date = date
  }
  
  return request({
    url: '/api/v2/gamification/daily-stats',
    method: 'get',
    params
  })
}

/**
 * 获取排行榜
 * @param {number} type - 排行榜类型 (1=Weekly, 2=Monthly, 3=AllTime)
 * @param {number} topN - 前N名 (默认10)
 */
export function getLeaderboard(type = 1, topN = 10) {
  return request({
    url: '/api/v2/gamification/leaderboard',
    method: 'get',
    params: {
      type,
      topN
    }
  })
}

/**
 * 获取当前用户在排行榜中的位置
 * @param {number} type - 排行榜类型 (1=Weekly, 2=Monthly, 3=AllTime)
 */
export function getMyRank(type = 1) {
  return request({
    url: '/api/v2/gamification/my-rank',
    method: 'get',
    params: {
      type
    }
  })
}

/**
 * 初始化用户游戏化数据
 */
export function initializeUserStats() {
  return request({
    url: '/api/v2/gamification/initialize',
    method: 'post'
  })
}

/**
 * 手动触发任务领取奖励 (测试用)
 * @param {number} taskId - 任务ID
 */
export function triggerClaimReward(taskId) {
  return request({
    url: `/api/v2/gamification/rewards/claim/${taskId}`,
    method: 'post'
  })
}

/**
 * 手动触发任务完成奖励 (测试用)
 * @param {number} taskId - 任务ID
 * @param {boolean} isOnTime - 是否按时完成
 */
export function triggerCompleteReward(taskId, isOnTime = false) {
  return request({
    url: `/api/v2/gamification/rewards/complete/${taskId}`,
    method: 'post',
    params: {
      isOnTime
    }
  })
}

/**
 * 任务领取API (集成游戏化奖励)
 * @param {number} taskId - 任务ID
 * @param {string} notes - 备注
 */
export function claimTask(taskId, notes = '') {
  return request({
    url: `/api/v2/tasks/${taskId}/claim`,
    method: 'post',
    data: {
      notes
    }
  })
}

/**
 * 获取用户今日任务领取记录
 */
export function getUserTodayClaims() {
  return request({
    url: '/api/v2/tasks/claims/today',
    method: 'get'
  })
}

/**
 * 更新任务领取状态
 * @param {number} claimId - 领取记录ID
 * @param {string} status - 新状态 (Claimed, Started, Completed, Cancelled)
 * @param {string} notes - 备注
 */
export function updateClaimStatus(claimId, status, notes = '') {
  return request({
    url: `/api/v2/tasks/claims/${claimId}/status`,
    method: 'patch',
    data: {
      claimStatus: status,
      notes
    }
  })
}

/**
 * 获取今日班次任务统计
 * @param {Object} params - 查询参数
 * @param {number} params.shiftId - 班次ID (可选)
 * @param {number} params.userId - 用户ID (可选)
 * @param {string} params.statisticsDate - 统计日期 (可选)
 */
export function getTodayShiftStatistics(params = {}) {
  return request({
    url: '/api/v2/tasks/claims/statistics',
    method: 'get',
    params
  })
}

/**
 * 获取周统计汇总 - 所有人按周的任务创建、领取、完成数量
 * @param {number} weekOffset - 周偏移量 (0=本周, -1=上周, 1=下周)
 */
export function getWeeklyStats(weekOffset = 0) {
  return request({
    url: '/api/v2/gamification/weekly-stats',
    method: 'get',
    params: {
      weekOffset
    }
  })
}

/**
 * 获取用户周统计详情
 * @param {number} userId - 用户ID (可选，默认当前用户)
 * @param {number} weekOffset - 周偏移量 (0=本周, -1=上周, 1=下周)
 */
export function getUserWeeklyStats(userId = null, weekOffset = 0) {
  const params = { weekOffset }
  if (userId) {
    params.userId = userId
  }

  return request({
    url: '/api/v2/gamification/weekly-stats/user',
    method: 'get',
    params
  })
}

// 排行榜类型常量
export const LeaderboardType = {
  WEEKLY: 1,
  MONTHLY: 2,
  ALL_TIME: 3
}

// 任务领取状态常量
export const TaskClaimStatus = {
  CLAIMED: 'Claimed',
  STARTED: 'Started', 
  COMPLETED: 'Completed',
  CANCELLED: 'Cancelled'
}

// 游戏化事件类型常量
export const GamificationEventType = {
  TASK_COMPLETED: 'TaskCompleted',
  TASK_CLAIMED: 'TaskClaimed',
  TASK_CREATED: 'TaskCreated',
  TASK_UPDATED: 'TaskUpdated',
  BADGE_EARNED: 'BadgeEarned',
  LEVEL_UP: 'LevelUp',
  POINTS_SPENT: 'PointsSpent',
  DAILY_LOGIN: 'DailyLogin',
  COMMENT_ADDED: 'CommentAdded',
  STREAK_MAINTAINED: 'StreakMaintained',
  ON_TIME_COMPLETION: 'OnTimeCompletion'
}

export default {
  getUserStats,
  getUserStatsById,
  getDailyTaskStats,
  getLeaderboard,
  getMyRank,
  initializeUserStats,
  triggerClaimReward,
  triggerCompleteReward,
  claimTask,
  getUserTodayClaims,
  updateClaimStatus,
  getTodayShiftStatistics,
  getWeeklyStats,
  getUserWeeklyStats,
  LeaderboardType,
  TaskClaimStatus,
  GamificationEventType
}
