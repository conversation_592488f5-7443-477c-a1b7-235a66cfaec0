<template>
  <el-dialog :model-value="visible" @update:model-value="$emit('update:visible', $event)" title="库存状态调整" width="800px" @close="handleClose">
    <div class="status-adjustment-container">
      <!-- 当前库存状态概览 -->
      <el-card class="current-status-card" style="margin-bottom: 16px;">
        <template #header>
          <span>当前库存状态</span>
        </template>
        <el-row :gutter="16">
          <el-col v-for="status in currentStatusBreakdown" :key="status.statusCode" :span="6">
            <div class="status-item">
              <div class="status-name" :style="{ color: status.color }">{{ status.statusName }}</div>
              <div class="status-quantity">{{ status.quantity }} {{ unit }}</div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 状态调整表单 -->
      <el-form ref="adjustmentFormRef" :model="adjustmentForm" label-width="120px">
        <div class="adjustment-grid">
          <div v-for="status in statusTypes" :key="status.id" class="adjustment-item">
            <el-card class="status-adjustment-card">
              <template #header>
                <div class="status-header">
                  <span :style="{ color: status.color }">{{ status.name }}</span>
                  <span class="current-qty">(当前: {{ getCurrentQuantity(status.code) }})</span>
                </div>
              </template>
              
              <div class="adjustment-controls">
                <el-form-item :label="`调整数量`" style="margin-bottom: 8px;">
                  <el-input-number
                    v-model="adjustmentForm.adjustments[status.id]"
                    :min="getMinQuantity(status.code)"
                    :max="getMaxQuantity(status.code)"
                    :step="1"
                    size="small"
                    style="width: 100%"
                    @change="handleQuantityChange(status.id, $event)"
                  />
                </el-form-item>
                
                <div class="adjustment-buttons">
                  <el-button-group size="small">
                    <el-button @click="quickAdjust(status.id, 1)">+1</el-button>
                    <el-button @click="quickAdjust(status.id, 5)">+5</el-button>
                    <el-button @click="quickAdjust(status.id, -1)">-1</el-button>
                    <el-button @click="quickAdjust(status.id, -5)">-5</el-button>
                  </el-button-group>
                </div>
                
                <div class="result-quantity">
                  调整后: {{ getResultQuantity(status.code) }} {{ unit }}
                </div>
              </div>
            </el-card>
          </div>
        </div>

        <!-- 调整原因 -->
        <el-form-item label="调整原因" prop="reason" style="margin-top: 16px;">
          <el-input
            v-model="adjustmentForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入状态调整原因（必填）"
          />
        </el-form-item>

        <!-- 调整汇总 -->
        <el-card class="adjustment-summary" style="margin-top: 16px;">
          <template #header>
            <span>调整汇总</span>
          </template>
          <div class="summary-content">
            <div class="summary-item">
              <span>调整前总数: {{ totalBefore }} {{ unit }}</span>
            </div>
            <div class="summary-item">
              <span>调整后总数: {{ totalAfter }} {{ unit }}</span>
            </div>
            <div class="summary-item" :class="{ 'quantity-change': totalChange !== 0 }">
              <span>数量变化: {{ totalChange > 0 ? '+' : '' }}{{ totalChange }} {{ unit }}</span>
            </div>
          </div>
        </el-card>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :disabled="!canSubmit">确认调整</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  partId: {
    type: Number,
    required: true
  },
  currentStatusBreakdown: {
    type: Array,
    default: () => []
  },
  statusTypes: {
    type: Array,
    default: () => []
  },
  unit: {
    type: String,
    default: '个'
  }
})

// Emits
const emit = defineEmits(['update:visible', 'submit'])

// 响应式数据
const adjustmentFormRef = ref()
const adjustmentForm = reactive({
  adjustments: {},
  reason: ''
})

// 初始化调整数量
const initializeAdjustments = () => {
  const adjustments = {}
  props.statusTypes.forEach(status => {
    adjustments[status.id] = getCurrentQuantity(status.code)
  })
  adjustmentForm.adjustments = adjustments
}

// 计算属性
const totalBefore = computed(() => {
  return props.currentStatusBreakdown.reduce((sum, status) => sum + status.quantity, 0)
})

const totalAfter = computed(() => {
  return Object.values(adjustmentForm.adjustments).reduce((sum, qty) => sum + (qty || 0), 0)
})

const totalChange = computed(() => {
  return totalAfter.value - totalBefore.value
})

const canSubmit = computed(() => {
  return adjustmentForm.reason.trim() !== '' && hasChanges.value
})

const hasChanges = computed(() => {
  return props.statusTypes.some(status => {
    const currentQty = getCurrentQuantity(status.code)
    const newQty = adjustmentForm.adjustments[status.id] || 0
    return currentQty !== newQty
  })
})

// 方法
const getCurrentQuantity = (statusCode) => {
  const status = props.currentStatusBreakdown.find(s => s.statusCode === statusCode)
  return status ? status.quantity : 0
}

const getResultQuantity = (statusCode) => {
  const status = props.statusTypes.find(s => s.code === statusCode)
  return status ? (adjustmentForm.adjustments[status.id] || 0) : 0
}

const getMinQuantity = (statusCode) => {
  return 0 // 最小数量为0
}

const getMaxQuantity = (statusCode) => {
  return 9999 // 设置一个合理的最大值
}

const quickAdjust = (statusId, delta) => {
  const currentValue = adjustmentForm.adjustments[statusId] || 0
  const newValue = Math.max(0, currentValue + delta)
  adjustmentForm.adjustments[statusId] = newValue
}

const handleQuantityChange = (statusId, value) => {
  adjustmentForm.adjustments[statusId] = value || 0
}

const handleSubmit = async () => {
  if (!adjustmentForm.reason.trim()) {
    ElMessage.warning('请输入调整原因')
    return
  }

  if (!hasChanges.value) {
    ElMessage.warning('没有检测到状态数量变化')
    return
  }

  // 构建调整数据
  const adjustments = []
  props.statusTypes.forEach(status => {
    const currentQty = getCurrentQuantity(status.code)
    const newQty = adjustmentForm.adjustments[status.id] || 0
    
    if (currentQty !== newQty) {
      adjustments.push({
        statusId: status.id,
        statusCode: status.code,
        statusName: status.name,
        fromQuantity: currentQty,
        toQuantity: newQty,
        changeQuantity: newQty - currentQty
      })
    }
  })

  const submitData = {
    partId: props.partId,
    adjustments: adjustments,
    reason: adjustmentForm.reason,
    totalChange: totalChange.value
  }

  emit('submit', submitData)
}

const handleClose = () => {
  emit('update:visible', false)
  // 重置表单
  adjustmentForm.reason = ''
  initializeAdjustments()
}

// 监听props变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    initializeAdjustments()
  }
})

watch(() => props.statusTypes, () => {
  if (props.visible) {
    initializeAdjustments()
  }
}, { deep: true })
</script>

<style scoped>
.status-adjustment-container {
  max-height: 600px;
  overflow-y: auto;
}

.current-status-card .status-item {
  text-align: center;
  padding: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.status-item .status-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.status-item .status-quantity {
  font-size: 18px;
  font-weight: bold;
}

.adjustment-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.status-adjustment-card {
  border: 1px solid #e4e7ed;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-qty {
  font-size: 12px;
  color: #909399;
}

.adjustment-controls {
  text-align: center;
}

.adjustment-buttons {
  margin: 8px 0;
}

.result-quantity {
  font-size: 14px;
  font-weight: bold;
  color: #409eff;
  margin-top: 8px;
}

.adjustment-summary .summary-content {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.summary-item {
  font-size: 14px;
}

.quantity-change {
  font-weight: bold;
  color: #e6a23c;
}

.dialog-footer {
  text-align: right;
}
</style>
