import{_ as s,b as a,d as r,e,w as o,a as t,u as c,o as i,A as l}from"./index-C7OOw0MO.js";const n={class:"error-page"},d={class:"error-container"},m={class:"error-image"},p={class:"error-actions"},u=s({__name:"404",setup(s){const u=c(),g=()=>{u.go(-1)},_=()=>{u.push("/")};return(s,c)=>{const u=t("el-image"),v=t("el-button");return i(),a("div",n,[r("div",d,[r("div",m,[e(u,{src:"/images/404.svg",alt:"404",class:"error-img"})]),c[2]||(c[2]=r("h1",{class:"error-title"},"404",-1)),c[3]||(c[3]=r("p",{class:"error-message"},"抱歉，您访问的页面不存在",-1)),r("div",p,[e(v,{type:"primary",onClick:g},{default:o((()=>c[0]||(c[0]=[l("返回上一页")]))),_:1}),e(v,{onClick:_},{default:o((()=>c[1]||(c[1]=[l("返回首页")]))),_:1})])])])}}},[["__scopeId","data-v-a0d0a9b3"]]);export{u as default};
