# 基础解耦回滚脚本
# 用于紧急情况下快速回滚到原始状态

param(
    [switch]$Force,
    [switch]$BackupFirst = $true
)

Write-Host "🔄 基础解耦回滚脚本" -ForegroundColor Yellow
Write-Host "===========================================" -ForegroundColor Gray

if (-not $Force) {
    Write-Host "⚠️ 警告: 此操作将回滚基础解耦的所有更改!" -ForegroundColor Red
    Write-Host "这将会:" -ForegroundColor Yellow
    Write-Host "  - 移除新增的服务接口" -ForegroundColor Yellow
    Write-Host "  - 删除V1.1版本的控制器" -ForegroundColor Yellow
    Write-Host "  - 恢复原始的Startup.cs配置" -ForegroundColor Yellow
    Write-Host "  - 移除性能监控相关代码" -ForegroundColor Yellow
    
    $confirmation = Read-Host "`n确定要继续吗? (输入 'YES' 确认)"
    if ($confirmation -ne "YES") {
        Write-Host "❌ 回滚操作已取消" -ForegroundColor Red
        exit 1
    }
}

Write-Host "`n🚀 开始回滚操作..." -ForegroundColor Green

# 创建备份目录
$backupDir = "backup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
if ($BackupFirst) {
    Write-Host "📦 创建备份: $backupDir" -ForegroundColor Cyan
    New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
    
    # 备份修改的文件
    $filesToBackup = @(
        "Startup.cs",
        "Core/Interfaces/Services/IUserService.cs",
        "Application/Services/Adapters/UserServiceAdapter.cs",
        "Controllers/V1_1/UserController.cs",
        "Core/Monitoring/PerformanceMonitor.cs",
        "Controllers/MonitoringController.cs"
    )
    
    foreach ($file in $filesToBackup) {
        if (Test-Path $file) {
            $backupPath = Join-Path $backupDir $file
            $backupParentDir = Split-Path $backupPath -Parent
            New-Item -ItemType Directory -Path $backupParentDir -Force | Out-Null
            Copy-Item $file $backupPath -Force
            Write-Host "  ✅ 已备份: $file" -ForegroundColor Gray
        }
    }
}

# 回滚步骤
Write-Host "`n🔧 执行回滚步骤..." -ForegroundColor Cyan

# 1. 恢复Startup.cs
Write-Host "1. 恢复Startup.cs配置..." -ForegroundColor Yellow
if (Test-Path "Startup.cs.backup") {
    Copy-Item "Startup.cs.backup" "Startup.cs" -Force
    Write-Host "   ✅ 已从备份恢复Startup.cs" -ForegroundColor Green
} else {
    # 手动移除新增的服务注册
    $startupContent = Get-Content "Startup.cs" -Raw
    
    # 移除基础解耦相关的注册
    $startupContent = $startupContent -replace "(?s)// ========== 基础解耦.*?// 这样可以确保现有代码继续正常工作，同时新代码可以使用新接口", ""
    
    Set-Content "Startup.cs" $startupContent
    Write-Host "   ✅ 已移除新增的服务注册" -ForegroundColor Green
}

# 2. 删除新增的接口文件
Write-Host "2. 删除新增的接口文件..." -ForegroundColor Yellow
$interfaceFiles = @(
    "Core/Interfaces/Services/IUserService.cs",
    "Core/Interfaces/Services/IAssetService.cs",
    "Core/Interfaces/Services/ITaskService.cs"
)

foreach ($file in $interfaceFiles) {
    if (Test-Path $file) {
        Remove-Item $file -Force
        Write-Host "   ✅ 已删除: $file" -ForegroundColor Green
    }
}

# 删除空目录
if (Test-Path "Core/Interfaces/Services" -and (Get-ChildItem "Core/Interfaces/Services").Count -eq 0) {
    Remove-Item "Core/Interfaces/Services" -Force
    Write-Host "   ✅ 已删除空目录: Core/Interfaces/Services" -ForegroundColor Green
}

if (Test-Path "Core/Interfaces" -and (Get-ChildItem "Core/Interfaces").Count -eq 0) {
    Remove-Item "Core/Interfaces" -Force
    Write-Host "   ✅ 已删除空目录: Core/Interfaces" -ForegroundColor Green
}

# 3. 删除适配器文件
Write-Host "3. 删除适配器文件..." -ForegroundColor Yellow
$adapterFiles = @(
    "Application/Services/Adapters/UserServiceAdapter.cs"
)

foreach ($file in $adapterFiles) {
    if (Test-Path $file) {
        Remove-Item $file -Force
        Write-Host "   ✅ 已删除: $file" -ForegroundColor Green
    }
}

# 删除空目录
if (Test-Path "Application/Services/Adapters" -and (Get-ChildItem "Application/Services/Adapters").Count -eq 0) {
    Remove-Item "Application/Services/Adapters" -Force
    Write-Host "   ✅ 已删除空目录: Application/Services/Adapters" -ForegroundColor Green
}

# 4. 删除V1.1控制器
Write-Host "4. 删除V1.1控制器..." -ForegroundColor Yellow
if (Test-Path "Controllers/V1_1") {
    Remove-Item "Controllers/V1_1" -Recurse -Force
    Write-Host "   ✅ 已删除: Controllers/V1_1" -ForegroundColor Green
}

# 5. 删除监控相关文件
Write-Host "5. 删除监控相关文件..." -ForegroundColor Yellow
$monitoringFiles = @(
    "Core/Monitoring/PerformanceMonitor.cs",
    "Controllers/MonitoringController.cs"
)

foreach ($file in $monitoringFiles) {
    if (Test-Path $file) {
        Remove-Item $file -Force
        Write-Host "   ✅ 已删除: $file" -ForegroundColor Green
    }
}

# 删除空目录
if (Test-Path "Core/Monitoring" -and (Get-ChildItem "Core/Monitoring").Count -eq 0) {
    Remove-Item "Core/Monitoring" -Force
    Write-Host "   ✅ 已删除空目录: Core/Monitoring" -ForegroundColor Green
}

# 6. 删除测试脚本
Write-Host "6. 删除测试脚本..." -ForegroundColor Yellow
$testFiles = @(
    "test-decoupling.ps1",
    "基础解耦安全实施方案.md"
)

foreach ($file in $testFiles) {
    if (Test-Path $file) {
        Remove-Item $file -Force
        Write-Host "   ✅ 已删除: $file" -ForegroundColor Green
    }
}

# 验证回滚结果
Write-Host "`n🔍 验证回滚结果..." -ForegroundColor Cyan

$rollbackSuccess = $true
$filesToCheck = @(
    "Core/Interfaces/Services/IUserService.cs",
    "Application/Services/Adapters/UserServiceAdapter.cs",
    "Controllers/V1_1/UserController.cs",
    "Core/Monitoring/PerformanceMonitor.cs",
    "Controllers/MonitoringController.cs"
)

foreach ($file in $filesToCheck) {
    if (Test-Path $file) {
        Write-Host "   ❌ 文件仍然存在: $file" -ForegroundColor Red
        $rollbackSuccess = $false
    }
}

# 检查Startup.cs是否包含基础解耦相关内容
$startupContent = Get-Content "Startup.cs" -Raw
if ($startupContent -match "基础解耦") {
    Write-Host "   ❌ Startup.cs仍包含基础解耦相关内容" -ForegroundColor Red
    $rollbackSuccess = $false
}

# 输出结果
Write-Host "`n📋 回滚结果:" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Gray

if ($rollbackSuccess) {
    Write-Host "✅ 回滚成功完成!" -ForegroundColor Green
    Write-Host "   所有基础解耦相关的更改已被移除" -ForegroundColor Gray
    Write-Host "   系统已恢复到原始状态" -ForegroundColor Gray
    
    if ($BackupFirst) {
        Write-Host "   备份文件保存在: $backupDir" -ForegroundColor Gray
    }
    
    Write-Host "`n🔄 建议操作:" -ForegroundColor Cyan
    Write-Host "   1. 重启应用程序以确保更改生效" -ForegroundColor Yellow
    Write-Host "   2. 测试原有功能是否正常" -ForegroundColor Yellow
    Write-Host "   3. 检查日志确认无异常" -ForegroundColor Yellow
} else {
    Write-Host "❌ 回滚未完全成功!" -ForegroundColor Red
    Write-Host "   请手动检查并清理剩余文件" -ForegroundColor Yellow
    Write-Host "   或联系开发团队协助处理" -ForegroundColor Yellow
}

Write-Host "`n🎯 回滚脚本执行完成!" -ForegroundColor Green
