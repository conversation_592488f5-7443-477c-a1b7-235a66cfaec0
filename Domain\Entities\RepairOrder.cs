// File: Domain/Entities/RepairOrder.cs
// Description: 返厂维修单主表实体，支持主表-明细表设计模式，扩展支持备件状态管理和故障联动

#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ItAssetsSystem.Models.Entities;

namespace ItAssetsSystem.Domain.Entities;

/// <summary>
/// 返厂维修记录实体（扩展版）
/// </summary>
[Table("repair_orders")]
public class RepairOrder
{
    /// <summary>
    /// 返厂单ID
    /// </summary>
    [Key]
    [Column("id")]
    public int Id { get; set; }

    /// <summary>
    /// 返厂单号
    /// </summary>
    [Required]
    [Column("order_code")]
    [StringLength(50)]
    public string OrderCode { get; set; } = null!;

    /// <summary>
    /// 维修类型
    /// </summary>
    [Column("repair_type")]
    public RepairOrderType RepairType { get; set; } = RepairOrderType.SparePartRepair;

    /// <summary>
    /// 维修标题
    /// </summary>
    [Column("title")]
    [StringLength(200)]
    public string? Title { get; set; }

    /// <summary>
    /// 维修描述
    /// </summary>
    [Column("description")]
    public string? Description { get; set; }

    /// <summary>
    /// 优先级
    /// </summary>
    [Column("priority")]
    public RepairPriority Priority { get; set; } = RepairPriority.Medium;

    /// <summary>
    /// 关联故障ID
    /// </summary>
    [Column("fault_id")]
    public int? FaultId { get; set; }

    /// <summary>
    /// 关联资产ID
    /// </summary>
    [Column("asset_id")]
    public int? AssetId { get; set; }

    /// <summary>
    /// 供应商ID
    /// </summary>
    [Column("supplier_id")]
    public int SupplierId { get; set; }

    /// <summary>
    /// 发货日期
    /// </summary>
    [Column("send_date")]
    public DateTime? SendDate { get; set; }

    /// <summary>
    /// 预计返回日期
    /// </summary>
    [Column("expected_return_date")]
    public DateTime? ExpectedReturnDate { get; set; }

    /// <summary>
    /// 实际返回日期
    /// </summary>
    [Column("actual_return_date")]
    public DateTime? ActualReturnDate { get; set; }

    /// <summary>
    /// 预估费用
    /// </summary>
    [Column("estimated_cost")]
    public decimal? EstimatedCost { get; set; }

    /// <summary>
    /// 总费用
    /// </summary>
    [Column("total_cost")]
    public decimal TotalCost { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Column("status")]
    public RepairOrderStatus Status { get; set; } = RepairOrderStatus.PendingApproval;

    /// <summary>
    /// 备注
    /// </summary>
    [Column("notes")]
    [StringLength(500)]
    public string? Notes { get; set; }

    /// <summary>
    /// 维修结果
    /// </summary>
    [Column("repair_result")]
    public string? RepairResult { get; set; }

    /// <summary>
    /// 创建人ID
    /// </summary>
    [Column("creator_id")]
    public int CreatorId { get; set; }

    /// <summary>
    /// 审核人ID
    /// </summary>
    [Column("approver_id")]
    public int? ApproverId { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [Column("created_at")]
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    [Column("updated_at")]
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    // 导航属性
    public virtual Supplier Supplier { get; set; } = null!;
    public virtual User Creator { get; set; } = null!;
    public virtual User? Approver { get; set; }
    public virtual ICollection<RepairItem> RepairItems { get; set; } = new List<RepairItem>();
    // 暂时注释掉，避免Entity Framework关系配置错误
    // public virtual ICollection<SparePartTransaction> Transactions { get; set; } = new List<SparePartTransaction>();
}

/// <summary>
/// 返厂维修类型枚举
/// </summary>
public enum RepairOrderType
{
    /// <summary>
    /// 故障维修
    /// </summary>
    FaultRepair = 1,

    /// <summary>
    /// 备件维修
    /// </summary>
    SparePartRepair = 2,

    /// <summary>
    /// 预防性维修
    /// </summary>
    PreventiveMaintenance = 3
}

/// <summary>
/// 维修优先级枚举
/// </summary>
public enum RepairPriority
{
    /// <summary>
    /// 紧急
    /// </summary>
    Emergency = 1,

    /// <summary>
    /// 高
    /// </summary>
    High = 2,

    /// <summary>
    /// 中
    /// </summary>
    Medium = 3,

    /// <summary>
    /// 低
    /// </summary>
    Low = 4
}

/// <summary>
/// 返厂维修状态枚举
/// </summary>
public enum RepairOrderStatus
{
    /// <summary>
    /// 待审核
    /// </summary>
    PendingApproval = 1,

    /// <summary>
    /// 已审核
    /// </summary>
    Approved = 2,

    /// <summary>
    /// 已发货
    /// </summary>
    Shipped = 3,

    /// <summary>
    /// 维修中
    /// </summary>
    InRepair = 4,

    /// <summary>
    /// 已完成
    /// </summary>
    Completed = 5,

    /// <summary>
    /// 已取消
    /// </summary>
    Cancelled = 6
}