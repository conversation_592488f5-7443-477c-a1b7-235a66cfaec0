<template>
  <div class="performance-dashboard">
    <el-card class="dashboard-card">
      <template #header>
        <div class="card-header">
          <span>API性能对比仪表板</span>
          <div class="header-actions">
            <el-switch
              v-model="monitoringEnabled"
              @change="toggleMonitoring"
              active-text="监控开启"
              inactive-text="监控关闭"
            />
            <el-button 
              type="primary" 
              size="small" 
              @click="refreshData"
              :loading="loading"
            >
              刷新数据
            </el-button>
            <el-button 
              type="success" 
              size="small" 
              @click="exportData"
            >
              导出数据
            </el-button>
            <el-button 
              type="warning" 
              size="small" 
              @click="clearData"
            >
              清除数据
            </el-button>
          </div>
        </div>
      </template>

      <!-- 实时指标概览 -->
      <div class="metrics-overview">
        <h4>实时性能指标</h4>
        <el-row :gutter="16">
          <el-col 
            v-for="(metric, key) in realTimeMetrics" 
            :key="key"
            :span="8"
          >
            <el-card class="metric-card" shadow="hover">
              <div class="metric-info">
                <div class="metric-name">{{ formatApiName(key) }}</div>
                <div class="metric-value">
                  <span class="duration">{{ metric.avgDuration.toFixed(2) }}ms</span>
                  <span class="success-rate" :class="getSuccessRateClass(metric.successRate)">
                    {{ metric.successRate.toFixed(1) }}%
                  </span>
                </div>
                <div class="metric-details">
                  最近{{ metric.recentCalls }}次调用 | {{ formatTime(metric.lastCall) }}
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 版本对比详情 -->
      <div class="comparison-details" v-if="Object.keys(comparisons).length > 0">
        <h4>版本性能对比</h4>
        <el-tabs v-model="activeTab" type="card">
          <el-tab-pane 
            v-for="(comparison, apiName) in comparisons"
            :key="apiName"
            :label="formatApiName(apiName)"
            :name="apiName"
          >
            <div class="comparison-content">
              <!-- 对比摘要 -->
              <div class="comparison-summary">
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-card class="version-card v1-card">
                      <template #header>
                        <span>V1 版本</span>
                      </template>
                      <div class="version-stats">
                        <div class="stat-item">
                          <span class="label">平均响应时间:</span>
                          <span class="value">{{ comparison.v1.avgDuration.toFixed(2) }}ms</span>
                        </div>
                        <div class="stat-item">
                          <span class="label">成功率:</span>
                          <span class="value">{{ comparison.v1.successRate.toFixed(1) }}%</span>
                        </div>
                        <div class="stat-item">
                          <span class="label">内存使用:</span>
                          <span class="value">{{ formatBytes(comparison.v1.avgMemory) }}</span>
                        </div>
                        <div class="stat-item">
                          <span class="label">调用次数:</span>
                          <span class="value">{{ comparison.v1.count }}</span>
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                  <el-col :span="12">
                    <el-card class="version-card v1-1-card">
                      <template #header>
                        <span>V1.1 版本</span>
                      </template>
                      <div class="version-stats">
                        <div class="stat-item">
                          <span class="label">平均响应时间:</span>
                          <span class="value">{{ comparison.v1_1.avgDuration.toFixed(2) }}ms</span>
                        </div>
                        <div class="stat-item">
                          <span class="label">成功率:</span>
                          <span class="value">{{ comparison.v1_1.successRate.toFixed(1) }}%</span>
                        </div>
                        <div class="stat-item">
                          <span class="label">内存使用:</span>
                          <span class="value">{{ formatBytes(comparison.v1_1.avgMemory) }}</span>
                        </div>
                        <div class="stat-item">
                          <span class="label">调用次数:</span>
                          <span class="value">{{ comparison.v1_1.count }}</span>
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                </el-row>
              </div>

              <!-- 改进指标 -->
              <div class="improvement-metrics">
                <h5>性能改进指标</h5>
                <el-row :gutter="16">
                  <el-col :span="6">
                    <div class="improvement-item">
                      <div class="improvement-label">响应时间改进</div>
                      <div class="improvement-value" :class="getImprovementClass(comparison.comparison.durationImprovement)">
                        {{ formatImprovement(comparison.comparison.durationImprovement) }}
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="improvement-item">
                      <div class="improvement-label">内存使用改进</div>
                      <div class="improvement-value" :class="getImprovementClass(comparison.comparison.memoryImprovement)">
                        {{ formatImprovement(comparison.comparison.memoryImprovement) }}
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="improvement-item">
                      <div class="improvement-label">成功率改进</div>
                      <div class="improvement-value" :class="getImprovementClass(comparison.comparison.successRateImprovement)">
                        {{ formatImprovement(comparison.comparison.successRateImprovement) }}
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="improvement-item">
                      <div class="improvement-label">推荐建议</div>
                      <div class="recommendation" :class="getRecommendationClass(comparison.comparison.recommendation)">
                        {{ formatRecommendation(comparison.comparison.recommendation) }}
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <!-- 详细统计 -->
              <div class="detailed-stats">
                <h5>详细统计信息</h5>
                <el-table :data="getDetailedStatsData(comparison)" size="small">
                  <el-table-column prop="metric" label="指标" width="150" />
                  <el-table-column prop="v1" label="V1版本" width="120" />
                  <el-table-column prop="v1_1" label="V1.1版本" width="120" />
                  <el-table-column prop="improvement" label="改进幅度">
                    <template #default="{ row }">
                      <span :class="getImprovementClass(row.improvementValue)">
                        {{ row.improvement }}
                      </span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 无数据提示 -->
      <div v-else class="no-data">
        <el-empty description="暂无性能对比数据">
          <el-button type="primary" @click="startComparison">开始性能对比</el-button>
        </el-empty>
      </div>
    </el-card>
  </div>
</template>

<script>
import { useApiAdapter } from '@/plugins/api-adapter'

export default {
  name: 'PerformanceDashboard',
  setup() {
    const {
      apiAdapter,
      getPerformanceComparison,
      getRealTimeMetrics,
      clearPerformanceData,
      exportPerformanceData,
      setPerformanceMonitoringEnabled
    } = useApiAdapter()

    return {
      apiAdapter,
      getPerformanceComparison,
      getRealTimeMetrics,
      clearPerformanceData,
      exportPerformanceData,
      setPerformanceMonitoringEnabled
    }
  },
  data() {
    return {
      loading: false,
      monitoringEnabled: true,
      realTimeMetrics: {},
      comparisons: {},
      activeTab: '',
      refreshInterval: null
    }
  },
  async mounted() {
    await this.refreshData()
    this.startAutoRefresh()
    this.listenToPerformanceEvents()
  },
  beforeUnmount() {
    this.stopAutoRefresh()
  },
  methods: {
    async refreshData() {
      this.loading = true
      try {
        this.realTimeMetrics = this.getRealTimeMetrics()
        this.comparisons = this.getPerformanceComparison()
        
        // 设置默认活跃标签
        if (Object.keys(this.comparisons).length > 0 && !this.activeTab) {
          this.activeTab = Object.keys(this.comparisons)[0]
        }
      } catch (error) {
        this.$message.error('刷新数据失败: ' + error.message)
      } finally {
        this.loading = false
      }
    },

    toggleMonitoring(enabled) {
      this.setPerformanceMonitoringEnabled(enabled)
      this.$message.success(enabled ? '性能监控已启用' : '性能监控已禁用')
    },

    async startComparison() {
      try {
        // 切换到V1.1版本进行对比
        await this.apiAdapter.switchVersion('user', 'v1.1')
        await this.apiAdapter.switchVersion('asset', 'v1.1')
        await this.apiAdapter.switchVersion('task', 'v1.1')
        
        this.$message.success('已切换到V1.1版本，开始收集性能数据')
      } catch (error) {
        this.$message.error('启动性能对比失败: ' + error.message)
      }
    },

    exportData() {
      try {
        const data = this.exportPerformanceData()
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `performance-data-${new Date().toISOString().slice(0, 10)}.json`
        link.click()
        URL.revokeObjectURL(url)
        
        this.$message.success('性能数据导出成功')
      } catch (error) {
        this.$message.error('导出数据失败: ' + error.message)
      }
    },

    clearData() {
      this.$confirm('确定要清除所有性能数据吗？', '确认清除', {
        type: 'warning'
      }).then(() => {
        this.clearPerformanceData()
        this.refreshData()
        this.$message.success('性能数据已清除')
      }).catch(() => {
        // 用户取消
      })
    },

    startAutoRefresh() {
      this.refreshInterval = setInterval(() => {
        this.refreshData()
      }, 5000) // 每5秒刷新一次
    },

    stopAutoRefresh() {
      if (this.refreshInterval) {
        clearInterval(this.refreshInterval)
        this.refreshInterval = null
      }
    },

    listenToPerformanceEvents() {
      window.addEventListener('performanceComparison', (event) => {
        const { apiName, comparison } = event.detail
        this.comparisons[apiName] = comparison
        this.$message.info(`${this.formatApiName(apiName)} 性能对比数据已更新`)
      })
    },

    // 格式化方法
    formatApiName(key) {
      const parts = key.split('_')[0].split('.')
      if (parts.length === 2) {
        const [service, method] = parts
        const serviceNames = {
          user: '用户',
          asset: '资产',
          task: '任务'
        }
        return `${serviceNames[service] || service}.${method}`
      }
      return key
    },

    formatTime(timestamp) {
      if (!timestamp) return '-'
      return new Date(timestamp).toLocaleTimeString()
    },

    formatBytes(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    formatImprovement(value) {
      if (value > 0) {
        return `+${value.toFixed(1)}%`
      } else if (value < 0) {
        return `${value.toFixed(1)}%`
      }
      return '0%'
    },

    formatRecommendation(recommendation) {
      const recommendations = {
        strong_recommend_v1_1: '强烈推荐V1.1',
        recommend_v1_1: '推荐V1.1',
        recommend_v1: '推荐V1',
        neutral: '性能相当'
      }
      return recommendations[recommendation] || recommendation
    },

    getSuccessRateClass(rate) {
      if (rate >= 95) return 'success'
      if (rate >= 90) return 'warning'
      return 'danger'
    },

    getImprovementClass(value) {
      if (value > 5) return 'improvement-positive'
      if (value < -5) return 'improvement-negative'
      return 'improvement-neutral'
    },

    getRecommendationClass(recommendation) {
      if (recommendation.includes('recommend_v1_1')) return 'recommend-v1-1'
      if (recommendation.includes('recommend_v1')) return 'recommend-v1'
      return 'recommend-neutral'
    },

    getDetailedStatsData(comparison) {
      return [
        {
          metric: '平均响应时间',
          v1: `${comparison.v1.avgDuration.toFixed(2)}ms`,
          v1_1: `${comparison.v1_1.avgDuration.toFixed(2)}ms`,
          improvement: this.formatImprovement(comparison.comparison.durationImprovement),
          improvementValue: comparison.comparison.durationImprovement
        },
        {
          metric: '最小响应时间',
          v1: `${comparison.v1.minDuration.toFixed(2)}ms`,
          v1_1: `${comparison.v1_1.minDuration.toFixed(2)}ms`,
          improvement: '-',
          improvementValue: 0
        },
        {
          metric: '最大响应时间',
          v1: `${comparison.v1.maxDuration.toFixed(2)}ms`,
          v1_1: `${comparison.v1_1.maxDuration.toFixed(2)}ms`,
          improvement: '-',
          improvementValue: 0
        },
        {
          metric: 'P95响应时间',
          v1: `${comparison.v1.p95Duration.toFixed(2)}ms`,
          v1_1: `${comparison.v1_1.p95Duration.toFixed(2)}ms`,
          improvement: '-',
          improvementValue: 0
        },
        {
          metric: '成功率',
          v1: `${comparison.v1.successRate.toFixed(1)}%`,
          v1_1: `${comparison.v1_1.successRate.toFixed(1)}%`,
          improvement: this.formatImprovement(comparison.comparison.successRateImprovement),
          improvementValue: comparison.comparison.successRateImprovement
        }
      ]
    }
  }
}
</script>

<style scoped>
.performance-dashboard {
  padding: 20px;
}

.dashboard-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.metrics-overview,
.comparison-details {
  margin-bottom: 30px;
}

.metric-card {
  margin-bottom: 16px;
}

.metric-info {
  text-align: center;
}

.metric-name {
  font-weight: bold;
  margin-bottom: 8px;
  color: #303133;
}

.metric-value {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.duration {
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
}

.success-rate {
  font-size: 14px;
  font-weight: bold;
}

.success-rate.success { color: #67c23a; }
.success-rate.warning { color: #e6a23c; }
.success-rate.danger { color: #f56c6c; }

.metric-details {
  font-size: 12px;
  color: #909399;
}

.comparison-summary {
  margin-bottom: 24px;
}

.version-card {
  height: 100%;
}

.v1-card .el-card__header {
  background-color: #f0f9ff;
  color: #1e40af;
}

.v1-1-card .el-card__header {
  background-color: #f0fdf4;
  color: #166534;
}

.version-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
}

.stat-item .label {
  color: #606266;
}

.stat-item .value {
  font-weight: bold;
  color: #303133;
}

.improvement-metrics {
  margin-bottom: 24px;
}

.improvement-item {
  text-align: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.improvement-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.improvement-value {
  font-size: 18px;
  font-weight: bold;
}

.improvement-positive { color: #67c23a; }
.improvement-negative { color: #f56c6c; }
.improvement-neutral { color: #909399; }

.recommendation {
  font-size: 14px;
  font-weight: bold;
}

.recommend-v1-1 { color: #67c23a; }
.recommend-v1 { color: #e6a23c; }
.recommend-neutral { color: #909399; }

.detailed-stats {
  margin-top: 24px;
}

.no-data {
  text-align: center;
  padding: 40px;
}

h4, h5 {
  margin-bottom: 16px;
  color: #303133;
  border-bottom: 2px solid #e4e7ed;
  padding-bottom: 8px;
}
</style>
