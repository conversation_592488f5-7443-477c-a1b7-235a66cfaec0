using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Core.Interfaces.Services;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Services;
using ItAssetsSystem.Models.Entities;
using ItAssetsSystem.Core.Monitoring;

namespace ItAssetsSystem.Application.Services.Adapters
{
    /// <summary>
    /// 用户服务适配器 - 包装现有业务逻辑，实现新接口
    /// 确保向后兼容，不影响现有功能
    /// </summary>
    public class UserServiceAdapter : IUserService
    {
        private readonly AppDbContext _context;
        private readonly ITokenService _tokenService;
        private readonly ILogger<UserServiceAdapter> _logger;
        private readonly IPerformanceMonitor _performanceMonitor;

        public UserServiceAdapter(
            AppDbContext context,
            ITokenService tokenService,
            ILogger<UserServiceAdapter> logger,
            IPerformanceMonitor performanceMonitor = null)
        {
            _context = context;
            _tokenService = tokenService;
            _logger = logger;
            _performanceMonitor = performanceMonitor;
        }

        /// <summary>
        /// 用户认证 - 复用现有的登录逻辑
        /// </summary>
        public async Task<AuthResultDto> AuthenticateAsync(LoginModel model)
        {
            // 使用性能监控包装操作
            if (_performanceMonitor != null)
            {
                return await _performanceMonitor.MonitorAndCompareAsync("UserAuthenticate",
                    () => AuthenticateInternalAsync(model), "v1.1");
            }

            return await AuthenticateInternalAsync(model);
        }

        /// <summary>
        /// 内部认证实现
        /// </summary>
        private async Task<AuthResultDto> AuthenticateInternalAsync(LoginModel model)
        {
            try
            {
                _logger.LogInformation("用户认证开始: {Username}", model.Username);

                // 复用现有的用户查询逻辑
                var user = await _context.Users
                    .Include(u => u.UserRoles)
                        .ThenInclude(ur => ur.Role)
                    .Include(u => u.Department)
                    .FirstOrDefaultAsync(u => u.Username == model.Username);

                if (user == null)
                {
                    _logger.LogWarning("用户不存在: {Username}", model.Username);
                    return new AuthResultDto 
                    { 
                        Success = false, 
                        Message = "用户名不存在",
                        ErrorCode = "USER_NOT_FOUND"
                    };
                }

                if (!user.IsActive)
                {
                    _logger.LogWarning("用户已禁用: {Username}", model.Username);
                    return new AuthResultDto 
                    { 
                        Success = false, 
                        Message = "用户已被禁用",
                        ErrorCode = "USER_DISABLED"
                    };
                }

                // 保持现有的简化密码验证逻辑
                if (model.Password != "123456")
                {
                    _logger.LogWarning("密码错误: {Username}", model.Username);
                    return new AuthResultDto 
                    { 
                        Success = false, 
                        Message = "密码错误",
                        ErrorCode = "INVALID_PASSWORD"
                    };
                }

                // 生成Token（复用现有逻辑）
                var token = _tokenService.GenerateToken(user);

                // 更新最后登录时间
                user.LastLoginAt = DateTime.Now;
                await _context.SaveChangesAsync();

                _logger.LogInformation("用户认证成功: {Username}", model.Username);

                return new AuthResultDto
                {
                    Success = true,
                    Token = token,
                    User = MapToUserDto(user),
                    Message = "登录成功"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "用户认证失败: {Username}", model.Username);
                return new AuthResultDto 
                { 
                    Success = false, 
                    Message = "登录出错: " + ex.Message,
                    ErrorCode = "AUTHENTICATION_ERROR"
                };
            }
        }

        /// <summary>
        /// 根据ID获取用户信息
        /// </summary>
        public async Task<UserDto> GetByIdAsync(int id)
        {
            // 使用性能监控包装操作
            if (_performanceMonitor != null)
            {
                return await _performanceMonitor.MonitorAndCompareAsync("UserGetById",
                    () => GetByIdInternalAsync(id), "v1.1");
            }

            return await GetByIdInternalAsync(id);
        }

        /// <summary>
        /// 内部获取用户信息实现
        /// </summary>
        private async Task<UserDto> GetByIdInternalAsync(int id)
        {
            try
            {
                var user = await _context.Users
                    .Include(u => u.UserRoles)
                        .ThenInclude(ur => ur.Role)
                    .Include(u => u.Department)
                    .FirstOrDefaultAsync(u => u.Id == id);

                return user != null ? MapToUserDto(user) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户信息失败: {UserId}", id);
                return null;
            }
        }

        /// <summary>
        /// 根据用户名获取用户信息
        /// </summary>
        public async Task<UserDto> GetByUsernameAsync(string username)
        {
            try
            {
                var user = await _context.Users
                    .Include(u => u.UserRoles)
                        .ThenInclude(ur => ur.Role)
                    .Include(u => u.Department)
                    .FirstOrDefaultAsync(u => u.Username == username);

                return user != null ? MapToUserDto(user) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据用户名获取用户信息失败: {Username}", username);
                return null;
            }
        }

        /// <summary>
        /// 验证Token有效性
        /// </summary>
        public async Task<bool> ValidateTokenAsync(string token)
        {
            try
            {
                if (string.IsNullOrEmpty(token))
                    return false;

                // 这里可以添加更复杂的Token验证逻辑
                // 目前简化处理，后续可以扩展
                return !string.IsNullOrEmpty(token) && token.Length > 10;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Token验证失败");
                return false;
            }
        }

        /// <summary>
        /// 获取用户权限列表
        /// </summary>
        public async Task<List<string>> GetUserPermissionsAsync(int userId)
        {
            try
            {
                var user = await _context.Users
                    .Include(u => u.UserRoles)
                        .ThenInclude(ur => ur.Role)
                    .FirstOrDefaultAsync(u => u.Id == userId);

                if (user == null)
                    return new List<string>();

                // 返回用户的角色列表作为权限
                return user.UserRoles.Select(ur => ur.Role.Name).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户权限失败: {UserId}", userId);
                return new List<string>();
            }
        }

        /// <summary>
        /// 将User实体映射为UserDto
        /// </summary>
        private UserDto MapToUserDto(User user)
        {
            return new UserDto
            {
                Id = user.Id,
                Username = user.Username,
                Name = user.Name,
                Email = user.Email,
                Mobile = user.Mobile,
                Position = user.Position,
                DepartmentId = user.DepartmentId,
                DepartmentName = user.Department?.Name,
                Roles = user.UserRoles?.Select(ur => ur.Role.Name).ToList() ?? new List<string>(),
                IsActive = user.IsActive,
                LastLoginAt = user.LastLoginAt
            };
        }
    }
}
