using ItAssetsSystem.Models.Entities.Gamification;
using ItAssetsSystem.Models.DTOs.Gamification;
using ItAssetsSystem.Services.Interfaces;

namespace ItAssetsSystem.Services.Interfaces
{
    /// <summary>
    /// 游戏化服务接口
    /// </summary>
    public interface IGamificationService
    {
        /// <summary>
        /// 获取用户游戏化统计信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户游戏化统计信息</returns>
        Task<GamificationUserStatsDto?> GetUserStatsAsync(int userId);

        /// <summary>
        /// 初始化用户游戏化数据
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>初始化的用户统计信息</returns>
        Task<GamificationUserStatsDto> InitializeUserStatsAsync(int userId);

        /// <summary>
        /// 任务领取奖励
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="taskId">任务ID</param>
        /// <returns>奖励信息</returns>
        Task<GamificationRewardDto> ClaimTaskRewardAsync(int userId, long taskId);

        /// <summary>
        /// 任务完成奖励
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="taskId">任务ID</param>
        /// <param name="isOnTime">是否按时完成</param>
        /// <returns>奖励信息</returns>
        Task<GamificationRewardDto> CompleteTaskRewardAsync(int userId, long taskId, bool isOnTime = false);

        /// <summary>
        /// 任务创建奖励
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="taskId">任务ID</param>
        /// <returns>奖励信息</returns>
        Task<GamificationRewardDto> CreateTaskRewardAsync(int userId, long taskId);

        /// <summary>
        /// 任务更新奖励
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="taskId">任务ID</param>
        /// <returns>奖励信息</returns>
        Task<GamificationRewardDto> UpdateTaskRewardAsync(int userId, long taskId);

        /// <summary>
        /// 获取用户每日任务统计
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="date">日期</param>
        /// <returns>每日任务统计</returns>
        Task<DailyTaskStatsDto> GetDailyTaskStatsAsync(int userId, DateTime date);

        /// <summary>
        /// 获取用户排行榜信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="leaderboardType">排行榜类型</param>
        /// <returns>排行榜信息</returns>
        Task<UserLeaderboardDto?> GetUserLeaderboardAsync(int userId, LeaderboardType leaderboardType);

        /// <summary>
        /// 获取排行榜前N名
        /// </summary>
        /// <param name="leaderboardType">排行榜类型</param>
        /// <param name="topN">前N名</param>
        /// <returns>排行榜列表</returns>
        Task<List<UserLeaderboardDto>> GetTopLeaderboardAsync(LeaderboardType leaderboardType, int topN = 10);

        /// <summary>
        /// 记录游戏化事件
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="eventType">事件类型</param>
        /// <param name="xpChange">经验值变动</param>
        /// <param name="pointsChange">积分变动</param>
        /// <param name="reason">原因</param>
        /// <param name="relatedTaskId">关联任务ID</param>
        /// <param name="metadata">元数据</param>
        /// <returns>记录的日志</returns>
        Task<GamificationLog> LogEventAsync(int userId, string eventType, int xpChange = 0, int pointsChange = 0, 
            string? reason = null, long? relatedTaskId = null, string? metadata = null);

        /// <summary>
        /// 计算等级所需经验值
        /// </summary>
        /// <param name="level">等级</param>
        /// <returns>所需经验值</returns>
        int CalculateXPForLevel(int level);

        /// <summary>
        /// 根据经验值计算等级
        /// </summary>
        /// <param name="xp">经验值</param>
        /// <returns>等级</returns>
        int CalculateLevelFromXP(int xp);

        /// <summary>
        /// 获取周统计汇总 - 所有人按周的任务创建、领取、完成数量
        /// </summary>
        /// <param name="weekOffset">周偏移量 (0=本周, -1=上周, 1=下周)</param>
        /// <returns>周统计汇总</returns>
        Task<List<WeeklyTaskStatsDto>> GetWeeklyStatsAsync(int weekOffset = 0);

        /// <summary>
        /// 获取用户周统计详情
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="weekOffset">周偏移量 (0=本周, -1=上周, 1=下周)</param>
        /// <returns>用户周统计详情</returns>
        Task<WeeklyTaskStatsDto?> GetUserWeeklyStatsAsync(int userId, int weekOffset = 0);
    }

    /// <summary>
    /// 排行榜类型
    /// </summary>
    public enum LeaderboardType
    {
        /// <summary>
        /// 周排行榜
        /// </summary>
        Weekly = 1,

        /// <summary>
        /// 月排行榜
        /// </summary>
        Monthly = 2,

        /// <summary>
        /// 总排行榜
        /// </summary>
        AllTime = 3
    }
}
