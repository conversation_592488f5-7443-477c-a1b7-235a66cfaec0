import{aG as e,j as a,r as s,c as l,m as t,af as i,ag as n,b as r,d as c,Y as d,a9 as o,f as u,w as v,a8 as m,a as p,u as f,o as h,e as g,b5 as y,aC as _,t as b,bi as w,A as k,bj as I,bk as D,F as z,h as P,bl as L,bm as T,U as j,aP as C,_ as S}from"./index-C7OOw0MO.js";import{u as U}from"./gamification-CzHSNMa2.js";import{t as A}from"./task-BTGSy_AK.js";const x={class:"profile-view"},G={class:"user-info"},q={class:"avatar-wrapper"},F={class:"avatar-uploader-icon"},B={class:"user-name"},M={class:"user-title"},N={class:"user-level"},R={class:"level-label"},J={class:"user-meta"},O={class:"meta-item"},V={class:"meta-value"},W={class:"meta-item"},Y={class:"meta-value"},$={class:"meta-item"},E={class:"meta-value"},H={class:"meta-item"},K={class:"meta-value"},Q={class:"meta-item"},X={class:"meta-value"},Z={class:"contact-info"},ee={class:"contact-item"},ae={class:"contact-item"},se={class:"card-header"},le={class:"tags-content"},te={key:0,class:"inventory-grid"},ie=["title"],ne={class:"stat-item"},re={class:"stat-value"},ce={class:"stat-item"},de={class:"stat-value"},oe={class:"stat-item"},ue={class:"stat-value"},ve={class:"stat-item"},me={class:"stat-value"},pe={class:"card-header"},fe={key:0,class:"achievements-grid"},he={class:"achievement-info"},ge={class:"achievement-name"},ye={class:"achievement-date"},_e={class:"card-header"},be=S(e({__name:"ProfileView",setup(e){const S=f(),be=a(),we=U(),ke=s(!1),Ie=s(!1),De=s(!1),ze=s(!1),Pe=s(!1),Le=s(null),Te=s([]),je=s(!1),Ce=s({gold:0,diamonds:0}),Se=s("-"),Ue=l((()=>je.value?we.achievements:we.achievements.slice(0,6)));async function Ae(){Ie.value=!0;try{await new Promise((e=>setTimeout(e,400))),Le.value={total:75,completed:58,pending:5,onTimeRate:95}}catch(e){m.error("加载任务统计失败"),Le.value=null}finally{Ie.value=!1}}async function xe(){ze.value=!0;try{const e={pageSize:5,assignee:be.userInfo.id,sortBy:"createDate",sortOrder:"desc"},a=await A.getTaskList(e);Te.value=(null==a?void 0:a.list)||[]}catch(e){m.error("加载近期任务失败"),Te.value=[]}finally{ze.value=!1}}async function Ge(){try{await new Promise((e=>setTimeout(e,200))),Ce.value={gold:1250,diamonds:55}}catch(e){}}async function qe(){try{await new Promise((e=>setTimeout(e,250))),Se.value="#3"}catch(e){}}const Fe=e=>{if(!e)return"-";try{return new Date(e).toLocaleDateString()}catch{return"-"}},Be=e=>{if(!e)return"-";try{return new Date(e).toLocaleString()}catch{return"-"}},Me=e=>{if(!e)return"-";try{return new Date(e).toLocaleDateString("zh-CN",{month:"2-digit",day:"2-digit"})}catch{return"-"}},Ne={unstarted:{label:"未开始",type:"info"},"in-progress":{label:"进行中",type:"primary"},completed:{label:"已完成",type:"success"},overdue:{label:"已逾期",type:"danger"},todo:{label:"待办",type:"info"}},Re=e=>{var a;return(null==(a=Ne[e])?void 0:a.label)||e},Je=async e=>{var a,s;Pe.value=!0;try{const a=await C.uploadAvatar(e.file);if(a&&a.success&&a.data){const{avatarUrl:e,accessUrl:s}=a.data;s?be.setAvatarWithFullUrl(s):e&&be.setAvatar(e),m.success(a.message||"头像更新成功！")}else{const e=(null==a?void 0:a.message)||"头像上传失败，响应数据格式不正确";m.error(e)}}catch(l){let e="头像上传失败，请稍后再试";(null==(s=null==(a=null==l?void 0:l.response)?void 0:a.data)?void 0:s.message)?e=l.response.data.message:(null==l?void 0:l.message)&&(e=l.message),m.error(e)}finally{Pe.value=!1}},Oe=e=>["image/jpeg","image/png","image/gif"].includes(e.type)?!(e.size/1024/1024>5)||(m.error("头像图片大小不能超过 5MB!"),!1):(m.error("头像图片必须是 JPG, PNG, 或 GIF 格式!"),!1);return t((()=>{ke.value=!0,0!==we.score||we.isLoading||we.initializeStore(),Promise.allSettled([Ae(),xe(),Ge(),qe()]).finally((()=>{ke.value=!1}))})),(e,a)=>{const s=p("el-avatar"),l=p("el-icon"),t=p("el-divider"),m=p("el-card"),f=p("el-button"),C=p("el-empty"),U=p("el-col"),A=p("el-row"),Pe=p("el-tooltip"),Ae=p("el-table-column"),xe=p("el-table"),Ge=n("loading");return i((h(),r("div",x,[a[24]||(a[24]=c("div",{class:"page-header"},"个人中心",-1)),u(be).userInfo&&!ke.value?(h(),d(A,{key:0,gutter:20},{default:v((()=>[g(U,{span:8},{default:v((()=>[g(m,{shadow:"hover",class:"user-card"},{default:v((()=>[c("div",G,[c("div",q,[g(u(y),{class:"avatar-uploader",action:"#","show-file-list":!1,"http-request":Je,"before-upload":Oe},{default:v((()=>[g(s,{size:100,src:u(be).computedAvatarUrl||"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",class:"profile-avatar"},null,8,["src"]),c("div",F,[g(l,null,{default:v((()=>[g(u(_))])),_:1}),a[2]||(a[2]=c("span",null,"点击更换",-1))])])),_:1})]),c("h2",B,b(u(be).userInfo.name||"用户名"),1),c("div",M,b(u(be).userInfo.department||"部门")+" - "+b(u(be).userInfo.position||"职位"),1),c("div",N,[c("span",R,"Level "+b(u(we).level||1)+" - "+b(u(we).levelTitle),1),g(u(w),{percentage:u(we).currentLevelProgress,"stroke-width":10,status:"success"},{default:v((()=>{var e;return[c("span",null,b(u(we).score)+" / "+b((null==(e=u(we).nextLevelInfo)?void 0:e.points)||"Max"),1)]})),_:1},8,["percentage"])]),c("div",J,[c("div",O,[a[3]||(a[3]=c("div",{class:"meta-label"},"加入时间",-1)),c("div",V,b(Fe(u(be).userInfo.joinDate)||"-"),1)]),c("div",W,[a[4]||(a[4]=c("div",{class:"meta-label"},"总经验",-1)),c("div",Y,b(u(we).score),1)]),c("div",$,[a[6]||(a[6]=c("div",{class:"meta-label"},"金币",-1)),c("div",E,[a[5]||(a[5]=c("img",{src:"https://cdn-icons-png.flaticon.com/512/2933/2933116.png",class:"coin-icon-sm"},null,-1)),k(" "+b(Ce.value.gold||0),1)])]),c("div",H,[a[8]||(a[8]=c("div",{class:"meta-label"},"钻石",-1)),c("div",K,[a[7]||(a[7]=c("img",{src:"https://cdn-icons-png.flaticon.com/512/2933/2933151.png",class:"diamond-icon-sm"},null,-1)),k(" "+b(Ce.value.diamonds||0),1)])]),c("div",Q,[a[9]||(a[9]=c("div",{class:"meta-label"},"排名",-1)),c("div",X,b(Se.value||"-"),1)])])]),g(t),c("div",Z,[a[10]||(a[10]=c("h3",null,"联系方式",-1)),c("div",ee,[g(l,null,{default:v((()=>[g(u(I))])),_:1}),c("span",null,b(u(be).userInfo.email||"未设置"),1)]),c("div",ae,[g(l,null,{default:v((()=>[g(u(D))])),_:1}),c("span",null,b(u(be).userInfo.phone||"未设置"),1)])])])),_:1}),g(m,{shadow:"hover",class:"tags-card"},{header:v((()=>[c("div",se,[a[12]||(a[12]=c("span",null,"我的标签",-1)),g(f,{link:"",type:"primary"},{default:v((()=>a[11]||(a[11]=[k("编辑")]))),_:1})])])),default:v((()=>[c("div",le,[u(be).userInfo.tags&&u(be).userInfo.tags.length>0?(h(!0),r(z,{key:0},P(u(be).userInfo.tags,(e=>(h(),d(u(L),{key:e,class:"user-tag",type:"info"},{default:v((()=>[k(b(e),1)])),_:2},1024)))),128)):(h(),d(C,{key:1,description:"暂无标签","image-size":50}))])])),_:1}),g(m,{shadow:"hover",class:"inventory-card"},{header:v((()=>a[13]||(a[13]=[c("div",{class:"card-header"},[c("span",null,"我的背包")],-1)]))),default:v((()=>[u(we).inventory.length>0?(h(),r("div",te,[(h(!0),r(z,null,P(u(we).inventory,(e=>(h(),r("div",{key:e.id,class:"inventory-item"},[g(u(T),{value:e.quantity>1?e.quantity:null,type:"primary"},{default:v((()=>[g(s,{size:50,src:e.icon||"path/to/default/item/icon.png",class:"item-icon"},null,8,["src"])])),_:2},1032,["value"]),c("div",{class:"item-name",title:e.description},b(e.name),9,ie)])))),128))])):(h(),d(C,{key:1,description:"背包空空如也","image-size":50}))])),_:1})])),_:1}),g(U,{span:16},{default:v((()=>[i((h(),d(m,{shadow:"hover",class:"stats-card"},{header:v((()=>a[14]||(a[14]=[c("div",{class:"card-header"},[c("span",null,"任务统计")],-1)]))),default:v((()=>[Le.value?(h(),d(A,{key:0,gutter:20,class:"stat-row"},{default:v((()=>[g(U,{span:6},{default:v((()=>[c("div",ne,[c("div",re,b(Le.value.total||0),1),a[15]||(a[15]=c("div",{class:"stat-label"},"总任务数",-1))])])),_:1}),g(U,{span:6},{default:v((()=>[c("div",ce,[c("div",de,b(Le.value.completed||0),1),a[16]||(a[16]=c("div",{class:"stat-label"},"已完成",-1))])])),_:1}),g(U,{span:6},{default:v((()=>[c("div",oe,[c("div",ue,b(Le.value.onTimeRate||0)+"%",1),a[17]||(a[17]=c("div",{class:"stat-label"},"按时完成率",-1))])])),_:1}),g(U,{span:6},{default:v((()=>[c("div",ve,[c("div",me,b(Le.value.pending||0),1),a[18]||(a[18]=c("div",{class:"stat-label"},"待处理",-1))])])),_:1})])),_:1})):o("",!0),a[19]||(a[19]=c("div",{class:"chart-area"},[c("div",{class:"chart-placeholder"},"任务完成趋势图表 (待实现)")],-1))])),_:1})),[[Ge,Ie.value]]),i((h(),d(m,{shadow:"hover",class:"achievements-card"},{header:v((()=>[c("div",pe,[a[20]||(a[20]=c("span",null,"我的成就",-1)),g(f,{link:"",type:"primary",onClick:a[0]||(a[0]=e=>je.value=!je.value)},{default:v((()=>[k(b(je.value?"收起":`查看全部 (${u(we).achievements.length})`),1)])),_:1})])])),default:v((()=>[u(we).achievements.length>0?(h(),r("div",fe,[(h(!0),r(z,null,P(Ue.value,(e=>(h(),r("div",{key:e.id,class:"achievement-item"},[g(Pe,{content:e.description,placement:"top"},{default:v((()=>[g(s,{size:40,icon:e.icon||u(j),class:"achievement-icon"},null,8,["icon"])])),_:2},1032,["content"]),c("div",he,[c("div",ge,b(e.name),1),c("div",ye,b(Be(e.achievedDate)),1)])])))),128))])):o("",!0)])),_:1})),[[Ge,De.value]]),i((h(),d(m,{shadow:"hover",class:"recent-tasks-card"},{header:v((()=>[c("div",_e,[a[22]||(a[22]=c("span",null,"近期任务",-1)),g(f,{link:"",type:"primary",onClick:a[1]||(a[1]=e=>u(S).push("/main/tasks/list"))},{default:v((()=>a[21]||(a[21]=[k("查看全部")]))),_:1})])])),default:v((()=>[Te.value.length>0?(h(),d(xe,{key:0,data:Te.value,style:{width:"100%"}},{default:v((()=>[g(Ae,{prop:"title",label:"任务名称","min-width":"180","show-overflow-tooltip":""}),g(Ae,{prop:"endDate",label:"截止时间",width:"120"},{default:v((({row:e})=>[k(b(Me(e.endDate)),1)])),_:1}),g(Ae,{label:"状态",width:"100"},{default:v((({row:e})=>{return[g(u(L),{type:(a=e.status,(null==(s=Ne[a])?void 0:s.type)||"info")},{default:v((()=>[k(b(Re(e.status)),1)])),_:2},1032,["type"])];var a,s})),_:1}),g(Ae,{label:"操作",width:"100",align:"center"},{default:v((({row:e})=>[g(f,{type:"primary",link:"",onClick:a=>{return s=e.id,void S.push({name:"TaskDetail",params:{id:s}});var s}},{default:v((()=>a[23]||(a[23]=[k("查看")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])):o("",!0)])),_:1})),[[Ge,ze.value]])])),_:1})])),_:1})):ke.value?o("",!0):(h(),d(C,{key:1,description:"无法加载用户信息"}))])),[[Ge,ke.value]])}}}),[["__scopeId","data-v-f9bf8121"]]);export{be as default};
