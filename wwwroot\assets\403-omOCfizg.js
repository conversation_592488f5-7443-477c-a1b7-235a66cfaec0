import{_ as s,b as a,d as e,e as r,w as o,a as t,u as c,o as l,f as i,ah as n,A as d}from"./index-CkwLz8y6.js";const u={class:"error-page"},p={class:"error-container"},_={class:"error-image"},f={class:"error-actions"},m=s({__name:"403",setup(s){const m=c(),v=()=>{m.go(-1)},g=()=>{m.push("/")};return(s,c)=>{const m=t("el-icon"),C=t("el-button");return l(),a("div",u,[e("div",p,[e("div",_,[r(m,{size:"160",color:"#F56C6C"},{default:o((()=>[r(i(n))])),_:1})]),c[2]||(c[2]=e("h1",{class:"error-title"},"403",-1)),c[3]||(c[3]=e("p",{class:"error-message"},"抱歉，您没有权限访问此页面",-1)),e("div",f,[r(C,{type:"primary",onClick:v},{default:o((()=>c[0]||(c[0]=[d("返回上一页")]))),_:1}),r(C,{onClick:g},{default:o((()=>c[1]||(c[1]=[d("返回首页")]))),_:1})])])])}}},[["__scopeId","data-v-5e43eabc"]]);export{m as default};
