<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IT资产管理系统 - API性能对比演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .controls {
            padding: 30px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }

        .control-group {
            display: flex;
            gap: 20px;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            padding: 20px 30px;
            background: #f1f5f9;
            border-left: 4px solid #3b82f6;
            margin: 20px 30px;
            border-radius: 0 8px 8px 0;
        }

        .dashboard {
            padding: 30px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .version-panel {
            background: #f8fafc;
            border-radius: 12px;
            padding: 25px;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .version-panel:hover {
            border-color: #3b82f6;
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.1);
        }

        .version-panel h3 {
            color: #1e293b;
            margin-bottom: 20px;
            font-size: 1.5rem;
            text-align: center;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .metric:last-child {
            border-bottom: none;
        }

        .metric-label {
            font-weight: 600;
            color: #475569;
        }

        .metric-value {
            font-weight: 700;
            font-size: 1.1rem;
        }

        .metric-value.good {
            color: #059669;
        }

        .metric-value.warning {
            color: #d97706;
        }

        .metric-value.danger {
            color: #dc2626;
        }

        .comparison {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 12px;
            padding: 25px;
            margin-top: 20px;
        }

        .comparison h3 {
            text-align: center;
            color: #0c4a6e;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .comparison-item {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        }

        .comparison-item h4 {
            color: #374151;
            margin-bottom: 10px;
        }

        .improvement {
            font-size: 1.5rem;
            font-weight: 700;
        }

        .improvement.positive {
            color: #059669;
        }

        .improvement.negative {
            color: #dc2626;
        }

        .improvement.neutral {
            color: #6b7280;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .log {
            background: #1e293b;
            color: #e2e8f0;
            padding: 20px;
            margin: 20px 30px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 200px;
            overflow-y: auto;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-entry.success {
            color: #10b981;
        }

        .log-entry.error {
            color: #ef4444;
        }

        .log-entry.info {
            color: #3b82f6;
        }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .control-group {
                flex-direction: column;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 API性能对比演示</h1>
            <p>IT资产管理系统 V1 vs V1.1 版本性能对比</p>
        </div>

        <div class="controls">
            <div class="control-group">
                <button class="btn btn-primary" onclick="testV1Performance()">
                    <span id="v1-loading" style="display: none;" class="loading"></span>
                    测试 V1 版本性能
                </button>
                <button class="btn btn-success" onclick="testV1_1Performance()">
                    <span id="v1_1-loading" style="display: none;" class="loading"></span>
                    测试 V1.1 版本性能
                </button>
                <button class="btn btn-warning" onclick="runComparison()">
                    <span id="compare-loading" style="display: none;" class="loading"></span>
                    运行性能对比
                </button>
                <button class="btn btn-danger" onclick="clearResults()">清除结果</button>
            </div>
        </div>

        <div class="status" id="status">
            <strong>状态:</strong> 准备就绪，点击按钮开始测试
        </div>

        <div class="dashboard">
            <div class="version-panel">
                <h3>🔵 V1 版本</h3>
                <div class="metric">
                    <span class="metric-label">平均响应时间:</span>
                    <span class="metric-value" id="v1-response-time">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">成功率:</span>
                    <span class="metric-value" id="v1-success-rate">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">内存使用:</span>
                    <span class="metric-value" id="v1-memory">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">请求数量:</span>
                    <span class="metric-value" id="v1-requests">-</span>
                </div>
            </div>

            <div class="version-panel">
                <h3>🟢 V1.1 版本</h3>
                <div class="metric">
                    <span class="metric-label">平均响应时间:</span>
                    <span class="metric-value" id="v1_1-response-time">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">成功率:</span>
                    <span class="metric-value" id="v1_1-success-rate">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">内存使用:</span>
                    <span class="metric-value" id="v1_1-memory">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">请求数量:</span>
                    <span class="metric-value" id="v1_1-requests">-</span>
                </div>
            </div>

            <div class="comparison" id="comparison" style="display: none;">
                <h3>📊 性能对比结果</h3>
                <div class="comparison-grid">
                    <div class="comparison-item">
                        <h4>响应时间改进</h4>
                        <div class="improvement" id="response-improvement">-</div>
                    </div>
                    <div class="comparison-item">
                        <h4>成功率改进</h4>
                        <div class="improvement" id="success-improvement">-</div>
                    </div>
                    <div class="comparison-item">
                        <h4>内存使用改进</h4>
                        <div class="improvement" id="memory-improvement">-</div>
                    </div>
                    <div class="comparison-item">
                        <h4>推荐建议</h4>
                        <div class="improvement" id="recommendation">-</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="log" id="log">
            <div class="log-entry info">[INFO] 性能测试系统已初始化</div>
            <div class="log-entry info">[INFO] 后端服务地址: http://localhost:5001</div>
            <div class="log-entry info">[INFO] 准备开始性能测试...</div>
        </div>
    </div>

    <script>
        // 性能测试数据存储
        let v1Results = null;
        let v1_1Results = null;

        // 日志函数
        function addLog(message, type = 'info') {
            const log = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        // 更新状态
        function updateStatus(message) {
            document.getElementById('status').innerHTML = `<strong>状态:</strong> ${message}`;
        }

        // 测试API性能
        async function testApiPerformance(version, endpoints) {
            const results = {
                version: version,
                responseTimes: [],
                successCount: 0,
                totalRequests: 0,
                memoryUsage: performance.memory ? performance.memory.usedJSHeapSize : 0,
                startTime: performance.now()
            };

            addLog(`开始测试 ${version} 版本性能`, 'info');

            for (const endpoint of endpoints) {
                for (let i = 0; i < 5; i++) { // 每个端点测试5次
                    const startTime = performance.now();
                    try {
                        const response = await fetch(`http://localhost:5001${endpoint}`);
                        const endTime = performance.now();
                        
                        results.responseTimes.push(endTime - startTime);
                        results.totalRequests++;
                        
                        if (response.ok) {
                            results.successCount++;
                        }
                        
                        addLog(`${endpoint} - ${Math.round(endTime - startTime)}ms`, 'success');
                    } catch (error) {
                        const endTime = performance.now();
                        results.responseTimes.push(endTime - startTime);
                        results.totalRequests++;
                        addLog(`${endpoint} - 错误: ${error.message}`, 'error');
                    }
                    
                    // 短暂延迟避免过于频繁的请求
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }

            results.endTime = performance.now();
            results.avgResponseTime = results.responseTimes.reduce((a, b) => a + b, 0) / results.responseTimes.length;
            results.successRate = (results.successCount / results.totalRequests) * 100;
            
            if (performance.memory) {
                results.memoryUsage = performance.memory.usedJSHeapSize - results.memoryUsage;
            }

            addLog(`${version} 版本测试完成 - 平均响应时间: ${Math.round(results.avgResponseTime)}ms`, 'success');
            return results;
        }

        // 测试V1版本性能
        async function testV1Performance() {
            const button = document.querySelector('.btn-primary');
            const loading = document.getElementById('v1-loading');
            
            button.disabled = true;
            loading.style.display = 'inline-block';
            updateStatus('正在测试 V1 版本性能...');

            try {
                const endpoints = [
                    '/api/User/health',
                    '/api/Asset/health',
                    '/api/Task/health'
                ];

                v1Results = await testApiPerformance('V1', endpoints);
                
                // 更新UI
                document.getElementById('v1-response-time').textContent = `${Math.round(v1Results.avgResponseTime)}ms`;
                document.getElementById('v1-response-time').className = 'metric-value ' + getResponseTimeClass(v1Results.avgResponseTime);
                
                document.getElementById('v1-success-rate').textContent = `${v1Results.successRate.toFixed(1)}%`;
                document.getElementById('v1-success-rate').className = 'metric-value ' + getSuccessRateClass(v1Results.successRate);
                
                document.getElementById('v1-memory').textContent = formatBytes(v1Results.memoryUsage);
                document.getElementById('v1-requests').textContent = v1Results.totalRequests;

                updateStatus('V1 版本性能测试完成');
            } catch (error) {
                addLog(`V1 版本测试失败: ${error.message}`, 'error');
                updateStatus('V1 版本性能测试失败');
            } finally {
                button.disabled = false;
                loading.style.display = 'none';
            }
        }

        // 测试V1.1版本性能
        async function testV1_1Performance() {
            const button = document.querySelector('.btn-success');
            const loading = document.getElementById('v1_1-loading');
            
            button.disabled = true;
            loading.style.display = 'inline-block';
            updateStatus('正在测试 V1.1 版本性能...');

            try {
                const endpoints = [
                    '/api/v1.1/user/health',
                    '/api/v1.1/asset/health',
                    '/api/v1.1/task/health'
                ];

                v1_1Results = await testApiPerformance('V1.1', endpoints);
                
                // 更新UI
                document.getElementById('v1_1-response-time').textContent = `${Math.round(v1_1Results.avgResponseTime)}ms`;
                document.getElementById('v1_1-response-time').className = 'metric-value ' + getResponseTimeClass(v1_1Results.avgResponseTime);
                
                document.getElementById('v1_1-success-rate').textContent = `${v1_1Results.successRate.toFixed(1)}%`;
                document.getElementById('v1_1-success-rate').className = 'metric-value ' + getSuccessRateClass(v1_1Results.successRate);
                
                document.getElementById('v1_1-memory').textContent = formatBytes(v1_1Results.memoryUsage);
                document.getElementById('v1_1-requests').textContent = v1_1Results.totalRequests;

                updateStatus('V1.1 版本性能测试完成');
            } catch (error) {
                addLog(`V1.1 版本测试失败: ${error.message}`, 'error');
                updateStatus('V1.1 版本性能测试失败');
            } finally {
                button.disabled = false;
                loading.style.display = 'none';
            }
        }

        // 运行性能对比
        async function runComparison() {
            if (!v1Results || !v1_1Results) {
                addLog('请先完成两个版本的性能测试', 'error');
                updateStatus('需要先完成两个版本的性能测试');
                return;
            }

            const button = document.querySelector('.btn-warning');
            const loading = document.getElementById('compare-loading');
            
            button.disabled = true;
            loading.style.display = 'inline-block';
            updateStatus('正在分析性能对比结果...');

            try {
                // 计算改进百分比
                const responseImprovement = calculateImprovement(v1Results.avgResponseTime, v1_1Results.avgResponseTime);
                const successImprovement = calculateImprovement(v1Results.successRate, v1_1Results.successRate, true);
                const memoryImprovement = calculateImprovement(v1Results.memoryUsage, v1_1Results.memoryUsage);

                // 更新对比结果
                document.getElementById('response-improvement').textContent = formatImprovement(responseImprovement);
                document.getElementById('response-improvement').className = 'improvement ' + getImprovementClass(responseImprovement);

                document.getElementById('success-improvement').textContent = formatImprovement(successImprovement);
                document.getElementById('success-improvement').className = 'improvement ' + getImprovementClass(successImprovement);

                document.getElementById('memory-improvement').textContent = formatImprovement(memoryImprovement);
                document.getElementById('memory-improvement').className = 'improvement ' + getImprovementClass(memoryImprovement);

                // 生成推荐建议
                const recommendation = generateRecommendation(responseImprovement, successImprovement, memoryImprovement);
                document.getElementById('recommendation').textContent = recommendation.text;
                document.getElementById('recommendation').className = 'improvement ' + recommendation.class;

                // 显示对比区域
                document.getElementById('comparison').style.display = 'block';

                addLog('性能对比分析完成', 'success');
                updateStatus('性能对比分析完成');
            } catch (error) {
                addLog(`性能对比分析失败: ${error.message}`, 'error');
                updateStatus('性能对比分析失败');
            } finally {
                button.disabled = false;
                loading.style.display = 'none';
            }
        }

        // 清除结果
        function clearResults() {
            v1Results = null;
            v1_1Results = null;
            
            // 清除V1结果
            document.getElementById('v1-response-time').textContent = '-';
            document.getElementById('v1-success-rate').textContent = '-';
            document.getElementById('v1-memory').textContent = '-';
            document.getElementById('v1-requests').textContent = '-';
            
            // 清除V1.1结果
            document.getElementById('v1_1-response-time').textContent = '-';
            document.getElementById('v1_1-success-rate').textContent = '-';
            document.getElementById('v1_1-memory').textContent = '-';
            document.getElementById('v1_1-requests').textContent = '-';
            
            // 隐藏对比区域
            document.getElementById('comparison').style.display = 'none';
            
            // 清除日志
            document.getElementById('log').innerHTML = `
                <div class="log-entry info">[INFO] 性能测试系统已重置</div>
                <div class="log-entry info">[INFO] 准备开始新的性能测试...</div>
            `;
            
            updateStatus('结果已清除，准备开始新的测试');
            addLog('测试结果已清除', 'info');
        }

        // 辅助函数
        function calculateImprovement(oldValue, newValue, higherIsBetter = false) {
            if (oldValue === 0) return 0;
            
            const improvement = higherIsBetter 
                ? ((newValue - oldValue) / oldValue) * 100
                : ((oldValue - newValue) / oldValue) * 100;
            
            return Math.round(improvement * 100) / 100;
        }

        function formatImprovement(value) {
            if (value > 0) {
                return `+${value.toFixed(1)}%`;
            } else if (value < 0) {
                return `${value.toFixed(1)}%`;
            }
            return '0%';
        }

        function getImprovementClass(value) {
            if (value > 5) return 'positive';
            if (value < -5) return 'negative';
            return 'neutral';
        }

        function getResponseTimeClass(time) {
            if (time < 100) return 'good';
            if (time < 300) return 'warning';
            return 'danger';
        }

        function getSuccessRateClass(rate) {
            if (rate >= 95) return 'good';
            if (rate >= 90) return 'warning';
            return 'danger';
        }

        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function generateRecommendation(responseImprovement, successImprovement, memoryImprovement) {
            if (responseImprovement > 10 && memoryImprovement > 5 && successImprovement >= 0) {
                return { text: '强烈推荐V1.1', class: 'positive' };
            } else if (responseImprovement > 5 && successImprovement >= 0) {
                return { text: '推荐V1.1', class: 'positive' };
            } else if (responseImprovement < -10 || successImprovement < -5) {
                return { text: '推荐V1', class: 'negative' };
            } else {
                return { text: '性能相当', class: 'neutral' };
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('性能对比演示页面已加载', 'success');
            updateStatus('准备就绪，点击按钮开始测试');
        });
    </script>
</body>
</html>
