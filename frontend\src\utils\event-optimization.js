/**
 * 事件监听器性能优化工具
 * 提供优化的事件监听器添加/移除方法，自动应用 passive 选项
 */

// 需要使用 passive 选项的事件类型
const PASSIVE_EVENTS = [
  'scroll',
  'wheel',
  'touchstart',
  'touchmove',
  'touchend',
  'touchcancel',
  'mousewheel',
  'resize',
  'orientationchange'
]

// 需要使用 passive: false 的事件类型（需要阻止默认行为）
const NON_PASSIVE_EVENTS = [
  'keydown',
  'keyup',
  'keypress'
]

/**
 * 优化的事件监听器添加方法
 * @param {Element|Window|Document} target - 目标元素
 * @param {string} type - 事件类型
 * @param {Function} listener - 事件处理函数
 * @param {Object|boolean} options - 选项对象或 useCapture 布尔值
 * @returns {Function} 移除事件监听器的函数
 */
export function addOptimizedEventListener(target, type, listener, options = {}) {
  // 如果 options 是布尔值，转换为对象格式
  if (typeof options === 'boolean') {
    options = { capture: options }
  }

  // 自动设置 passive 选项
  if (PASSIVE_EVENTS.includes(type) && options.passive === undefined) {
    options.passive = true
  } else if (NON_PASSIVE_EVENTS.includes(type) && options.passive === undefined) {
    options.passive = false
  }

  // 添加事件监听器
  target.addEventListener(type, listener, options)

  // 返回移除函数
  return () => {
    target.removeEventListener(type, listener, options)
  }
}

/**
 * 批量添加事件监听器
 * @param {Element|Window|Document} target - 目标元素
 * @param {Object} events - 事件映射对象 { eventType: handler }
 * @param {Object} options - 通用选项
 * @returns {Function} 移除所有事件监听器的函数
 */
export function addMultipleEventListeners(target, events, options = {}) {
  const removeFunctions = []

  Object.entries(events).forEach(([type, listener]) => {
    const removeFunction = addOptimizedEventListener(target, type, listener, options)
    removeFunctions.push(removeFunction)
  })

  // 返回移除所有事件监听器的函数
  return () => {
    removeFunctions.forEach(remove => remove())
  }
}

/**
 * 防抖事件监听器
 * @param {Element|Window|Document} target - 目标元素
 * @param {string} type - 事件类型
 * @param {Function} listener - 事件处理函数
 * @param {number} delay - 防抖延迟（毫秒）
 * @param {Object} options - 事件选项
 * @returns {Function} 移除事件监听器的函数
 */
export function addDebouncedEventListener(target, type, listener, delay = 100, options = {}) {
  let timeoutId = null

  const debouncedListener = (event) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => {
      listener(event)
    }, delay)
  }

  return addOptimizedEventListener(target, type, debouncedListener, options)
}

/**
 * 节流事件监听器
 * @param {Element|Window|Document} target - 目标元素
 * @param {string} type - 事件类型
 * @param {Function} listener - 事件处理函数
 * @param {number} delay - 节流延迟（毫秒）
 * @param {Object} options - 事件选项
 * @returns {Function} 移除事件监听器的函数
 */
export function addThrottledEventListener(target, type, listener, delay = 100, options = {}) {
  let lastTime = 0

  const throttledListener = (event) => {
    const now = Date.now()
    if (now - lastTime >= delay) {
      lastTime = now
      listener(event)
    }
  }

  return addOptimizedEventListener(target, type, throttledListener, options)
}

/**
 * 一次性事件监听器
 * @param {Element|Window|Document} target - 目标元素
 * @param {string} type - 事件类型
 * @param {Function} listener - 事件处理函数
 * @param {Object} options - 事件选项
 * @returns {Function} 移除事件监听器的函数
 */
export function addOnceEventListener(target, type, listener, options = {}) {
  options.once = true
  return addOptimizedEventListener(target, type, listener, options)
}

/**
 * Vue 3 组合式 API 的事件监听器 Hook
 * @param {Ref<Element>} targetRef - 目标元素的 ref
 * @param {string} type - 事件类型
 * @param {Function} listener - 事件处理函数
 * @param {Object} options - 事件选项
 */
export function useOptimizedEventListener(targetRef, type, listener, options = {}) {
  let removeFunction = null

  const cleanup = () => {
    if (removeFunction) {
      removeFunction()
      removeFunction = null
    }
  }

  const setup = () => {
    cleanup()
    if (targetRef.value) {
      removeFunction = addOptimizedEventListener(targetRef.value, type, listener, options)
    }
  }

  // 在 Vue 3 中使用
  if (typeof window !== 'undefined' && window.Vue) {
    const { watch, onUnmounted } = window.Vue
    
    watch(targetRef, setup, { immediate: true })
    onUnmounted(cleanup)
  }

  return { setup, cleanup }
}

/**
 * 检查浏览器是否支持 passive 事件监听器
 * @returns {boolean}
 */
export function supportsPassiveEvents() {
  let supportsPassive = false
  
  try {
    const opts = Object.defineProperty({}, 'passive', {
      get() {
        supportsPassive = true
        return false
      }
    })
    
    window.addEventListener('testPassive', null, opts)
    window.removeEventListener('testPassive', null, opts)
  } catch (e) {
    // 忽略错误
  }
  
  return supportsPassive
}

// 导出默认配置
export const DEFAULT_OPTIONS = {
  passive: true,
  capture: false
}

// 导出事件类型常量
export { PASSIVE_EVENTS, NON_PASSIVE_EVENTS }
