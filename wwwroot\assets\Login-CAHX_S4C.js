import{a2 as e,a3 as s,a4 as a,_ as l,c as r,j as o,r as n,a5 as t,b as u,d as i,e as d,w as c,t as m,f as p,a as g,u as f,s as v,o as w,a6 as y,a7 as b,A as _,a8 as h}from"./index-CkwLz8y6.js";import"./task-Uzj9rZkj.js";import{s as k}from"./system-9jEcQzSp.js";e().use((({store:e})=>{e.router=s(a)}));const V={class:"login-container"},x={class:"login-banner"},j={class:"banner-content"},U={class:"login-logo"},q={class:"system-name"},A={class:"system-desc"},C={class:"login-form-container"},F={class:"login-form-wrapper"},I={class:"login-footer"},K={class:"copyright"},L=l({__name:"Login",setup(e){const s=r((()=>(new Date).getFullYear())),a=f(),l=v(),L=o(),z=n(null),D=n(!1),M=t({username:"",password:"",remember:!1}),P={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度为3-20个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:30,message:"密码长度为6-30个字符",trigger:"blur"}]},R=async()=>{var e,s;if(z.value)try{await z.value.validate((e=>{if(!e)return h.warning("请正确填写用户名和密码"),!1})),D.value=!0;try{const e={username:M.username,password:M.password},s=await L.login(e);s&&s.success?(h.success("登录成功，欢迎回来！"),Y()):h.error((null==s?void 0:s.message)||"登录失败，请检查用户名和密码")}catch(a){if((null==(e=null==a?void 0:a.message)?void 0:e.includes("404"))||(null==(s=null==a?void 0:a.message)?void 0:s.includes("network error"))){h.warning("后端API不可用，尝试模拟登录..."),k.useMock=!0;try{const e=await L.login({username:M.username,password:M.password});if(e&&e.success)return h.success("模拟登录成功，欢迎回来！"),void Y();h.error((null==e?void 0:e.message)||"模拟登录失败")}catch(l){h.error("登录失败: "+((null==l?void 0:l.message)||"未知错误"))}}else h.error("登录失败: "+((null==a?void 0:a.message)||"未知错误"))}}catch(r){h.error("登录失败: "+((null==r?void 0:r.message)||"未知错误"))}finally{D.value=!1}},T=()=>{a.push("/test")},Y=()=>{const e=l.query.redirect||"/main/dashboard";setTimeout((()=>{L.token?a.push(e).catch((()=>{a.push("/")})):h.error("登录状态异常，请重新登录")}),100)};return(e,a)=>{const l=g("el-icon"),r=g("el-input"),o=g("el-form-item"),n=g("el-checkbox"),t=g("el-link"),f=g("el-button"),v=g("el-form");return w(),u("div",V,[i("div",x,[i("div",j,[i("div",U,[d(l,{size:80,color:"#ffffff"},{default:c((()=>[d(p(y))])),_:1})]),i("h1",q,m(p(k).name),1),i("p",A,m(p(k).description),1)])]),i("div",C,[i("div",F,[a[7]||(a[7]=i("h2",{class:"login-title"},"系统登录",-1)),d(v,{ref_key:"loginFormRef",ref:z,model:M,rules:P,class:"login-form"},{default:c((()=>[d(o,{prop:"username"},{default:c((()=>[d(r,{modelValue:M.username,"onUpdate:modelValue":a[0]||(a[0]=e=>M.username=e),placeholder:"请输入用户名","prefix-icon":"User",clearable:"",onKeyup:b(R,["enter"])},null,8,["modelValue"])])),_:1}),d(o,{prop:"password"},{default:c((()=>[d(r,{modelValue:M.password,"onUpdate:modelValue":a[1]||(a[1]=e=>M.password=e),type:"password",placeholder:"请输入密码","prefix-icon":"Lock","show-password":"",clearable:"",onKeyup:b(R,["enter"])},null,8,["modelValue"])])),_:1}),d(o,null,{default:c((()=>[d(n,{modelValue:M.remember,"onUpdate:modelValue":a[2]||(a[2]=e=>M.remember=e)},{default:c((()=>a[3]||(a[3]=[_("记住我")]))),_:1},8,["modelValue"]),d(t,{type:"primary",class:"forget-password",underline:!1},{default:c((()=>a[4]||(a[4]=[_("忘记密码？")]))),_:1})])),_:1}),d(o,null,{default:c((()=>[d(f,{type:"primary",class:"login-button",loading:D.value,onClick:R},{default:c((()=>a[5]||(a[5]=[_(" 登录 ")]))),_:1},8,["loading"])])),_:1}),d(o,null,{default:c((()=>[d(f,{type:"success",class:"test-button",onClick:T},{default:c((()=>a[6]||(a[6]=[_(" 测试页面跳转 ")]))),_:1})])),_:1})])),_:1},8,["model"]),i("div",I,[i("p",K,"© "+m(s.value)+" "+m(p(k).name),1)])])])])}}},[["__scopeId","data-v-4fd8c931"]]);export{L as default};
