using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using ItAssetsSystem.Core.Interfaces.Services;

namespace ItAssetsSystem.Controllers.V1_1
{
    /// <summary>
    /// 任务控制器 V1.1 - 使用新的服务接口
    /// 与现有V1控制器并行部署，确保向后兼容
    /// </summary>
    [ApiController]
    [Route("api/v1.1/[controller]")]
    [Authorize]
    public class TaskController : ControllerBase
    {
        private readonly ITaskService _taskService;
        private readonly ILogger<TaskController> _logger;

        public TaskController(ITaskService taskService, ILogger<TaskController> logger)
        {
            _taskService = taskService;
            _logger = logger;
        }

        /// <summary>
        /// 分页获取任务列表 - 使用新的服务接口
        /// </summary>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <param name="keyword">关键词</param>
        /// <param name="status">状态</param>
        /// <param name="priority">优先级</param>
        /// <param name="assigneeId">负责人ID</param>
        /// <param name="createdBy">创建人ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>分页结果</returns>
        [HttpGet]
        public async Task<IActionResult> GetTasks(
            [FromQuery] int pageIndex = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string keyword = null,
            [FromQuery] string status = null,
            [FromQuery] string priority = null,
            [FromQuery] int? assigneeId = null,
            [FromQuery] int? createdBy = null,
            [FromQuery] System.DateTime? startDate = null,
            [FromQuery] System.DateTime? endDate = null)
        {
            _logger.LogInformation("V1.1 获取任务列表: pageIndex={PageIndex}, pageSize={PageSize}", 
                pageIndex, pageSize);

            try
            {
                var query = new TaskQueryDto
                {
                    PageIndex = pageIndex,
                    PageSize = pageSize,
                    Keyword = keyword,
                    Status = status,
                    Priority = priority,
                    AssigneeId = assigneeId,
                    CreatedBy = createdBy,
                    StartDate = startDate,
                    EndDate = endDate
                };

                var result = await _taskService.GetPagedAsync(query);

                return Ok(new
                {
                    success = true,
                    data = new
                    {
                        items = result.Items,
                        totalCount = result.TotalCount,
                        pageIndex = result.PageIndex,
                        pageSize = result.PageSize,
                        totalPages = result.TotalPages
                    },
                    message = "获取任务列表成功",
                    version = "v1.1"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "V1.1 获取任务列表异常");
                return StatusCode(500, new
                {
                    success = false,
                    message = "获取任务列表失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 根据ID获取任务信息
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <returns>任务信息</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetTask(long id)
        {
            _logger.LogInformation("V1.1 获取任务信息: {TaskId}", id);

            try
            {
                var task = await _taskService.GetByIdAsync(id);
                if (task == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "任务不存在",
                        version = "v1.1"
                    });
                }

                return Ok(new
                {
                    success = true,
                    data = task,
                    message = "获取任务信息成功",
                    version = "v1.1"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "V1.1 获取任务信息异常: {TaskId}", id);
                return StatusCode(500, new
                {
                    success = false,
                    message = "获取任务信息失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 创建任务
        /// </summary>
        /// <param name="dto">创建任务DTO</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public async Task<IActionResult> CreateTask([FromBody] CreateTaskDto dto)
        {
            _logger.LogInformation("V1.1 创建任务: {TaskName}", dto.Title);

            try
            {
                var task = await _taskService.CreateAsync(dto);
                return Ok(new
                {
                    success = true,
                    data = task,
                    message = "任务创建成功",
                    version = "v1.1"
                });
            }
            catch (System.InvalidOperationException ex)
            {
                _logger.LogWarning("V1.1 创建任务业务异常: {Message}", ex.Message);
                return BadRequest(new
                {
                    success = false,
                    message = ex.Message,
                    version = "v1.1"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "V1.1 创建任务异常: {TaskName}", dto.Title);
                return StatusCode(500, new
                {
                    success = false,
                    message = "创建任务失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 更新任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="dto">更新任务DTO</param>
        /// <returns>更新结果</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateTask(long id, [FromBody] UpdateTaskDto dto)
        {
            _logger.LogInformation("V1.1 更新任务: {TaskId}", id);

            try
            {
                var task = await _taskService.UpdateAsync(id, dto);
                return Ok(new
                {
                    success = true,
                    data = task,
                    message = "任务更新成功",
                    version = "v1.1"
                });
            }
            catch (System.InvalidOperationException ex)
            {
                _logger.LogWarning("V1.1 更新任务业务异常: {Message}", ex.Message);
                return BadRequest(new
                {
                    success = false,
                    message = ex.Message,
                    version = "v1.1"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "V1.1 更新任务异常: {TaskId}", id);
                return StatusCode(500, new
                {
                    success = false,
                    message = "更新任务失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 分配任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="request">分配请求</param>
        /// <returns>分配结果</returns>
        [HttpPost("{id}/assign")]
        public async Task<IActionResult> AssignTask(long id, [FromBody] AssignTaskRequest request)
        {
            _logger.LogInformation("V1.1 分配任务: TaskId={TaskId}, AssigneeId={AssigneeId}", id, request.AssigneeId);

            try
            {
                var success = await _taskService.AssignAsync(id, request.AssigneeId);
                if (!success)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "任务分配失败",
                        version = "v1.1"
                    });
                }

                return Ok(new
                {
                    success = true,
                    message = "任务分配成功",
                    version = "v1.1"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "V1.1 分配任务异常: TaskId={TaskId}", id);
                return StatusCode(500, new
                {
                    success = false,
                    message = "分配任务失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 完成任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <returns>完成结果</returns>
        [HttpPost("{id}/complete")]
        public async Task<IActionResult> CompleteTask(long id)
        {
            _logger.LogInformation("V1.1 完成任务: {TaskId}", id);

            try
            {
                var success = await _taskService.CompleteAsync(id);
                if (!success)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "任务完成失败",
                        version = "v1.1"
                    });
                }

                return Ok(new
                {
                    success = true,
                    message = "任务完成成功",
                    version = "v1.1"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "V1.1 完成任务异常: {TaskId}", id);
                return StatusCode(500, new
                {
                    success = false,
                    message = "完成任务失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 删除任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteTask(long id)
        {
            _logger.LogInformation("V1.1 删除任务: {TaskId}", id);

            try
            {
                var success = await _taskService.DeleteAsync(id);
                if (!success)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "任务不存在",
                        version = "v1.1"
                    });
                }

                return Ok(new
                {
                    success = true,
                    message = "任务删除成功",
                    version = "v1.1"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "V1.1 删除任务异常: {TaskId}", id);
                return StatusCode(500, new
                {
                    success = false,
                    message = "删除任务失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 获取用户任务统计
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>任务统计</returns>
        [HttpGet("statistics/{userId}")]
        public async Task<IActionResult> GetUserTaskStatistics(int userId)
        {
            _logger.LogInformation("V1.1 获取用户任务统计: {UserId}", userId);

            try
            {
                var statistics = await _taskService.GetUserTaskStatisticsAsync(userId);
                return Ok(new
                {
                    success = true,
                    data = statistics,
                    message = "获取任务统计成功",
                    version = "v1.1"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "V1.1 获取用户任务统计异常: {UserId}", userId);
                return StatusCode(500, new
                {
                    success = false,
                    message = "获取任务统计失败",
                    version = "v1.1"
                });
            }
        }

        #region 周期性任务管理

        /// <summary>
        /// 分页获取周期性任务计划列表
        /// </summary>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <param name="keyword">关键词</param>
        /// <param name="status">状态</param>
        /// <param name="recurrenceType">周期类型</param>
        /// <param name="creatorUserId">创建人ID</param>
        /// <returns>分页结果</returns>
        [HttpGet("periodic-schedules")]
        public async Task<IActionResult> GetPeriodicSchedules(
            [FromQuery] int pageIndex = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string keyword = null,
            [FromQuery] string status = null,
            [FromQuery] string recurrenceType = null,
            [FromQuery] int? creatorUserId = null)
        {
            _logger.LogInformation("V1.1 获取周期性任务计划列表: pageIndex={PageIndex}, pageSize={PageSize}",
                pageIndex, pageSize);

            try
            {
                var query = new PeriodicTaskScheduleQueryDto
                {
                    PageIndex = pageIndex,
                    PageSize = pageSize,
                    Keyword = keyword,
                    Status = status,
                    RecurrenceType = recurrenceType,
                    CreatorUserId = creatorUserId
                };

                var result = await _taskService.GetPeriodicSchedulesPagedAsync(query);

                return Ok(new
                {
                    success = true,
                    data = new
                    {
                        items = result.Items,
                        totalCount = result.TotalCount,
                        pageIndex = result.PageIndex,
                        pageSize = result.PageSize,
                        totalPages = result.TotalPages
                    },
                    message = "获取周期性任务计划列表成功",
                    version = "v1.1"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "V1.1 获取周期性任务计划列表异常");
                return StatusCode(500, new
                {
                    success = false,
                    message = "获取周期性任务计划列表失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 根据ID获取周期性任务计划详情
        /// </summary>
        /// <param name="id">计划ID</param>
        /// <returns>计划详情</returns>
        [HttpGet("periodic-schedules/{id}")]
        public async Task<IActionResult> GetPeriodicSchedule(long id)
        {
            _logger.LogInformation("V1.1 获取周期性任务计划详情: {ScheduleId}", id);

            try
            {
                var schedule = await _taskService.GetPeriodicScheduleByIdAsync(id);
                if (schedule == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "周期性任务计划不存在",
                        version = "v1.1"
                    });
                }

                return Ok(new
                {
                    success = true,
                    data = schedule,
                    message = "获取周期性任务计划详情成功",
                    version = "v1.1"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "V1.1 获取周期性任务计划详情异常: {ScheduleId}", id);
                return StatusCode(500, new
                {
                    success = false,
                    message = "获取周期性任务计划详情失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 创建周期性任务计划
        /// </summary>
        /// <param name="dto">创建计划DTO</param>
        /// <returns>创建结果</returns>
        [HttpPost("periodic-schedules")]
        public async Task<IActionResult> CreatePeriodicSchedule([FromBody] CreatePeriodicTaskScheduleDto dto)
        {
            _logger.LogInformation("V1.1 创建周期性任务计划: {ScheduleName}", dto.Name);

            try
            {
                var schedule = await _taskService.CreatePeriodicScheduleAsync(dto);
                return Ok(new
                {
                    success = true,
                    data = schedule,
                    message = "周期性任务计划创建成功",
                    version = "v1.1"
                });
            }
            catch (System.InvalidOperationException ex)
            {
                _logger.LogWarning("V1.1 创建周期性任务计划业务异常: {Message}", ex.Message);
                return BadRequest(new
                {
                    success = false,
                    message = ex.Message,
                    version = "v1.1"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "V1.1 创建周期性任务计划异常: {ScheduleName}", dto.Name);
                return StatusCode(500, new
                {
                    success = false,
                    message = "创建周期性任务计划失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 更新周期性任务计划
        /// </summary>
        /// <param name="id">计划ID</param>
        /// <param name="dto">更新计划DTO</param>
        /// <returns>更新结果</returns>
        [HttpPut("periodic-schedules/{id}")]
        public async Task<IActionResult> UpdatePeriodicSchedule(long id, [FromBody] UpdatePeriodicTaskScheduleDto dto)
        {
            _logger.LogInformation("V1.1 更新周期性任务计划: {ScheduleId}", id);

            try
            {
                var schedule = await _taskService.UpdatePeriodicScheduleAsync(id, dto);
                return Ok(new
                {
                    success = true,
                    data = schedule,
                    message = "周期性任务计划更新成功",
                    version = "v1.1"
                });
            }
            catch (System.InvalidOperationException ex)
            {
                _logger.LogWarning("V1.1 更新周期性任务计划业务异常: {Message}", ex.Message);
                return BadRequest(new
                {
                    success = false,
                    message = ex.Message,
                    version = "v1.1"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "V1.1 更新周期性任务计划异常: {ScheduleId}", id);
                return StatusCode(500, new
                {
                    success = false,
                    message = "更新周期性任务计划失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 删除周期性任务计划
        /// </summary>
        /// <param name="id">计划ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("periodic-schedules/{id}")]
        public async Task<IActionResult> DeletePeriodicSchedule(long id)
        {
            _logger.LogInformation("V1.1 删除周期性任务计划: {ScheduleId}", id);

            try
            {
                var success = await _taskService.DeletePeriodicScheduleAsync(id);
                if (!success)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "周期性任务计划不存在",
                        version = "v1.1"
                    });
                }

                return Ok(new
                {
                    success = true,
                    message = "周期性任务计划删除成功",
                    version = "v1.1"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "V1.1 删除周期性任务计划异常: {ScheduleId}", id);
                return StatusCode(500, new
                {
                    success = false,
                    message = "删除周期性任务计划失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 启用/禁用周期性任务计划
        /// </summary>
        /// <param name="id">计划ID</param>
        /// <param name="request">启用/禁用请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("periodic-schedules/{id}/toggle")]
        public async Task<IActionResult> TogglePeriodicSchedule(long id, [FromBody] ToggleScheduleRequest request)
        {
            _logger.LogInformation("V1.1 切换周期性任务计划状态: ScheduleId={ScheduleId}, Enabled={Enabled}", id, request.Enabled);

            try
            {
                var success = await _taskService.EnablePeriodicScheduleAsync(id, request.Enabled);
                if (!success)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "切换周期性任务计划状态失败",
                        version = "v1.1"
                    });
                }

                return Ok(new
                {
                    success = true,
                    message = request.Enabled ? "周期性任务计划已启用" : "周期性任务计划已禁用",
                    version = "v1.1"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "V1.1 切换周期性任务计划状态异常: ScheduleId={ScheduleId}", id);
                return StatusCode(500, new
                {
                    success = false,
                    message = "切换周期性任务计划状态失败",
                    version = "v1.1"
                });
            }
        }

        #endregion

        /// <summary>
        /// 健康检查端点
        /// </summary>
        /// <returns>服务状态</returns>
        [HttpGet("health")]
        [AllowAnonymous]
        public IActionResult Health()
        {
            return Ok(new
            {
                status = "healthy",
                version = "v1.1",
                service = "TaskService",
                timestamp = System.DateTime.UtcNow,
                features = new[]
                {
                    "基础任务管理",
                    "周期性任务管理",
                    "任务统计",
                    "性能监控"
                }
            });
        }
    }

    /// <summary>
    /// 分配任务请求模型
    /// </summary>
    public class AssignTaskRequest
    {
        public int AssigneeId { get; set; }
    }

    /// <summary>
    /// 切换周期性任务计划状态请求模型
    /// </summary>
    public class ToggleScheduleRequest
    {
        public bool Enabled { get; set; }
    }
}
