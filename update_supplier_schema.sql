-- 供应商表结构更新脚本
-- 添加供应商类型字段，支持采购和维修供应商统一管理

-- 1. 检查是否已存在supplier_type字段
SELECT COUNT(*) as field_exists FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = 'itassets' AND TABLE_NAME = 'suppliers' AND COLUMN_NAME = 'supplier_type';

-- 2. 添加新的供应商类型字段（如果不存在）
ALTER TABLE suppliers
ADD COLUMN IF NOT EXISTS supplier_type INT NOT NULL DEFAULT 1 COMMENT '供应商类型：1=采购，2=维修，3=采购+维修';

-- 3. 更新现有数据，根据原有Type字段设置新的supplier_type
-- 原有Type字段：0硬件，1软件，2服务 -> 都设置为采购供应商
UPDATE suppliers
SET supplier_type = 1
WHERE supplier_type = 1 OR supplier_type IS NULL; -- 设置默认值为采购供应商

-- 3. 添加一些示例维修供应商数据
INSERT INTO suppliers (Name, Code, supplier_type, ContactPerson, ContactPhone, ContactEmail, Address, Notes, IsActive, CreatedAt, UpdatedAt) VALUES
('北京设备维修中心', 'BJWX001', 2, '张师傅', '13800138001', '<EMAIL>', '北京市朝阳区维修大街1号', '专业设备维修服务', 1, NOW(), NOW()),
('上海工控维修公司', 'SHWX001', 2, '李工程师', '13800138002', '<EMAIL>', '上海市浦东新区工控路88号', '工控设备专业维修', 1, NOW(), NOW()),
('深圳电子设备维修', 'SZWX001', 2, '王技师', '13800138003', '<EMAIL>', '深圳市南山区科技园维修中心', '电子设备维修专家', 1, NOW(), NOW()),
('广州综合维修服务', 'GZWX001', 3, '陈经理', '13800138004', '<EMAIL>', '广州市天河区维修服务大厦', '提供采购和维修一体化服务', 1, NOW(), NOW());

-- 4. 创建索引优化查询性能
CREATE INDEX idx_suppliers_supplier_type ON suppliers(supplier_type);
CREATE INDEX idx_suppliers_is_active ON suppliers(IsActive);
CREATE INDEX idx_suppliers_type_active ON suppliers(supplier_type, IsActive);

-- 5. 查看更新后的供应商数据
SELECT 
    Id,
    Name,
    Code,
    supplier_type,
    CASE supplier_type
        WHEN 1 THEN '采购供应商'
        WHEN 2 THEN '维修供应商'
        WHEN 3 THEN '采购+维修供应商'
        ELSE '未知类型'
    END as supplier_type_name,
    ContactPerson,
    ContactPhone,
    IsActive
FROM suppliers
ORDER BY supplier_type, Name;

-- 6. 统计各类型供应商数量
SELECT 
    supplier_type,
    CASE supplier_type
        WHEN 1 THEN '采购供应商'
        WHEN 2 THEN '维修供应商'
        WHEN 3 THEN '采购+维修供应商'
        ELSE '未知类型'
    END as supplier_type_name,
    COUNT(*) as count,
    SUM(CASE WHEN IsActive = 1 THEN 1 ELSE 0 END) as active_count
FROM suppliers
GROUP BY supplier_type
ORDER BY supplier_type;
