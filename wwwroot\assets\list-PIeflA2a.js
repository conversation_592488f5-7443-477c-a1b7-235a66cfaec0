import{aj as e,_ as a,r as l,p as t,m as s,q as r,b as o,o as u,e as n,w as d,d as i,a as p,f as c,ay as v,az as m,A as f,a8 as g,a5 as y,am as w,V as h,ag as b,Y as _,a9 as V,F as P,h as k,v as T,ax as S,af as C,t as x,aA as z,an as $,P as U,aB as I,aC as N,ao as R,a7 as F}from"./index-C7OOw0MO.js";import{g as M}from"./spareparts-NQmkyISR.js";import{g as K}from"./asset-CPgk_3Sp.js";import"./system-9jEcQzSp.js";const D="/fault",Y={getFaultList:a=>e.get(D,a),getFaultById:a=>e.get(`${D}/${a}`),createFault:a=>e.post("/api/v2/faults",{faultMode:a.faultMode||"asset",assetId:a.assetId||null,assetKeyword:a.assetKeyword||null,deviceName:a.deviceName||null,faultType:a.faultType,title:a.title,description:a.description,priority:a.priority,happenTime:a.happenTime,autoGenerateSparePartRecord:a.autoGenerateSparePartRecord||!1,sparePartInfo:a.sparePartInfo||null}),updateFault:(a,l)=>e.put(`${D}/${a}`,l),deleteFault:a=>e.delete(`${D}/${a}`),getFaultTypeList:a=>e.get("/fault-types",a),createFaultType:a=>e.post("/fault-types",a),updateFaultType:(a,l)=>e.put(`/fault-types/${a}`,l),deleteFaultType:a=>e.delete(`/fault-types/${a}`),assignFault:(a,l)=>e.post(`${D}/${a}/assign`,l),startFaultProcessing:a=>e.put(`${D}/${a}/start-processing`),pauseFaultProcessing:(a,l)=>e.put(`${D}/${a}/pause-processing`,l),completeFault:(a,l)=>e.put(`${D}/${a}/complete`,l),closeFault:(a,l)=>e.put(`${D}/${a}/close`,l),reopenFault:(a,l)=>e.put(`${D}/${a}/reopen`,l),addFaultRecord:(a,l)=>e.post(`${D}/${a}/records`,l),getFaultRecords:(a,l)=>e.get(`${D}/${a}/records`,l),exportFaults:a=>e.download(`${D}/export`,a,"faults.xlsx"),getFaultStatistics:a=>e.get(`${D}/statistics`,a),useSpareParts:(a,l)=>e.post(`${D}/${a}/use-spare-parts`,l),createReturnToFactory:(a,l)=>e.post(`${D}/${a}/return-to-factory`,l),searchAssets:a=>e.get("/assets/search",a)},j={class:"qr-scanner"},G={class:"scanner-container"},Q={key:0,class:"no-camera"},q={key:1,class:"loading"},B={key:2,class:"camera-view"},O={class:"dialog-footer"},E=a({__name:"QrCodeScanner",props:{modelValue:{type:Boolean,default:!1},scanType:{type:String,default:"asset",validator:e=>["asset","faultType","deviceType"].includes(e)}},emits:["update:modelValue","scan-success","manual-input"],setup(e,{emit:a}){const y=e,w=a,h=l(!1),b=l(!1),_=l(!0),V=l(null),P=l(null),k=l(null),T=l(null);t((()=>y.modelValue),(e=>{h.value=e,e?S():C()}));const S=async()=>{b.value=!0;try{if(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)return _.value=!1,void(b.value=!1);const e={video:{width:{ideal:400},height:{ideal:300},facingMode:"environment"}};k.value=await navigator.mediaDevices.getUserMedia(e),V.value&&(V.value.srcObject=k.value,V.value.onloadedmetadata=()=>{b.value=!1,x()})}catch(e){_.value=!1,b.value=!1,g.error("无法访问摄像头，请检查权限设置")}},C=()=>{k.value&&(k.value.getTracks().forEach((e=>e.stop())),k.value=null),T.value&&(clearInterval(T.value),T.value=null)},x=()=>{V.value&&P.value&&(T.value=setInterval((()=>{z()}),500))},z=()=>{if(!V.value||!P.value)return;const e=V.value,a=P.value,l=a.getContext("2d");if(a.width=e.videoWidth,a.height=e.videoHeight,l.drawImage(e,0,0,a.width,a.height),l.getImageData(0,0,a.width,a.height),Math.random()<.1){const e=$();U(e)}},$=()=>{switch(y.scanType){case"asset":return{type:"asset",code:"PC-2024-001",name:"联想台式机",id:1001};case"faultType":return{type:"faultType",code:"FT-HW-001",name:"硬件故障",id:1};case"deviceType":return{type:"deviceType",code:"DT-PC-001",name:"台式电脑",id:1};default:return{type:"unknown",code:"UNKNOWN-001",name:"未知类型",id:0}}},U=e=>{g.success(`扫描成功：${e.name} (${e.code})`),w("scan-success",e),I()},I=()=>{C(),w("update:modelValue",!1)},N=()=>{w("manual-input",y.scanType),I()};return s((()=>{y.modelValue&&(h.value=!0,S())})),r((()=>{C()})),(e,a)=>{const l=p("el-icon"),t=p("el-button"),s=p("el-dialog");return u(),o("div",j,[n(s,{modelValue:h.value,"onUpdate:modelValue":a[0]||(a[0]=e=>h.value=e),title:"扫描二维码",width:"500px","before-close":I},{footer:d((()=>[i("div",O,[n(t,{onClick:I},{default:d((()=>a[4]||(a[4]=[f("取消")]))),_:1}),n(t,{type:"primary",onClick:N},{default:d((()=>a[5]||(a[5]=[f("手动输入")]))),_:1})])])),default:d((()=>[i("div",G,[_.value?b.value?(u(),o("div",q,[n(l,{size:"48",class:"is-loading"},{default:d((()=>[n(c(m))])),_:1}),a[2]||(a[2]=i("p",null,"正在启动摄像头...",-1))])):(u(),o("div",B,[i("video",{ref_key:"videoRef",ref:V,autoplay:"",playsinline:""},null,512),i("canvas",{ref_key:"canvasRef",ref:P,style:{display:"none"}},null,512),a[3]||(a[3]=i("div",{class:"scan-frame"},[i("div",{class:"corner top-left"}),i("div",{class:"corner top-right"}),i("div",{class:"corner bottom-left"}),i("div",{class:"corner bottom-right"})],-1))])):(u(),o("div",Q,[n(l,{size:"48"},{default:d((()=>[n(c(v))])),_:1}),a[1]||(a[1]=i("p",null,"未检测到摄像头",-1))]))]),a[6]||(a[6]=i("div",{class:"scanner-tips"},[i("p",null,"请将二维码对准扫描框"),i("p",null,"支持资产码、故障类型码等")],-1))])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-b5621b23"]]),H={class:"fault-list-container"},L={class:"page-header"},A={class:"page-actions"},W={class:"filter-container"},J={key:0,class:"asset-info"},X={class:"asset-name"},Z={class:"asset-code text-secondary"},ee={class:"pagination-container"},ae={class:"asset-suggestion-item"},le={class:"asset-main"},te={class:"asset-name"},se={class:"asset-code"},re={class:"asset-detail"},oe={class:"asset-sn"},ue={class:"asset-location"},ne={key:0,class:"selected-asset"},de={class:"asset-info"},ie={class:"asset-name"},pe={class:"asset-code"},ce={key:0,class:"asset-sn"},ve={class:"form-tip"},me={class:"spare-part-option"},fe={class:"spare-part-name"},ge={class:"spare-part-spec"},ye={class:"spare-part-brand"},we={class:"form-tip"},he={class:"fault-type-input-group"},be={class:"form-tip"},_e={class:"form-tip"},Ve={key:2,class:"spare-part-section"},Pe={class:"dialog-footer"},ke={class:"search-bar"},Te={class:"asset-pagination"},Se={class:"dialog-footer"},Ce={class:"spare-parts-dialog-content"},xe={class:"fault-info"},ze={class:"spare-parts-selection"},$e={class:"section-header"},Ue={key:0,class:"stock-info"},Ie={class:"dialog-footer"},Ne={class:"return-to-factory-dialog-content"},Re={class:"fault-info"},Fe={class:"dialog-footer"},Me=a({__name:"list",setup(e){const a=20,t=l(!1),r=l([]),v=l(null),m=y({currentPage:1,pageSize:10,total:0}),D=y({code:"",assetKeyword:"",faultType:"",status:"",timeRange:[]}),j=[{label:"硬件故障",value:"hardware"},{label:"软件故障",value:"software"},{label:"网络故障",value:"network"},{label:"外设故障",value:"peripheral"},{label:"其他故障",value:"other"}],G=[{label:"紧急",value:"critical"},{label:"高",value:"high"},{label:"中",value:"medium"},{label:"低",value:"low"}],Q=[{label:"待处理",value:"pending"},{label:"处理中",value:"processing"},{label:"已修复",value:"resolved"},{label:"已关闭",value:"closed"},{label:"待返修",value:"repair_pending"},{label:"返修中",value:"repairing"}],q=[{text:"最近一周",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-6048e5),[a,e]}},{text:"最近一个月",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-2592e6),[a,e]}},{text:"最近三个月",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-7776e6),[a,e]}}],B=l(!1),O=l({faultMode:"asset",assetId:"",assetName:"",assetCode:"",assetSn:"",assetKeyword:"",deviceName:"",title:"",faultType:"hardware",priority:"medium",happenTime:(new Date).toISOString().slice(0,16),description:"",attachments:[],autoGenerateSparePartRecord:!1,sparePartName:"",sparePartSpecification:"",sparePartBrand:"",sparePartQuantity:1,sparePartPrice:null}),Me=l(null),Ke=l([]),De=l([]),Ye=l(!1),je={title:[{required:!0,message:"请输入故障标题",trigger:"blur"},{min:2,max:100,message:"长度在 2 到 100 个字符",trigger:"blur"}],description:[{max:1e3,message:"长度不能超过 1000 个字符",trigger:"blur"}],sparePartName:[{validator:(e,a,l)=>{O.value.autoGenerateSparePartRecord&&!a?l(new Error("启用自动生成备件记录时，备件名称不能为空")):l()},trigger:"blur"}]},Ge=l([]),Qe=l(!1),qe=l(""),Be=l(!1),Oe=l([]),Ee=y({currentPage:1,pageSize:10,total:0});s((()=>{He()}));const He=async()=>{var e,a;t.value=!0;const l={page:m.currentPage,pageSize:m.pageSize,code:D.code,assetKeyword:D.assetKeyword,faultType:D.faultType,status:D.status,startTime:null==(e=D.timeRange)?void 0:e[0],endTime:null==(a=D.timeRange)?void 0:a[1]};try{const e=await Y.getFaultList(l);e.success?(r.value=e.data.items||e.data||[],m.total=e.data.total||e.data.length||0):(g.error(e.message||"获取故障列表失败"),r.value=[],m.total=0)}catch(s){g.error("获取故障列表失败"),r.value=[],m.total=0}finally{t.value=!1}},Le=()=>{m.currentPage=1,He()},Ae=()=>{D.code="",D.assetKeyword="",D.faultType="",D.status="",D.timeRange=[],m.currentPage=1,He()},We=e=>{m.pageSize=e,He()},Je=e=>{m.currentPage=e,He()},Xe=()=>{g.success("开始导出数据，请稍候...")},Ze=()=>{ea(),B.value=!0},ea=()=>{Object.keys(O.value).forEach((e=>{O.value[e]="priority"===e?"medium":"faultType"===e?"hardware":"faultMode"===e?"asset":"happenTime"===e?(new Date).toISOString().slice(0,16):"sparePartQuantity"===e?1:"autoGenerateSparePartRecord"!==e&&("attachments"===e?[]:"sparePartPrice"===e?null:"")})),Ge.value=[],Ke.value=[],De.value=[]},aa=async()=>{var e;try{await(null==(e=Me.value)?void 0:e.validate())}catch(a){return}if(!O.value.title||O.value.title.trim().length<2)g.warning("请输入至少2个字符的故障标题");else{if(O.value.autoGenerateSparePartRecord){if(!O.value.sparePartName)return void g.warning("启用自动生成备件记录时，备件名称不能为空");if(O.value.sparePartQuantity<=0)return void g.warning("备件数量必须大于0")}try{const e={faultMode:O.value.faultMode,assetId:"asset"===O.value.faultMode&&O.value.assetId||null,assetKeyword:"asset"===O.value.faultMode?O.value.assetKeyword:null,deviceName:"offline"===O.value.faultMode?O.value.deviceName:null,faultType:O.value.faultType,title:O.value.title,description:O.value.description,priority:O.value.priority,happenTime:O.value.happenTime,autoGenerateSparePartRecord:O.value.autoGenerateSparePartRecord,sparePartInfo:O.value.autoGenerateSparePartRecord?{name:O.value.sparePartName,specification:O.value.sparePartSpecification,brand:O.value.sparePartBrand,quantity:O.value.sparePartQuantity,price:O.value.sparePartPrice}:null},a=await Y.createFault(e);a.success?(g.success("故障登记成功"+(O.value.autoGenerateSparePartRecord?"，备件记录已自动创建":"")),B.value=!1,He()):g.error(a.message||"故障登记失败")}catch(a){g.error("故障登记失败")}}},la=()=>{Qe.value=!0,qe.value=O.value.assetKeyword,ta()},ta=async()=>{Be.value=!0;const e={keyword:qe.value,page:Ee.currentPage,pageSize:a};try{const a=await K({keyword:qe.value,pageSize:e.pageSize,pageIndex:e.page});a.success?(Oe.value=a.data.items||a.data||[],Ee.total=a.data.total||a.data.length||0):(g.error(a.message||"搜索资产失败"),Oe.value=[],Ee.total=0)}catch(l){g.error("搜索资产失败"),Oe.value=[],Ee.total=0}finally{Be.value=!1}},sa=e=>{O.value.assetId=e.id,O.value.assetName=e.name,O.value.assetCode=e.code,O.value.assetKeyword=`${e.name} (${e.code})`,Qe.value=!1},ra=()=>{O.value.assetId="",O.value.assetName="",O.value.assetCode="",O.value.assetSn="",O.value.assetKeyword=""},oa=e=>{"asset"===e?O.value.deviceName="":ra()},ua=async(e,a)=>{if(!e||e.length<2)a([]);else try{const l=await K({keyword:e,pageSize:10,pageIndex:1});if(l.success){const e=l.data.items||l.data||[];a(e.map((e=>({id:e.id,name:e.name,code:e.code,sn:e.sn,locationName:e.locationName||"未分配",value:`${e.name} (${e.code})`}))))}else a([])}catch(l){a([])}},na=e=>{O.value.assetId=e.id,O.value.assetName=e.name,O.value.assetCode=e.code,O.value.assetSn=e.sn,O.value.assetKeyword=e.value},da=async()=>{if(!(De.value.length>0)){Ye.value=!0;try{const e=await M({pageSize:100,onlyNames:!0});if(e.success){const a={};(e.data.items||e.data||[]).forEach((e=>{a[e.name]||(a[e.name]={id:e.id,name:e.name,specification:e.specification,brand:e.brand})})),De.value=Object.values(a)}else g.error("获取备件台账失败"),De.value=[]}catch(e){g.error("获取备件台账失败"),De.value=[]}finally{Ye.value=!1}}},ia=()=>{xa.value="asset",Ca.value=!0},pa=()=>{xa.value="faultType",Ca.value=!0},ca=e=>{switch(e.type){case"asset":O.value.assetId=e.id,O.value.assetName=e.name,O.value.assetCode=e.code,O.value.assetKeyword=`${e.name} (${e.code})`,g.success(`已关联资产：${e.name}`);break;case"faultType":O.value.faultType=e.id,g.success(`已选择故障类型：${e.name}`);break;case"deviceType":e.name.includes("电脑")||e.name.includes("PC")?O.value.faultType="hardware":(e.name.includes("打印机")||e.name.includes("扫码器"))&&(O.value.faultType="peripheral"),g.success(`已识别设备类型：${e.name}`)}},va=e=>{switch(e){case"asset":la();break;case"faultType":g.info("请从下拉列表中选择故障类型");break;case"deviceType":g.info("请手动选择对应的故障类型")}},ma=e=>{Ee.pageSize=e,ta()},fa=e=>{Ee.currentPage=e,ta()},ga=e=>{if(e.size>10485760){g.warning("文件大小不能超过10MB");const a=Ge.value.indexOf(e);return void(-1!==a&&Ge.value.splice(a,1))}const a=e.name.split(".").pop().toLowerCase();if(["jpg","jpeg","png","pdf"].includes(a));else{g.warning("只支持jpg、png、pdf格式文件");const a=Ge.value.indexOf(e);-1!==a&&Ge.value.splice(a,1)}},ya=e=>{const a=Ge.value.indexOf(e);-1!==a&&Ge.value.splice(a,1)},wa=e=>({hardware:"硬件故障",software:"软件故障",network:"网络故障",peripheral:"外设故障",other:"其他故障"}[e]||"未知类型"),ha=e=>({critical:"紧急",high:"高",medium:"中",low:"低"}[e]||"未知"),ba=e=>({pending:"待处理",processing:"处理中",resolved:"已修复",closed:"已关闭",repair_pending:"待返修",repairing:"返修中"}[e]||"未知"),_a=e=>"processing"===e.status,Va=e=>["pending","processing"].includes(e.status),Pa=l(!1),ka=l(null),Ta=y({spareParts:[],notes:""}),Sa=l([]),Ca=l(!1),xa=l("asset"),za=e=>{ka.value=e,Ta.spareParts=[],Ta.notes="",Pa.value=!0,(async()=>{Ye.value=!0;try{const e=await M({pageSize:100});e.success?Sa.value=e.data.items||e.data||[]:(g.error("获取备件列表失败"),Sa.value=[])}catch(e){g.error("获取备件列表失败"),Sa.value=[]}finally{Ye.value=!1}})()},$a=()=>{Ta.spareParts.push({sparePartId:"",sparePartName:"",quantity:1,notes:""})},Ua=async()=>{try{if(0===Ta.spareParts.length)return void g.warning("请至少添加一个备件");for(const a of Ta.spareParts){if(!a.sparePartId)return void g.warning("请选择备件");if(a.quantity<=0)return void g.warning("备件数量必须大于0")}const e=await Y.useSpareParts(ka.value.id,{spareParts:Ta.spareParts,notes:Ta.notes});if(!e.success)return void g.error(e.message||"备件使用记录失败");g.success("备件使用记录成功"),Pa.value=!1,He()}catch(e){g.error("备件使用记录失败："+e.message)}},Ia=l(!1),Na=y({supplierId:"",reason:"",estimatedReturnTime:"",notes:""}),Ra=async()=>{try{if(!Na.supplierId)return void g.warning("请选择供应商");if(!Na.reason)return void g.warning("请输入返厂原因");const e=await Y.createReturnToFactory(ka.value.id,{assetId:ka.value.assetId,supplierId:Na.supplierId,reason:Na.reason,estimatedReturnTime:Na.estimatedReturnTime,notes:Na.notes});if(!e.success)return void g.error(e.message||"创建返厂记录失败");g.success("返厂记录创建成功"),Ia.value=!1,He()}catch(e){g.error("创建返厂记录失败："+e.message)}},Fa=e=>{const a=Sa.value.find((a=>a.id===e));return a&&a.stockQuantity||0};return(e,a)=>{const l=p("el-button"),s=p("el-input"),y=p("el-form-item"),M=p("el-option"),K=p("el-select"),Y=p("el-date-picker"),Ke=p("el-form"),He=p("el-card"),ea=p("el-table-column"),Ma=p("el-tag"),Ka=p("el-table"),Da=p("el-pagination"),Ya=p("el-radio"),ja=p("el-radio-group"),Ga=p("el-button-group"),Qa=p("el-autocomplete"),qa=p("el-text"),Ba=p("el-checkbox"),Oa=p("el-divider"),Ea=p("el-col"),Ha=p("el-row"),La=p("el-input-number"),Aa=p("el-icon"),Wa=p("el-upload"),Ja=p("el-dialog"),Xa=b("loading");return u(),o("div",H,[i("div",L,[a[39]||(a[39]=i("h2",{class:"page-title"},"故障列表",-1)),i("div",A,[n(l,{type:"primary",onClick:Ze,icon:c(w)},{default:d((()=>a[37]||(a[37]=[f(" 登记故障 ")]))),_:1},8,["icon"]),n(l,{type:"primary",onClick:Xe,icon:c(h)},{default:d((()=>a[38]||(a[38]=[f(" 导出数据 ")]))),_:1},8,["icon"])])]),n(He,{class:"filter-card"},{default:d((()=>[i("div",W,[D?(u(),_(Ke,{key:0,inline:!0,model:D,class:"filter-form"},{default:d((()=>[n(y,{label:"故障编号"},{default:d((()=>[n(s,{modelValue:D.code,"onUpdate:modelValue":a[0]||(a[0]=e=>D.code=e),placeholder:"故障编号",clearable:""},null,8,["modelValue"])])),_:1}),n(y,{label:"资产信息"},{default:d((()=>[n(s,{modelValue:D.assetKeyword,"onUpdate:modelValue":a[1]||(a[1]=e=>D.assetKeyword=e),placeholder:"资产名称/编号/SN",clearable:""},null,8,["modelValue"])])),_:1}),n(y,{label:"故障类型"},{default:d((()=>[n(K,{modelValue:D.faultType,"onUpdate:modelValue":a[2]||(a[2]=e=>D.faultType=e),placeholder:"全部类型",clearable:""},{default:d((()=>[(u(),o(P,null,k(j,(e=>n(M,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),n(y,{label:"处理状态"},{default:d((()=>[n(K,{modelValue:D.status,"onUpdate:modelValue":a[3]||(a[3]=e=>D.status=e),placeholder:"全部状态",clearable:""},{default:d((()=>[(u(),o(P,null,k(Q,(e=>n(M,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),n(y,{label:"发生时间"},{default:d((()=>[n(Y,{modelValue:D.timeRange,"onUpdate:modelValue":a[4]||(a[4]=e=>D.timeRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",shortcuts:q},null,8,["modelValue"])])),_:1}),n(y,null,{default:d((()=>[n(l,{type:"primary",onClick:Le,icon:c(T)},{default:d((()=>a[40]||(a[40]=[f(" 搜索 ")]))),_:1},8,["icon"]),n(l,{onClick:Ae,icon:c(S)},{default:d((()=>a[41]||(a[41]=[f(" 重置 ")]))),_:1},8,["icon"])])),_:1})])),_:1},8,["model"])):V("",!0)])])),_:1}),n(He,{class:"data-card"},{default:d((()=>[C((u(),_(Ka,{ref_key:"faultTable",ref:v,data:r.value||[],border:"",style:{width:"100%"}},{default:d((()=>[n(ea,{prop:"code",label:"故障编号",width:"120",sortable:""}),n(ea,{prop:"title",label:"故障标题","min-width":"180","show-overflow-tooltip":""}),n(ea,{prop:"assetInfo",label:"故障资产",width:"200","show-overflow-tooltip":""},{default:d((e=>[e.row?(u(),o("div",J,[i("div",X,x(e.row.assetName||"未关联资产"),1),i("div",Z,x(e.row.assetCode||""),1)])):V("",!0)])),_:1}),n(ea,{prop:"faultType",label:"故障类型",width:"120"},{default:d((e=>{return[e.row?(u(),_(Ma,{key:0,type:(a=e.row.faultType,{hardware:"danger",software:"warning",network:"info",peripheral:"success",other:""}[a]||""),size:"small"},{default:d((()=>[f(x(wa(e.row.faultType)),1)])),_:2},1032,["type"])):V("",!0)];var a})),_:1}),n(ea,{prop:"priority",label:"优先级",width:"100"},{default:d((e=>{return[e.row?(u(),_(Ma,{key:0,type:(a=e.row.priority,{critical:"danger",high:"warning",medium:"",low:"info"}[a]||""),size:"small"},{default:d((()=>[f(x(ha(e.row.priority)),1)])),_:2},1032,["type"])):V("",!0)];var a})),_:1}),n(ea,{prop:"status",label:"处理状态",width:"120"},{default:d((e=>{return[e.row?(u(),_(Ma,{key:0,type:(a=e.row.status,{pending:"info",processing:"warning",resolved:"success",closed:"",repair_pending:"warning",repairing:"danger"}[a]||""),size:"small"},{default:d((()=>[f(x(ba(e.row.status)),1)])),_:2},1032,["type"])):V("",!0)];var a})),_:1}),n(ea,{prop:"reportUser",label:"报告人",width:"120"}),n(ea,{prop:"reportTime",label:"报告时间",width:"180",sortable:""}),n(ea,{prop:"handler",label:"处理人",width:"120"}),n(ea,{prop:"updateTime",label:"更新时间",width:"180",sortable:""}),n(ea,{label:"操作",width:"280",fixed:"right"},{default:d((e=>{return[e.row?(u(),o(P,{key:0},[n(l,{type:"primary",text:"",size:"small",onClick:a=>{return l=e.row,void g.info(`查看故障详情：${l.code}`);var l},icon:c(z)},{default:d((()=>a[42]||(a[42]=[f(" 详情 ")]))),_:2},1032,["onClick","icon"]),n(l,{type:"success",text:"",size:"small",onClick:a=>{return l=e.row,void g.info(`处理故障：${l.code}`);var l},icon:c($),disabled:(t=e.row,!["pending","processing"].includes(t.status))},{default:d((()=>a[43]||(a[43]=[f(" 处理 ")]))),_:2},1032,["onClick","icon","disabled"]),n(l,{type:"info",text:"",size:"small",onClick:a=>za(e.row),icon:c(U),disabled:!_a(e.row)},{default:d((()=>a[44]||(a[44]=[f(" 用料 ")]))),_:2},1032,["onClick","icon","disabled"]),n(l,{type:"warning",text:"",size:"small",onClick:a=>(e=>{ka.value=e,Na.supplierId="",Na.reason="",Na.estimatedReturnTime="",Na.notes="",Ia.value=!0})(e.row),icon:c(I),disabled:!Va(e.row)},{default:d((()=>a[45]||(a[45]=[f(" 返厂 ")]))),_:2},1032,["onClick","icon","disabled"])],64)):V("",!0)];var t})),_:1})])),_:1},8,["data"])),[[Xa,t.value]]),i("div",ee,[n(Da,{"current-page":m.currentPage,"onUpdate:currentPage":a[5]||(a[5]=e=>m.currentPage=e),"page-size":m.pageSize,"onUpdate:pageSize":a[6]||(a[6]=e=>m.pageSize=e),"page-sizes":[10,20,50,100],background:!0,layout:"total, sizes, prev, pager, next, jumper",total:m.total,onSizeChange:We,onCurrentChange:Je},null,8,["current-page","page-size","total"])])])),_:1}),n(Ja,{title:"登记故障",modelValue:B.value,"onUpdate:modelValue":a[22]||(a[22]=e=>B.value=e),width:"700px","append-to-body":""},{footer:d((()=>[i("div",Pe,[n(l,{onClick:a[21]||(a[21]=e=>B.value=!1)},{default:d((()=>a[59]||(a[59]=[f("取 消")]))),_:1}),n(l,{type:"primary",onClick:aa},{default:d((()=>a[60]||(a[60]=[f("确 定")]))),_:1})])])),default:d((()=>[n(Ke,{ref_key:"faultFormRef",ref:Me,model:O.value,rules:je,"label-width":"100px"},{default:d((()=>[n(y,{label:"故障模式",prop:"faultMode"},{default:d((()=>[n(ja,{modelValue:O.value.faultMode,"onUpdate:modelValue":a[7]||(a[7]=e=>O.value.faultMode=e),onChange:oa},{default:d((()=>[n(Ya,{value:"asset"},{default:d((()=>a[46]||(a[46]=[f("有资产编号设备")]))),_:1}),n(Ya,{value:"offline"},{default:d((()=>a[47]||(a[47]=[f("线下设备（备件台账）")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),"asset"===O.value.faultMode?(u(),_(y,{key:0,label:"资产",prop:"assetId"},{default:d((()=>[n(Qa,{modelValue:O.value.assetKeyword,"onUpdate:modelValue":a[8]||(a[8]=e=>O.value.assetKeyword=e),"fetch-suggestions":ua,placeholder:"请输入资产编号/名称/SN进行搜索",clearable:"",maxlength:100,style:{width:"100%"},"trigger-on-focus":!1,onSelect:na,onClear:ra},{default:d((({item:e})=>[i("div",ae,[i("div",le,[i("span",te,x(e.name),1),i("span",se,x(e.code),1)]),i("div",re,[i("span",oe,"SN: "+x(e.sn||"无"),1),i("span",ue,x(e.locationName||"未分配"),1)])])])),append:d((()=>[n(Ga,null,{default:d((()=>[n(l,{icon:c(N),onClick:ia},{default:d((()=>a[48]||(a[48]=[f("扫码")]))),_:1},8,["icon"]),n(l,{icon:c(T),onClick:la},{default:d((()=>a[49]||(a[49]=[f("高级搜索")]))),_:1},8,["icon"])])),_:1})])),_:1},8,["modelValue"]),O.value.assetId?(u(),o("div",ne,[i("div",de,[i("span",ie,x(O.value.assetName),1),i("span",pe,x(O.value.assetCode),1),O.value.assetSn?(u(),o("span",ce,"SN: "+x(O.value.assetSn),1)):V("",!0)]),n(l,{link:"",type:"danger",onClick:ra,icon:c(R),size:"small"},{default:d((()=>a[50]||(a[50]=[f(" 清除 ")]))),_:1},8,["icon"])])):V("",!0),i("div",ve,[n(qa,{type:"info",size:"small"},{default:d((()=>a[51]||(a[51]=[f(" 输入时会自动搜索匹配的资产，选择后保存资产信息，否则保存输入的内容 ")]))),_:1})])])),_:1})):V("",!0),"offline"===O.value.faultMode?(u(),_(y,{key:1,label:"设备名称",prop:"deviceName"},{default:d((()=>[n(K,{modelValue:O.value.deviceName,"onUpdate:modelValue":a[9]||(a[9]=e=>O.value.deviceName=e),placeholder:"请从备件台账中选择设备名称",filterable:"",clearable:"",style:{width:"100%"},loading:Ye.value,onFocus:da},{default:d((()=>[(u(!0),o(P,null,k(De.value,(e=>(u(),_(M,{key:e.id,label:e.name,value:e.name},{default:d((()=>[i("div",me,[i("span",fe,x(e.name),1),i("span",ge,x(e.specification||"无规格"),1),i("span",ye,x(e.brand||"无品牌"),1)])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue","loading"]),i("div",we,[n(qa,{type:"info",size:"small"},{default:d((()=>a[52]||(a[52]=[f(" 线下设备模式：从备件台账中选择设备名称，适用于电池、线缆等无固定资产编号的设备 ")]))),_:1})])])),_:1})):V("",!0),n(y,{label:"故障标题",prop:"title"},{default:d((()=>[n(s,{modelValue:O.value.title,"onUpdate:modelValue":a[10]||(a[10]=e=>O.value.title=e),placeholder:"请输入故障标题",maxlength:100,"show-word-limit":"",clearable:""},null,8,["modelValue"])])),_:1}),n(y,{label:"故障类型",prop:"faultType"},{default:d((()=>[i("div",he,[n(K,{modelValue:O.value.faultType,"onUpdate:modelValue":a[11]||(a[11]=e=>O.value.faultType=e),placeholder:"请选择故障类型",style:{width:"calc(100% - 80px)"},clearable:"",filterable:""},{default:d((()=>[(u(),o(P,null,k(j,(e=>n(M,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"]),n(l,{icon:c(N),onClick:pa,style:{"margin-left":"8px"}},{default:d((()=>a[53]||(a[53]=[f("扫码")]))),_:1},8,["icon"])]),i("div",be,[n(qa,{type:"info",size:"small"},{default:d((()=>a[54]||(a[54]=[f(" 支持扫描故障类型二维码自动选择，默认为硬件故障 ")]))),_:1})])])),_:1}),n(y,{label:"优先级",prop:"priority"},{default:d((()=>[n(K,{modelValue:O.value.priority,"onUpdate:modelValue":a[12]||(a[12]=e=>O.value.priority=e),placeholder:"请选择优先级",style:{width:"100%"},clearable:""},{default:d((()=>[(u(),o(P,null,k(G,(e=>n(M,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),n(y,{label:"发生时间",prop:"happenTime"},{default:d((()=>[n(Y,{modelValue:O.value.happenTime,"onUpdate:modelValue":a[13]||(a[13]=e=>O.value.happenTime=e),type:"datetime-local",placeholder:"请选择故障发生时间",style:{width:"100%"},"value-format":"YYYY-MM-DDTHH:mm"},null,8,["modelValue"])])),_:1}),n(y,{label:"故障描述",prop:"description"},{default:d((()=>[n(s,{modelValue:O.value.description,"onUpdate:modelValue":a[14]||(a[14]=e=>O.value.description=e),type:"textarea",rows:4,placeholder:"请详细描述故障现象、影响范围等信息",maxlength:1e3,"show-word-limit":"",resize:"none"},null,8,["modelValue"])])),_:1}),n(y,{label:"备件管理"},{default:d((()=>[n(Ba,{modelValue:O.value.autoGenerateSparePartRecord,"onUpdate:modelValue":a[15]||(a[15]=e=>O.value.autoGenerateSparePartRecord=e)},{default:d((()=>a[55]||(a[55]=[f(" 自动生成备件入库记录 ")]))),_:1},8,["modelValue"]),i("div",_e,[n(qa,{type:"info",size:"small"},{default:d((()=>a[56]||(a[56]=[f(" 勾选后将自动创建一条备件入库记录，适用于电池等消耗性故障 ")]))),_:1})])])),_:1}),O.value.autoGenerateSparePartRecord?(u(),o("div",Ve,[n(Oa,{"content-position":"left"},{default:d((()=>a[57]||(a[57]=[f("备件信息")]))),_:1}),n(Ha,{gutter:16},{default:d((()=>[n(Ea,{span:12},{default:d((()=>[n(y,{label:"备件名称",prop:"sparePartName"},{default:d((()=>[n(s,{modelValue:O.value.sparePartName,"onUpdate:modelValue":a[16]||(a[16]=e=>O.value.sparePartName=e),placeholder:"请输入备件名称",maxlength:50,clearable:""},null,8,["modelValue"])])),_:1})])),_:1}),n(Ea,{span:12},{default:d((()=>[n(y,{label:"规格型号"},{default:d((()=>[n(s,{modelValue:O.value.sparePartSpecification,"onUpdate:modelValue":a[17]||(a[17]=e=>O.value.sparePartSpecification=e),placeholder:"请输入规格型号",maxlength:100,clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(Ha,{gutter:16},{default:d((()=>[n(Ea,{span:8},{default:d((()=>[n(y,{label:"品牌"},{default:d((()=>[n(s,{modelValue:O.value.sparePartBrand,"onUpdate:modelValue":a[18]||(a[18]=e=>O.value.sparePartBrand=e),placeholder:"请输入品牌",maxlength:30,clearable:""},null,8,["modelValue"])])),_:1})])),_:1}),n(Ea,{span:8},{default:d((()=>[n(y,{label:"数量"},{default:d((()=>[n(La,{modelValue:O.value.sparePartQuantity,"onUpdate:modelValue":a[19]||(a[19]=e=>O.value.sparePartQuantity=e),min:1,max:999},null,8,["modelValue"])])),_:1})])),_:1}),n(Ea,{span:8},{default:d((()=>[n(y,{label:"单价"},{default:d((()=>[n(La,{modelValue:O.value.sparePartPrice,"onUpdate:modelValue":a[20]||(a[20]=e=>O.value.sparePartPrice=e),precision:2,min:0},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])):V("",!0),n(y,{label:"附件"},{default:d((()=>[n(Wa,{action:"#","list-type":"picture-card","auto-upload":!1,limit:5,"on-change":ga,"on-remove":ya,"file-list":Ge.value},{default:d((()=>[n(Aa,null,{default:d((()=>[n(c(w))])),_:1})])),_:1},8,["file-list"]),a[58]||(a[58]=i("div",{class:"upload-tip text-secondary"}," 支持jpg、png、pdf格式，最多5个文件，每个不超过10MB ",-1))])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),n(Ja,{title:"选择资产",modelValue:Qe.value,"onUpdate:modelValue":a[27]||(a[27]=e=>Qe.value=e),width:"800px","append-to-body":""},{footer:d((()=>[i("div",Se,[n(l,{onClick:a[26]||(a[26]=e=>Qe.value=!1)},{default:d((()=>a[61]||(a[61]=[f("取 消")]))),_:1})])])),default:d((()=>[i("div",ke,[n(s,{modelValue:qe.value,"onUpdate:modelValue":a[23]||(a[23]=e=>qe.value=e),placeholder:"请输入资产名称/编号/SN搜索",onKeyup:F(c(ta),["enter"])},{append:d((()=>[n(l,{icon:c(T),onClick:c(ta)},null,8,["icon","onClick"])])),_:1},8,["modelValue","onKeyup"])]),C((u(),_(Ka,{ref:"assetTable",data:Oe.value,style:{width:"100%"},height:"400px",border:"",onRowClick:sa},{default:d((()=>[n(ea,{prop:"code",label:"资产编号",width:"120"}),n(ea,{prop:"name",label:"资产名称","min-width":"150"}),n(ea,{prop:"type",label:"资产类型",width:"120"},{default:d((e=>[e.row?(u(),_(Ma,{key:0,size:"small"},{default:d((()=>[f(x(e.row.typeName||"未知"),1)])),_:2},1024)):V("",!0)])),_:1}),n(ea,{prop:"sn",label:"序列号",width:"150"}),n(ea,{prop:"status",label:"状态",width:"100"},{default:d((e=>{return[e.row?(u(),_(Ma,{key:0,size:"small",type:(a=e.row.status,{in_use:"success",idle:"info",repairing:"danger",borrowed:"warning"}[a]||"")},{default:d((()=>[f(x(e.row.statusText||"未知"),1)])),_:2},1032,["type"])):V("",!0)];var a})),_:1}),n(ea,{prop:"department",label:"所属部门",width:"120"}),n(ea,{prop:"location",label:"位置",width:"150"})])),_:1},8,["data"])),[[Xa,Be.value]]),i("div",Te,[n(Da,{"current-page":Ee.currentPage,"onUpdate:currentPage":a[24]||(a[24]=e=>Ee.currentPage=e),"page-size":Ee.pageSize,"onUpdate:pageSize":a[25]||(a[25]=e=>Ee.pageSize=e),"page-sizes":[10,20,50],background:!0,layout:"total, sizes, prev, pager, next",total:Ee.total,onSizeChange:ma,onCurrentChange:fa},null,8,["current-page","page-size","total"])])])),_:1},8,["modelValue"]),n(Ja,{modelValue:Pa.value,"onUpdate:modelValue":a[29]||(a[29]=e=>Pa.value=e),title:"故障维修使用备件",width:"70%","close-on-click-modal":!1},{footer:d((()=>[i("span",Ie,[n(l,{onClick:a[28]||(a[28]=e=>Pa.value=!1)},{default:d((()=>a[68]||(a[68]=[f("取消")]))),_:1}),n(l,{type:"primary",onClick:Ua},{default:d((()=>a[69]||(a[69]=[f("确认使用")]))),_:1})])])),default:d((()=>{var e,t,r,p;return[i("div",Ce,[i("div",xe,[a[65]||(a[65]=i("h4",null,"故障信息",-1)),i("p",null,[a[62]||(a[62]=i("strong",null,"故障编号：",-1)),f(x(null==(e=ka.value)?void 0:e.code),1)]),i("p",null,[a[63]||(a[63]=i("strong",null,"故障标题：",-1)),f(x(null==(t=ka.value)?void 0:t.title),1)]),i("p",null,[a[64]||(a[64]=i("strong",null,"故障资产：",-1)),f(x(null==(r=ka.value)?void 0:r.assetName)+" ("+x(null==(p=ka.value)?void 0:p.assetCode)+")",1)])]),i("div",ze,[i("div",$e,[a[67]||(a[67]=i("h4",null,"备件使用",-1)),n(l,{type:"primary",size:"small",onClick:$a},{default:d((()=>a[66]||(a[66]=[f("添加备件")]))),_:1})]),n(Ka,{data:Ta.spareParts,border:"",style:{width:"100%"}},{default:d((()=>[n(ea,{label:"备件",width:"200"},{default:d((e=>[n(K,{modelValue:e.row.sparePartId,"onUpdate:modelValue":a=>e.row.sparePartId=a,placeholder:"选择备件",filterable:"",onChange:a=>((e,a)=>{const l=Sa.value.find((a=>a.id===e));l&&(Ta.spareParts[a].sparePartName=l.name,Ta.spareParts[a].maxQuantity=l.stockQuantity||0)})(a,e.$index),loading:Ye.value},{default:d((()=>[(u(!0),o(P,null,k(Sa.value,(e=>(u(),_(M,{key:e.id,label:`${e.name} (库存: ${e.stockQuantity||0})`,value:e.id,disabled:(e.stockQuantity||0)<=0},null,8,["label","value","disabled"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue","onChange","loading"])])),_:1}),n(ea,{label:"库存",width:"80",align:"center"},{default:d((e=>[e.row?(u(),o("span",Ue,x(Fa(e.row.sparePartId)),1)):V("",!0)])),_:1}),n(ea,{label:"使用数量",width:"120",align:"center"},{default:d((e=>[e.row?(u(),_(La,{key:0,modelValue:e.row.quantity,"onUpdate:modelValue":a=>e.row.quantity=a,min:1,max:Fa(e.row.sparePartId),size:"small"},null,8,["modelValue","onUpdate:modelValue","max"])):V("",!0)])),_:1}),n(ea,{label:"备注","min-width":"150"},{default:d((e=>[e.row?(u(),_(s,{key:0,modelValue:e.row.notes,"onUpdate:modelValue":a=>e.row.notes=a,placeholder:"备注信息",size:"small"},null,8,["modelValue","onUpdate:modelValue"])):V("",!0)])),_:1}),n(ea,{label:"操作",width:"80",align:"center"},{default:d((e=>[e.row?(u(),_(l,{key:0,type:"danger",text:"",size:"small",onClick:a=>{return l=e.$index,void Ta.spareParts.splice(l,1);var l},icon:c(R)},null,8,["onClick","icon"])):V("",!0)])),_:1})])),_:1},8,["data"])])])]})),_:1},8,["modelValue"]),n(Ja,{modelValue:Ia.value,"onUpdate:modelValue":a[35]||(a[35]=e=>Ia.value=e),title:"创建返厂记录",width:"60%","close-on-click-modal":!1},{footer:d((()=>[i("span",Fe,[n(l,{onClick:a[34]||(a[34]=e=>Ia.value=!1)},{default:d((()=>a[74]||(a[74]=[f("取消")]))),_:1}),n(l,{type:"primary",onClick:Ra},{default:d((()=>a[75]||(a[75]=[f("确认返厂")]))),_:1})])])),default:d((()=>{var e,l,t,r;return[i("div",Ne,[i("div",Re,[a[73]||(a[73]=i("h4",null,"故障信息",-1)),i("p",null,[a[70]||(a[70]=i("strong",null,"故障编号：",-1)),f(x(null==(e=ka.value)?void 0:e.code),1)]),i("p",null,[a[71]||(a[71]=i("strong",null,"故障标题：",-1)),f(x(null==(l=ka.value)?void 0:l.title),1)]),i("p",null,[a[72]||(a[72]=i("strong",null,"故障资产：",-1)),f(x(null==(t=ka.value)?void 0:t.assetName)+" ("+x(null==(r=ka.value)?void 0:r.assetCode)+")",1)])]),n(Ke,{model:Na,"label-width":"100px"},{default:d((()=>[n(y,{label:"供应商",required:""},{default:d((()=>[n(K,{modelValue:Na.supplierId,"onUpdate:modelValue":a[30]||(a[30]=e=>Na.supplierId=e),placeholder:"请选择供应商",style:{width:"100%"}},{default:d((()=>[n(M,{label:"联想",value:"1"}),n(M,{label:"惠普",value:"2"}),n(M,{label:"戴尔",value:"3"}),n(M,{label:"华为",value:"4"})])),_:1},8,["modelValue"])])),_:1}),n(y,{label:"返厂原因",required:""},{default:d((()=>[n(s,{modelValue:Na.reason,"onUpdate:modelValue":a[31]||(a[31]=e=>Na.reason=e),type:"textarea",rows:3,placeholder:"请详细描述返厂原因"},null,8,["modelValue"])])),_:1}),n(y,{label:"预计返回时间"},{default:d((()=>[n(Y,{modelValue:Na.estimatedReturnTime,"onUpdate:modelValue":a[32]||(a[32]=e=>Na.estimatedReturnTime=e),type:"date",placeholder:"选择预计返回时间",style:{width:"100%"},"value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),n(y,{label:"备注"},{default:d((()=>[n(s,{modelValue:Na.notes,"onUpdate:modelValue":a[33]||(a[33]=e=>Na.notes=e),type:"textarea",rows:2,placeholder:"其他备注信息"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])]})),_:1},8,["modelValue"]),n(E,{modelValue:Ca.value,"onUpdate:modelValue":a[36]||(a[36]=e=>Ca.value=e),"scan-type":xa.value,onScanSuccess:ca,onManualInput:va},null,8,["modelValue","scan-type"])])}}},[["__scopeId","data-v-55273856"]]);export{Me as default};
