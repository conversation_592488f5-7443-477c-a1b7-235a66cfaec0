import{_ as t,k as e,r as n,c as a,m as r,a8 as o,b as i,d as u,e as l,w as s,F as c,h as d,A as f,t as m,a as h,o as g,f as w,v,Y as b,am as y,i as p,a9 as k,B as x,b4 as M,as as _,ao as C,an as D,b1 as H,G as Y,a6 as P}from"./index-C7OOw0MO.js";import{s as T,t as S,c as q,n as N,g as E,b as L,d as z,e as O,f as Q}from"./en-US-B4gbL6zc.js";function F(t,e){return T(t,{...e,weekStartsOn:1})}function G(t,e){const n=S(t,null==e?void 0:e.in),a=n.getFullYear(),r=q(n,0);r.setFullYear(a+1,0,4),r.setHours(0,0,0,0);const o=F(r),i=q(n,0);i.setFullYear(a,0,4),i.setHours(0,0,0,0);const u=F(i);return n.getTime()>=o.getTime()?a+1:n.getTime()>=u.getTime()?a:a-1}function V(t,e){const n=S(t,null==e?void 0:e.in);return n.setHours(0,0,0,0),n}function B(t){return!(!((e=t)instanceof Date||"object"==typeof e&&"[object Date]"===Object.prototype.toString.call(e))&&"number"!=typeof t||isNaN(+S(t)));var e}function I(t,e){const n=S(t,null==e?void 0:e.in),a=function(t,e,n){const[a,r]=N(null==n?void 0:n.in,t,e),o=V(a),i=V(r),u=+o-E(o),l=+i-E(i);return Math.round((u-l)/L)}(n,function(t,e){const n=S(t,null==e?void 0:e.in);return n.setFullYear(n.getFullYear(),0,1),n.setHours(0,0,0,0),n}(n));return a+1}function W(t,e){const n=S(t,null==e?void 0:e.in),a=+F(n)-+function(t,e){const n=G(t,e),a=q(t,0);return a.setFullYear(n,0,4),a.setHours(0,0,0,0),F(a)}(n);return Math.round(a/z)+1}function $(t,e){var n,a,r,o;const i=S(t,null==e?void 0:e.in),u=i.getFullYear(),l=O(),s=(null==e?void 0:e.firstWeekContainsDate)??(null==(a=null==(n=null==e?void 0:e.locale)?void 0:n.options)?void 0:a.firstWeekContainsDate)??l.firstWeekContainsDate??(null==(o=null==(r=l.locale)?void 0:r.options)?void 0:o.firstWeekContainsDate)??1,c=q((null==e?void 0:e.in)||t,0);c.setFullYear(u+1,0,s),c.setHours(0,0,0,0);const d=T(c,e),f=q((null==e?void 0:e.in)||t,0);f.setFullYear(u,0,s),f.setHours(0,0,0,0);const m=T(f,e);return+i>=+d?u+1:+i>=+m?u:u-1}function U(t,e){const n=S(t,null==e?void 0:e.in),a=+T(n,e)-+function(t,e){var n,a,r,o;const i=O(),u=(null==e?void 0:e.firstWeekContainsDate)??(null==(a=null==(n=null==e?void 0:e.locale)?void 0:n.options)?void 0:a.firstWeekContainsDate)??i.firstWeekContainsDate??(null==(o=null==(r=i.locale)?void 0:r.options)?void 0:o.firstWeekContainsDate)??1,l=$(t,e),s=q((null==e?void 0:e.in)||t,0);return s.setFullYear(l,0,u),s.setHours(0,0,0,0),T(s,e)}(n,e);return Math.round(a/z)+1}function X(t,e){return(t<0?"-":"")+Math.abs(t).toString().padStart(e,"0")}const j={y(t,e){const n=t.getFullYear(),a=n>0?n:1-n;return X("yy"===e?a%100:a,e.length)},M(t,e){const n=t.getMonth();return"M"===e?String(n+1):X(n+1,2)},d:(t,e)=>X(t.getDate(),e.length),a(t,e){const n=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];default:return"am"===n?"a.m.":"p.m."}},h:(t,e)=>X(t.getHours()%12||12,e.length),H:(t,e)=>X(t.getHours(),e.length),m:(t,e)=>X(t.getMinutes(),e.length),s:(t,e)=>X(t.getSeconds(),e.length),S(t,e){const n=e.length,a=t.getMilliseconds();return X(Math.trunc(a*Math.pow(10,n-3)),e.length)}},A="midnight",R="noon",K="morning",Z="afternoon",J="evening",tt="night",et={G:function(t,e,n){const a=t.getFullYear()>0?1:0;switch(e){case"G":case"GG":case"GGG":return n.era(a,{width:"abbreviated"});case"GGGGG":return n.era(a,{width:"narrow"});default:return n.era(a,{width:"wide"})}},y:function(t,e,n){if("yo"===e){const e=t.getFullYear(),a=e>0?e:1-e;return n.ordinalNumber(a,{unit:"year"})}return j.y(t,e)},Y:function(t,e,n,a){const r=$(t,a),o=r>0?r:1-r;if("YY"===e){return X(o%100,2)}return"Yo"===e?n.ordinalNumber(o,{unit:"year"}):X(o,e.length)},R:function(t,e){return X(G(t),e.length)},u:function(t,e){return X(t.getFullYear(),e.length)},Q:function(t,e,n){const a=Math.ceil((t.getMonth()+1)/3);switch(e){case"Q":return String(a);case"QQ":return X(a,2);case"Qo":return n.ordinalNumber(a,{unit:"quarter"});case"QQQ":return n.quarter(a,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(a,{width:"narrow",context:"formatting"});default:return n.quarter(a,{width:"wide",context:"formatting"})}},q:function(t,e,n){const a=Math.ceil((t.getMonth()+1)/3);switch(e){case"q":return String(a);case"qq":return X(a,2);case"qo":return n.ordinalNumber(a,{unit:"quarter"});case"qqq":return n.quarter(a,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(a,{width:"narrow",context:"standalone"});default:return n.quarter(a,{width:"wide",context:"standalone"})}},M:function(t,e,n){const a=t.getMonth();switch(e){case"M":case"MM":return j.M(t,e);case"Mo":return n.ordinalNumber(a+1,{unit:"month"});case"MMM":return n.month(a,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(a,{width:"narrow",context:"formatting"});default:return n.month(a,{width:"wide",context:"formatting"})}},L:function(t,e,n){const a=t.getMonth();switch(e){case"L":return String(a+1);case"LL":return X(a+1,2);case"Lo":return n.ordinalNumber(a+1,{unit:"month"});case"LLL":return n.month(a,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(a,{width:"narrow",context:"standalone"});default:return n.month(a,{width:"wide",context:"standalone"})}},w:function(t,e,n,a){const r=U(t,a);return"wo"===e?n.ordinalNumber(r,{unit:"week"}):X(r,e.length)},I:function(t,e,n){const a=W(t);return"Io"===e?n.ordinalNumber(a,{unit:"week"}):X(a,e.length)},d:function(t,e,n){return"do"===e?n.ordinalNumber(t.getDate(),{unit:"date"}):j.d(t,e)},D:function(t,e,n){const a=I(t);return"Do"===e?n.ordinalNumber(a,{unit:"dayOfYear"}):X(a,e.length)},E:function(t,e,n){const a=t.getDay();switch(e){case"E":case"EE":case"EEE":return n.day(a,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(a,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(a,{width:"short",context:"formatting"});default:return n.day(a,{width:"wide",context:"formatting"})}},e:function(t,e,n,a){const r=t.getDay(),o=(r-a.weekStartsOn+8)%7||7;switch(e){case"e":return String(o);case"ee":return X(o,2);case"eo":return n.ordinalNumber(o,{unit:"day"});case"eee":return n.day(r,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(r,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},c:function(t,e,n,a){const r=t.getDay(),o=(r-a.weekStartsOn+8)%7||7;switch(e){case"c":return String(o);case"cc":return X(o,e.length);case"co":return n.ordinalNumber(o,{unit:"day"});case"ccc":return n.day(r,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(r,{width:"narrow",context:"standalone"});case"cccccc":return n.day(r,{width:"short",context:"standalone"});default:return n.day(r,{width:"wide",context:"standalone"})}},i:function(t,e,n){const a=t.getDay(),r=0===a?7:a;switch(e){case"i":return String(r);case"ii":return X(r,e.length);case"io":return n.ordinalNumber(r,{unit:"day"});case"iii":return n.day(a,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(a,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(a,{width:"short",context:"formatting"});default:return n.day(a,{width:"wide",context:"formatting"})}},a:function(t,e,n){const a=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(a,{width:"narrow",context:"formatting"});default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(t,e,n){const a=t.getHours();let r;switch(r=12===a?R:0===a?A:a/12>=1?"pm":"am",e){case"b":case"bb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},B:function(t,e,n){const a=t.getHours();let r;switch(r=a>=17?J:a>=12?Z:a>=4?K:tt,e){case"B":case"BB":case"BBB":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},h:function(t,e,n){if("ho"===e){let e=t.getHours()%12;return 0===e&&(e=12),n.ordinalNumber(e,{unit:"hour"})}return j.h(t,e)},H:function(t,e,n){return"Ho"===e?n.ordinalNumber(t.getHours(),{unit:"hour"}):j.H(t,e)},K:function(t,e,n){const a=t.getHours()%12;return"Ko"===e?n.ordinalNumber(a,{unit:"hour"}):X(a,e.length)},k:function(t,e,n){let a=t.getHours();return 0===a&&(a=24),"ko"===e?n.ordinalNumber(a,{unit:"hour"}):X(a,e.length)},m:function(t,e,n){return"mo"===e?n.ordinalNumber(t.getMinutes(),{unit:"minute"}):j.m(t,e)},s:function(t,e,n){return"so"===e?n.ordinalNumber(t.getSeconds(),{unit:"second"}):j.s(t,e)},S:function(t,e){return j.S(t,e)},X:function(t,e,n){const a=t.getTimezoneOffset();if(0===a)return"Z";switch(e){case"X":return at(a);case"XXXX":case"XX":return rt(a);default:return rt(a,":")}},x:function(t,e,n){const a=t.getTimezoneOffset();switch(e){case"x":return at(a);case"xxxx":case"xx":return rt(a);default:return rt(a,":")}},O:function(t,e,n){const a=t.getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+nt(a,":");default:return"GMT"+rt(a,":")}},z:function(t,e,n){const a=t.getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+nt(a,":");default:return"GMT"+rt(a,":")}},t:function(t,e,n){return X(Math.trunc(+t/1e3),e.length)},T:function(t,e,n){return X(+t,e.length)}};function nt(t,e=""){const n=t>0?"-":"+",a=Math.abs(t),r=Math.trunc(a/60),o=a%60;return 0===o?n+String(r):n+String(r)+e+X(o,2)}function at(t,e){if(t%60==0){return(t>0?"-":"+")+X(Math.abs(t)/60,2)}return rt(t,e)}function rt(t,e=""){const n=t>0?"-":"+",a=Math.abs(t);return n+X(Math.trunc(a/60),2)+e+X(a%60,2)}const ot=(t,e)=>{switch(t){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});default:return e.date({width:"full"})}},it=(t,e)=>{switch(t){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});default:return e.time({width:"full"})}},ut={p:it,P:(t,e)=>{const n=t.match(/(P+)(p+)?/)||[],a=n[1],r=n[2];if(!r)return ot(t,e);let o;switch(a){case"P":o=e.dateTime({width:"short"});break;case"PP":o=e.dateTime({width:"medium"});break;case"PPP":o=e.dateTime({width:"long"});break;default:o=e.dateTime({width:"full"})}return o.replace("{{date}}",ot(a,e)).replace("{{time}}",it(r,e))}},lt=/^D+$/,st=/^Y+$/,ct=["D","DD","YY","YYYY"];const dt=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,ft=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,mt=/^'([^]*?)'?$/,ht=/''/g,gt=/[a-zA-Z]/;function wt(t,e,n){var a,r,o,i;const u=O(),l=u.locale??Q,s=u.firstWeekContainsDate??(null==(r=null==(a=u.locale)?void 0:a.options)?void 0:r.firstWeekContainsDate)??1,c=u.weekStartsOn??(null==(i=null==(o=u.locale)?void 0:o.options)?void 0:i.weekStartsOn)??0,d=S(t,null==n?void 0:n.in);if(!B(d))throw new RangeError("Invalid time value");let f=e.match(ft).map((t=>{const e=t[0];if("p"===e||"P"===e){return(0,ut[e])(t,l.formatLong)}return t})).join("").match(dt).map((t=>{if("''"===t)return{isToken:!1,value:"'"};const e=t[0];if("'"===e)return{isToken:!1,value:vt(t)};if(et[e])return{isToken:!0,value:t};if(e.match(gt))throw new RangeError("Format string contains an unescaped latin alphabet character `"+e+"`");return{isToken:!1,value:t}}));l.localize.preprocessor&&(f=l.localize.preprocessor(d,f));const m={firstWeekContainsDate:s,weekStartsOn:c,locale:l};return f.map((n=>{if(!n.isToken)return n.value;const a=n.value;(function(t){return st.test(t)}(a)||function(t){return lt.test(t)}(a))&&function(t,e,n){const a=function(t,e,n){const a="Y"===t[0]?"years":"days of the month";return`Use \`${t.toLowerCase()}\` instead of \`${t}\` (in \`${e}\`) for formatting ${a} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(t,e,n);if(ct.includes(t))throw new RangeError(a)}(a,e,String(t));return(0,et[a[0]])(d,a,l.localize,m)})).join("")}function vt(t){const e=t.match(mt);return e?e[1].replace(ht,"'"):t}const bt={class:"quick-memo-list-view"},yt={class:"memo-container"},pt={class:"memo-list-section"},kt={class:"list-header"},xt={class:"search-bar"},Mt={class:"memo-list"},_t=["onClick"],Ct={class:"memo-item-header"},Dt={class:"memo-title"},Ht={class:"memo-preview"},Yt={class:"memo-footer"},Pt={class:"memo-meta"},Tt={class:"meta-item"},St={class:"meta-item"},qt={class:"memo-actions"},Nt={key:0,class:"memo-edit-section"},Et={class:"edit-header"},Lt={class:"header-actions"},zt={class:"meta-info"},Ot={class:"meta-item"},Qt={class:"meta-item"},Ft={class:"edit-toolbar"},Gt={class:"edit-content"},Vt={class:"edit-footer"},Bt={class:"auto-save-hint"},It={key:1,class:"memo-empty"},Wt=t({__name:"QuickMemoListView",setup(t){const T=e(),S=n(!1),q=a((()=>T.quickMemos)),N=a((()=>T.categories)),E=n(""),L=n(""),z=n(null);let O=null;const Q=a((()=>{let t=[...q.value];if(E.value){const e=E.value.toLowerCase();t=t.filter((t=>{var n;return(null==(n=t.title)?void 0:n.toLowerCase().includes(e))||V(t.content).toLowerCase().includes(e)}))}return L.value&&(t=t.filter((t=>{var e;return(null==(e=t.category)?void 0:e.id)===L.value}))),t.sort(((t,e)=>new Date(e.updatedAt)-new Date(t.updatedAt)))}));r((()=>{F(),0===T.categories.length&&T.fetchCategories().catch((t=>{}))}));const F=async()=>{S.value=!0;try{await T.fetchQuickMemos()}catch(t){o.error("获取随手记列表失败")}finally{S.value=!1}},G=t=>t?wt(new Date(t),"yyyy-MM-dd HH:mm:ss"):"N/A",V=t=>t?t.replace(/<[^>]*>/g,""):"",B=()=>{},I=()=>{},W=async()=>{try{const t=`新建随手记 ${wt(new Date,"yyyy-MM-dd HH:mm:ss")}`,e=await T.createQuickMemo({title:t,content:"",categoryId:null});await F(),$(e)}catch(t){o.error("创建随手记失败")}},$=t=>{var e;z.value={...t,categoryId:null==(e=t.category)?void 0:e.id}},U=async()=>{var t;if(null==(t=z.value)?void 0:t.id)try{await T.editQuickMemo({id:z.value.id,title:z.value.title,content:z.value.content,categoryId:z.value.categoryId}),await F()}catch(e){o.error("保存失败")}},X=()=>{O&&clearTimeout(O),O=setTimeout(U,1e3)},j=()=>{O&&clearTimeout(O),O=setTimeout(U,1e3)},A=()=>{U()};return(t,e)=>{const n=h("el-icon"),a=h("el-input"),r=h("el-option"),S=h("el-select"),q=h("el-button"),O=h("el-tag"),U=h("el-popconfirm"),R=h("el-button-group"),K=h("el-divider"),Z=h("el-empty");return g(),i("div",bt,[u("div",yt,[u("div",pt,[u("div",kt,[u("div",xt,[l(a,{modelValue:E.value,"onUpdate:modelValue":e[0]||(e[0]=t=>E.value=t),placeholder:"搜索标题或内容",class:"search-input",clearable:"",onInput:B},{prefix:s((()=>[l(n,null,{default:s((()=>[l(w(v))])),_:1})])),_:1},8,["modelValue"]),l(S,{modelValue:L.value,"onUpdate:modelValue":e[1]||(e[1]=t=>L.value=t),placeholder:"分类筛选",clearable:"",onChange:I},{default:s((()=>[l(r,{label:"全部",value:""}),(g(!0),i(c,null,d(N.value,(t=>(g(),b(r,{key:t.id,label:t.name,value:t.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])]),l(q,{type:"primary",class:"create-btn",onClick:W},{default:s((()=>[l(n,null,{default:s((()=>[l(w(y))])),_:1}),e[7]||(e[7]=f(" 新建随手记 "))])),_:1})]),u("div",Mt,[(g(!0),i(c,null,d(Q.value,(t=>{var a,r;return g(),i("div",{key:t.id,class:p(["memo-item",{active:(null==(a=z.value)?void 0:a.id)===t.id}]),onClick:e=>$(t)},[u("div",Ct,[u("span",Dt,m(t.title||"无标题"),1),t.category?(g(),b(O,{key:0,color:(r=t.category,r&&r.color?r.color:"#409EFF"),effect:"dark",size:"small"},{default:s((()=>[f(m(t.category.name),1)])),_:2},1032,["color"])):k("",!0)]),u("div",Ht,m(V(t.content).slice(0,100)),1),u("div",Yt,[u("div",Pt,[u("span",Tt,[l(n,null,{default:s((()=>[l(w(x))])),_:1}),f(" "+m(t.userName),1)]),u("span",St,[l(n,null,{default:s((()=>[l(w(M))])),_:1}),f(" "+m(G(t.updatedAt)),1)])]),u("div",qt,[l(U,{title:"确定删除这条随手记吗？",onConfirm:e=>(async t=>{var e;try{await T.removeQuickMemo(t),o.success("随手记删除成功"),(null==(e=z.value)?void 0:e.id)===t&&(z.value=null),F()}catch(n){o.error("删除随手记失败")}})(t.id),"confirm-button-text":"确定","cancel-button-text":"取消",onClick:e[3]||(e[3]=_((()=>{}),["stop"]))},{reference:s((()=>[l(q,{size:"small",type:"danger",text:"",onClick:e[2]||(e[2]=_((()=>{}),["stop"]))},{default:s((()=>[l(n,null,{default:s((()=>[l(w(C))])),_:1})])),_:1})])),_:2},1032,["onConfirm"])])])],10,_t)})),128))])]),z.value?(g(),i("div",Nt,[u("div",Et,[l(a,{modelValue:z.value.title,"onUpdate:modelValue":e[4]||(e[4]=t=>z.value.title=t),placeholder:"请输入标题",class:"title-input",onChange:X},null,8,["modelValue"]),u("div",Lt,[l(S,{modelValue:z.value.categoryId,"onUpdate:modelValue":e[5]||(e[5]=t=>z.value.categoryId=t),placeholder:"选择分类",class:"category-select",onChange:A},{default:s((()=>[(g(!0),i(c,null,d(N.value,(t=>(g(),b(r,{key:t.id,label:t.name,value:t.id},{default:s((()=>[l(O,{color:t.color,effect:"dark",size:"small"},{default:s((()=>[f(m(t.name),1)])),_:2},1032,["color"])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"]),u("div",zt,[u("span",Ot,[l(n,null,{default:s((()=>[l(w(x))])),_:1}),f(" "+m(z.value.userName),1)]),u("span",Qt,[l(n,null,{default:s((()=>[l(w(M))])),_:1}),f(" "+m(G(z.value.updatedAt)),1)])])])]),u("div",Ft,[l(R,null,{default:s((()=>[l(q,{link:"",title:"标题1"},{default:s((()=>e[8]||(e[8]=[f("H1")]))),_:1}),l(q,{link:"",title:"标题2"},{default:s((()=>e[9]||(e[9]=[f("H2")]))),_:1}),l(q,{link:"",title:"标题3"},{default:s((()=>e[10]||(e[10]=[f("H3")]))),_:1})])),_:1}),l(K,{direction:"vertical"}),l(R,null,{default:s((()=>[l(q,{link:"",title:"编辑"},{default:s((()=>[l(n,null,{default:s((()=>[l(w(D))])),_:1})])),_:1}),l(q,{link:"",title:"文档"},{default:s((()=>[l(n,null,{default:s((()=>[l(w(H))])),_:1})])),_:1})])),_:1}),l(K,{direction:"vertical"}),l(R,null,{default:s((()=>[l(q,{link:"",title:"有序列表"},{default:s((()=>[l(n,null,{default:s((()=>[l(w(Y))])),_:1})])),_:1}),l(q,{link:"",title:"无序列表"},{default:s((()=>[l(n,null,{default:s((()=>[l(w(P))])),_:1})])),_:1})])),_:1})]),u("div",Gt,[l(a,{modelValue:z.value.content,"onUpdate:modelValue":e[6]||(e[6]=t=>z.value.content=t),type:"textarea",rows:20,placeholder:"开始编写...",class:"content-editor",onChange:j,resize:"none"},null,8,["modelValue"])]),u("div",Vt,[u("span",Bt,[l(n,null,{default:s((()=>[l(w(M))])),_:1}),e[11]||(e[11]=f(" 自动保存 "))])])])):(g(),i("div",It,[l(Z,{description:"选择一条随手记开始编辑"})]))])])}}},[["__scopeId","data-v-7fedb391"]]);export{Wt as default};
