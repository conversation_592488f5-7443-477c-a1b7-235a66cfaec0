import{_ as a,r as e,c as l,m as s,b as t,d as r,e as n,w as i,a8 as u,a as c,u as d,ag as o,o as v,af as m,Y as p,a9 as h,f,aS as b,A as g,t as y,U as _,aW as w,F as k,h as C,b8 as z,b9 as x}from"./index-C7OOw0MO.js";import{u as S}from"./gamification-CzHSNMa2.js";import{f as U,z as V}from"./zh-CN-iI_Mcmum.js";import"./en-US-B4gbL6zc.js";const E={class:"leaderboard-view page-container"},j={class:"card-header"},T={key:0,class:"top-users"},N={class:"top-user second-place"},A={class:"user-info"},L={class:"user-name"},P={class:"user-score"},R={class:"top-user first-place"},W={class:"crown"},D={class:"user-info"},F={class:"user-name"},I={class:"user-score"},Y={class:"top-user third-place"},Z={class:"user-info"},q={class:"user-name"},B={class:"user-score"},G={class:"user-cell"},H={style:{"font-weight":"600"}},J={class:"card-header"},K={key:0,class:"achievement-list thin-scrollbar"},M={class:"achievement-info"},O={class:"achievement-user"},Q={class:"achievement-title"},X={class:"achievement-time"},$={key:2,class:"card-footer"},aa={class:"card-header"},ea={key:0,class:"star-of-month"},la={class:"star-info"},sa={class:"star-stats"},ta={class:"stat-item"},ra={class:"stat-value"},na={class:"stat-item"},ia={class:"stat-value"},ua={class:"stat-item"},ca={class:"stat-value"},da="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",oa=a({__name:"LeaderboardView",setup(a){const oa=d(),va=S(),ma=e(!1),pa=e(!1),ha=e(!1),fa=e("month"),ba=l((()=>va.leaderboard)),ga=l((()=>va.achievements.slice(0,5))),ya=l((()=>va.achievements)),_a=e(null),wa=e(!1);async function ka(){ma.value=!0;try{await va.fetchLeaderboard(fa.value)}catch(a){u.error("获取排行榜数据失败")}finally{ma.value=!1}}const Ca=a=>a+4,za=a=>{if(!a)return"";try{const e=new Date(a);return isNaN(e.getTime())?"无效日期":U(e,{addSuffix:!0,locale:V})}catch(e){return"无效日期"}},xa=a=>{if(!a||"string"!=typeof a)return"?";const e=a.split(" ");let l=e[0].substring(0,1).toUpperCase();return e.length>1&&(l+=e[e.length-1].substring(0,1).toUpperCase()),l},Sa=a=>{a.target.style.display="none"};return s((()=>{ka(),async function(){pa.value=!0;try{await va.fetchAchievements()}catch(a){u.error("获取成就数据失败")}finally{pa.value=!1}}(),async function(){ha.value=!0;try{await new Promise((a=>setTimeout(a,400))),_a.value={id:"user1",name:"张三",avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",department:"研发部",taskCount:15,monthlyRankScore:890,achievementCount:3}}catch(a){u.error("获取本月之星数据失败"),_a.value=null}finally{ha.value=!1}}()})),(a,e)=>{const l=c("el-icon"),s=c("el-radio-button"),u=c("el-radio-group"),d=c("el-avatar"),S=c("el-empty"),U=c("el-table-column"),V=c("el-button"),va=c("el-table"),Ua=c("el-card"),Va=c("el-col"),Ea=c("el-row"),ja=c("el-dialog"),Ta=o("loading");return v(),t("div",E,[e[21]||(e[21]=r("h2",{class:"page-title"},"游戏化中心 - 排行榜与成就",-1)),n(Ea,{gutter:20},{default:i((()=>[n(Va,{xs:24,md:16},{default:i((()=>[m((v(),p(Ua,{shadow:"never",class:"leaderboard-card"},{header:i((()=>[r("div",j,[r("span",null,[n(l,null,{default:i((()=>[n(f(w))])),_:1}),e[4]||(e[4]=g(" 积分排行榜"))]),n(u,{modelValue:fa.value,"onUpdate:modelValue":e[0]||(e[0]=a=>fa.value=a),size:"small",onChange:ka},{default:i((()=>[n(s,{label:"week"},{default:i((()=>e[5]||(e[5]=[g("本周")]))),_:1}),n(s,{label:"month"},{default:i((()=>e[6]||(e[6]=[g("本月")]))),_:1}),n(s,{label:"year"},{default:i((()=>e[7]||(e[7]=[g("全年")]))),_:1}),n(s,{label:"all"},{default:i((()=>e[8]||(e[8]=[g("总计")]))),_:1})])),_:1},8,["modelValue"])])])),default:i((()=>[ba.value.length>=3?(v(),t("div",T,[r("div",N,[e[9]||(e[9]=r("div",{class:"rank-badge"},"2",-1)),n(d,{size:70,src:f(b)(ba.value[1].avatar)||da,class:"user-avatar",onError:Sa},{default:i((()=>[g(y(xa(ba.value[1].name)),1)])),_:1},8,["src"]),r("div",A,[r("div",L,y(ba.value[1].name),1),r("div",P,y(ba.value[1].rankScore||0)+" 积分",1)])]),r("div",R,[r("div",W,[n(l,null,{default:i((()=>[n(f(_))])),_:1})]),e[10]||(e[10]=r("div",{class:"rank-badge"},"1",-1)),n(d,{size:90,src:f(b)(ba.value[0].avatar)||da,class:"user-avatar",onError:Sa},{default:i((()=>[g(y(xa(ba.value[0].name)),1)])),_:1},8,["src"]),r("div",D,[r("div",F,y(ba.value[0].name),1),r("div",I,y(ba.value[0].rankScore||0)+" 积分",1)])]),r("div",Y,[e[11]||(e[11]=r("div",{class:"rank-badge"},"3",-1)),n(d,{size:70,src:f(b)(ba.value[2].avatar)||da,class:"user-avatar",onError:Sa},{default:i((()=>[g(y(xa(ba.value[2].name)),1)])),_:1},8,["src"]),r("div",Z,[r("div",q,y(ba.value[2].name),1),r("div",B,y(ba.value[2].rankScore||0)+" 积分",1)])])])):!ma.value&&ba.value.length<3?(v(),p(S,{key:1,description:"暂无足够数据生成Top 3排行"})):h("",!0),ba.value.length>3?(v(),p(va,{key:2,data:ba.value.slice(3),stripe:"",style:{width:"100%"},class:"leaderboard-table"},{default:i((()=>[n(U,{type:"index",index:Ca,width:"70",label:"排名",align:"center"}),n(U,{label:"用户","min-width":"150"},{default:i((({row:a})=>[r("div",G,[n(d,{size:30,src:f(b)(a.avatar)||da,class:"table-avatar",onError:Sa},{default:i((()=>[g(y(xa(a.name)),1)])),_:2},1032,["src"]),r("span",null,y(a.name),1)])])),_:1}),n(U,{prop:"department",label:"部门","min-width":"100"}),n(U,{prop:"rankScore",label:"积分",sortable:"","min-width":"100",align:"right"},{default:i((({row:a})=>[r("span",H,y(a.rankScore||0),1)])),_:1}),n(U,{prop:"completedTasks",label:"完成任务",sortable:"","min-width":"100",align:"center"}),n(U,{label:"操作",width:"100",align:"center"},{default:i((({row:a})=>[n(V,{link:"",type:"primary",size:"small",onClick:e=>{var l;(l=a.id)&&oa.push({name:"UserProfile",params:{id:l}}).catch((a=>{}))}},{default:i((()=>e[12]||(e[12]=[g("查看")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])):h("",!0),ma.value||0!==ba.value.length?h("",!0):(v(),p(S,{key:3,description:"暂无排行数据"}))])),_:1})),[[Ta,ma.value]])])),_:1}),n(Va,{xs:24,md:8},{default:i((()=>[m((v(),p(Ua,{shadow:"never",class:"achievements-card mb-4"},{header:i((()=>[r("div",J,[r("span",null,[n(l,null,{default:i((()=>[n(f(z))])),_:1}),e[13]||(e[13]=g(" 最新成就"))])])])),default:i((()=>[ga.value.length>0?(v(),t("div",K,[(v(!0),t(k,null,C(ga.value,((a,l)=>{var s,u;return v(),t("div",{key:l,class:"achievement-item"},[n(d,{size:36,src:f(b)(null==(s=a.user)?void 0:s.avatar)||da,class:"achievement-avatar",onError:Sa},{default:i((()=>{var e;return[g(y(xa(null==(e=a.user)?void 0:e.name)),1)]})),_:2},1032,["src"]),r("div",M,[r("span",O,y((null==(u=a.user)?void 0:u.name)||"未知用户"),1),r("span",Q,[e[14]||(e[14]=g("获得了成就: ")),r("strong",null,y(a.title),1)]),r("div",X,y(za(a.time)),1)])])})),128))])):(v(),p(S,{key:1,description:"暂无最新成就记录"})),ya.value.length>0?(v(),t("div",$,[n(V,{type:"primary",plain:"",size:"small",onClick:e[1]||(e[1]=a=>wa.value=!0)},{default:i((()=>e[15]||(e[15]=[g(" 查看所有成就 ")]))),_:1})])):h("",!0)])),_:1})),[[Ta,pa.value]]),m((v(),p(Ua,{shadow:"never",class:"star-card"},{header:i((()=>[r("div",aa,[r("span",null,[n(l,null,{default:i((()=>[n(f(x))])),_:1}),e[16]||(e[16]=g(" 本月之星"))])])])),default:i((()=>[_a.value?(v(),t("div",ea,[n(d,{size:80,src:f(b)(_a.value.avatar)||da,class:"star-avatar",onError:Sa},{default:i((()=>[g(y(xa(_a.value.name)),1)])),_:1},8,["src"]),r("div",la,[r("h3",null,y(_a.value.name),1),r("p",null,y(_a.value.department||"-"),1),r("div",sa,[r("div",ta,[r("span",ra,y(_a.value.taskCount||0),1),e[17]||(e[17]=r("span",{class:"stat-label"},"完成任务",-1))]),r("div",na,[r("span",ia,y(_a.value.monthlyRankScore||0),1),e[18]||(e[18]=r("span",{class:"stat-label"},"本月积分",-1))]),r("div",ua,[r("span",ca,y(_a.value.achievementCount||0),1),e[19]||(e[19]=r("span",{class:"stat-label"},"获得成就",-1))])])])])):(v(),p(S,{key:1,description:"暂无本月之星数据"}))])),_:1})),[[Ta,ha.value]])])),_:1})])),_:1}),n(ja,{title:"所有成就记录",modelValue:wa.value,"onUpdate:modelValue":e[3]||(e[3]=a=>wa.value=a),width:"clamp(500px, 60%, 800px)",top:"8vh","append-to-body":"",draggable:""},{footer:i((()=>[n(V,{onClick:e[2]||(e[2]=a=>wa.value=!1)},{default:i((()=>e[20]||(e[20]=[g("关闭")]))),_:1})])),default:i((()=>[m((v(),p(va,{data:ya.value,style:{width:"100%"},height:"450px"},{default:i((()=>[n(U,{prop:"user.name",label:"用户",width:"120"}),n(U,{prop:"title",label:"成就名称","min-width":"150"}),n(U,{prop:"time",label:"获得时间",width:"160"},{default:i((({row:a})=>[g(y(za(a.time)),1)])),_:1})])),_:1},8,["data"])),[[Ta,pa.value]])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-e62b32b2"]]);export{oa as default};
