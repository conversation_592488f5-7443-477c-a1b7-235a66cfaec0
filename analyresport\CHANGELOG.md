# 修改记录

<!-- 以下是修改记录条目 -->
| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Application/Features/Tasks/Services/TaskService.cs | 优化任务查询性能：添加MapTasksToDtosOptimizedAsync批量映射方法，避免N+1查询问题 | 已解决 |
| 修改 | Core/Abstractions/ITaskRepository.cs | 新增批量查询接口：GetSubTaskCountsBatchAsync、GetCommentCountsBatchAsync、GetAttachmentCountsBatchAsync、GetTasksByIdsAsync | 已解决 |
| 修改 | Infrastructure/Data/Repositories/TaskRepository.cs | 实现批量统计查询方法，使用GroupBy和Dictionary优化性能 | 已解决 |
| 创建 | 任务查询性能优化报告.md | 详细记录性能优化方案、预期效果和修改说明 | 已解决 |
| 创建 | Application/Features/Location/Dtos/LocationDepartmentInheritanceDto.cs | 创建位置部门继承功能相关DTO定义，包含位置部门继承信息、资产摘要和部门位置统计DTO | 已解决 |
| 创建 | Application/Features/Location/Services/ILocationDepartmentInheritanceService.cs | 创建位置部门继承服务接口，定义获取位置部门继承信息的方法 | 已解决 |
| 创建 | Application/Features/Location/Services/LocationDepartmentInheritanceService.cs | 创建位置部门继承服务实现，使用CTE递归查询处理部门继承逻辑 | 已解决 |
| 创建 | Api/V2/Controllers/LocationDepartmentInheritanceController.cs | 创建位置部门继承V2控制器，提供标准JSON响应格式的API接口 | 已解决 |
| 修改 | Startup.cs | 注册位置部门继承服务依赖注入配置 | 已解决 |
| 创建 | frontend/src/api/locationDepartmentInheritance.js | 创建前端API封装，提供位置部门继承相关接口调用方法 | 已解决 |
| 创建 | frontend/src/views/locations/components/DepartmentInheritancePanel.vue | 创建位置部门继承信息展示组件，可集成到现有位置管理页面 | 已解决 |
| 创建 | 使用示例和集成指南.md | 创建详细的API使用指南和前后端集成说明文档 | 已解决 |
| 创建 | Application/Features/Location/Dtos/LocationClassificationQueryDto.cs | 创建位置分类查询相关DTO，支持多维度筛选和分页排序 | 已解决 |
| 修改 | Application/Features/Location/Services/ILocationDepartmentInheritanceService.cs | 扩展接口，添加分类查询和统计分析方法 | 已解决 |
| 修改 | Application/Features/Location/Services/LocationDepartmentInheritanceService.cs | 实现基于各种分类的高级查询功能，支持部门、位置类型、资产类型等多维度筛选 | 已解决 |
| 修改 | Api/V2/Controllers/LocationDepartmentInheritanceController.cs | 扩展控制器，添加分类搜索和统计分析API接口 | 已解决 |
| 修改 | frontend/src/api/locationDepartmentInheritance.js | 扩展前端API，添加分类查询相关方法 | 已解决 |
| 创建 | frontend/src/views/locations/components/LocationClassificationSearch.vue | 创建位置分类高级搜索组件，支持多条件筛选和统计展示 | 已解决 |
| 创建 | 位置分类查询功能说明.md | 创建完整的分类查询功能使用说明和实战示例文档 | 已解决 |
| 创建 | docs/位置分类查询功能技术文档.md | 创建详细的技术文档，包含实现原理、架构设计、性能优化和故障排除 | 已解决 |
| 创建 | docs/api/位置分类查询API文档.md | 创建完整的API接口文档，包含请求响应示例、数据模型和使用指南 | 已解决 |
| 创建 | docs/位置分类查询功能README.md | 创建功能总览文档，整合所有相关文档和使用指南 | 已解决 |
| 创建 | frontend/src/views/dashboard/FactoryLayoutDashboard.vue | 创建数字化工厂看板主页面，基于React参考实现Vue 3版本 | 已解决 |
| 创建 | frontend/src/views/dashboard/components/FactoryLocation.vue | 创建工厂位置组件，支持状态显示、交互效果和响应式设计 | 已解决 |
| 创建 | frontend/src/views/dashboard/components/LocationDetailsModal.vue | 创建位置详情弹窗组件，展示监控数据、告警信息和设备列表 | 已解决 |
| 创建 | frontend/src/styles/factory-dashboard.scss | 创建工厂看板专用样式文件，提供Tailwind CSS备用样式 | 已解决 |
| 修改 | frontend/src/router/routes.js | 添加工厂数字化看板路由配置 | 已解决 |
| 创建 | frontend/src/views/dashboard/README.md | 创建数字化工厂看板使用说明文档 | 已解决 |
| 修改 | Domain/Entities/Tasks/Task.cs | 将IsOverdue属性中的DateTime.UtcNow改为DateTime.Now，使用本地时间判断任务是否过期 | 已解决 |
| 修改 | Application/Features/Tasks/Services/TaskService.cs | 将所有DateTime.UtcNow改为DateTime.Now，移除ToUniversalTime()调用，将TimeZoneInfo.Utc改为TimeZoneInfo.Local，确保任务相关时间字段使用本地时间而非UTC时间 | 已解决 |
| 修改 | Infrastructure/Services/PeriodicTaskGenerationService.cs | 将DateTime.UtcNow改为DateTime.Now，TimeZoneInfo.Utc改为TimeZoneInfo.Local，确保周期性任务生成使用本地时间 | 已解决 |
| 修改 | Core/Events/Tasks/AttachmentDeletedEvent.cs | 将DateTime.UtcNow改为DateTime.Now，确保附件删除事件时间戳使用本地时间 | 已解决 |
| 修改 | frontend/src/views/tasks/components/PeriodicTaskDialog.vue | 修复周期性任务创建的日期格式问题，将日期选择器的value-format改为不包含时区信息的格式，处理空endDate字段，确保后端能正确解析DateTime类型 | 已解决 |
| 修改 | Core/Services/NotificationService.cs | 修复通知服务字段映射问题，将ResourceType/ResourceId/CreatedAt改为ReferenceType/ReferenceId/CreationTimestamp，解决数据库写入问题 | 已解决 |
| 修改 | frontend/src/utils/notification-service.js | 重构前端通知服务代码，确保通知字段正确映射，修复实时通知功能 | 已解决 |
| 修改 | frontend/src/views/tasks/components/NotificationCenter.vue | 优化通知中心组件，正确处理任务通知的数据库字段名映射 | 已解决 |
| 修改 | Application/Features/Tasks/Services/TaskService.cs | 增强任务服务通知功能，为任务状态变更、完成、评论、附件和内容变更等操作添加通知写入和推送功能，确保所有操作都记录并推送给用户 | 已解决 |
| 修改 | Application/Features/Tasks/Services/TaskService.cs | 注入INotificationService并在任务相关方法中添加通知调用，解决任务分配通知发送问题 | 已解决 |
| 修改 | frontend/src/api/taskEnhanced.js | 修复API路径前缀，防止路由冲突 | 已解决 |
| 修改 | frontend/src/api/taskEnhanced.js | 修改API基础路径，将'/api/v2/tasks'改为'/api/v2/tasks-enhanced'，与后端控制器路由一致，解决请求匹配到多个端点的问题 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 修复Comment实体的时间字段列名映射，将CreationTimestamp属性的映射从"creation_timestamp"改为"CreationTimestamp"，LastUpdatedTimestamp从"last_updated_timestamp"改为"LastUpdatedTimestamp"，解决"Unknown column 'c.creation_timestamp' in 'field list'"错误，修复任务详情404问题 | 已解决 |
| 修改 | frontend/src/api/task.js | 修复API路径，将所有API路径中的`/api/v2/tasks`改为`/v2/tasks`，移除重复的`/api`前缀，因为request.js中已经设置baseURL为'/api'，修复了仪表盘任务统计数据404错误问题 | 已解决 |
| 修改 | Core/Services/ApiResponseFactory.cs | 添加一个接受IEnumerable<string>参数的CreateFail方法重载，用于处理多个错误消息的情况 | 已解决 |
| 修改 | Api/V2/TasksController.cs | 将所有ApiResponse<T>.CreateFail和ApiResponse<T>.CreateSuccess调用替换为ApiResponseFactory.CreateFail和ApiResponseFactory.CreateSuccess，并添加using ItAssetsSystem.Core.Services; | 已解决 |
| 修改 | Api/V2/QuickMemosController.cs | 将所有ApiResponse<T>.CreateFail和ApiResponse<T>.CreateSuccess调用替换为ApiResponseFactory.CreateFail和ApiResponseFactory.CreateSuccess，并添加using ItAssetsSystem.Core.Services; | 已解决 |
| 修改 | Api/V2/PeriodicTaskSchedulesController.cs | 将所有ApiResponse<T>.CreateFail和ApiResponse<T>.CreateSuccess调用替换为ApiResponseFactory.CreateFail和ApiResponseFactory.CreateSuccess，并添加using ItAssetsSystem.Core.Services; | 已解决 |
| 修改 | Api/V2/Controllers/ProfileController.cs | 将所有ApiResponse<T>.Error和ApiResponse<T>.Ok调用替换为ApiResponseFactory.CreateFail和ApiResponseFactory.CreateSuccess，并添加using ItAssetsSystem.Core.Services; | 已解决 |
| 创建 | Core/Services/ApiResponseFactory.cs | 创建API响应工厂类，提供CreateSuccess和CreateFail静态方法，用于生成标准API响应 | 已解决 |
| 创建 | Models/ApiResponse.cs | 创建API响应模型类，用于标准化API返回格式 | 已解决 |
| 修改 | Core/Abstractions/ICurrentUserService.cs | 在ICurrentUserService接口中添加GetCurrentUserId方法，用于获取当前用户ID | 已解决 |
| 修改 | Core/Services/CurrentUserService.cs | 在CurrentUserService实现类中添加GetCurrentUserId方法的实现，返回当前用户ID | 已解决 |
| 修改 | Application/Features/Tasks/Dtos/TaskQueryParametersDto.cs | 添加CreateStartDate和CreateEndDate属性，用于按创建时间范围筛选任务 | 已解决 |
| 修改 | Application/Features/Tasks/Dtos/TaskDto.cs | 添加CreatedAt和CompletedAt属性别名，提供与CreationTimestamp和ActualEndDate的兼容性 | 已解决 |
| 修改 | Api/V2/Controllers/TasksControllerEnhanced.cs | 修复Count方法调用错误，将tasks.Count改为tasks.Count()，并将所有ApiResponse<T>.CreateFail和ApiResponse<T>.CreateSuccess调用替换为ApiResponseFactory.CreateFail<T>和ApiResponseFactory.CreateSuccess<T> | 已解决 |
| 修改 | Application/Features/Tasks/Services/ITaskService.cs | 取消注释GetTaskPriorities、GetTaskTypes和GetTaskStatuses方法，以支持任务控制器中的选项获取功能 | 已解决 |
| 修改 | Api/V2/Controllers/TasksControllerEnhanced.cs | 修改增强版任务控制器的路由路径，从`api/v2/tasks`改为`api/v2/tasks-enhanced`，解决与原始TasksController的路由冲突，修复"The request matched multiple endpoints"错误 | 已解决 |
| 修改 | frontend/src/api/taskEnhanced.js | 更新前端API基础路径，从`/api/v2/tasks`改为`/api/v2/tasks-enhanced`，与后端控制器路由匹配 | 已解决 |
| 修改 | frontend/src/api/tasks.js | 添加getTaskDetails函数导出，作为taskApi.getTaskDetail的别名，解决TaskDetailsDialog.vue中导入错误"The requested module does not provide an export named 'getTaskDetails'" | 已解决 |
| 修改 | Infrastructure/Data/Repositories/TaskRepository.cs | 修复GetTaskByIdWithDetailsAsync方法，使用分步查询替代Include操作，解决"The expression 't.PeriodicTaskSchedule' is invalid inside an 'Include' operation"错误 | 已解决 |
| 创建 | frontend/src/views/tasks/components/BatchStatusDialog.vue | 创建批量状态更新对话框组件，支持同时更新多个任务的状态 | 已解决 |
| 创建 | frontend/src/views/tasks/components/TaskPreviewPopup.vue | 创建任务预览弹出组件，用于鼠标悬停时快速预览任务内容 | 已解决 |
| 创建 | frontend/src/views/tasks/components/NotificationCenter.vue | 创建通知中心组件，用于展示任务相关通知 | 已解决 |
| 修改 | frontend/src/views/tasks/ModernKanbanView.vue | 修复ModernKanbanView组件中对BatchStatusDialog、TaskPreviewPopup和NotificationCenter组件的属性绑定。将v-model改为v-model:visible，position属性拆分为top和left，并添加缺失的回调函数，解决组件渲染错误 | 已解决 |
| 修改 | frontend/src/api/task.js | 修复API路径问题，将所有'/v2/tasks'路径改为'/api/v2/tasks'，解决了前端通过baseURL='/api'后导致的实际请求URL变成'/api/v2/tasks'而找不到后端API的问题 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 修复Comment实体的CommentId属性列名映射，将"comment_id"改为"CommentId"，与数据库表结构一致，解决"Unknown column 'c.comment_id' in 'field list'"错误，修复TaskDetailsDialog.vue无法获取任务详情的问题 | 已解决 |
| 修改 | frontend/src/api/task.js | 增加导出 taskApi 对象，修复 BatchStatusDialog.vue 导入 taskApi 报错 | 已解决 |

## 2025年6月14日 - 修复备件新增时因缺少 is_active 数据库列导致的报错
## 2025年6月14日 - 修复备件新增时数据库列名映射错误

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Domain/Entities/SparePart.cs | 将 `SparePart` 实体中 `CreatorUserId` 属性的列名映射从 `creator_user_id` 修改为 `user_id`，以匹配 `spare_parts` 表的实际列名，解决新增备件时 "Unknown column 'creator_user_id' in 'field list'" 错误。 | 已解决 |

## 2025年6月13日 - 备品备件实体数据库列名映射修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Domain/Entities/SparePart.cs | 修复SparePart实体中的列名映射，将`specification`改为`spec`，`stock_quantity`改为`quantity`，`min_stock`改为`min_threshold`，`price`改为`purchase_price`，`remarks`改为`notes`，以匹配数据库表结构，解决"Unknown column 'creation_time' in 'field list'"错误 | 已解决 |
| 修改 | Domain/Entities/SparePartTransaction.cs | 修复SparePartTransaction实体中的列名映射，将`reference_number`改为`reference`，`operator_user_id`改为`user_id`，`operation_time`改为`transaction_time`，并添加缺失的`reason`、`related_asset_id`和`related_fault_id`字段，以匹配数据库表结构 | 已解决 |
| 修改 | Application/Features/SpareParts/Services/SparePartService.cs | 更新SparePartService中的代码，添加对新增字段的处理，确保出入库操作时正确设置`Reason`字段和关联资产/故障ID | 已解决 |
| 修改 | Application/Features/SpareParts/Dtos/SparePartOutboundRequest.cs | 更新SparePartOutboundRequest类，增加字段长度限制，添加`RelatedAssetId`和`RelatedFaultId`属性，支持关联资产和故障 | 已解决 |
| 修改 | Application/Features/SpareParts/Dtos/SparePartInboundRequest.cs | 更新SparePartInboundRequest类，增加关联单号字段长度限制，与数据库一致 | 已解决 |

## 2025年6月13日 - 备品备件实体列名映射修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Domain/Entities/SparePart.cs | 修复SparePart实体中的时间字段列名映射，将`creation_time`改为`created_at`，`last_update_time`改为`updated_at`，以匹配数据库表结构，解决"Unknown column 'creation_time' in 'field list'"错误 | 已解决 |
| 修改 | Infrastructure/Data/Repositories/SparePartRepository.cs | 更新SparePartRepository中的属性引用，将CreationTime改为CreatedAt，LastUpdateTime改为UpdatedAt，确保与实体属性名一致 | 已解决 |
| 修改 | Application/Features/SpareParts/Services/SparePartService.cs | 更新SparePartService中的属性引用，将CreationTime改为CreatedAt，LastUpdateTime改为UpdatedAt，确保与实体属性名一致 | 已解决 |
| 修改 | Application/Features/SpareParts/Dtos/SparePartDto.cs | 更新SparePartDto中的属性名称，将CreationTime改为CreatedAt，LastUpdateTime改为UpdatedAt，确保与实体属性名一致 | 已解决 |
| 修改 | Domain/Entities/SparePart.cs | 修复SparePart实体中的列名映射，将`specification`改为`spec`，`stock_quantity`改为`quantity`，`min_stock`改为`min_threshold`，`price`改为`purchase_price`，`remarks`改为`notes`，以匹配数据库表结构，解决"Unknown column 'creation_time' in 'field list'"错误 | 已解决 |
| 修改 | Domain/Entities/SparePartTransaction.cs | 修复SparePartTransaction实体中的列名映射，将`reference_number`改为`reference`，`operator_user_id`改为`user_id`，`operation_time`改为`transaction_time`，并添加缺失的`reason`、`related_asset_id`和`related_fault_id`字段，以匹配数据库表结构 | 已解决 |
| 修改 | Application/Features/SpareParts/Services/SparePartService.cs | 更新SparePartService中的代码，添加对新增字段的处理，确保出入库操作时正确设置`Reason`字段和关联资产/故障ID | 已解决 |
| 修改 | Application/Features/SpareParts/Dtos/SparePartOutboundRequest.cs | 更新SparePartOutboundRequest类，增加字段长度限制，添加`RelatedAssetId`和`RelatedFaultId`属性，支持关联资产和故障 | 已解决 |
| 修改 | Application/Features/SpareParts/Dtos/SparePartInboundRequest.cs | 更新SparePartInboundRequest类，增加关联单号字段长度限制，与数据库一致 | 已解决 |

## 2025年6月13日 - 备品备件模块API路径与控制器修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | frontend/src/views/spareparts/SparePartListView.vue | 完善备件台账管理页面，实现备件列表展示、新增、编辑、删除和出入库功能 | 已解决 |
| 修改 | frontend/src/api/spareparts.js | 确保API路径正确，使用/v2/前缀避免重复的/api前缀，与后端控制器路径匹配 | 已解决 |
| 修改 | frontend/src/stores/modules/spareparts.js | 优化store状态管理，确保类型和库位数据正确加载 | 已解决 |

## 2025年6月13日 - 备品备件模块Bug修复与API集成

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Application/Features/SpareParts/Services/SparePartTypeService.cs | 添加GetTypeTreeAsync()方法的实现，该方法按照树形结构返回类型列表，修复接口实现缺失问题 | 已解决 |
| 修改 | Application/Features/SpareParts/Services/SparePartTypeService.cs | 修复MapToDto方法中的字段映射，将CreationTime映射到CreatedAt，LastUpdateTime映射到UpdatedAt，并删除不存在的字段映射 | 已解决 |
| 修改 | Application/Features/SpareParts/Dtos/SparePartTransactionRequest.cs | 删除重复定义的SparePartInboundRequest和SparePartOutboundRequest类，只保留SparePartTransactionRequest基类 | 已解决 |
| 修改 | Application/Features/SpareParts/Dtos/SparePartInboundRequest.cs | 更新SparePartInboundRequest使其继承自SparePartTransactionRequest，并添加字段映射方法 | 已解决 |
| 修改 | Application/Features/SpareParts/Dtos/SparePartOutboundRequest.cs | 更新SparePartOutboundRequest使其继承自SparePartTransactionRequest，并添加字段映射方法 | 已解决 |
| 修改 | Api/V2/Controllers/SparePartController.cs | 在SparePartInbound和SparePartOutbound方法中调用请求的MapToBaseClass方法，确保正确映射属性 | 已解决 |
| 创建 | Core/Services/CurrentUserService.cs | 创建CurrentUserService实现类，实现ICurrentUserService接口，提供当前用户上下文信息 | 已解决 |
| 修改 | Startup.cs | 在ConfigureServices方法中添加CurrentUserService服务注册，解决依赖注入错误 | 已解决 |
| 修改 | Application/Features/SpareParts/Dtos/CreateSparePartLocationRequest.cs | 移除不存在的Capacity和MaxWeight字段，与数据库结构保持一致 | 已解决 |
| 修改 | frontend/src/api/spareparts.js | 替换模拟数据，使用实际的API调用，包括类型、库位、备件、出入库等相关接口 | 已解决 |

## 2025年6月7日 - Bug修复和前端优化

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Infrastructure/Data/AppDbContext.cs | 修复Task实体映射，确保与数据库表中的实际列名完全一致，解决了"Unknown column 't.task_id'"错误 | 已解决 |
| 修改 | frontend/src/api/spareparts.js | 修复API路径，移除重复的'/api'前缀，解决了前端请求404错误问题 | 已解决 |

## 2025年6月8日 - 数据库列名映射修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Infrastructure/Data/AppDbContext.cs | 修复Comment实体的TaskId属性映射，将task_id改为TaskId，与数据库表列名保持一致，解决了"Unknown column 'c.task_id' in 'where clause'"错误 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 修复Attachment实体的TaskId和CommentId属性映射，将task_id和comment_id改为TaskId和CommentId，与数据库表列名保持一致 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 修复TaskHistory实体的TaskId、CommentId和AttachmentId属性映射，统一使用PascalCase列名映射，解决数据库查询错误 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 修复TaskAssignee实体的TaskId属性映射，将task_id改为TaskId，保持命名一致性 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 修复PdcaPlan实体的TaskId属性映射，将task_id改为TaskId，保持命名一致性 | 已解决 |

## 2025年6月9日 - 前端API路径修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | frontend/src/api/spareparts.js | 修复前端API调用路径，移除重复的'/api'前缀，解决了请求URL变成'/api/api/v2/...'导致的404错误。修改所有API调用为'/v2/...'路径格式，让axios使用配置好的baseURL自动添加'/api'前缀。 | 已解决 |

## 2025年6月9日 - API路由修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Api/V2/Controllers/SparePartTypeController.cs | 修改备品备件类型控制器的路由，从`api/v2/spareparttypes`改为`api/v2/spareparttype`，使其与前端请求路径匹配 | 已解决 |
| 修改 | Api/V2/Controllers/SparePartTypeController.cs | 添加缺失的GetTypeTree方法，实现`/tree`路由端点，返回树形结构的类型列表 | 已解决 |
| 修改 | Api/V2/Controllers/SparePartTypesController.cs | 修改备品备件类型控制器的路由，从`api/v2/spare-part-types`改为`api/v2/spareparttype`，使其与前端请求路径匹配，并暂时注释授权要求 | 已解决 |
| 删除 | Api/V2/Controllers/SparePartTypeController.cs | 删除重复的备品备件类型控制器，避免路由冲突 | 已解决 |
| 创建 | Api/V2/Controllers/TestController.cs | 创建测试控制器，用于诊断SparePartTypeService问题，包含测试接口 | 已解决 |
| 修改 | frontend/src/api/spareparts.js | 修改前端API调用路径，从`/v2/spareparttype`改为`/api/v2/spare-part-types`，与后端控制器路由匹配 | 已解决 |

## 2025年6月10日 - 备品备件类型前端表单修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | frontend/src/views/spareparts/SparePartTypeView.vue | 添加类型编码(Code)输入框，修复前端表单提交因缺少必填字段导致的400错误 | 已解决 |

## 2025年6月10日 - 备品备件类型创建方法修复UpdatedAt字段

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Infrastructure/Data/Repositories/SparePartTypeRepository.cs | 修复CreateAsync方法，确保在创建新备品备件类型时设置UpdatedAt字段为当前时间，解决"Column 'updated_at' cannot be null"错误 | 已解决 |

## YYYY年MM月DD日 HH:MM:SS - 修复SparePartLocationRepository编译错误和AppDbContext警告

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Infrastructure/Data/Repositories/SparePartLocationRepository.cs | 将 `CreationTime` 更新为 `CreatedAt`，`LastUpdateTime` 更新为 `UpdatedAt`，以匹配实体属性更改，解决CS1061编译错误。同时在创建时也设置 `UpdatedAt`。 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 初始化 `SparePartTransactions` DbSet属性为 `null!`，以解决CS8618编译警告。 | 已解决 |

## YYYY年MM月DD日 HH:MM:SS - 修复编译警告CS0108

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Application/Features/SpareParts/Dtos/SparePartQuery.cs | 在 `SortBy` 属性前添加 `new` 关键字，以解决CS0108警告（隐藏继承成员）。 | 已解决 |
| 修改 | Application/Features/SpareParts/Dtos/SparePartQuery.cs | 在 `SortDirection` 属性前添加 `new` 关键字，以解决CS0108警告（隐藏继承成员）。 | 已解决 |
| 修改 | Application/Features/SpareParts/Dtos/SparePartTransactionQuery.cs | 在 `SortBy` 和 `SortDirection` 属性前添加 `new` 关键字，以解决CS0108警告（隐藏继承成员）。 | 已解决 |

## 2025年05月24日 17:08:28 - 修复QuickMemo实体属性名错误

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Infrastructure/Data/AppDbContext.cs | 将QuickMemo实体配置中的 `PositionX` 改为 `PositionXDb`，`PositionY` 改为 `PositionYDb`，以匹配实体定义中的 `[Column("PositionX")]` 和 `[Column("PositionY")]` 属性。 | 已解决 |

## 2025年05月24日 16:57:01 - 修复Task实体导航属性和PdcaPlan关系配置

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Infrastructure/Data/AppDbContext.cs | 1. 将Task实体的导航属性 `CreatorUser` 改为 `Creator`，`AssigneeUser` 改为 `Assignee`，`TaskHistories` 改为 `History`，`TaskAssignees` 改为 `Assignees`。 2. 修复了PdcaPlan与Task的关系配置，移除了对Task.PdcaPlan不存在的导航属性的引用。 | 已解决 |

## 2025年05月24日 16:36:38 - 添加ConfigureNotesModule的建议实现

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Infrastructure/Data/AppDbContext.cs | 根据 `analyresport/20250510后端更改建议.md` 中的建议，在 `AppDbContext` 中添加了 `ConfigureNotesModule` 方法的实现，用于配置 `Note` 和 `NoteCategory` 实体。 | 已解决 |

## 2025年05月23日 15:28:00 - 修复PeriodicTaskSchedule列名映射

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Domain/Entities/Tasks/PeriodicTaskSchedule.cs | 将 `CreatorUserId` 属性的列名从 `creator_user_id` 改为 `CreatorUserId`，以解决 "Unknown column 'p.CreatorUserId' in 'field list'" 错误。 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 确认 `PeriodicTaskSchedule` 实体在 `AppDbContext` 中的列名映射配置正确（如果存在）。当前实体直接使用属性名作为列名，无需额外配置。 | 已解决 |

## YYYY年MM月DD日 HH:MM:SS - 修复SparePartLocationService中的属性名错误

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Application/Features/SpareParts/Services/SparePartLocationService.cs | 将 `CreationTime` 更新为 `CreatedAt`，`LastUpdateTime` 更新为 `UpdatedAt`，以匹配实体属性更改，解决CS0117和CS1061编译错误。同时在创建时也设置 `UpdatedAt`。 | 已解决 |

## 2025年6月13日 - 备品备件实体列名映射修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Domain/Entities/SparePart.cs | 修复SparePart实体中的时间字段列名映射，将`creation_time`改为`created_at`，`last_update_time`改为`updated_at`，以匹配数据库表结构，解决"Unknown column 'creation_time' in 'field list'"错误 | 已解决 |
| 修改 | Infrastructure/Data/Repositories/SparePartRepository.cs | 更新SparePartRepository中的属性引用，将CreationTime改为CreatedAt，LastUpdateTime改为UpdatedAt，确保与实体属性名一致 | 已解决 |
| 修改 | Application/Features/SpareParts/Services/SparePartService.cs | 更新SparePartService中的属性引用，将CreationTime改为CreatedAt，LastUpdateTime改为UpdatedAt，确保与实体属性名一致 | 已解决 |
| 修改 | Application/Features/SpareParts/Dtos/SparePartDto.cs | 更新SparePartDto中的属性名称，将CreationTime改为CreatedAt，LastUpdateTime改为UpdatedAt，确保与实体属性名一致 | 已解决 |
| 修改 | Domain/Entities/SparePart.cs | 修复SparePart实体中的列名映射，将`specification`改为`spec`，`stock_quantity`改为`quantity`，`min_stock`改为`min_threshold`，`price`改为`purchase_price`，`remarks`改为`notes`，以匹配数据库表结构，解决"Unknown column 'creation_time' in 'field list'"错误 | 已解决 |
| 修改 | Domain/Entities/SparePartTransaction.cs | 修复SparePartTransaction实体中的列名映射，将`reference_number`改为`reference`，`operator_user_id`改为`user_id`，`operation_time`改为`transaction_time`，并添加缺失的`reason`、`related_asset_id`和`related_fault_id`字段，以匹配数据库表结构 | 已解决 |
| 修改 | Application/Features/SpareParts/Services/SparePartService.cs | 更新SparePartService中的代码，添加对新增字段的处理，确保出入库操作时正确设置`Reason`字段和关联资产/故障ID | 已解决 |
| 修改 | Application/Features/SpareParts/Dtos/SparePartOutboundRequest.cs | 更新SparePartOutboundRequest类，增加字段长度限制，添加`RelatedAssetId`和`RelatedFaultId`属性，支持关联资产和故障 | 已解决 |
| 修改 | Application/Features/SpareParts/Dtos/SparePartInboundRequest.cs | 更新SparePartInboundRequest类，增加关联单号字段长度限制，与数据库一致 | 已解决 |

## 2025年6月12日 - SparePartLocation实体列名映射与审计功能修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Domain/Entities/SparePartLocation.cs | 修改SparePartLocation实体中的CreationTime属性的列名映射，从"creation_time"改为"created_at"，与数据库实际列名匹配 | 已解决 |
| 修改 | Domain/Entities/SparePartLocation.cs | 修改SparePartLocation实体中的LastUpdateTime属性的列名映射，从"last_update_time"改为"updated_at"，与数据库实际列名匹配，并将类型从DateTime?改为DateTime | 已解决 |
| 修改 | Domain/Entities/SparePartLocation.cs | 将SparePartLocation实体中的CreatorUserId属性标记为[NotMapped]，因为数据库表中不存在该列 | 已解决 |
| 修改 | Domain/Entities/SparePartLocation.cs | 将SparePartLocation实体中的SortOrder属性标记为[NotMapped]，因为数据库表中不存在该列 | 已解决 |
| 修改 | Domain/Entities/SparePartLocation.cs | 使SparePartLocation实体实现IAuditableEntity接口，以便自动处理CreatedAt和UpdatedAt时间戳。添加了`using ItAssetsSystem.Models;`。将原`CreationTime`重命名为`CreatedAt`，`LastUpdateTime`重命名为`UpdatedAt`并修改类型为`DateTime`。 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 为SparePartLocation实体添加明确的列名映射配置，确保所有属性正确映射到数据库列名，解决"Unknown column 's.creation_time' in 'field list'"错误 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 从SparePartLocation实体的配置中移除CreatorUserId的列映射，修复"Unknown column 's.creator_user_id' in 'field list'"错误 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 从SparePartLocation实体的配置中移除SortOrder的列映射，修复"Unknown column 's.sort_order' in 'field list'"错误 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 更新ConfigureSparePartsModule中对SparePartLocation的配置，确保CreatedAt和UpdatedAt的属性名与实体中的修改一致，以修复"Column 'updated_at' cannot be null"错误。 | 已解决 |

## 2025年6月11日 - 优化备品备件管理功能结构

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | frontend/src/layouts/DefaultLayout.vue | 从侧边栏菜单中移除备件仓库管理菜单项，简化备品备件管理模块 | 已解决 |
| 修改 | frontend/src/router/routes.js | 从路由配置中删除备件仓库管理路由，优化路由结构 | 已解决 |
| 修改 | frontend/src/views/spareparts/SparePartLocationView.vue | 重构库位管理页面，实现表格展示和CRUD操作，取消仓库依赖，直接管理库位 | 已解决 |
| 修改 | frontend/src/api/spareparts.js | 更新库位管理API路径，使用正确的后端API格式"/v2/spare-part-locations" | 已解决 |
| 修改 | frontend/src/stores/modules/spareparts.js | 移除备件仓库相关代码，优化库位管理逻辑，不再需要根据仓库筛选库位 | 已解决 |
| 删除 | frontend/src/views/spareparts/SparePartWarehouseView.vue | 删除不再需要的备件仓库管理页面 | 已解决 |

## 2025年6月10日 - 备品备件类型API路由修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Api/V2/Controllers/SparePartTypesController.cs | 修改SparePartTypesController的路由前缀，从"v2/spare-part-types"改回"api/v2/spare-part-types"，使其与前端请求路径一致 | 已解决 |

## 2025年6月10日 - SparePartType实体移除不存在列的映射

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Domain/Entities/SparePartType.cs | 将SparePartType实体中的`CreatorUserId`、`IsActive`和`SortOrder`属性标记为`[NotMapped]`，因为这些列在数据库表中不存在 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 移除SparePartType实体配置中对不存在列的映射 | 已解决 |
| 修改 | Application/Features/SpareParts/Services/SparePartTypeService.cs | 修改CreateTypeAsync和UpdateTypeAsync方法，移除对不存在列的设置 | 已解决 |

## 2025年6月10日 - SparePartTypeService异常处理增强

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Application/Features/SpareParts/Services/SparePartTypeService.cs | 增强SparePartTypeService中的异常处理，在GetAllTypesAsync和GetTypeTreeAsync方法中添加try-catch，捕获异常并记录日志，返回空列表代替500错误，有助于诊断问题 | 部分解决 |

## 2025年6月10日 - SparePartType实体列名映射修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Domain/Entities/SparePartType.cs | 修改SparePartType实体中的Column特性，将`CreatedAt`和`UpdatedAt`属性上的列名从大写PascalCase改为小写下划线风格`created_at`和`updated_at`，与数据库表结构保持一致 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 修复SparePartType实体中的列名映射，将`CreatedAt`和`UpdatedAt`属性映射到数据库中实际的`created_at`和`updated_at`列，解决"Unknown column 's.CreatedAt' in 'field list'"错误 | 已解决 |

## 2025年6月9日 - 备品备件类型API路径修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Api/V2/Controllers/SparePartTypesController.cs | 修改备品备件类型控制器的路由，从`api/v2/spare-part-types`改为`v2/spare-part-types`，使其与前端API调用匹配（因为axios已配置baseURL为'/api'）| 部分解决 |

## 2025年6月9日 - 备品备件类型列名映射修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Domain/Entities/SparePartType.cs | 修改SparePartType实体中的时间属性名称，将`CreationTime`改为`CreatedAt`，`LastUpdateTime`改为`UpdatedAt`，以匹配数据库表的实际列名 | 已解决 |
| 修改 | Infrastructure/Data/Repositories/SparePartTypeRepository.cs | 修改SparePartTypeRepository中的时间属性引用，更新为使用新的属性名称 | 已解决 |
| 修改 | Application/Features/SpareParts/Services/SparePartTypeService.cs | 修改SparePartTypeService中的时间属性引用，确保与实体属性名称一致 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 更新SparePartType实体的Fluent API配置，确保属性名称与实体定义一致 | 已解决 |

## 2025年6月9日 - 前端API路径修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | frontend/src/api/spareparts.js | 修复前端API调用路径，移除重复的'/api'前缀，解决了请求URL变成'/api/api/v2/...'导致的404错误。修改所有API调用为'/v2/...'路径格式，让axios使用配置好的baseURL自动添加'/api'前缀。 | 已解决 |

## 2025年6月9日 - API路由修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Api/V2/Controllers/SparePartTypeController.cs | 修改备品备件类型控制器的路由，从`api/v2/spareparttypes`改为`api/v2/spareparttype`，使其与前端请求路径匹配 | 已解决 |
| 修改 | Api/V2/Controllers/SparePartTypeController.cs | 添加缺失的GetTypeTree方法，实现`/tree`路由端点，返回树形结构的类型列表 | 已解决 |
| 修改 | Api/V2/Controllers/SparePartTypesController.cs | 修改备品备件类型控制器的路由，从`api/v2/spare-part-types`改为`api/v2/spareparttype`，使其与前端请求路径匹配，并暂时注释授权要求 | 已解决 |
| 删除 | Api/V2/Controllers/SparePartTypeController.cs | 删除重复的备品备件类型控制器，避免路由冲突 | 已解决 |
| 创建 | Api/V2/Controllers/TestController.cs | 创建测试控制器，用于诊断SparePartTypeService问题，包含测试接口 | 已解决 |
| 修改 | frontend/src/api/spareparts.js | 修改前端API调用路径，从`/v2/spareparttype`改为`/api/v2/spare-part-types`，与后端控制器路由匹配 | 已解决 |

## 2025年6月8日 - 数据库列名映射修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Infrastructure/Data/AppDbContext.cs | 修复Comment实体的TaskId属性映射，将task_id改为TaskId，与数据库表列名保持一致，解决了"Unknown column 'c.task_id' in 'where clause'"错误 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 修复Attachment实体的TaskId和CommentId属性映射，将task_id和comment_id改为TaskId和CommentId，与数据库表列名保持一致 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 修复TaskHistory实体的TaskId、CommentId和AttachmentId属性映射，统一使用PascalCase列名映射，解决数据库查询错误 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 修复TaskAssignee实体的TaskId属性映射，将task_id改为TaskId，保持命名一致性 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 修复PdcaPlan实体的TaskId属性映射，将task_id改为TaskId，保持命名一致性 | 已解决 |

## 2025年6月2日 - 实体映射与导航属性错误修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Infrastructure/Data/AppDbContext.cs | 修复了Task实体导航属性映射，将CreatorUser改为Creator，AssigneeUser改为Assignee，TaskHistories改为History，TaskAssignees改为Assignees，解决Entity Framework查询时引发的"Unknown column"错误。 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 修复了PdcaPlan与Task的关系配置，将Task.PdcaPlan导航属性移除，解决了Task实体中不存在PdcaPlan属性的错误。 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 修复了QuickMemo实体属性映射，将PositionX改为PositionXDb，PositionY改为PositionYDb，确保正确映射到数据库中的PositionX和PositionY列。 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 添加了缺失的ConfigureNotesModule方法，配置QuickMemo和QuickMemoCategory实体的表名、主键、属性和关系映射，解决了"ConfigureNotesModule未定义"编译错误。 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 修复了PeriodicTaskSchedule实体中CreatorUserId列映射，确保正确指向creator_user_id列，解决"Unknown column 'p.CreatorUserId' in 'field list'"运行时错误。 | 已解决 |

## YYYY年MM月DD日 - 后端依赖注入修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Startup.cs | 在 ConfigureServices 方法中为 ITaskService 及其实现 TaskService 添加了作用域依赖注入注册 (services.AddScoped<ITaskService, TaskService>();)，以解决 'Unable to resolve service' 错误。 | 预期已解决 |
| 修改 | Startup.cs | 为确保 TaskService 和 ITaskService 类型能被正确解析，尝试在文件顶部添加了 using ItAssetsSystem.Application.Features.Tasks.Services; 指令。 (自动应用模型未应用此更改，但编译器应能解析) | 部分解决 |

## 2025年5月31日 - 前端任务详情弹窗修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | frontend/src/views/tasks/components/TaskDetailsDialog.vue | 修复了`open`方法处理传入参数的逻辑。之前该方法期望一个包含`id`属性的对象，但实际从父组件传入的是直接的任务ID (`taskId`)。这导致ID有效性检查失败，从而阻止了后续获取任务详情的API调用。修改后，`open`方法现在直接接收`taskId`，并正确使用它来调用`loadTaskDetails`方法。 | 已解决 |
| 修改 | frontend/src/views/tasks/components/TaskDetailsDialog.vue | 在任务详情弹窗中添加了"操作记录"区域，使用el-timeline组件展示任务的历史变更记录，包括操作人、操作类型、时间和描述等信息。同时添加了相应的SCSS样式。 | 已解决 |

## 2025年5月31日 - 实体映射关系修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Domain/Entities/Tasks/Task.cs | 为Task实体中所有导航属性添加ForeignKey特性，明确标注每个导航属性对应的外键字段，解决EF Core创建多余的影子属性的问题 | 已解决 |
| 修改 | Domain/Entities/Tasks/TaskHistory.cs | 为TaskHistory实体添加Key特性和ForeignKey属性，明确与Task、User、Comment和Attachment的关系，避免影子属性的生成 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 统一TaskHistory实体的列名约定，将所有snake_case列名更新为PascalCase，与其他实体保持一致，移除不必要的WithMany关系映射 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 修复TaskAssignee实体配置，只配置外键属性映射但不配置导航属性关系，解决TaskAssignee与Task、User实体键类型不匹配问题 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 优化Comment实体配置，只保留Task和ParentComment的导航属性关系，忽略User导航属性，避免INT/BIGINT主键类型不匹配 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 优化Attachment实体配置，保留Task导航属性关系但忽略UploaderUser导航属性，避免User表INT主键与Attachment表BIGINT外键不匹配 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 重构Task与PeriodicTaskSchedule之间的关系配置，移除双向关系改为单向关系并使用Ignore方法，解决循环依赖和外键类型不匹配问题 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 修复PeriodicTaskSchedule实体配置，忽略所有导航属性(TemplateTask, GeneratedTasks, CreatorUser)，避免与Task和User实体的循环引用和类型不匹配问题 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 移除Task实体中PreviousInstanceTask与自身的循环引用关系，改为使用Ignore方法处理导航属性 | 已解决 |
| 修改 | Domain/Entities/Tasks/Task.cs | 为Task实体中的PeriodicTaskSchedule导航属性添加[NotMapped]特性，解决即使添加了ForeignKey特性EF Core仍然创建影子属性的问题 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 在Task实体配置中添加 `entity.Ignore(t => t.Assignee)`，显式忽略Assignee导航属性，彻底解决EF Core生成并查询不存在的`AssigneeId`列导致的"Unknown column 't.AssigneeId'"错误。 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 在Task实体配置中添加 `entity.Ignore(t => t.Creator)`，显式忽略Creator导航属性，解决EF Core生成并查询不存在的`CreatorId`列导致的"Unknown column 't.CreatorId'"错误。 | 已解决 |

## 2025年5月30日 - 任务查询方法优化

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Application/Features/Tasks/Services/TaskService.cs | 重构UpdateTaskAsync方法，不再使用从数据库获取的Task对象进行更新，而是创建新的Task对象填充所需属性，避免从数据库获取的导航属性引起的影子属性问题 | 已解决 |
| 修改 | Infrastructure/Data/Repositories/TaskRepository.cs | 修改GetTaskByIdAsync方法，使用Select投影避免加载导航属性产生的AssetId1影子属性，修复"Unknown column 't.AssetId1' in 'field list'"错误 | 已解决 |
| 修改 | Infrastructure/Data/Repositories/TaskRepository.cs | 重构GetTaskByIdWithDetailsAsync方法，从使用Include关联加载改为分步独立查询，避免导航属性造成的影子属性问题，完美解决任务详情查询错误 | 已解决 |
| 修改 | Infrastructure/Data/Repositories/TaskRepository.cs | 重构UpdateTaskAsync方法，使用FindAsync获取现有任务后手动更新属性，避免Update方法自动追踪导航属性引起的影子属性问题，解决"更新任务需要两次才能成功"的问题 | 已解决 |

## 2025年5月29日 - 任务查询错误修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Domain/Entities/Tasks/Task.cs | 为Task实体中的Asset和Location导航属性添加ForeignKey特性，明确映射到AssetId和LocationId字段，解决EF Core生成影子属性AssetId1的问题 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 优化Task与Asset和Location的关系配置，添加显式约束名称(HasConstraintName)，清晰地表明关系，避免EF Core生成多余的影子属性 | 已解决 |
| 修改 | Infrastructure/Data/Repositories/TaskRepository.cs | 修改GetTasksPagedAsync方法，使用Select投影仅选择所需的基本字段，避免引用导航属性导致的多余字段生成，解决"Unknown column 't.AssetId1'"错误 | 已解决 |
| 修改 | Startup.cs | 在开发环境中启用EF Core的敏感数据日志记录(EnableSensitiveDataLogging)和详细错误信息(EnableDetailedErrors)，便于调试导航属性和外键映射问题 | 已解决 |

## 2025年5月28日 - 任务列表功能优化

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | frontend/src/views/tasks/SimpleTaskListView.vue | 修复任务列表视图，调整字段映射和显示逻辑，使其能正确显示数据库中的任务信息，包括工序、项目阶段、负责人、开始/结束时间等字段 | 已解决 |
| 修改 | frontend/src/views/tasks/components/TaskFormDialog.vue | 更新任务表单对话框组件，使其与数据库任务字段保持一致，修改状态选项、优先级选项、日期字段名称，并新增任务类型和积分字段 | 已解决 |
| 修改 | frontend/src/api/task.js | 优化任务API，修正getTaskList方法的参数映射，确保与前端过滤器和后端API匹配，并增强createTask和updateTask方法的数据处理 | 已解决 |

## 2025年5月27日 - 头像UI交互优化

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | frontend/src/views/user/ProfileView.vue | 优化头像更换交互：1) 添加avatar-wrapper包装元素；2) 重构CSS样式确保更换图标正确覆盖在头像上；3) 优化hover效果，使"点击更换"提示只在鼠标悬停时显示 | 已解决 |
| 修改 | frontend/src/stores/modules/user.js | 修复头像信息在会话间丢失的问题：1) 在用户登录后立即调用getUserInfoAction获取完整用户信息，包括头像URL；2) 修改逻辑确保优先使用完整用户信息，只在获取失败时才使用登录接口的基本信息；3) 确保即使在退出后重新登录也能正确显示之前上传的头像 | 已解决 |

## 2025年5月26日 - 头像显示修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | frontend/src/views/user/ProfileView.vue | 修复个人中心页面头像显示问题，将头像绑定从userStore.userInfo.avatar改为userStore.computedAvatarUrl，确保使用正确的完整URL显示头像，解决默认头像显示问题 | 已解决 |

## 2025年5月25日 - 头像上传功能优化

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Infrastructure/Services/LocalStorageFileService.cs | 优化SaveAvatarAsync方法，使其返回标准化的相对路径，避免前导斜杠导致的路径解析问题 | 已解决 |
| 修改 | Application/Features/Avatars/Commands/UploadAvatarCommandHandler.cs | 增加GetBaseUrl方法使用IHttpContextAccessor获取当前请求的基础URL，添加完整的访问URL到响应中，解决开发/生产环境的路径差异 | 已解决 |
| 修改 | Application/Features/Avatars/Dtos/UploadAvatarResponseDto.cs | 增加AccessUrl字段，同时返回相对路径和完整访问URL，方便前端灵活处理不同环境 | 已解决 |
| 修改 | Api/V2/Controllers/ProfileController.cs | 增加AvatarUploadResultDto类，处理新增的AccessUrl字段，优化头像上传接口响应 | 已解决 |
| 修改 | frontend/src/views/user/ProfileView.vue | 改进handleUploadAvatar方法，优先使用服务器返回的完整URL解决开发和生产环境路径差异问题 | 已解决 |
| 修改 | frontend/src/stores/modules/user.js | 新增setAvatarWithFullUrl方法，支持使用完整URL更新头像，同时保存相对路径确保兼容性 | 已解决 |
| 修改 | Startup.cs | 在ConfigureServices方法中注册IHttpContextAccessor服务，支持在CommandHandler中获取当前请求信息 | 已解决 |
| 修改 | Application/Features/Avatars/Commands/UploadAvatarCommand.cs | 删除文件中的重复UploadAvatarResponseDto类定义，修复"UploadAvatarResponseDto未包含AccessUrl的定义"编译错误 | 已解决 |

## 2025年5月17日 - 随手记功能完善

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | frontend/src/stores/modules/quickMemo.js | 修复了创建和更新随手记时未发送 `content` 字段的问题。确保 `createQuickMemo` 和 `editQuickMemo` action 将 `content` 包含在发送给 API 的 payload 中。 | 已解决 |
| 修改 | frontend/src/components/QuickMemo/MemoFormDrawer.vue | 将表单输入焦点改为内容字段，移除独立的标题输入框，根据内容自动生成标题（提取内容前15字符，移除HTML标签），以优化用户输入体验。 | 已解决 |

## 2025年5月12日 - 任务列表页面实现

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 创建 | frontend/src/views/tasks/components | 创建任务组件目录，用于存放任务相关可复用组件 | 已解决 |
| 创建 | frontend/src/views/tasks/components/TaskFormDialog.vue | 创建任务表单对话框组件，提供创建和编辑任务的功能 | 已解决 |
| 创建 | frontend/src/views/tasks/components/TaskDetailsDialog.vue | 创建任务详情对话框组件，提供任务详细信息的查看功能 | 已解决 |
| 创建 | frontend/src/api/tasks.js | 创建任务API适配器，统一适配不同版本的后端任务API，并提供模拟数据支持 | 已解决 |
| 修改 | frontend/src/views/tasks/SimpleTaskListView.vue | 优化任务列表页面，更改表格布局，添加工序、项目阶段、问题事项等列，集成外部对话框组件，改进用户交互体验 | 已解决 |

## 2025年5月12日 - QuickMemo数据库字段修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Domain/Entities/Notes/QuickMemo.cs | 修改QuickMemo实体中的Position属性字段名，确保可以正确映射到数据库列名 | 已解决 |
| 创建 | RunDbMigration.cs | 创建一键式数据库迁移工具脚本，添加缺失的position_x, position_y等列 | 已解决 |
| 创建 | RunDbMigration.csproj | 创建数据库迁移工具项目文件 | 已解决 |
| 创建 | add_quickmemo_position_columns.sql | 创建SQL脚本用于添加quick_memos表的必要列 | 已解决 |

## 2025年5月17日 - 随手记页面功能解耦

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | frontend/src/views/quickmemo/QuickMemoPageView.vue | 将随手记便利贴页面恢复为单一功能：1) 移除侧边栏和标签页切换；2) 移除词云相关组件和功能；3) 添加悬浮添加按钮，替代原侧边栏中的按钮功能 | 已解决 |
| 创建 | frontend/src/views/quickmemo/MemoWordCloudView.vue | 创建独立的词云页面组件，将词云功能从便利贴页面中分离出来，包含查看词云和添加随手记功能 | 已解决 |
| 修改 | frontend/src/router/routes.js | 添加新的`/main/memo-wordcloud`路由，指向词云独立页面，确保词云功能可以通过单独的菜单项访问 | 已解决 |

## 2025年5月16日 - 词云组件3D效果增强与导入路径修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | frontend/src/views/quickmemo/QuickMemoPageView.vue | 修复错误的导入路径，将`@/components/QuickMemo/QuickMemoListView.vue`改为`@/views/quickmemo/QuickMemoListView.vue`，解决了组件加载失败的问题 | 已解决 |
| 修改 | frontend/src/components/QuickMemo/MemoWordCloud.vue | 将原有的2D词云改造为真正的3D球体效果：1) 使用Three.js和OrbitControls实现可拖动旋转的3D词云球体；2) 添加球体内单词的缓慢微动效果；3) 实现鼠标悬停高亮与点击交互；4) 优化渲染性能与资源管理；5) 确保词云在整个页面内可以自由拖动旋转，更符合用户期望的3D效果 | 已解决 |
| 修改 | frontend/src/package.json | 添加three.js依赖，用于实现3D词云效果 | 已解决 |

## 2025年5月16日 - 便利贴坐标问题修复与词云3D效果优化

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | frontend/src/components/QuickMemo/QuickMemoBoard.vue | 修复添加词云侧边栏后导致的便利贴坐标计算混乱问题：1) 增加boardOffset对象存储和传递面板相对位置信息；2) 添加窗口大小变化和滚动事件监听以实时更新偏移量；3) 添加侧边栏状态变化监听，确保便利贴位置正确更新；4) 改进新便利贴位置计算逻辑，考虑当前可见区域和滚动状态 | 已解决 |
| 修改 | frontend/src/components/QuickMemo/StickyNote.vue | 重构便利贴拖拽逻辑：1) 添加对boardOffset属性的支持；2) 重写交互实例初始化逻辑，允许动态重建；3) 优化拖拽和调整大小的位置计算；4) 改进事件处理以避免编辑状态的冲突；5) 修复了因坐标计算错误导致无法拖动的问题 | 已解决 |
| 修改 | frontend/src/components/QuickMemo/MemoWordCloud.vue | 将平面词云改造为球形3D效果：1) 使用球面坐标算法均匀分布关键词；2) 实现自动旋转和深度层次感；3) 添加动态高亮和悬停效果；4) 使用黄金螺旋算法优化词分布；5) 实现3D到2D的投影，根据z轴位置计算透明度和大小，创造立体层次感；6) 应用动画效果使词云微微旋转，实现"像一个球，有层次微微动的"效果 | 已解决 |

## 2025年5月16日 - 随手记列表视图错误修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | frontend/src/views/quickmemo/QuickMemoListView.vue | 修复两个错误：1) 更正计算属性memos引用的store属性名从memos改为quickMemos；2) 修改fetchMemos方法调用的store方法名从fetchMemos改为fetchQuickMemos；3) 修改删除方法调用从deleteMemo改为removeQuickMemo | 已解决 |
| 修改 | frontend/src/components/QuickMemo/MemoFormDrawer.vue | 重构MemoFormDrawer组件以支持两种控制模式：1) 通过props (visible, memoId, isEditing) 控制；2) 通过store状态控制。添加只读模式支持，优化抽屉标题计算，修复关闭/保存逻辑，确保组件能够在列表视图和原有场景中都能正常使用 | 已解决 |

## 2025年5月16日 - 随手记词云功能实现

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 创建 | frontend/src/components/QuickMemo/MemoWordCloud.vue | 创建随手记词云组件，实现从随手记内容中提取关键词并以词云方式展示，支持根据词频调整字体大小和颜色，点击词条可查看相关随手记 | 已解决 |
| 修改 | frontend/src/components/QuickMemo/QuickMemoBoard.vue | 重构随手记便利贴板组件，添加可收缩的侧边栏用于展示词云组件，实现侧边栏状态持久化存储 | 已解决 |
| 修改 | frontend/src/views/quickmemo/QuickMemoPageView.vue | 更新随手记页面视图，修改样式以适应新添加的词云侧边栏，避免出现双滚动条等问题 | 已解决 |

## 2025年5月13日 (续)

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Infrastructure/Data/AppDbContext.cs | 在 ConfigureNotesModule 方法中为 QuickMemo 实体的 PositionX, PositionY, SizeWidth, SizeHeight, ZIndex 属性显式配置了 HasColumnName，以使用 PascalCase 列名 (例如 PositionX)，确保与数据库列名一致并解决 'Unknown column position_x' 运行时错误。 | 已配置，待测试 |

## 2025年5月13日

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Domain/Entities/Tasks/Task.cs | 移除了实体类中与数据库表结构不符的 `Notes` 属性，该属性导致EF Core查询时试图选取不存在的列，引发 "Unknown column 't.Notes'" SQL错误。 | 已解决 |
| 修改 | Domain/Entities/Tasks/Task.cs | 从Task实体中移除了冗余的 `DueDate` 和 `CompletedAt` 属性，这两个属性在数据库中没有对应列，并导致了"Unknown column"的SQL执行错误。实际的截止日期和完成时间分别由 `PlanEndDate` 和 `ActualEndDate` 表示。 | 已解决 |
| 修改 | Domain/Entities/Tasks/Task.cs | 移除了废弃的 PeriodicRuleId 和 PeriodicRule 属性，并将 PdcaPlanId 的类型从 int? 修改为 long? 以匹配关联实体的主键类型。解决了CS0246编译错误。 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 更新了Task实体的配置，移除了对 PeriodicRuleId 的映射，添加了对 PdcaPlanId 的正确列映射，并配置了 Task 与 PeriodicTaskSchedule 的关系。 | 已解决 |
| 修改 | Domain/Entities/Tasks/Task.cs | 修正了Task实体类的属性命名，使其与数据库表结构保持一致。包括将复杂的Status和TaskType属性改为直接字符串属性（无需通过_statusId和_taskTypeId转换），将RelatedAssetId改为AssetId，RelatedLocationId改为LocationId，ClaimedByUserId改为AssigneeUserId，CreatedAt改为CreationTimestamp，UpdatedAt改为LastUpdatedTimestamp等。 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 修正了Task实体在EF Core中的配置，调整了属性与数据库列的映射关系。将TaskId正确映射到TaskId列（而非Id列），Name映射到Name列（而非Title列），移除了_statusId和_taskTypeId的配置，改为直接映射Status和TaskType字符串属性。 | 已解决 |
| 修改 | Infrastructure/Data/Repositories/TaskRepository.cs | 修正了TaskRepository.cs中对Task实体属性的引用，包括在查询和更新操作中使用正确的属性名，删除了对_statusId和_taskTypeId的特殊处理。 | 已解决 |

## 2025年4月6日

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Models/Entities/Task.cs | 修复了错误使用的[NotMapped]特性，将其从方法上移除，添加了两个属性AssigneeList和MentionedList，并正确应用了[NotMapped]特性。错误原因是[NotMapped]特性仅能应用于属性或字段，不能应用于方法。 | 已解决 |
| 修改 | Core/Plugins/TaskManagementPlugin.cs | 移除了IsEnabled、Initialize和Shutdown方法上的override关键字，因为基类PluginBase中没有对应的虚拟或抽象方法可以重写。同时解决了Task类型冲突问题，使用System.Threading.Tasks.Task的完全限定名称。 | 已解决 |
| 修改 | Core/Services/ScoreCalculationService.cs | 修复了WaitForNextTickAsync的返回值问题，添加.AsTask()方法将ValueTask<bool>转换为Task类型。解决了TaskStatus枚举的命名冲突，使用ItAssetsSystem.Models.Entities.TaskStatus的完全限定名称。 | 已解决 |
| 创建 | CHANGE_RULES.md | 创建了修改记录规则文件，定义了项目中所有修改的记录格式、操作类型、路径记录、说明要求和问题状态等规则。 | 已解决 |
| 创建 | CHANGELOG.md | 创建了修改记录文件，记录所有对项目的修改历史，便于追踪变更和解决问题。 | 已解决 |
| 创建 | frontend/src/components/Tasks/RewardPanel.vue | 创建任务奖励面板组件，用于在任务完成时显示获得的积分和道具奖励，包含奖励标题、描述、积分展示和道具列表等功能 | 已解决 |
| 创建 | frontend/src/components/Tasks/TaskRewardPopup.vue | 创建任务奖励弹窗组件，作为RewardPanel的容器，提供弹窗功能，在任务完成等操作后弹出显示奖励内容 | 已解决 |

## 2025年4月7日

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | frontend/src/api/task.js | 重构任务管理API接口，添加新功能接口，移除旧接口，统一URL路径 | 已解决 |
| 修改 | frontend/src/api/task.js | 修复任务类型API、任务进度记录API等路径错误，统一使用request对象格式，避免混用request.get和request({})语法。 | 已解决 |
| 修改 | Controllers/TaskController.cs | 添加任务列表分页API路由[HttpGet("list")]，返回带分页信息的模拟数据，确保与前端API调用匹配。 | 已解决 |
| 修改 | Controllers/TaskController.cs | 添加任务进度记录、排行榜、用户道具和签到API，提供模拟数据支持前端功能调用。 | 已解决 |

## 2025年6月15日 11:45:30 - 修复任务管理API兼容性问题

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | frontend/src/api/task.js | 添加getTasks和getTaskById方法作为getTaskList和getTaskDetail的别名，解决前端视图组件调用API不兼容的问题 | 已解决 |

## 2025年6月15日 12:30:15 - 修复前端任务API导入问题

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 创建 | frontend/src/api/tasks.js | 创建tasks.js文件，从task.js导入并重新导出taskApi对象，解决"The requested module '/src/api/tasks.js' does not provide an export named 'taskApi'"错误 | 已解决 |

## 2025年6月15日 13:15:45 - 修复任务API参数映射问题

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | frontend/src/api/task.js | 修复getTaskList方法中的参数映射：1) 将pageIndex映射为pageNumber；2) 将search映射为searchTerm；3) 将startDate/endDate映射为fromDate/toDate；4) 添加状态值映射函数，确保前端状态值(todo, doing等)正确转换为后端API接受的格式(Todo, InProgress等) | 已解决 |

## 创建 | frontend/src/views/tasks/components/TaskComments.vue | 创建任务评论组件 TaskComments.vue，修复 TaskDetailDialog.vue 缺失评论组件导致的导入错误 | 已解决 |

## 创建 | frontend/src/views/tasks/components/TaskAttachments.vue | 创建任务附件组件 TaskAttachments.vue，修复 TaskDetailDialog.vue 缺失附件组件导致的导入错误 | 已解决 |

| 修改 | frontend/src/views/tasks/SimpleTaskListView.vue | 标记为废弃，顶部添加废弃注释，建议使用 EnhancedTaskListView.vue 替代 | 已解决 |
| 修改 | frontend/src/views/tasks/TaskListView.vue | 标记为废弃，顶部添加废弃注释，建议使用 EnhancedTaskListView.vue 替代 | 已解决 |
| 修改 | frontend/src/views/tasks/KanbanView.vue | 标记为废弃，顶部添加废弃注释，建议使用 ModernKanbanView.vue 或 EnhancedTaskListView.vue 替代 | 已解决 |
| 修改 | frontend/src/views/tasks/ModernKanbanViewSimple.vue | 标记为废弃，顶部添加废弃注释，建议使用 ModernKanbanView.vue 替代 | 已解决 |

## 创建 | frontend/src/components/TagInput.vue | 创建标签输入组件 TagInput.vue，修复 TaskDetailDialog.vue 缺失标签输入组件导致的导入错误 | 已解决 |

## 修改 | frontend/src/api/asset.js | 增加统一导出 assetApi 对象，修复 AssetSelect.vue 导入 assetApi 报错 | 已解决 |

| 修改 | frontend/src/api/location.js | 增加导出 locationApi 对象，修复 LocationSelect.vue 导入 locationApi 报错 | 已解决 |

| 修改 | frontend/src/views/user/ProfileView.vue | 将 import taskApi from '../../api/task.js' 改为 import { taskApi } from '../../api/task.js'，彻底修复 default 导出错误 | 已解决 |
| 修改 | frontend/src/views/tasks/TaskDetailView.vue | 将 import taskApi from '../../api/task.js' 改为 import { taskApi } from '../../api/task.js'，彻底修复 default 导出错误 | 已解决 |
| 修改 | frontend/src/views/tasks/KanbanView.deprecated.vue | 将 import taskApi from '../../api/task.js' 改为 import { taskApi } from '../../api/task.js'，彻底修复 default 导出错误 | 已解决 |
| 修改 | frontend/src/views/dashboard/index.vue | 将 import taskApi from '@/api/task.js' 改为 import { taskApi } from '@/api/task.js'，彻底修复 default 导出错误 | 已解决 |

| 修改 | Application/Features/Tasks/Dtos/AddCommentRequestDto.cs | 添加#nullable enable，修复CS8632警告 | 已解决 |
| 修改 | Application/Features/Tasks/Dtos/AttachmentDto.cs | 添加#nullable enable，修复CS8632警告 | 已解决 |
| 修改 | Application/Features/Tasks/Dtos/CompleteTaskRequestDto.cs | 添加#nullable enable，修复CS8632警告 | 已解决 |
| 修改 | Application/Features/Tasks/Dtos/CreateTaskRequestDto.cs | 添加#nullable enable，修复CS8632警告 | 已解决 |
| 修改 | Application/Features/Tasks/Dtos/UpdateTaskRequestDto.cs | 添加#nullable enable，修复CS8632警告 | 已解决 |
| 修改 | Application/Features/Tasks/Services/TaskService.cs | 添加#nullable enable，修复CS8632警告 | 已解决 |
| 修改 | Api/V2/Controllers/TasksControllerEnhanced.cs | 修正XML注释参数名与方法签名一致，修复CS1572/CS1573警告 | 已解决 |

| 修改 | frontend/src/views/tasks/TaskFormView.vue | 删除<style scoped>中.quick-actions .el-button后多余的}，修复CSS语法错误和构建失败 | 已解决 |

| 修改 | frontend/src/views/system/users.vue | @import改为@use，消除Sass @import弃用警告 | 已解决 |
| 修改 | frontend/src/views/system/roles.vue | @import改为@use，消除Sass @import弃用警告 | 已解决 |
| 修改 | frontend/src/views/system/menus.vue | @import改为@use，消除Sass @import弃用警告 | 已解决 |
| 修改 | frontend/src/views/system/logs.vue | @import改为@use，消除Sass @import弃用警告 | 已解决 |
| 修改 | frontend/src/views/system/index.vue | @import改为@use，消除Sass @import弃用警告 | 已解决 |
| 修改 | frontend/src/views/system/departments.vue | @import改为@use，消除Sass @import弃用警告 | 已解决 |
| 修改 | frontend/src/views/purchases/index.vue | @import改为@use，消除Sass @import弃用警告 | 已解决 |
| 修改 | frontend/src/views/faults/maintenance.vue | @import改为@use，消除Sass @import弃用警告 | 已解决 |
| 修改 | frontend/src/views/faults/list.vue | @import改为@use，消除Sass @import弃用警告 | 已解决 |
| 修改 | frontend/src/views/faults/index.vue | @import改为@use，消除Sass @import弃用警告 | 已解决 |

| 修改 | frontend/src/views/system/users.vue | 批量将el-button的type="link"改为type="text"，消除Element Plus警告 | 已解决 |
| 修改 | frontend/src/views/system/roles.vue | 批量将el-button的type="link"改为type="text"，消除Element Plus警告 | 已解决 |
| 修改 | frontend/src/views/system/menus.vue | 批量将el-button的type="link"改为type="text"，消除Element Plus警告 | 已解决 |
| 修改 | frontend/src/views/system/departments.vue | 批量将el-button的type="link"改为type="text"，消除Element Plus警告 | 已解决 |
| 修改 | frontend/src/views/LeaderboardView.vue | 批量将el-button的type="link"改为type="text"，消除Element Plus警告 | 已解决 |
| 修改 | frontend/src/views/dashboard/index.vue | 批量将el-button的type="link"改为type="text"，消除Element Plus警告 | 已解决 |
| 修改 | frontend/src/views/asset/list.vue | 批量将el-button的type="link"改为type="text"，消除Element Plus警告 | 已解决 |
| 修改 | frontend/src/components/Tasks/QuickTaskCreator.vue | 批量将el-button的type="link"改为type="text"，消除Element Plus警告 | 已解决 |
| 修改 | frontend/src/components/Tasks/EnhancedTaskCard.vue | 批量将el-button的type="link"改为type="text"，消除Element Plus警告 | 已解决 |

| 修改 | Controllers/UserController.cs | 用户列表接口（/api/User GET）只返回激活用户，并在返回字段中增加 isActive 字段 | 已解决 |
| 修改 | frontend/src/views/tasks/EnhancedTaskListView.vue | 修复任务列表负责人头像不显示问题，渲染前自动补全 assigneeAvatarUrl 字段，保证 UserAvatar 组件能正确显示头像 | 已解决 |

| 修改 | Domain/Entities/Tasks/Task.cs | 增加 IsDeleted 字段，实现软删除支持 | 已解决 |
| 修改 | Application/Features/Tasks/Services/TaskService.cs | DeleteTaskAsync 改为设置 IsDeleted=true，兼容 Status=Archived，所有查询自动过滤软删除 | 已解决 |
| 修改 | Infrastructure/Data/Extensions/YourDbContext.cs | 配置 Task 实体全局软删除查询过滤器 | 已解决 |
| 修改 | Migrations/202506xx_AddIsDeletedToTasks.sql | 新增 tasks.IsDeleted 字段，默认0 | 已解决 |

| 修改 | Migrations/AppDbContextModelSnapshot.cs | 删除了 TaskHistory 实体中关于 AttachmentId1、CommentId1 的所有属性、索引、外键定义，彻底修复模型快照污染 | 已解决 |
| 修改 | Migrations/20250528110408_InitClean.cs | 删除了 taskhistory 表创建中关于 AttachmentId1、CommentId1 的所有列、索引、外键定义，彻底修复迁移污染 | 已解决 |
| 修改 | Migrations/20250528110408_InitClean.Designer.cs | 删除了 TaskHistory 实体中关于 AttachmentId1、CommentId1 的所有属性、索引、外键定义，彻底修复迁移快照污染 | 已解决 |

## 2025年6月15日 11:45:30 - 修复任务管理API兼容性问题

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | frontend/src/api/task.js | 添加getTasks和getTaskById方法作为getTaskList和getTaskDetail的别名，解决前端视图组件调用API不兼容的问题 | 已解决 |

## 2025年6月15日 12:30:15 - 修复前端任务API导入问题

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 创建 | frontend/src/api/tasks.js | 创建tasks.js文件，从task.js导入并重新导出taskApi对象，解决"The requested module '/src/api/tasks.js' does not provide an export named 'taskApi'"错误 | 已解决 |

## 2025年6月15日 13:15:45 - 修复任务API参数映射问题

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | frontend/src/api/task.js | 修复getTaskList方法中的参数映射：1) 将pageIndex映射为pageNumber；2) 将search映射为searchTerm；3) 将startDate/endDate映射为fromDate/toDate；4) 添加状态值映射函数，确保前端状态值(todo, doing等)正确转换为后端API接受的格式(Todo, InProgress等) | 已解决 |

## 创建 | frontend/src/views/tasks/components/TaskComments.vue | 创建任务评论组件 TaskComments.vue，修复 TaskDetailDialog.vue 缺失评论组件导致的导入错误 | 已解决 |

## 创建 | frontend/src/views/tasks/components/TaskAttachments.vue | 创建任务附件组件 TaskAttachments.vue，修复 TaskDetailDialog.vue 缺失附件组件导致的导入错误 | 已解决 |

| 修改 | frontend/src/views/tasks/SimpleTaskListView.vue | 标记为废弃，顶部添加废弃注释，建议使用 EnhancedTaskListView.vue 替代 | 已解决 |
| 修改 | frontend/src/views/tasks/TaskListView.vue | 标记为废弃，顶部添加废弃注释，建议使用 EnhancedTaskListView.vue 替代 | 已解决 |
| 修改 | frontend/src/views/tasks/KanbanView.vue | 标记为废弃，顶部添加废弃注释，建议使用 ModernKanbanView.vue 或 EnhancedTaskListView.vue 替代 | 已解决 |
| 修改 | frontend/src/views/tasks/ModernKanbanViewSimple.vue | 标记为废弃，顶部添加废弃注释，建议使用 ModernKanbanView.vue 替代 | 已解决 |

## 创建 | frontend/src/components/TagInput.vue | 创建标签输入组件 TagInput.vue，修复 TaskDetailDialog.vue 缺失标签输入组件导致的导入错误 | 已解决 |

## 修改 | frontend/src/api/asset.js | 增加统一导出 assetApi 对象，修复 AssetSelect.vue 导入 assetApi 报错 | 已解决 |

| 修改 | frontend/src/api/location.js | 增加导出 locationApi 对象，修复 LocationSelect.vue 导入 locationApi 报错 | 已解决 |

| 修改 | frontend/src/views/user/ProfileView.vue | 将 import taskApi from '../../api/task.js' 改为 import { taskApi } from '../../api/task.js'，彻底修复 default 导出错误 | 已解决 |
| 修改 | frontend/src/views/tasks/TaskDetailView.vue | 将 import taskApi from '../../api/task.js' 改为 import { taskApi } from '../../api/task.js'，彻底修复 default 导出错误 | 已解决 |
| 修改 | frontend/src/views/tasks/KanbanView.deprecated.vue | 将 import taskApi from '../../api/task.js' 改为 import { taskApi } from '../../api/task.js'，彻底修复 default 导出错误 | 已解决 |
| 修改 | frontend/src/views/dashboard/index.vue | 将 import taskApi from '@/api/task.js' 改为 import { taskApi } from '@/api/task.js'，彻底修复 default 导出错误 | 已解决 |

| 修改 | Application/Features/Tasks/Dtos/AddCommentRequestDto.cs | 添加#nullable enable，修复CS8632警告 | 已解决 |
| 修改 | Application/Features/Tasks/Dtos/AttachmentDto.cs | 添加#nullable enable，修复CS8632警告 | 已解决 |
| 修改 | Application/Features/Tasks/Dtos/CompleteTaskRequestDto.cs | 添加#nullable enable，修复CS8632警告 | 已解决 |
| 修改 | Application/Features/Tasks/Dtos/CreateTaskRequestDto.cs | 添加#nullable enable，修复CS8632警告 | 已解决 |
| 修改 | Application/Features/Tasks/Dtos/UpdateTaskRequestDto.cs | 添加#nullable enable，修复CS8632警告 | 已解决 |
| 修改 | Application/Features/Tasks/Services/TaskService.cs | 添加#nullable enable，修复CS8632警告 | 已解决 |
| 修改 | Api/V2/Controllers/TasksControllerEnhanced.cs | 修正XML注释参数名与方法签名一致，修复CS1572/CS1573警告 | 已解决 |

| 修改 | frontend/src/views/tasks/TaskFormView.vue | 删除<style scoped>中.quick-actions .el-button后多余的}，修复CSS语法错误和构建失败 | 已解决 |

| 修改 | frontend/src/views/system/users.vue | @import改为@use，消除Sass @import弃用警告 | 已解决 |
| 修改 | frontend/src/views/system/roles.vue | @import改为@use，消除Sass @import弃用警告 | 已解决 |
| 修改 | frontend/src/views/system/menus.vue | @import改为@use，消除Sass @import弃用警告 | 已解决 |
| 修改 | frontend/src/views/system/logs.vue | @import改为@use，消除Sass @import弃用警告 | 已解决 |
| 修改 | frontend/src/views/system/index.vue | @import改为@use，消除Sass @import弃用警告 | 已解决 |
| 修改 | frontend/src/views/system/departments.vue | @import改为@use，消除Sass @import弃用警告 | 已解决 |
| 修改 | frontend/src/views/purchases/index.vue | @import改为@use，消除Sass @import弃用警告 | 已解决 |
| 修改 | frontend/src/views/faults/maintenance.vue | @import改为@use，消除Sass @import弃用警告 | 已解决 |
| 修改 | frontend/src/views/faults/list.vue | @import改为@use，消除Sass @import弃用警告 | 已解决 |
| 修改 | frontend/src/views/faults/index.vue | @import改为@use，消除Sass @import弃用警告 | 已解决 |

| 修改 | frontend/src/views/system/users.vue | 批量将el-button的type="link"改为type="text"，消除Element Plus警告 | 已解决 |
| 修改 | frontend/src/views/system/roles.vue | 批量将el-button的type="link"改为type="text"，消除Element Plus警告 | 已解决 |
| 修改 | frontend/src/views/system/menus.vue | 批量将el-button的type="link"改为type="text"，消除Element Plus警告 | 已解决 |
| 修改 | frontend/src/views/system/departments.vue | 批量将el-button的type="link"改为type="text"，消除Element Plus警告 | 已解决 |
| 修改 | frontend/src/views/LeaderboardView.vue | 批量将el-button的type="link"改为type="text"，消除Element Plus警告 | 已解决 |
| 修改 | frontend/src/views/dashboard/index.vue | 批量将el-button的type="link"改为type="text"，消除Element Plus警告 | 已解决 |
| 修改 | frontend/src/views/asset/list.vue | 批量将el-button的type="link"改为type="text"，消除Element Plus警告 | 已解决 |
| 修改 | frontend/src/components/Tasks/QuickTaskCreator.vue | 批量将el-button的type="link"改为type="text"，消除Element Plus警告 | 已解决 |
| 修改 | frontend/src/components/Tasks/EnhancedTaskCard.vue | 批量将el-button的type="link"改为type="text"，消除Element Plus警告 | 已解决 |

| 修改 | Controllers/UserController.cs | 用户列表接口（/api/User GET）只返回激活用户，并在返回字段中增加 isActive 字段 | 已解决 |
| 修改 | frontend/src/views/tasks/EnhancedTaskListView.vue | 修复任务列表负责人头像不显示问题，渲染前自动补全 assigneeAvatarUrl 字段，保证 UserAvatar 组件能正确显示头像 | 已解决 |

| 修改 | Domain/Entities/Tasks/Task.cs | 增加 IsDeleted 字段，实现软删除支持 | 已解决 |
| 修改 | Application/Features/Tasks/Services/TaskService.cs | DeleteTaskAsync 改为设置 IsDeleted=true，兼容 Status=Archived，所有查询自动过滤软删除 | 已解决 |
| 修改 | Infrastructure/Data/Extensions/YourDbContext.cs | 配置 Task 实体全局软删除查询过滤器 | 已解决 |
| 修改 | Migrations/202506xx_AddIsDeletedToTasks.sql | 新增 tasks.IsDeleted 字段，默认0 | 已解决 |

| 修改 | Migrations/AppDbContextModelSnapshot.cs | 删除了 TaskHistory 实体中关于 AttachmentId1、CommentId1 的所有属性、索引、外键定义，彻底修复模型快照污染 | 已解决 |
| 修改 | Migrations/20250528110408_InitClean.cs | 删除了 taskhistory 表创建中关于 AttachmentId1、CommentId1 的所有列、索引、外键定义，彻底修复迁移污染 | 已解决 |
| 修改 | Migrations/20250528110408_InitClean.Designer.cs | 删除了 TaskHistory 实体中关于 AttachmentId1、CommentId1 的所有属性、索引、外键定义，彻底修复迁移快照污染 | 已解决 |

## 2024-06-06 任务多负责人修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | frontend/src/api/task.js | 修复updateTask方法，确保传递协作者列表到后端 | 已解决 |
| 修改 | frontend/src/components/Tasks/QuickTaskCreator.vue | 修改快速创建任务组件，确保正确处理多负责人 | 已解决 |
| 修改 | frontend/src/views/tasks/components/TaskDetailDialog.vue | 修复任务详情对话框编辑功能，确保多负责人保存正确 | 已解决 |

## 2025年6月16日 09:42:56 - 修复任务多负责人保存问题

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Application/Features/Tasks/Services/TaskService.cs | 修复UpdateTaskAsync方法中的协作者处理逻辑，确保在更新任务时正确处理主负责人和协作者列表，解决协作者无法正确保存到数据库的问题 | 已解决 |
| 修改 | Application/Features/Tasks/Services/TaskService.cs | 修复CreateTaskAsync方法中的协作者处理逻辑，确保在创建任务时正确处理主负责人和协作者列表，避免协作者列表丢失 | 已解决 |
| 修改 | Infrastructure/Data/Repositories/TaskRepository.cs | 优化GetAssigneesByTaskIdAsync方法，添加错误处理，确保即使查询失败也能返回空列表而不是抛出异常 | 已解决 |
| 修改 | Infrastructure/Data/Repositories/TaskRepository.cs | 完善AddAssigneeAsync和RemoveAssigneeAsync方法，增加日志记录和重复记录检查，提高任务协作者管理的稳定性 | 已解决 |
| 修改 | Infrastructure/Data/Repositories/ITaskRepository.cs | 更新接口方法签名和文档注释，确保与实现类保持一致 | 已解决 |
| 修改 | frontend/src/api/task.js | 优化updateTask方法，确保正确处理多负责人情况，将第一个人设为主负责人，其余设为协作者，并删除原始assigneeUserIds字段避免后端混淆 | 已解决 |

## 2025年05月08日 18:22:34 - 修复通知模块数据库列名访问错误

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Core/Services/NotificationService.cs | 修复GetUserNotificationsAsync方法中的查询，将CreatedAt改为CreationTimestamp，并使用Select投影填充API兼容属性，解决了错误"Unknown column 'n.notification_id' in 'field list'" | 已解决 |
| 修改 | Api/V2/Controllers/NotificationsController.cs | 修改控制器使用BaseController中的ApiResponse类进行返回，替换原有的命名空间引用 | 已解决 |

## 2025年05月08日 18:45:23 - 修复通知模块数据库列名访问错误

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | Infrastructure/Data/AppDbContext.cs | 修复ConfigureNotificationModule方法中的列名映射，将snake_case格式改为PascalCase格式，与数据库实际结构一致，解决了"Unknown column 'n.notification_id' in 'field list'"错误 | 已解决 |
| 修改 | Core/Services/NotificationService.cs | 将MarkAsReadAsync方法中的ReadAt字段改为ReadTimestamp，与数据库表结构保持一致 | 已解决 |

## 修改 | Infrastructure/Data/AppDbContext.cs | 修改Notification实体的列映射，将snake_case列名改为PascalCase以匹配数据库结构 | 已解决 |
| 修改 | Domain/Entities/Notification.cs | 为不存在于数据库表中的属性添加[NotMapped]特性，避免EF Core尝试映射不存在的列 | 已解决 |

| 创建 | Core/Hubs/NotificationHub.cs | 创建SignalR NotificationHub，用于实时推送通知 | 已解决 |
| 修改 | Core/Services/NotificationService.cs | 添加SignalR实时通知功能，在创建通知时推送到用户 | 已解决 |
| 修改 | Startup.cs | 添加SignalR服务配置和Hub路由映射 | 已解决 |
| 创建 | frontend/src/utils/notification-service.js | 创建前端SignalR通知服务，用于接收实时通知 | 已解决 |
| 修改 | frontend/src/stores/modules/notification.js | 修改通知存储模块，添加处理实时通知的方法 | 已解决 |
| 修改 | frontend/src/views/tasks/components/NotificationCenter.vue | 在组件挂载时初始化SignalR连接 | 已解决 |
| 修改 | frontend/src/main.js | 在应用启动时初始化通知轮询 | 已解决 |
| 修改 | frontend/package.json | 添加@microsoft/signalr依赖包 | 已解决 |

| 修改 | Domain/Entities/Notification.cs | 删除Notification实体中的导航属性，解决'UserId1'字段错误 | 已解决 |
| 修改 | Infrastructure/Data/AppDbContext.cs | 修复通知实体映射配置，消除多余的导航属性关联 | 已解决 |
| 修改 | Core/Services/NotificationService.cs | 修改通知服务，避免使用导航属性，添加错误处理 | 已解决 |
| 创建 | Core/Initialization/NotificationInitializer.cs | 创建通知表初始化器，自动检查并修复数据库中的UserId1列问题 | 已解决 |
| 创建 | Infrastructure/Data/Extensions/HostExtensions.cs | 创建主机扩展方法FixNotificationTableAsync，避免与已有InitializeDatabaseAsync方法冲突 | 已解决 |
| 修改 | Program.cs | 在应用启动流程中添加通知表结构修复步骤 | 已解决 |

| 创建 | Api/V2/Controllers/NotificationTestController.cs | 创建通知测试控制器，用于测试通知系统 | 已解决 |
| 修改 | Core/Services/NotificationService.cs | 增强通知服务中的日志记录，方便调试通知问题 | 已解决 |
| 修改 | Core/Hubs/NotificationHub.cs | 改进NotificationHub的连接验证方式，允许通过查询参数传递用户ID | 已解决 |
| 修改 | frontend/src/utils/notification-service.js | 改进前端SignalR连接方式，添加isConnected属性用于监控连接状态 | 已解决 |
| 修改 | frontend/src/api/notification.js | 添加测试通知相关的API接口 | 已解决 |
| 修改 | frontend/src/views/tasks/components/NotificationCenter.vue | 添加测试按钮，方便调试通知功能 | 已解决 |
| 创建 | frontend/src/views/test/NotificationTestPage.vue | 创建通知测试页面，提供详细的通知系统测试工具 | 已解决 |
| 创建 | frontend/src/views/test/IndexView.vue | 创建测试页面的索引视图 | 已解决 |

## 2025年07月10日 10:25:36 - 路由配置错误修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | frontend/src/router/routes.js | 修复路由配置错误，将顶级的"test"路由路径(没有以斜杠开头)修改为合并到已有的"/test"路由中，解决"Route paths should start with a "/": "test" should be "/test""错误 | 已解决 |

## 2025年07月10日 10:55:42 - 通知系统问题修复

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 创建 | frontend/src/views/notifications/NotificationsListView.vue | 创建通知列表页面组件，用于显示所有通知信息，支持标记已读、删除和按类型筛选通知 | 已解决 |
| 修改 | frontend/src/router/routes.js | 添加通知中心路由配置，路径为"/main/notifications" | 已解决 |
| 修改 | frontend/src/views/tasks/components/NotificationCenter.vue | 优化通知图标样式，确保图标可点击，并正确处理通知点击事件 | 已解决 |
| 修改 | frontend/src/stores/modules/notification.js | 修复通知Store中的导入语句，正确导入notificationApi而非默认导出 | 已解决 |

## 2025年07月10日 11:35:24 - 通知中心交互优化

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 修改 | frontend/src/views/tasks/components/NotificationCenter.vue | 将通知中心组件从el-popover改为el-dropdown，解决点击不能打开的问题。同时优化样式，添加更明确的日志输出，方便调试通知跳转问题 | 已解决 |
