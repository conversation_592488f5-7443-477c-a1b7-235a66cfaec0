import{_ as e,r as a,a5 as l,c as t,p as s,m as r,a8 as u,b as n,d as o,a9 as c,e as d,w as i,t as v,f as m,af as p,u as f,a as y,o as b,aG as k,bI as V,v as h,A as _,Y as w,F as I,h as g,aH as C,bJ as S}from"./index-CkwLz8y6.js";import{l as q,g as x}from"./spareparts-Cv2l4Tzu.js";const U={class:"quick-outbound-view"},T={class:"mobile-header"},N={class:"scan-search-area"},z={key:0,class:"part-info-card"},L={class:"part-header"},j={class:"part-name"},M={class:"part-code"},A={class:"part-details"},F={class:"info-item"},G={class:"value"},H={class:"info-item"},J={class:"value"},O={class:"info-item"},Q={class:"value"},Y={key:1,class:"part-selector"},$={key:2,class:"outbound-form"},B={key:3,class:"submit-area"},D={class:"part-search"},E={class:"part-list"},K=["onClick"],P={class:"part-main-info"},R={class:"part-item-name"},W={class:"part-item-code"},X={class:"part-extra-info"},Z={class:"stock"},ee=e({__name:"MobileQuickOutView",setup(e){const ee=f(),ae=a(""),le=a(null),te=a(!1),se=a(""),re=a(!1),ue=a([]),ne=a([]),oe=a([]),ce=l({partId:null,quantity:1,reasonType:3,locationId:null,receiverId:null,referenceNumber:"",remarks:""}),de=t((()=>{if(!se.value)return ne.value.filter((e=>e.currentStock>0));const e=se.value.toLowerCase();return ne.value.filter((a=>a.currentStock>0&&(a.name.toLowerCase().includes(e)||a.code.toLowerCase().includes(e))))}));s((()=>ce.reasonType),(e=>{3===e&&0===oe.value.length&&me()})),r((()=>{ie(),ve()}));const ie=async()=>{try{const e=await q();e.success?(ue.value=e.data,ue.value.length>0&&(ce.locationId=ue.value[0].id)):u.error(e.message||"获取库位失败")}catch(e){u.error("获取库位失败，请稍后重试")}},ve=async()=>{try{const e=await x({pageIndex:1,pageSize:100});e.success?ne.value=e.data.items:u.error(e.message||"获取备件列表失败")}catch(e){u.error("获取备件列表失败，请稍后重试")}},me=async()=>{try{const e=await S();e.success?oe.value=e.data:u.error(e.message||"获取用户列表失败")}catch(e){u.error("获取用户列表失败，请稍后重试")}},pe=()=>{ee.back()},fe=()=>{u.info("扫码功能待实现")},ye=()=>{te.value=!0},be=()=>{le.value=null,ce.partId=null},ke=async()=>{if(le.value)if(ce.locationId)if(3!==ce.reasonType||ce.receiverId)if(ce.quantity>le.value.currentStock)u.warning(`出库数量不能超过当前库存(${le.value.currentStock})`);else{re.value=!0;try{const e={partId:ce.partId,quantity:ce.quantity,type:2,reasonType:ce.reasonType,locationId:ce.locationId,receiverId:ce.receiverId,referenceNumber:ce.referenceNumber,remarks:ce.remarks},a=await(void 0)(e);a.success?(u.success("出库成功"),be(),ce.quantity=1,ce.receiverId=null,ce.referenceNumber="",ce.remarks="",ae.value=""):u.error(a.message||"出库失败")}catch(e){u.error("出库失败，请稍后重试")}finally{re.value=!1}}else u.warning("请选择领用人");else u.warning("请选择库位");else u.warning("请先选择备件")};return(e,a)=>{const l=y("el-icon"),t=y("el-button"),s=y("el-input"),r=y("el-input-number"),f=y("el-form-item"),S=y("el-option"),q=y("el-select"),x=y("el-form"),ee=y("el-drawer");return b(),n("div",U,[o("div",T,[o("div",{class:"back-button",onClick:pe},[d(l,null,{default:i((()=>[d(m(k))])),_:1})]),a[9]||(a[9]=o("div",{class:"title"},"快速出库",-1)),a[10]||(a[10]=o("div",null,null,-1))]),o("div",N,[d(s,{modelValue:ae.value,"onUpdate:modelValue":a[0]||(a[0]=e=>ae.value=e),placeholder:"扫描或输入备件编号/条码",clearable:""},{prefix:i((()=>[d(l,null,{default:i((()=>[d(m(h))])),_:1})])),append:i((()=>[d(t,{icon:m(V),onClick:fe},null,8,["icon"])])),_:1},8,["modelValue"])]),le.value?(b(),n("div",z,[o("div",L,[o("div",null,[o("div",j,v(le.value.name),1),o("div",M,v(le.value.code),1)]),d(t,{type:"danger",size:"small",icon:m(p),circle:"",onClick:be},null,8,["icon"])]),o("div",A,[o("div",F,[a[11]||(a[11]=o("span",{class:"label"},"类型:",-1)),o("span",G,v(le.value.typeName),1)]),o("div",H,[a[12]||(a[12]=o("span",{class:"label"},"规格:",-1)),o("span",J,v(le.value.specification),1)]),o("div",O,[a[13]||(a[13]=o("span",{class:"label"},"当前库存:",-1)),o("span",Q,v(le.value.currentStock),1)])])])):c("",!0),le.value?c("",!0):(b(),n("div",Y,[d(t,{type:"primary",block:"",onClick:ye},{default:i((()=>a[14]||(a[14]=[_("选择备件")]))),_:1})])),le.value?(b(),n("div",$,[d(x,{model:ce,"label-position":"top"},{default:i((()=>[d(f,{label:"出库数量"},{default:i((()=>{var e;return[d(r,{modelValue:ce.quantity,"onUpdate:modelValue":a[1]||(a[1]=e=>ce.quantity=e),min:1,max:(null==(e=le.value)?void 0:e.currentStock)||1,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue","max"])]})),_:1}),d(f,{label:"出库原因"},{default:i((()=>[d(q,{modelValue:ce.reasonType,"onUpdate:modelValue":a[2]||(a[2]=e=>ce.reasonType=e),placeholder:"选择出库原因",style:{width:"100%"}},{default:i((()=>[d(S,{label:"领用出库",value:3}),d(S,{label:"报废出库",value:4}),d(S,{label:"盘点调整",value:5})])),_:1},8,["modelValue"])])),_:1}),d(f,{label:"库位"},{default:i((()=>[d(q,{modelValue:ce.locationId,"onUpdate:modelValue":a[3]||(a[3]=e=>ce.locationId=e),filterable:"",placeholder:"选择库位",style:{width:"100%"}},{default:i((()=>[(b(!0),n(I,null,g(ue.value,(e=>(b(),w(S,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),3===ce.reasonType?(b(),w(f,{key:0,label:"领用人"},{default:i((()=>[d(q,{modelValue:ce.receiverId,"onUpdate:modelValue":a[4]||(a[4]=e=>ce.receiverId=e),filterable:"",placeholder:"选择领用人",style:{width:"100%"}},{default:i((()=>[(b(!0),n(I,null,g(oe.value,(e=>(b(),w(S,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})):c("",!0),d(f,{label:"关联单号"},{default:i((()=>[d(s,{modelValue:ce.referenceNumber,"onUpdate:modelValue":a[5]||(a[5]=e=>ce.referenceNumber=e),placeholder:"选填，相关单号"},null,8,["modelValue"])])),_:1}),d(f,{label:"备注"},{default:i((()=>[d(s,{modelValue:ce.remarks,"onUpdate:modelValue":a[6]||(a[6]=e=>ce.remarks=e),type:"textarea",placeholder:"选填，备注信息",rows:3},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])):c("",!0),le.value?(b(),n("div",B,[d(t,{type:"warning",block:"",onClick:ke,loading:re.value},{default:i((()=>a[15]||(a[15]=[_(" 确认出库 ")]))),_:1},8,["loading"])])):c("",!0),d(ee,{modelValue:te.value,"onUpdate:modelValue":a[8]||(a[8]=e=>te.value=e),title:"选择备件",direction:"bottom",size:"90%"},{default:i((()=>[o("div",D,[d(s,{modelValue:se.value,"onUpdate:modelValue":a[7]||(a[7]=e=>se.value=e),placeholder:"搜索备件",clearable:""},{prefix:i((()=>[d(l,null,{default:i((()=>[d(m(h))])),_:1})])),_:1},8,["modelValue"])]),o("div",E,[(b(!0),n(I,null,g(de.value,((e,a)=>(b(),n("div",{key:a,class:"part-item",onClick:a=>(e=>{e.currentStock<=0?u.warning("该备件库存为0，无法出库"):(le.value=e,ce.partId=e.id,ce.quantity=Math.min(1,e.currentStock),te.value=!1)})(e)},[o("div",P,[o("div",R,v(e.name),1),o("div",W,v(e.code),1)]),o("div",X,[o("span",Z,"库存: "+v(e.currentStock),1),d(l,null,{default:i((()=>[d(m(C))])),_:1})])],8,K)))),128))])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-008678d0"]]);export{ee as default};
