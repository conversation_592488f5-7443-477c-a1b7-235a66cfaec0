<template>
  <el-dialog
    v-model="visible"
    title="创建返厂维修单"
    width="80%"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      v-loading="loading"
    >
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="维修类型" prop="type">
            <el-select v-model="form.type" placeholder="请选择维修类型" style="width: 100%">
              <el-option label="故障维修" :value="1" />
              <el-option label="备件维修" :value="2" />
              <el-option label="预防性维修" :value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="优先级" prop="priority">
            <el-select v-model="form.priority" placeholder="请选择优先级" style="width: 100%">
              <el-option label="紧急" :value="1" />
              <el-option label="高" :value="2" />
              <el-option label="中" :value="3" />
              <el-option label="低" :value="4" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="维修标题" prop="title">
        <el-input v-model="form.title" placeholder="请输入维修标题" />
      </el-form-item>

      <el-form-item label="维修描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入维修描述"
        />
      </el-form-item>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="关联故障">
            <el-select
              v-model="form.faultId"
              placeholder="选择关联故障（可选）"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="fault in faults"
                :key="fault.id"
                :label="fault.title"
                :value="fault.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="维修供应商" prop="supplierId">
            <el-select
              v-model="form.supplierId"
              placeholder="选择维修供应商"
              style="width: 100%"
            >
              <el-option
                v-for="supplier in suppliers"
                :key="supplier.id"
                :label="supplier.name"
                :value="supplier.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="预估费用">
            <el-input-number
              v-model="form.estimatedCost"
              :min="0"
              :precision="2"
              placeholder="预估费用"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预估天数">
            <el-input-number
              v-model="form.estimatedDays"
              :min="1"
              placeholder="预估维修天数"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="备注">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="2"
          placeholder="请输入备注"
        />
      </el-form-item>

      <!-- 选择维修物品 -->
      <el-form-item label="维修物品" prop="selectedItems">
        <div class="item-selection">
          <el-button type="primary" @click="showItemSelector">选择物品</el-button>
          <div v-if="form.selectedItems.length > 0" class="selected-items">
            <div
              v-for="item in form.selectedItems"
              :key="item.id"
              class="selected-item"
            >
              <div class="item-info">
                <span class="item-name">{{ item.partName }}</span>
                <span class="item-code">{{ item.partCode }}</span>
                <span class="item-quantity">{{ item.quantity }}个</span>
              </div>
              <div class="item-actions">
                <el-button
                  type="text"
                  size="small"
                  @click="removeItem(item.id)"
                >
                  移除
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          创建返厂单
        </el-button>
      </div>
    </template>

    <!-- 物品选择对话框 -->
    <ItemSelectorDialog
      v-model="itemSelectorVisible"
      @confirm="handleItemsSelected"
    />
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import ItemSelectorDialog from './ItemSelectorDialog.vue'
import { getFaults } from '@/api/faults'
import { getMaintenanceSuppliers } from '@/api/spareparts'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const submitting = ref(false)
const itemSelectorVisible = ref(false)

const formRef = ref()
const faults = ref([])
const suppliers = ref([])

// 表单数据
const form = reactive({
  type: 2, // 默认备件维修
  title: '',
  description: '',
  priority: 3, // 默认中等优先级
  faultId: null,
  supplierId: null,
  estimatedCost: null,
  estimatedDays: null,
  notes: '',
  selectedItems: []
})

// 表单验证规则
const rules = {
  type: [{ required: true, message: '请选择维修类型', trigger: 'change' }],
  title: [{ required: true, message: '请输入维修标题', trigger: 'blur' }],
  description: [{ required: true, message: '请输入维修描述', trigger: 'blur' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
  supplierId: [{ required: true, message: '请选择维修供应商', trigger: 'change' }],
  selectedItems: [
    {
      type: 'array',
      min: 1,
      message: '请至少选择一个维修物品',
      trigger: 'change'
    }
  ]
}

// 方法
const handleClose = () => {
  visible.value = false
  emit('update:modelValue', false)
  resetForm()
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    type: 2,
    title: '',
    description: '',
    priority: 3,
    faultId: null,
    supplierId: null,
    estimatedCost: null,
    estimatedDays: null,
    notes: '',
    selectedItems: []
  })
}

const showItemSelector = () => {
  itemSelectorVisible.value = true
}

const handleItemsSelected = (items) => {
  form.selectedItems = items
}

const removeItem = (itemId) => {
  const index = form.selectedItems.findIndex(item => item.id === itemId)
  if (index > -1) {
    form.selectedItems.splice(index, 1)
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    // TODO: 调用API创建返厂维修单
    // const response = await repairOrderApi.createRepairOrder(form)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('返厂维修单创建成功')
    emit('success')
    handleClose()
  } catch (error) {
    if (error.message) {
      ElMessage.error('创建失败: ' + error.message)
    }
  } finally {
    submitting.value = false
  }
}

const loadData = async () => {
  loading.value = true
  try {
    // 加载故障列表和供应商列表
    const [faultsRes, suppliersRes] = await Promise.all([
      getFaults({ status: 0 }), // 获取待处理的故障
      getMaintenanceSuppliers() // 获取维修供应商
    ])

    if (faultsRes.success) {
      faults.value = faultsRes.data.items || faultsRes.data || []
    }

    if (suppliersRes.success) {
      suppliers.value = suppliersRes.data || []
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 监听器
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    loadData()
  }
})

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.item-selection {
  width: 100%;
}

.selected-items {
  margin-top: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px;
  background-color: #f5f7fa;
}

.selected-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  margin-bottom: 8px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.selected-item:last-child {
  margin-bottom: 0;
}

.item-info {
  display: flex;
  gap: 12px;
  align-items: center;
}

.item-name {
  font-weight: 500;
  color: #303133;
}

.item-code {
  color: #909399;
  font-size: 12px;
}

.item-quantity {
  color: #67c23a;
  font-size: 12px;
}
</style>
