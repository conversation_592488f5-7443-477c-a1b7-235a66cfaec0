import{_ as e,r as s,l as t,b as a,d as i,e as r,w as c,a as n,o,A as l,t as u,ba as d,a8 as m}from"./index-C7OOw0MO.js";import{N as p}from"./NotificationCenter-CE1GE5Vk.js";import"./notification-service-BchTNjTa.js";const g={class:"notification-test"},f={class:"test-results"},v=e({__name:"NotificationTest",setup(e){const v=s(!1),y=s({}),w=t(),S=async()=>{try{const e=await d.getNotifications();y.value.getNotifications={success:e.success,data:e.data,timestamp:(new Date).toISOString()},e.success?m.success(`获取成功，共${e.data.notifications.length}条通知`):m.error("获取失败: "+e.message)}catch(e){y.value.getNotifications={error:e.message,timestamp:(new Date).toISOString()},m.error("测试失败: "+e.message)}},_=async()=>{try{const e=await d.sendTestNotification();y.value.sendNotification={success:e.success,message:e.message,timestamp:(new Date).toISOString()},e.success?(m.success("测试通知发送成功"),setTimeout((()=>{w.fetchNotifications(!0)}),1e3)):m.error("发送失败: "+e.message)}catch(e){y.value.sendNotification={error:e.message,timestamp:(new Date).toISOString()},m.error("发送失败: "+e.message)}},N=async()=>{try{const e=await d.getUnreadCount();y.value.getUnreadCount={success:e.success,data:e.data,timestamp:(new Date).toISOString()},e.success?m.success(`未读通知数量: ${e.data}`):m.error("获取失败: "+e.message)}catch(e){y.value.getUnreadCount={error:e.message,timestamp:(new Date).toISOString()},m.error("获取失败: "+e.message)}};return(e,s)=>{const t=n("el-button"),d=n("el-space"),m=n("el-card");return o(),a("div",g,[s[9]||(s[9]=i("h2",null,"通知系统测试页面",-1)),r(m,{class:"test-card"},{header:c((()=>s[2]||(s[2]=[i("span",null,"API测试",-1)]))),default:c((()=>[r(d,{direction:"vertical",style:{width:"100%"}},{default:c((()=>[r(t,{onClick:S,type:"primary"},{default:c((()=>s[3]||(s[3]=[l(" 获取通知列表 ")]))),_:1}),r(t,{onClick:_,type:"success"},{default:c((()=>s[4]||(s[4]=[l(" 发送测试通知 ")]))),_:1}),r(t,{onClick:N,type:"info"},{default:c((()=>s[5]||(s[5]=[l(" 获取未读数量 ")]))),_:1})])),_:1})])),_:1}),r(m,{class:"test-card",style:{"margin-top":"20px"}},{header:c((()=>s[6]||(s[6]=[i("span",null,"通知中心组件测试",-1)]))),default:c((()=>[r(p,{mode:"drawer",visible:v.value,"onUpdate:visible":s[0]||(s[0]=e=>v.value=e)},null,8,["visible"]),r(t,{onClick:s[1]||(s[1]=e=>v.value=!0),type:"primary"},{default:c((()=>s[7]||(s[7]=[l(" 打开通知中心 (Drawer模式) ")]))),_:1})])),_:1}),r(m,{class:"test-card",style:{"margin-top":"20px"}},{header:c((()=>s[8]||(s[8]=[i("span",null,"测试结果",-1)]))),default:c((()=>[i("div",f,[i("pre",null,u(JSON.stringify(y.value,null,2)),1)])])),_:1})])}}},[["__scopeId","data-v-d638c4d9"]]);export{v as default};
