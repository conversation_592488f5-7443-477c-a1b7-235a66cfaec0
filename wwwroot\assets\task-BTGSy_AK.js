import{aj as s}from"./index-C7OOw0MO.js";const e={getTaskList(e){const t={pageNumber:e.pageNumber||1,pageSize:e.pageSize||10,status:e.status||void 0,priority:e.priority||void 0,assigneeUserId:e.assigneeUserId||void 0,searchTerm:e.searchTerm||void 0};return s.get("/v2/tasks",{params:t})},getTasks(s){return this.getTaskList(s)},getTaskDetail:e=>s.get(`/v2/tasks/${e}`),getTaskById(s){return this.getTaskDetail(s)},createTask(e){const t={name:e.name,description:e.description||"",priority:e.priority||"Medium",status:e.status||"Todo",taskType:e.taskType||"Normal",planStartDate:e.planStartDate,planEndDate:e.planEndDate,assigneeUserId:e.assigneeUserId,parentTaskId:e.parentTaskId,projectId:e.projectId,assetId:e.assetId,locationId:e.locationId,points:e.points||0,pDCAStage:e.pDCAStage||""};return e.assigneeUserId&&Array.isArray(e.collaboratorUserIds)?t.collaboratorUserIds=e.collaboratorUserIds:Array.isArray(e.assigneeUserIds)&&e.assigneeUserIds.length>0&&(e.assigneeUserIds.length>1?(t.collaboratorUserIds=e.assigneeUserIds.slice(1),t.assigneeUserId||(t.assigneeUserId=e.assigneeUserIds[0])):(t.assigneeUserId||(t.assigneeUserId=e.assigneeUserIds[0]),t.collaboratorUserIds=[])),s({url:"/v2/tasks",method:"post",data:t})},updateTask(e,t){if(!e||0===e||"0"===e)return Promise.reject(new Error("任务ID不能为空或为0"));const a={...t};return t.assigneeUserId&&Array.isArray(t.collaboratorUserIds)||(Array.isArray(t.assigneeUserIds)&&t.assigneeUserIds.length>0?(a.assigneeUserId=t.assigneeUserIds[0],a.collaboratorUserIds=t.assigneeUserIds.slice(1),delete a.assigneeUserIds):t.assigneeUserId||Array.isArray(t.collaboratorUserIds)&&t.collaboratorUserIds.length),s({url:`/v2/tasks/${e}`,method:"put",data:a})},updateTaskStatus:(e,t)=>s.patch(`/v2/tasks/${e}/status`,t),updateTaskProgress:(e,t)=>s.patch(`/v2/tasks/${e}/progress`,t),assignTask:(e,t)=>s.patch(`/v2/tasks/${e}/assign`,t),completeTask:(e,t={})=>s.patch(`/v2/tasks/${e}/complete`,t),deleteTask:e=>s.delete(`/v2/tasks/${e}`),addComment:(e,t)=>s.post(`/v2/tasks/${e}/comments`,t),getComments:(e,t)=>s.get(`/v2/tasks/${e}/comments`,{params:t}),uploadTaskAttachment:(e,t)=>s.post(`/v2/tasks/${e}/attachments`,t,{headers:{"Content-Type":"multipart/form-data"}}),getTaskAttachments:e=>s.get(`/v2/tasks/${e}/attachments`),deleteTaskAttachment:(e,t)=>s.delete(`/v2/tasks/${e}/attachments/${t}`),getTaskActivityLog:(e,t)=>s.get(`/v2/tasks/${e}/history`,{params:t}),getTaskStatusOptions:()=>s.get("/v2/tasks/options/statuses"),getTaskPriorityOptions:()=>s.get("/v2/tasks/options/priorities"),getTaskTypeOptions:()=>s.get("/v2/tasks/options/types"),getComments:e=>s.get(`/v2/tasks/${e}/comments`),getTaskHistory:e=>s.get(`/v2/tasks/${e}/history`),recordTaskView:e=>s.post(`/v2/tasks/${e}/view`),getTodayViewedCount:()=>s.get("/v2/tasks/viewed-today")};e.getTaskComments=e.getComments,e.getTaskHistory=e.getTaskActivityLog;export{e as t};
