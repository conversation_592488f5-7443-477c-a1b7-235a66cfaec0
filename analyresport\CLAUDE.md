# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Backend (.NET 6)
```bash
# Development
dotnet run
dotnet watch run

# Database migrations  
dotnet ef migrations add <MigrationName>
dotnet ef database update

# Build and test
dotnet build
dotnet publish
```

### Frontend (Vue 3 + Vite)
```bash
# Development server (http://localhost:5173)
npm run dev

# Production build
npm run build

# Preview production build
npm run preview
```

## Architecture Overview

This is an **IT Assets Management System** with a **dual-track architecture**:

### Clean Architecture Implementation
- **Domain Layer**: Business entities and rules (`Domain/Entities/`)
- **Application Layer**: Use cases, DTOs, services (`Application/Features/`)
- **Infrastructure Layer**: Data access, external services (`Infrastructure/`)
- **API Layer**: Controllers and endpoints (`Api/`, `Controllers/`)

### Dual-Track Development Pattern
- **Core/Legacy Modules (V1)**: Frozen codebase with INT primary keys, mixed data access patterns
- **New Modules (V2)**: BIGINT primary keys, pure EF Core + CQRS pattern, `/api/v2/` routing

## Critical Development Rules

### Code Generation Rules (Mandatory)
1. **File Structure**: All new code must follow Clean Architecture in Domain/Application/Infrastructure/Api layers
2. **API Standards**: 
   - V2 APIs must use DTOs and standard JSON responses `{success, data, message?, error?, pagination?}`
   - V1 APIs are **FROZEN** - no modifications allowed
3. **Database Schema**:
   - New tables use **BIGINT AUTO_INCREMENT** primary keys
   - Core tables keep **INT** primary keys - never modify
4. **Data Access**: New modules must use pure EF Core + Repository pattern
5. **V2 Module Requirements**: Use `/api/v2/` routing, DTOs in `Application/Features/.../Dtos`

### Core vs New Module Boundaries
- **Core Modules (FROZEN)**: Assets, Locations, Users, Departments, Faults, Purchases
- **New Modules (V2)**: Tasks, SpareParts, QuickMemos, Gamification
- New modules can only READ from core tables (via V1 APIs or EF Core AsNoTracking)

## Key Technical Specifications

### Backend Stack
- .NET 6 with Entity Framework Core 6
- MySQL primary database, SQLite fallback
- JWT authentication with Serilog logging
- MediatR for CQRS in V2 modules

### Frontend Stack  
- Vue 3 + Composition API
- Element Plus UI framework
- Pinia state management
- Axios with proxy to backend (port 5001)

### Database Configuration
- Connection string in `appsettings.json`
- Auto-migration on startup
- Backup/restore functionality built-in

## Change Management

All file modifications must be recorded in `CHANGELOG.md` using this format:

| 操作 | 路径 | 说明 | 是否有问题 |
| --- | --- | --- | --- |
| 创建/修改/删除 | 文件相对路径 | 修改详细描述 | 已解决/未解决/部分解决 |

## Main Feature Modules

### Core Business Features
- **Asset Management**: IT asset lifecycle tracking
- **Location Management**: Hierarchical location structures  
- **Fault Management**: Equipment fault tracking and maintenance
- **Purchase Management**: Procurement workflow

### V2 New Features
- **Task Management**: Regular, periodic, and PDCA tasks with gamification
- **Spare Parts Management**: Inventory management system
- **Quick Memo System**: Note-taking with 3D visualization

### System Features
- **Plugin System**: Extensible architecture with dynamic loading
- **Offline Support**: Network resilience with operation queuing
- **Import/Export**: CSV/Excel data exchange capabilities
- **Backup/Restore**: Automated data protection

## Development Environment Setup

1. **Prerequisites**: .NET 6 SDK, Node.js, MySQL server (or SQLite for development)
2. **Backend**: Runs on `https://localhost:5001`
3. **Frontend**: Runs on `http://localhost:5173` with proxy to backend
4. **Database**: Auto-initializes directories for logs, backups, imports/exports

When developing new features, always follow V2 patterns and ensure changes are properly logged in CHANGELOG.md.