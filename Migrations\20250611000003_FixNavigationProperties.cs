using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ItAssetsSystem.Migrations
{
    /// <summary>
    /// 修复导航属性配置，忽略NotMapped属性避免EF Core生成错误SQL
    /// </summary>
    public partial class FixNavigationProperties : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // 这个迁移主要是为了更新模型配置
            // 确保EF Core正确忽略NotMapped的导航属性
            migrationBuilder.Sql("SELECT 1"); // 空操作，只是为了触发模型重新构建
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // 无需回滚操作
        }
    }
}