#!/usr/bin/env python3
"""
高级打包脚本 - 包含代码混淆和额外保护
"""

import os
import sys
import subprocess
import shutil
import tempfile
from pathlib import Path

def install_build_dependencies():
    """安装构建依赖"""
    dependencies = [
        "pyinstaller>=5.0.0",
        "pyarmor",  # 代码混淆工具
        "upx",      # 可执行文件压缩工具（可选）
    ]
    
    print("📦 安装构建依赖...")
    for dep in dependencies:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                         check=True, capture_output=True)
            print(f"✅ 已安装: {dep}")
        except subprocess.CalledProcessError:
            print(f"⚠️  安装失败: {dep} (可能需要手动安装)")

def obfuscate_code():
    """使用PyArmor混淆代码"""
    print("🔒 开始代码混淆...")
    
    # 创建临时目录用于混淆后的代码
    obfuscated_dir = Path("obfuscated_src")
    if obfuscated_dir.exists():
        shutil.rmtree(obfuscated_dir)
    
    try:
        # 初始化PyArmor
        subprocess.run(["pyarmor", "init", "--src", ".", "--entry", "main.py"], 
                      check=True, capture_output=True)
        
        # 混淆代码
        subprocess.run(["pyarmor", "build", "--output", str(obfuscated_dir)], 
                      check=True, capture_output=True)
        
        print("✅ 代码混淆完成")
        return obfuscated_dir
        
    except subprocess.CalledProcessError as e:
        print(f"⚠️  代码混淆失败: {e}")
        print("将使用原始代码进行打包...")
        return None
    except FileNotFoundError:
        print("⚠️  PyArmor 未安装，跳过代码混淆...")
        return None

def create_protected_spec():
    """创建带保护的spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-
import sys
from PyInstaller.utils.hooks import collect_all

# 收集所有隐藏导入
datas = []
binaries = []
hiddenimports = []

# 添加项目模块
for module in ['augment_tools_core', 'click', 'colorama']:
    try:
        tmp_datas, tmp_binaries, tmp_hiddenimports = collect_all(module)
        datas += tmp_datas
        binaries += tmp_binaries
        hiddenimports += tmp_hiddenimports
    except:
        pass

# 手动添加关键模块
hiddenimports.extend([
    'augment_tools_core',
    'augment_tools_core.cli',
    'augment_tools_core.common_utils',
    'augment_tools_core.database_manager',
    'augment_tools_core.telemetry_manager',
    'click',
    'colorama',
    'sqlite3',
    'json',
    'pathlib',
    'subprocess',
    'uuid',
    'random',
    'string',
])

# 添加数据文件
datas.append(('README.md', '.'))

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'torch',
        'tensorflow',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 移除不需要的模块以减小体积
a.binaries = [x for x in a.binaries if not any(
    exclude in x[0].lower() for exclude in [
        'qt', 'tk', 'tcl', 'matplotlib', 'numpy', 'scipy'
    ]
)]

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AugmentCode-Free',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,  # 移除调试信息
    upx=True,    # 启用UPX压缩
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件
)
'''
    
    with open('protected.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("📝 创建了保护性spec文件")

def build_with_protection():
    """使用保护性配置构建"""
    print("🔨 开始保护性构建...")
    
    # 清理旧文件
    for path in ['build', 'dist']:
        if os.path.exists(path):
            shutil.rmtree(path)
    
    # 构建
    cmd = [sys.executable, "-m", "PyInstaller", "protected.spec", "--clean", "--noconfirm"]
    subprocess.run(cmd, check=True)

def compress_executable():
    """压缩可执行文件（如果UPX可用）"""
    exe_path = Path("dist/AugmentCode-Free")
    if sys.platform == "win32":
        exe_path = exe_path.with_suffix(".exe")
    
    if not exe_path.exists():
        print("⚠️  找不到可执行文件，跳过压缩")
        return
    
    try:
        # 检查UPX是否可用
        subprocess.run(["upx", "--version"], check=True, capture_output=True)
        
        print("🗜️  压缩可执行文件...")
        subprocess.run(["upx", "--best", str(exe_path)], check=True)
        print("✅ 压缩完成")
        
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️  UPX 不可用，跳过压缩")

def create_distribution_package():
    """创建分发包"""
    print("📦 创建分发包...")
    
    dist_dir = Path("distribution")
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    dist_dir.mkdir()
    
    # 复制可执行文件
    exe_name = "AugmentCode-Free"
    if sys.platform == "win32":
        exe_name += ".exe"
    
    src_exe = Path("dist") / exe_name
    if src_exe.exists():
        shutil.copy2(src_exe, dist_dir / exe_name)
    
    # 复制README
    if Path("README.md").exists():
        shutil.copy2("README.md", dist_dir / "README.md")
    
    # 创建使用说明
    usage_content = """# AugmentCode-Free 使用说明

## 运行方式
直接双击可执行文件或在命令行中运行：

### Windows:
```
AugmentCode-Free.exe
```

### Linux/macOS:
```
./AugmentCode-Free
```

## 功能说明
1. 清理 VS Code 数据库中的特定条目
2. 修改 VS Code 遥测ID
3. 运行所有工具

## 注意事项
- 请在使用前关闭所有 VS Code 实例
- 建议在使用前备份重要数据
- 本工具仅用于合法的软件维护目的

## 技术支持
如有问题，请联系开发者。
"""
    
    with open(dist_dir / "使用说明.txt", "w", encoding="utf-8") as f:
        f.write(usage_content)
    
    print(f"✅ 分发包已创建: {dist_dir.absolute()}")

def main():
    """主函数"""
    print("🚀 AugmentCode-Free 高级打包工具")
    print("=" * 60)
    
    try:
        # 1. 安装构建依赖
        install_build_dependencies()
        
        # 2. 代码混淆（可选）
        obfuscated_dir = obfuscate_code()
        
        # 3. 创建保护性spec文件
        create_protected_spec()
        
        # 4. 构建
        build_with_protection()
        
        # 5. 压缩（可选）
        compress_executable()
        
        # 6. 创建分发包
        create_distribution_package()
        
        print("\n🎉 高级打包完成！")
        print("📁 分发包位置: distribution/")
        print("🔒 源码已完全隐藏并受到保护")
        
        # 清理临时文件
        if obfuscated_dir and obfuscated_dir.exists():
            shutil.rmtree(obfuscated_dir)
        
        for temp_file in ["protected.spec", ".pyarmor_config"]:
            if os.path.exists(temp_file):
                os.remove(temp_file)
        
    except Exception as e:
        print(f"❌ 构建失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
