import{_ as a,r as e,a5 as l,c as s,m as t,a8 as o,b as i,d,e as r,aq as n,a9 as u,w as p,aJ as c,Y as v,F as m,h as y,a as f,u as g,o as b,A as h,t as _,i as k,f as C,bG as D,bH as V,L as w,B as N}from"./index-CkwLz8y6.js";import{d as x}from"./spareparts-Cv2l4Tzu.js";const z={class:"mobile-spare-part-view"},I={class:"mobile-header"},T={class:"actions"},S={class:"mobile-filter-form"},q={class:"form-actions"},B={class:"quick-actions"},U={class:"record-list"},O={class:"card-header"},Y={class:"card-id"},M={class:"card-body"},j={class:"info-row"},L={class:"value"},P={class:"info-row"},A={class:"value"},F={class:"info-row"},G={class:"info-row"},H={class:"value"},J={class:"info-row"},E={class:"value"},K={class:"info-row"},Q={class:"value"},R={class:"info-row"},W={class:"value"},X={key:0,class:"load-more"},Z={key:1,class:"no-more"},$={class:"bottom-nav"},aa=a({__name:"MobileSparePartView",setup(a){const aa=g(),ea=e(!1),la=e(!1),sa=e([]),ta=e(0),oa=e([]),ia=e(!1),da=e(!1),ra=e(!1),na=l({pageIndex:1,pageSize:10,partName:"",partCode:"",type:"",reasonType:"",startDate:"",endDate:"",sortBy:"operationTime",sortOrder:"desc"});s((()=>(oa.value&&2===oa.value.length?(na.startDate=oa.value[0],na.endDate=oa.value[1]):(na.startDate="",na.endDate=""),oa.value))),t((()=>{ua()}));const ua=async()=>{ea.value=!0;try{const a={pageIndex:na.pageIndex,pageSize:na.pageSize,partName:na.partName||void 0,partCode:na.partCode||void 0,type:na.type||void 0,reasonType:na.reasonType||void 0,startDate:na.startDate||void 0,endDate:na.endDate||void 0,sortBy:na.sortBy||void 0,sortOrder:na.sortOrder||void 0},e=await x(a);e.success?(sa.value=e.data.items,ta.value=e.data.totalCount,ra.value=sa.value.length>=ta.value):o.error(e.message||"获取出入库记录失败")}catch(a){o.error("获取出入库记录失败，请稍后重试")}finally{ea.value=!1}},pa=async()=>{if(!la.value&&!ra.value){la.value=!0,na.pageIndex+=1;try{const a={pageIndex:na.pageIndex,pageSize:na.pageSize,partName:na.partName||void 0,partCode:na.partCode||void 0,type:na.type||void 0,reasonType:na.reasonType||void 0,startDate:na.startDate||void 0,endDate:na.endDate||void 0,sortBy:na.sortBy||void 0,sortOrder:na.sortOrder||void 0},e=await x(a);e.success?(sa.value=[...sa.value,...e.data.items],ra.value=sa.value.length>=ta.value):o.error(e.message||"加载更多数据失败")}catch(a){o.error("加载更多数据失败，请稍后重试")}finally{la.value=!1}}},ca=a=>{if(!a)return"";return new Date(a).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",hour12:!1})},va=()=>{na.pageIndex=1,ia.value=!1,ua()},ma=()=>{na.partName="",na.partCode="",na.type="",na.reasonType="",oa.value=[],na.startDate="",na.endDate="",na.pageIndex=1},ya=()=>{ia.value=!0},fa=()=>{da.value=!0},ga=()=>{da.value=!1,aa.push("/main/spareparts/mobile/quick-in")},ba=()=>{da.value=!1,aa.push("/main/spareparts/mobile/quick-out")},ha=()=>{aa.push("/main/dashboard")},_a=()=>{},ka=()=>{aa.push("/main/spareparts/list")},Ca=()=>{aa.push("/main/user/profile")};return(a,e)=>{const l=f("el-button"),s=f("el-input"),t=f("el-form-item"),o=f("el-option"),g=f("el-select"),x=f("el-date-picker"),aa=f("el-form"),ta=f("el-drawer"),ua=f("el-empty"),Da=f("el-tag"),Va=f("el-icon"),wa=c("loading");return b(),i("div",z,[d("div",I,[e[6]||(e[6]=d("div",{class:"title"},"备件管理",-1)),d("div",T,[r(l,{type:"primary",size:"small",circle:"",icon:"Plus",onClick:fa}),r(l,{size:"small",circle:"",icon:"Search",onClick:ya})])]),r(ta,{modelValue:ia.value,"onUpdate:modelValue":e[4]||(e[4]=a=>ia.value=a),title:"筛选条件",direction:"right",size:"90%"},{default:p((()=>[d("div",S,[r(aa,{model:na,"label-position":"top"},{default:p((()=>[r(t,{label:"备件名称"},{default:p((()=>[r(s,{modelValue:na.partName,"onUpdate:modelValue":e[0]||(e[0]=a=>na.partName=a),placeholder:"输入备件名称"},null,8,["modelValue"])])),_:1}),r(t,{label:"备件编号"},{default:p((()=>[r(s,{modelValue:na.partCode,"onUpdate:modelValue":e[1]||(e[1]=a=>na.partCode=a),placeholder:"输入备件编号"},null,8,["modelValue"])])),_:1}),r(t,{label:"操作类型"},{default:p((()=>[r(g,{modelValue:na.type,"onUpdate:modelValue":e[2]||(e[2]=a=>na.type=a),placeholder:"选择操作类型",style:{width:"100%"}},{default:p((()=>[r(o,{label:"全部",value:null}),r(o,{label:"入库",value:1}),r(o,{label:"出库",value:2})])),_:1},8,["modelValue"])])),_:1}),r(t,{label:"时间范围"},{default:p((()=>[r(x,{modelValue:oa.value,"onUpdate:modelValue":e[3]||(e[3]=a=>oa.value=a),type:"daterange",style:{width:"100%"},"start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),d("div",q,[r(l,{type:"primary",onClick:va},{default:p((()=>e[7]||(e[7]=[h("查询")]))),_:1}),r(l,{onClick:ma},{default:p((()=>e[8]||(e[8]=[h("重置")]))),_:1})])])),_:1},8,["model"])])])),_:1},8,["modelValue"]),r(ta,{modelValue:da.value,"onUpdate:modelValue":e[5]||(e[5]=a=>da.value=a),title:"快速操作",direction:"right",size:"90%"},{default:p((()=>[d("div",B,[r(l,{type:"primary",onClick:ga,block:""},{default:p((()=>e[9]||(e[9]=[h("快速入库")]))),_:1}),e[11]||(e[11]=d("div",{style:{margin:"10px 0"}},null,-1)),r(l,{type:"warning",onClick:ba,block:""},{default:p((()=>e[10]||(e[10]=[h("快速出库")]))),_:1})])])),_:1},8,["modelValue"]),n((b(),i("div",U,[0===sa.value.length?(b(),v(ua,{key:0,description:"暂无数据"})):u("",!0),(b(!0),i(m,null,y(sa.value,((a,l)=>(b(),i("div",{key:l,class:"record-card"},[d("div",O,[r(Da,{type:1===a.type?"success":"warning"},{default:p((()=>[h(_(a.typeName),1)])),_:2},1032,["type"]),d("span",Y,"#"+_(a.id),1)]),d("div",M,[d("div",j,[e[12]||(e[12]=d("span",{class:"label"},"备件",-1)),d("span",L,_(a.partName),1)]),d("div",P,[e[13]||(e[13]=d("span",{class:"label"},"编号",-1)),d("span",A,_(a.partCode),1)]),d("div",F,[e[14]||(e[14]=d("span",{class:"label"},"数量",-1)),d("span",{class:k(["value",1===a.type?"quantity-in":"quantity-out"])},_(1===a.type?"+":"-")+_(Math.abs(a.quantity)),3)]),d("div",G,[e[15]||(e[15]=d("span",{class:"label"},"库位",-1)),d("span",H,_(a.locationName),1)]),d("div",J,[e[16]||(e[16]=d("span",{class:"label"},"原因",-1)),d("span",E,_(a.reasonTypeName),1)]),d("div",K,[e[17]||(e[17]=d("span",{class:"label"},"操作人",-1)),d("span",Q,_(a.operatorName),1)]),d("div",R,[e[18]||(e[18]=d("span",{class:"label"},"时间",-1)),d("span",W,_(ca(a.operationTime)),1)])])])))),128))])),[[wa,ea.value]]),sa.value.length>0&&!ra.value?(b(),i("div",X,[r(l,{link:"",onClick:pa,loading:la.value},{default:p((()=>e[19]||(e[19]=[h("加载更多")]))),_:1},8,["loading"])])):u("",!0),ra.value&&sa.value.length>0?(b(),i("div",Z,e[20]||(e[20]=[d("span",null,"没有更多数据了",-1)]))):u("",!0),d("div",$,[d("div",{class:"nav-item",onClick:ha},[r(Va,null,{default:p((()=>[r(C(D))])),_:1}),e[21]||(e[21]=d("span",null,"首页",-1))]),d("div",{class:"nav-item active",onClick:_a},[r(Va,null,{default:p((()=>[r(C(V))])),_:1}),e[22]||(e[22]=d("span",null,"出入库",-1))]),d("div",{class:"nav-item",onClick:ka},[r(Va,null,{default:p((()=>[r(C(w))])),_:1}),e[23]||(e[23]=d("span",null,"备件",-1))]),d("div",{class:"nav-item",onClick:Ca},[r(Va,null,{default:p((()=>[r(C(N))])),_:1}),e[24]||(e[24]=d("span",null,"我的",-1))])])])}}},[["__scopeId","data-v-41d9ba81"]]);export{aa as default};
