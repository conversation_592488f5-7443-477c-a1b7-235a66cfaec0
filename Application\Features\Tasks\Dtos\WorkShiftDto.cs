// File: Application/Features/Tasks/Dtos/WorkShiftDto.cs
// Description: 班次DTO

using System;
using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Application.Features.Tasks.Dtos
{
    /// <summary>
    /// 班次DTO
    /// </summary>
    public class WorkShiftDto
    {
        /// <summary>
        /// 班次ID
        /// </summary>
        public long ShiftId { get; set; }

        /// <summary>
        /// 班次名称
        /// </summary>
        public string ShiftName { get; set; } = string.Empty;

        /// <summary>
        /// 班次代码
        /// </summary>
        public string ShiftCode { get; set; } = string.Empty;

        /// <summary>
        /// 班次类型
        /// </summary>
        public string ShiftType { get; set; } = string.Empty;

        /// <summary>
        /// 开始时间
        /// </summary>
        public TimeSpan StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public TimeSpan EndTime { get; set; }

        /// <summary>
        /// 任务领取时间
        /// </summary>
        public TimeSpan TaskClaimTime { get; set; }

        /// <summary>
        /// 是否跨天
        /// </summary>
        public bool IsOvernight { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// 创建班次DTO
    /// </summary>
    public class CreateWorkShiftDto
    {
        /// <summary>
        /// 班次名称
        /// </summary>
        [Required(ErrorMessage = "班次名称不能为空")]
        [MaxLength(50, ErrorMessage = "班次名称长度不能超过50个字符")]
        public string ShiftName { get; set; } = string.Empty;

        /// <summary>
        /// 班次代码
        /// </summary>
        [Required(ErrorMessage = "班次代码不能为空")]
        [MaxLength(20, ErrorMessage = "班次代码长度不能超过20个字符")]
        public string ShiftCode { get; set; } = string.Empty;

        /// <summary>
        /// 班次类型 (Day-白班, Night-夜班, Swing-中班)
        /// </summary>
        [Required(ErrorMessage = "班次类型不能为空")]
        public string ShiftType { get; set; } = "Day";

        /// <summary>
        /// 开始时间
        /// </summary>
        [Required(ErrorMessage = "开始时间不能为空")]
        public TimeSpan StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        [Required(ErrorMessage = "结束时间不能为空")]
        public TimeSpan EndTime { get; set; }

        /// <summary>
        /// 任务领取时间
        /// </summary>
        [Required(ErrorMessage = "任务领取时间不能为空")]
        public TimeSpan TaskClaimTime { get; set; }

        /// <summary>
        /// 是否跨天
        /// </summary>
        public bool IsOvernight { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [MaxLength(500, ErrorMessage = "描述长度不能超过500个字符")]
        public string? Description { get; set; }
    }

    /// <summary>
    /// 用户班次分配DTO
    /// </summary>
    public class UserShiftAssignmentDto
    {
        /// <summary>
        /// 分配ID
        /// </summary>
        public long AssignmentId { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 班次ID
        /// </summary>
        public long ShiftId { get; set; }

        /// <summary>
        /// 生效日期
        /// </summary>
        public DateTime EffectiveDate { get; set; }

        /// <summary>
        /// 失效日期
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// 分配类型
        /// </summary>
        public string AssignmentType { get; set; } = string.Empty;

        /// <summary>
        /// 备注
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 班次名称
        /// </summary>
        public string? ShiftName { get; set; }
    }

    /// <summary>
    /// 创建用户班次分配DTO
    /// </summary>
    public class CreateUserShiftAssignmentDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public int UserId { get; set; }

        /// <summary>
        /// 班次ID
        /// </summary>
        [Required(ErrorMessage = "班次ID不能为空")]
        public long ShiftId { get; set; }

        /// <summary>
        /// 生效日期
        /// </summary>
        [Required(ErrorMessage = "生效日期不能为空")]
        public DateTime EffectiveDate { get; set; }

        /// <summary>
        /// 失效日期
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// 分配类型 (Permanent-固定, Temporary-临时, Rotation-轮班)
        /// </summary>
        [Required(ErrorMessage = "分配类型不能为空")]
        public string AssignmentType { get; set; } = "Permanent";

        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string? Notes { get; set; }
    }
}
