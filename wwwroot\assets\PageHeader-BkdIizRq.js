import{_ as s,b as e,o as a,d as t,a9 as i,t as d,aO as r}from"./index-C7OOw0MO.js";const c={class:"page-header"},o={class:"header-left"},p={class:"title"},l={key:0,class:"description"},n={class:"header-right"},g=s({__name:"PageHeader",props:{title:{type:String,required:!0},description:{type:String,default:""}},setup:s=>(g,h)=>(a(),e("div",c,[t("div",o,[t("h2",p,d(s.title),1),s.description?(a(),e("p",l,d(s.description),1)):i("",!0)]),t("div",n,[r(g.$slots,"actions",{},void 0,!0)])]))},[["__scopeId","data-v-21c6c352"]]);export{g as P};
