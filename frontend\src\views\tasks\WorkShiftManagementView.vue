<template>
  <div class="shift-management-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>班次管理</h1>
        <p class="subtitle">管理工作班次设置和用户分配</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          新增班次
        </el-button>
      </div>
    </div>

    <!-- 班次列表 -->
    <el-card class="shift-list-card">
      <template #header>
        <div class="card-header">
          <span>班次列表</span>
          <el-button text @click="loadShifts">
            <el-icon><RefreshRight /></el-icon>
            刷新
          </el-button>
        </div>
      </template>

      <el-table 
        :data="shiftList" 
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="shiftName" label="班次名称" width="150" />
        <el-table-column prop="shiftCode" label="班次代码" width="120" />
        <el-table-column prop="shiftType" label="班次类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getShiftTypeColor(row.shiftType)">
              {{ getShiftTypeName(row.shiftType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="工作时间" width="200">
          <template #default="{ row }">
            <span>{{ row.startTime }} - {{ row.endTime }}</span>
            <el-tag v-if="row.isOvernight" size="small" type="warning" style="margin-left: 8px">
              跨夜
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="taskClaimTime" label="任务领取时间" width="150" />
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.isActive"
              @change="handleStatusChange(row)"
              :loading="row.statusLoading"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="editShift(row)">编辑</el-button>
            <el-button size="small" @click="manageUsers(row)">用户分配</el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="deleteShift(row)"
              :disabled="row.isActive"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增/编辑班次对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogMode === 'create' ? '新增班次' : '编辑班次'"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="班次名称" prop="shiftName">
          <el-input v-model="form.shiftName" placeholder="请输入班次名称" />
        </el-form-item>
        <el-form-item label="班次代码" prop="shiftCode">
          <el-input v-model="form.shiftCode" placeholder="请输入班次代码" />
        </el-form-item>
        <el-form-item label="班次类型" prop="shiftType">
          <el-select v-model="form.shiftType" placeholder="请选择班次类型" style="width: 100%">
            <el-option label="白班" value="Day" />
            <el-option label="夜班" value="Night" />
            <el-option label="中班" value="Evening" />
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime">
          <el-time-picker
            v-model="form.startTime"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            placeholder="选择开始时间"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-time-picker
            v-model="form.endTime"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            placeholder="选择结束时间"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="任务领取时间" prop="taskClaimTime">
          <el-time-picker
            v-model="form.taskClaimTime"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            placeholder="选择任务领取时间"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="跨夜班次">
          <el-switch v-model="form.isOvernight" />
          <span style="margin-left: 10px; color: #909399; font-size: 12px">
            如果班次跨越午夜，请开启此选项
          </span>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入班次描述"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleSubmit"
            :loading="submitting"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户分配对话框 -->
    <el-dialog
      v-model="userAssignDialogVisible"
      title="用户分配"
      width="900px"
      @open="loadUserAssignments"
    >
      <div v-if="currentShift">
        <div class="assignment-header">
          <h4>{{ currentShift.shiftName }} - 用户分配</h4>
          <p class="shift-info">班次时间: {{ currentShift.startTime }} - {{ currentShift.endTime }}</p>
        </div>

        <!-- 添加用户区域 -->
        <div class="add-user-section">
          <h5>添加用户到班次</h5>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-select
                v-model="selectedUserId"
                placeholder="选择用户"
                filterable
                clearable
                style="width: 100%"
                :loading="usersLoading"
              >
                <el-option
                  v-for="user in availableUsers"
                  :key="user.id"
                  :label="user.name"
                  :value="user.id"
                />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-date-picker
                v-model="assignmentForm.effectiveDate"
                type="date"
                placeholder="生效日期"
                style="width: 100%"
              />
            </el-col>
            <el-col :span="6">
              <el-button
                type="primary"
                @click="addUserToShift"
                :loading="assignmentSubmitting"
                :disabled="!selectedUserId"
              >
                添加
              </el-button>
            </el-col>
          </el-row>
          <el-row style="margin-top: 8px">
            <el-col :span="24">
              <el-input
                v-model="assignmentForm.notes"
                placeholder="备注（可选）"
                type="textarea"
                :rows="2"
              />
            </el-col>
          </el-row>
        </div>

        <!-- 已分配用户列表 -->
        <div class="assigned-users-section">
          <h5>已分配用户</h5>
          <el-table
            :data="assignedUsers"
            v-loading="assignmentsLoading"
            style="width: 100%"
            size="small"
          >
            <el-table-column prop="userName" label="用户姓名" width="120" />
            <el-table-column prop="effectiveDate" label="生效日期" width="120">
              <template #default="{ row }">
                {{ formatDate(row.effectiveDate) }}
              </template>
            </el-table-column>
            <el-table-column prop="expiryDate" label="失效日期" width="120">
              <template #default="{ row }">
                {{ row.expiryDate ? formatDate(row.expiryDate) : '永久有效' }}
              </template>
            </el-table-column>
            <el-table-column prop="assignmentType" label="分配类型" width="100">
              <template #default="{ row }">
                <el-tag size="small" :type="getAssignmentTypeColor(row.assignmentType)">
                  {{ getAssignmentTypeName(row.assignmentType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="notes" label="备注" show-overflow-tooltip />
            <el-table-column label="操作" width="80">
              <template #default="{ row }">
                <el-button
                  size="small"
                  type="danger"
                  @click="removeUserAssignment(row)"
                  :loading="row.removing"
                >
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="userAssignDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, RefreshRight } from '@element-plus/icons-vue'
import workShiftApi from '@/api/workShift'

// 响应式数据
const loading = ref(false)
const shiftList = ref([])
const dialogVisible = ref(false)
const userAssignDialogVisible = ref(false)
const dialogMode = ref('create') // 'create' | 'edit'
const currentShift = ref(null)
const submitting = ref(false)
const formRef = ref()

// 用户分配相关数据
const assignedUsers = ref([])
const availableUsers = ref([])
const selectedUserId = ref(null)
const assignmentsLoading = ref(false)
const usersLoading = ref(false)
const assignmentSubmitting = ref(false)
const assignmentForm = reactive({
  effectiveDate: new Date(),
  notes: ''
})

// 表单数据
const form = reactive({
  shiftName: '',
  shiftCode: '',
  shiftType: 'Day',
  startTime: '',
  endTime: '',
  taskClaimTime: '',
  isOvernight: false,
  description: '',
  isActive: true
})

// 表单验证规则
const rules = {
  shiftName: [
    { required: true, message: '请输入班次名称', trigger: 'blur' }
  ],
  shiftCode: [
    { required: true, message: '请输入班次代码', trigger: 'blur' }
  ],
  shiftType: [
    { required: true, message: '请选择班次类型', trigger: 'change' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ],
  taskClaimTime: [
    { required: true, message: '请选择任务领取时间', trigger: 'change' }
  ]
}

// 方法
const loadShifts = async () => {
  loading.value = true
  try {
    const response = await workShiftApi.getAllShifts()
    if (response.success) {
      shiftList.value = response.data?.map(shift => ({
        ...shift,
        statusLoading: false
      })) || []
    } else {
      ElMessage.error('获取班次列表失败')
    }
  } catch (error) {
    console.error('获取班次列表失败:', error)
    ElMessage.error('获取班次列表失败')
  } finally {
    loading.value = false
  }
}

const showCreateDialog = () => {
  dialogMode.value = 'create'
  resetForm()
  dialogVisible.value = true
}

const editShift = (shift) => {
  dialogMode.value = 'edit'
  currentShift.value = shift
  Object.assign(form, {
    shiftName: shift.shiftName,
    shiftCode: shift.shiftCode,
    shiftType: shift.shiftType,
    startTime: shift.startTime,
    endTime: shift.endTime,
    taskClaimTime: shift.taskClaimTime,
    isOvernight: shift.isOvernight,
    description: shift.description,
    isActive: shift.isActive
  })
  dialogVisible.value = true
}

const manageUsers = (shift) => {
  currentShift.value = shift
  userAssignDialogVisible.value = true
}

// 用户分配相关方法
const loadUserAssignments = async () => {
  if (!currentShift.value) return

  assignmentsLoading.value = true
  usersLoading.value = true

  try {
    // 加载已分配用户
    const assignmentsResponse = await workShiftApi.getShiftAssignments(currentShift.value.shiftId)
    if (assignmentsResponse.success) {
      assignedUsers.value = assignmentsResponse.data?.map(assignment => ({
        ...assignment,
        removing: false
      })) || []
    }

    // 加载所有用户
    const usersResponse = await workShiftApi.getAllUsers()
    if (usersResponse.success) {
      const allUsers = usersResponse.data?.items || usersResponse.data || []
      // 过滤掉已分配的用户
      const assignedUserIds = assignedUsers.value.map(a => a.userId)
      availableUsers.value = allUsers.filter(user => !assignedUserIds.includes(user.id))
    }
  } catch (error) {
    console.error('加载用户分配数据失败:', error)
    ElMessage.error('加载用户分配数据失败')
  } finally {
    assignmentsLoading.value = false
    usersLoading.value = false
  }
}

const addUserToShift = async () => {
  if (!selectedUserId.value || !currentShift.value) return

  assignmentSubmitting.value = true
  try {
    const assignmentData = {
      userId: selectedUserId.value,
      shiftId: currentShift.value.shiftId,
      effectiveDate: assignmentForm.effectiveDate,
      notes: assignmentForm.notes || ''
    }

    const response = await workShiftApi.assignUserToShift(assignmentData)
    if (response.success) {
      ElMessage.success('用户分配成功')
      selectedUserId.value = null
      assignmentForm.notes = ''
      assignmentForm.effectiveDate = new Date()
      await loadUserAssignments() // 重新加载数据
    } else {
      ElMessage.error(response.message || '用户分配失败')
    }
  } catch (error) {
    console.error('用户分配失败:', error)
    ElMessage.error('用户分配失败')
  } finally {
    assignmentSubmitting.value = false
  }
}

const removeUserAssignment = async (assignment) => {
  try {
    await ElMessageBox.confirm(
      `确定要移除用户"${assignment.userName}"的班次分配吗？`,
      '确认移除',
      { type: 'warning' }
    )

    assignment.removing = true
    const response = await workShiftApi.removeUserShiftAssignment(assignment.assignmentId)
    if (response.success) {
      ElMessage.success('移除成功')
      await loadUserAssignments() // 重新加载数据
    } else {
      ElMessage.error(response.message || '移除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除用户分配失败:', error)
      ElMessage.error('移除用户分配失败')
    }
  } finally {
    assignment.removing = false
  }
}

const deleteShift = async (shift) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除班次"${shift.shiftName}"吗？`,
      '确认删除',
      { type: 'warning' }
    )
    
    // TODO: 实现删除班次的API调用
    ElMessage.success('删除成功')
    loadShifts()
  } catch (error) {
    // 用户取消删除
  }
}

const handleStatusChange = async (shift) => {
  shift.statusLoading = true
  try {
    // TODO: 实现更新班次状态的API调用
    ElMessage.success(`班次已${shift.isActive ? '启用' : '禁用'}`)
  } catch (error) {
    // 恢复原状态
    shift.isActive = !shift.isActive
    ElMessage.error('状态更新失败')
  } finally {
    shift.statusLoading = false
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    if (dialogMode.value === 'create') {
      const response = await workShiftApi.createShift(form)
      if (response.success) {
        ElMessage.success('班次创建成功')
        dialogVisible.value = false
        loadShifts()
      } else {
        ElMessage.error(response.message || '班次创建失败')
      }
    } else {
      // TODO: 实现编辑班次的API调用
      ElMessage.success('班次更新成功')
      dialogVisible.value = false
      loadShifts()
    }
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleDialogClose = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  Object.assign(form, {
    shiftName: '',
    shiftCode: '',
    shiftType: 'Day',
    startTime: '',
    endTime: '',
    taskClaimTime: '',
    isOvernight: false,
    description: '',
    isActive: true
  })
  currentShift.value = null
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const getShiftTypeColor = (type) => {
  const colorMap = {
    'Day': 'success',
    'Night': 'info',
    'Evening': 'warning'
  }
  return colorMap[type] || 'primary'
}

const getShiftTypeName = (type) => {
  const nameMap = {
    'Day': '白班',
    'Night': '夜班',
    'Evening': '中班'
  }
  return nameMap[type] || type
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

const getAssignmentTypeColor = (type) => {
  const colorMap = {
    'Permanent': 'success',
    'Temporary': 'warning',
    'Substitute': 'info'
  }
  return colorMap[type] || 'primary'
}

const getAssignmentTypeName = (type) => {
  const nameMap = {
    'Permanent': '永久',
    'Temporary': '临时',
    'Substitute': '替代'
  }
  return nameMap[type] || type
}

// 生命周期
onMounted(() => {
  loadShifts()
})
</script>

<style scoped>
.shift-management-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
}

.subtitle {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.shift-list-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.assignment-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.assignment-header h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
}

.shift-info {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.add-user-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.add-user-section h5 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
}

.assigned-users-section h5 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
}
</style>
