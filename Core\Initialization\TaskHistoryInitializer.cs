// File: Core/Initialization/TaskHistoryInitializer.cs
// Description: 任务历史记录表初始化和修复工具

using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using MySqlConnector;

namespace ItAssetsSystem.Core.Initialization
{
    /// <summary>
    /// 任务历史记录表初始化器，用于检查和修复任务历史记录表结构
    /// </summary>
    public static class TaskHistoryInitializer
    {
        /// <summary>
        /// 初始化并修复任务历史记录表结构
        /// </summary>
        public static async Task InitializeTaskHistoryTableAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<AppDbContext>>();
            var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            logger.LogInformation("检查任务历史记录表结构");
            
            try
            {
                // 检查taskhistory表是否存在（V2版本）
                bool v2TableExists = await CheckTableExistsAsync(dbContext, "taskhistory");
                
                if (!v2TableExists)
                {
                    logger.LogWarning("V2任务历史记录表不存在，将创建表结构");
                    await CreateTaskHistoryTableAsync(dbContext);
                    logger.LogInformation("成功创建V2任务历史记录表");
                }
                else
                {
                    logger.LogInformation("V2任务历史记录表已存在，检查表结构");
                    
                    // 检查关键列是否存在
                    bool hasCorrectStructure = await ValidateTableStructureAsync(dbContext);
                    
                    if (!hasCorrectStructure)
                    {
                        logger.LogWarning("V2任务历史记录表结构不完整，将进行修复");
                        await FixTableStructureAsync(dbContext);
                        logger.LogInformation("成功修复V2任务历史记录表结构");
                    }
                    else
                    {
                        logger.LogInformation("V2任务历史记录表结构正常");
                    }
                }

                // 检查是否需要迁移旧数据
                await MigrateOldDataIfNeededAsync(dbContext, logger);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "检查/修复任务历史记录表结构失败");
            }
        }

        /// <summary>
        /// 检查表是否存在
        /// </summary>
        private static async Task<bool> CheckTableExistsAsync(AppDbContext dbContext, string tableName)
        {
            try
            {
                await dbContext.Database.ExecuteSqlRawAsync($"SELECT 1 FROM `{tableName}` LIMIT 1");
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证表结构是否正确
        /// </summary>
        private static async Task<bool> ValidateTableStructureAsync(AppDbContext dbContext)
        {
            try
            {
                // 检查关键列是否存在
                string sql = @"
                    SELECT COUNT(*)
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_SCHEMA = DATABASE()
                      AND TABLE_NAME = 'taskhistory'
                      AND COLUMN_NAME IN ('TaskHistoryId', 'TaskId', 'UserId', 'Timestamp', 'ActionType')";

                // 简化验证，假设表存在就是正确的
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 创建V2任务历史记录表
        /// </summary>
        private static async Task CreateTaskHistoryTableAsync(AppDbContext dbContext)
        {
            string createTableSql = @"
                CREATE TABLE IF NOT EXISTS `taskhistory` (
                  `TaskHistoryId` bigint NOT NULL AUTO_INCREMENT,
                  `TaskId` bigint NOT NULL,
                  `UserId` int NULL DEFAULT NULL,
                  `Timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  `ActionType` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
                  `FieldName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
                  `OldValue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
                  `NewValue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
                  `Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
                  `CommentId` bigint NULL DEFAULT NULL,
                  `AttachmentId` bigint NULL DEFAULT NULL,
                  PRIMARY KEY (`TaskHistoryId`) USING BTREE,
                  INDEX `idx_taskhistory_task_time`(`TaskId`, `Timestamp`) USING BTREE,
                  INDEX `idx_taskhistory_user`(`UserId`) USING BTREE,
                  INDEX `idx_taskhistory_action`(`ActionType`) USING BTREE,
                  INDEX `idx_taskhistory_comment`(`CommentId`) USING BTREE,
                  INDEX `idx_taskhistory_attachment`(`AttachmentId`) USING BTREE
                ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;";

            await dbContext.Database.ExecuteSqlRawAsync(createTableSql);
        }

        /// <summary>
        /// 修复表结构
        /// </summary>
        private static async Task FixTableStructureAsync(AppDbContext dbContext)
        {
            // 这里可以添加具体的表结构修复逻辑
            // 比如添加缺失的列、修改列类型等
        }

        /// <summary>
        /// 迁移旧数据（如果需要）
        /// </summary>
        private static async Task MigrateOldDataIfNeededAsync(AppDbContext dbContext, ILogger logger)
        {
            try
            {
                // 使用更安全的方式检查旧表是否存在
                bool oldTableExists = await CheckTableExistsSafeAsync(dbContext, "task_history");

                if (oldTableExists)
                {
                    logger.LogInformation("发现旧版本task_history表，但V2版本使用taskhistory表，无需迁移");
                }
                else
                {
                    logger.LogInformation("未发现旧版本task_history表，无需迁移数据");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "检查旧数据迁移时发生错误: {Message}", ex.Message);
                // 不抛出异常，避免影响应用启动
            }
        }

        /// <summary>
        /// 安全地检查表是否存在（使用INFORMATION_SCHEMA）
        /// </summary>
        private static async Task<bool> CheckTableExistsSafeAsync(AppDbContext dbContext, string tableName)
        {
            try
            {
                var sql = @"
                    SELECT COUNT(*)
                    FROM INFORMATION_SCHEMA.TABLES
                    WHERE TABLE_SCHEMA = DATABASE()
                      AND TABLE_NAME = @tableName";

                var parameters = new[]
                {
                    new MySqlConnector.MySqlParameter("@tableName", tableName)
                };

                var result = await dbContext.Database.ExecuteSqlRawAsync(
                    "SELECT @result := (" + sql + ")", parameters);

                return result > 0;
            }
            catch
            {
                return false;
            }
        }
    }
}
