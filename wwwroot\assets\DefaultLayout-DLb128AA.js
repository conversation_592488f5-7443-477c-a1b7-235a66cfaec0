import{_ as e,u as a,c as t,r as l,a as n,b as s,o as i,d as u,e as d,w as o,f as r,g as c,n as m,F as p,h as f,i as _,t as v,j as x,k as y,l as g,m as k,p as h,q as b,s as w,v as C,x as I,y as D,z as V,A as L,B as M,C as S,D as j,E as T,G as U,H as N,I as P,J as $,K as A,L as z,M as q,N as F,O as R,P as B,Q as E,R as G,S as H,T as J,U as K,V as O,W as Q,X as W,Y as X,Z as Y,$ as Z,a0 as ee,a1 as ae}from"./index-CkwLz8y6.js";import{u as te}from"./gamification-DuHbQD0u.js";import{n as le}from"./notification-service-BTmwzUoW.js";import{f as ne,z as se}from"./zh-CN-B1csyosV.js";import{N as ie}from"./NotificationCenter-BgpTKwJk.js";import{s as ue}from"./system-9jEcQzSp.js";import"./en-US-BvtvdVHO.js";const de={class:"activity-ticker-container"},oe={class:"ticker-icon"},re={class:"ticker-wrap"},ce=["onClick"],me={class:"item-content"},pe={class:"item-time"},fe=["onClick"],_e={class:"item-content"},ve={class:"item-time"},xe=e({__name:"ActivityTicker",setup(e){const x=a(),y=te(),g=t((()=>y.recentActivities.map((e=>({id:e.id,type:e.type||"notification",content:e.text||"无内容",timestamp:e.timestamp,relatedId:e.relatedId}))))),k=l(null),h=t((()=>{const e=g.value.length;return`${Math.max(5,3*e)}s`})),b=e=>{switch(e){case"task_assigned":return"新任务";case"task_completed":return"任务完成";case"reward":return"奖励";case"mention":return"＠提及";case"system":return"系统";case"achievement":return"成就达成";case"comment":return"新评论";default:return"通知"}},w=e=>{if(!e)return"";try{const a=new Date(e);return isNaN(a.getTime())?"无效日期":ne(a,{addSuffix:!0,locale:se})}catch(a){return"无效日期"}},C=e=>{"task_assigned"!==e.type&&"mention"!==e.type&&"task_completed"!==e.type||!e.relatedId?"achievement"===e.type&&e.relatedId:x.push({name:"TaskDetail",params:{id:e.relatedId}}).catch((e=>{}))},I=e=>{const a=e.target.closest(".ticker-list");a&&(a.style.animationPlayState="paused")},D=e=>{const a=e.target.closest(".ticker-list");a&&(a.style.animationPlayState="running")};return(e,a)=>{const t=n("el-icon");return i(),s("div",de,[u("div",oe,[d(t,null,{default:o((()=>[d(r(c))])),_:1})]),u("div",re,[u("ul",{class:"ticker-list",style:m({animationDuration:h.value}),ref_key:"tickerListRef",ref:k},[(i(!0),s(p,null,f(g.value,(e=>(i(),s("li",{key:e.id,class:"ticker-item",onClick:a=>C(e),onMouseover:I,onMouseout:D},[u("span",{class:_(["item-type",`type-${e.type}`])},"["+v(b(e.type))+"]",3),u("span",me,v(e.content),1),u("span",pe,v(w(e.timestamp)),1)],40,ce)))),128)),(i(!0),s(p,null,f(g.value,(e=>(i(),s("li",{key:`dup-${e.id}`,class:"ticker-item",onClick:a=>C(e),onMouseover:I,onMouseout:D,"aria-hidden":"true"},[u("span",{class:_(["item-type",`type-${e.type}`])},"["+v(b(e.type))+"]",3),u("span",_e,v(e.content),1),u("span",ve,v(w(e.timestamp)),1)],40,fe)))),128))],4)])])}}},[["__scopeId","data-v-7bb0690c"]]),ye={class:"app-container"},ge={class:"header-left"},ke={class:"logo"},he={class:"header-right"},be={class:"user-avatar-name"},we={class:"username"},Ce=e({__name:"DefaultLayout",setup(e){const c=a(),m=w(),p=x(),f=te(),ne=y(),se=g(),de=l("true"===localStorage.getItem("sidebarCollapse")),oe=l("");l(["Dashboard","AssetList","LocationTree","FaultList","TaskList"]);const re=t((()=>{const{meta:e,path:a}=m;return e.activeMenu?e.activeMenu:a}));l("var(--sidebar-bg)"),l("var(--sidebar-text-color)"),l("var(--sidebar-active-text-color)");const ce=e=>{switch(e){case"profile":c.push("/main/user/profile");break;case"password":ae.alert("修改密码功能待实现","提示",{type:"info"});break;case"logout":p.setLogoutDialogVisible(!0)}},me=async()=>{try{await p.logout(),c.push(`/login?redirect=${m.fullPath}`),p.setLogoutDialogVisible(!1)}catch(e){ae.alert("退出登录失败","错误",{type:"error"}),p.setLogoutDialogVisible(!1)}};k((async()=>{var e,a;if(!(null==(e=p.userInfo)?void 0:e.id))try{await p.getUserInfo()}catch(t){}f.initializeStore();try{await se.fetchUnreadCount(),se.startPolling(),(null==(a=p.userInfo)?void 0:a.id)&&await le.initConnection(p.userInfo.id)}catch(t){}de.value="true"===localStorage.getItem("sidebarCollapse")})),h(de,(e=>{localStorage.setItem("sidebarCollapse",e)}));const pe=()=>{ne.openMemoDrawer("create")},fe=l(!1),_e=t((()=>se.unreadCount)),ve=()=>{fe.value=!0},Ce=e=>{c.push(`/main/tasks/detail/${e}`),fe.value=!1};return b((()=>{le.disconnect(),se.stopPolling()})),(e,a)=>{const t=n("el-input"),l=n("el-button"),c=n("el-tooltip"),m=n("el-badge"),f=n("el-avatar"),x=n("el-icon"),y=n("el-dropdown-item"),g=n("el-dropdown-menu"),k=n("el-dropdown"),h=n("el-header"),b=n("el-menu-item"),w=n("el-sub-menu"),ae=n("el-menu"),te=n("el-scrollbar"),le=n("el-aside"),ne=n("router-view"),se=n("el-main"),Ie=n("el-container"),De=n("el-dialog");return i(),s("div",ye,[d(h,{class:"header"},{default:o((()=>[u("div",ge,[u("div",ke,v(r(ue).appName||"IT资产管理系统"),1)]),u("div",he,[d(t,{modelValue:oe.value,"onUpdate:modelValue":a[0]||(a[0]=e=>oe.value=e),placeholder:"全局搜索...",style:{width:"250px","margin-right":"15px"},clearable:"","prefix-icon":r(C)},null,8,["modelValue","prefix-icon"]),d(c,{content:"新建随手记",placement:"bottom"},{default:o((()=>[d(l,{icon:r(I),circle:"",onClick:pe,style:{"margin-right":"10px"}},null,8,["icon"])])),_:1}),d(c,{content:"查看通知",placement:"bottom"},{default:o((()=>[d(m,{value:_e.value,hidden:0===_e.value,class:"item notification-badge-header",max:99},{default:o((()=>[d(l,{icon:r(D),circle:"",onClick:ve},null,8,["icon"])])),_:1},8,["value","hidden"])])),_:1}),d(k,{trigger:"click",onCommand:ce},{dropdown:o((()=>[d(g,null,{default:o((()=>[d(y,{command:"profile"},{default:o((()=>[d(x,null,{default:o((()=>[d(r(M))])),_:1}),a[5]||(a[5]=L("个人中心 "))])),_:1}),d(y,{command:"password"},{default:o((()=>[d(x,null,{default:o((()=>[d(r(S))])),_:1}),a[6]||(a[6]=L("修改密码 "))])),_:1}),d(y,{divided:"",command:"logout"},{default:o((()=>[d(x,null,{default:o((()=>[d(r(j))])),_:1}),a[7]||(a[7]=L("退出登录 "))])),_:1})])),_:1})])),default:o((()=>{var e;return[u("span",be,[d(f,{size:32,src:r(p).computedAvatarUrl,class:"avatar"},null,8,["src"]),u("span",we,v((null==(e=r(p).userInfo)?void 0:e.name)||"用户名"),1),d(x,{class:"el-icon--right"},{default:o((()=>[d(r(V))])),_:1})])]})),_:1})])])),_:1}),d(Ie,null,{default:o((()=>[d(le,{class:"sidebar",width:de.value?"64px":"210px"},{default:o((()=>[d(te,null,{default:o((()=>[d(ae,{"default-active":re.value,collapse:de.value,class:"el-menu-vertical",router:"","unique-opened":!0},{default:o((()=>[d(b,{index:"/main/dashboard"},{title:o((()=>a[8]||(a[8]=[L("仪表盘")]))),default:o((()=>[d(x,null,{default:o((()=>[d(r(T))])),_:1})])),_:1}),d(w,{index:"/main/asset"},{title:o((()=>[d(x,null,{default:o((()=>[d(r(N))])),_:1}),a[9]||(a[9]=u("span",null,"资产管理",-1))])),default:o((()=>[d(b,{index:"/main/asset/list"},{default:o((()=>[d(x,null,{default:o((()=>[d(r(U))])),_:1}),a[10]||(a[10]=u("span",null,"资产列表",-1))])),_:1}),d(b,{index:"/main/asset/type"},{default:o((()=>[d(x,null,{default:o((()=>[d(r(S))])),_:1}),a[11]||(a[11]=u("span",null,"资产类型",-1))])),_:1})])),_:1}),d(w,{index:"/main/locations"},{title:o((()=>[d(x,null,{default:o((()=>[d(r(P))])),_:1}),a[12]||(a[12]=u("span",null,"位置管理",-1))])),default:o((()=>[d(b,{index:"/main/locations/structure"},{default:o((()=>a[13]||(a[13]=[u("span",null,"位置结构",-1)]))),_:1}),d(b,{index:"/main/locations/relations"},{default:o((()=>a[14]||(a[14]=[u("span",null,"位置关联",-1)]))),_:1})])),_:1}),d(w,{index:"/main/faults"},{title:o((()=>[d(x,null,{default:o((()=>[d(r($))])),_:1}),a[15]||(a[15]=u("span",null,"故障管理",-1))])),default:o((()=>[d(b,{index:"/main/faults/list"},{default:o((()=>a[16]||(a[16]=[u("span",null,"故障列表",-1)]))),_:1}),d(b,{index:"/main/faults/maintenance"},{default:o((()=>a[17]||(a[17]=[u("span",null,"返厂/维修",-1)]))),_:1})])),_:1}),d(w,{index:"/main/purchases"},{title:o((()=>[d(x,null,{default:o((()=>[d(r(A))])),_:1}),a[18]||(a[18]=u("span",null,"采购管理",-1))])),default:o((()=>[d(b,{index:"/main/purchases/list"},{default:o((()=>a[19]||(a[19]=[u("span",null,"采购列表",-1)]))),_:1})])),_:1}),d(w,{index:"/main/spareparts"},{title:o((()=>[d(x,null,{default:o((()=>[d(r(B))])),_:1}),a[20]||(a[20]=u("span",null,"备品备件管理",-1))])),default:o((()=>[d(b,{index:"/main/spareparts/list"},{default:o((()=>[d(x,null,{default:o((()=>[d(r(z))])),_:1}),a[21]||(a[21]=u("span",null,"备件台账",-1))])),_:1}),d(b,{index:"/main/spareparts/types"},{default:o((()=>[d(x,null,{default:o((()=>[d(r(q))])),_:1}),a[22]||(a[22]=u("span",null,"备件类型管理",-1))])),_:1}),d(b,{index:"/main/spareparts/locations"},{default:o((()=>[d(x,null,{default:o((()=>[d(r(F))])),_:1}),a[23]||(a[23]=u("span",null,"备件库位管理",-1))])),_:1}),d(b,{index:"/main/spareparts/transactions"},{default:o((()=>[d(x,null,{default:o((()=>[d(r(R))])),_:1}),a[24]||(a[24]=u("span",null,"出入库记录",-1))])),_:1})])),_:1}),d(w,{index:"/main/tasks"},{title:o((()=>[d(x,null,{default:o((()=>[d(r(z))])),_:1}),a[25]||(a[25]=u("span",null,"任务中心",-1))])),default:o((()=>[d(b,{index:"/main/tasks/simple-list"},{default:o((()=>[d(x,null,{default:o((()=>[d(r(U))])),_:1}),a[26]||(a[26]=u("span",null,"任务列表",-1))])),_:1}),d(b,{index:"/main/tasks/pdca-tracker"},{default:o((()=>[d(x,null,{default:o((()=>[d(r(E))])),_:1}),a[27]||(a[27]=u("span",null,"PDCA跟踪",-1))])),_:1}),d(b,{index:"/main/tasks/periodic"},{default:o((()=>[d(x,null,{default:o((()=>[d(r(G))])),_:1}),a[28]||(a[28]=u("span",null,"周期性任务",-1))])),_:1}),d(b,{index:"/main/tasks/kanban"},{default:o((()=>[d(x,null,{default:o((()=>[d(r(H))])),_:1}),a[29]||(a[29]=u("span",null,"任务看板",-1))])),_:1})])),_:1}),d(b,{index:"/main/quickmemo"},{title:o((()=>a[30]||(a[30]=[L("随手记")]))),default:o((()=>[d(x,null,{default:o((()=>[d(r(J))])),_:1})])),_:1}),d(b,{index:"/main/memo-list"},{title:o((()=>a[31]||(a[31]=[L("随手记列表")]))),default:o((()=>[d(x,null,{default:o((()=>[d(r(z))])),_:1})])),_:1}),d(b,{index:"/main/leaderboard"},{title:o((()=>a[32]||(a[32]=[L("排行榜")]))),default:o((()=>[d(x,null,{default:o((()=>[d(r(K))])),_:1})])),_:1}),d(b,{index:"/main/user/profile"},{title:o((()=>a[33]||(a[33]=[L("个人中心")]))),default:o((()=>[d(x,null,{default:o((()=>[d(r(M))])),_:1})])),_:1}),d(w,{index:"/main/system"},{title:o((()=>[d(x,null,{default:o((()=>[d(r(S))])),_:1}),a[34]||(a[34]=u("span",null,"系统管理",-1))])),default:o((()=>[d(b,{index:"/main/system/users"},{default:o((()=>a[35]||(a[35]=[L("用户管理")]))),_:1}),d(b,{index:"/main/system/roles"},{default:o((()=>a[36]||(a[36]=[L("角色管理")]))),_:1}),d(b,{index:"/main/system/menus"},{default:o((()=>a[37]||(a[37]=[L("菜单管理")]))),_:1}),d(b,{index:"/main/system/departments"},{default:o((()=>a[38]||(a[38]=[L("部门管理")]))),_:1}),d(b,{index:"/main/system/personnel"},{default:o((()=>a[39]||(a[39]=[L("人员管理")]))),_:1}),d(b,{index:"/main/system/logs"},{default:o((()=>a[40]||(a[40]=[L("审计日志")]))),_:1})])),_:1}),d(w,{index:"/test"},{title:o((()=>[d(x,null,{default:o((()=>[d(r(Q))])),_:1}),a[41]||(a[41]=u("span",null,"功能测试",-1))])),default:o((()=>[d(b,{index:"/test/export"},{default:o((()=>[d(x,null,{default:o((()=>[d(r(O))])),_:1}),a[42]||(a[42]=u("span",null,"导出测试",-1))])),_:1})])),_:1})])),_:1},8,["default-active","collapse"])])),_:1})])),_:1},8,["width"]),d(se,{class:"main-content"},{default:o((()=>[d(xe),d(ne,null,{default:o((({Component:e})=>[d(W,{name:"fade-transform",mode:"out-in"},{default:o((()=>[(i(),X(Y(e)))])),_:2},1024)])),_:1})])),_:1})])),_:1}),u("div",{class:_(["collapse-btn",{collapsed:de.value}]),onClick:a[1]||(a[1]=e=>de.value=!de.value)},[d(x,null,{default:o((()=>[de.value?(i(),X(r(Z),{key:0})):(i(),X(r(ee),{key:1}))])),_:1})],2),d(De,{title:"退出确认",modelValue:r(p).isLogoutDialogVisible,"onUpdate:modelValue":a[3]||(a[3]=e=>r(p).isLogoutDialogVisible=e),width:"380px"},{footer:o((()=>[d(l,{onClick:a[2]||(a[2]=e=>r(p).setLogoutDialogVisible(!1))},{default:o((()=>a[43]||(a[43]=[L("取消")]))),_:1}),d(l,{type:"primary",onClick:me},{default:o((()=>a[44]||(a[44]=[L("确定")]))),_:1})])),default:o((()=>[a[45]||(a[45]=u("span",null,"确定要退出登录吗？",-1))])),_:1},8,["modelValue"]),d(ie,{visible:fe.value,"onUpdate:visible":a[4]||(a[4]=e=>fe.value=e),mode:"drawer",onViewTask:Ce},null,8,["visible"])])}}},[["__scopeId","data-v-688ff30a"]]);export{Ce as default};
