-- =============================================
-- 扩展现有数据库表以支持备件状态管理
-- 基于现有20250608数据库结构的最小化扩展方案
-- =============================================

-- 1. 创建备件状态类型表
CREATE TABLE `spare_part_status_types` (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(20) NOT NULL COMMENT '状态代码',
  `name` varchar(50) NOT NULL COMMENT '状态名称',
  `category` varchar(20) NOT NULL COMMENT '状态分类: Available, Unavailable, InTransit, Reserved',
  `color` varchar(10) NOT NULL COMMENT '显示颜色',
  `icon` varchar(50) NULL COMMENT '图标',
  `description` varchar(200) NULL COMMENT '状态描述',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_status_code` (`code`),
  INDEX `idx_status_category` (`category`)
) COMMENT='备件状态类型表';

-- 插入默认状态数据
INSERT INTO `spare_part_status_types` (`code`, `name`, `category`, `color`, `icon`, `description`, `sort_order`) VALUES
-- 可用状态
('NEW', '新品', 'Available', '#67c23a', 'CircleCheck', '全新未使用的备件', 1),
('GOOD', '良好', 'Available', '#409eff', 'Select', '使用过但状态良好的备件', 2),
('REFURBISHED', '翻新', 'Available', '#e6a23c', 'Refresh', '维修后可用的备件', 3),
-- 不可用状态
('FAULTY', '故障', 'Unavailable', '#f56c6c', 'Warning', '有故障需要维修的备件', 4),
('DAMAGED', '损坏', 'Unavailable', '#f56c6c', 'Close', '严重损坏的备件', 5),
('SCRAPPED', '报废', 'Unavailable', '#909399', 'Delete', '无法修复已报废的备件', 6),
-- 在途状态
('UNDER_REPAIR', '返厂中', 'InTransit', '#e6a23c', 'Truck', '送厂维修中的备件', 7),
('REPAIRING', '维修中', 'InTransit', '#e6a23c', 'Tools', '正在维修的备件', 8),
('PENDING_INSPECTION', '待检验', 'InTransit', '#e6a23c', 'View', '维修完成待检验的备件', 9),
-- 预留状态
('ALLOCATED', '已分配', 'Reserved', '#909399', 'Lock', '已分配给特定设备的备件', 10),
('RESERVED', '预留', 'Reserved', '#909399', 'Collection', '为特定用途预留的备件', 11);

-- 2. 创建备件库存明细表（支持状态区分）
CREATE TABLE `spare_part_inventories` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `part_id` int NOT NULL COMMENT '备件ID',
  `location_id` int NOT NULL COMMENT '库位ID',
  `status_id` int NOT NULL COMMENT '状态ID',
  `quantity` int NOT NULL DEFAULT 0 COMMENT '数量',
  `batch_number` varchar(50) NULL COMMENT '批次号',
  `serial_numbers` json NULL COMMENT '序列号列表（JSON数组）',
  `purchase_date` date NULL COMMENT '采购日期',
  `warranty_expire_date` date NULL COMMENT '保修到期日期',
  `supplier_id` int NULL COMMENT '供应商ID',
  `unit_cost` decimal(10,2) NULL COMMENT '单位成本',
  `notes` varchar(500) NULL COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_inventory_part` (`part_id`),
  INDEX `idx_inventory_location` (`location_id`),
  INDEX `idx_inventory_status` (`status_id`),
  INDEX `idx_inventory_batch` (`batch_number`),
  FOREIGN KEY (`part_id`) REFERENCES `spare_parts` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`location_id`) REFERENCES `spare_part_locations` (`id`) ON DELETE RESTRICT,
  FOREIGN KEY (`status_id`) REFERENCES `spare_part_status_types` (`id`) ON DELETE RESTRICT
) COMMENT='备件库存明细表（按状态分类）';

-- 3. 扩展spare_part_transactions表，添加状态变更支持
ALTER TABLE `spare_part_transactions` 
ADD COLUMN `from_status_id` int NULL COMMENT '变更前状态ID' AFTER `reason`,
ADD COLUMN `to_status_id` int NULL COMMENT '变更后状态ID' AFTER `from_status_id`,
ADD COLUMN `inventory_id` bigint NULL COMMENT '关联库存明细ID' AFTER `to_status_id`,
ADD COLUMN `repair_order_id` int NULL COMMENT '关联返厂单ID' AFTER `inventory_id`,
ADD INDEX `idx_transaction_from_status` (`from_status_id`),
ADD INDEX `idx_transaction_to_status` (`to_status_id`),
ADD INDEX `idx_transaction_inventory` (`inventory_id`),
ADD INDEX `idx_transaction_repair_order` (`repair_order_id`);

-- 4. 创建备件状态变更历史表
CREATE TABLE `spare_part_status_histories` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `inventory_id` bigint NOT NULL COMMENT '库存明细ID',
  `from_status_id` int NULL COMMENT '变更前状态',
  `to_status_id` int NOT NULL COMMENT '变更后状态',
  `quantity` int NOT NULL COMMENT '变更数量',
  `reason` varchar(200) NOT NULL COMMENT '变更原因',
  `operator_id` int NOT NULL COMMENT '操作人ID',
  `transaction_id` int NULL COMMENT '关联交易记录ID',
  `repair_order_id` int NULL COMMENT '关联返厂单ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_status_history_inventory` (`inventory_id`),
  INDEX `idx_status_history_from_status` (`from_status_id`),
  INDEX `idx_status_history_to_status` (`to_status_id`),
  INDEX `idx_status_history_operator` (`operator_id`),
  FOREIGN KEY (`inventory_id`) REFERENCES `spare_part_inventories` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`from_status_id`) REFERENCES `spare_part_status_types` (`id`) ON DELETE RESTRICT,
  FOREIGN KEY (`to_status_id`) REFERENCES `spare_part_status_types` (`id`) ON DELETE RESTRICT,
  FOREIGN KEY (`operator_id`) REFERENCES `users` (`Id`) ON DELETE RESTRICT
) COMMENT='备件状态变更历史表';

-- 5. 扩展repairorders表，添加备件维修支持
ALTER TABLE `repairorders`
ADD COLUMN `repair_type` tinyint NOT NULL DEFAULT 2 COMMENT '维修类型: 1=故障维修, 2=备件维修, 3=预防性维修' AFTER `OrderCode`,
ADD COLUMN `title` varchar(200) NULL COMMENT '维修标题' AFTER `repair_type`,
ADD COLUMN `description` text NULL COMMENT '维修描述' AFTER `title`,
ADD COLUMN `priority` tinyint NOT NULL DEFAULT 3 COMMENT '优先级: 1=紧急, 2=高, 3=中, 4=低' AFTER `description`,
ADD COLUMN `fault_id` int NULL COMMENT '关联故障ID' AFTER `priority`,
ADD COLUMN `asset_id` int NULL COMMENT '关联资产ID' AFTER `fault_id`,
ADD COLUMN `estimated_cost` decimal(10,2) NULL COMMENT '预估费用' AFTER `asset_id`,
ADD COLUMN `approver_id` int NULL COMMENT '审核人ID' AFTER `estimated_cost`,
ADD INDEX `idx_repair_type` (`repair_type`),
ADD INDEX `idx_repair_priority` (`priority`),
ADD INDEX `idx_repair_fault` (`fault_id`),
ADD INDEX `idx_repair_asset` (`asset_id`);

-- 6. 扩展repairitems表，添加备件维修支持
ALTER TABLE `repairitems`
ADD COLUMN `item_type` tinyint NOT NULL DEFAULT 1 COMMENT '物品类型: 1=备件, 2=资产组件' AFTER `RepairOrderId`,
ADD COLUMN `part_id` int NULL COMMENT '备件ID（当item_type=1时）' AFTER `item_type`,
ADD COLUMN `component_name` varchar(200) NULL COMMENT '组件名称' AFTER `part_id`,
ADD COLUMN `serial_number` varchar(100) NULL COMMENT '序列号' AFTER `component_name`,
ADD COLUMN `quantity` int NOT NULL DEFAULT 1 COMMENT '数量' AFTER `serial_number`,
ADD COLUMN `before_status` varchar(50) NULL COMMENT '维修前状态' AFTER `quantity`,
ADD COLUMN `after_status` varchar(50) NULL COMMENT '维修后状态' AFTER `before_status`,
ADD INDEX `idx_repair_item_part` (`part_id`),
ADD INDEX `idx_repair_item_type` (`item_type`);

-- 7. 创建库存汇总视图
CREATE OR REPLACE VIEW `v_spare_part_stock_summary` AS
SELECT 
    sp.id as part_id,
    sp.code as part_code,
    sp.name as part_name,
    sp.unit,
    spt.code as status_code,
    spt.name as status_name,
    spt.category as status_category,
    spt.color as status_color,
    COALESCE(SUM(spi.quantity), 0) as quantity,
    spl.id as location_id,
    spl.name as location_name
FROM spare_parts sp
CROSS JOIN spare_part_status_types spt
LEFT JOIN spare_part_inventories spi ON sp.id = spi.part_id AND spt.id = spi.status_id
LEFT JOIN spare_part_locations spl ON spi.location_id = spl.id
WHERE spt.is_active = 1
GROUP BY sp.id, spt.id, spl.id;

-- 8. 创建可用库存汇总视图
CREATE OR REPLACE VIEW `v_spare_part_available_stock` AS
SELECT 
    sp.id as part_id,
    sp.code as part_code,
    sp.name as part_name,
    sp.unit,
    COALESCE(SUM(CASE WHEN spt.category = 'Available' THEN spi.quantity ELSE 0 END), 0) as available_quantity,
    COALESCE(SUM(CASE WHEN spt.category = 'Unavailable' THEN spi.quantity ELSE 0 END), 0) as unavailable_quantity,
    COALESCE(SUM(CASE WHEN spt.category = 'InTransit' THEN spi.quantity ELSE 0 END), 0) as in_transit_quantity,
    COALESCE(SUM(CASE WHEN spt.category = 'Reserved' THEN spi.quantity ELSE 0 END), 0) as reserved_quantity,
    COALESCE(SUM(spi.quantity), 0) as total_quantity
FROM spare_parts sp
LEFT JOIN spare_part_inventories spi ON sp.id = spi.part_id
LEFT JOIN spare_part_status_types spt ON spi.status_id = spt.id AND spt.is_active = 1
GROUP BY sp.id;

-- 9. 数据迁移：将现有库存数据迁移到新的库存明细表
-- 为每个现有备件创建"良好"状态的库存记录
INSERT INTO spare_part_inventories (part_id, location_id, status_id, quantity, notes, created_at, updated_at)
SELECT 
    sp.id,
    sp.location_id,
    (SELECT id FROM spare_part_status_types WHERE code = 'GOOD'),
    sp.quantity,
    '数据迁移：原有库存',
    sp.created_at,
    sp.updated_at
FROM spare_parts sp
WHERE sp.quantity > 0;

-- 10. 创建触发器：自动更新spare_parts表的总库存
DELIMITER ;;
CREATE TRIGGER `update_spare_part_quantity_after_inventory_change`
AFTER INSERT ON `spare_part_inventories`
FOR EACH ROW
BEGIN
    UPDATE spare_parts 
    SET quantity = (
        SELECT COALESCE(SUM(quantity), 0) 
        FROM spare_part_inventories 
        WHERE part_id = NEW.part_id
    )
    WHERE id = NEW.part_id;
END;;

CREATE TRIGGER `update_spare_part_quantity_after_inventory_update`
AFTER UPDATE ON `spare_part_inventories`
FOR EACH ROW
BEGIN
    UPDATE spare_parts 
    SET quantity = (
        SELECT COALESCE(SUM(quantity), 0) 
        FROM spare_part_inventories 
        WHERE part_id = NEW.part_id
    )
    WHERE id = NEW.part_id;
END;;

CREATE TRIGGER `update_spare_part_quantity_after_inventory_delete`
AFTER DELETE ON `spare_part_inventories`
FOR EACH ROW
BEGIN
    UPDATE spare_parts 
    SET quantity = (
        SELECT COALESCE(SUM(quantity), 0) 
        FROM spare_part_inventories 
        WHERE part_id = OLD.part_id
    )
    WHERE id = OLD.part_id;
END;;
DELIMITER ;
