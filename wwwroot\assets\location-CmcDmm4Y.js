import{aj as t}from"./index-C7OOw0MO.js";import{s as o}from"./system-9jEcQzSp.js";const e={getLocationTree:()=>t.get("/Location/tree"),getLocationList:o=>t.get("/Location/list",{params:o}),getLocations:o=>t.get("/Location",{params:o}),getWorkstationLocations:()=>t.get("/Location/workstations"),getLocationsForDropdown:o=>t.get("/Location/dropdown",{params:o}),initRootLocation:()=>t.post("/Location/init"),createLocation:o=>t.post("/Location",o),updateLocation:(o,e)=>t.put(`/Location/${o}`,e),deleteLocation:o=>t.delete(`/Location/${o}`),getLocationDetail:o=>t.get(`/Location/${o}`),getLocationUsers:o=>t({url:`/Location/${o}/users`,method:"get"}),updateLocationUsers:(o,e)=>t.post(`/Location/${o}/users`,e),removeLocationUser:(o,e)=>t({url:`/Location/${o}/users/${e}`,method:"delete"}),getLocationDepartment:o=>t.get(`/Location/${o}/department`),updateLocationDepartment:(o,e)=>t.post(`/Location/${o}/department`,e),searchLocations:o=>t.get("/Location/search",{params:o}),getLocationAssets(o){const e=o.locationId;return delete o.locationId,t.get(`/Location/${e}/assets`,{params:o})},relateDepartment:o=>t.post("/Location/relate-department",o),relateAssets:(o,e)=>t.post(`/Location/${o}/relate-assets`,e),unrelateAssets:(o,e)=>t.post(`/Location/${o}/unrelate-assets`,{assetIds:e}),getLocationHistory:(o,e)=>t.get(`/Location/${o}/history`,{params:e}),getLocationUsersBatch:o=>t.post("/Location/users/batch",{locationIds:o}),setLocationDepartment:(o,e)=>t({url:`/Location/${o}/department`,method:"post",data:{departmentId:e}}),setLocationUsers:(o,e,a=!1)=>t({url:`/Location/${o}/users`,method:"post",data:{users:e,replaceExisting:a}})};export{e as l};
