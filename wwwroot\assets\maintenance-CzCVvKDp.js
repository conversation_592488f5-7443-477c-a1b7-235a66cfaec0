import{aK as e,_ as a,r as t,a5 as l,m as r,a8 as s,b as o,d as n,e as c,w as i,f as d,aB as u,V as p,a as m,aJ as g,o as y,A as f,F as b,h as v,v as w,aY as h,aq as _,Y as k,t as R,a9 as $,aC as T,b1 as V,b2 as x,aE as z,a1 as C}from"./index-CkwLz8y6.js";const S="/ReturnToFactory",P={getReturnToFactoryList:a=>e.get(S,a),getReturnToFactoryById:a=>e.get(`${S}/${a}`),createReturnToFactory:a=>e.post(S,a),updateReturnToFactory:(a,t)=>e.put(`${S}/${a}`,t),deleteReturnToFactory:a=>e.delete(`${S}/${a}`),updateReturnStatus:(a,t)=>e.put(`${S}/${a}/status`,t),completeReturn:(a,t)=>e.post(`${S}/${a}/complete`,t),replenishSpareParts:(a,t)=>e.post(`${S}/${a}/replenish-spare-parts`,t),getReturnStatistics:a=>e.get(`${S}/statistics`,a),exportReturns:a=>e.download(`${S}/export`,a,"returns.xlsx")},F={class:"maintenance-container"},U={class:"page-header"},K={class:"page-actions"},B={class:"filter-container"},Y={class:"asset-info"},N={class:"asset-name"},D={class:"asset-code text-secondary"},I={class:"asset-sn text-secondary"},L={class:"pagination-container"},j=a({__name:"maintenance",setup(e){const a=t(!1),S=t("all"),j=t([]),M=t(null),q=l({currentPage:1,pageSize:10,total:0}),A=l({code:"",assetKeyword:"",type:"",status:"",timeRange:[]}),E=[{label:"返厂维修",value:"factory"},{label:"第三方维修",value:"third_party"},{label:"保修服务",value:"warranty"}],J=[{label:"待发出",value:"pending"},{label:"维修中",value:"repairing"},{label:"已完成",value:"completed"},{label:"已取消",value:"cancelled"}];r((()=>{G()}));const G=async()=>{var e,t;a.value=!0;const l={page:q.currentPage,pageSize:q.pageSize,code:A.code,assetKeyword:A.assetKeyword,type:A.type,status:"all"===S.value?A.status:S.value,startTime:null==(e=A.timeRange)?void 0:e[0],endTime:null==(t=A.timeRange)?void 0:t[1]};try{const e=await P.getReturnToFactoryList(l);e.success?(j.value=e.data.items||e.data||[],q.total=e.data.total||e.data.length||0):(s.error(e.message||"获取维修列表失败"),j.value=[],q.total=0)}catch(r){s.error("获取维修列表失败"),j.value=[],q.total=0}finally{a.value=!1}},H=()=>{q.currentPage=1,G()},O=()=>{q.currentPage=1,G()},Q=()=>{A.code="",A.assetKeyword="",A.type="",A.status="",A.timeRange=[],q.currentPage=1,G()},W=e=>{q.pageSize=e,G()},X=e=>{q.currentPage=e,G()},Z=(e,a)=>{let t="",l="",r="";"send"===a?(t="确认发出",l=`确认将资产 ${e.assetName} 发出维修吗？`,r="已更新为维修中状态"):"receive"===a&&(t="确认接收",l=`确认已接收维修完成的资产 ${e.assetName} 吗？`,r="已更新为维修完成状态"),C.confirm(l,t,{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{const t=await P.updateReturnStatus(e.id,{status:"send"===a?"sent":"returned",notes:`${"send"===a?"发出维修":"接收维修完成"} - ${(new Date).toLocaleString()}`});t.success?(s.success(r),G()):s.error(t.message||"状态更新失败")}catch(t){s.error("状态更新失败")}})).catch((()=>{}))},ee=()=>{s.info("打开新建维修单页面")},ae=()=>{s.success("开始导出数据，请稍候...")},te=e=>({factory:"返厂维修",third_party:"第三方维修",warranty:"保修服务"}[e]||"未知"),le=e=>({pending:"待发出",repairing:"维修中",completed:"已完成",cancelled:"已取消"}[e]||"未知");return(e,t)=>{const l=m("el-button"),r=m("el-input"),re=m("el-form-item"),se=m("el-option"),oe=m("el-select"),ne=m("el-date-picker"),ce=m("el-form"),ie=m("el-card"),de=m("el-tab-pane"),ue=m("el-tabs"),pe=m("el-table-column"),me=m("el-tag"),ge=m("el-table"),ye=m("el-pagination"),fe=g("loading");return y(),o("div",F,[n("div",U,[t[10]||(t[10]=n("h2",{class:"page-title"},"返厂/维修管理",-1)),n("div",K,[c(l,{type:"primary",onClick:ee,icon:d(u)},{default:i((()=>t[8]||(t[8]=[f(" 创建维修单 ")]))),_:1},8,["icon"]),c(l,{type:"primary",onClick:ae,icon:d(p)},{default:i((()=>t[9]||(t[9]=[f(" 导出数据 ")]))),_:1},8,["icon"])])]),c(ie,{class:"filter-card"},{default:i((()=>[n("div",B,[c(ce,{inline:!0,model:A,class:"filter-form"},{default:i((()=>[c(re,{label:"单号"},{default:i((()=>[c(r,{modelValue:A.code,"onUpdate:modelValue":t[0]||(t[0]=e=>A.code=e),placeholder:"维修单号",clearable:""},null,8,["modelValue"])])),_:1}),c(re,{label:"资产信息"},{default:i((()=>[c(r,{modelValue:A.assetKeyword,"onUpdate:modelValue":t[1]||(t[1]=e=>A.assetKeyword=e),placeholder:"资产名称/编号/SN",clearable:""},null,8,["modelValue"])])),_:1}),c(re,{label:"类型"},{default:i((()=>[c(oe,{modelValue:A.type,"onUpdate:modelValue":t[2]||(t[2]=e=>A.type=e),placeholder:"全部类型",clearable:""},{default:i((()=>[(y(),o(b,null,v(E,(e=>c(se,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),c(re,{label:"状态"},{default:i((()=>[c(oe,{modelValue:A.status,"onUpdate:modelValue":t[3]||(t[3]=e=>A.status=e),placeholder:"全部状态",clearable:""},{default:i((()=>[(y(),o(b,null,v(J,(e=>c(se,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),c(re,{label:"申请时间"},{default:i((()=>[c(ne,{modelValue:A.timeRange,"onUpdate:modelValue":t[4]||(t[4]=e=>A.timeRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),c(re,null,{default:i((()=>[c(l,{type:"primary",onClick:O,icon:d(w)},{default:i((()=>t[11]||(t[11]=[f(" 搜索 ")]))),_:1},8,["icon"]),c(l,{onClick:Q,icon:d(h)},{default:i((()=>t[12]||(t[12]=[f(" 重置 ")]))),_:1},8,["icon"])])),_:1})])),_:1},8,["model"])])])),_:1}),c(ie,{class:"data-card"},{default:i((()=>[c(ue,{modelValue:S.value,"onUpdate:modelValue":t[5]||(t[5]=e=>S.value=e),onTabClick:H},{default:i((()=>[c(de,{label:"全部",name:"all"}),c(de,{label:"待发出",name:"pending"}),c(de,{label:"维修中",name:"repairing"}),c(de,{label:"已完成",name:"completed"})])),_:1},8,["modelValue"]),_((y(),k(ge,{ref_key:"maintenanceTable",ref:M,data:j.value,border:"",style:{width:"100%"}},{default:i((()=>[c(pe,{prop:"code",label:"维修单号",width:"150",sortable:""}),c(pe,{prop:"assetInfo",label:"资产信息",width:"220","show-overflow-tooltip":""},{default:i((e=>[n("div",Y,[n("div",N,R(e.row.assetName),1),n("div",D,R(e.row.assetCode),1),n("div",I,"SN: "+R(e.row.assetSn),1)])])),_:1}),c(pe,{prop:"type",label:"类型",width:"100"},{default:i((e=>{return[c(me,{type:(a=e.row.type,{factory:"primary",third_party:"warning",warranty:"success"}[a]||""),size:"small"},{default:i((()=>[f(R(te(e.row.type)),1)])),_:2},1032,["type"])];var a})),_:1}),c(pe,{prop:"status",label:"状态",width:"120"},{default:i((e=>{return[c(me,{type:(a=e.row.status,{pending:"info",repairing:"warning",completed:"success",cancelled:"danger"}[a]||""),size:"small"},{default:i((()=>[f(R(le(e.row.status)),1)])),_:2},1032,["type"])];var a})),_:1}),c(pe,{prop:"vendor",label:"维修厂商",width:"150"}),c(pe,{prop:"applicant",label:"申请人",width:"100"}),c(pe,{prop:"applyTime",label:"申请时间",width:"180",sortable:""}),c(pe,{prop:"sendTime",label:"发出时间",width:"180",sortable:""}),c(pe,{prop:"estimatedReturnTime",label:"预计返回时间",width:"180"}),c(pe,{prop:"actualReturnTime",label:"实际返回时间",width:"180"}),c(pe,{prop:"cost",label:"维修费用",width:"120"},{default:i((e=>[f(R(e.row.cost?`￥${e.row.cost}`:"-"),1)])),_:1}),c(pe,{label:"操作",width:"240",fixed:"right"},{default:i((e=>[c(l,{type:"primary",text:"",size:"small",onClick:a=>{return t=e.row,void s.info(`查看维修单详情：${t.code}`);var t},icon:d(T)},{default:i((()=>t[13]||(t[13]=[f(" 详情 ")]))),_:2},1032,["onClick","icon"]),"pending"===e.row.status?(y(),k(l,{key:0,type:"success",text:"",size:"small",onClick:a=>Z(e.row,"send"),icon:d(V)},{default:i((()=>t[14]||(t[14]=[f(" 发出 ")]))),_:2},1032,["onClick","icon"])):$("",!0),"repairing"===e.row.status?(y(),k(l,{key:1,type:"warning",text:"",size:"small",onClick:a=>Z(e.row,"receive"),icon:d(x)},{default:i((()=>t[15]||(t[15]=[f(" 接收 ")]))),_:2},1032,["onClick","icon"])):$("",!0),"pending"===e.row.status?(y(),k(l,{key:2,type:"danger",text:"",size:"small",onClick:a=>{return t=e.row,void C.confirm(`确认删除维修单 ${t.code} 吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{const e=await P.deleteReturnToFactory(t.id);e.success?(s.success("删除成功"),G()):s.error(e.message||"删除失败")}catch(e){s.error("删除失败")}})).catch((()=>{}));var t},icon:d(z)},{default:i((()=>t[16]||(t[16]=[f(" 删除 ")]))),_:2},1032,["onClick","icon"])):$("",!0)])),_:1})])),_:1},8,["data"])),[[fe,a.value]]),n("div",L,[c(ye,{"current-page":q.currentPage,"onUpdate:currentPage":t[6]||(t[6]=e=>q.currentPage=e),"page-size":q.pageSize,"onUpdate:pageSize":t[7]||(t[7]=e=>q.pageSize=e),"page-sizes":[10,20,50,100],background:!0,layout:"total, sizes, prev, pager, next, jumper",total:q.total,onSizeChange:W,onCurrentChange:X},null,8,["current-page","page-size","total"])])])),_:1})])}}},[["__scopeId","data-v-706caf45"]]);export{j as default};
