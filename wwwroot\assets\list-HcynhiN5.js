import{aK as e,_ as a,r as l,a5 as t,m as o,a8 as r,b as u,d as i,e as n,w as s,f as d,aB as c,V as p,a as m,aJ as h,o as v,A as g,F as f,h as y,v as b,aY as w,aq as V,Y as _,t as P,a9 as $,aC as x,ab as C,af as I,K as k,P as U,i as A,a1 as S}from"./index-CkwLz8y6.js";import{a as z}from"./spareparts-Cv2l4Tzu.js";const q="/v2/purchase",Q={getPurchaseList:a=>e.get(q,a),getPurchaseById:a=>e.get(`${q}/${a}`),createPurchase:a=>e.post(q,a),createPurchaseOrder:a=>e.post(q,a),updatePurchase:(a,l)=>e.put(`${q}/${a}`,l),deletePurchase:a=>e.delete(`${q}/${a}`),getSupplierList:a=>e.get("/suppliers",a),getSupplierById:a=>e.get(`/suppliers/${a}`),createSupplier:a=>e.post("/suppliers",a),updateSupplier:(a,l)=>e.put(`/suppliers/${a}`,l),deleteSupplier:a=>e.delete(`/suppliers/${a}`),submitPurchaseForApproval:a=>e.put(`${q}/${a}/submit`),approvePurchase:(a,l)=>e.put(`${q}/${a}/approve`,l),rejectPurchase:(a,l)=>e.put(`${q}/${a}/reject`,l),confirmPurchaseOrder:(a,l)=>e.put(`${q}/${a}/order`,l),confirmPurchaseReceived:(a,l)=>e.put(`${q}/${a}/receive`,l),confirmPurchaseStorage:(a,l)=>e.put(`${q}/${a}/storage`,l),completePurchase:a=>e.put(`${q}/${a}/complete`),cancelPurchase:(a,l)=>e.put(`${q}/${a}/cancel`,l),getPurchaseApprovalHistory:(a,l)=>e.get(`${q}/${a}/approval-history`,l),uploadPurchaseAttachment:(a,l)=>e.upload(`${q}/${a}/attachments`,l),getPurchaseAttachments:(a,l)=>e.get(`${q}/${a}/attachments`,l),deletePurchaseAttachment:(a,l)=>e.delete(`${q}/${a}/attachments/${l}`),exportPurchases:a=>e.download(`${q}/export`,a,"purchases.xlsx"),getPurchaseStatistics:a=>e.get(`${q}/statistics`,a),getSuppliersV2:()=>e.get(`${q}/suppliers`),getPurchasableAssetTypes:()=>e.get(`${q}/asset-types`),processDeliveredItems:(a,l)=>e.post(`${q}/${a}/process-items`,l),approvePurchase:a=>e.put(`${q}/${a}/approve`),rejectPurchase:(a,l)=>e.put(`${q}/${a}/reject`,l),completePurchase:(a,l)=>e.put(`${q}/${a}/complete`,l)},N={class:"purchase-list-container"},L={class:"page-header"},T={class:"page-actions"},B={class:"filter-container"},D={key:0,class:"item-count-badge"},j={class:"pagination-container"},R={class:"receive-dialog-content"},Y={class:"order-info"},F={class:"items-conversion"},O={class:"conversion-tips"},K={class:"dialog-footer"},M={class:"purchase-items"},H={class:"dialog-footer"},J=a({__name:"list",setup(e){const a=l(!1),q=l("all"),J=l([]),W=l(null),E=l([]),G=l([]),X=l(!1),Z=t({currentPage:1,pageSize:10,total:0}),ee=t({code:"",name:"",type:"",status:"",timeRange:[]}),ae=[{label:"新设备",value:"new_device"},{label:"更换设备",value:"replacement"},{label:"配件",value:"accessories"},{label:"软件",value:"software"},{label:"服务",value:"service"}],le=[{label:"待审批",value:"pending"},{label:"已审批",value:"approved"},{label:"已采购",value:"purchased"},{label:"已入库",value:"received"},{label:"已拒绝",value:"rejected"},{label:"已取消",value:"cancelled"}];o((()=>{te(),(async()=>{try{const e=await Q.getSuppliersV2();e.success?E.value=e.data||[]:E.value=[{id:1,name:"联想"},{id:2,name:"惠普"},{id:3,name:"戴尔"},{id:4,name:"华为"}]}catch(e){E.value=[{id:1,name:"联想"},{id:2,name:"惠普"},{id:3,name:"戴尔"},{id:4,name:"华为"}]}})()}));const te=async()=>{var e;a.value=!0;const l={pageIndex:Z.currentPage,pageSize:Z.pageSize,orderCode:ee.code,title:ee.name,status:"all"===q.value?ee.status:q.value};try{const a=await Q.getPurchaseList(l);a.success?(J.value=a.data||[],Z.total=(null==(e=a.pagination)?void 0:e.totalCount)||0):(r.error(a.message||"获取采购列表失败"),J.value=[],Z.total=0)}catch(t){r.error("获取采购列表失败"),J.value=[],Z.total=0}finally{a.value=!1}},oe=()=>{Z.currentPage=1,te()},re=()=>{Z.currentPage=1,te()},ue=()=>{ee.code="",ee.name="",ee.type="",ee.status="",ee.timeRange=[],Z.currentPage=1,te()},ie=e=>{Z.pageSize=e,te()},ne=e=>{Z.currentPage=e,te()},se=l(!1),de=l(null),ce=t({items:[]}),pe=e=>{de.value=e,ce.items=e.items.map((e=>({...e,toAssetQuantity:0,toSparePartQuantity:e.quantity,assetLocationId:null,sparePartLocationId:null}))),se.value=!0},me=async()=>{try{for(const l of ce.items){if(l.toAssetQuantity+l.toSparePartQuantity!==l.quantity)return void r.warning(`${l.name} 的转化数量总和必须等于采购数量`);if(l.toAssetQuantity>0&&!l.assetLocationId)return void r.warning(`${l.name} 转为资产时必须选择资产位置`);if(l.toSparePartQuantity>0&&!l.sparePartLocationId)return void r.warning(`${l.name} 转为备件时必须选择备件库位`)}const e=ce.items.map((e=>({purchaseItemId:e.id,toSparePartQuantity:e.toSparePartQuantity,sparePartLocationId:e.sparePartLocationId,toAssetQuantity:e.toAssetQuantity,assetLocationId:e.assetLocationId}))),a=await Q.processDeliveredItems(de.value.id,e);a.success?(r.success("入库转化成功"),se.value=!1,te()):r.error(a.message||"入库转化失败")}catch(e){r.error("入库转化失败："+e.message)}},he=()=>{r.success("开始导出数据，请稍候...")},ve=()=>{Ve()},ge=l(!1),fe=l(!1),ye=l(null),be=t({name:"",type:"",supplierId:"",estimatedAmount:0,description:"",items:[{sparePartId:null,materialNumber:"",name:"",quantity:1,unitPrice:0,specification:"",notes:""}]}),we={name:[{required:!0,message:"请输入采购名称",trigger:"blur"},{min:2,max:100,message:"采购名称长度在 2 到 100 个字符",trigger:"blur"}],type:[{required:!0,message:"请选择采购类型",trigger:"change"}],supplierId:[{required:!0,message:"请选择供应商",trigger:"change"}],estimatedAmount:[{required:!0,message:"请输入预计金额",trigger:"blur"},{type:"number",min:0,message:"预计金额必须大于等于0",trigger:"blur"}]},Ve=()=>{_e(),ge.value=!0},_e=()=>{var e;be.name="",be.type="",be.supplierId="",be.estimatedAmount=0,be.description="",be.items=[{sparePartId:null,materialNumber:"",name:"",quantity:1,unitPrice:0,specification:"",notes:""}],null==(e=ye.value)||e.clearValidate()},Pe=()=>{be.items.push({sparePartId:null,materialNumber:"",name:"",quantity:1,unitPrice:0,specification:"",notes:""})},$e=e=>{},xe=async()=>{var e;try{await(null==(e=ye.value)?void 0:e.validate())}catch(l){return}const a=be.items.filter((e=>e.name&&e.quantity>0));if(0!==a.length){fe.value=!0;try{const e={supplierId:parseInt(be.supplierId),requesterId:1,expectedDeliveryDate:null,notes:be.description,items:a.map((e=>({itemName:e.name,itemCode:e.materialNumber||"",specification:e.specification,assetTypeId:1,unitPrice:parseFloat(e.unitPrice),quantity:parseInt(e.quantity),notes:e.notes||""})))},l=await Q.createPurchaseOrder(e);l.success?(r.success("采购订单创建成功"),ge.value=!1,te()):r.error(l.message||"创建采购订单失败")}catch(l){r.error("创建采购订单失败")}finally{fe.value=!1}}else r.warning("请至少添加一个有效的采购物品")},Ce=e=>{if(!e)return"-";if("0001-01-01T00:00:00"===e||e.startsWith("0001-01-01"))return"-";try{const a=new Date(e);return isNaN(a.getTime())?"-":a.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch(a){return"-"}};return(e,l)=>{const t=m("el-button"),o=m("el-input"),Ve=m("el-form-item"),Ie=m("el-option"),ke=m("el-select"),Ue=m("el-date-picker"),Ae=m("el-form"),Se=m("el-card"),ze=m("el-tab-pane"),qe=m("el-tabs"),Qe=m("el-table-column"),Ne=m("el-tag"),Le=m("el-table"),Te=m("el-pagination"),Be=m("el-input-number"),De=m("el-alert"),je=m("el-dialog"),Re=m("el-col"),Ye=m("el-row"),Fe=h("loading");return v(),u("div",N,[i("div",L,[l[19]||(l[19]=i("h2",{class:"page-title"},"采购列表",-1)),i("div",T,[n(t,{type:"primary",onClick:ve,icon:d(c)},{default:s((()=>l[17]||(l[17]=[g(" 新建采购 ")]))),_:1},8,["icon"]),n(t,{type:"primary",onClick:he,icon:d(p)},{default:s((()=>l[18]||(l[18]=[g(" 导出数据 ")]))),_:1},8,["icon"])])]),n(Se,{class:"filter-card"},{default:s((()=>[i("div",B,[n(Ae,{inline:!0,model:ee,class:"filter-form"},{default:s((()=>[n(Ve,{label:"采购单号"},{default:s((()=>[n(o,{modelValue:ee.code,"onUpdate:modelValue":l[0]||(l[0]=e=>ee.code=e),placeholder:"采购单号",clearable:""},null,8,["modelValue"])])),_:1}),n(Ve,{label:"采购名称"},{default:s((()=>[n(o,{modelValue:ee.name,"onUpdate:modelValue":l[1]||(l[1]=e=>ee.name=e),placeholder:"采购名称",clearable:""},null,8,["modelValue"])])),_:1}),n(Ve,{label:"采购类型"},{default:s((()=>[n(ke,{modelValue:ee.type,"onUpdate:modelValue":l[2]||(l[2]=e=>ee.type=e),placeholder:"全部类型",clearable:""},{default:s((()=>[(v(),u(f,null,y(ae,(e=>n(Ie,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),n(Ve,{label:"状态"},{default:s((()=>[n(ke,{modelValue:ee.status,"onUpdate:modelValue":l[3]||(l[3]=e=>ee.status=e),placeholder:"全部状态",clearable:""},{default:s((()=>[(v(),u(f,null,y(le,(e=>n(Ie,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),n(Ve,{label:"采购时间"},{default:s((()=>[n(Ue,{modelValue:ee.timeRange,"onUpdate:modelValue":l[4]||(l[4]=e=>ee.timeRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),n(Ve,null,{default:s((()=>[n(t,{type:"primary",onClick:re,icon:d(b)},{default:s((()=>l[20]||(l[20]=[g(" 搜索 ")]))),_:1},8,["icon"]),n(t,{onClick:ue,icon:d(w)},{default:s((()=>l[21]||(l[21]=[g(" 重置 ")]))),_:1},8,["icon"])])),_:1})])),_:1},8,["model"])])])),_:1}),n(Se,{class:"data-card"},{default:s((()=>[n(qe,{modelValue:q.value,"onUpdate:modelValue":l[5]||(l[5]=e=>q.value=e),onTabClick:oe},{default:s((()=>[n(ze,{label:"全部",name:"all"}),n(ze,{label:"待审批",name:"pending"}),n(ze,{label:"待采购",name:"approved"}),n(ze,{label:"已采购",name:"purchased"}),n(ze,{label:"已入库",name:"received"})])),_:1},8,["modelValue"]),V((v(),_(Le,{ref_key:"purchaseTable",ref:W,data:J.value,border:"",style:{width:"100%"}},{default:s((()=>[n(Qe,{prop:"orderCode",label:"采购单号",width:"150",sortable:""}),n(Qe,{label:"物料编号",width:"120","show-overflow-tooltip":""},{default:s((e=>[g(P(e.row.primaryItemCode||"-"),1)])),_:1}),n(Qe,{label:"物料名称","min-width":"180","show-overflow-tooltip":""},{default:s((e=>[g(P(e.row.primaryItemName||"-")+" ",1),e.row.itemCount>1?(v(),u("span",D," +"+P(e.row.itemCount-1),1)):$("",!0)])),_:1}),n(Qe,{prop:"description",label:"类型",width:"100","show-overflow-tooltip":""}),n(Qe,{prop:"status",label:"状态",width:"120"},{default:s((e=>{return[n(Ne,{type:(a=e.row.status,{pending:"info",approved:"success",purchased:"warning",received:"primary",rejected:"danger",cancelled:"danger"}[a]||""),size:"small"},{default:s((()=>[g(P(e.row.statusName),1)])),_:2},1032,["type"])];var a})),_:1}),n(Qe,{prop:"totalAmount",label:"金额",width:"120"},{default:s((e=>[g(" ￥"+P((e.row.totalAmount||0).toLocaleString()),1)])),_:1}),n(Qe,{label:"数量",width:"80",align:"center"},{default:s((e=>[g(P(e.row.totalQuantity||0),1)])),_:1}),n(Qe,{prop:"applicantName",label:"申请人",width:"100"}),n(Qe,{prop:"applicationTime",label:"申请时间",width:"180",sortable:""},{default:s((e=>[g(P(Ce(e.row.applicationTime)),1)])),_:1}),n(Qe,{label:"审批人",width:"100"},{default:s((e=>[g(P(e.row.approverName||"-"),1)])),_:1}),n(Qe,{prop:"createdAt",label:"创建时间",width:"180",sortable:""},{default:s((e=>[g(P(Ce(e.row.createdAt)),1)])),_:1}),n(Qe,{prop:"supplierName",label:"供应商",width:"150","show-overflow-tooltip":""}),n(Qe,{label:"入库时间",width:"180",sortable:""},{default:s((e=>[g(P(e.row.actualDeliveryDate?Ce(e.row.actualDeliveryDate):"-"),1)])),_:1}),n(Qe,{label:"操作",width:"220",fixed:"right"},{default:s((e=>[n(t,{type:"primary",text:"",size:"small",onClick:a=>{return l=e.row,void r.info(`查看采购单详情：${l.code}`);var l},icon:d(x)},{default:s((()=>l[22]||(l[22]=[g(" 详情 ")]))),_:2},1032,["onClick","icon"]),"pending"===e.row.status?(v(),u(f,{key:0},[n(t,{type:"success",text:"",size:"small",onClick:a=>{return l=e.row,void S.confirm(`确认审批通过采购单"${l.name}"吗？`,"审批确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{const e=await Q.approvePurchase(l.id);e.success?(r.success("审批通过成功"),te()):r.error(e.message||"审批失败")}catch(e){r.error("审批失败")}})).catch((()=>{}));var l},icon:d(C)},{default:s((()=>l[23]||(l[23]=[g(" 审批 ")]))),_:2},1032,["onClick","icon"]),n(t,{type:"danger",text:"",size:"small",onClick:a=>{return l=e.row,void S.prompt("请输入拒绝原因","拒绝理由",{confirmButtonText:"确定",cancelButtonText:"取消",inputType:"textarea",inputPlaceholder:"请输入拒绝理由..."}).then((async({value:e})=>{if(e)try{const a=await Q.rejectPurchase(l.id,{reason:e});a.success?(r.success("已拒绝该采购申请"),te()):r.error(a.message||"拒绝失败")}catch(a){r.error("拒绝失败")}else r.warning("请输入拒绝理由")})).catch((()=>{}));var l},icon:d(I)},{default:s((()=>l[24]||(l[24]=[g(" 拒绝 ")]))),_:2},1032,["onClick","icon"])],64)):$("",!0),"approved"===e.row.status?(v(),_(t,{key:1,type:"success",text:"",size:"small",onClick:a=>{return l=e.row,void S.prompt("请输入供应商信息","完成采购",{confirmButtonText:"确定",cancelButtonText:"取消",inputPlaceholder:"请输入供应商名称"}).then((async({value:e})=>{if(e)try{const a=await Q.completePurchase(l.id,{vendor:e});a.success?(r.success("采购完成"),te()):r.error(a.message||"完成采购失败")}catch(a){r.error("完成采购失败")}else r.warning("请输入供应商信息")})).catch((()=>{}));var l},icon:d(k)},{default:s((()=>l[25]||(l[25]=[g(" 完成采购 ")]))),_:2},1032,["onClick","icon"])):$("",!0),"purchased"===e.row.status?(v(),_(t,{key:2,type:"warning",text:"",size:"small",onClick:a=>{return l=e.row,void pe(l);var l},icon:d(U)},{default:s((()=>l[26]||(l[26]=[g(" 入库 ")]))),_:2},1032,["onClick","icon"])):$("",!0)])),_:1})])),_:1},8,["data"])),[[Fe,a.value]]),i("div",j,[n(Te,{"current-page":Z.currentPage,"onUpdate:currentPage":l[6]||(l[6]=e=>Z.currentPage=e),"page-size":Z.pageSize,"onUpdate:pageSize":l[7]||(l[7]=e=>Z.pageSize=e),"page-sizes":[10,20,50,100],background:!0,layout:"total, sizes, prev, pager, next, jumper",total:Z.total,onSizeChange:ie,onCurrentChange:ne},null,8,["current-page","page-size","total"])])])),_:1}),n(je,{modelValue:se.value,"onUpdate:modelValue":l[9]||(l[9]=e=>se.value=e),title:"采购物品入库转化",width:"80%","close-on-click-modal":!1},{footer:s((()=>[i("span",K,[n(t,{onClick:l[8]||(l[8]=e=>se.value=!1)},{default:s((()=>l[35]||(l[35]=[g("取消")]))),_:1}),n(t,{type:"primary",onClick:me},{default:s((()=>l[36]||(l[36]=[g("确认入库")]))),_:1})])])),default:s((()=>{var e,a,t;return[i("div",R,[i("div",Y,[l[30]||(l[30]=i("h4",null,"采购单信息",-1)),i("p",null,[l[27]||(l[27]=i("strong",null,"采购单号：",-1)),g(P(null==(e=de.value)?void 0:e.code),1)]),i("p",null,[l[28]||(l[28]=i("strong",null,"采购名称：",-1)),g(P(null==(a=de.value)?void 0:a.name),1)]),i("p",null,[l[29]||(l[29]=i("strong",null,"供应商：",-1)),g(P(null==(t=de.value)?void 0:t.vendor),1)])]),i("div",F,[l[33]||(l[33]=i("h4",null,"物品转化设置",-1)),n(Le,{data:ce.items,border:"",style:{width:"100%"}},{default:s((()=>[n(Qe,{prop:"name",label:"物品名称",width:"150"}),n(Qe,{prop:"model",label:"型号规格",width:"150"}),n(Qe,{prop:"quantity",label:"采购数量",width:"100",align:"center"}),n(Qe,{label:"转为资产",width:"200",align:"center"},{default:s((e=>[n(Be,{modelValue:e.row.toAssetQuantity,"onUpdate:modelValue":a=>e.row.toAssetQuantity=a,min:0,max:e.row.quantity,size:"small",style:{width:"80px"}},null,8,["modelValue","onUpdate:modelValue","max"]),l[31]||(l[31]=i("br",null,null,-1)),n(ke,{modelValue:e.row.assetLocationId,"onUpdate:modelValue":a=>e.row.assetLocationId=a,placeholder:"选择位置",size:"small",style:{width:"100%","margin-top":"5px"},disabled:0===e.row.toAssetQuantity},{default:s((()=>[n(Ie,{label:"办公室A",value:1}),n(Ie,{label:"办公室B",value:2}),n(Ie,{label:"会议室",value:3}),n(Ie,{label:"机房",value:4})])),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])])),_:1}),n(Qe,{label:"转为备件",width:"200",align:"center"},{default:s((e=>[n(Be,{modelValue:e.row.toSparePartQuantity,"onUpdate:modelValue":a=>e.row.toSparePartQuantity=a,min:0,max:e.row.quantity,size:"small",style:{width:"80px"}},null,8,["modelValue","onUpdate:modelValue","max"]),l[32]||(l[32]=i("br",null,null,-1)),n(ke,{modelValue:e.row.sparePartLocationId,"onUpdate:modelValue":a=>e.row.sparePartLocationId=a,placeholder:"选择库位",size:"small",style:{width:"100%","margin-top":"5px"},disabled:0===e.row.toSparePartQuantity},{default:s((()=>[n(Ie,{label:"备件库A区",value:1}),n(Ie,{label:"备件库B区",value:2}),n(Ie,{label:"备件库C区",value:3})])),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])])),_:1}),n(Qe,{label:"剩余数量",width:"100",align:"center"},{default:s((e=>[i("span",{class:A({"text-danger":e.row.quantity-e.row.toAssetQuantity-e.row.toSparePartQuantity!=0})},P(e.row.quantity-e.row.toAssetQuantity-e.row.toSparePartQuantity),3)])),_:1})])),_:1},8,["data"])]),i("div",O,[n(De,{title:"转化说明",type:"info",closable:!1,"show-icon":""},{default:s((()=>l[34]||(l[34]=[i("ul",null,[i("li",null,"每个物品的转化数量总和必须等于采购数量"),i("li",null,"转为资产：物品将作为固定资产进行管理，生成资产编号"),i("li",null,"转为备件：物品将进入备品备件库存，用于维修和更换"),i("li",null,"可以同时转化为资产和备件，按需分配数量")],-1)]))),_:1})])])]})),_:1},8,["modelValue"]),n(je,{modelValue:ge.value,"onUpdate:modelValue":l[16]||(l[16]=e=>ge.value=e),title:"新建采购",width:"800px","close-on-click-modal":!1,onClose:_e},{footer:s((()=>[i("div",H,[n(t,{onClick:l[15]||(l[15]=e=>ge.value=!1)},{default:s((()=>l[39]||(l[39]=[g("取消")]))),_:1}),n(t,{type:"primary",onClick:xe,loading:fe.value},{default:s((()=>l[40]||(l[40]=[g(" 确定 ")]))),_:1},8,["loading"])])])),default:s((()=>[n(Ae,{ref_key:"createFormRef",ref:ye,model:be,rules:we,"label-width":"120px","label-position":"right"},{default:s((()=>[n(Ye,{gutter:20},{default:s((()=>[n(Re,{span:12},{default:s((()=>[n(Ve,{label:"采购名称",prop:"name"},{default:s((()=>[n(o,{modelValue:be.name,"onUpdate:modelValue":l[10]||(l[10]=e=>be.name=e),placeholder:"请输入采购名称",maxlength:"100","show-word-limit":""},null,8,["modelValue"])])),_:1})])),_:1}),n(Re,{span:12},{default:s((()=>[n(Ve,{label:"采购类型",prop:"type"},{default:s((()=>[n(ke,{modelValue:be.type,"onUpdate:modelValue":l[11]||(l[11]=e=>be.type=e),placeholder:"请选择采购类型",style:{width:"100%"}},{default:s((()=>[n(Ie,{label:"设备采购",value:"equipment"}),n(Ie,{label:"软件采购",value:"software"}),n(Ie,{label:"服务采购",value:"service"}),n(Ie,{label:"备件采购",value:"spare_parts"})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(Ye,{gutter:20},{default:s((()=>[n(Re,{span:12},{default:s((()=>[n(Ve,{label:"供应商",prop:"supplierId"},{default:s((()=>[n(ke,{modelValue:be.supplierId,"onUpdate:modelValue":l[12]||(l[12]=e=>be.supplierId=e),placeholder:"请选择供应商",style:{width:"100%"},filterable:"","allow-create":"",onChange:$e},{default:s((()=>[(v(!0),u(f,null,y(E.value,(e=>(v(),_(Ie,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1}),n(Re,{span:12},{default:s((()=>[n(Ve,{label:"预计金额",prop:"estimatedAmount"},{default:s((()=>[n(Be,{modelValue:be.estimatedAmount,"onUpdate:modelValue":l[13]||(l[13]=e=>be.estimatedAmount=e),min:0,precision:2,placeholder:"请输入预计金额",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(Ve,{label:"采购说明",prop:"description"},{default:s((()=>[n(o,{modelValue:be.description,"onUpdate:modelValue":l[14]||(l[14]=e=>be.description=e),type:"textarea",rows:3,placeholder:"请输入采购说明",maxlength:"500","show-word-limit":""},null,8,["modelValue"])])),_:1}),n(Ve,{label:"采购物品"},{default:s((()=>[i("div",M,[(v(!0),u(f,null,y(be.items,((e,a)=>(v(),u("div",{key:a,class:"purchase-item"},[n(Ye,{gutter:10,align:"middle",style:{"margin-bottom":"10px"}},{default:s((()=>[n(Re,{span:5},{default:s((()=>[n(ke,{modelValue:e.sparePartId,"onUpdate:modelValue":a=>e.sparePartId=a,placeholder:"选择备件",filterable:"",remote:"","remote-method":e=>(async e=>{if(e){X.value=!0;try{const a=await z(e);a.success?G.value=a.data||[]:G.value=[]}catch(a){G.value=[]}finally{X.value=!1}}else G.value=[]})(e),loading:X.value,onChange:e=>((e,a)=>{const l=G.value.find((a=>a.id===e));if(l){const e=be.items[a];e.materialNumber=l.materialNumber||l.code,e.name=l.name,e.specification=l.specification||""}})(e,a),style:{width:"100%"}},{default:s((()=>[(v(!0),u(f,null,y(G.value,(e=>(v(),_(Ie,{key:e.id,label:`${e.materialNumber||e.code} - ${e.name}`,value:e.id},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue","remote-method","loading","onChange"])])),_:2},1024),n(Re,{span:4},{default:s((()=>[n(o,{modelValue:e.materialNumber,"onUpdate:modelValue":a=>e.materialNumber=a,placeholder:"物料编号",readonly:"",style:{"background-color":"#f5f7fa"}},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024),n(Re,{span:5},{default:s((()=>[n(o,{modelValue:e.name,"onUpdate:modelValue":a=>e.name=a,placeholder:"物品名称",maxlength:"100"},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024),n(Re,{span:3},{default:s((()=>[n(Be,{modelValue:e.quantity,"onUpdate:modelValue":a=>e.quantity=a,min:1,placeholder:"数量",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024),n(Re,{span:3},{default:s((()=>[n(Be,{modelValue:e.unitPrice,"onUpdate:modelValue":a=>e.unitPrice=a,min:0,precision:2,placeholder:"单价",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024),n(Re,{span:2},{default:s((()=>[n(t,{type:"danger",size:"small",icon:d(I),onClick:e=>(e=>{be.items.length>1&&be.items.splice(e,1)})(a),disabled:be.items.length<=1},{default:s((()=>l[37]||(l[37]=[g(" 删除 ")]))),_:2},1032,["icon","onClick","disabled"])])),_:2},1024)])),_:2},1024),n(Ye,{gutter:10,style:{"margin-bottom":"10px"}},{default:s((()=>[n(Re,{span:12},{default:s((()=>[n(o,{modelValue:e.specification,"onUpdate:modelValue":a=>e.specification=a,placeholder:"规格型号",maxlength:"200"},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024),n(Re,{span:12},{default:s((()=>[n(o,{modelValue:e.notes,"onUpdate:modelValue":a=>e.notes=a,placeholder:"备注",maxlength:"200"},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024)])),_:2},1024)])))),128)),n(t,{type:"primary",size:"small",icon:d(c),onClick:Pe,style:{"margin-top":"10px"}},{default:s((()=>l[38]||(l[38]=[g(" 添加物品 ")]))),_:1},8,["icon"])])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-d7b1e158"]]);export{J as default};
