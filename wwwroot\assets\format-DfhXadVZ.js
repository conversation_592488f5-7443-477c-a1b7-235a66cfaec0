function t(t,e="YYYY-MM-DD HH:mm:ss"){if(!t)return"";const r=new Date(t);if(isNaN(r.getTime()))return"";const a=r.getFullYear(),n=String(r.getMonth()+1).padStart(2,"0"),Y=String(r.getDate()).padStart(2,"0"),i=String(r.getHours()).padStart(2,"0"),g=String(r.getMinutes()).padStart(2,"0"),s=String(r.getSeconds()).padStart(2,"0");return e.replace("YYYY",a).replace("MM",n).replace("DD",Y).replace("HH",i).replace("mm",g).replace("ss",s)}function e(t,e="YYYY-MM-DD HH:mm"){if(!t)return"";const r=t instanceof Date?t:new Date(t),a=r.getFullYear(),n=String(r.getMonth()+1).padStart(2,"0"),Y=String(r.getDate()).padStart(2,"0"),i=String(r.getHours()).padStart(2,"0"),g=String(r.getMinutes()).padStart(2,"0"),s=String(r.getSeconds()).padStart(2,"0");return"YYYY-MM-DD HH:mm"===e?`${a}-${n}-${Y} ${i}:${g}`:"YYYY-MM-DD"===e?`${a}-${n}-${Y}`:"HH:mm"===e?`${i}:${g}`:`${a}-${n}-${Y} ${i}:${g}:${s}`}function r(e){if(!e)return"";const r=new Date(e);if(isNaN(r.getTime()))return"";const a=new Date,n=r.getTime()-a.getTime(),Y=Math.ceil(n/864e5);if(Y<0){const r=Math.abs(Y);return 0===r?"今天":1===r?"昨天":r<7?`${r}天前`:r<30?`${Math.floor(r/7)}周前`:t(e,"YYYY-MM-DD")}return 0===Y?"今天":1===Y?"明天":Y<7?`${Y}天后`:Y<30?`${Math.floor(Y/7)}周后`:t(e,"YYYY-MM-DD")}export{r as a,e as b,t as f};
