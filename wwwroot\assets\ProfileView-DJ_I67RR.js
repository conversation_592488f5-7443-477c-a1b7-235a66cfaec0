import{b3 as e,j as a,r as s,c as l,m as t,aq as i,aJ as n,b as c,d as r,Y as d,a9 as o,f as u,w as v,a8 as m,a as p,u as f,o as h,e as g,bp as y,b0 as b,t as _,bA as w,A as k,bB as I,bC as D,F as z,h as L,bD as P,bE as T,U as j,bc as A,_ as C}from"./index-CkwLz8y6.js";import{u as S}from"./gamification-DuHbQD0u.js";import{t as U}from"./task-Uzj9rZkj.js";const q={class:"profile-view"},x={class:"user-info"},B={class:"avatar-wrapper"},F={class:"avatar-uploader-icon"},G={class:"user-name"},J={class:"user-title"},M={class:"user-level"},N={class:"level-label"},R={class:"user-meta"},E={class:"meta-item"},O={class:"meta-value"},V={class:"meta-item"},W={class:"meta-value"},Y={class:"meta-item"},$={class:"meta-value"},H={class:"meta-item"},K={class:"meta-value"},Q={class:"meta-item"},X={class:"meta-value"},Z={class:"contact-info"},ee={class:"contact-item"},ae={class:"contact-item"},se={class:"card-header"},le={class:"tags-content"},te={key:0,class:"inventory-grid"},ie=["title"],ne={class:"stat-item"},ce={class:"stat-value"},re={class:"stat-item"},de={class:"stat-value"},oe={class:"stat-item"},ue={class:"stat-value"},ve={class:"stat-item"},me={class:"stat-value"},pe={class:"card-header"},fe={key:0,class:"achievements-grid"},he={class:"achievement-info"},ge={class:"achievement-name"},ye={class:"achievement-date"},be={class:"card-header"},_e=C(e({__name:"ProfileView",setup(e){const C=f(),_e=a(),we=S(),ke=s(!1),Ie=s(!1),De=s(!1),ze=s(!1),Le=s(!1),Pe=s(null),Te=s([]),je=s(!1),Ae=s({gold:0,diamonds:0}),Ce=s("-"),Se=l((()=>je.value?we.achievements:we.achievements.slice(0,6)));async function Ue(){Ie.value=!0;try{await new Promise((e=>setTimeout(e,400))),Pe.value={total:75,completed:58,pending:5,onTimeRate:95}}catch(e){m.error("加载任务统计失败"),Pe.value=null}finally{Ie.value=!1}}async function qe(){ze.value=!0;try{const e={pageSize:5,assignee:_e.userInfo.id,sortBy:"createDate",sortOrder:"desc"},a=await U.getTaskList(e);Te.value=(null==a?void 0:a.list)||[]}catch(e){m.error("加载近期任务失败"),Te.value=[]}finally{ze.value=!1}}async function xe(){try{await new Promise((e=>setTimeout(e,200))),Ae.value={gold:1250,diamonds:55}}catch(e){}}async function Be(){try{await new Promise((e=>setTimeout(e,250))),Ce.value="#3"}catch(e){}}const Fe=e=>{if(!e)return"-";try{return new Date(e).toLocaleDateString()}catch{return"-"}},Ge=e=>{if(!e)return"-";try{return new Date(e).toLocaleString()}catch{return"-"}},Je=e=>{if(!e)return"-";try{return new Date(e).toLocaleDateString("zh-CN",{month:"2-digit",day:"2-digit"})}catch{return"-"}},Me={unstarted:{label:"未开始",type:"info"},"in-progress":{label:"进行中",type:"primary"},completed:{label:"已完成",type:"success"},overdue:{label:"已逾期",type:"danger"},todo:{label:"待办",type:"info"}},Ne=e=>{var a;return(null==(a=Me[e])?void 0:a.label)||e},Re=async e=>{var a,s;Le.value=!0;try{const a=await A.uploadAvatar(e.file);if(a&&a.success&&a.data){const{avatarUrl:e,accessUrl:s}=a.data;s?_e.setAvatarWithFullUrl(s):e&&_e.setAvatar(e),m.success(a.message||"头像更新成功！")}else{const e=(null==a?void 0:a.message)||"头像上传失败，响应数据格式不正确";m.error(e)}}catch(l){let e="头像上传失败，请稍后再试";(null==(s=null==(a=null==l?void 0:l.response)?void 0:a.data)?void 0:s.message)?e=l.response.data.message:(null==l?void 0:l.message)&&(e=l.message),m.error(e)}finally{Le.value=!1}},Ee=e=>["image/jpeg","image/png","image/gif"].includes(e.type)?!(e.size/1024/1024>5)||(m.error("头像图片大小不能超过 5MB!"),!1):(m.error("头像图片必须是 JPG, PNG, 或 GIF 格式!"),!1);return t((()=>{ke.value=!0,0!==we.score||we.isLoading||we.initializeStore(),Promise.allSettled([Ue(),qe(),xe(),Be()]).finally((()=>{ke.value=!1}))})),(e,a)=>{const s=p("el-avatar"),l=p("el-icon"),t=p("el-divider"),m=p("el-card"),f=p("el-button"),A=p("el-empty"),S=p("el-col"),U=p("el-row"),Le=p("el-tooltip"),Ue=p("el-table-column"),qe=p("el-table"),xe=n("loading");return i((h(),c("div",q,[a[24]||(a[24]=r("div",{class:"page-header"},"个人中心",-1)),u(_e).userInfo&&!ke.value?(h(),d(U,{key:0,gutter:20},{default:v((()=>[g(S,{span:8},{default:v((()=>[g(m,{shadow:"hover",class:"user-card"},{default:v((()=>[r("div",x,[r("div",B,[g(u(y),{class:"avatar-uploader",action:"#","show-file-list":!1,"http-request":Re,"before-upload":Ee},{default:v((()=>[g(s,{size:100,src:u(_e).computedAvatarUrl||"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",class:"profile-avatar"},null,8,["src"]),r("div",F,[g(l,null,{default:v((()=>[g(u(b))])),_:1}),a[2]||(a[2]=r("span",null,"点击更换",-1))])])),_:1})]),r("h2",G,_(u(_e).userInfo.name||"用户名"),1),r("div",J,_(u(_e).userInfo.department||"部门")+" - "+_(u(_e).userInfo.position||"职位"),1),r("div",M,[r("span",N,"Level "+_(u(we).level||1)+" - "+_(u(we).levelTitle),1),g(u(w),{percentage:u(we).currentLevelProgress,"stroke-width":10,status:"success"},{default:v((()=>{var e;return[r("span",null,_(u(we).score)+" / "+_((null==(e=u(we).nextLevelInfo)?void 0:e.points)||"Max"),1)]})),_:1},8,["percentage"])]),r("div",R,[r("div",E,[a[3]||(a[3]=r("div",{class:"meta-label"},"加入时间",-1)),r("div",O,_(Fe(u(_e).userInfo.joinDate)||"-"),1)]),r("div",V,[a[4]||(a[4]=r("div",{class:"meta-label"},"总经验",-1)),r("div",W,_(u(we).score),1)]),r("div",Y,[a[6]||(a[6]=r("div",{class:"meta-label"},"金币",-1)),r("div",$,[a[5]||(a[5]=r("img",{src:"https://cdn-icons-png.flaticon.com/512/2933/2933116.png",class:"coin-icon-sm"},null,-1)),k(" "+_(Ae.value.gold||0),1)])]),r("div",H,[a[8]||(a[8]=r("div",{class:"meta-label"},"钻石",-1)),r("div",K,[a[7]||(a[7]=r("img",{src:"https://cdn-icons-png.flaticon.com/512/2933/2933151.png",class:"diamond-icon-sm"},null,-1)),k(" "+_(Ae.value.diamonds||0),1)])]),r("div",Q,[a[9]||(a[9]=r("div",{class:"meta-label"},"排名",-1)),r("div",X,_(Ce.value||"-"),1)])])]),g(t),r("div",Z,[a[10]||(a[10]=r("h3",null,"联系方式",-1)),r("div",ee,[g(l,null,{default:v((()=>[g(u(I))])),_:1}),r("span",null,_(u(_e).userInfo.email||"未设置"),1)]),r("div",ae,[g(l,null,{default:v((()=>[g(u(D))])),_:1}),r("span",null,_(u(_e).userInfo.phone||"未设置"),1)])])])),_:1}),g(m,{shadow:"hover",class:"tags-card"},{header:v((()=>[r("div",se,[a[12]||(a[12]=r("span",null,"我的标签",-1)),g(f,{link:"",type:"primary"},{default:v((()=>a[11]||(a[11]=[k("编辑")]))),_:1})])])),default:v((()=>[r("div",le,[u(_e).userInfo.tags&&u(_e).userInfo.tags.length>0?(h(!0),c(z,{key:0},L(u(_e).userInfo.tags,(e=>(h(),d(u(P),{key:e,class:"user-tag",type:"info"},{default:v((()=>[k(_(e),1)])),_:2},1024)))),128)):(h(),d(A,{key:1,description:"暂无标签","image-size":50}))])])),_:1}),g(m,{shadow:"hover",class:"inventory-card"},{header:v((()=>a[13]||(a[13]=[r("div",{class:"card-header"},[r("span",null,"我的背包")],-1)]))),default:v((()=>[u(we).inventory.length>0?(h(),c("div",te,[(h(!0),c(z,null,L(u(we).inventory,(e=>(h(),c("div",{key:e.id,class:"inventory-item"},[g(u(T),{value:e.quantity>1?e.quantity:null,type:"primary"},{default:v((()=>[g(s,{size:50,src:e.icon||"path/to/default/item/icon.png",class:"item-icon"},null,8,["src"])])),_:2},1032,["value"]),r("div",{class:"item-name",title:e.description},_(e.name),9,ie)])))),128))])):(h(),d(A,{key:1,description:"背包空空如也","image-size":50}))])),_:1})])),_:1}),g(S,{span:16},{default:v((()=>[i((h(),d(m,{shadow:"hover",class:"stats-card"},{header:v((()=>a[14]||(a[14]=[r("div",{class:"card-header"},[r("span",null,"任务统计")],-1)]))),default:v((()=>[Pe.value?(h(),d(U,{key:0,gutter:20,class:"stat-row"},{default:v((()=>[g(S,{span:6},{default:v((()=>[r("div",ne,[r("div",ce,_(Pe.value.total||0),1),a[15]||(a[15]=r("div",{class:"stat-label"},"总任务数",-1))])])),_:1}),g(S,{span:6},{default:v((()=>[r("div",re,[r("div",de,_(Pe.value.completed||0),1),a[16]||(a[16]=r("div",{class:"stat-label"},"已完成",-1))])])),_:1}),g(S,{span:6},{default:v((()=>[r("div",oe,[r("div",ue,_(Pe.value.onTimeRate||0)+"%",1),a[17]||(a[17]=r("div",{class:"stat-label"},"按时完成率",-1))])])),_:1}),g(S,{span:6},{default:v((()=>[r("div",ve,[r("div",me,_(Pe.value.pending||0),1),a[18]||(a[18]=r("div",{class:"stat-label"},"待处理",-1))])])),_:1})])),_:1})):o("",!0),a[19]||(a[19]=r("div",{class:"chart-area"},[r("div",{class:"chart-placeholder"},"任务完成趋势图表 (待实现)")],-1))])),_:1})),[[xe,Ie.value]]),i((h(),d(m,{shadow:"hover",class:"achievements-card"},{header:v((()=>[r("div",pe,[a[20]||(a[20]=r("span",null,"我的成就",-1)),g(f,{link:"",type:"primary",onClick:a[0]||(a[0]=e=>je.value=!je.value)},{default:v((()=>[k(_(je.value?"收起":`查看全部 (${u(we).achievements.length})`),1)])),_:1})])])),default:v((()=>[u(we).achievements.length>0?(h(),c("div",fe,[(h(!0),c(z,null,L(Se.value,(e=>(h(),c("div",{key:e.id,class:"achievement-item"},[g(Le,{content:e.description,placement:"top"},{default:v((()=>[g(s,{size:40,icon:e.icon||u(j),class:"achievement-icon"},null,8,["icon"])])),_:2},1032,["content"]),r("div",he,[r("div",ge,_(e.name),1),r("div",ye,_(Ge(e.achievedDate)),1)])])))),128))])):o("",!0)])),_:1})),[[xe,De.value]]),i((h(),d(m,{shadow:"hover",class:"recent-tasks-card"},{header:v((()=>[r("div",be,[a[22]||(a[22]=r("span",null,"近期任务",-1)),g(f,{link:"",type:"primary",onClick:a[1]||(a[1]=e=>u(C).push("/main/tasks/list"))},{default:v((()=>a[21]||(a[21]=[k("查看全部")]))),_:1})])])),default:v((()=>[Te.value.length>0?(h(),d(qe,{key:0,data:Te.value,style:{width:"100%"}},{default:v((()=>[g(Ue,{prop:"title",label:"任务名称","min-width":"180","show-overflow-tooltip":""}),g(Ue,{prop:"endDate",label:"截止时间",width:"120"},{default:v((({row:e})=>[k(_(Je(e.endDate)),1)])),_:1}),g(Ue,{label:"状态",width:"100"},{default:v((({row:e})=>{return[g(u(P),{type:(a=e.status,(null==(s=Me[a])?void 0:s.type)||"info")},{default:v((()=>[k(_(Ne(e.status)),1)])),_:2},1032,["type"])];var a,s})),_:1}),g(Ue,{label:"操作",width:"100",align:"center"},{default:v((({row:e})=>[g(f,{type:"primary",link:"",onClick:a=>{return s=e.id,void C.push({name:"TaskDetail",params:{id:s}});var s}},{default:v((()=>a[23]||(a[23]=[k("查看")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])):o("",!0)])),_:1})),[[xe,ze.value]])])),_:1})])),_:1})):ke.value?o("",!0):(h(),d(A,{key:1,description:"无法加载用户信息"}))])),[[xe,ke.value]])}}}),[["__scopeId","data-v-f9bf8121"]]);export{_e as default};
