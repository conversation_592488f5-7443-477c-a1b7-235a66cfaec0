// File: Application/Features/Tasks/Services/TaskWatermarkService.cs
// Description: 任务完成水印服务

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Application.Common.Dtos;

namespace ItAssetsSystem.Application.Features.Tasks.Services
{
    /// <summary>
    /// 任务完成水印服务
    /// </summary>
    public class TaskWatermarkService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<TaskWatermarkService> _logger;

        // 预定义的水印颜色
        private readonly List<string> _watermarkColors = new List<string>
        {
            "#FF6B6B", // 红色
            "#4ECDC4", // 青色
            "#45B7D1", // 蓝色
            "#96CEB4", // 绿色
            "#FFEAA7", // 黄色
            "#DDA0DD", // 紫色
            "#98D8C8", // 薄荷绿
            "#F7DC6F", // 金黄色
            "#BB8FCE", // 淡紫色
            "#85C1E9", // 天蓝色
            "#F8C471", // 橙色
            "#82E0AA"  // 浅绿色
        };

        public TaskWatermarkService(AppDbContext context, ILogger<TaskWatermarkService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 为任务设置完成水印
        /// </summary>
        public async Task<ApiResponse<bool>> SetTaskCompletionWatermarkAsync(long taskId, int completedByUserId)
        {
            try
            {
                var task = await _context.Tasks.FirstOrDefaultAsync(t => t.TaskId == taskId);
                if (task == null)
                {
                    return ApiResponse<bool>.CreateFail("任务不存在");
                }

                // 移除状态检查，因为在状态更新的同一事务中调用，状态可能还未提交
                // 改为检查任务是否已经有水印信息，避免重复设置
                if (task.CompletedByUserId.HasValue && task.CompletedByUserId.Value == completedByUserId)
                {
                    _logger.LogInformation("任务 {TaskId} 已经设置了用户 {UserId} 的完成水印，跳过重复设置", taskId, completedByUserId);
                    return ApiResponse<bool>.CreateSuccess(true, "任务完成水印已存在");
                }

                // 获取用户信息
                var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == completedByUserId);
                if (user == null)
                {
                    return ApiResponse<bool>.CreateFail("完成用户不存在");
                }

                // 为用户分配一个固定的颜色（基于用户ID）
                var watermarkColor = GetUserWatermarkColor(completedByUserId);

                // 设置水印信息
                task.CompletedByUserId = completedByUserId;
                task.CompletionWatermarkColor = watermarkColor;
                task.LastUpdatedTimestamp = DateTime.Now;

                await _context.SaveChangesAsync();

                _logger.LogInformation("为任务 {TaskId} 设置完成水印，完成用户: {UserId}, 颜色: {Color}",
                    taskId, completedByUserId, watermarkColor);

                return ApiResponse<bool>.CreateSuccess(true, "设置任务完成水印成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置任务完成水印时发生错误");
                return ApiResponse<bool>.CreateFail("设置任务完成水印失败: " + ex.Message);
            }
        }

        /// <summary>
        /// 获取用户的水印颜色（基于用户ID的固定分配）
        /// </summary>
        public string GetUserWatermarkColor(int userId)
        {
            // 使用用户ID对颜色数量取模，确保每个用户有固定的颜色
            var colorIndex = userId % _watermarkColors.Count;
            return _watermarkColors[colorIndex];
        }

        /// <summary>
        /// 获取任务的完成水印信息
        /// </summary>
        public async Task<ApiResponse<TaskWatermarkDto?>> GetTaskWatermarkAsync(long taskId)
        {
            try
            {
                var task = await _context.Tasks
                    .Include(t => t.Creator) // 假设有Creator导航属性
                    .FirstOrDefaultAsync(t => t.TaskId == taskId);

                if (task == null)
                {
                    return ApiResponse<TaskWatermarkDto?>.CreateFail("任务不存在");
                }

                if (task.CompletedByUserId == null || string.IsNullOrEmpty(task.CompletionWatermarkColor))
                {
                    return ApiResponse<TaskWatermarkDto?>.CreateSuccess(null, "任务无完成水印");
                }

                // 获取完成用户信息
                var completedByUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.Id == task.CompletedByUserId.Value);

                if (completedByUser == null)
                {
                    return ApiResponse<TaskWatermarkDto?>.CreateSuccess(null, "完成用户不存在");
                }

                var watermarkDto = new TaskWatermarkDto
                {
                    TaskId = taskId,
                    CompletedByUserId = task.CompletedByUserId.Value,
                    CompletedByUserName = completedByUser.Name,
                    WatermarkColor = task.CompletionWatermarkColor,
                    CompletedAt = task.ActualEndDate
                };

                return ApiResponse<TaskWatermarkDto?>.CreateSuccess(watermarkDto, "获取任务水印信息成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务水印信息时发生错误");
                return ApiResponse<TaskWatermarkDto?>.CreateFail("获取任务水印信息失败: " + ex.Message);
            }
        }

        /// <summary>
        /// 批量获取任务的完成水印信息
        /// </summary>
        public async Task<ApiResponse<List<TaskWatermarkDto>>> GetTasksWatermarkAsync(List<long> taskIds)
        {
            try
            {
                var tasks = await _context.Tasks
                    .Where(t => taskIds.Contains(t.TaskId) 
                             && t.CompletedByUserId != null 
                             && !string.IsNullOrEmpty(t.CompletionWatermarkColor))
                    .ToListAsync();

                var userIds = tasks.Select(t => t.CompletedByUserId!.Value).Distinct().ToList();
                var users = await _context.Users
                    .Where(u => userIds.Contains(u.Id))
                    .ToDictionaryAsync(u => u.Id, u => u.Name);

                var watermarks = tasks.Select(t => new TaskWatermarkDto
                {
                    TaskId = t.TaskId,
                    CompletedByUserId = t.CompletedByUserId!.Value,
                    CompletedByUserName = users.GetValueOrDefault(t.CompletedByUserId!.Value, "未知用户"),
                    WatermarkColor = t.CompletionWatermarkColor!,
                    CompletedAt = t.ActualEndDate
                }).ToList();

                return ApiResponse<List<TaskWatermarkDto>>.CreateSuccess(watermarks, "批量获取任务水印信息成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量获取任务水印信息时发生错误");
                return ApiResponse<List<TaskWatermarkDto>>.CreateFail("批量获取任务水印信息失败: " + ex.Message);
            }
        }

        /// <summary>
        /// 清除任务完成水印
        /// </summary>
        public async Task<ApiResponse<bool>> ClearTaskWatermarkAsync(long taskId)
        {
            try
            {
                var task = await _context.Tasks.FirstOrDefaultAsync(t => t.TaskId == taskId);
                if (task == null)
                {
                    return ApiResponse<bool>.CreateFail("任务不存在");
                }

                task.CompletedByUserId = null;
                task.CompletionWatermarkColor = null;
                task.LastUpdatedTimestamp = DateTime.Now;

                await _context.SaveChangesAsync();

                return ApiResponse<bool>.CreateSuccess(true, "清除任务完成水印成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清除任务完成水印时发生错误");
                return ApiResponse<bool>.CreateFail("清除任务完成水印失败: " + ex.Message);
            }
        }

        /// <summary>
        /// 获取用户完成任务统计（带水印颜色）
        /// </summary>
        public async Task<ApiResponse<List<UserCompletionStatisticsDto>>> GetUserCompletionStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                var query = _context.Tasks.AsQueryable();

                if (startDate.HasValue)
                {
                    query = query.Where(t => t.ActualEndDate >= startDate.Value);
                }

                if (endDate.HasValue)
                {
                    query = query.Where(t => t.ActualEndDate <= endDate.Value);
                }

                var completedTasks = await query
                    .Where(t => t.Status == "Done" && t.CompletedByUserId != null)
                    .GroupBy(t => t.CompletedByUserId!.Value)
                    .Select(g => new
                    {
                        UserId = g.Key,
                        CompletedCount = g.Count(),
                        LastCompletedAt = g.Max(t => t.ActualEndDate)
                    })
                    .ToListAsync();

                var userIds = completedTasks.Select(ct => ct.UserId).ToList();
                var users = await _context.Users
                    .Where(u => userIds.Contains(u.Id))
                    .ToDictionaryAsync(u => u.Id, u => u.Name);

                var statistics = completedTasks.Select(ct => new UserCompletionStatisticsDto
                {
                    UserId = ct.UserId,
                    UserName = users.GetValueOrDefault(ct.UserId, "未知用户"),
                    CompletedTasksCount = ct.CompletedCount,
                    WatermarkColor = GetUserWatermarkColor(ct.UserId),
                    LastCompletedAt = ct.LastCompletedAt
                }).OrderByDescending(s => s.CompletedTasksCount).ToList();

                return ApiResponse<List<UserCompletionStatisticsDto>>.CreateSuccess(statistics, "获取用户完成任务统计成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户完成任务统计时发生错误");
                return ApiResponse<List<UserCompletionStatisticsDto>>.CreateFail("获取用户完成任务统计失败: " + ex.Message);
            }
        }
    }

    /// <summary>
    /// 任务水印DTO
    /// </summary>
    public class TaskWatermarkDto
    {
        public long TaskId { get; set; }
        public int CompletedByUserId { get; set; }
        public string CompletedByUserName { get; set; } = string.Empty;
        public string WatermarkColor { get; set; } = string.Empty;
        public DateTime? CompletedAt { get; set; }
    }

    /// <summary>
    /// 用户完成任务统计DTO
    /// </summary>
    public class UserCompletionStatisticsDto
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public int CompletedTasksCount { get; set; }
        public string WatermarkColor { get; set; } = string.Empty;
        public DateTime? LastCompletedAt { get; set; }
    }
}
