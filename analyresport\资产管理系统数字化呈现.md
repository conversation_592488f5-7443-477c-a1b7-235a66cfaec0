# IT资产管理系统 - 数字化呈现设计方案

## 项目概述

本文档基于现有IT资产管理系统的代码分析，提出了一套完整的资产数字化呈现解决方案。旨在实现资产数据的多维度统计、可视化展示和大屏监控，提升资产管理的数字化水平。

## 一、现状分析

### 1.1 前端资产列表页面现状

**当前接口使用情况：**
- **主接口**：`GET /Asset` - 分页查询资产列表
- **支持接口**：
  - `GET /AssetType` - 获取资产类型列表
  - `GET /Location/dropdown` - 获取位置下拉列表
  - `GET /Asset/{id}` - 获取资产详情
  - `GET /Asset/{id}/history` - 获取资产历史

**显示字段分析：**
```javascript
// 资产列表显示的核心字段
{
  assetCode: "资产编号",         // IT-类型-年月日-序号
  financialCode: "财务编号",     // 财务管理编号
  name: "资产名称",             // 设备名称
  assetTypeName: "资产类型",     // 电脑、扫码器、打印机等
  model: "规格型号",            // 设备型号
  brand: "品牌",               // 设备品牌
  locationName: "位置",         // 完整位置路径
  status: "状态"               // 0闲置、1在用、2维修、3报废
}
```

### 1.2 位置管理层级结构分析

**5层位置架构：**
```
层级0: 工厂 (Factory)          - type: 0
├── 层级1: 产线 (Production Line)   - type: 1
    ├── 层级2: 工序 (Process)        - type: 2
        ├── 层级3: 工位 (Workstation)    - type: 3
            ├── 层级4: 设备位置 (Equipment) - type: 4 ⭐层级5目标层
```

**层级5位置获取方式：**
- 接口：`GET /Location/tree` - 获取完整树形结构
- 筛选条件：`location.type === 4` （设备位置级别）
- 关联信息：位置关联了使用部门和使用人员

### 1.3 数据库结构支撑

**核心表关系：**
```sql
-- 资产表 (assets) - 核心数据
- Id, assetCode, name, AssetTypeId, LocationId, DepartmentId, Status

-- 位置表 (locations) - 5层结构
- Id, Name, Code, Type(0-4), ParentId

-- 资产类型表 (assettypes) - 分类管理
- Id, Name, Code, Description

-- 部门表 (departments) - 组织架构
- Id, Name, Code, ParentId

-- 位置用户关联表 (locationusers) - 位置与人员关联
- LocationId, UserId, UserType
```

## 二、数字化呈现需求

### 2.1 统计维度需求

#### A. 按位置统计 (层级5维度)
```javascript
// 需求场景
"某个位置有多少个电脑"
"某个位置有多少个扫码器" 
"各个位置的资产分布情况"

// 数据结构
{
  locationId: "位置ID",
  locationName: "产线A-工序1-工位2-设备位置3",
  locationCode: "PA_P1_W2_E3",
  fullPath: "工厂1/产线A/工序1/工位2/设备位置3",
  assetCounts: {
    "电脑": 5,
    "扫码器": 2,
    "打印机": 1
  },
  totalAssets: 8,
  department: "生产部",
  users: ["张三", "李四"]
}
```

#### B. 按部门统计
```javascript
// 需求场景  
"某个部门有多少个电脑"
"某个部门有多少个扫码器"
"各个部门的资产分布情况"

// 数据结构
{
  departmentId: "部门ID",
  departmentName: "生产部",
  assetCounts: {
    "电脑": 20,
    "扫码器": 10, 
    "打印机": 5
  },
  totalAssets: 35,
  locations: ["位置1", "位置2", "位置3"],
  personnel: 15
}
```

#### C. 按资产类型统计 (全局维度)
```javascript
// 需求场景
"总共有多少个电脑"
"总共有多少个扫码器"  
"各类型资产的总数量"

// 数据结构
{
  assetTypeTotals: {
    "电脑": 50,
    "扫码器": 25,
    "打印机": 15,
    "路由器": 8,
    "服务器": 3
  },
  totalAssets: 101
}
```

#### D. 交叉维度统计
```javascript
// 按位置和资产类型交叉统计
{
  locationAssetMatrix: {
    "位置1": { "电脑": 5, "扫码器": 2 },
    "位置2": { "电脑": 3, "打印机": 1 }
  }
}

// 按部门和资产类型交叉统计  
{
  departmentAssetMatrix: {
    "生产部": { "电脑": 20, "扫码器": 10 },
    "质检部": { "电脑": 8, "扫码器": 5 }
  }
}
```

### 2.2 位置分布图可视化需求

#### A. 大屏展示要求
- **目标**：根据位置分布图点亮对应位置区域
- **应用场景**：生产管理大屏、资产监控中心
- **交互需求**：点击位置查看详细资产信息

#### B. 可视化特性
```javascript
// 位置标记特性
{
  位置点大小: "根据资产数量动态调整半径",
  位置点颜色: "根据主要资产类型确定颜色",
  位置点状态: "正常/告警/离线等状态显示",
  悬浮提示: "显示位置名称和资产统计",
  点击详情: "弹窗显示详细资产列表"
}

// 图例说明
{
  颜色图例: {
    "电脑": "#409EFF",    // 蓝色
    "扫码器": "#67C23A",  // 绿色  
    "打印机": "#E6A23C",  // 橙色
    "路由器": "#F56C6C",  // 红色
    "服务器": "#909399"   // 灰色
  },
  大小图例: {
    "小圆点": "1-5个资产",
    "中圆点": "6-15个资产", 
    "大圆点": "16+个资产"
  }
}
```

## 三、技术实现方案

### 3.1 后端API扩展

#### A. 资产统计API
```csharp
// 新增统计控制器
[ApiController]
[Route("api/[controller]")]
public class AssetStatisticsController : ControllerBase
{
    /// <summary>
    /// 获取资产多维度统计数据
    /// </summary>
    [HttpGet]
    public async Task<ApiResponse<AssetStatisticsDto>> GetAssetStatistics(
        [FromQuery] AssetStatisticsQuery query)
    {
        var result = new AssetStatisticsDto
        {
            // 按位置统计 (仅层级5)
            ByLocation = await GetAssetsByLocation(level: 4),
            
            // 按部门统计
            ByDepartment = await GetAssetsByDepartment(),
            
            // 按资产类型统计
            ByAssetType = await GetAssetsByType(),
            
            // 交叉统计矩阵
            LocationAssetMatrix = await GetLocationAssetMatrix(),
            DepartmentAssetMatrix = await GetDepartmentAssetMatrix(),
            
            // 汇总信息
            Summary = new StatisticsSummaryDto
            {
                TotalAssets = await GetTotalAssets(),
                TotalLevel5Locations = await GetLevel5LocationCount(),
                TotalDepartments = await GetDepartmentCount(),
                LastUpdated = DateTime.Now
            }
        };
        
        return ApiResponse<AssetStatisticsDto>.Success(result);
    }
}
```

#### B. 数据传输对象定义
```csharp
// 统计数据DTO
public class AssetStatisticsDto
{
    public Dictionary<int, LocationStatisticsDto> ByLocation { get; set; }
    public Dictionary<int, DepartmentStatisticsDto> ByDepartment { get; set; }
    public Dictionary<string, int> ByAssetType { get; set; }
    public Dictionary<string, Dictionary<string, int>> LocationAssetMatrix { get; set; }
    public Dictionary<string, Dictionary<string, int>> DepartmentAssetMatrix { get; set; }
    public StatisticsSummaryDto Summary { get; set; }
}

// 位置统计DTO
public class LocationStatisticsDto
{
    public int LocationId { get; set; }
    public string LocationName { get; set; }
    public string LocationCode { get; set; }
    public string FullPath { get; set; }
    public Dictionary<string, int> AssetCounts { get; set; }
    public int TotalAssets { get; set; }
    public string DepartmentName { get; set; }
    public List<string> Users { get; set; }
    // 位置坐标信息（用于地图标记）
    public double? CoordinateX { get; set; }
    public double? CoordinateY { get; set; }
}

// 部门统计DTO
public class DepartmentStatisticsDto
{
    public int DepartmentId { get; set; }
    public string DepartmentName { get; set; }
    public Dictionary<string, int> AssetCounts { get; set; }
    public int TotalAssets { get; set; }
    public List<string> Locations { get; set; }
    public int PersonnelCount { get; set; }
}
```

#### C. 层级5位置专用API
```csharp
/// <summary>
/// 获取所有层级5位置（设备位置）
/// </summary>
[HttpGet("level5")]
public async Task<ApiResponse<List<Level5LocationDto>>> GetLevel5Locations()
{
    var level5Locations = await _locationService.GetLocationsByType(4);
    
    var result = level5Locations.Select(loc => new Level5LocationDto
    {
        Id = loc.Id,
        Name = loc.Name,
        Code = loc.Code,
        FullPath = loc.GetFullPath(),
        AssetCount = loc.Assets?.Count ?? 0,
        DepartmentName = loc.Department?.Name,
        CoordinateX = loc.CoordinateX,
        CoordinateY = loc.CoordinateY,
        // 资产类型分布
        AssetTypeDistribution = loc.Assets?
            .GroupBy(a => a.AssetType.Name)
            .ToDictionary(g => g.Key, g => g.Count())
    }).ToList();
    
    return ApiResponse<List<Level5LocationDto>>.Success(result);
}
```

### 3.2 前端实现方案

#### A. 资产统计页面组件
```vue
<!-- /src/views/asset/AssetStatisticsView.vue -->
<template>
  <div class="asset-statistics-container">
    <!-- 统计概览仪表板 -->
    <div class="statistics-dashboard">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ summary.totalAssets }}</div>
              <div class="metric-label">总资产数</div>
              <div class="metric-trend">
                <el-icon><TrendCharts /></el-icon>
                <span class="trend-text">较上月 +5%</span>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ summary.totalLevel5Locations }}</div>
              <div class="metric-label">设备位置</div>
              <div class="metric-trend">
                <el-icon><Location /></el-icon>
                <span class="trend-text">层级5位置</span>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ summary.totalDepartments }}</div>
              <div class="metric-label">使用部门</div>
              <div class="metric-trend">
                <el-icon><OfficeBuilding /></el-icon>
                <span class="trend-text">活跃部门</span>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ assetTypeCount }}</div>
              <div class="metric-label">资产类型</div>
              <div class="metric-trend">
                <el-icon><Grid /></el-icon>
                <span class="trend-text">分类管理</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 多维度统计切换 -->
    <div class="statistics-content">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>资产分布统计</span>
            <div class="header-actions">
              <el-button-group>
                <el-button 
                  :type="activeView === 'location' ? 'primary' : 'default'"
                  @click="activeView = 'location'"
                >
                  <el-icon><MapLocation /></el-icon>
                  按位置统计
                </el-button>
                <el-button 
                  :type="activeView === 'department' ? 'primary' : 'default'"
                  @click="activeView = 'department'"
                >
                  <el-icon><OfficeBuilding /></el-icon>
                  按部门统计
                </el-button>
                <el-button 
                  :type="activeView === 'assetType' ? 'primary' : 'default'"
                  @click="activeView = 'assetType'"
                >
                  <el-icon><Grid /></el-icon>
                  按类型统计
                </el-button>
                <el-button 
                  :type="activeView === 'visualization' ? 'primary' : 'default'"
                  @click="activeView = 'visualization'"
                >
                  <el-icon><Monitor /></el-icon>
                  可视化地图
                </el-button>
              </el-button-group>
            </div>
          </div>
        </template>

        <!-- 按位置统计表格 -->
        <div v-show="activeView === 'location'" class="statistics-table">
          <location-statistics-table 
            :data="statisticsData.byLocation"
            :loading="loading"
            @row-click="handleLocationClick"
          />
        </div>

        <!-- 按部门统计表格 -->
        <div v-show="activeView === 'department'" class="statistics-table">
          <department-statistics-table 
            :data="statisticsData.byDepartment"
            :loading="loading"
          />
        </div>

        <!-- 按资产类型统计图表 -->
        <div v-show="activeView === 'assetType'" class="statistics-chart">
          <asset-type-statistics-chart 
            :data="statisticsData.byAssetType"
            :loading="loading"
          />
        </div>

        <!-- 位置分布可视化地图 -->
        <div v-show="activeView === 'visualization'" class="visualization-map">
          <location-distribution-map
            :locations="level5Locations"
            :statistics="statisticsData.byLocation"
            :fullscreen="false"
            @location-click="handleLocationClick"
          />
        </div>
      </el-card>
    </div>

    <!-- 位置详情弹窗 -->
    <el-dialog 
      v-model="locationDetailVisible" 
      :title="selectedLocation?.locationName"
      width="800px"
    >
      <location-asset-detail
        v-if="selectedLocation"
        :location="selectedLocation"
        :assets="selectedLocationAssets"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import assetStatisticsApi from '@/api/assetStatistics'

// 响应式数据
const loading = ref(false)
const activeView = ref('location')
const statisticsData = reactive({
  byLocation: {},
  byDepartment: {}, 
  byAssetType: {},
  locationAssetMatrix: {},
  departmentAssetMatrix: {}
})
const summary = reactive({
  totalAssets: 0,
  totalLevel5Locations: 0,
  totalDepartments: 0,
  lastUpdated: null
})
const level5Locations = ref([])

// 位置详情相关
const locationDetailVisible = ref(false)
const selectedLocation = ref(null)
const selectedLocationAssets = ref([])

// 计算属性
const assetTypeCount = computed(() => {
  return Object.keys(statisticsData.byAssetType).length
})

// 获取统计数据
const fetchStatisticsData = async () => {
  loading.value = true
  try {
    const response = await assetStatisticsApi.getAssetStatistics()
    if (response.success) {
      Object.assign(statisticsData, response.data)
      Object.assign(summary, response.data.summary)
      ElMessage.success('统计数据加载成功')
    } else {
      ElMessage.error(response.message || '获取统计数据失败')
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
  } finally {
    loading.value = false
  }
}

// 获取层级5位置数据
const fetchLevel5Locations = async () => {
  try {
    const response = await assetStatisticsApi.getLevel5Locations()
    if (response.success) {
      level5Locations.value = response.data
    }
  } catch (error) {
    console.error('获取层级5位置失败:', error)
  }
}

// 处理位置点击事件
const handleLocationClick = async (location) => {
  selectedLocation.value = location
  // 获取该位置的详细资产列表
  try {
    const response = await assetStatisticsApi.getLocationAssets(location.locationId)
    if (response.success) {
      selectedLocationAssets.value = response.data
      locationDetailVisible.value = true
    }
  } catch (error) {
    console.error('获取位置资产详情失败:', error)
    ElMessage.error('获取位置资产详情失败')
  }
}

// 组件挂载时加载数据
onMounted(() => {
  fetchStatisticsData()
  fetchLevel5Locations()
})
</script>
```

#### B. 位置统计表格组件
```vue
<!-- /src/components/statistics/LocationStatisticsTable.vue -->
<template>
  <div class="location-statistics-table">
    <el-table 
      :data="tableData" 
      :loading="loading"
      row-key="locationId"
      @row-click="handleRowClick"
      class="statistics-table"
    >
      <el-table-column prop="locationName" label="位置名称" min-width="200">
        <template #default="{ row }">
          <div class="location-info">
            <div class="location-name">{{ row.locationName }}</div>
            <div class="location-code">{{ row.locationCode }}</div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="fullPath" label="完整路径" min-width="300" show-overflow-tooltip />
      
      <el-table-column prop="departmentName" label="使用部门" width="120" />
      
      <!-- 动态显示各类型资产列 -->
      <el-table-column 
        v-for="assetType in assetTypes" 
        :key="assetType"
        :label="assetType"
        width="80"
        align="center"
      >
        <template #default="{ row }">
          <el-tag 
            v-if="row.assetCounts[assetType] > 0"
            :type="getAssetTypeTagType(assetType)"
            size="small"
          >
            {{ row.assetCounts[assetType] }}
          </el-tag>
          <span v-else class="zero-count">0</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="totalAssets" label="总计" width="80" align="center">
        <template #default="{ row }">
          <el-tag type="primary" size="small">
            {{ row.totalAssets }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="使用人员" min-width="200">
        <template #default="{ row }">
          <div v-if="row.users && row.users.length > 0" class="users-list">
            <el-tag 
              v-for="user in row.users.slice(0, 3)" 
              :key="user"
              size="small"
              class="user-tag"
            >
              {{ user }}
            </el-tag>
            <span v-if="row.users.length > 3" class="more-users">
              +{{ row.users.length - 3 }}
            </span>
          </div>
          <span v-else class="no-users">暂无人员</span>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="120" fixed="right">
        <template #default="{ row }">
          <el-button 
            type="text" 
            size="small" 
            @click.stop="viewLocationDetail(row)"
          >
            <el-icon><View /></el-icon>
            查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['row-click', 'view-detail'])

// 转换为表格数据
const tableData = computed(() => {
  return Object.values(props.data)
})

// 获取所有资产类型
const assetTypes = computed(() => {
  const types = new Set()
  Object.values(props.data).forEach(location => {
    Object.keys(location.assetCounts || {}).forEach(type => {
      types.add(type)
    })
  })
  return Array.from(types).sort()
})

// 资产类型标签颜色
const getAssetTypeTagType = (assetType) => {
  const typeColorMap = {
    '电脑': 'primary',
    '扫码器': 'success',
    '打印机': 'warning',
    '路由器': 'danger',
    '服务器': 'info'
  }
  return typeColorMap[assetType] || 'default'
}

// 处理行点击
const handleRowClick = (row) => {
  emit('row-click', row)
}

// 查看位置详情
const viewLocationDetail = (row) => {
  emit('view-detail', row)
}
</script>

<style lang="scss" scoped>
.location-statistics-table {
  .location-info {
    .location-name {
      font-weight: 500;
      color: #303133;
    }
    .location-code {
      font-size: 12px;
      color: #909399;
      margin-top: 2px;
    }
  }
  
  .zero-count {
    color: #C0C4CC;
    font-size: 12px;
  }
  
  .users-list {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    
    .user-tag {
      margin: 0;
    }
    
    .more-users {
      font-size: 12px;
      color: #909399;
    }
  }
  
  .no-users {
    color: #C0C4CC;
    font-size: 12px;
  }
}

.statistics-table {
  :deep(.el-table__row) {
    cursor: pointer;
    
    &:hover {
      background-color: #f5f7fa;
    }
  }
}
</style>
```

#### C. 位置分布图可视化组件
```vue
<!-- /src/components/visualization/LocationDistributionMap.vue -->
<template>
  <div class="location-distribution-map" :class="{ fullscreen: fullscreen }">
    <!-- 工具栏 -->
    <div class="map-toolbar">
      <div class="toolbar-left">
        <el-button-group size="small">
          <el-button @click="zoomIn"><el-icon><ZoomIn /></el-icon></el-button>
          <el-button @click="zoomOut"><el-icon><ZoomOut /></el-icon></el-button>
          <el-button @click="resetZoom"><el-icon><Refresh /></el-icon></el-button>
        </el-button-group>
      </div>
      
      <div class="toolbar-right">
        <el-button 
          size="small" 
          type="primary" 
          @click="enterFullscreen"
          v-if="!fullscreen"
        >
          <el-icon><FullScreen /></el-icon>
          全屏显示
        </el-button>
        <el-button 
          size="small" 
          @click="exitFullscreen"
          v-else
        >
          <el-icon><Aim /></el-icon>
          退出全屏
        </el-button>
      </div>
    </div>

    <!-- SVG地图容器 -->
    <div class="map-container" ref="mapContainer">
      <svg 
        :width="mapWidth" 
        :height="mapHeight" 
        :viewBox="`0 0 ${mapWidth} ${mapHeight}`"
        class="location-svg-map"
      >
        <!-- 背景网格 -->
        <defs>
          <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
            <path d="M 50 0 L 0 0 0 50" fill="none" stroke="#f0f0f0" stroke-width="1"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#grid)" />
        
        <!-- 工厂布局背景图 -->
        <image 
          v-if="backgroundImage" 
          :href="backgroundImage" 
          :width="mapWidth" 
          :height="mapHeight"
          opacity="0.3"
        />
        
        <!-- 位置区域划分 -->
        <g class="location-areas">
          <rect 
            v-for="area in layoutAreas"
            :key="area.id"
            :x="area.x" 
            :y="area.y"
            :width="area.width" 
            :height="area.height"
            :fill="area.color"
            :stroke="area.strokeColor"
            stroke-width="2"
            fill-opacity="0.1"
            class="area-rect"
          />
        </g>
        
        <!-- 位置点标记 -->
        <g class="location-points">
          <g 
            v-for="location in visualLocations" 
            :key="location.locationId"
            class="location-group"
            @click="selectLocation(location)"
            @mouseenter="showTooltip(location, $event)"
            @mouseleave="hideTooltip"
          >
            <!-- 位置圆点 -->
            <circle
              :cx="location.x"
              :cy="location.y"
              :r="getLocationRadius(location)"
              :fill="getLocationColor(location)"
              :stroke="getLocationStroke(location)"
              :stroke-width="getStrokeWidth(location)"
              class="location-point"
              :class="{ 
                'location-active': selectedLocationId === location.locationId,
                'location-hover': hoveredLocationId === location.locationId
              }"
            />
            
            <!-- 位置标签 -->
            <text
              :x="location.x"
              :y="location.y - getLocationRadius(location) - 8"
              text-anchor="middle"
              class="location-label"
              :class="{ 'label-small': getLocationRadius(location) < 15 }"
            >
              {{ location.locationName }}
            </text>
            
            <!-- 资产数量显示 -->
            <text
              :x="location.x"
              :y="location.y + 5"
              text-anchor="middle"
              class="asset-count-text"
              :fill="getTextColor(location)"
            >
              {{ location.totalAssets }}
            </text>
            
            <!-- 告警图标 -->
            <g v-if="hasAlert(location)" class="alert-icon">
              <circle 
                :cx="location.x + getLocationRadius(location) - 5"
                :cy="location.y - getLocationRadius(location) + 5"
                r="6"
                fill="#F56C6C"
              />
              <text 
                :x="location.x + getLocationRadius(location) - 5"
                :y="location.y - getLocationRadius(location) + 9"
                text-anchor="middle"
                fill="white"
                font-size="10"
                font-weight="bold"
              >!</text>
            </g>
          </g>
        </g>
        
        <!-- 连接线（显示位置关系） -->
        <g class="location-connections" v-if="showConnections">
          <line 
            v-for="connection in locationConnections"
            :key="`${connection.from}-${connection.to}`"
            :x1="connection.x1"
            :y1="connection.y1"
            :x2="connection.x2"
            :y2="connection.y2"
            stroke="#ddd"
            stroke-width="1"
            stroke-dasharray="5,5"
          />
        </g>
      </svg>
    </div>

    <!-- 图例 -->
    <div class="map-legend">
      <div class="legend-title">图例说明</div>
      
      <!-- 颜色图例 -->
      <div class="legend-section">
        <div class="legend-subtitle">资产类型</div>
        <div class="legend-items">
          <div 
            v-for="(color, assetType) in assetTypeColors" 
            :key="assetType"
            class="legend-item"
          >
            <span 
              class="legend-color-dot" 
              :style="{ backgroundColor: color }"
            ></span>
            <span class="legend-label">{{ assetType }}</span>
          </div>
        </div>
      </div>
      
      <!-- 大小图例 -->
      <div class="legend-section">
        <div class="legend-subtitle">资产数量</div>
        <div class="legend-items">
          <div class="legend-item">
            <span class="legend-size-dot small"></span>
            <span class="legend-label">1-5个</span>
          </div>
          <div class="legend-item">
            <span class="legend-size-dot medium"></span>
            <span class="legend-label">6-15个</span>
          </div>
          <div class="legend-item">
            <span class="legend-size-dot large"></span>
            <span class="legend-label">16+个</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 悬浮提示框 -->
    <div 
      v-show="tooltipVisible"
      class="location-tooltip"
      :style="{ left: tooltipX + 'px', top: tooltipY + 'px' }"
    >
      <div class="tooltip-header">
        <div class="tooltip-title">{{ tooltipLocation?.locationName }}</div>
        <div class="tooltip-code">{{ tooltipLocation?.locationCode }}</div>
      </div>
      <div class="tooltip-content">
        <div class="tooltip-section">
          <div class="section-title">资产统计</div>
          <div class="asset-stats">
            <div 
              v-for="(count, assetType) in tooltipLocation?.assetCounts" 
              :key="assetType"
              class="stat-item"
            >
              <span 
                class="stat-dot" 
                :style="{ backgroundColor: assetTypeColors[assetType] }"
              ></span>
              <span class="stat-label">{{ assetType }}:</span>
              <span class="stat-value">{{ count }}个</span>
            </div>
          </div>
        </div>
        <div class="tooltip-section" v-if="tooltipLocation?.departmentName">
          <div class="section-title">使用部门</div>
          <div class="department-name">{{ tooltipLocation.departmentName }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, reactive, onMounted, nextTick } from 'vue'

const props = defineProps({
  locations: {
    type: Array,
    default: () => []
  },
  statistics: {
    type: Object,
    default: () => ({})
  },
  fullscreen: {
    type: Boolean,
    default: false
  },
  backgroundImage: {
    type: String,
    default: ''
  },
  showConnections: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['location-click', 'enter-fullscreen', 'exit-fullscreen'])

// 响应式数据
const mapContainer = ref(null)
const mapWidth = ref(1200)
const mapHeight = ref(800)
const selectedLocationId = ref(null)
const hoveredLocationId = ref(null)

// 提示框相关
const tooltipVisible = ref(false)
const tooltipX = ref(0)
const tooltipY = ref(0)
const tooltipLocation = ref(null)

// 资产类型颜色映射
const assetTypeColors = reactive({
  '电脑': '#409EFF',
  '扫码器': '#67C23A',
  '打印机': '#E6A23C',
  '路由器': '#F56C6C',
  '服务器': '#909399',
  '交换机': '#9C27B0',
  '投影仪': '#FF9800'
})

// 布局区域配置
const layoutAreas = ref([
  { id: 'area1', x: 100, y: 100, width: 300, height: 200, color: '#E3F2FD', strokeColor: '#2196F3' },
  { id: 'area2', x: 450, y: 100, width: 300, height: 200, color: '#E8F5E8', strokeColor: '#4CAF50' },
  { id: 'area3', x: 100, y: 350, width: 300, height: 200, color: '#FFF3E0', strokeColor: '#FF9800' },
  { id: 'area4', x: 450, y: 350, width: 300, height: 200, color: '#FCE4EC', strokeColor: '#E91E63' }
])

// 处理后的可视化位置数据
const visualLocations = computed(() => {
  return props.locations.map(location => {
    const stats = props.statistics[location.id] || {}
    return {
      ...location,
      ...stats,
      x: getLocationX(location),
      y: getLocationY(location),
      totalAssets: stats.totalAssets || 0,
      assetCounts: stats.assetCounts || {}
    }
  })
})

// 位置连接线数据
const locationConnections = computed(() => {
  const connections = []
  // 这里可以根据位置的层级关系生成连接线
  // 示例：连接同一产线下的工位
  return connections
})

// 坐标计算函数
const getLocationX = (location) => {
  // 根据位置编码或名称映射到X坐标
  // 这里使用简单的哈希算法，实际应该根据真实布局配置
  const hash = location.locationCode?.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0)
    return a & a
  }, 0) || 0
  
  return 150 + (Math.abs(hash) % 800)
}

const getLocationY = (location) => {
  // 根据位置编码或名称映射到Y坐标
  const hash = location.locationCode?.split('').reduce((a, b) => {
    a = ((a << 7) - a) + b.charCodeAt(0)
    return a & a
  }, 0) || 0
  
  return 150 + (Math.abs(hash) % 500)
}

// 样式计算函数
const getLocationRadius = (location) => {
  const count = location.totalAssets || 0
  if (count === 0) return 8
  if (count <= 5) return 12
  if (count <= 15) return 18
  return 25
}

const getLocationColor = (location) => {
  const assetCounts = location.assetCounts || {}
  if (Object.keys(assetCounts).length === 0) return '#DCDFE6'
  
  // 找出数量最多的资产类型
  const mainAssetType = Object.keys(assetCounts).reduce((a, b) => 
    assetCounts[a] > assetCounts[b] ? a : b
  )
  
  return assetTypeColors[mainAssetType] || '#DCDFE6'
}

const getLocationStroke = (location) => {
  if (selectedLocationId.value === location.locationId) return '#E6A23C'
  if (hasAlert(location)) return '#F56C6C'
  return '#fff'
}

const getStrokeWidth = (location) => {
  if (selectedLocationId.value === location.locationId) return 3
  return 2
}

const getTextColor = (location) => {
  const radius = getLocationRadius(location)
  return radius > 15 ? '#fff' : '#303133'
}

// 告警检测
const hasAlert = (location) => {
  // 这里可以定义告警规则，例如：
  // 1. 某个位置长时间没有资产
  // 2. 资产数量异常
  // 3. 设备故障等
  return location.totalAssets === 0 && location.shouldHaveAssets
}

// 交互事件处理
const selectLocation = (location) => {
  selectedLocationId.value = location.locationId
  emit('location-click', location)
}

const showTooltip = (location, event) => {
  hoveredLocationId.value = location.locationId
  tooltipLocation.value = location
  tooltipX.value = event.clientX + 10
  tooltipY.value = event.clientY - 50
  tooltipVisible.value = true
}

const hideTooltip = () => {
  hoveredLocationId.value = null
  tooltipVisible.value = false
}

// 地图控制
const zoomIn = () => {
  // 放大地图逻辑
  mapWidth.value = Math.min(mapWidth.value * 1.2, 2400)
  mapHeight.value = Math.min(mapHeight.value * 1.2, 1600)
}

const zoomOut = () => {
  // 缩小地图逻辑
  mapWidth.value = Math.max(mapWidth.value * 0.8, 600)
  mapHeight.value = Math.max(mapHeight.value * 0.8, 400)
}

const resetZoom = () => {
  // 重置地图大小
  mapWidth.value = 1200
  mapHeight.value = 800
}

const enterFullscreen = () => {
  emit('enter-fullscreen')
}

const exitFullscreen = () => {
  emit('exit-fullscreen')
}

// 组件挂载
onMounted(() => {
  nextTick(() => {
    if (mapContainer.value) {
      // 根据容器大小调整地图尺寸
      const rect = mapContainer.value.getBoundingClientRect()
      if (rect.width > 0) {
        mapWidth.value = Math.max(rect.width, 800)
        mapHeight.value = Math.max(rect.height, 600)
      }
    }
  })
})
</script>

<style lang="scss" scoped>
.location-distribution-map {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
  
  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background-color: #1a1a1a;
  }
  
  .map-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #fff;
    border-bottom: 1px solid #e4e7ed;
    
    .toolbar-left {
      .el-button-group {
        .el-button {
          padding: 6px 8px;
        }
      }
    }
  }
  
  .map-container {
    position: relative;
    width: 100%;
    height: calc(100% - 60px);
    overflow: auto;
    background-color: #f0f2f5;
    
    .location-svg-map {
      display: block;
      margin: 0 auto;
      background-color: #fff;
      border: 1px solid #e4e7ed;
    }
  }
  
  .location-group {
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover .location-point {
      stroke-width: 3;
      filter: brightness(1.1);
    }
    
    .location-point {
      transition: all 0.3s ease;
      
      &.location-active {
        stroke: #E6A23C !important;
        stroke-width: 4;
        filter: drop-shadow(0 0 8px rgba(230, 162, 60, 0.6));
      }
      
      &.location-hover {
        transform: scale(1.1);
      }
    }
    
    .location-label {
      font-size: 12px;
      font-weight: 500;
      fill: #303133;
      pointer-events: none;
      
      &.label-small {
        font-size: 10px;
      }
    }
    
    .asset-count-text {
      font-size: 10px;
      font-weight: bold;
      pointer-events: none;
    }
    
    .alert-icon {
      animation: pulse 2s infinite;
    }
  }
  
  .area-rect {
    transition: all 0.3s ease;
    
    &:hover {
      fill-opacity: 0.2;
    }
  }
  
  .map-legend {
    position: absolute;
    top: 80px;
    right: 20px;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    min-width: 200px;
    
    .legend-title {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 12px;
      border-bottom: 1px solid #e4e7ed;
      padding-bottom: 8px;
    }
    
    .legend-section {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .legend-subtitle {
        font-size: 12px;
        font-weight: 500;
        color: #606266;
        margin-bottom: 8px;
      }
      
      .legend-items {
        display: flex;
        flex-direction: column;
        gap: 6px;
        
        .legend-item {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .legend-color-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 1px solid #fff;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
          }
          
          .legend-size-dot {
            border-radius: 50%;
            background-color: #409EFF;
            border: 2px solid #fff;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            
            &.small {
              width: 8px;
              height: 8px;
            }
            
            &.medium {
              width: 12px;
              height: 12px;
            }
            
            &.large {
              width: 16px;
              height: 16px;
            }
          }
          
          .legend-label {
            font-size: 12px;
            color: #606266;
          }
        }
      }
    }
  }
  
  .location-tooltip {
    position: fixed;
    background-color: rgba(0, 0, 0, 0.9);
    color: #fff;
    border-radius: 8px;
    padding: 12px;
    font-size: 12px;
    max-width: 280px;
    z-index: 10000;
    pointer-events: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    
    .tooltip-header {
      margin-bottom: 8px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
      padding-bottom: 6px;
      
      .tooltip-title {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 2px;
      }
      
      .tooltip-code {
        font-size: 11px;
        color: #ccc;
      }
    }
    
    .tooltip-content {
      .tooltip-section {
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .section-title {
          font-size: 11px;
          color: #ccc;
          margin-bottom: 4px;
        }
        
        .asset-stats {
          display: flex;
          flex-direction: column;
          gap: 3px;
          
          .stat-item {
            display: flex;
            align-items: center;
            gap: 6px;
            
            .stat-dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;
            }
            
            .stat-label {
              flex: 1;
              font-size: 11px;
            }
            
            .stat-value {
              font-weight: 600;
              color: #409EFF;
            }
          }
        }
        
        .department-name {
          font-size: 12px;
          color: #67C23A;
        }
      }
    }
  }
}

// 动画定义
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

// 全屏模式样式调整
.fullscreen {
  .map-container {
    height: calc(100vh - 60px);
  }
  
  .map-toolbar {
    background-color: rgba(26, 26, 26, 0.9);
    color: #fff;
    border-bottom: 1px solid #444;
  }
  
  .map-legend {
    background-color: rgba(26, 26, 26, 0.9);
    color: #fff;
    border: 1px solid #444;
  }
}
</style>
```

### 3.3 API接口扩展实现

#### A. 新增统计控制器
```csharp
// Controllers/AssetStatisticsController.cs
[ApiController]
[Route("api/[controller]")]
public class AssetStatisticsController : ControllerBase
{
    private readonly IAssetStatisticsService _statisticsService;
    private readonly ILocationService _locationService;
    private readonly IAssetService _assetService;

    public AssetStatisticsController(
        IAssetStatisticsService statisticsService,
        ILocationService locationService,
        IAssetService assetService)
    {
        _statisticsService = statisticsService;
        _locationService = locationService;
        _assetService = assetService;
    }

    /// <summary>
    /// 获取资产多维度统计数据
    /// </summary>
    [HttpGet]
    public async Task<ApiResponse<AssetStatisticsDto>> GetAssetStatistics()
    {
        try
        {
            var result = await _statisticsService.GetComprehensiveStatisticsAsync();
            return ApiResponse<AssetStatisticsDto>.Success(result);
        }
        catch (Exception ex)
        {
            return ApiResponse<AssetStatisticsDto>.Error($"获取统计数据失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取所有层级5位置（设备位置）
    /// </summary>
    [HttpGet("level5-locations")]
    public async Task<ApiResponse<List<Level5LocationDto>>> GetLevel5Locations()
    {
        try
        {
            var level5Locations = await _locationService.GetLocationsByTypeAsync(4);
            var result = new List<Level5LocationDto>();

            foreach (var location in level5Locations)
            {
                var assets = await _assetService.GetAssetsByLocationAsync(location.Id);
                var assetTypeDistribution = assets
                    .GroupBy(a => a.AssetType.Name)
                    .ToDictionary(g => g.Key, g => g.Count());

                result.Add(new Level5LocationDto
                {
                    LocationId = location.Id,
                    LocationName = location.Name,
                    LocationCode = location.Code,
                    FullPath = await _locationService.GetLocationFullPathAsync(location.Id),
                    AssetCount = assets.Count,
                    DepartmentName = location.Department?.Name,
                    AssetTypeDistribution = assetTypeDistribution,
                    CoordinateX = location.CoordinateX,
                    CoordinateY = location.CoordinateY
                });
            }

            return ApiResponse<List<Level5LocationDto>>.Success(result);
        }
        catch (Exception ex)
        {
            return ApiResponse<List<Level5LocationDto>>.Error($"获取层级5位置失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取指定位置的资产列表
    /// </summary>
    [HttpGet("location/{locationId}/assets")]
    public async Task<ApiResponse<List<AssetDto>>> GetLocationAssets(int locationId)
    {
        try
        {
            var assets = await _assetService.GetAssetsByLocationAsync(locationId);
            var result = assets.Select(a => new AssetDto
            {
                Id = a.Id,
                AssetCode = a.AssetCode,
                Name = a.Name,
                AssetTypeName = a.AssetType.Name,
                Model = a.Model,
                Brand = a.Brand,
                Status = a.Status,
                StatusText = GetStatusText(a.Status)
            }).ToList();

            return ApiResponse<List<AssetDto>>.Success(result);
        }
        catch (Exception ex)
        {
            return ApiResponse<List<AssetDto>>.Error($"获取位置资产失败: {ex.Message}");
        }
    }

    private string GetStatusText(int status)
    {
        return status switch
        {
            0 => "闲置",
            1 => "在用",
            2 => "维修中",
            3 => "报废",
            _ => "未知"
        };
    }
}
```

#### B. 统计服务实现
```csharp
// Services/AssetStatisticsService.cs
public interface IAssetStatisticsService
{
    Task<AssetStatisticsDto> GetComprehensiveStatisticsAsync();
    Task<Dictionary<int, LocationStatisticsDto>> GetAssetsByLocationAsync();
    Task<Dictionary<int, DepartmentStatisticsDto>> GetAssetsByDepartmentAsync();
    Task<Dictionary<string, int>> GetAssetsByTypeAsync();
}

public class AssetStatisticsService : IAssetStatisticsService
{
    private readonly AppDbContext _context;
    private readonly IMapper _mapper;

    public AssetStatisticsService(AppDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<AssetStatisticsDto> GetComprehensiveStatisticsAsync()
    {
        var result = new AssetStatisticsDto
        {
            ByLocation = await GetAssetsByLocationAsync(),
            ByDepartment = await GetAssetsByDepartmentAsync(),
            ByAssetType = await GetAssetsByTypeAsync(),
            Summary = await GetStatisticsSummaryAsync()
        };

        // 生成交叉统计矩阵
        result.LocationAssetMatrix = GenerateLocationAssetMatrix(result.ByLocation);
        result.DepartmentAssetMatrix = GenerateDepartmentAssetMatrix(result.ByDepartment);

        return result;
    }

    public async Task<Dictionary<int, LocationStatisticsDto>> GetAssetsByLocationAsync()
    {
        // 只统计层级5位置（type = 4）
        var level5Locations = await _context.Locations
            .Where(l => l.Type == 4 && l.IsActive)
            .Include(l => l.Assets)
                .ThenInclude(a => a.AssetType)
            .Include(l => l.Department)
            .Include(l => l.LocationUsers)
                .ThenInclude(lu => lu.Personnel)
            .ToListAsync();

        var result = new Dictionary<int, LocationStatisticsDto>();

        foreach (var location in level5Locations)
        {
            var assetCounts = location.Assets
                .Where(a => a.Status != 3) // 排除报废资产
                .GroupBy(a => a.AssetType.Name)
                .ToDictionary(g => g.Key, g => g.Count());

            var users = location.LocationUsers
                .Where(lu => lu.UserType == 0) // 使用人员
                .Select(lu => lu.Personnel.Name)
                .ToList();

            result[location.Id] = new LocationStatisticsDto
            {
                LocationId = location.Id,
                LocationName = location.Name,
                LocationCode = location.Code,
                FullPath = await GetLocationFullPath(location),
                AssetCounts = assetCounts,
                TotalAssets = assetCounts.Values.Sum(),
                DepartmentName = location.Department?.Name,
                Users = users
            };
        }

        return result;
    }

    public async Task<Dictionary<int, DepartmentStatisticsDto>> GetAssetsByDepartmentAsync()
    {
        var departments = await _context.Departments
            .Include(d => d.Locations)
                .ThenInclude(l => l.Assets)
                    .ThenInclude(a => a.AssetType)
            .Include(d => d.Personnel)
            .Where(d => d.IsActive)
            .ToListAsync();

        var result = new Dictionary<int, DepartmentStatisticsDto>();

        foreach (var department in departments)
        {
            var allAssets = department.Locations
                .SelectMany(l => l.Assets)
                .Where(a => a.Status != 3) // 排除报废资产
                .ToList();

            var assetCounts = allAssets
                .GroupBy(a => a.AssetType.Name)
                .ToDictionary(g => g.Key, g => g.Count());

            var locations = department.Locations
                .Where(l => l.Type == 4) // 只包含层级5位置
                .Select(l => l.Name)
                .ToList();

            result[department.Id] = new DepartmentStatisticsDto
            {
                DepartmentId = department.Id,
                DepartmentName = department.Name,
                AssetCounts = assetCounts,
                TotalAssets = assetCounts.Values.Sum(),
                Locations = locations,
                PersonnelCount = department.Personnel.Count
            };
        }

        return result;
    }

    public async Task<Dictionary<string, int>> GetAssetsByTypeAsync()
    {
        var assetTypeCounts = await _context.Assets
            .Where(a => a.Status != 3) // 排除报废资产
            .GroupBy(a => a.AssetType.Name)
            .Select(g => new { AssetType = g.Key, Count = g.Count() })
            .ToDictionaryAsync(x => x.AssetType, x => x.Count);

        return assetTypeCounts;
    }

    private async Task<StatisticsSummaryDto> GetStatisticsSummaryAsync()
    {
        var totalAssets = await _context.Assets
            .Where(a => a.Status != 3)
            .CountAsync();

        var totalLevel5Locations = await _context.Locations
            .Where(l => l.Type == 4 && l.IsActive)
            .CountAsync();

        var totalDepartments = await _context.Departments
            .Where(d => d.IsActive)
            .CountAsync();

        return new StatisticsSummaryDto
        {
            TotalAssets = totalAssets,
            TotalLevel5Locations = totalLevel5Locations,
            TotalDepartments = totalDepartments,
            LastUpdated = DateTime.Now
        };
    }

    private Dictionary<string, Dictionary<string, int>> GenerateLocationAssetMatrix(
        Dictionary<int, LocationStatisticsDto> locationStats)
    {
        var matrix = new Dictionary<string, Dictionary<string, int>>();

        foreach (var location in locationStats.Values)
        {
            matrix[location.LocationName] = location.AssetCounts;
        }

        return matrix;
    }

    private Dictionary<string, Dictionary<string, int>> GenerateDepartmentAssetMatrix(
        Dictionary<int, DepartmentStatisticsDto> departmentStats)
    {
        var matrix = new Dictionary<string, Dictionary<string, int>>();

        foreach (var department in departmentStats.Values)
        {
            matrix[department.DepartmentName] = department.AssetCounts;
        }

        return matrix;
    }

    private async Task<string> GetLocationFullPath(Location location)
    {
        var pathParts = new List<string>();
        var current = location;

        while (current != null)
        {
            pathParts.Insert(0, current.Name);
            if (current.ParentId.HasValue)
            {
                current = await _context.Locations
                    .FirstOrDefaultAsync(l => l.Id == current.ParentId.Value);
            }
            else
            {
                break;
            }
        }

        return string.Join(" / ", pathParts);
    }
}
```

### 3.4 前端API服务
```javascript
// /src/api/assetStatistics.js
import request from '@/utils/request'

const assetStatisticsApi = {
  /**
   * 获取资产多维度统计数据
   */
  getAssetStatistics() {
    return request.get('/AssetStatistics')
  },

  /**
   * 获取所有层级5位置
   */
  getLevel5Locations() {
    return request.get('/AssetStatistics/level5-locations')
  },

  /**
   * 获取指定位置的资产列表
   * @param {number} locationId 位置ID
   */
  getLocationAssets(locationId) {
    return request.get(`/AssetStatistics/location/${locationId}/assets`)
  },

  /**
   * 获取位置资产分布（用于地图可视化）
   * @param {Object} params 查询参数
   */
  getLocationDistribution(params) {
    return request.get('/AssetStatistics/location-distribution', { params })
  }
}

export default assetStatisticsApi
```

## 四、实施计划

### 阶段一：基础统计功能（2周）
1. **后端开发**
   - 创建AssetStatisticsController和相关Service
   - 实现按位置、部门、资产类型的统计API
   - 添加层级5位置查询接口

2. **前端开发**
   - 创建资产统计页面
   - 实现统计数据表格展示
   - 添加基础图表组件

### 阶段二：可视化地图（2周）
1. **地图组件开发**
   - 实现SVG位置分布图
   - 添加位置坐标映射逻辑
   - 实现交互功能（点击、悬浮）

2. **数据集成**
   - 位置统计数据与地图组件对接
   - 实现实时数据更新
   - 添加图例和工具栏

### 阶段三：大屏展示（1周）
1. **全屏模式**
   - 实现全屏切换功能
   - 优化大屏显示效果
   - 添加自动刷新机制

2. **性能优化**
   - 数据缓存策略
   - 组件懒加载
   - 动画性能优化

## 五、技术要点

### 5.1 数据获取策略
- **层级5位置识别**：`WHERE location.type = 4`
- **资产关联查询**：通过LocationId关联获取资产列表
- **部门关联**：通过位置的DepartmentId获取部门信息
- **实时统计**：支持缓存和实时查询两种模式

### 5.2 可视化技术选型
- **SVG渲染**：适合矢量图形，支持缩放和交互
- **坐标映射**：位置编码到平面坐标的转换算法
- **色彩编码**：资产类型和数量的视觉映射
- **响应式设计**：支持不同屏幕尺寸

### 5.3 性能优化
- **数据缓存**：Redis缓存统计结果，减少数据库查询
- **分页加载**：大量位置数据分批加载
- **虚拟滚动**：表格组件支持大数据量展示
- **防抖节流**：用户交互事件的性能优化

## 六、扩展功能

### 6.1 告警功能
- 位置资产数量异常告警
- 长期无资产位置提醒
- 资产集中度过高告警

### 6.2 历史统计
- 资产数量变化趋势
- 位置使用率统计
- 部门资产配置分析

### 6.3 配置管理
- 位置坐标在线配置
- 布局图片上传管理
- 统计规则自定义

---

## 总结

本方案提供了一套完整的IT资产数字化呈现解决方案，涵盖了从数据统计、可视化展示到大屏监控的全流程。通过多维度统计和直观的位置分布图，能够帮助管理者快速了解资产分布状况，提高资产管理效率。

**核心价值：**
1. **数字化转型**：将传统的资产统计转化为可视化数字看板
2. **多维度分析**：支持按位置、部门、资产类型的立体化统计
3. **实时监控**：提供大屏展示模式，适合管理层决策使用
4. **交互体验**：丰富的用户交互，支持钻取和详情查看

该方案基于现有系统架构，具有良好的可扩展性和维护性，能够满足当前和未来的业务需求。