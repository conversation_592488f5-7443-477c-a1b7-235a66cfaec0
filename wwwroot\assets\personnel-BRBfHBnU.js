import{aj as e}from"./index-C7OOw0MO.js";import{s as a}from"./system-9jEcQzSp.js";const t={getDepartmentList:a=>e.get("/Department",{params:a}).then((e=>({data:e}))).catch((e=>({data:{success:!1,message:e.message||"服务器错误"}}))),getDepartments(e){return this.getDepartmentList(e)},getDepartmentTree:()=>e.get("/Department/tree").then((e=>({data:e}))).catch((e=>({data:{success:!1,message:e.message||"服务器错误"}}))),getDepartmentDetail:a=>e.get(`/Department/${a}`).then((e=>({data:e}))).catch((e=>({data:{success:!1,message:e.message||"服务器错误"}}))),createDepartment(a){const t={name:a.name,code:a.code,description:a.description||"",parentId:a.parentId||null,managerId:a.managerId||null,deputyManagerId:a.deputyManagerId||null,isActive:"active"===a.status};return e.post("/Department",t).then((e=>({data:e}))).catch((e=>{var a,t;return{data:{success:!1,message:(null==(t=null==(a=e.response)?void 0:a.data)?void 0:t.message)||e.message||"服务器错误"}}}))},updateDepartment(a,t){const s={name:t.name,code:t.code,description:t.description||"",parentId:t.parentId||null,managerId:t.managerId||null,deputyManagerId:t.deputyManagerId||null,isActive:"active"===t.status};return e.put(`/Department/${a}`,s).then((e=>({data:e}))).catch((e=>{var a,t;return{data:{success:!1,message:(null==(t=null==(a=e.response)?void 0:a.data)?void 0:t.message)||e.message||"服务器错误"}}}))},deleteDepartment:a=>e.delete(`/Department/${a}`).then((e=>({data:e}))).catch((e=>({data:{success:!1,message:e.message||"服务器错误"}}))),updateLocationDepartment:(a,t)=>e.put(`/Location/${a}/department`,t).then((e=>({data:e}))).catch((e=>({data:{success:!1,message:e.message||"服务器错误"}}))),getDepartmentUsers:a=>e.get(`/Department/${a}/users`).then((e=>({data:e}))).catch((e=>({data:{success:!1,message:e.message||"服务器错误"}})))},s={getPersonnelList:(a={})=>e.get("/Personnel",{params:a}).then((e=>e&&"object"==typeof e&&"success"in e?{data:e}:{data:{success:!0,data:Array.isArray(e)?e:(null==e?void 0:e.data)||[],message:"获取成功"}})).catch((e=>{var a,t;return{data:{success:!1,data:[],message:(null==(t=null==(a=e.response)?void 0:a.data)?void 0:t.message)||e.message||"服务器错误"}}})),getPersonnelById:a=>e.get(`/Personnel/${a}`).then((e=>({data:e}))).catch((e=>({data:{success:!1,message:e.message||"服务器错误"}}))),createPersonnel(a){const t={name:a.name,position:a.position||"",contact:a.contact||"",departmentId:a.departmentId,employeeCode:a.employeeCode||""};return e.post("/Personnel",t).then((e=>({data:e}))).catch((e=>{var a,t;return{data:{success:!1,message:(null==(t=null==(a=e.response)?void 0:a.data)?void 0:t.message)||e.message||"服务器错误"}}}))},updatePersonnel(a,t){const s={name:t.name,position:t.position||"",contact:t.contact||"",departmentId:t.departmentId,employeeCode:t.employeeCode||""},n=`/Personnel/${a}`;return e.put(n,s).then((e=>({data:e}))).catch((e=>{var a,t;return{data:{success:!1,message:(null==(t=null==(a=e.response)?void 0:a.data)?void 0:t.message)||e.message||"服务器错误"}}}))},deletePersonnel(a){const t=`/Personnel/${a}`;return e.delete(t).then((e=>({data:e}))).catch((e=>{var a,t;return{data:{success:!1,message:(null==(t=null==(a=e.response)?void 0:a.data)?void 0:t.message)||e.message||"服务器错误"}}}))}};export{t as d,s as p};
