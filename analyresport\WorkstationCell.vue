<template>
  <div
    class="modern-workstation-cell"
    :class="[
      `status-${workstation.status}`,
      {
        'highlighted': workstation.isHighlighted,
        'selected': isSelected,
        'hovered': isHovered,
        'pulse-animation': workstation.status === 'error'
      }
    ]"
    :style="cellStyle"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- Status indicator with glow effect -->
    <div class="status-indicator">
      <div class="status-icon-container">
        <el-icon class="status-icon" :class="`status-${workstation.status}`">
          <Check v-if="workstation.status === 'operational'" />
          <Warning v-else-if="workstation.status === 'warning'" />
          <Close v-else-if="workstation.status === 'error'" />
          <Setting v-else />
        </el-icon>
        <div class="status-glow" :class="`glow-${workstation.status}`"></div>
      </div>
    </div>

    <!-- Workstation ID -->
    <div class="workstation-id">
      {{ workstation.locationId }}
    </div>

    <!-- Efficiency bar -->
    <div class="efficiency-bar">
      <div 
        class="efficiency-fill" 
        :class="`efficiency-${getEfficiencyLevel(workstation.efficiency)}`"
        :style="{ width: workstation.efficiency + '%' }"
      ></div>
    </div>

    <!-- Fault badge -->
    <div v-if="workstation.faultCount > 0" class="fault-badge">
      {{ workstation.faultCount }}
    </div>

    <!-- Hover tooltip -->
    <transition name="tooltip-fade">
      <div v-if="isHovered" class="hover-tooltip">
        <div class="tooltip-header">
          <span class="tooltip-name">{{ workstation.locationName }}</span>
          <span class="tooltip-status" :class="`status-${workstation.status}`">
            {{ getStatusText(workstation.status) }}
          </span>
        </div>
        <div class="tooltip-metrics">
          <div class="metric">
            <span class="metric-label">效率:</span>
            <span class="metric-value">{{ workstation.efficiency }}%</span>
          </div>
          <div class="metric">
            <span class="metric-label">设备:</span>
            <span class="metric-value">{{ workstation.assetCount }}</span>
          </div>
          <div class="metric">
            <span class="metric-label">任务:</span>
            <span class="metric-value">{{ workstation.taskCount }}</span>
          </div>
        </div>
      </div>
    </transition>

    <!-- Selection ring -->
    <div v-if="isSelected" class="selection-ring"></div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Check, Warning, Close, Setting } from '@element-plus/icons-vue'

const props = defineProps({
  workstation: {
    type: Object,
    required: true
  },
  isSelected: {
    type: Boolean,
    default: false
  },
  isHovered: {
    type: Boolean,
    default: false
  },
  cellSize: {
    type: Object,
    default: () => ({ width: 32, height: 28 })
  }
})

const emit = defineEmits(['click', 'mouseenter', 'mouseleave'])

const cellStyle = computed(() => ({
  position: 'absolute',
  left: `${props.workstation.x}px`,
  top: `${props.workstation.y}px`,
  width: `${props.cellSize.width}px`,
  height: `${props.cellSize.height}px`,
}))

const getEfficiencyLevel = (efficiency) => {
  if (efficiency >= 80) return 'high'
  if (efficiency >= 60) return 'medium'
  return 'low'
}

const getStatusText = (status) => {
  const statusMap = {
    operational: '正常',
    warning: '警告',
    error: '故障',
    idle: '空闲'
  }
  return statusMap[status] || '未知'
}

const handleClick = () => {
  emit('click', props.workstation.locationId)
}

const handleMouseEnter = () => {
  emit('mouseenter', props.workstation.locationId)
}

const handleMouseLeave = () => {
  emit('mouseleave')
}
</script>

<style scoped>
.modern-workstation-cell {
  position: relative;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  background: rgba(26, 54, 80, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.3);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(8px);
}

/* Status-based styling */
.modern-workstation-cell.status-operational {
  border-color: rgba(16, 185, 129, 0.4);
  background: rgba(16, 185, 129, 0.1);
}

.modern-workstation-cell.status-warning {
  border-color: rgba(245, 158, 11, 0.4);
  background: rgba(245, 158, 11, 0.1);
}

.modern-workstation-cell.status-error {
  border-color: rgba(239, 68, 68, 0.4);
  background: rgba(239, 68, 68, 0.1);
}

.modern-workstation-cell.status-idle {
  border-color: rgba(59, 130, 246, 0.4);
  background: rgba(59, 130, 246, 0.1);
}

/* Hover effects */
.modern-workstation-cell:hover {
  transform: scale(1.1) translateZ(0);
  z-index: 10;
}

.modern-workstation-cell.status-operational:hover {
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.modern-workstation-cell.status-warning:hover {
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
}

.modern-workstation-cell.status-error:hover {
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

.modern-workstation-cell.status-idle:hover {
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

/* Status indicator */
.status-indicator {
  position: relative;
  margin-bottom: 2px;
}

.status-icon-container {
  position: relative;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-icon {
  font-size: 10px;
  position: relative;
  z-index: 2;
}

.status-icon.status-operational {
  color: #10b981;
}

.status-icon.status-warning {
  color: #f59e0b;
}

.status-icon.status-error {
  color: #ef4444;
}

.status-icon.status-idle {
  color: #3b82f6;
}

.status-glow {
  position: absolute;
  inset: -2px;
  border-radius: 50%;
  z-index: 1;
  opacity: 0.3;
  filter: blur(2px);
}

.status-glow.glow-operational {
  background: #10b981;
}

.status-glow.glow-warning {
  background: #f59e0b;
}

.status-glow.glow-error {
  background: #ef4444;
}

.status-glow.glow-idle {
  background: #3b82f6;
}

/* Workstation ID */
.workstation-id {
  font-size: 8px;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 1px;
  text-align: center;
  line-height: 1;
}

/* Efficiency bar */
.efficiency-bar {
  width: 80%;
  height: 2px;
  background: rgba(59, 130, 246, 0.2);
  border-radius: 1px;
  overflow: hidden;
  margin-bottom: 1px;
}

.efficiency-fill {
  height: 100%;
  border-radius: 1px;
  transition: width 0.5s ease;
}

.efficiency-fill.efficiency-high {
  background: #10b981;
}

.efficiency-fill.efficiency-medium {
  background: #f59e0b;
}

.efficiency-fill.efficiency-low {
  background: #ef4444;
}

/* Fault badge */
.fault-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 7px;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  z-index: 5;
}

/* Highlighted state */
.modern-workstation-cell.highlighted {
  border: 2px dashed #66b0ff;
  animation: highlight-pulse 2s infinite;
}

/* Selected state */
.modern-workstation-cell.selected {
  z-index: 15;
}

.selection-ring {
  position: absolute;
  inset: -3px;
  border: 2px solid #66b0ff;
  border-radius: 10px;
  pointer-events: none;
  animation: selection-pulse 1.5s infinite;
}

/* Hover tooltip */
.hover-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(12, 26, 37, 0.95);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  padding: 8px 10px;
  white-space: nowrap;
  z-index: 20;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
  margin-bottom: 8px;
  font-size: 10px;
  min-width: 120px;
}

.hover-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: rgba(12, 26, 37, 0.95);
}

.tooltip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  padding-bottom: 4px;
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

.tooltip-name {
  font-weight: 600;
  color: #e2e8f0;
  font-size: 9px;
}

.tooltip-status {
  font-size: 8px;
  font-weight: 500;
  padding: 1px 4px;
  border-radius: 4px;
}

.tooltip-status.status-operational {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.tooltip-status.status-warning {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.tooltip-status.status-error {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.tooltip-status.status-idle {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.tooltip-metrics {
  display: flex;
  gap: 8px;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1px;
}

.metric-label {
  font-size: 7px;
  color: #94a3b8;
}

.metric-value {
  font-size: 8px;
  font-weight: 600;
  color: #e2e8f0;
}

/* Animations */
@keyframes pulse-animation {
  0%, 100% {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.4);
  }
  50% {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.6);
  }
}

.pulse-animation {
  animation: pulse-animation 2s infinite;
}

@keyframes highlight-pulse {
  0%, 100% {
    border-color: #66b0ff;
    box-shadow: 0 0 10px rgba(102, 176, 255, 0.3);
  }
  50% {
    border-color: #3d8fd1;
    box-shadow: 0 0 20px rgba(102, 176, 255, 0.5);
  }
}

@keyframes selection-pulse {
  0%, 100% {
    border-color: #66b0ff;
    box-shadow: 0 0 15px rgba(102, 176, 255, 0.4);
  }
  50% {
    border-color: #3d8fd1;
    box-shadow: 0 0 25px rgba(102, 176, 255, 0.6);
  }
}

/* Tooltip transition */
.tooltip-fade-enter-active,
.tooltip-fade-leave-active {
  transition: all 0.3s ease;
}

.tooltip-fade-enter-from,
.tooltip-fade-leave-to {
  opacity: 0;
  transform: translateX(-50%) translateY(10px);
}

/* Responsive sizing */
@media (max-width: 1200px) {
  .modern-workstation-cell {
    transform-origin: center;
  }
  
  .workstation-id {
    font-size: 7px;
  }
  
  .status-icon {
    font-size: 8px;
  }
  
  .efficiency-bar {
    height: 1.5px;
  }
  
  .fault-badge {
    width: 10px;
    height: 10px;
    font-size: 6px;
  }
}

@media (max-width: 768px) {
  .hover-tooltip {
    display: none; /* Hide tooltips on mobile */
  }
}
</style>