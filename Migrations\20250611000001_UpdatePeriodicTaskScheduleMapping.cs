using Microsoft.EntityFrameworkCore.Migrations;
using System;

#nullable disable

namespace ItAssetsSystem.Migrations
{
    /// <summary>
    /// 更新PeriodicTaskSchedule的EF Core映射，确保与数据库结构一致
    /// </summary>
    public partial class UpdatePeriodicTaskScheduleMapping : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // 这个迁移主要是为了重新生成模型快照
            // 确保EF Core使用正确的表名和列名映射
            
            // 确保 periodictaskschedules 表的列名映射正确
            migrationBuilder.Sql("SELECT 1"); // 空操作，只是为了触发模型重新构建
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // 无需回滚操作
        }
    }
}