<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分页测试 - IT资产管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .controls {
            padding: 30px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }

        .control-group {
            display: flex;
            gap: 20px;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            padding: 20px 30px;
            background: #f1f5f9;
            border-left: 4px solid #3b82f6;
            margin: 20px 30px;
            border-radius: 0 8px 8px 0;
        }

        .pagination-controls {
            padding: 20px 30px;
            display: flex;
            gap: 10px;
            align-items: center;
            justify-content: center;
        }

        .pagination-controls input {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            width: 80px;
            text-align: center;
        }

        .pagination-controls select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
        }

        .data-display {
            padding: 30px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .data-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
        }

        .data-table tr:hover {
            background: #f9fafb;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .log {
            background: #1e293b;
            color: #e2e8f0;
            padding: 20px;
            margin: 20px 30px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-entry.success {
            color: #10b981;
        }

        .log-entry.error {
            color: #ef4444;
        }

        .log-entry.info {
            color: #3b82f6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 分页功能测试</h1>
            <p>测试资产API的分页功能是否正常工作</p>
        </div>

        <div class="controls">
            <div class="control-group">
                <button class="btn btn-primary" onclick="testPagination()">
                    <span id="test-loading" style="display: none;" class="loading"></span>
                    开始分页测试
                </button>
                <button class="btn btn-primary" onclick="clearResults()">清除结果</button>
            </div>
        </div>

        <div class="pagination-controls">
            <label>页码:</label>
            <input type="number" id="pageIndex" value="1" min="1">
            
            <label>每页大小:</label>
            <select id="pageSize">
                <option value="5">5条/页</option>
                <option value="10">10条/页</option>
                <option value="20" selected>20条/页</option>
                <option value="50">50条/页</option>
            </select>
            
            <button class="btn btn-primary" onclick="loadPage()">
                <span id="load-loading" style="display: none;" class="loading"></span>
                加载指定页
            </button>
        </div>

        <div class="status" id="status">
            <strong>状态:</strong> 准备就绪，点击按钮开始测试
        </div>

        <div class="data-display">
            <h3>数据展示</h3>
            <table class="data-table" id="dataTable">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>资产ID</th>
                        <th>资产编号</th>
                        <th>资产名称</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody id="dataTableBody">
                    <tr>
                        <td colspan="5" style="text-align: center; color: #6b7280;">暂无数据</td>
                    </tr>
                </tbody>
            </table>
            
            <div id="paginationInfo" style="text-align: center; color: #6b7280;">
                分页信息将在这里显示
            </div>
        </div>

        <div class="log" id="log">
            <div class="log-entry info">[INFO] 分页测试系统已初始化</div>
            <div class="log-entry info">[INFO] 后端服务地址: http://localhost:5001</div>
            <div class="log-entry info">[INFO] 准备开始分页测试...</div>
        </div>
    </div>

    <script>
        // 日志函数
        function addLog(message, type = 'info') {
            const log = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        // 更新状态
        function updateStatus(message) {
            document.getElementById('status').innerHTML = `<strong>状态:</strong> ${message}`;
        }

        // 加载指定页面
        async function loadPage() {
            const pageIndex = parseInt(document.getElementById('pageIndex').value) || 1;
            const pageSize = parseInt(document.getElementById('pageSize').value) || 20;
            const loading = document.getElementById('load-loading');
            
            loading.style.display = 'inline-block';
            updateStatus(`正在加载第${pageIndex}页，每页${pageSize}条...`);

            try {
                const url = `http://localhost:5001/api/Asset?pageIndex=${pageIndex}&pageSize=${pageSize}`;
                addLog(`请求URL: ${url}`, 'info');
                
                const response = await fetch(url);
                const data = await response.json();
                
                addLog(`响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                addLog(`响应数据: ${JSON.stringify(data, null, 2)}`, 'info');
                
                if (data.success && data.data) {
                    displayData(data.data, pageIndex, pageSize);
                    updateStatus(`成功加载第${pageIndex}页数据`);
                } else {
                    addLog('API返回失败: ' + (data.message || '未知错误'), 'error');
                    updateStatus('加载数据失败');
                }
            } catch (error) {
                addLog(`请求失败: ${error.message}`, 'error');
                updateStatus('请求失败');
            } finally {
                loading.style.display = 'none';
            }
        }

        // 显示数据
        function displayData(data, pageIndex, pageSize) {
            const tbody = document.getElementById('dataTableBody');
            const paginationInfo = document.getElementById('paginationInfo');
            
            if (data.items && data.items.length > 0) {
                tbody.innerHTML = '';
                
                data.items.forEach((item, index) => {
                    const row = document.createElement('tr');
                    const serialNumber = (pageIndex - 1) * pageSize + index + 1;
                    
                    row.innerHTML = `
                        <td>${serialNumber}</td>
                        <td>${item.id}</td>
                        <td>${item.assetCode || '-'}</td>
                        <td>${item.name || '-'}</td>
                        <td>${item.statusName || item.status || '-'}</td>
                    `;
                    tbody.appendChild(row);
                });
                
                paginationInfo.innerHTML = `
                    当前页: ${data.pageIndex || pageIndex} | 
                    每页大小: ${data.pageSize || pageSize} | 
                    总记录数: ${data.total || data.totalCount || 0} | 
                    总页数: ${data.totalPages || Math.ceil((data.total || data.totalCount || 0) / pageSize)}
                `;
                
                addLog(`显示了${data.items.length}条记录`, 'success');
            } else {
                tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; color: #6b7280;">暂无数据</td></tr>';
                paginationInfo.innerHTML = '暂无数据';
                addLog('没有数据返回', 'info');
            }
        }

        // 测试分页功能
        async function testPagination() {
            const loading = document.getElementById('test-loading');
            loading.style.display = 'inline-block';
            updateStatus('正在进行分页测试...');
            
            try {
                addLog('开始分页测试', 'info');
                
                // 测试第1页
                addLog('测试第1页...', 'info');
                await loadSpecificPage(1, 5);
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 测试第2页
                addLog('测试第2页...', 'info');
                await loadSpecificPage(2, 5);
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 测试第3页
                addLog('测试第3页...', 'info');
                await loadSpecificPage(3, 5);
                
                addLog('分页测试完成', 'success');
                updateStatus('分页测试完成');
            } catch (error) {
                addLog(`分页测试失败: ${error.message}`, 'error');
                updateStatus('分页测试失败');
            } finally {
                loading.style.display = 'none';
            }
        }

        // 加载特定页面
        async function loadSpecificPage(pageIndex, pageSize) {
            const url = `http://localhost:5001/api/Asset?pageIndex=${pageIndex}&pageSize=${pageSize}`;
            addLog(`请求: ${url}`, 'info');
            
            const response = await fetch(url);
            const data = await response.json();
            
            if (data.success && data.data && data.data.items) {
                const firstItem = data.data.items[0];
                const lastItem = data.data.items[data.data.items.length - 1];
                
                addLog(`第${pageIndex}页结果: ${data.data.items.length}条记录`, 'success');
                addLog(`  首条记录ID: ${firstItem.id}, 编号: ${firstItem.assetCode}`, 'info');
                addLog(`  末条记录ID: ${lastItem.id}, 编号: ${lastItem.assetCode}`, 'info');
                addLog(`  总记录数: ${data.data.total}`, 'info');
                
                // 如果是最后一次调用，显示数据
                if (pageIndex === 3) {
                    displayData(data.data, pageIndex, pageSize);
                }
            } else {
                addLog(`第${pageIndex}页请求失败`, 'error');
            }
        }

        // 清除结果
        function clearResults() {
            document.getElementById('dataTableBody').innerHTML = 
                '<tr><td colspan="5" style="text-align: center; color: #6b7280;">暂无数据</td></tr>';
            document.getElementById('paginationInfo').innerHTML = '分页信息将在这里显示';
            document.getElementById('log').innerHTML = `
                <div class="log-entry info">[INFO] 结果已清除</div>
                <div class="log-entry info">[INFO] 准备开始新的测试...</div>
            `;
            updateStatus('结果已清除，准备开始新的测试');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('分页测试页面已加载', 'success');
            updateStatus('准备就绪，点击按钮开始测试');
        });
    </script>
</body>
</html>
