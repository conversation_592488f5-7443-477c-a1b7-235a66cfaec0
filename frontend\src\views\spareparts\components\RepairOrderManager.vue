<template>
  <div class="repair-order-manager">
    <!-- 返厂维修汇总单列表 -->
    <el-card class="repair-orders-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">🔧 返厂维修汇总单</span>
          <div class="header-actions">
            <el-button type="primary" @click="handleCreateRepairOrder">创建维修单</el-button>
            <el-button type="success" @click="handleImportFromFaults">从故障清单导入</el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :inline="true" :model="queryParams" class="filter-form">
          <el-form-item label="维修单号">
            <el-input v-model="queryParams.orderNumber" placeholder="维修单号" clearable />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="queryParams.status" placeholder="选择状态" clearable>
              <el-option label="待提交" value="PENDING" />
              <el-option label="已提交" value="SUBMITTED" />
              <el-option label="维修中" value="REPAIRING" />
              <el-option label="已完成" value="COMPLETED" />
              <el-option label="已取消" value="CANCELLED" />
            </el-select>
          </el-form-item>
          <el-form-item label="供应商">
            <el-select v-model="queryParams.supplierId" placeholder="选择供应商" clearable>
              <el-option v-for="supplier in suppliers" :key="supplier.id" :label="supplier.name" :value="supplier.id" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="loadRepairOrders">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 维修单列表 -->
      <el-table :data="repairOrders" border style="width: 100%">
        <el-table-column prop="orderNumber" label="维修单号" width="150" />
        <el-table-column prop="title" label="维修标题" min-width="200" />
        <el-table-column prop="supplierName" label="维修供应商" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">{{ getStatusText(row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="itemCount" label="维修项目数" width="100" align="center" />
        <el-table-column prop="estimatedCost" label="预估费用" width="120" align="right">
          <template #default="{ row }">
            ¥{{ row.estimatedCost ? row.estimatedCost.toFixed(2) : '0.00' }}
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="150">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="info" size="small" @click="handleViewDetails(row)">详情</el-button>
            <el-button type="primary" size="small" @click="handleEditOrder(row)" v-if="row.status === 'PENDING'">编辑</el-button>
            <el-button type="success" size="small" @click="handleSubmitOrder(row)" v-if="row.status === 'PENDING'">提交</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageIndex"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="loadRepairOrders"
          @current-change="loadRepairOrders"
        />
      </div>
    </el-card>

    <!-- 创建/编辑维修单对话框 -->
    <RepairOrderDialog
      v-model:visible="repairOrderDialogVisible"
      :order-data="currentRepairOrder"
      :mode="dialogMode"
      @submit="handleRepairOrderSubmit"
    />

    <!-- 从故障清单导入对话框 -->
    <FaultImportDialog
      v-model:visible="faultImportDialogVisible"
      @import="handleFaultImport"
    />

    <!-- 维修单详情对话框 -->
    <RepairOrderDetailDialog
      v-model:visible="detailDialogVisible"
      :order-id="currentOrderId"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import RepairOrderDialog from './RepairOrderDialog.vue'
import FaultImportDialog from './FaultImportDialog.vue'
import RepairOrderDetailDialog from './RepairOrderDetailDialog.vue'
import {
  getRepairOrders,
  submitRepairOrder,
  getMaintenanceSuppliers
} from '@/api/spareparts'

// 响应式数据
const repairOrders = ref([])
const suppliers = ref([])
const total = ref(0)

const queryParams = reactive({
  pageIndex: 1,
  pageSize: 10,
  orderNumber: '',
  status: '',
  supplierId: null
})

const repairOrderDialogVisible = ref(false)
const faultImportDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const currentRepairOrder = ref({})
const currentOrderId = ref(null)
const dialogMode = ref('create') // 'create' | 'edit'

// 方法
const loadRepairOrders = async () => {
  try {
    const response = await getRepairOrders(queryParams)
    if (response.success) {
      repairOrders.value = response.data.items || []
      total.value = response.data.totalCount || 0
    }
  } catch (error) {
    console.error('加载维修单失败:', error)
    ElMessage.error('加载维修单失败')
  }
}

const loadSuppliers = async () => {
  try {
    const response = await getMaintenanceSuppliers()
    if (response.success) {
      suppliers.value = response.data || []
    }
  } catch (error) {
    console.error('加载维修供应商失败:', error)
  }
}

const handleCreateRepairOrder = () => {
  currentRepairOrder.value = {}
  dialogMode.value = 'create'
  repairOrderDialogVisible.value = true
}

const handleImportFromFaults = () => {
  faultImportDialogVisible.value = true
}

const handleEditOrder = (order) => {
  currentRepairOrder.value = { ...order }
  dialogMode.value = 'edit'
  repairOrderDialogVisible.value = true
}

const handleViewDetails = (order) => {
  currentOrderId.value = order.id
  detailDialogVisible.value = true
}

const handleSubmitOrder = async (order) => {
  try {
    await ElMessageBox.confirm('确认提交此维修单？提交后将无法修改。', '确认提交', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await submitRepairOrder(order.id)
    if (response.success) {
      ElMessage.success('维修单提交成功')
      await loadRepairOrders()
    } else {
      ElMessage.error('提交失败: ' + response.message)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交维修单失败:', error)
      ElMessage.error('提交维修单失败')
    }
  }
}

const handleRepairOrderSubmit = async (orderData) => {
  await loadRepairOrders()
  repairOrderDialogVisible.value = false
}

const handleFaultImport = async (faultData) => {
  // 从故障数据创建维修单
  currentRepairOrder.value = {
    title: `故障维修 - ${faultData.title}`,
    description: faultData.description,
    faultId: faultData.id,
    priority: faultData.priority,
    relatedAssetId: faultData.assetId
  }
  dialogMode.value = 'create'
  faultImportDialogVisible.value = false
  repairOrderDialogVisible.value = true
}

const resetQuery = () => {
  Object.assign(queryParams, {
    pageIndex: 1,
    pageSize: 10,
    orderNumber: '',
    status: '',
    supplierId: null
  })
  loadRepairOrders()
}

const getStatusTagType = (status) => {
  const statusMap = {
    'PENDING': '',
    'SUBMITTED': 'warning',
    'REPAIRING': 'primary',
    'COMPLETED': 'success',
    'CANCELLED': 'danger'
  }
  return statusMap[status] || ''
}

const getStatusText = (status) => {
  const statusMap = {
    'PENDING': '待提交',
    'SUBMITTED': '已提交',
    'REPAIRING': '维修中',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消'
  }
  return statusMap[status] || status
}

const formatDateTime = (dateTime) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadRepairOrders()
  loadSuppliers()
})
</script>

<style scoped>
.repair-order-manager {
  padding: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-weight: 600;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.filter-section {
  margin-bottom: 16px;
}

.filter-form {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 16px;
  text-align: right;
}
</style>
