import{aj as t}from"./index-C7OOw0MO.js";function r(r){return t({url:"/v2/spareparttype",method:"get",params:r})}function a(r){return t({url:"/v2/spareparttype/tree",method:"get",params:r})}function e(r){return t({url:"/v2/spareparttype",method:"post",data:r})}function n(r,a){return t({url:`/v2/spareparttype/${r}`,method:"put",data:a})}function s(r){return t({url:`/v2/spareparttype/${r}`,method:"delete"})}function u(r){return t({url:"/v2/sparepartlocation",method:"get",params:r})}function o(r){return t({url:"/v2/sparepartlocation",method:"post",data:r})}function p(r,a){return t({url:`/v2/sparepartlocation/${r}`,method:"put",data:a})}function d(r){return t({url:`/v2/sparepartlocation/${r}`,method:"delete"})}function i(r={}){return t({url:"/v2/spare-parts",method:"get",params:r})}function c(r){return t({url:`/v2/spare-parts/${r}`,method:"get"})}function l(r){return t({url:"/v2/spare-parts",method:"post",data:r})}function m(r,a){return t({url:`/v2/spare-parts/${r}`,method:"put",data:a})}function f(r){return t({url:`/v2/spare-parts/${r}`,method:"delete"})}function h(r={}){return t({url:"/v2/spare-part-transactions",method:"get",params:r})}function v(r){return t({url:"/v2/spare-parts/transactions/in",method:"post",data:r})}function g(r){return t({url:"/v2/spare-parts/transactions/out",method:"post",data:r})}function $(){return t({url:"/v2/spare-parts/area-stats",method:"get"})}export{g as a,h as b,l as c,f as d,c as e,d as f,i as g,p as h,o as i,$ as j,u as k,s as l,n as m,e as n,a as o,r as p,v as s,m as u};
