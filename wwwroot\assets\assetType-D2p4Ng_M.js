import{aj as e}from"./index-C7OOw0MO.js";import{s as t}from"./system-9jEcQzSp.js";function s(t={}){const s={};return s.page=t.page||1,s.pageSize=t.pageSize||1e3,void 0!==t.isActive&&(s.isActive=t.isActive),t.keyword&&(s.keyword=t.keyword),e.get("/AssetType",{params:s}).then((e=>{if(e&&e.data&&Array.isArray(e.data.items)){const t=[],s=new Set;return e.data.items.forEach((e=>{s.has(e.id)||(s.add(e.id),t.push(e))})),e.data.items=t,e}return e})).catch((e=>{throw e}))}const r={getAssetTypes:s,getAssetTypeById:function(t){return e.get(`/AssetType/${t}`)},createAssetType:function(t){const s={...t};if(void 0!==s.parentId&&null!==s.parentId&&"string"==typeof s.parentId){const e=parseInt(s.parentId,10);isNaN(e)?""===s.parentId.trim()&&(s.parentId=null):s.parentId=e}return e.post("/AssetType",s)},updateAssetType:function(t,s){let r=t;return"string"==typeof t&&(r=parseInt(t,10),isNaN(r))?Promise.reject(new Error("资产类型ID无效")):s&&s.id&&"string"==typeof s.id&&(s.id=parseInt(s.id,10),isNaN(s.id))?Promise.reject(new Error("资产类型data.id无效")):e.put(`/AssetType/${r}`,s)},deleteAssetType:function(t){return e.delete(`/AssetType/${t}`)},getAssetTypeSpecs:function(t){return e.get(`/AssetType/${t}/specifications`)},updateAssetTypeSpecs:function(t,s){return e.put(`/AssetType/${t}/specifications`,s)},exportAssetTypes:function(t){return e.get("/AssetType/export",{params:t,responseType:"blob",headers:{Accept:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel,text/csv"}}).then((e=>e)).catch((e=>{throw e}))},getAssetTypeImportTemplate:function(){return e.get("/Import/template",{params:{entityType:"AssetTypes",format:"excel"},responseType:"blob"})},importAssetTypes:function(t){return e.post("/Import/data",t,{params:{entityType:"AssetTypes"},headers:{"Content-Type":"multipart/form-data"}})},toggleAssetTypeActive:function(t){let s=t;return"string"==typeof t&&(s=parseInt(t,10),isNaN(s))?Promise.reject(new Error("资产类型ID无效")):e.put(`/AssetType/${s}/toggle-active`)}};export{r as a,s as g};
