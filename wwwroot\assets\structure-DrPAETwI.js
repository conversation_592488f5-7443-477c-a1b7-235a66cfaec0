import{_ as e,r as a,c as l,a5 as t,p as d,m as s,al as r,b as n,d as u,e as o,w as i,f as c,am as p,$ as m,a0 as v,a8 as y,a as f,ag as g,o as h,A as I,af as b,Y as _,a9 as U,t as V,an as k,ao as w,F as C,h as D,ap as $,aq as x,ar as A,a1 as L}from"./index-C7OOw0MO.js";import{l as T}from"./location-CmcDmm4Y.js";import{d as j,p as B}from"./personnel-BRBfHBnU.js";import"./system-9jEcQzSp.js";const E={class:"location-structure-container"},q={class:"page-header"},N={class:"page-actions"},z={class:"tree-container"},F={class:"tree-node"},P={class:"label"},O={class:"code"},S={key:0,class:"count"},Y={class:"actions"},Z={key:0,class:"form-tip"},G={key:0,class:"form-tip"},H={class:"dialog-footer"},J={class:"dialog-footer"},K={class:"dialog-footer"},M=e({__name:"structure",setup(e){const M=a([]),Q=a(!1),R=a([]),W=a(null),X=a(!1),ee=a("add"),ae=l((()=>"add"===ee.value?"添加位置":"edit"===ee.value?"编辑位置":"addChild"===ee.value?"添加子位置":"位置信息")),le=a(null),te=a(!1),de=a(!1),se=a(!1),re=t({departmentId:null,locationId:null}),ne=a(!1),ue=a(null),oe=a([]),ie=a([]),ce=a(!1),pe={children:"children",label:"name"},me=a([]);a([]);const ve=a([]),ye=a([]),fe=a(!1),ge=t({id:void 0,name:"",code:"",type:void 0,parentId:null,description:"",address:"",remark:"",defaultDepartmentId:null,managerId:null,usageUserIds:[],isActive:!0}),he={name:[{required:!0,message:"请输入位置名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],code:[{required:!0,message:"请输入位置编码",trigger:"blur"},{pattern:/^[A-Z0-9_]{2,20}$/,message:"编码只能包含大写字母、数字和下划线",trigger:"blur"}],type:[{required:!0,message:"请选择位置类型",trigger:"change"}]},Ie=[{value:0,label:"工厂"},{value:1,label:"产线"},{value:2,label:"工序"},{value:3,label:"工位"},{value:4,label:"设备位置"}];l((()=>{if(void 0===ge.type)return[];if(0===ge.type)return[];const e=ge.type-1,a=l=>l.reduce(((l,t)=>(t.type===e&&l.push({id:t.id,name:`${t.name} [${t.code}]`,type:t.type}),t.children&&l.push(...a(t.children)),l)),[]);return a(M.value)}));const be=l((()=>{if(0===ge.type)return[];const e=ge.type-1,a=l=>l.reduce(((l,t)=>(t.type===e&&l.push({...t,children:void 0}),t.children&&l.push(...a(t.children)),l)),[]);return a(M.value)}));d((()=>ge.type),((e,a)=>{void 0!==a&&(ge.parentId=null),0===e&&(ge.parentId=null)}),{immediate:!0});const _e=async()=>{try{const e=await B.getPersonnelList();if(e.data&&e.data.success){const a=e.data.data||[];ve.value=a.map((e=>({value:e.id,label:`${e.name||"未命名"} ${e.employeeCode?`(${e.employeeCode})`:""} - ${e.departmentName||"无部门"}`}))),ye.value=a.map((e=>({value:e.id,label:`${e.name||"未命名"} ${e.employeeCode?`(${e.employeeCode})`:""} - ${e.departmentName||"无部门"}`})))}}catch(e){y.error("获取人员列表失败")}};d((()=>ge.defaultDepartmentId),(async e=>{if(e){const a=me.value.find((a=>a.id===e));a&&a.managerId?ge.managerId=a.managerId:ge.managerId=null}else ge.managerId=null})),d((()=>ge.parentId),(e=>{if(e){be.value.some((a=>a.id===e))||(ge.parentId=null)}}));const Ue=async()=>{Q.value=!0;try{const e=await T.getLocationTree();if(e.success){M.value=e.data||[];const a=M.value.map((e=>e.id));R.value=a}else y.error(e.message||"获取位置树结构失败"),M.value=[]}catch(e){y.error("获取位置树结构失败"),M.value=[]}finally{Q.value=!1}},Ve=()=>{const e=[],a=l=>{l.forEach((l=>{e.push(l.id),l.children&&l.children.length>0&&a(l.children)}))};a(M.value),R.value=e},ke=()=>{R.value=[]},we=()=>{ee.value="add",Te(),ge.type=0,X.value=!0},Ce=e=>{if(!e.children||0===e.children.length)return 0;let a=e.children.length;for(const l of e.children)a+=Ce(l);return a},De=async()=>{try{fe.value=!0;const e=await j.getDepartments();e.data&&e.data.success?me.value=e.data.data||[]:y.error(e.data&&e.data.message||"获取部门列表失败")}catch(e){y.error("获取部门列表失败")}finally{fe.value=!1}},$e=async()=>{Q.value=!0;try{const e=await B.getPersonnelList();if(e.data&&e.data.success){const a=e.data.data||[];oe.value=a.map((e=>({id:e.id,name:e.name||e.employeeCode,disabled:!1})))}else y.warning("获取人员列表失败"),oe.value=[]}catch(e){y.error("获取人员列表失败"),oe.value=[]}finally{Q.value=!1}},xe=async()=>{if(re.locationId)try{const e=await T.updateLocationDepartment(re.locationId,{departmentId:re.departmentId});e.success?(y.success("部门关联设置成功"),se.value=!1,Ue()):y.error(e.message||"设置部门关联失败")}catch(e){y.error("保存部门关联失败")}else y.warning("未选择位置")},Ae=async()=>{if(ue.value)try{const e={replaceExisting:!0,users:ie.value.map((e=>({personnelId:e,userType:0})))},a=await T.updateLocationUsers(ue.value,e);a.success?(y.success("人员关联设置成功"),ne.value=!1,Ue()):y.error(a.message||"设置人员关联失败")}catch(e){y.error("保存人员关联失败")}else y.warning("未选择位置")},Le=async()=>{if(le.value)try{await le.value.validate(),Q.value=!0;const a={id:ge.id,name:ge.name,code:ge.code,type:ge.type,parentId:ge.parentId,description:ge.remark,address:ge.address,defaultDepartmentId:ge.defaultDepartmentId,isActive:!0};let l;if(l="edit"===ee.value?await T.updateLocation(ge.id,a):await T.createLocation(a),l.success){if(y.success("edit"===ee.value?"更新位置成功":"添加位置成功"),ge.usageUserIds&&ge.usageUserIds.length>0){const a="edit"===ee.value?ge.id:l.data.id,t={replaceExisting:!0,users:[]};if(ge.usageUserIds&&ge.usageUserIds.length>0&&ge.usageUserIds.forEach((e=>{t.users.push({personnelId:e,userType:0})})),t.users.length>0)try{await T.updateLocationUsers(a,t)}catch(e){y.warning("位置创建成功，但关联人员失败")}}X.value=!1,Ue()}else y.error(l.message||"操作失败")}catch(e){y.error("表单验证失败，请检查输入")}finally{Q.value=!1}},Te=()=>{le.value&&le.value.resetFields(),Object.assign(ge,{id:void 0,name:"",code:"",type:void 0,parentId:null,description:"",address:"",remark:"",defaultDepartmentId:null,managerId:null,usageUserIds:[],isActive:!0})},je=async e=>{de.value=!0;try{const a=await T.getLocationDetail(e);if(a.success&&a.data){if(a.usageUsers&&Array.isArray(a.usageUsers)&&a.usageUsers.length>0){const e=a.usageUsers.map((e=>e.id||e.personnelId));a.data.usageUserIds=e;const l=a.usageUsers.map((e=>`${e.name||""}${e.employeeCode?` (${e.employeeCode})`:""}${e.departmentName?` - ${e.departmentName}`:""}`));l.length>0&&(a.data.usersDisplay=l.join(", "))}else a.data.usageUserIds=[],a.data.usersDisplay=null;return a.managers&&Array.isArray(a.managers)&&a.managers.length>0&&(a.data.managerId=a.managers[0].id||a.managers[0].personnelId),await _e(),a.data}return null}catch(a){return null}finally{de.value=!1}};s((()=>{Promise.all([Ue(),De(),$e(),_e()]).catch((e=>{}))}));r((()=>{M.value=[],R.value=[]}));const Be=async e=>{try{const a=await je(e);a&&(a.defaultDepartmentId?ge.defaultDepartmentId=a.defaultDepartmentId:a.parentId&&a.type>1&&Be(a.parentId),a.usageUserIds&&a.usageUserIds.length>0&&(ge.usageUserIds=[...a.usageUserIds]))}catch(a){}},Ee=e=>{e&&(0===ye.value.length&&_e(),ge.usageUserIds&&ge.usageUserIds.length)};return(e,a)=>{const l=f("el-button"),t=f("el-tree"),d=f("el-empty"),s=f("el-card"),r=f("el-input"),j=f("el-form-item"),B=f("el-option"),ue=f("el-select"),fe=f("el-icon"),De=f("el-form"),$e=f("el-dialog"),qe=f("el-transfer"),Ne=g("loading");return h(),n("div",E,[u("div",q,[a[20]||(a[20]=u("h2",{class:"page-title"},"位置结构",-1)),u("div",N,[o(l,{type:"primary",onClick:we,icon:c(p)},{default:i((()=>a[17]||(a[17]=[I("添加位置")]))),_:1},8,["icon"]),o(l,{type:"success",onClick:Ve,icon:c(m)},{default:i((()=>a[18]||(a[18]=[I("展开全部")]))),_:1},8,["icon"]),o(l,{onClick:ke,icon:c(v)},{default:i((()=>a[19]||(a[19]=[I("折叠全部")]))),_:1},8,["icon"])])]),o(s,{class:"tree-card"},{default:i((()=>[u("div",z,[b((h(),_(t,{ref_key:"locationTree",ref:W,data:M.value,props:pe,"node-key":"id","expand-on-click-node":!1,"expanded-keys":R.value,class:"location-tree"},{default:i((({node:e,data:a})=>[u("div",F,[u("span",P,V(a.name),1),u("span",O,"["+V(a.code)+"]",1),a.assetCount>0?(h(),n("span",S,"资产: "+V(a.assetCount),1)):U("",!0),u("div",Y,[o(l,{size:"small",type:"primary",onClick:e=>(async e=>{ee.value="edit",te.value=!e.parentId,Te(),Q.value=!0,de.value=!0;try{await _e();const a=await je(e.id);a&&A((()=>{ge.type=a.type,A((()=>{Object.assign(ge,{id:a.id,name:a.name,code:a.code,type:a.type,parentId:a.parentId,address:a.address,remark:a.description,defaultDepartmentId:a.defaultDepartmentId,managerId:a.managerId,usersDisplay:a.usersDisplay||null}),a.usageUserIds&&Array.isArray(a.usageUserIds)?ge.usageUserIds=[...a.usageUserIds]:ge.usageUserIds=[],!a.defaultDepartmentId&&a.parentId&&a.type>1&&Be(a.parentId),X.value=!0}))}))}catch(a){y.error("加载位置详情失败")}finally{Q.value=!1,de.value=!1}})(a),icon:c(k),circle:"",title:"编辑位置"},null,8,["onClick","icon"]),o(l,{size:"small",type:"success",onClick:e=>(e=>{if(ee.value="addChild",Te(),ge.parentId=e.id,ge.type=e.type+1,e.children){const a=e.children.length;ge.code=`${e.code}_${String(a+1).padStart(2,"0")}`}else ge.code=`${e.code}_01`;e.defaultDepartmentId&&(ge.defaultDepartmentId=e.defaultDepartmentId),X.value=!0})(a),icon:c(p),circle:"",title:"添加子位置"},null,8,["onClick","icon"]),o(l,{size:"small",type:"danger",onClick:e=>(async e=>{if(e.assetCount>0)return void y.warning(`该位置已关联 ${e.assetCount} 个资产，请先解除关联`);let a=!1;if(!(e.children&&e.children.length>0&&(a=await L.confirm(`该位置包含 ${e.children.length} 个子位置，是否一并删除？`,"警告",{confirmButtonText:"是，一并删除",cancelButtonText:"否，仅删除该位置",type:"warning",distinguishCancelAndClose:!0}).then((()=>!0)).catch((e=>{if("cancel"===e)return!1;throw new Error("cancel-all")})).catch((e=>"cancel-all"===e.message&&null)),null===a)))try{await L.confirm(a?`将删除此位置及其所有子位置（共 ${Ce(e)+1} 个），确定继续吗？`:"确定要删除该位置吗？删除后不可恢复！","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const l=await T.deleteLocation(e.id,{recursive:a});l.success?(y.success("删除成功"),Ue()):y.error(l.message||"删除失败")}catch(l){"cancel"!==l&&y.error("删除失败")}})(a),icon:c(w),circle:"",title:"删除位置"},null,8,["onClick","icon"])])])])),_:1},8,["data","expanded-keys"])),[[Ne,Q.value]]),0!==M.value.length||Q.value?U("",!0):(h(),_(d,{key:0,description:"暂无位置数据"}))])])),_:1}),o($e,{title:ae.value,modelValue:X.value,"onUpdate:modelValue":a[10]||(a[10]=e=>X.value=e),width:"600px","append-to-body":""},{footer:i((()=>[u("div",H,[o(l,{onClick:a[9]||(a[9]=e=>X.value=!1)},{default:i((()=>a[24]||(a[24]=[I("取 消")]))),_:1}),o(l,{type:"primary",onClick:Le},{default:i((()=>a[25]||(a[25]=[I("确 定")]))),_:1})])])),default:i((()=>[o(De,{ref_key:"locationForm",ref:le,model:ge,rules:he,"label-width":"100px"},{default:i((()=>[o(j,{label:"位置名称",prop:"name"},{default:i((()=>[o(r,{modelValue:ge.name,"onUpdate:modelValue":a[0]||(a[0]=e=>ge.name=e),placeholder:"请输入位置名称"},null,8,["modelValue"])])),_:1}),o(j,{label:"位置编码",prop:"code"},{default:i((()=>[o(r,{modelValue:ge.code,"onUpdate:modelValue":a[1]||(a[1]=e=>ge.code=e),placeholder:"请输入位置编码"},null,8,["modelValue"])])),_:1}),o(j,{label:"位置类型",prop:"type"},{default:i((()=>[o(ue,{modelValue:ge.type,"onUpdate:modelValue":a[2]||(a[2]=e=>ge.type=e),placeholder:"请选择位置类型",style:{width:"100%"}},{default:i((()=>[(h(),n(C,null,D(Ie,(e=>o(B,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),o(j,{label:"上级位置",prop:"parentId"},{default:i((()=>[o(ue,{modelValue:ge.parentId,"onUpdate:modelValue":a[3]||(a[3]=e=>ge.parentId=e),disabled:0===ge.type,clearable:"",filterable:"",placeholder:"请选择上级位置",style:{width:"100%"}},{default:i((()=>[(h(!0),n(C,null,D(be.value,(e=>(h(),_(B,{key:e.id,label:`${e.name} [${e.code}]`,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","disabled"])])),_:1}),o(j,{label:"详细地址",prop:"address"},{default:i((()=>[o(r,{modelValue:ge.address,"onUpdate:modelValue":a[4]||(a[4]=e=>ge.address=e),placeholder:"请输入详细地址"},null,8,["modelValue"])])),_:1}),o(j,{label:"备注",prop:"remark"},{default:i((()=>[o(r,{modelValue:ge.remark,"onUpdate:modelValue":a[5]||(a[5]=e=>ge.remark=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])])),_:1}),o(j,{label:"默认部门",prop:"defaultDepartmentId"},{default:i((()=>[o(ue,{modelValue:ge.defaultDepartmentId,"onUpdate:modelValue":a[6]||(a[6]=e=>ge.defaultDepartmentId=e),clearable:"",filterable:"",placeholder:"请选择默认部门",style:{width:"100%"}},{default:i((()=>[(h(!0),n(C,null,D(me.value,(e=>(h(),_(B,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),o(j,{label:"位置负责人",prop:"managerId"},{default:i((()=>[o(ue,{modelValue:ge.managerId,"onUpdate:modelValue":a[7]||(a[7]=e=>ge.managerId=e),disabled:"",placeholder:"位置负责人由部门决定",style:{width:"100%"}},{default:i((()=>[(h(!0),n(C,null,D(ve.value,(e=>(h(),_(B,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),ge.defaultDepartmentId?(h(),n("div",Z,[o(fe,null,{default:i((()=>[o(c($))])),_:1}),a[21]||(a[21]=I(" 位置负责人由部门自动决定 "))])):U("",!0)])),_:1}),o(j,{label:"使用人",prop:"usageUserIds"},{default:i((()=>[o(ue,{modelValue:ge.usageUserIds,"onUpdate:modelValue":a[8]||(a[8]=e=>ge.usageUserIds=e),multiple:"",filterable:"",placeholder:"请选择使用人",style:{width:"100%"},onVisibleChange:Ee,"value-key":"value",loading:ce.value},x({default:i((()=>[(h(!0),n(C,null,D(ye.value,(e=>(h(),_(B,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:2},[de.value?{name:"prefix",fn:i((()=>[o(fe,{class:"is-loading"},{default:i((()=>a[22]||(a[22]=[u("svg",{class:"circular",viewBox:"25 25 50 50"},[u("circle",{cx:"50",cy:"50",r:"20",fill:"none"})],-1)]))),_:1}),a[23]||(a[23]=u("span",null,"加载中...",-1))])),key:"0"}:void 0]),1032,["modelValue","loading"]),ge.usersDisplay?(h(),n("div",G,[o(fe,null,{default:i((()=>[o(c($))])),_:1}),I(" 已关联用户: "+V(ge.usersDisplay),1)])):U("",!0)])),_:1})])),_:1},8,["model"])])),_:1},8,["title","modelValue"]),o($e,{modelValue:se.value,"onUpdate:modelValue":a[13]||(a[13]=e=>se.value=e),title:"设置使用部门",width:"500px"},{footer:i((()=>[u("div",J,[o(l,{onClick:a[12]||(a[12]=e=>se.value=!1)},{default:i((()=>a[26]||(a[26]=[I("取消")]))),_:1}),o(l,{type:"primary",onClick:xe},{default:i((()=>a[27]||(a[27]=[I("确认")]))),_:1})])])),default:i((()=>[o(De,{model:re,"label-width":"100px"},{default:i((()=>[o(j,{label:"选择部门"},{default:i((()=>[o(ue,{modelValue:re.departmentId,"onUpdate:modelValue":a[11]||(a[11]=e=>re.departmentId=e),placeholder:"请选择部门",clearable:"",filterable:"",style:{width:"100%"}},{default:i((()=>[(h(!0),n(C,null,D(me.value,(e=>(h(),_(B,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),o($e,{modelValue:ne.value,"onUpdate:modelValue":a[16]||(a[16]=e=>ne.value=e),title:"设置使用人",width:"700px"},{footer:i((()=>[u("div",K,[o(l,{onClick:a[15]||(a[15]=e=>ne.value=!1)},{default:i((()=>a[28]||(a[28]=[I("取消")]))),_:1}),o(l,{type:"primary",onClick:Ae},{default:i((()=>a[29]||(a[29]=[I("确认")]))),_:1})])])),default:i((()=>[o(qe,{modelValue:ie.value,"onUpdate:modelValue":a[14]||(a[14]=e=>ie.value=e),data:oe.value,titles:["可选人员","已选人员"],props:{key:"id",label:"name"},filterable:""},null,8,["modelValue","data"])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-50a4d497"]]);export{M as default};
