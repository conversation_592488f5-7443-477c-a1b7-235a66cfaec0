/**
 * IT资产管理系统 - 班次管理API
 * 文件路径: src/api/workShift.js
 * 功能描述: 提供班次管理和任务领取相关的API服务
 */

import request from '@/utils/request'

/**
 * 班次管理相关API
 */
const workShiftApi = {
  /**
   * 获取所有班次
   * @returns {Promise}
   */
  getAllShifts() {
    return request.get('/v2/work-shifts')
  },

  /**
   * 创建班次
   * @param {object} data 班次数据
   * @returns {Promise}
   */
  createShift(data) {
    return request.post('/v2/work-shifts', data)
  },

  /**
   * 分配用户到班次
   * @param {object} data 分配数据
   * @returns {Promise}
   */
  assignUserToShift(data) {
    return request.post('/v2/work-shifts/assignments', data)
  },

  /**
   * 获取用户当前班次
   * @returns {Promise}
   */
  getUserCurrentShift() {
    return request.get('/v2/work-shifts/current')
  },

  /**
   * 获取指定用户的当前班次
   * @param {number} userId 用户ID
   * @returns {Promise}
   */
  getUserCurrentShiftById(userId) {
    return request.get(`/v2/work-shifts/users/${userId}/current`)
  },

  /**
   * 领取任务
   * @param {object} data 领取数据
   * @returns {Promise}
   */
  claimTask(data) {
    return request.post('/v2/work-shifts/claim-task', data)
  },

  /**
   * 更新任务领取状态
   * @param {number} claimId 领取记录ID
   * @param {object} data 状态数据
   * @returns {Promise}
   */
  updateClaimStatus(claimId, data) {
    return request.put(`/v2/work-shifts/claims/${claimId}/status`, data)
  },

  /**
   * 获取用户今日任务领取记录
   * @returns {Promise}
   */
  getUserTodayClaims() {
    return request.get('/v2/work-shifts/claims/today')
  },

  /**
   * 获取指定用户今日任务领取记录
   * @param {number} userId 用户ID
   * @returns {Promise}
   */
  getUserTodayClaimsById(userId) {
    return request.get(`/v2/work-shifts/users/${userId}/claims/today`)
  },

  /**
   * 获取今日班次任务统计
   * @param {object} params 查询参数
   * @returns {Promise}
   */
  getTodayShiftStatistics(params = {}) {
    return request.get('/v2/work-shifts/statistics/today', { params })
  },

  /**
   * 获取可领取的任务列表
   * @param {object} params 查询参数
   * @returns {Promise}
   */
  getAvailableTasks(params = {}) {
    // 获取状态为Todo的任务
    return request.get('/v2/tasks', { 
      params: { 
        ...params, 
        status: 'Todo',
        pageSize: 100 // 获取更多可选任务
      } 
    })
  }
}

export { workShiftApi }
export default workShiftApi
