import{bg as e,r as t,c as a}from"./index-CkwLz8y6.js";const n=[{level:1,points:0,title:"新手入门"},{level:2,points:100,title:"初级专员"},{level:3,points:300,title:"中级能手"},{level:4,points:600,title:"高级专家"},{level:5,points:1e3,title:"资深大师"}],i=e("gamification",(()=>{const e=t(0),i=t(1),c=t("新手入门"),s=t(100),l=t(0),o=t([]),u=t([]),r=t([]),v=t([]),d=t(!1),m=t(null),p=a((()=>n.find((e=>e.level===i.value))||n[0])),h=a((()=>n.find((e=>e.level===i.value+1))));async function f(){d.value=!0;try{const t={success:!0,data:{score:150,achievements:[],inventory:[]}};t.success&&t.data&&(e.value=t.data.score||0,g(e.value))}catch(t){}finally{d.value=!1}}async function y(){try{const e={success:!0,data:[{id:"ach1",name:"首次任务",description:"完成你的第一个任务",icon:"Star",achievedDate:"2024-04-01"},{id:"ach2",name:"评论达人",description:"发表 10 条评论",icon:"ChatDotRound",achievedDate:null}]};e.success&&(o.value=e.data||[])}catch(e){}}async function S(){try{const e={success:!0,data:[{id:"item1",name:"积分加速卡",description:"任务积分 +10%",icon:"MagicStick",quantity:2},{id:"item2",name:"改名卡",description:"修改一次昵称",icon:"EditPen",quantity:1}]};e.success&&(u.value=e.data||[])}catch(e){}}async function D(e=!1){var t;try{e&&m.value&&m.value;const a=(new Date).toISOString(),n={success:!0,data:[{id:"act3",text:'完成了任务 "部署测试环境"',timestamp:a,icon:"CircleCheck",type:"task_completed"},{id:"act2",text:"张三 发表了评论",timestamp:new Date(Date.now()-6e4).toISOString(),icon:"ChatDotRound",type:"comment"},{id:"act1",text:'获得了成就 "首次任务"',timestamp:new Date(Date.now()-12e4).toISOString(),icon:"Trophy",type:"achievement"}]};if(n.success&&Array.isArray(n.data)){const a=n.data;if(a.length>0){if(e){const e=new Set(r.value.map((e=>e.id))),t=a.filter((t=>!e.has(t.id)));r.value=[...t,...r.value]}else r.value=a;m.value=(null==(t=r.value[0])?void 0:t.timestamp)||m.value}r.value.length>50&&(r.value=r.value.slice(0,50))}}catch(a){}}function g(e){var t,a;let o=1,u=n[0].title,r=(null==(t=n[1])?void 0:t.points)||1/0,v=0;for(let i=0;i<n.length&&e>=n[i].points;i++)o=n[i].level,u=n[i].title,v=n[i].points,r=(null==(a=n[i+1])?void 0:a.points)||1/0;if(i.value=o,c.value=u,r===1/0)s.value=0,l.value=100;else{const t=r-v,a=e-v;s.value=r-e,l.value=t>0?Math.min(100,Math.floor(a/t*100)):100}}return{score:e,level:i,levelTitle:c,pointsToNextLevel:s,currentLevelProgress:l,achievements:o,inventory:u,recentActivities:r,leaderboard:v,isLoading:d,lastActivityTimestamp:m,currentLevelInfo:p,nextLevelInfo:h,fetchGamificationStatus:f,fetchAchievements:y,fetchInventory:S,fetchRecentActivities:D,fetchLeaderboard:async function(e="weekly"){try{const e={success:!0,data:[{userId:"user1",name:"张三",score:250,rank:1,avatar:"..."},{userId:"user3",name:"王五",score:180,rank:2,avatar:"..."},{userId:"user2",name:"李四",score:120,rank:3,avatar:"..."}]};e.success&&(v.value=e.data||[])}catch(t){}},recordEvent:async function(t,a={}){try{if("task_completed"===t){const n=a.points||20;e.value+=n,g(e.value),r.value.unshift({id:`act_${Date.now()}`,text:`完成了任务获得了 ${n} 积分`,timestamp:(new Date).toISOString(),icon:"CircleCheck",type:t})}if("comment_added"===t){const n=a.points||2;e.value+=n,g(e.value),r.value.unshift({id:`act_${Date.now()}`,text:`发表评论获得了 ${n} 积分`,timestamp:(new Date).toISOString(),icon:"ChatDotRound",type:t})}}catch(n){}},initializeStore:function(){f(),y(),S(),D()}}}));export{i as u};
