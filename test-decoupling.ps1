# 基础解耦测试脚本
# 用于验证新接口功能和性能

Write-Host "🚀 开始基础解耦测试..." -ForegroundColor Green

# 配置
$baseUrl = "http://localhost:5001"
$v1Url = "$baseUrl/api/user"
$v11Url = "$baseUrl/api/v1.1/user"
$monitoringUrl = "$baseUrl/api/monitoring"

# 测试数据
$loginData = @{
    username = "admin"
    password = "123456"
} | ConvertTo-Json

Write-Host "`n📋 测试计划:" -ForegroundColor Yellow
Write-Host "1. 测试原有V1用户登录API"
Write-Host "2. 测试新V1.1用户登录API"
Write-Host "3. 对比性能数据"
Write-Host "4. 验证功能一致性"

# 函数：发送HTTP请求
function Invoke-ApiTest {
    param(
        [string]$Url,
        [string]$Method = "GET",
        [string]$Body = $null,
        [hashtable]$Headers = @{}
    )
    
    try {
        $params = @{
            Uri = $Url
            Method = $Method
            ContentType = "application/json"
            Headers = $Headers
        }
        
        if ($Body) {
            $params.Body = $Body
        }
        
        $response = Invoke-RestMethod @params
        return @{
            Success = $true
            Data = $response
            StatusCode = 200
        }
    }
    catch {
        return @{
            Success = $false
            Error = $_.Exception.Message
            StatusCode = $_.Exception.Response.StatusCode.value__
        }
    }
}

# 函数：测试登录API
function Test-LoginApi {
    param(
        [string]$ApiUrl,
        [string]$Version
    )
    
    Write-Host "`n🔐 测试 $Version 登录API: $ApiUrl" -ForegroundColor Cyan
    
    $startTime = Get-Date
    $result = Invoke-ApiTest -Url "$ApiUrl/login" -Method "POST" -Body $loginData
    $endTime = Get-Date
    $duration = ($endTime - $startTime).TotalMilliseconds
    
    if ($result.Success) {
        Write-Host "✅ $Version 登录成功" -ForegroundColor Green
        Write-Host "   响应时间: $([math]::Round($duration, 2))ms" -ForegroundColor Gray
        Write-Host "   Token: $($result.Data.data.token.Substring(0, 20))..." -ForegroundColor Gray
        Write-Host "   用户: $($result.Data.data.user.username) ($($result.Data.data.user.name))" -ForegroundColor Gray
        
        if ($result.Data.version) {
            Write-Host "   API版本: $($result.Data.version)" -ForegroundColor Gray
        }
        
        return @{
            Success = $true
            Token = $result.Data.data.token
            Duration = $duration
            User = $result.Data.data.user
        }
    }
    else {
        Write-Host "❌ $Version 登录失败: $($result.Error)" -ForegroundColor Red
        return @{
            Success = $false
            Error = $result.Error
            Duration = $duration
        }
    }
}

# 函数：测试用户信息获取
function Test-GetUserApi {
    param(
        [string]$ApiUrl,
        [string]$Version,
        [string]$Token,
        [int]$UserId
    )
    
    Write-Host "`n👤 测试 $Version 获取用户信息API" -ForegroundColor Cyan
    
    $headers = @{
        "Authorization" = "Bearer $Token"
    }
    
    $startTime = Get-Date
    $result = Invoke-ApiTest -Url "$ApiUrl/$UserId" -Headers $headers
    $endTime = Get-Date
    $duration = ($endTime - $startTime).TotalMilliseconds
    
    if ($result.Success) {
        Write-Host "✅ $Version 获取用户信息成功" -ForegroundColor Green
        Write-Host "   响应时间: $([math]::Round($duration, 2))ms" -ForegroundColor Gray
        Write-Host "   用户ID: $($result.Data.data.id)" -ForegroundColor Gray
        Write-Host "   用户名: $($result.Data.data.username)" -ForegroundColor Gray
        
        return @{
            Success = $true
            Duration = $duration
            User = $result.Data.data
        }
    }
    else {
        Write-Host "❌ $Version 获取用户信息失败: $($result.Error)" -ForegroundColor Red
        return @{
            Success = $false
            Error = $result.Error
            Duration = $duration
        }
    }
}

# 函数：获取性能报告
function Get-PerformanceReport {
    param([string]$OperationName)
    
    Write-Host "`n📊 获取性能报告: $OperationName" -ForegroundColor Cyan
    
    $result = Invoke-ApiTest -Url "$monitoringUrl/performance/$OperationName"
    
    if ($result.Success -and $result.Data.data.VersionReports) {
        Write-Host "✅ 性能报告获取成功" -ForegroundColor Green
        
        foreach ($versionReport in $result.Data.data.VersionReports) {
            Write-Host "   版本: $($versionReport.Version)" -ForegroundColor Yellow
            Write-Host "     总请求数: $($versionReport.TotalRequests)" -ForegroundColor Gray
            Write-Host "     成功率: $([math]::Round($versionReport.SuccessRate, 2))%" -ForegroundColor Gray
            Write-Host "     平均响应时间: $([math]::Round($versionReport.AverageDuration, 2))ms" -ForegroundColor Gray
            Write-Host "     性能比率: $([math]::Round($versionReport.PerformanceRatio, 2))" -ForegroundColor Gray
        }
    }
    else {
        Write-Host "⚠️ 性能报告暂无数据或获取失败" -ForegroundColor Yellow
    }
}

# 开始测试
Write-Host "`n🧪 开始执行测试..." -ForegroundColor Green

# 测试V1 API
$v1LoginResult = Test-LoginApi -ApiUrl $v1Url -Version "V1"

# 测试V1.1 API
$v11LoginResult = Test-LoginApi -ApiUrl $v11Url -Version "V1.1"

# 如果登录成功，测试获取用户信息
if ($v1LoginResult.Success -and $v1LoginResult.User) {
    $v1GetUserResult = Test-GetUserApi -ApiUrl $v1Url -Version "V1" -Token $v1LoginResult.Token -UserId $v1LoginResult.User.id
}

if ($v11LoginResult.Success -and $v11LoginResult.User) {
    $v11GetUserResult = Test-GetUserApi -ApiUrl $v11Url -Version "V1.1" -Token $v11LoginResult.Token -UserId $v11LoginResult.User.id
}

# 等待一下让性能数据收集完成
Start-Sleep -Seconds 2

# 获取性能报告
Get-PerformanceReport -OperationName "UserAuthenticate"
Get-PerformanceReport -OperationName "UserGetById"

# 测试总结
Write-Host "`n📋 测试总结:" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Gray

if ($v1LoginResult.Success -and $v11LoginResult.Success) {
    Write-Host "✅ 功能一致性: 通过" -ForegroundColor Green
    Write-Host "   V1登录响应时间: $([math]::Round($v1LoginResult.Duration, 2))ms" -ForegroundColor Gray
    Write-Host "   V1.1登录响应时间: $([math]::Round($v11LoginResult.Duration, 2))ms" -ForegroundColor Gray
    
    $performanceDiff = $v11LoginResult.Duration - $v1LoginResult.Duration
    if ($performanceDiff -le 50) {
        Write-Host "✅ 性能表现: 良好 (差异: $([math]::Round($performanceDiff, 2))ms)" -ForegroundColor Green
    }
    elseif ($performanceDiff -le 100) {
        Write-Host "⚠️ 性能表现: 可接受 (差异: $([math]::Round($performanceDiff, 2))ms)" -ForegroundColor Yellow
    }
    else {
        Write-Host "❌ 性能表现: 需要优化 (差异: $([math]::Round($performanceDiff, 2))ms)" -ForegroundColor Red
    }
}
else {
    Write-Host "❌ 功能一致性: 失败" -ForegroundColor Red
}

Write-Host "`n🎯 基础解耦测试完成!" -ForegroundColor Green
Write-Host "建议: 如果测试通过，可以继续进行资产服务接口化" -ForegroundColor Cyan
