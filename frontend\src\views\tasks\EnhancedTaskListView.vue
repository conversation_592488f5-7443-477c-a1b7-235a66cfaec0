<template>
  <div class="enhanced-task-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>📋 任务管理中心</h1>
        <p class="subtitle">高效管理，轻松协作</p>
      </div>
      <div class="header-right">

        <QuickTaskCreator
          trigger-text="⚡ 快速创建"
          trigger-class="quick-create-btn"
          @created="onTaskCreated"
          @expand-to-full-form="handleExpandToFullForm"
        />
        <el-button type="primary" @click="showCreateFormDialog">
          <el-icon><DocumentAdd /></el-icon>
          详细创建
        </el-button>
      </div>
    </div>

    <!-- 智能过滤器 -->
    <el-card class="filter-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>🔍 智能筛选</span>
          <el-button link @click="resetFilters" size="small">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>
      </template>
      
      <div class="filter-grid">
        <!-- 状态 -->
        <el-select v-model="filters.status" placeholder="任务状态" clearable class="filter-item">
          <el-option label="全部状态" value="" />
          <el-option label="📋 待处理" value="Todo" />
          <el-option label="🔄 进行中" value="InProgress" />
          <el-option label="✅ 已完成" value="Done" />
          <el-option label="❌ 已取消" value="Cancelled" />
        </el-select>
        <!-- 优先级 -->
        <el-select v-model="filters.priority" placeholder="优先级" clearable class="filter-item">
          <el-option label="全部优先级" value="" />
          <el-option label="🔴 高优先级" value="High" />
          <el-option label="🟡 中优先级" value="Medium" />
          <el-option label="🟢 低优先级" value="Low" />
        </el-select>
        <!-- 负责人多选+头像 -->
        <el-select v-model="filters.assigneeIds" multiple placeholder="负责人" clearable filterable class="filter-item" collapse-tags collapse-tags-tooltip>
          <el-option
            v-for="user in users"
            :key="user.id"
            :label="user.name"
            :value="user.id"
          >
            <el-avatar :src="user.avatar" :size="18" style="margin-right:6px" />
            <span>{{ user.name }}</span>
            <span class="user-dept">{{ user.department }}</span>
          </el-option>
        </el-select>
        <!-- 搜索 -->
        <el-input
          v-model="searchQuery"
          placeholder="🔍 搜索任务标题、描述..."
          clearable
          class="search-input"
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <!-- 日期 -->
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="date-picker"
          @change="handleDateRangeChange"
        />
        <!-- 搜索按钮 -->
        <el-button type="primary" @click="handleSearch" :loading="loading" class="search-btn">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card total" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon">📊</div>
          <div class="stat-info">
            <div class="stat-number">{{ taskStats.total }}</div>
            <div class="stat-label">总任务</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card progress" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon">🔄</div>
          <div class="stat-info">
            <div class="stat-number">{{ taskStats.inProgress }}</div>
            <div class="stat-label">进行中</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card completed" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon">✅</div>
          <div class="stat-info">
            <div class="stat-number">{{ taskStats.completed }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card overdue" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon">⏰</div>
          <div class="stat-info">
            <div class="stat-number">{{ taskStats.overdue }}</div>
            <div class="stat-label">已逾期</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 批量操作栏 -->
    <div v-if="selectedTasks.length > 0" class="batch-actions-bar">
      <div class="batch-info">
        <span>已选择 {{ selectedTasks.length }} 个任务</span>
        <el-button link @click="clearSelection">取消选择</el-button>
      </div>
      <div class="batch-actions">
        <el-button size="small" @click="showBatchAssignDialog = true">
          <el-icon><User /></el-icon>
          批量分配
        </el-button>
        <el-button size="small" @click="showBatchStatusDialog = true">
          <el-icon><Edit /></el-icon>
          批量修改状态
        </el-button>
        <el-button size="small" type="danger" @click="batchDeleteTasks">
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 增强任务表格 -->
    <el-card class="table-card" shadow="hover">
      <template #header>
        <div class="table-header">
          <span>📋 任务列表 ({{ filteredTasks.length }})</span>
          <div class="table-actions">
            <el-button-group size="small">
              <el-button 
                :type="viewMode === 'table' ? 'primary' : ''"
                @click="viewMode = 'table'"
              >
                <el-icon><Grid /></el-icon>
                表格
              </el-button>
              <el-button 
                :type="viewMode === 'card' ? 'primary' : ''"
                @click="viewMode = 'card'"
              >
                <el-icon><Collection /></el-icon>
                卡片
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'">
        <el-table
          :data="tasksWithAvatar"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          row-key="taskId"
          class="enhanced-table"
        >
          <el-table-column type="selection" width="50" />
          
          <el-table-column label="优先级" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getPriorityTagType(row.priority)" size="small">
                {{ getPriorityIcon(row.priority) }} {{ getPriorityText(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="任务名称" min-width="200">
            <template #default="{ row }">
              <div class="task-name-cell" @click="showTaskDetail(row)">
                <div class="task-content">
                  <span class="task-title">{{ row.name }}</span>
                  <div v-if="row.description" class="task-desc">{{ truncateText(row.description, 50) }}</div>
                </div>
                <!-- 完成水印 - 透明背景覆盖效果 -->
                <div
                  v-if="row.status === 'Done' && row.completedByUserName"
                  class="completion-watermark"
                  :style="{ '--watermark-color': row.completionWatermarkColor || '#409EFF' }"
                >
                  <span class="watermark-text">{{ row.completedByUserName }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="负责人" width="200">
            <template #default="{ row }">
              <div class="assignee-cell">
                <UserAvatarStack
                  :users="getAllAssignees(row)"
                  :is-main-user-primary="true"
                  :max-users="4"
                  avatar-size="6"
                  :overlap="-10"
                  class="small"
                />
                
                <span v-if="!row.assigneeUserId" class="no-assignee">未分配</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="状态" width="120" align="center">
            <template #default="{ row }">
              <div class="status-cell">
                <el-select
                  :model-value="row.status"
                  @change="(value) => handleStatusChange(row, value)"
                  size="small"
                  class="status-select"
                >
                  <el-option label="待处理" value="Todo" />
                  <el-option label="进行中" value="InProgress" />
                  <el-option label="已完成" value="Done" />
                  <el-option label="已取消" value="Canceled" />
                </el-select>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="开始日期" width="120">
            <template #default="{ row }">
              <div class="date-cell">
                <span v-if="row.planStartDate" class="start-date">
                  {{ formatDate(row.planStartDate) }}
                </span>
                <span v-else class="no-date">未设置</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="截止日期" width="150">
            <template #default="{ row }">
              <div v-if="row.planEndDate" class="date-cell">
                <div class="due-date-container">
                  <span :class="{ 'overdue': isOverdue(row), 'due-soon': isDueSoon(row) }">
                    {{ formatDate(row.planEndDate) }}
                  </span>
                  <div v-if="isOverdue(row)" class="overdue-warning">
                    <el-text type="danger" size="small">
                      已逾期 {{ getOverdueDays(row) }} 天
                    </el-text>
                  </div>
                </div>
              </div>
              <span v-else class="no-date">未设置</span>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="220" align="center">
            <template #default="{ row }">
              <div class="action-buttons">
                <!-- 游戏化任务领取按钮 -->
                <el-button
                  v-if="canClaimTask(row)"
                  type="success"
                  size="small"
                  @click="claimTask(row)"
                  :loading="row.claiming"
                  class="claim-button"
                >
                  <el-icon><Trophy /></el-icon>
                  领取
                </el-button>

                <!-- 已领取标识 -->
                <el-tag
                  v-else-if="isTaskClaimed(row)"
                  type="info"
                  size="small"
                  class="claimed-tag"
                >
                  <el-icon><User /></el-icon>
                  已领取
                </el-tag>

                <!-- 常规操作按钮 -->
                <el-button link size="small" @click="showTaskDetail(row)">
                  <el-icon><View /></el-icon>
                </el-button>
                <el-button link size="small" @click="editTask(row)">
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-dropdown @command="(command) => handleQuickAction(command, row)">
                  <el-button link size="small">
                    <el-icon><More /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="clone">克隆任务</el-dropdown-item>
                      <el-dropdown-item command="assign">重新分配</el-dropdown-item>
                      <el-dropdown-item command="complete" v-if="row.status !== 'Done'">标记完成</el-dropdown-item>
                      <el-dropdown-item command="delete" divided>删除任务</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <div class="task-cards-grid">
          <EnhancedTaskCard
            v-for="task in paginatedTasks"
            :key="task.taskId"
            :task="task"
            :selected="selectedTaskIds.includes(task.taskId)"
            @select="toggleTaskSelection"
            @click="showTaskDetail"
            @quick-action="handleTaskQuickAction"
            @status-change="handleQuickStatusChange"
          />
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :small="false"
          :disabled="loading"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="filteredTasks.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 对话框：任务详情 -->
    <TaskDetailDialog
      v-model="showDetailDialog"
      :task="currentTask"
      @close="showDetailDialog = false"
      @updated="onTaskUpdated"
    />

    <!-- 对话框：批量分配 -->
    <BatchAssignDialog
      v-model="showBatchAssignDialog"
      :task-ids="selectedTaskIds"
      @close="showBatchAssignDialog = false"
      @assigned="onBatchAssigned"
    />

    <!-- 对话框：批量状态修改 -->
    <BatchStatusDialog
      v-model="showBatchStatusDialog"
      :task-ids="selectedTaskIds"
      @close="showBatchStatusDialog = false"
      @updated="onBatchUpdated"
    />

    <!-- 对话框：任务表单 -->
    <TaskFormDialog
      v-model:visible="showCreateDialog"
      :isEdit="isEditMode"
      :formData="editingTask"
      @close="closeTaskForm"
      @submit="onTaskFormSubmit"
    />

    <!-- 对话框：任务领取 -->
    <TaskClaimDialog
      v-model="showClaimDialog"
      @success="onTaskClaimed"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  DocumentAdd, Refresh, Search, User, Edit, Delete, Grid, Collection,
  View, More, Trophy
} from '@element-plus/icons-vue'

// 组件导入
import QuickTaskCreator from '@/components/Tasks/QuickTaskCreatorSimple.vue'
import EnhancedTaskCard from '@/components/Tasks/EnhancedTaskCard.vue'
import UserAvatar from '@/components/UserAvatar.vue'
import UserAvatarStack from '@/components/UserAvatarStack.vue'
import TaskDetailDialog from './components/TaskDetailDialog.vue'
import BatchAssignDialog from './components/BatchAssignDialog.vue'
import BatchStatusDialog from './components/BatchStatusDialog.vue'
import TaskFormDialog from './components/TaskFormDialog.vue'
import TaskClaimDialog from '@/components/Tasks/TaskClaimDialog.vue'

// Store 和 API
import { useTaskEnhancedStore } from '@/stores/modules/taskEnhanced'
import { taskApi } from '@/api/task'
import userApi from '@/api/user'
import { getFullAvatarUrl } from '@/stores/modules/user'

// 响应式数据
const taskStore = useTaskEnhancedStore()
const loading = ref(false)
const viewMode = ref('table') // 'table' | 'card'
const users = ref([])
const searchQuery = ref('')
const dateRange = ref([])
const selectedTasks = ref([])
const editingTask = ref(null)
const isEditMode = ref(false)
const todayViewedCount = ref(0)
const currentUser = ref(null)

// 过滤器
const filters = reactive({
  status: '',
  priority: '',
  assigneeIds: [] // 多选负责人
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 20
})

// 对话框状态
const showDetailDialog = ref(false)
const showCreateDialog = ref(false)
const showBatchAssignDialog = ref(false)
const showBatchStatusDialog = ref(false)
const showClaimDialog = ref(false)
const currentTask = ref(null)

// 计算属性
const filteredTasks = computed(() => {
  let tasks = taskStore.tasks || []
  
  // 应用状态过滤
  if (filters.status) {
    tasks = tasks.filter(task => task.status === filters.status)
  }
  
  // 应用优先级过滤
  if (filters.priority) {
    tasks = tasks.filter(task => task.priority === filters.priority)
  }
  
  // 应用负责人多选过滤
  if (filters.assigneeIds && filters.assigneeIds.length > 0) {
    tasks = tasks.filter(task => filters.assigneeIds.includes(task.assigneeUserId))
  }
  
  // 应用搜索
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    tasks = tasks.filter(task => 
      task.name?.toLowerCase().includes(query) ||
      task.description?.toLowerCase().includes(query)
    )
  }
  
  // 应用日期范围
  if (dateRange.value && dateRange.value.length === 2) {
    const [start, end] = dateRange.value
    tasks = tasks.filter(task => {
      if (!task.planEndDate) return false
      const taskDate = new Date(task.planEndDate)
      return taskDate >= start && taskDate <= end
    })
  }
  
  return tasks
})

const paginatedTasks = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return filteredTasks.value.slice(start, end)
})

const taskStats = computed(() => {
  const tasks = filteredTasks.value
  return {
    total: tasks.length,
    inProgress: tasks.filter(t => t.status === 'InProgress').length,
    completed: tasks.filter(t => t.status === 'Done').length,
    overdue: tasks.filter(t => isOverdue(t)).length
  }
})

const selectedTaskIds = computed(() => selectedTasks.value.map(t => t.taskId))

// 在 paginatedTasks 计算属性后添加一个映射，补全 assigneeAvatarUrl 字段
const tasksWithAvatar = computed(() => {
  return paginatedTasks.value.map(row => {
    // 防御性编程，确保数据安全
    if (!row) return row
    
    const user = users.value.find(u => u && u.id === row.assigneeUserId)
    return {
      ...row,
      assigneeUserName: user?.name || row.assigneeUserName || '未知用户',
      assigneeAvatarUrl: getFullAvatarUrl(row.assigneeAvatarUrl || user?.avatarUrl || user?.avatar || ''),
    }
  }).filter(Boolean) // 过滤掉空值
})

// 方法
const loadTasks = async (forceRefresh = false) => {
  loading.value = true
  try {
    await taskStore.fetchTasks({
      status: filters.status,
      priority: filters.priority,
      assigneeIds: filters.assigneeIds,
      search: searchQuery.value,
      pageNumber: 1,
      pageSize: 1000, // 获取更多数据用于前端分页
      forceRefresh: forceRefresh // 添加强制刷新参数
    })
  } catch (error) {
    ElMessage.error('加载任务列表失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const loadUsers = async () => {
  try {
    const response = await userApi.getUserList()
    if (response && response.data) {
      users.value = response.data.map(user => {
        if (!user) return null
        return {
          id: user.id || user.userId || user.ID || 0,
          name: user.name || user.userName || user.username || user.displayName || '未知用户',
          department: user.department || user.departmentName || '',
          avatar: user.avatar || user.avatarUrl || ''
        }
      }).filter(user => user && user.id > 0)
    } else {
      users.value = []
    }
  } catch (error) {
    console.error('加载用户列表失败:', error)
    users.value = []
  }
}

const loadCurrentUser = async () => {
  try {
    const response = await userApi.getCurrentUser()
    if (response && response.data) {
      currentUser.value = {
        id: response.data.id || response.data.userId || response.data.ID,
        name: response.data.name || response.data.userName || response.data.username || response.data.displayName,
        department: response.data.department || response.data.departmentName || '',
        avatar: response.data.avatar || response.data.avatarUrl || ''
      }
    }
  } catch (error) {
    console.error('获取当前用户信息失败:', error)
    // 从localStorage获取用户信息作为备选
    const userInfo = localStorage.getItem('userInfo')
    if (userInfo) {
      try {
        const parsed = JSON.parse(userInfo)
        currentUser.value = {
          id: parsed.id || parsed.userId || parsed.ID,
          name: parsed.name || parsed.userName || parsed.username || parsed.displayName,
          department: parsed.department || parsed.departmentName || '',
          avatar: parsed.avatar || parsed.avatarUrl || ''
        }
      } catch (e) {
        console.error('解析本地用户信息失败:', e)
      }
    }
  }
}

const handleSearch = () => {
  pagination.currentPage = 1
  loadTasks()
}

const resetFilters = () => {
  Object.assign(filters, { status: '', priority: '', assigneeIds: [] })
  searchQuery.value = ''
  dateRange.value = []
  pagination.currentPage = 1
  loadTasks()
}

const handleDateRangeChange = () => {
  handleSearch()
}

const handleSelectionChange = (selection) => {
  selectedTasks.value = selection
}

const clearSelection = () => {
  selectedTasks.value = []
}

const toggleTaskSelection = (taskId, selected) => {
  if (selected) {
    const task = filteredTasks.value.find(t => t.taskId === taskId)
    if (task && !selectedTasks.value.find(t => t.taskId === taskId)) {
      selectedTasks.value.push(task)
    }
  } else {
    selectedTasks.value = selectedTasks.value.filter(t => t.taskId !== taskId)
  }
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.currentPage = 1
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
}

// 任务操作
const showTaskDetail = (task) => {
  currentTask.value = task
  showDetailDialog.value = true
  recordTaskView(task.taskId)
}

const editTask = async (task) => {
  try {
    // 获取完整的任务详情，包括所有负责人和协作者信息
    console.log('开始编辑任务，获取完整详情:', task.taskId)
    const response = await taskApi.getTaskDetail(task.taskId)
    
    if (response.success && response.data) {
      console.log('获取到完整任务详情:', response.data)
      editingTask.value = response.data
      isEditMode.value = true
      showCreateDialog.value = true
    } else {
      ElMessage.error('获取任务详情失败: ' + (response.message || '未知错误'))
    }
  } catch (error) {
    console.error('获取任务详情出错:', error)
    ElMessage.error('获取任务详情时发生错误')
  }
}

const showCreateFormDialog = () => {
  editingTask.value = null
  isEditMode.value = false
  showCreateDialog.value = true
}

const navigateToCreateTask = () => {
  showCreateFormDialog()
}

const closeTaskForm = () => {
  showCreateDialog.value = false
  editingTask.value = null
  isEditMode.value = false
}

const onTaskCreated = (newTask) => {
  ElMessage.success('任务创建成功!')
  loadTasks(true) // 强制刷新
}

const onTaskUpdated = () => {
  loadTasks(true) // 强制刷新，确保获取最新数据
}

const handleExpandToFullForm = (formData) => {
  editingTask.value = formData
  isEditMode.value = false
  showCreateDialog.value = true
}

const handleQuickAction = async (command, task) => {
  switch (command) {
    case 'clone':
      await cloneTask(task)
      break
    case 'assign':
      selectedTasks.value = [task]
      showBatchAssignDialog.value = true
      break
    case 'complete':
      await completeTask(task)
      break
    case 'delete':
      await deleteTask(task)
      break
  }
}

const handleTaskQuickAction = (payload) => {
  handleQuickAction(payload.action, payload.task)
}

const handleQuickStatusChange = async (payload) => {
  try {
    await taskStore.updateTaskStatus(payload.taskId, payload.newStatus)
    ElMessage.success('任务状态更新成功')
    loadTasks(true) // 强制刷新
  } catch (error) {
    ElMessage.error('状态更新失败: ' + error.message)
  }
}

const cloneTask = async (task) => {
  try {
    const clonedTask = {
      ...task,
      name: `${task.name} (副本)`,
      status: 'Todo',
      progress: 0
    }
    delete clonedTask.taskId
    
    await taskStore.createTask(clonedTask)
    ElMessage.success('任务克隆成功')
    loadTasks(true) // 强制刷新
  } catch (error) {
    ElMessage.error('任务克隆失败: ' + error.message)
  }
}

const completeTask = async (task) => {
  try {
    // 使用专门的完成任务API，这样会自动设置完成用户和水印
    const response = await taskApi.completeTask(task.taskId, {
      remarks: `任务完成操作`
    })

    if (response && response.success) {
      ElMessage.success('任务已标记为完成')
      loadTasks(true) // 强制刷新以显示水印
    } else {
      ElMessage.error(response?.message || '任务完成失败')
    }
  } catch (error) {
    console.error('完成任务失败:', error)
    ElMessage.error('操作失败: ' + error.message)
  }
}

const deleteTask = async (task) => {
  try {
    await ElMessageBox.confirm(`确定要删除任务"${task.name}"吗？`, '确认删除', {
      type: 'warning'
    })
    await taskStore.deleteTask(task.taskId)
    ElMessage.success('任务删除成功')
    loadTasks(true) // 强制刷新
  } catch (error) {
    if (error?.response?.status === 404) {
      ElMessage.error('任务已被删除或不存在')
      loadTasks(true) // 强制刷新
    } else if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

// 批量操作
const batchDeleteTasks = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedTasks.value.length} 个任务吗？`, 
      '确认批量删除', 
      { type: 'warning' }
    )
    await taskStore.batchDeleteTasks(selectedTaskIds.value)
    ElMessage.success('批量删除成功')
    clearSelection()
    loadTasks()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败: ' + error.message)
    }
  }
}

const onBatchAssigned = () => {
  ElMessage.success('批量分配成功')
  clearSelection()
  loadTasks(true) // 强制刷新，确保显示最新的负责人分配
}

const onBatchUpdated = () => {
  ElMessage.success('批量状态修改成功')
  clearSelection()
  loadTasks(true) // 强制刷新，确保显示最新的状态更新
}

// 工具方法
const truncateText = (text, length) => {
  if (!text || text.length <= length) return text
  return text.substring(0, length) + '...'
}

const getCollaborators = (task) => {
  if (!task) return []
  return task.collaborators || task.assignees?.filter(a => a && a.role !== 'Primary') || task.participants || []
}

const getPriorityTagType = (priority) => {
  const types = { High: 'danger', Medium: 'warning', Low: 'info' }
  return types[priority] || 'info'
}

const getPriorityIcon = (priority) => {
  const icons = { High: '🔴', Medium: '🟡', Low: '🟢' }
  return icons[priority] || '⚪'
}

const getPriorityText = (priority) => {
  const texts = { High: '高', Medium: '中', Low: '低' }
  return texts[priority] || priority
}

const getStatusTagType = (status) => {
  const types = { Todo: 'info', InProgress: 'warning', Done: 'success', Cancelled: 'danger' }
  return types[status] || 'info'
}

const getStatusIcon = (status) => {
  const icons = { Todo: '📋', InProgress: '🔄', Done: '✅', Cancelled: '❌' }
  return icons[status] || '❓'
}

const getStatusText = (status) => {
  const texts = { Todo: '待处理', InProgress: '进行中', Done: '已完成', Cancelled: '已取消' }
  return texts[status] || status
}

const getProgressColor = (progress) => {
  if (progress >= 80) return '#67c23a'
  if (progress >= 50) return '#e6a23c'
  if (progress >= 20) return '#409eff'
  return '#f56c6c'
}

const isOverdue = (task) => {
  if (!task.planEndDate || task.status === 'Done') return false
  return new Date(task.planEndDate) < new Date()
}

const isDueSoon = (task) => {
  if (!task.planEndDate || task.status === 'Done') return false
  const dueDate = new Date(task.planEndDate)
  const today = new Date()
  const diffDays = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24))
  return diffDays <= 2 && diffDays >= 0
}

const formatRelativeDate = (date) => {
  if (!date) return ''
  const d = new Date(date)
  const now = new Date()
  const diffDays = Math.ceil((d - now) / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) return `${Math.abs(diffDays)}天前`
  if (diffDays === 0) return '今天'
  if (diffDays === 1) return '明天'
  if (diffDays <= 7) return `${diffDays}天后`
  return d.toLocaleDateString()
}

const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('zh-CN')
}

const getOverdueDays = (task) => {
  if (!task.planEndDate) return 0
  const dueDate = new Date(task.planEndDate)
  const today = new Date()
  return Math.floor((today - dueDate) / (1000 * 60 * 60 * 24))
}

const handleStatusChange = async (task, newStatus) => {
  try {
    loading.value = true
    await taskStore.updateTask({
      taskId: task.taskId,
      status: newStatus
    })
    // 更新本地数据
    task.status = newStatus
    ElMessage.success('状态更新成功')
  } catch (error) {
    ElMessage.error('状态更新失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const getUserById = (id) => users.value.find(u => u.id === id)

// 生命周期
onMounted(() => {
  loadTasks()
  loadUsers()
  loadCurrentUser()
  loadTodayViewedCount()
})

// 实现 onTaskFormSubmit 方法，调用后端创建/编辑接口，成功后关闭弹窗并刷新任务列表。
const onTaskFormSubmit = async (taskData) => {
  try {
    if (isEditMode.value) {
      // 确保在更新时传递正确的taskId
      const taskToUpdate = {
        ...taskData,
        taskId: editingTask.value.taskId
      }
      console.log('编辑任务，提交数据:', taskToUpdate)
      await taskStore.updateTask(taskToUpdate)
      ElMessage.success('任务更新成功!')
    } else {
      console.log('创建任务，提交数据:', taskData)
      await taskStore.createTask(taskData)
      ElMessage.success('任务创建成功!')
    }
    closeTaskForm()
    
    // 重新加载任务列表以更新头像显示
    console.log('任务操作完成，重新加载任务列表')
    await loadTasks()
    
  } catch (error) {
    console.error('任务操作失败:', error)
    ElMessage.error('操作失败: ' + (error.message || '未知错误'))
  }
}

async function recordTaskView(taskId) {
  await taskApi.recordTaskView(taskId)
  await loadTodayViewedCount()
}

async function loadTodayViewedCount() {
  const res = await taskApi.getTodayViewedCount()
  todayViewedCount.value = res.data?.count || 0
}

// 游戏化任务领取相关方法
const canClaimTask = (task) => {
  // 任务可以被领取的条件：
  // 1. 任务状态为待办(Todo)
  // 2. 任务没有被分配给任何人，或者分配给当前用户
  // 3. 任务没有被其他人领取
  return task.status === 'Todo' &&
         (!task.assigneeUserId || task.assigneeUserId === currentUser.value?.id) &&
         !task.claimedByUserId
}

const isTaskClaimed = (task) => {
  // 检查任务是否已被领取
  return task.claimedByUserId && task.claimedByUserId !== currentUser.value?.id
}

const claimTask = async (task) => {
  if (!canClaimTask(task)) {
    ElMessage.warning('该任务无法领取')
    return
  }

  try {
    // 设置领取状态
    task.claiming = true

    // 调用任务领取API
    const response = await taskApi.claimTask(task.taskId, {
      notes: `用户主动领取任务: ${task.name}`
    })

    console.log('任务领取API响应:', response)

    if (response && response.success) {
      // 更新任务状态
      task.claimedByUserId = currentUser.value?.id
      task.claimedByUserName = currentUser.value?.name
      task.assigneeUserId = currentUser.value?.id
      task.status = 'InProgress' // 领取后自动变为进行中

      // 显示成功消息
      ElMessage.success({
        message: `🎉 成功领取任务: ${task.name}`,
        duration: 3000,
        showClose: true
      })

      // 刷新任务列表
      loadTasks(true)
    } else {
      ElMessage.error(response?.message || response?.error || '任务领取失败')
    }
  } catch (error) {
    console.error('任务领取失败:', error)
    ElMessage.error('任务领取失败，请稍后重试')
  } finally {
    task.claiming = false
  }
}

// 获取所有负责人和协作者 - 按照用户建议的清晰逻辑重写
const getAllAssignees = (task) => {
  if (!task) return []
  
  console.log('getAllAssignees - 原始task数据:', {
    taskId: task.taskId,
    assigneeUserId: task.assigneeUserId,
    assigneeUserName: task.assigneeUserName,
    assignees: task.assignees,
    participants: task.participants
  })
  
  const assignees = []
  
  // 1. 首先添加主负责人 (来自tasks表的AssigneeUserId)
  if (task.assigneeUserId) {
    assignees.push({
      id: task.assigneeUserId,
      name: task.assigneeUserName || '未知用户',
      avatarUrl: getFullAvatarUrl(task.assigneeAvatarUrl || ''),
      role: 'Primary',
      isPrimary: true
    })
    console.log('添加主负责人:', {
      id: task.assigneeUserId,
      name: task.assigneeUserName,
      avatarUrl: task.assigneeAvatarUrl
    })
  }
  
  // 2. 然后添加协作者 (来自taskassignees表, 通过assignees字段)
  if (task.assignees && Array.isArray(task.assignees) && task.assignees.length > 0) {
    task.assignees.forEach(assignee => {
      // 防止重复添加主负责人
      if (assignee.userId !== task.assigneeUserId) {
        assignees.push({
          id: assignee.userId,
          name: assignee.userName || '未知用户',
          avatarUrl: getFullAvatarUrl(assignee.avatarUrl || ''),
          role: 'Collaborator',
          isPrimary: false
        })
        console.log('添加协作者:', {
          id: assignee.userId,
          name: assignee.userName,
          avatarUrl: assignee.avatarUrl
        })
      }
    })
  }
  
  
  return assignees
}
</script>

<style scoped>
.enhanced-task-list {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
}

.subtitle {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.filter-card {
  margin-bottom: 12px;
  padding: 12px 16px 8px 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
  padding: 0;
}

.filter-item {
  min-width: 140px;
  max-width: 180px;
  flex: 0 0 160px;
}

.search-input {
  min-width: 180px;
  max-width: 220px;
  flex: 1 1 200px;
}

.date-picker {
  min-width: 180px;
  max-width: 220px;
  flex: 1 1 200px;
}

.search-btn {
  min-width: 80px;
  padding: 0 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.batch-actions-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 16px 24px;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #e4e7ed;
}

.batch-info {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #606266;
}

.batch-actions {
  display: flex;
  gap: 8px;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.enhanced-table {
  border-radius: 8px;
  overflow: hidden;
}

.task-name-cell {
  cursor: pointer;
}

.task-title {
  font-weight: 600;
  color: #409eff;
  margin-bottom: 4px;
}

.task-desc {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.assignee-cell {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
}

.main-assignee {
  display: flex;
  align-items: center;
  gap: 4px;
}

.assignee-name {
  margin-left: 4px;
  font-size: 13px;
  color: #303133;
}

.collaborators {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 8px;
}

.collaborator-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.collaborator-name {
  font-size: 12px;
  color: #606266;
  margin-left: 2px;
}

.more-collaborators {
  font-size: 12px;
  color: #409eff;
  margin-left: 4px;
}

.no-assignee {
  color: #c0c4cc;
  font-size: 12px;
}

.progress-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  text-align: center;
}

.date-cell .overdue {
  color: #f56c6c;
  font-weight: bold;
}

.date-cell .due-soon {
  color: #e6a23c;
  font-weight: bold;
}

.no-date {
  color: #c0c4cc;
  font-size: 12px;
}

.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.card-view {
  padding: 16px 0;
}

.task-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 16px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e4e7ed;
}

.user-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-dept {
  font-size: 12px;
  color: #999;
}

/* 负责人单元格自定义样式 */
.assignee-cell :deep(.el-avatar) {
  transform: scale(0.7);
  transform-origin: left center;
}

/* 快速创建按钮样式 */
:deep(.quick-create-btn) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  color: white;
}

:deep(.quick-create-btn:hover) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

/* 状态选择器样式 */
.status-select {
  width: 100px;
}

.status-select :deep(.el-input__inner) {
  text-align: center;
  font-size: 12px;
  padding: 0 4px;
}

/* 日期单元格样式 */
.date-cell {
  font-size: 12px;
  line-height: 1.4;
}

.start-date {
  color: #606266;
}

.due-date-container {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.overdue-warning {
  margin-top: 2px;
}

.overdue-warning .el-text {
  font-size: 11px !important;
  line-height: 1.2;
}

/* 状态单元格和完成水印样式 */
.status-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

/* 任务名称单元格样式 */
.task-name-cell {
  position: relative;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.task-name-cell:hover {
  background-color: #f5f7fa;
}

.task-content {
  position: relative;
  z-index: 1;
}

.task-title {
  font-weight: 500;
  color: #303133;
  line-height: 1.4;
}

.task-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.3;
}

/* 完成水印样式 - 真正的透明水印效果 */
.completion-watermark {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-15deg);
  pointer-events: none;
  z-index: 2;
  user-select: none;
}

.watermark-text {
  font-size: 14px;
  font-weight: bold;
  color: var(--watermark-color, #409EFF);
  opacity: 0.15;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  border: 2px solid var(--watermark-color, #409EFF);
  padding: 4px 8px;
  border-radius: 6px;
  background: transparent;
  white-space: nowrap;
  transition: opacity 0.3s ease;
}

.task-name-cell:hover .watermark-text {
  opacity: 0.25;
}

/* 游戏化任务领取按钮样式 */
.claim-button {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  border: none;
  color: white;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
  transition: all 0.3s ease;
  animation: pulse-glow 2s infinite;
}

.claim-button:hover {
  background: linear-gradient(135deg, #85ce61, #67c23a);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.5);
  transform: translateY(-1px);
}

.claim-button:active {
  transform: translateY(0);
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
  }
  50% {
    box-shadow: 0 2px 12px rgba(103, 194, 58, 0.6);
  }
}

.claimed-tag {
  background: linear-gradient(135deg, #909399, #c0c4cc);
  border: none;
  color: white;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}
</style>