-- 🎮 游戏化系统数据库表创建脚本
-- 创建日期: 2025-06-20
-- 用途: 为游戏化任务系统创建必要的数据库表

-- 1. 创建游戏化用户统计表
CREATE TABLE IF NOT EXISTS `gamification_userstats` (
  `UserId` BIGINT NOT NULL AUTO_INCREMENT COMMENT '用户统计关联ID (BIGINT PK)',
  `CoreUserId` INT NOT NULL COMMENT '对应的核心用户ID (关联 users.Id - INT)',
  `CurrentXP` INT NOT NULL DEFAULT 0 COMMENT '当前经验值',
  `CurrentLevel` INT NOT NULL DEFAULT 1 COMMENT '当前等级',
  `PointsBalance` INT NOT NULL DEFAULT 0 COMMENT '当前可用积分',
  `CompletedTasksCount` INT NOT NULL DEFAULT 0 COMMENT '累计完成任务数',
  `OnTimeTasksCount` INT NOT NULL DEFAULT 0 COMMENT '累计按时完成任务数',
  `StreakCount` INT NOT NULL DEFAULT 0 COMMENT '当前连续活动/完成任务天数',
  `LastActivityTimestamp` DATETIME NULL COMMENT '最后活跃时间戳',
  `LastStreakTimestamp` DATETIME NULL COMMENT '上次增加连续记录的日期',
  `LastUpdatedTimestamp` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间戳',
  PRIMARY KEY (`UserId`),
  UNIQUE KEY `UK_GamificationUserStats_CoreUserId` (`CoreUserId`),
  CONSTRAINT `FK_GamificationUserStats_Users` FOREIGN KEY (`CoreUserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏化用户统计表';

-- 2. 创建游戏化事件日志表
CREATE TABLE IF NOT EXISTS `gamification_log` (
  `LogId` BIGINT NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `UserId` BIGINT NOT NULL COMMENT '关联用户ID (关联 gamification_userstats.UserId)',
  `Timestamp` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '事件发生时间',
  `EventType` VARCHAR(100) NOT NULL COMMENT '事件类型',
  `XPChange` INT NOT NULL DEFAULT 0 COMMENT '经验值变动',
  `PointsChange` INT NOT NULL DEFAULT 0 COMMENT '积分变动',
  `LevelBefore` INT NULL COMMENT '变动前等级',
  `LevelAfter` INT NULL COMMENT '变动后等级',
  `Reason` VARCHAR(255) NULL COMMENT '变动原因文字描述',
  `RelatedTaskId` BIGINT NULL COMMENT '关联的任务ID',
  `RelatedBadgeId` BIGINT NULL COMMENT '关联的徽章ID',
  `RelatedItemId` BIGINT NULL COMMENT '关联的物品ID',
  `Metadata` TEXT NULL COMMENT '其他元数据 (JSON格式)',
  PRIMARY KEY (`LogId`),
  KEY `IDX_GamificationLog_UserId` (`UserId`),
  KEY `IDX_GamificationLog_EventType` (`EventType`),
  KEY `IDX_GamificationLog_Timestamp` (`Timestamp`),
  KEY `IDX_GamificationLog_RelatedTaskId` (`RelatedTaskId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏化事件日志表';

-- 3. 检查任务领取表是否存在，如果不存在则创建
CREATE TABLE IF NOT EXISTS `task_claims` (
  `claim_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '领取记录ID',
  `task_id` BIGINT NOT NULL COMMENT '任务ID',
  `claimed_by` INT NOT NULL COMMENT '领取用户ID',
  `shift_id` BIGINT NOT NULL COMMENT '班次ID',
  `claimed_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '领取时间',
  `claim_date` DATE NOT NULL COMMENT '领取日期',
  `claim_status` VARCHAR(20) NOT NULL DEFAULT 'Claimed' COMMENT '领取状态',
  `started_at` DATETIME NULL COMMENT '开始时间',
  `completed_at` DATETIME NULL COMMENT '完成时间',
  `notes` VARCHAR(1000) NULL COMMENT '备注',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`claim_id`),
  KEY `IDX_TaskClaims_TaskId` (`task_id`),
  KEY `IDX_TaskClaims_ClaimedBy` (`claimed_by`),
  KEY `IDX_TaskClaims_ShiftId` (`shift_id`),
  KEY `IDX_TaskClaims_ClaimDate` (`claim_date`),
  CONSTRAINT `FK_TaskClaims_Tasks` FOREIGN KEY (`task_id`) REFERENCES `tasks_v2` (`TaskId`) ON DELETE CASCADE,
  CONSTRAINT `FK_TaskClaims_Users` FOREIGN KEY (`claimed_by`) REFERENCES `users` (`Id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务领取记录表';

-- 4. 插入测试数据 (可选)
-- 为当前用户初始化游戏化数据
INSERT IGNORE INTO `gamification_userstats` (`CoreUserId`, `CurrentXP`, `CurrentLevel`, `PointsBalance`, `CompletedTasksCount`, `OnTimeTasksCount`, `StreakCount`, `LastActivityTimestamp`, `LastUpdatedTimestamp`)
SELECT 
    u.Id,
    0 as CurrentXP,
    1 as CurrentLevel,
    0 as PointsBalance,
    0 as CompletedTasksCount,
    0 as OnTimeTasksCount,
    0 as StreakCount,
    NOW() as LastActivityTimestamp,
    NOW() as LastUpdatedTimestamp
FROM users u
WHERE u.Id IN (1, 6) -- admin 和 zhaizhihao 用户
ON DUPLICATE KEY UPDATE LastUpdatedTimestamp = NOW();

-- 5. 验证表创建
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_ROWS
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('gamification_userstats', 'gamification_log', 'task_claims')
ORDER BY TABLE_NAME;

-- 6. 显示游戏化用户统计
SELECT 
    gus.UserId,
    gus.CoreUserId,
    u.Name as UserName,
    gus.CurrentXP,
    gus.CurrentLevel,
    gus.PointsBalance,
    gus.CompletedTasksCount,
    gus.OnTimeTasksCount,
    gus.LastActivityTimestamp
FROM gamification_userstats gus
LEFT JOIN users u ON gus.CoreUserId = u.Id
ORDER BY gus.CoreUserId;

-- 7. 创建周统计视图 (用于排行榜)
CREATE OR REPLACE VIEW `v_weekly_task_stats` AS
SELECT 
    u.Id as UserId,
    u.Name as UserName,
    u.Department,
    COALESCE(weekly_claims.ClaimedCount, 0) as WeeklyClaimedCount,
    COALESCE(weekly_completed.CompletedCount, 0) as WeeklyCompletedCount,
    COALESCE(weekly_created.CreatedCount, 0) as WeeklyCreatedCount,
    COALESCE(gus.PointsBalance, 0) as TotalPoints,
    COALESCE(gus.CurrentLevel, 1) as CurrentLevel,
    COALESCE(gus.CompletedTasksCount, 0) as TotalCompletedTasks,
    YEARWEEK(NOW(), 1) as WeekNumber
FROM users u
LEFT JOIN gamification_userstats gus ON u.Id = gus.CoreUserId
LEFT JOIN (
    SELECT 
        claimed_by as UserId,
        COUNT(*) as ClaimedCount
    FROM task_claims 
    WHERE YEARWEEK(claim_date, 1) = YEARWEEK(NOW(), 1)
    GROUP BY claimed_by
) weekly_claims ON u.Id = weekly_claims.UserId
LEFT JOIN (
    SELECT 
        claimed_by as UserId,
        COUNT(*) as CompletedCount
    FROM task_claims 
    WHERE YEARWEEK(claim_date, 1) = YEARWEEK(NOW(), 1)
    AND claim_status = 'Completed'
    GROUP BY claimed_by
) weekly_completed ON u.Id = weekly_completed.UserId
LEFT JOIN (
    SELECT 
        CreatedBy as UserId,
        COUNT(*) as CreatedCount
    FROM tasks_v2 
    WHERE YEARWEEK(CreatedAt, 1) = YEARWEEK(NOW(), 1)
    GROUP BY CreatedBy
) weekly_created ON u.Id = weekly_created.UserId
WHERE u.IsActive = 1
ORDER BY TotalPoints DESC, WeeklyCompletedCount DESC, WeeklyClaimedCount DESC;

-- 完成提示
SELECT '🎮 游戏化数据库表创建完成！' as Message;
SELECT '✅ 已创建表: gamification_userstats, gamification_log, task_claims' as Status;
SELECT '✅ 已创建视图: v_weekly_task_stats (周统计排行榜)' as Views;
SELECT '✅ 已初始化测试用户游戏化数据' as TestData;
