# 备品备件管理模块设计文档

## 1. 模块概述

备品备件管理模块是IT资产管理系统的重要组成部分，用于管理各类维修、更换用的备品备件。该模块实现备件的分类管理、库存跟踪、出入库操作、库存预警等功能，支持PC端与移动端操作，提升备品备件管理的规范化和高效性。

### 1.1 主要功能

- **备品备件台账**：集中管理所有备件信息
- **备件分类管理**：支持多级分类结构
- **库位管理**：灵活设置备件存放位置，支持分区域管理
- **出入库操作**：支持PC端和移动端快速操作
- **库存预警**：低于安全库存自动预警
- **出入库记录**：完整追踪备件流动历史
- **移动端支持**：优化的手机界面和扫码功能

## 2. 数据库设计

### 2.1 备品备件主表 (spare_parts)

```sql
CREATE TABLE `spare_parts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(50) NOT NULL COMMENT '备件编号',
  `name` varchar(100) NOT NULL COMMENT '备件名称',
  `type_id` int NOT NULL COMMENT '备件类型ID',
  `spec` varchar(200) NULL COMMENT '规格型号',
  `brand` varchar(100) NULL COMMENT '品牌',
  `quantity` int NOT NULL DEFAULT 0 COMMENT '当前库存量',
  `unit` varchar(20) NOT NULL COMMENT '单位',
  `min_threshold` int NOT NULL DEFAULT 5 COMMENT '最小安全库存',
  `warning_threshold` int NOT NULL DEFAULT 10 COMMENT '预警库存',
  `location_id` int NOT NULL COMMENT '库位ID (关联 spare_part_locations.id)',
  `purchase_price` decimal(10,2) NULL COMMENT '采购价格',
  `supplier_id` int NULL COMMENT '默认供应商',
  `notes` varchar(500) NULL COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_spare_parts_code` (`code`),
  INDEX `idx_spare_parts_type` (`type_id`),
  INDEX `idx_spare_parts_location` (`location_id`)
)
```

### 2.2 备件类型表 (spare_part_types)

```sql
CREATE TABLE `spare_part_types` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '类型名称',
  `code` varchar(20) NOT NULL COMMENT '类型编码',
  `parent_id` int NULL COMMENT '父类型ID',
  `path` varchar(200) NULL COMMENT '类型路径 (例如 1,5,15)',
  `level` int NOT NULL DEFAULT 1 COMMENT '层级',
  `description` varchar(200) NULL COMMENT '描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_part_types_code` (`code`),
  INDEX `idx_part_types_parent` (`parent_id`)
)
```

### 2.3 备品备件库位表 (spare_part_locations)

```sql
CREATE TABLE `spare_part_locations` (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(50) NOT NULL COMMENT '库位编号 (例如 A-1-1-1)',
  `name` varchar(100) NOT NULL COMMENT '库位名称 (例如 A区-1号货架-1层-1格)',
  `area` varchar(20) NOT NULL COMMENT '区域标识 (如 A区, B区)',
  `description` varchar(200) NULL COMMENT '库位描述',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_spare_part_locations_code` (`code`),
  INDEX `idx_spare_part_locations_area` (`area`)
) COMMENT = '备品备件库位表';
```

### 2.4 出入库记录表 (spare_part_transactions)

```sql
CREATE TABLE `spare_part_transactions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `part_id` int NOT NULL COMMENT '备件ID',
  `type` tinyint NOT NULL COMMENT '类型:1入库,2出库',
  `quantity` int NOT NULL COMMENT '数量',
  `user_id` int NOT NULL COMMENT '操作人ID',
  `location_id` int NOT NULL COMMENT '库位ID (关联 spare_part_locations.id)',
  `reference` varchar(100) NULL COMMENT '关联单号 (采购单、维修单等)',
  `reason_type` tinyint NULL COMMENT '原因类型:1采购,2退回,3领用,4报废,5盘点调整等',
  `reason` varchar(200) NULL COMMENT '原因/用途详细描述',
  `related_asset_id` int NULL COMMENT '关联资产ID (若出库用于特定资产)',
  `related_fault_id` int NULL COMMENT '关联故障ID (若出库用于特定维修)',
  `transaction_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_transactions_part` (`part_id`),
  INDEX `idx_transactions_user` (`user_id`),
  INDEX `idx_transactions_time` (`transaction_time`),
  INDEX `idx_transactions_type` (`type`)
)
```

### 2.5 数据关系

- `spare_parts.type_id` → `spare_part_types.id`
- `spare_parts.location_id` → `spare_part_locations.id`
- `spare_parts.supplier_id` → `suppliers.Id` (利用现有供应商表)
- `spare_part_transactions.part_id` → `spare_parts.id`
- `spare_part_transactions.user_id` → `users.Id` (利用现有用户表)
- `spare_part_transactions.location_id` → `spare_part_locations.id`
- `spare_part_transactions.related_asset_id` → `assets.Id` (可选关联)
- `spare_part_transactions.related_fault_id` → `faultrecords.Id` (可选关联)

## 3. API设计

### 3.1 备品备件台账API

```
GET    /api/v2/spare-parts              - 获取备件列表(支持分页、筛选)
GET    /api/v2/spare-parts/{id}         - 获取单个备件详情
POST   /api/v2/spare-parts              - 创建新备件
PUT    /api/v2/spare-parts/{id}         - 更新备件信息
DELETE /api/v2/spare-parts/{id}         - 删除备件
GET    /api/v2/spare-parts/low-stock    - 获取库存预警列表
```

#### 3.1.1 获取备件列表请求参数
```json
{
  "pageSize": 10,
  "pageIndex": 1,
  "sortBy": "name",
  "sortDirection": "asc",
  "filters": {
    "typeId": 1,
    "locationId": 2,
    "locationArea": "A",
    "keyword": "内存",
    "lowStock": true // true:仅显示低于预警库存, false:仅显示低于最小安全库存, null:显示所有
  }
}
```

#### 3.1.2 备件数据结构 (示例)
```json
{
  "id": 1,
  "code": "SP202505-001",
  "name": "DDR4内存条",
  "typeId": 5,
  "typeName": "内存",
  "spec": "16GB DDR4 3200MHz",
  "brand": "Kingston",
  "quantity": 8,
  "unit": "条",
  "minThreshold": 5,
  "warningThreshold": 10,
  "locationId": 3,
  "locationName": "A区-1号货架-1层-1格",
  "locationCode": "A-1-1-1",
  "locationArea": "A",
  "purchasePrice": 399.00,
  "supplierId": 2,
  "supplierName": "某科技有限公司",
  "notes": "服务器专用",
  "createdAt": "2025-05-10T14:30:00",
  "updatedAt": "2025-05-15T09:20:00"
}
```

### 3.2 备件类型API

```
GET    /api/v2/spare-part-types           - 获取类型列表(支持树形结构)
GET    /api/v2/spare-part-types/{id}      - 获取单个类型详情
POST   /api/v2/spare-part-types           - 创建新类型
PUT    /api/v2/spare-part-types/{id}      - 更新类型
DELETE /api/v2/spare-part-types/{id}      - 删除类型
```

#### 3.2.1 类型数据结构 (示例)
```json
{
  "id": 5,
  "name": "内存",
  "code": "RAM",
  "parentId": 1,
  "parentName": "计算机配件",
  "path": "1,5",
  "level": 2,
  "description": "各类内存模块",
  "children": [
    {
      "id": 15,
      "name": "DDR4",
      "code": "DDR4",
      "parentId": 5,
      "parentName": "内存",
      "path": "1,5,15",
      "level": 3,
      "description": "DDR4内存"
    }
  ]
}
```

### 3.3 备件库位管理API

```
GET    /api/v2/spare-part-locations             - 获取备件库位列表 (支持筛选区域等)
GET    /api/v2/spare-part-locations/{id}        - 获取单个备件库位详情
POST   /api/v2/spare-part-locations             - 创建新备件库位
PUT    /api/v2/spare-part-locations/{id}        - 更新备件库位信息
DELETE /api/v2/spare-part-locations/{id}        - 删除备件库位
GET    /api/v2/spare-part-locations/areas       - 获取所有区域列表
```

#### 3.3.1 库位数据结构 (示例)
```json
{
  "id": 1,
  "code": "A-1-1-1",
  "name": "A区-1号货架-1层-1格",
  "area": "A",
  "description": "主存储区计算机配件货架",
  "isActive": true,
  "partsCount": 5,
  "createdAt": "2025-05-01T09:00:00",
  "updatedAt": "2025-05-01T09:00:00"
}
```

### 3.4 出入库操作API

```
POST   /api/v2/spare-parts/transactions/in      - 备件入库
POST   /api/v2/spare-parts/transactions/out     - 备件出库
GET    /api/v2/spare-parts/{partId}/transactions - 获取指定备件的出入库记录 (支持分页)
GET    /api/v2/spare-parts/transactions         - 获取所有出入库记录 (支持分页和筛选)
```

#### 3.4.1 入库请求数据结构 (示例)
```json
{
  "partId": 1,
  "quantity": 5,
  "locationId": 3,  // 目标库位ID
  "reference": "PO-20250515-001",
  "reasonType": 1, // 1:采购入库
  "reason": "常规采购补充",
  "notes": "紧急订单"
}
```

#### 3.4.2 出库请求数据结构 (示例)
```json
{
  "partId": 1,
  "quantity": 2,
  "locationId": 3, // 来源库位ID
  "reference": "FR-20250515-001",
  "reasonType": 3, // 3:维修领用
  "reason": "服务器内存更换",
  "relatedAssetId": 105,
  "relatedFaultId": 28,
  "notes": "紧急维修"
}
```

#### 3.4.3 出入库记录数据结构 (示例)
```json
{
  "id": 101,
  "partId": 1,
  "partCode": "SP202505-001",
  "partName": "DDR4内存条",
  "type": 1, // 1:入库, 2:出库
  "quantity": 5,
  "userId": 10,
  "userName": "张三",
  "locationId": 3,
  "locationName": "A区-1号货架-1层-1格",
  "locationCode": "A-1-1-1",
  "reference": "PO-20250515-001",
  "reasonType": 1,
  "reason": "常规采购补充",
  "relatedAssetId": null,
  "relatedFaultId": null,
  "transactionTime": "2025-05-15T10:00:00"
}
```

## 4. 前端界面设计与交互优化

**通用交互增强原则**:
- **即时反馈**: 所有用户操作（如点击按钮、保存数据、删除条目）都应有明确的视觉反馈，例如加载指示器（Spinners/Loaders）、Toast提示（成功、警告、错误信息）、按钮禁用状态等。
- **智能提示与自动完成**: 在关键输入字段（如备件名称/编码搜索、类型选择、库位选择）中提供基于用户输入的建议列表和自动完成功能，减少输入错误，提升效率。
- **上下文菜单**: 对表格中的行或关键UI元素（如图标、卡片）使用右键菜单（或长按移动端），提供与当前上下文相关的快捷操作（如查看详情、编辑、快速出入库、打印标签等）。
- **可定制性与个性化**: 允许用户自定义表格列的显示/隐藏、顺序调整、列宽拖拽，并能保存用户的视图偏好。
- **减少页面跳转与保持上下文**: 优先使用模态框（Dialogs/Modals）或抽屉（Drawers）进行添加、编辑、查看详情等操作，避免不必要的页面刷新和跳转，保持用户当前工作流的连贯性。
- **键盘友好**: 为常用操作提供键盘快捷键支持（如Ctrl+S保存，Enter确认，Esc取消，表格导航等）。
- **清晰的导航与层级**: 确保模块内各页面导航清晰，层级关系明确，用户能轻松找到所需功能。
- **一致性**: 模块内及跨模块的UI元素、交互模式应保持高度一致性。

### 4.1 备品备件台账页面 (`/spare-parts`)

![备品备件台账](https://placeholder.com/spare-parts-list)

**主要功能与交互优化**:
- **表格展示**: 
    - **动态列**: 用户可选择显示/隐藏列，调整列顺序和宽度，并保存视图配置。
    - **行内操作**: 每行提供"编辑"、"删除"、"查看详情"、"出/入库"等快捷操作按钮或下拉菜单。
    - **库存高亮**: 当库存量低于`warning_threshold`时，数量单元格背景以黄色预警；低于`min_threshold`时，以红色严重预警。鼠标悬停可显示具体阈值。
    - **可排序与筛选**: 所有列头均可点击排序。提供表头筛选或独立的筛选区域。
- **筛选与搜索**:
    - **多条件筛选**: 提供独立的筛选面板或表头下拉筛选，可按备件类型（树形选择）、区域、库位、供应商、库存状态（正常、预警、缺货）等进行组合筛选。
    - **实时关键字搜索**: 输入关键字（备件编码、名称、规格、品牌）即时过滤列表。
- **批量操作**: 
    - 表格首列提供复选框，支持选中多个备件进行批量操作（如批量修改库位、批量出库到同一维修单）。
    - 批量操作按钮在用户选中条目后激活。
- **快捷操作**: 
    - 页面顶部提供"新增备件"、"快速入库"、"快速出库"等全局操作按钮。
- **备件详情查看**: 
    - 点击备件名称或"查看详情"按钮，可在抽屉或新页面展示备件完整信息，包括基本属性、当前库存、关联图片、出入库历史记录、关联的维修单等。

### 4.2 备件类型管理页面 (`/spare-part-types`)

![备件类型管理](https://placeholder.com/spare-part-types)

**主要功能与交互优化**:
- **树形结构展示**: 
    - 清晰展示类型层级，支持展开/折叠所有节点。
    - 节点旁可显示该类型下（包括子类型）的备件总数。
- **节点操作**: 
    - 鼠标悬停在类型节点上或点击节点旁的"更多"图标，显示操作菜单（新增子类型、编辑当前类型、删除当前类型）。
    - 删除有子类型或已关联备件的类型时，应有明确提示和确认。
- **拖拽调整**: 支持通过拖拽方式调整类型的父子关系和同级顺序，实时更新树结构。
- **类型筛选**: 点击树节点，右侧或下方可以联动显示属于该类型（及子类型）的所有备件列表。

### 4.3 库位管理页面 (`/spare-part-locations`)

![库位管理](https://placeholder.com/spare-part-locations)

**主要功能与交互优化**:
- **分区展示**: 
    - 可以按区域（如A区、B区）对库位进行分组显示。
    - 提供区域筛选功能，快速查看特定区域的库位。
- **库位列表**:
    - 表格展示所有库位信息（编码、名称、区域、描述等）。
    - 支持新增、编辑、删除库位。
    - 提供库位编码规则设置，确保编码格式的一致性（如A-1-1-1，表示A区-1号架-1层-1格）。
- **库位内备件查看**:
    - 点击库位可查看该库位下存放的所有备件，方便库位使用情况审查。
- **库位布局可视化 (可选高级功能)**: 
    - 提供简单的2D布局图，显示不同区域的库位分布。
    - 支持通过视觉界面点击特定库位查看详情或进行操作。
    - 通过颜色标识库位占用情况（如空闲、低占用、高占用、满载）。
- **快速批量编辑**:
    - 支持通过Excel模板批量导入库位数据，便于系统初始化。
    - 支持批量更新库位状态（如启用/停用）。
- **库位历史记录**:
    - 查看特定库位的出入库历史，了解库位使用情况。

### 4.4 快速出入库页面(移动端优化)

![快速出入库](https://placeholder.com/mobile-transactions)

**主要功能与交互优化**:
- **简洁界面**: 专为移动端优化，大按钮，清晰字体。
- **操作选择**: 首页清晰选择"入库"或"出库"。
- **备件选择**: 
    - **扫码优先**: 突出扫码按钮，支持调用摄像头扫描备件条码/二维码快速识别备件。
    - **手动搜索**: 若无码或扫码失败，提供关键字搜索（名称/编码/规格）备件，支持模糊匹配和下拉选择。
- **数量输入**: 
    - 识别备件后，显示当前库存和单位。
    - 提供易于点击的"+"、"-"按钮调整数量，也支持手动输入。
    - 出库时，数量不能超过当前库存，并有实时提示。
- **信息填写**: 
    - **库位选择**: 简化的区域和库位选择界面，采用两级联动选择（先选区域，再选具体库位）。
    - **关联信息**: 简化关联单号、原因等信息的填写，提供常用选项。
- **提交与反馈**: 提交后有明确的成功或失败提示。支持连续操作。
- **最近操作**: 可查看最近的出入库记录，方便追溯或撤销（如果业务允许）。
- **离线支持 (可选高级功能)**: 在网络不佳时，允许本地缓存出入库操作，联网后自动同步，并提示同步状态。

### 4.5 出入库记录页面 (`/spare-parts/transactions`)

![出入库记录](https://placeholder.com/transactions-history)

**主要功能与交互优化**:
- **列表/时间线展示**: 
    - 默认按时间倒序展示所有出入库记录。
    - 可选切换为时间线视图，更直观地追踪备件流动。
- **高级筛选与搜索**: 
    - 按日期范围、操作人、备件名称/编码、出/入库类型、区域、库位、关联单号等进行组合筛选。
    - 关键字搜索可应用于记录中的多个字段。
- **记录详情**: 点击记录可查看详细信息，包括操作时的完整上下文。
- **数据导出**: 支持将筛选结果导出为Excel或CSV文件。
- **关联跳转**: 若记录关联了资产或故障单，提供链接直接跳转到相应的资产详情页或故障详情页。

## 5. 移动端适配

除了上述"快速出入库页面"的专门设计外，其他PC端页面也应考虑移动端适配。

### 5.1 移动端界面优化

- **响应式设计**: 所有PC端页面在移动设备上应能自适应屏幕宽度，保证内容可读性和操作便捷性。
- **简化导航**: 使用汉堡菜单或底部Tab栏进行导航。
- **触摸优化**: 确保按钮、链接、表单控件等元素有足够的点击区域。
- **性能优化**: 减少不必要的动画和资源加载，保证移动端流畅性。

### 5.2 扫码功能集成

- **多场景应用**: 不仅限于快速出入库，盘点、查询备件详情等场景也可集成扫码功能。
- **调用原生能力**: 优先使用设备原生的扫码能力（如果打包成App），或可靠的Web扫码库。
- **反馈清晰**: 扫描成功、失败、无匹配结果等均有明确提示。

## 6. 实施步骤

1.  **数据库设计与创建**
    *   根据上述2.1-2.4节创建或修改数据表。
    *   建立正确的外键约束和索引。
    *   准备或导入初始数据（如备件类型、库位区域等）。

2.  **后端开发 (Clean Architecture)**
    *   **Domain层**: 定义 `SparePart`, `SparePartType`, `SparePartLocation`, `SparePartTransaction` 等实体及相关业务规则。
    *   **Application层**: 
        *   定义DTOs (如 `SparePartDto`, `CreateSparePartRequest`, `SparePartTransactionDto` 等)。
        *   创建服务接口 (如 `ISparePartService`, `ISparePartLocationService` 等) 和实现类。
        *   使用CQRS模式处理命令（如 `CreateSparePartCommand`）和查询（如 `GetSparePartsQuery`）。
        *   实现出入库核心业务逻辑，确保数据一致性（如更新库存量）。
    *   **Infrastructure层**: 
        *   实现仓储接口 (如 `ISparePartRepository` 等)，使用EF Core与数据库交互。
    *   **Api层 (V2规范)**:
        *   创建 `SparePartsController`, `SparePartTypesController`, `SparePartLocationsController`, `SparePartTransactionsController`。
        *   实现3.1-3.4节定义的API端点，遵循RESTful原则和项目V2 API规范（标准响应结构、JWT认证授权等）。
    *   编写单元测试和集成测试。

3.  **前端开发**
    *   根据4.1-4.5节的需求，设计和开发Vue组件和页面。
    *   重点关注用户交互体验和易用性，落实通用交互增强原则。
    *   实现移动端响应式布局和专门的移动端快速操作界面。
    *   集成扫码功能库。
    *   与后端API进行联调测试。

4.  **集成与测试**
    *   进行端到端的系统集成测试。
    *   邀请用户进行UAT（用户验收测试），收集反馈并迭代优化。
    *   进行性能测试和安全测试。

5.  **部署与培训**
    *   将模块部署到测试环境和生产环境。
    *   编写用户手册和操作指南。
    *   对相关用户进行系统操作培训。

## 7. 扩展功能 (后续考虑)

### 7.1 条码/二维码系统

-   为每个备件（或每批次）生成唯一二维码/条码标签。
-   标签上可包含备件ID、名称、规格、入库批次等信息。
-   提供标签批量打印功能。

### 7.2 统计分析与报表

-   备件出入库趋势图表（按月/周/日）。
-   各类备件库存周转率分析。
-   高频使用/低频使用备件排行。
-   备件采购成本及消耗成本分析报表。

### 7.3 智能预警与补货建议

-   基于历史消耗速率和采购周期，提供更智能的库存预警。
-   自动生成补货建议清单。
-   通过邮件、系统内消息等方式推送预警和建议。

### 7.4 与维修模块、采购模块的深度集成

-   维修工单创建时，可直接从备件库存中预留或领用所需备件。
-   维修完成后，自动更新备件消耗记录，并与资产的维修历史关联。
-   当备件库存低于阈值时，可触发采购申请流程或直接生成采购建议到采购模块。

## 8. 结论

备品备件管理模块通过规范化管理流程、数字化记录、强大的交互设计和移动端支持，能够大幅提升备件管理效率，降低因缺件导致的维修延误，提高资产维护质量，并为成本控制提供数据支持。关注用户体验是确保该模块成功的关键因素之一。 