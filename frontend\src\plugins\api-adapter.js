/**
 * API适配器Vue插件
 * 文件路径: src/plugins/api-adapter.js
 * 功能描述: 将API适配器注册为Vue插件，提供全局访问
 */

import apiAdapter from '@/api/adapter'
import { getToken } from '@/utils/auth'

/**
 * API适配器Vue插件
 */
const ApiAdapterPlugin = {
  /**
   * 安装插件
   * @param {Object} app Vue应用实例
   * @param {Object} options 插件选项
   */
  install(app, options = {}) {
    // 将API适配器添加到全局属性
    app.config.globalProperties.$apiAdapter = apiAdapter
    app.config.globalProperties.$userApi = apiAdapter.userApi
    app.config.globalProperties.$assetApi = apiAdapter.assetApi
    app.config.globalProperties.$taskApi = apiAdapter.taskApi

    // 提供组合式API支持
    app.provide('apiAdapter', apiAdapter)
    app.provide('userApi', apiAdapter.userApi)
    app.provide('assetApi', apiAdapter.assetApi)
    app.provide('taskApi', apiAdapter.taskApi)

    // 初始化API适配器
    const initializeAdapter = async () => {
      try {
        // 从token中获取用户信息（如果需要）
        const token = getToken()
        let userId = null
        
        if (token && options.extractUserIdFromToken) {
          userId = options.extractUserIdFromToken(token)
        }
        
        await apiAdapter.initialize(userId)
        console.log('API适配器插件初始化完成')
      } catch (error) {
        console.warn('API适配器插件初始化失败:', error)
      }
    }

    // 在应用挂载后初始化
    app.mixin({
      async mounted() {
        if (this.$root === this) {
          // 只在根组件中初始化一次
          await initializeAdapter()
        }
      }
    })

    // 添加全局方法
    app.config.globalProperties.$switchApiVersion = async (serviceName, version) => {
      return await apiAdapter.switchVersion(serviceName, version)
    }

    app.config.globalProperties.$getCurrentApiVersions = () => {
      return apiAdapter.getCurrentVersions()
    }

    app.config.globalProperties.$getApiHealthStatus = async () => {
      return await apiAdapter.getHealthStatus()
    }

    app.config.globalProperties.$getPerformanceComparison = (apiName = null) => {
      return apiAdapter.getPerformanceComparison(apiName)
    }

    app.config.globalProperties.$getRealTimeMetrics = () => {
      return apiAdapter.getRealTimeMetrics()
    }

    app.config.globalProperties.$clearPerformanceData = (apiName = null) => {
      return apiAdapter.clearPerformanceData(apiName)
    }

    app.config.globalProperties.$exportPerformanceData = () => {
      return apiAdapter.exportPerformanceData()
    }

    app.config.globalProperties.$setPerformanceMonitoringEnabled = (enabled) => {
      return apiAdapter.setPerformanceMonitoringEnabled(enabled)
    }

    // 监听版本切换事件
    apiAdapter.onVersionChange((event) => {
      const { serviceName, version } = event.detail
      console.log(`API版本切换事件: ${serviceName} -> ${version}`)
      
      // 触发Vue事件总线（如果存在）
      if (app.config.globalProperties.$eventBus) {
        app.config.globalProperties.$eventBus.emit('apiVersionChanged', {
          serviceName,
          version
        })
      }
    })

    console.log('API适配器Vue插件已安装')
  }
}

/**
 * 组合式API - 使用API适配器
 * @returns {Object} API适配器相关方法
 */
export function useApiAdapter() {
  const { inject } = require('vue')
  
  const adapter = inject('apiAdapter')
  const userApi = inject('userApi')
  const assetApi = inject('assetApi')
  const taskApi = inject('taskApi')

  return {
    apiAdapter: adapter,
    userApi,
    assetApi,
    taskApi,
    
    // 便捷方法
    switchVersion: async (serviceName, version) => {
      return await adapter.switchVersion(serviceName, version)
    },
    
    getCurrentVersions: () => {
      return adapter.getCurrentVersions()
    },
    
    getHealthStatus: async () => {
      return await adapter.getHealthStatus()
    },
    
    enableABTest: async (config) => {
      return await adapter.enableABTest(config)
    },
    
    getVersionStatistics: async () => {
      return await adapter.getVersionStatistics()
    },

    // 性能监控方法
    getPerformanceComparison: (apiName = null) => {
      return adapter.getPerformanceComparison(apiName)
    },

    getRealTimeMetrics: () => {
      return adapter.getRealTimeMetrics()
    },

    clearPerformanceData: (apiName = null) => {
      return adapter.clearPerformanceData(apiName)
    },

    exportPerformanceData: () => {
      return adapter.exportPerformanceData()
    },

    setPerformanceMonitoringEnabled: (enabled) => {
      return adapter.setPerformanceMonitoringEnabled(enabled)
    }
  }
}

/**
 * 组合式API - 使用用户API
 * @returns {Object} 用户API方法
 */
export function useUserApi() {
  const { inject } = require('vue')
  return inject('userApi')
}

/**
 * 组合式API - 使用资产API
 * @returns {Object} 资产API方法
 */
export function useAssetApi() {
  const { inject } = require('vue')
  return inject('assetApi')
}

/**
 * 组合式API - 使用任务API
 * @returns {Object} 任务API方法
 */
export function useTaskApi() {
  const { inject } = require('vue')
  return inject('taskApi')
}

export default ApiAdapterPlugin
