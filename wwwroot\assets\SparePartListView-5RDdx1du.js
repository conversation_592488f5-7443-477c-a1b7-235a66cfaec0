import{_ as e,r as l,a5 as a,c as o,m as t,a8 as d,b as r,d as u,af as n,e as i,w as s,ag as p,Y as m,a as c,o as f,A as b,a7 as v,F as g,h as y,i as V,t as h,a9 as _}from"./index-C7OOw0MO.js";import{u as k}from"./spareparts-PUry4Rrk.js";import{g as I,c as w,u as U,s as x,a as S}from"./spareparts-NQmkyISR.js";const C={class:"spare-part-list-view"},q={class:"page-header"},T={class:"filter-container"},z={class:"pagination-container"},F={class:"dialog-footer"},N={class:"dialog-footer"},O={class:"dialog-footer"},Q=e({__name:"SparePartListView",setup(e){const Q=l(!1),j=l([]),A=l(0),B=l(!1),$=l("add"),P=l(null),R=l(!1),D=l(!1),E=l(null),K=l(null),L=l({}),Y=k(),G=a({pageIndex:1,pageSize:10,name:"",code:"",typeId:"",stockStatus:"",sortBy:"",sortOrder:""}),H=a({id:null,code:"",name:"",typeId:"",specification:"",brand:"",unit:"",initialStock:0,warningThreshold:10,minStock:5,locationId:"",price:null,remarks:""}),J=a({partId:null,quantity:1,locationId:"",reasonType:1,referenceNumber:"",remarks:""}),M=a({partId:null,quantity:1,locationId:"",reasonType:3,referenceNumber:"",relatedAssetId:null,relatedFaultId:null,remarks:""}),W={code:[{required:!0,message:"请输入备件编号",trigger:"blur"},{max:50,message:"长度不能超过50个字符",trigger:"blur"}],name:[{required:!0,message:"请输入备件名称",trigger:"blur"},{max:100,message:"长度不能超过100个字符",trigger:"blur"}],typeId:[{required:!0,message:"请选择备件类型",trigger:"change"}],unit:[{required:!0,message:"请输入单位",trigger:"blur"},{max:10,message:"长度不能超过10个字符",trigger:"blur"}],locationId:[{required:!0,message:"请选择库位",trigger:"change"}]},X={quantity:[{required:!0,message:"请输入入库数量",trigger:"blur"},{type:"number",min:1,message:"数量必须大于0",trigger:"blur"}],locationId:[{required:!0,message:"请选择库位",trigger:"change"}],reasonType:[{required:!0,message:"请选择入库类型",trigger:"change"}]},Z={quantity:[{required:!0,message:"请输入出库数量",trigger:"blur"},{type:"number",min:1,message:"数量必须大于0",trigger:"blur"}],locationId:[{required:!0,message:"请选择库位",trigger:"change"}],reasonType:[{required:!0,message:"请选择出库类型",trigger:"change"}]},ee=o((()=>Y.typesTree)),le=o((()=>Y.locations));t((async()=>{await Promise.all([Y.fetchTypesTree(),Y.fetchLocations()]),ae()}));const ae=async()=>{Q.value=!0;try{const e=await I({pageIndex:G.pageIndex,pageSize:G.pageSize,name:G.name||void 0,code:G.code||void 0,typeId:G.typeId||void 0,stockStatus:G.stockStatus||void 0,sortBy:G.sortBy||void 0,sortOrder:G.sortOrder||void 0});e.success?(j.value=e.data.items,A.value=e.data.totalCount):d.error(e.message||"获取备件列表失败")}catch(e){d.error("获取备件列表失败")}finally{Q.value=!1}},oe=()=>{G.pageIndex=1,ae()},te=()=>{G.name="",G.code="",G.typeId="",G.stockStatus="",G.sortBy="",G.sortOrder="",oe()},de=e=>{e.prop&&e.order?(G.sortBy=e.prop,G.sortOrder="ascending"===e.order?"asc":"desc"):(G.sortBy="",G.sortOrder=""),ae()},re=e=>{G.pageSize=e,ae()},ue=e=>{G.pageIndex=e,ae()},ne=()=>{$.value="add",ie(),B.value=!0},ie=()=>{"add"===$.value&&Object.keys(H).forEach((e=>{H[e]="warningThreshold"===e?10:"minStock"===e?5:"initialStock"===e?0:""})),P.value&&P.value.resetFields()},se=async()=>{P.value&&await P.value.validate((async e=>{if(!e)return!1;try{let e;e="add"===$.value?await w(H):await U(H.id,H),e.success?(d.success("add"===$.value?"新增成功":"更新成功"),B.value=!1,ae()):d.error(e.message||("add"===$.value?"新增失败":"更新失败"))}catch(l){d.error("add"===$.value?"新增失败":"更新失败")}}))},pe=async()=>{E.value&&await E.value.validate((async e=>{if(!e)return!1;try{const e=await x(J);e.success?(d.success("入库成功"),R.value=!1,ae()):d.error(e.message||"入库失败")}catch(l){d.error("入库失败")}}))},me=async()=>{K.value&&await K.value.validate((async e=>{if(!e)return!1;try{const e=await S(M);e.success?(d.success("出库成功"),D.value=!1,ae()):d.error(e.message||"出库失败")}catch(l){d.error("出库失败")}}))};return(e,l)=>{const a=c("el-button"),o=c("el-input"),t=c("el-form-item"),k=c("el-option"),I=c("el-select"),w=c("el-form"),U=c("el-table-column"),x=c("el-table"),S=c("el-pagination"),Y=c("el-input-number"),ae=c("el-dialog"),ce=p("loading");return f(),r("div",C,[u("div",q,[l[39]||(l[39]=u("h2",null,"备件台账管理",-1)),i(a,{type:"primary",onClick:ne},{default:s((()=>l[38]||(l[38]=[b("新增备件")]))),_:1})]),u("div",T,[i(w,{inline:!0,model:G,class:"demo-form-inline"},{default:s((()=>[i(t,{label:"备件名称"},{default:s((()=>[i(o,{modelValue:G.name,"onUpdate:modelValue":l[0]||(l[0]=e=>G.name=e),placeholder:"备件名称",clearable:"",onKeyup:v(oe,["enter"])},null,8,["modelValue"])])),_:1}),i(t,{label:"备件编号"},{default:s((()=>[i(o,{modelValue:G.code,"onUpdate:modelValue":l[1]||(l[1]=e=>G.code=e),placeholder:"备件编号",clearable:"",onKeyup:v(oe,["enter"])},null,8,["modelValue"])])),_:1}),i(t,{label:"备件类型"},{default:s((()=>[i(I,{modelValue:G.typeId,"onUpdate:modelValue":l[2]||(l[2]=e=>G.typeId=e),placeholder:"备件类型",clearable:""},{default:s((()=>[(f(!0),r(g,null,y(ee.value,(e=>(f(),m(k,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(t,{label:"库存状态"},{default:s((()=>[i(I,{modelValue:G.stockStatus,"onUpdate:modelValue":l[3]||(l[3]=e=>G.stockStatus=e),placeholder:"库存状态",clearable:""},{default:s((()=>[i(k,{label:"正常",value:"normal"}),i(k,{label:"预警",value:"warning"}),i(k,{label:"不足",value:"danger"})])),_:1},8,["modelValue"])])),_:1}),i(t,null,{default:s((()=>[i(a,{type:"primary",onClick:oe},{default:s((()=>l[40]||(l[40]=[b("查询")]))),_:1}),i(a,{onClick:te},{default:s((()=>l[41]||(l[41]=[b("重置")]))),_:1})])),_:1})])),_:1},8,["model"])]),n((f(),m(x,{data:j.value,border:"",style:{width:"100%"},onSortChange:de},{default:s((()=>[i(U,{prop:"code",label:"备件编号",width:"120",sortable:"custom"}),i(U,{prop:"name",label:"备件名称",width:"150",sortable:"custom"}),i(U,{prop:"typeName",label:"备件类型",width:"120"}),i(U,{prop:"specification",label:"规格型号",width:"120"}),i(U,{prop:"brand",label:"品牌",width:"100"}),i(U,{prop:"stockQuantity",label:"库存数量",width:"100",sortable:"custom"},{default:s((e=>{return[u("span",{class:V((l=e.row,l.stockQuantity<=l.minStock?"stock-danger":l.stockQuantity<=l.warningThreshold?"stock-warning":"stock-normal"))},h(e.row.stockQuantity)+" "+h(e.row.unit),3)];var l})),_:1}),i(U,{prop:"minStock",label:"最小库存",width:"100"}),i(U,{prop:"locationName",label:"库位",width:"120"}),i(U,{prop:"price",label:"单价(元)",width:"100"},{default:s((e=>[b(h(e.row.price?e.row.price.toFixed(2):"-"),1)])),_:1}),i(U,{label:"操作",width:"280",fixed:"right"},{default:s((e=>[i(a,{type:"info",size:"small",onClick:l=>{return a=e.row,void d.info(`查看备件详情：${a.name}`);var a}},{default:s((()=>l[42]||(l[42]=[b("详情")]))),_:2},1032,["onClick"]),i(a,{type:"primary",size:"small",onClick:l=>{return a=e.row,$.value="edit",Object.keys(H).forEach((e=>{H[e]=a[e]})),void(B.value=!0);var a}},{default:s((()=>l[43]||(l[43]=[b("编辑")]))),_:2},1032,["onClick"]),i(a,{type:"warning",size:"small",onClick:l=>{return a=e.row,void d.info(`申请返厂：${a.name}`);var a}},{default:s((()=>l[44]||(l[44]=[b("返厂")]))),_:2},1032,["onClick"]),i(a,{type:"success",size:"small",onClick:l=>{return a=e.row,L.value=a,J.partId=a.id,J.locationId=a.locationId,J.quantity=1,void(R.value=!0);var a}},{default:s((()=>l[45]||(l[45]=[b("入库")]))),_:2},1032,["onClick"]),i(a,{type:"danger",size:"small",onClick:l=>{return a=e.row,L.value=a,M.partId=a.id,M.locationId=a.locationId,M.quantity=1,M.relatedAssetId=null,M.relatedFaultId=null,void(D.value=!0);var a}},{default:s((()=>l[46]||(l[46]=[b("出库")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[ce,Q.value]]),u("div",z,[i(S,{currentPage:G.pageIndex,"onUpdate:currentPage":l[4]||(l[4]=e=>G.pageIndex=e),"page-size":G.pageSize,"onUpdate:pageSize":l[5]||(l[5]=e=>G.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:A.value,onSizeChange:re,onCurrentChange:ue},null,8,["currentPage","page-size","total"])]),i(ae,{modelValue:B.value,"onUpdate:modelValue":l[19]||(l[19]=e=>B.value=e),title:"add"===$.value?"新增备件":"编辑备件",width:"650px",onClose:ie},{footer:s((()=>[u("div",F,[i(a,{onClick:l[18]||(l[18]=e=>B.value=!1)},{default:s((()=>l[47]||(l[47]=[b("取消")]))),_:1}),i(a,{type:"primary",onClick:se},{default:s((()=>l[48]||(l[48]=[b("确定")]))),_:1})])])),default:s((()=>[i(w,{ref_key:"formRef",ref:P,model:H,rules:W,"label-width":"100px",style:{"max-height":"500px","overflow-y":"auto"}},{default:s((()=>[i(t,{label:"备件编号",prop:"code"},{default:s((()=>[i(o,{modelValue:H.code,"onUpdate:modelValue":l[6]||(l[6]=e=>H.code=e),placeholder:"请输入备件编号"},null,8,["modelValue"])])),_:1}),i(t,{label:"备件名称",prop:"name"},{default:s((()=>[i(o,{modelValue:H.name,"onUpdate:modelValue":l[7]||(l[7]=e=>H.name=e),placeholder:"请输入备件名称"},null,8,["modelValue"])])),_:1}),i(t,{label:"备件类型",prop:"typeId"},{default:s((()=>[i(I,{modelValue:H.typeId,"onUpdate:modelValue":l[8]||(l[8]=e=>H.typeId=e),placeholder:"请选择备件类型",style:{width:"100%"}},{default:s((()=>[(f(!0),r(g,null,y(ee.value,(e=>(f(),m(k,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(t,{label:"规格型号",prop:"specification"},{default:s((()=>[i(o,{modelValue:H.specification,"onUpdate:modelValue":l[9]||(l[9]=e=>H.specification=e),placeholder:"请输入规格型号"},null,8,["modelValue"])])),_:1}),i(t,{label:"品牌",prop:"brand"},{default:s((()=>[i(o,{modelValue:H.brand,"onUpdate:modelValue":l[10]||(l[10]=e=>H.brand=e),placeholder:"请输入品牌"},null,8,["modelValue"])])),_:1}),i(t,{label:"单位",prop:"unit"},{default:s((()=>[i(o,{modelValue:H.unit,"onUpdate:modelValue":l[11]||(l[11]=e=>H.unit=e),placeholder:"请输入单位"},null,8,["modelValue"])])),_:1}),"add"===$.value?(f(),m(t,{key:0,label:"初始库存",prop:"initialStock"},{default:s((()=>[i(Y,{modelValue:H.initialStock,"onUpdate:modelValue":l[12]||(l[12]=e=>H.initialStock=e),min:0,placeholder:"请输入初始库存"},null,8,["modelValue"])])),_:1})):_("",!0),i(t,{label:"预警阈值",prop:"warningThreshold"},{default:s((()=>[i(Y,{modelValue:H.warningThreshold,"onUpdate:modelValue":l[13]||(l[13]=e=>H.warningThreshold=e),min:0,placeholder:"请输入预警阈值"},null,8,["modelValue"])])),_:1}),i(t,{label:"最小库存",prop:"minStock"},{default:s((()=>[i(Y,{modelValue:H.minStock,"onUpdate:modelValue":l[14]||(l[14]=e=>H.minStock=e),min:0,placeholder:"请输入最小库存"},null,8,["modelValue"])])),_:1}),i(t,{label:"库位",prop:"locationId"},{default:s((()=>[i(I,{modelValue:H.locationId,"onUpdate:modelValue":l[15]||(l[15]=e=>H.locationId=e),placeholder:"请选择库位",style:{width:"100%"}},{default:s((()=>[(f(!0),r(g,null,y(le.value,(e=>(f(),m(k,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(t,{label:"价格(元)",prop:"price"},{default:s((()=>[i(Y,{modelValue:H.price,"onUpdate:modelValue":l[16]||(l[16]=e=>H.price=e),min:0,precision:2,placeholder:"请输入价格"},null,8,["modelValue"])])),_:1}),i(t,{label:"备注",prop:"remarks"},{default:s((()=>[i(o,{modelValue:H.remarks,"onUpdate:modelValue":l[17]||(l[17]=e=>H.remarks=e),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"]),i(ae,{modelValue:R.value,"onUpdate:modelValue":l[27]||(l[27]=e=>R.value=e),title:"备件入库",width:"550px"},{footer:s((()=>[u("div",N,[i(a,{onClick:l[26]||(l[26]=e=>R.value=!1)},{default:s((()=>l[49]||(l[49]=[b("取消")]))),_:1}),i(a,{type:"primary",onClick:pe},{default:s((()=>l[50]||(l[50]=[b("确定")]))),_:1})])])),default:s((()=>[i(w,{ref_key:"inboundFormRef",ref:E,model:J,rules:X,"label-width":"100px"},{default:s((()=>[i(t,{label:"备件名称"},{default:s((()=>[i(o,{modelValue:L.value.name,"onUpdate:modelValue":l[20]||(l[20]=e=>L.value.name=e),disabled:""},null,8,["modelValue"])])),_:1}),i(t,{label:"当前库存"},{default:s((()=>[i(o,{value:`${L.value.stockQuantity} ${L.value.unit||""}`,disabled:""},null,8,["value"])])),_:1}),i(t,{label:"入库数量",prop:"quantity"},{default:s((()=>[i(Y,{modelValue:J.quantity,"onUpdate:modelValue":l[21]||(l[21]=e=>J.quantity=e),min:1},null,8,["modelValue"])])),_:1}),i(t,{label:"库位",prop:"locationId"},{default:s((()=>[i(I,{modelValue:J.locationId,"onUpdate:modelValue":l[22]||(l[22]=e=>J.locationId=e),placeholder:"请选择库位"},{default:s((()=>[(f(!0),r(g,null,y(le.value,(e=>(f(),m(k,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(t,{label:"入库类型",prop:"reasonType"},{default:s((()=>[i(I,{modelValue:J.reasonType,"onUpdate:modelValue":l[23]||(l[23]=e=>J.reasonType=e),placeholder:"请选择入库类型"},{default:s((()=>[i(k,{label:"采购入库",value:1}),i(k,{label:"退回入库",value:2}),i(k,{label:"盘点调整",value:5})])),_:1},8,["modelValue"])])),_:1}),i(t,{label:"关联单号",prop:"referenceNumber"},{default:s((()=>[i(o,{modelValue:J.referenceNumber,"onUpdate:modelValue":l[24]||(l[24]=e=>J.referenceNumber=e),placeholder:"请输入关联单号"},null,8,["modelValue"])])),_:1}),i(t,{label:"备注",prop:"remarks"},{default:s((()=>[i(o,{modelValue:J.remarks,"onUpdate:modelValue":l[25]||(l[25]=e=>J.remarks=e),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),i(ae,{modelValue:D.value,"onUpdate:modelValue":l[37]||(l[37]=e=>D.value=e),title:"备件出库",width:"550px"},{footer:s((()=>[u("div",O,[i(a,{onClick:l[36]||(l[36]=e=>D.value=!1)},{default:s((()=>l[51]||(l[51]=[b("取消")]))),_:1}),i(a,{type:"primary",onClick:me},{default:s((()=>l[52]||(l[52]=[b("确定")]))),_:1})])])),default:s((()=>[i(w,{ref_key:"outboundFormRef",ref:K,model:M,rules:Z,"label-width":"100px"},{default:s((()=>[i(t,{label:"备件名称"},{default:s((()=>[i(o,{modelValue:L.value.name,"onUpdate:modelValue":l[28]||(l[28]=e=>L.value.name=e),disabled:""},null,8,["modelValue"])])),_:1}),i(t,{label:"当前库存"},{default:s((()=>[i(o,{value:`${L.value.stockQuantity} ${L.value.unit||""}`,disabled:""},null,8,["value"])])),_:1}),i(t,{label:"出库数量",prop:"quantity"},{default:s((()=>[i(Y,{modelValue:M.quantity,"onUpdate:modelValue":l[29]||(l[29]=e=>M.quantity=e),min:1,max:L.value.stockQuantity},null,8,["modelValue","max"])])),_:1}),i(t,{label:"库位",prop:"locationId"},{default:s((()=>[i(I,{modelValue:M.locationId,"onUpdate:modelValue":l[30]||(l[30]=e=>M.locationId=e),placeholder:"请选择库位"},{default:s((()=>[(f(!0),r(g,null,y(le.value,(e=>(f(),m(k,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(t,{label:"出库类型",prop:"reasonType"},{default:s((()=>[i(I,{modelValue:M.reasonType,"onUpdate:modelValue":l[31]||(l[31]=e=>M.reasonType=e),placeholder:"请选择出库类型"},{default:s((()=>[i(k,{label:"领用出库",value:3}),i(k,{label:"报废出库",value:4}),i(k,{label:"盘点调整",value:5})])),_:1},8,["modelValue"])])),_:1}),i(t,{label:"关联单号",prop:"referenceNumber"},{default:s((()=>[i(o,{modelValue:M.referenceNumber,"onUpdate:modelValue":l[32]||(l[32]=e=>M.referenceNumber=e),placeholder:"请输入关联单号"},null,8,["modelValue"])])),_:1}),i(t,{label:"关联资产",prop:"relatedAssetId"},{default:s((()=>[i(o,{modelValue:M.relatedAssetId,"onUpdate:modelValue":l[33]||(l[33]=e=>M.relatedAssetId=e),placeholder:"请输入关联资产ID"},null,8,["modelValue"])])),_:1}),i(t,{label:"关联故障",prop:"relatedFaultId"},{default:s((()=>[i(o,{modelValue:M.relatedFaultId,"onUpdate:modelValue":l[34]||(l[34]=e=>M.relatedFaultId=e),placeholder:"请输入关联故障ID"},null,8,["modelValue"])])),_:1}),i(t,{label:"备注",prop:"remarks"},{default:s((()=>[i(o,{modelValue:M.remarks,"onUpdate:modelValue":l[35]||(l[35]=e=>M.remarks=e),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-183b0513"]]);export{Q as default};
