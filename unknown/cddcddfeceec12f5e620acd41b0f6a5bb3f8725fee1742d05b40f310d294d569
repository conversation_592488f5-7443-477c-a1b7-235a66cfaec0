# 企业级智能资产管理平台 - 最终实施方案

**版本: 4.0 (最终版)**
**核心策略: 核心模块冻结，新功能模块化、现代化开发。**
**交付物: 包含数据库、后端、前端的完整、可执行的升级改造步骤。**

---

## **第一部分：数据库与后端基础建设 (Phase 1)**

**目标**：为所有上层应用和分析功能，构建一个高性能、高扩展性的数据与服务基础。

### **步骤 1.1：数据库架构升级**

**说明**：此步骤将为新的返修流程和数据分析功能创建必要的数据库表，并安全地处理旧有数据。

#### **1.1.1 创建新表 (SQL)**
**操作**: 在 `itassets` 数据库中执行以下SQL脚本。
**目的**: 创建 `RepairOrders`, `RepairItems`, `AssetSnapshots` 表。

```sql
-- File: /database/migrations/phase1_01_create_new_tables.sql
-- Description: 为新的返修流程和数据分析功能创建表结构

-- 返修单主表 (替代原有的 returntofactories)
CREATE TABLE `RepairOrders` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `OrderCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '返修单号 (REP-YYYYMMDD-XXX)',
  `SupplierId` int NOT NULL COMMENT '维修服务商ID',
  `SendDate` datetime NULL DEFAULT NULL COMMENT '返厂寄出日期',
  `ExpectedReturnDate` datetime NULL DEFAULT NULL COMMENT '预计返回日期',
  `ActualReturnDate` datetime NULL DEFAULT NULL COMMENT '实际返回日期',
  `TotalCost` decimal(18,2) NULL DEFAULT 0.00 COMMENT '维修总费用',
  `Status` int NOT NULL DEFAULT 0 COMMENT '返修单状态: 0-草稿, 1-待返厂, 2-维修中, 3-部分返回, 4-已完成',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatorId` int NOT NULL COMMENT '创建人ID',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_RepairOrders_OrderCode` (`OrderCode`),
  KEY `IX_RepairOrders_SupplierId` (`SupplierId`),
  KEY `IX_RepairOrders_CreatorId` (`CreatorId`),
  CONSTRAINT `FK_RepairOrders_Suppliers_SupplierId` FOREIGN KEY (`SupplierId`) REFERENCES `suppliers` (`Id`) ON DELETE RESTRICT,
  CONSTRAINT `FK_RepairOrders_Users_CreatorId` FOREIGN KEY (`CreatorId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT
) ENGINE=InnoDB COMMENT='返厂维修单主表';

-- 返修单明细表
CREATE TABLE `RepairItems` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `RepairOrderId` int NOT NULL COMMENT '关联返修单主表ID',
  `AssetId` int NOT NULL COMMENT '关联资产ID',
  `FaultRecordId` int NULL DEFAULT NULL COMMENT '关联故障单ID',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '故障描述补充',
  `RepairCost` decimal(18,2) NULL DEFAULT 0.00 COMMENT '单项维修费用',
  `RepairStatus` int NOT NULL DEFAULT 0 COMMENT '维修状态: 0-待送修, 1-检测中, 2-维修中, 3-待返回, 4-已修复, 5-修复失败',
  `RepairResult` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '维修结果描述',
  PRIMARY KEY (`Id`),
  KEY `IX_RepairItems_RepairOrderId` (`RepairOrderId`),
  KEY `IX_RepairItems_AssetId` (`AssetId`),
  KEY `IX_RepairItems_FaultRecordId` (`FaultRecordId`),
  CONSTRAINT `FK_RepairItems_RepairOrders_RepairOrderId` FOREIGN KEY (`RepairOrderId`) REFERENCES `RepairOrders` (`Id`) ON DELETE CASCADE,
  CONSTRAINT `FK_RepairItems_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT,
  CONSTRAINT `FK_RepairItems_FaultRecords_FaultRecordId` FOREIGN KEY (`FaultRecordId`) REFERENCES `faultrecords` (`Id`) ON DELETE SET NULL
) ENGINE=InnoDB COMMENT='返厂维修单明细表';

-- 资产历史数据快照表
CREATE TABLE `AssetSnapshots` (
  `Id` bigint NOT NULL AUTO_INCREMENT,
  `SnapshotDate` date NOT NULL COMMENT '快照日期',
  `AssetId` int NOT NULL COMMENT '资产ID',
  `AssetCode` varchar(30) NOT NULL,
  `FinancialCode` varchar(50) NULL DEFAULT NULL,
  `AssetName` varchar(100) NOT NULL,
  `AssetTypeId` int NOT NULL,
  `LocationId` int NULL DEFAULT NULL,
  `DepartmentId` int NULL DEFAULT NULL,
  `Status` int NOT NULL,
  `Price` decimal(18,2) NULL DEFAULT NULL,
  `PurchaseDate` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `UK_AssetSnapshots_Date_AssetId` (`SnapshotDate`, `AssetId`)
) ENGINE=InnoDB COMMENT='资产历史数据快照表';
```

#### **1.1.2 数据迁移 (SQL, 可选)**
**操作**: 如果需要保留旧的返厂记录，请执行此脚本。

```sql
-- File: /database/migrations/phase1_02_migrate_data.sql
-- Description: 将旧返厂表数据迁移至新表

INSERT INTO RepairOrders (OrderCode, SupplierId, SendDate, ActualReturnDate, TotalCost, Status, Notes, CreatorId, CreatedAt, UpdatedAt)
SELECT Code, SupplierId, SendTime, ActualReturnTime, RepairCost,
    CASE Status WHEN 0 THEN 1 WHEN 1 THEN 2 WHEN 2 THEN 2 WHEN 3 THEN 4 ELSE 0 END,
    Notes, SenderId, CreatedAt, UpdatedAt
FROM returntofactories;

INSERT INTO RepairItems (RepairOrderId, AssetId, FaultRecordId, Description, RepairCost, RepairStatus, RepairResult)
SELECT
    (SELECT Id FROM RepairOrders ro WHERE ro.OrderCode = rtf.Code),
    rtf.AssetId, rtf.FaultRecordId, rtf.Notes, rtf.RepairCost,
    CASE Status WHEN 3 THEN 4 WHEN 4 THEN 5 ELSE 2 END,
    rtf.RepairResult
FROM returntofactories rtf;
```

#### **1.1.3 移除旧表 (SQL)**
**操作**: 确认数据迁移无误后，执行此脚本以保持数据库结构整洁。

```sql
-- File: /database/migrations/phase1_03_drop_old_table.sql
-- Description: 移除已废弃的旧返厂表

-- 建议先重命名以作备份: RENAME TABLE returntofactories TO _backup_returntofactories;
DROP TABLE returntofactories;
```

---

### **步骤 1.2：后端代码重构与新增**

**说明**: 此步骤将更新后端代码以匹配新的数据库模型，并创建新的API。所有新代码都遵循 Clean Architecture 和您定义的强制规范。

#### **1.2.1 更新 `Domain` 层**
**操作**: 在 `src/Domain/Entities/` 目录下创建新实体。

```csharp
// File: src/Domain/Entities/RepairOrder.cs
// Description: 返厂维修单主实体
namespace ItAssetsSystem.Domain.Entities;

public class RepairOrder
{
    public int Id { get; set; }
    public string OrderCode { get; set; }
    public int SupplierId { get; set; }
    public DateTime? SendDate { get; set; }
    public DateTime? ExpectedReturnDate { get; set; }
    public DateTime? ActualReturnDate { get; set; }
    public decimal TotalCost { get; set; }
    public int Status { get; set; }
    public string? Notes { get; set; }
    public int CreatorId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }

    public virtual Supplier Supplier { get; set; }
    public virtual User Creator { get; set; }
    public virtual ICollection<RepairItem> RepairItems { get; set; } = new List<RepairItem>();
}
```

```csharp
// File: src/Domain/Entities/RepairItem.cs
// Description: 返厂维修单明细实体
namespace ItAssetsSystem.Domain.Entities;

public class RepairItem
{
    public int Id { get; set; }
    public int RepairOrderId { get; set; }
    public int AssetId { get; set; }
    public int? FaultRecordId { get; set; }
    public string? Description { get; set; }
    public decimal RepairCost { get; set; }
    public int RepairStatus { get; set; }
    public string? RepairResult { get; set; }

    public virtual RepairOrder RepairOrder { get; set; }
    public virtual Asset Asset { get; set; }
    public virtual FaultRecord? FaultRecord { get; set; }
}
```

```csharp
// File: src/Domain/Entities/AssetSnapshot.cs
// Description: 资产历史数据快照实体
namespace ItAssetsSystem.Domain.Entities;

public class AssetSnapshot
{
    public long Id { get; set; }
    public DateTime SnapshotDate { get; set; }
    public int AssetId { get; set; }
    public string AssetCode { get; set; }
    public string? FinancialCode { get; set; }
    public string AssetName { get; set; }
    public int AssetTypeId { get; set; }
    public int? LocationId { get; set; }
    public int? DepartmentId { get; set; }
    public int Status { get; set; }
    public decimal? Price { get; set; }
    public DateTime? PurchaseDate { get; set; }
}
```

#### **1.2.2 更新 `Infrastructure` 层**
**操作**: 更新 `AppDbContext` 以识别新模型。

```csharp
// File: src/Infrastructure/Persistence/AppDbContext.cs
// Description: 更新数据库上下文以包含新实体

using Microsoft.EntityFrameworkCore;
using ItAssetsSystem.Domain.Entities;
using System.Reflection;

namespace ItAssetsSystem.Infrastructure.Persistence;

public class AppDbContext : DbContext
{
    public AppDbContext(DbContextOptions<AppDbContext> options) : base(options) {}

    // ... 其他所有现有 DbSet ...

    // 移除旧的 DbSet
    // public DbSet<ReturnToFactory> ReturnToFactories { get; set; } 

    // 添加新的 DbSet
    public DbSet<RepairOrder> RepairOrders { get; set; }
    public DbSet<RepairItem> RepairItems { get; set; }
    public DbSet<AssetSnapshot> AssetSnapshots { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        // 此方法会自动加载所有实现了 IEntityTypeConfiguration 的配置类
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
    }
}
```

**操作**: 在 `src/Infrastructure/Persistence/Configurations/` 目录下为新实体创建 Fluent API 配置。

```csharp
// File: src/Infrastructure/Persistence/Configurations/RepairOrderConfiguration.cs
// Description: RepairOrder 实体的 EF Core Fluent API 配置
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ItAssetsSystem.Domain.Entities;

namespace ItAssetsSystem.Infrastructure.Persistence.Configurations;

public class RepairOrderConfiguration : IEntityTypeConfiguration<RepairOrder>
{
    public void Configure(EntityTypeBuilder<RepairOrder> builder)
    {
        builder.ToTable("RepairOrders");
        builder.HasKey(e => e.Id);
        builder.HasIndex(e => e.OrderCode).IsUnique();
        builder.Property(e => e.OrderCode).IsRequired().HasMaxLength(50);
        builder.HasOne(e => e.Supplier).WithMany().HasForeignKey(e => e.SupplierId).OnDelete(DeleteBehavior.Restrict);
        builder.HasOne(e => e.Creator).WithMany().HasForeignKey(e => e.CreatorId).OnDelete(DeleteBehavior.Restrict);
    }
}

// File: src/Infrastructure/Persistence/Configurations/RepairItemConfiguration.cs
// Description: RepairItem 实体的 EF Core Fluent API 配置
// ... 类似地为 RepairItem 和 AssetSnapshot 创建配置类 ...
```

#### **1.2.3 新增 `Application` 层和 `Api` 层代码**
**操作**: 创建新的服务和API控制器。由于代码量巨大，此处提供核心骨架和逻辑。您需要创建相应的DTOs, Commands, Queries, Handlers。

```csharp
// File: src/Api/Endpoints/V2/StatisticsEndpoints.cs
// Description: 新的 V2 统计API端点
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using ItAssetsSystem.Application.Features.Statistics.Queries; // 假设的查询

namespace ItAssetsSystem.Api.Endpoints.V2;

public static class StatisticsEndpoints
{
    public static void MapStatisticsEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/v2/statistics").WithTags("Statistics V2");

        group.MapPost("/query", async (DynamicStatisticsQuery query, ISender sender) =>
        {
            var result = await sender.Send(query);
            return Results.Ok(result);
        })
        .WithName("ExecuteDynamicStatisticsQuery")
        .Produces<object>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest);
    }
}
```

**操作**: 在 `Program.cs` (或 `Startup.cs`) 中注册新的服务和后台任务，并映射新的API端点。

```csharp
// File: src/Api/Program.cs (片段)
// ...
// builder.Services.AddScoped<IRepairOrderService, RepairOrderService>();
// builder.Services.AddScoped<IDynamicStatisticsService, DynamicStatisticsService>();
// builder.Services.AddHostedService<AssetSnapshotService>();
// ...
// app.MapStatisticsEndpoints();
// app.MapRepairOrdersEndpoints(); // 新的返修模块API
// ...
```

---
## **第二阶段：前端革命 - 构建企业级“资产分析工作台”**

**目标**：提供一个视觉效果卓越、交互体验一流的单页面应用（SPA）原型，作为新分析模块的最终形态。

**操作**: 使用以下完整代码替换您项目中现有的报表或仪表盘页面。这是一个独立的、可直接在浏览器中运行的文件。


```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业级智能资产分析平台</title>
    <script src="[https://cdn.tailwindcss.com](https://cdn.tailwindcss.com)"></script>
    <script src="[https://cdn.jsdelivr.net/npm/chart.js](https://cdn.jsdelivr.net/npm/chart.js)"></script>
    <link href="[https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap](https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap)" rel="stylesheet">
    <style>
        body { font-family: 'Noto Sans SC', sans-serif; background-color: #f0f2f5; }
        .chart-container { position: relative; width: 100%; height: 350px; max-height: 400px; }
        ::-webkit-scrollbar { width: 6px; height: 6px; }
        ::-webkit-scrollbar-track { background: #f1f1f1; }
        ::-webkit-scrollbar-thumb { background: #ccc; border-radius: 3px; }
        ::-webkit-scrollbar-thumb:hover { background: #aaa; }
        .control-panel select:focus, .control-panel input:focus { box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5); }
        .kpi-card { transition: transform 0.2s, box-shadow 0.2s; }
        .kpi-card:hover { transform: translateY(-4px); box-shadow: 0 10px 20px -5px rgba(0, 0, 0, 0.1); }
    </style>
</head>
<body class="text-gray-800">
    <div class="flex h-screen bg-[#f0f2f5]">
        <aside class="w-1/4 max-w-xs bg-white p-6 shadow-lg overflow-y-auto flex-shrink-0 border-r border-gray-200 control-panel">
            <div class="flex items-center mb-8">
                <div class="bg-blue-600 text-white rounded-lg p-2 shadow-md">
                    <svg xmlns="[http://www.w3.org/2000/svg](http://www.w3.org/2000/svg)" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V7a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>
                </div>
                <h1 class="text-xl font-bold ml-3 text-gray-900">资产分析工作台</h1>
            </div>
            
            <div id="filters-panel">
                <div class="mb-6">
                    <label for="dimension-select" class="block text-sm font-semibold text-gray-700 mb-2">分析维度</label>
                    <select id="dimension-select" class="w-full p-2 border border-gray-300 rounded-md bg-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"></select>
                </div>
                <div class="mb-6">
                    <label for="metric-select" class="block text-sm font-semibold text-gray-700 mb-2">度量指标</label>
                    <select id="metric-select" class="w-full p-2 border border-gray-300 rounded-md bg-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"></select>
                </div>
                <div class="mb-6">
                    <label class="block text-sm font-semibold text-gray-700 mb-2">资产状态</label>
                    <div id="status-filter" class="space-y-2"></div>
                </div>
                 <div class="mb-6">
                    <label class="block text-sm font-semibold text-gray-700 mb-2">部门</label>
                     <select id="department-filter" class="w-full p-2 border border-gray-300 rounded-md bg-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"></select>
                </div>
            </div>
            <button id="reset-filters-btn" class="w-full mt-6 bg-gray-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-gray-700 transition duration-300">重置筛选</button>
        </aside>

        <main class="flex-1 p-6 md:p-8 overflow-y-auto">
            <header class="mb-6">
                <h2 id="dashboard-title" class="text-3xl font-bold text-gray-900">资产全局洞察</h2>
                <p id="dashboard-subtitle" class="text-gray-500 mt-1">在左侧面板中选择维度和筛选器以开始探索。</p>
            </header>

            <section id="kpi-container" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"></section>

            <section class="bg-white p-6 rounded-lg shadow-md">
                <h3 id="primary-chart-title" class="text-lg font-semibold mb-4 text-gray-800">资产分布</h3>
                <div class="chart-container">
                    <canvas id="mainChart"></canvas>
                </div>
            </section>

            <section class="bg-white p-6 rounded-lg shadow-md mt-8">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">数据明细</h3>
                    <span id="grid-summary" class="text-sm text-gray-500"></span>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full text-sm text-left text-gray-600">
                        <thead id="data-grid-head" class="text-xs text-gray-700 uppercase bg-gray-50"></thead>
                        <tbody id="data-grid-body"></tbody>
                    </table>
                </div>
            </section>
        </main>
    </div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const masterData = [
        { id: 1, assetCode: 'AST-CMP-001', name: '开发笔记本A', category: '电脑设备', department: '技术部', location: '研发中心', status: '在用', value: 1.2 }, { id: 2, assetCode: 'AST-SVR-001', name: '数据库服务器', category: '服务器', department: '技术部', location: '数据中心', status: '在用', value: 8.5 }, { id: 3, assetCode: 'AST-CMP-002', name: '销售笔记本B', category: '电脑设备', department: '销售部', location: '销售办公室', status: '在用', value: 0.8 }, { id: 4, assetCode: 'AST-PRN-001', name: '财务打印机', category: '办公设备', department: '财务部', location: '总部10F', status: '维修中', value: 0.5 }, { id: 5, assetCode: 'AST-CMP-003', name: '闲置笔记本C', category: '电脑设备', department: '技术部', location: '仓库', status: '闲置', value: 0.6 }, { id: 6, assetCode: 'AST-SVR-002', name: 'Web服务器', category: '服务器', department: '技术部', location: '数据中心', status: '在用', value: 5.0 }, { id: 7, assetCode: 'AST-DSK-001', name: '市场部工位', category: '办公家具', department: '市场部', location: '总部11F', status: '在用', value: 0.2 }, { id: 8, assetCode: 'AST-DSK-002', name: '销售部工位', category: '办公家具', department: '销售部', location: '销售办公室', status: '报废', value: 0.15 }, { id: 9, assetCode: 'AST-CMP-004', name: '销售笔记本D', category: '电脑设备', department: '销售部', location: '销售办公室', status: '在用', value: 0.9 }, { id: 10, assetCode: 'AST-PRJ-001', name: '会议室投影仪', category: '办公设备', department: '行政部', location: '会议室A', status: '在用', value: 1.5 },
    ];
    const config = {
        dimensions: [{ id: 'department', name: '部门' }, { id: 'category', name: '资产类别' }, { id: 'location', name: '位置' }],
        metrics: [{ id: 'count', name: '资产数量' }, { id: 'value', name: '资产总值 (万元)' }],
        statuses: ['在用', '闲置', '维修中', '报废'],
        departments: ['技术部', '销售部', '市场部', '行政部', '财务部']
    };
    let state = { dimension: 'department', metric: 'count', statusFilter: new Set(config.statuses), departmentFilter: 'all', chartFilter: null };
    let mainChart;

    const dimensionSelect = document.getElementById('dimension-select');
    const metricSelect = document.getElementById('metric-select');
    const statusFilterContainer = document.getElementById('status-filter');
    const departmentFilterSelect = document.getElementById('department-filter');
    const kpiContainer = document.getElementById('kpi-container');
    const chartTitle = document.getElementById('primary-chart-title');
    const gridHead = document.getElementById('data-grid-head');
    const gridBody = document.getElementById('data-grid-body');
    const gridSummary = document.getElementById('grid-summary');
    const dashboardTitle = document.getElementById('dashboard-title');
    const dashboardSubtitle = document.getElementById('dashboard-subtitle');
    const resetBtn = document.getElementById('reset-filters-btn');

    function processData() {
        const filteredData = masterData.filter(d => state.statusFilter.has(d.status) && (state.departmentFilter === 'all' || d.department === state.departmentFilter) && (!state.chartFilter || d[state.chartFilter.dimension] === state.chartFilter.value));
        const aggregation = filteredData.reduce((acc, item) => {
            const key = item[state.dimension];
            if (!acc[key]) acc[key] = { count: 0, value: 0 };
            acc[key].count++; acc[key].value += item.value;
            return acc;
        }, {});
        const sorted = Object.entries(aggregation).sort((a, b) => b[1][state.metric] - a[1][state.metric]);
        return { filtered: filteredData, aggregated: { labels: sorted.map(item => item[0]), values: sorted.map(item => item[1][state.metric]) } };
    }

    function renderControls() {
        dimensionSelect.innerHTML = config.dimensions.map(d => `<option value="${d.id}" ${state.dimension === d.id ? 'selected' : ''}>${d.name}</option>`).join('');
        metricSelect.innerHTML = config.metrics.map(m => `<option value="${m.id}" ${state.metric === m.id ? 'selected' : ''}>${m.name}</option>`).join('');
        statusFilterContainer.innerHTML = config.statuses.map(status => `<label class="flex items-center space-x-2 text-sm cursor-pointer hover:text-blue-600"><input type="checkbox" value="${status}" class="status-checkbox h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500" ${state.statusFilter.has(status) ? 'checked' : ''}><span>${status}</span></label>`).join('');
        departmentFilterSelect.innerHTML = `<option value="all">所有部门</option>` + config.departments.map(d => `<option value="${d}" ${state.departmentFilter === d ? 'selected' : ''}>${d}</option>`).join('');
    }

    function renderKPIs(data) {
        const totalValue = data.reduce((sum, d) => sum + d.value, 0);
        const activeCount = data.filter(d => d.status === '在用').length;
        const activeRate = data.length > 0 ? ((activeCount / data.length) * 100).toFixed(1) : 0;
        kpiContainer.innerHTML = `<div class="bg-white p-5 rounded-lg shadow-sm kpi-card"><p class="text-sm text-gray-500">资产总数</p><p class="text-3xl font-bold text-gray-900">${data.length.toLocaleString()}</p></div><div class="bg-white p-5 rounded-lg shadow-sm kpi-card"><p class="text-sm text-gray-500">资产总值</p><p class="text-3xl font-bold text-gray-900">${totalValue.toFixed(2)}<span class="text-base font-normal text-gray-600">万</span></p></div><div class="bg-white p-5 rounded-lg shadow-sm kpi-card"><p class="text-sm text-gray-500">在用率</p><p class="text-3xl font-bold text-green-600">${activeRate}%</p></div><div class="bg-white p-5 rounded-lg shadow-sm kpi-card"><p class="text-sm text-gray-500">维修中</p><p class="text-3xl font-bold text-orange-500">${data.filter(d => d.status === '维修中').length}</p></div>`;
    }

    function renderChart({ labels, values }) {
        const metricName = config.metrics.find(m => m.id === state.metric).name;
        const dimensionName = config.dimensions.find(d => d.id === state.dimension).name;
        chartTitle.textContent = `按${dimensionName}分析 - ${metricName}`;
        const ctx = document.getElementById('mainChart').getContext('2d');
        if (mainChart) mainChart.destroy();
        mainChart = new Chart(ctx, { type: 'bar', data: { labels, datasets: [{ label: metricName, data: values, backgroundColor: 'rgba(59, 130, 246, 0.7)', borderColor: 'rgba(59, 130, 246, 1)', borderWidth: 1, borderRadius: 5 }] }, options: { responsive: true, maintainAspectRatio: false, indexAxis: 'y', scales: { x: { beginAtZero: true, grid: { color: '#e5e7eb' } }, y: { grid: { display: false } } }, plugins: { legend: { display: false }, tooltip: { backgroundColor: '#fff', titleColor: '#1f2937', bodyColor: '#4b5563', borderColor: '#e5e7eb', borderWidth: 1, padding: 10, cornerRadius: 5 } }, onClick: (_, e) => { const a = mainChart.getElementsAtEventForMode(e, 'nearest', { intersect: true }, true); if (a.length) { state.chartFilter = { dimension: state.dimension, value: labels[a[0].index] }; updateDashboard(); } } } });
    }

    function renderGrid(data) {
        gridHead.innerHTML = `<tr>${['资产编号', '名称', '类别', '部门', '位置', '状态', '价值(万)'].map(h => `<th scope="col" class="px-6 py-3">${h}</th>`).join('')}</tr>`;
        gridBody.innerHTML = data.map(item => `
            <tr class="bg-white border-b hover:bg-gray-50">
                <td class="px-6 py-4 font-medium text-blue-600">${item.assetCode}</td><td class="px-6 py-4">${item.name}</td>
                <td class="px-6 py-4">${item.category}</td><td class="px-6 py-4">${item.department}</td>
                <td class="px-6 py-4">${item.location}</td>
                <td class="px-6 py-4"><span class="px-2 py-1 text-xs font-medium rounded-full ${ { '在用': 'bg-green-100 text-green-800', '闲置': 'bg-yellow-100 text-yellow-800', '维修中': 'bg-orange-100 text-orange-800', '报废': 'bg-gray-200 text-gray-800' }[item.status] }">${item.status}</span></td>
                <td class="px-6 py-4 text-right">${item.value.toFixed(2)}</td>
            </tr>`).join('') || `<tr><td colspan="7" class="text-center py-10 text-gray-500">没有符合条件的数据</td></tr>`;
        gridSummary.textContent = `显示 ${data.length} 条记录`;
    }

    function updateDashboardTitle() {
        if (state.chartFilter) {
            const dimName = config.dimensions.find(d => d.id === state.chartFilter.dimension).name;
            dashboardTitle.textContent = `${state.chartFilter.value} - 资产洞察`;
            dashboardSubtitle.textContent = `当前已下钻 "${dimName}" 维度至 "${state.chartFilter.value}"。点击“重置筛选”返回。`;
        } else if (state.departmentFilter !== 'all') {
            dashboardTitle.textContent = `${state.departmentFilter} - 资产洞察`;
            dashboardSubtitle.textContent = `当前已筛选“部门”为“${state.departmentFilter}”。`;
        } else {
            dashboardTitle.textContent = '资产全局洞察';
            dashboardSubtitle.textContent = '当前展示所有资产的概览。您可以在左侧面板进行筛选和维度切换。';
        }
    }

    function updateDashboard() {
        const { filtered, aggregated } = processData();
        renderKPIs(filtered);
        renderChart(aggregated);
        renderGrid(filtered);
        updateDashboardTitle();
    }

    function handleFilterChange() {
        state.dimension = dimensionSelect.value;
        state.metric = metricSelect.value;
        state.departmentFilter = departmentFilterSelect.value;
        state.statusFilter = new Set(Array.from(document.querySelectorAll('.status-checkbox:checked')).map(el => el.value));
        if (state.chartFilter && state.chartFilter.dimension !== state.dimension) {
            state.chartFilter = null; // Reset chart filter if main dimension changes
        }
        updateDashboard();
    }

    function resetAllFilters() {
        state = { dimension: 'department', metric: 'count', statusFilter: new Set(config.statuses), departmentFilter: 'all', chartFilter: null };
        renderControls();
        updateDashboard();
    }
    
    dimensionSelect.addEventListener('change', handleFilterChange);
    metricSelect.addEventListener('change', handleFilterChange);
    statusFilterContainer.addEventListener('change', handleFilterChange);
    departmentFilterSelect.addEventListener('change', handleFilterChange);
    resetBtn.addEventListener('click', resetAllFilters);

    renderControls();
    updateDashboard();
});
</script>
</body>
</html>
