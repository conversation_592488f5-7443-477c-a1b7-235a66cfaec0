-- 位置部门继承正确SQL解决方案
-- 解决位置如果没有DefaultDepartmentId时从父位置继承的问题

-- 方案1: 使用完整的CTE查询（推荐方案）
WITH RECURSIVE location_department_hierarchy AS (
    -- 基础情况：获取所有位置及其直接部门分配
    SELECT 
        l.Id as LocationId,
        l.Name as LocationName,
        l.ParentId,
        l.DefaultDepartmentId,
        l.Type as LocationType,
        l.Path,
        -- 如果有直接部门分配，使用它；否则标记为需要继承
        CASE WHEN l.DefaultDepartmentId IS NOT NULL 
             THEN l.DefaultDepartmentId 
             ELSE NULL 
        END as InheritedDepartmentId,
        0 as Level
    FROM locations l
    WHERE l.ParentId IS NULL  -- 从根节点开始
    
    UNION ALL
    
    -- 递归情况：处理子位置的部门继承
    SELECT 
        child.Id as LocationId,
        child.Name as LocationName,
        child.ParentId,
        child.DefaultDepartmentId,
        child.Type as LocationType,
        child.Path,
        -- 如果子位置有直接部门分配，使用它；否则继承父位置的部门
        CASE WHEN child.DefaultDepartmentId IS NOT NULL 
             THEN child.DefaultDepartmentId 
             ELSE parent.InheritedDepartmentId 
        END as InheritedDepartmentId,
        parent.Level + 1
    FROM locations child
    INNER JOIN location_department_hierarchy parent ON child.ParentId = parent.LocationId
)
-- 完整的资产查询，包含部门继承逻辑
SELECT 
    a.Id, 
    a.AssetCode, 
    a.FinancialCode,
    a.Name, 
    a.Model, 
    a.Brand, 
    a.SerialNumber,
    a.PurchaseDate,
    a.PurchasePrice,
    a.Status,
    at.Name AS assetTypeName,
    ldh.LocationName,
    ldh.LocationType,
    d.Name AS departmentName,
    d.Id AS departmentId,
    a.CreatedAt as createdAt, 
    a.UpdatedAt as updatedAt
FROM assets a
    LEFT JOIN assettypes at ON a.AssetTypeId = at.Id
    LEFT JOIN location_department_hierarchy ldh ON a.LocationId = ldh.LocationId
    LEFT JOIN departments d ON ldh.InheritedDepartmentId = d.Id
ORDER BY a.Id;

-- 方案2: 使用函数方式（如果不支持CTE）
-- 创建一个函数来获取位置的继承部门ID
DELIMITER //
CREATE FUNCTION GetLocationInheritedDepartment(location_id INT) 
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE dept_id INT DEFAULT NULL;
    DECLARE parent_id INT DEFAULT NULL;
    DECLARE current_id INT DEFAULT location_id;
    DECLARE depth INT DEFAULT 0;
    
    -- 最多向上查找10层，防止无限循环
    WHILE current_id IS NOT NULL AND depth < 10 DO
        -- 检查当前位置是否有部门分配
        SELECT DefaultDepartmentId, ParentId 
        INTO dept_id, parent_id
        FROM locations 
        WHERE Id = current_id;
        
        -- 如果找到部门分配，返回
        IF dept_id IS NOT NULL THEN
            RETURN dept_id;
        END IF;
        
        -- 否则向上一层查找
        SET current_id = parent_id;
        SET depth = depth + 1;
    END WHILE;
    
    RETURN NULL;
END //
DELIMITER ;

-- 使用函数的查询方式
SELECT 
    a.Id, 
    a.AssetCode, 
    a.FinancialCode,
    a.Name, 
    a.Model, 
    a.Brand, 
    a.SerialNumber,
    a.PurchaseDate,
    a.PurchasePrice,
    a.Status,
    at.Name AS assetTypeName,
    l.Name AS locationName,
    l.Type AS locationType,
    d.Name AS departmentName,
    d.Id AS departmentId,
    a.CreatedAt as createdAt, 
    a.UpdatedAt as updatedAt
FROM assets a
    LEFT JOIN assettypes at ON a.AssetTypeId = at.Id
    LEFT JOIN locations l ON a.LocationId = l.Id
    LEFT JOIN departments d ON GetLocationInheritedDepartment(l.Id) = d.Id
ORDER BY a.Id;

-- 方案3: 使用子查询方式（兼容性最好）
SELECT 
    a.Id, 
    a.AssetCode, 
    a.FinancialCode,
    a.Name, 
    a.Model, 
    a.Brand, 
    a.SerialNumber,
    a.PurchaseDate,
    a.PurchasePrice,
    a.Status,
    at.Name AS assetTypeName,
    l.Name AS locationName,
    l.Type AS locationType,
    d.Name AS departmentName,
    d.Id AS departmentId,
    a.CreatedAt as createdAt, 
    a.UpdatedAt as updatedAt
FROM assets a
    LEFT JOIN assettypes at ON a.AssetTypeId = at.Id
    LEFT JOIN locations l ON a.LocationId = l.Id
    LEFT JOIN departments d ON d.Id = (
        -- 子查询：查找位置的继承部门ID
        SELECT COALESCE(
            l1.DefaultDepartmentId,
            l2.DefaultDepartmentId, 
            l3.DefaultDepartmentId,
            l4.DefaultDepartmentId,
            l5.DefaultDepartmentId
        )
        FROM locations l1
            LEFT JOIN locations l2 ON l1.ParentId = l2.Id
            LEFT JOIN locations l3 ON l2.ParentId = l3.Id  
            LEFT JOIN locations l4 ON l3.ParentId = l4.Id
            LEFT JOIN locations l5 ON l4.ParentId = l5.Id
        WHERE l1.Id = l.Id
    )
ORDER BY a.Id;

-- 统计查询示例：按部门统计资产（使用继承部门）
WITH RECURSIVE location_department_hierarchy AS (
    SELECT 
        l.Id as LocationId,
        l.DefaultDepartmentId,
        l.ParentId,
        CASE WHEN l.DefaultDepartmentId IS NOT NULL 
             THEN l.DefaultDepartmentId 
             ELSE NULL 
        END as InheritedDepartmentId,
        0 as Level
    FROM locations l
    WHERE l.ParentId IS NULL
    
    UNION ALL
    
    SELECT 
        child.Id as LocationId,
        child.DefaultDepartmentId,
        child.ParentId,
        CASE WHEN child.DefaultDepartmentId IS NOT NULL 
             THEN child.DefaultDepartmentId 
             ELSE parent.InheritedDepartmentId 
        END as InheritedDepartmentId,
        parent.Level + 1
    FROM locations child
    INNER JOIN location_department_hierarchy parent ON child.ParentId = parent.LocationId
)
SELECT 
    d.Name as DepartmentName,
    COUNT(a.Id) as AssetCount,
    at.Name as AssetTypeName,
    COUNT(CASE WHEN a.Status = 1 THEN 1 END) as ActiveAssets,
    COUNT(CASE WHEN a.Status = 0 THEN 1 END) as InactiveAssets
FROM assets a
    LEFT JOIN assettypes at ON a.AssetTypeId = at.Id
    LEFT JOIN location_department_hierarchy ldh ON a.LocationId = ldh.LocationId
    LEFT JOIN departments d ON ldh.InheritedDepartmentId = d.Id
WHERE d.Id IS NOT NULL
GROUP BY d.Id, d.Name, at.Id, at.Name
ORDER BY d.Name, at.Name;