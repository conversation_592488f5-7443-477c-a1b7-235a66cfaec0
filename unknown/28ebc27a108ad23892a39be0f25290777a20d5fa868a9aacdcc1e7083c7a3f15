function t(t,e="YYYY-MM-DD HH:mm:ss"){if(!t)return"";const r=new Date(t);if(isNaN(r.getTime()))return"";const a=r.getFullYear(),n=String(r.getMonth()+1).padStart(2,"0"),g=String(r.getDate()).padStart(2,"0"),p=String(r.getHours()).padStart(2,"0"),i=String(r.getMinutes()).padStart(2,"0"),s=String(r.getSeconds()).padStart(2,"0");return e.replace("YYYY",a).replace("MM",n).replace("DD",g).replace("HH",p).replace("mm",i).replace("ss",s)}export{t as f};
