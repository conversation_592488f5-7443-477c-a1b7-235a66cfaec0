using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Threading.Tasks;
using ItAssetsSystem.Core.Configuration;

namespace ItAssetsSystem.Controllers
{
    /// <summary>
    /// API版本管理控制器
    /// 提供API版本切换和配置管理功能
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ApiVersionController : ControllerBase
    {
        private readonly ApiVersionConfiguration _versionConfig;
        private readonly ILogger<ApiVersionController> _logger;

        public ApiVersionController(
            IOptions<ApiVersionConfiguration> versionConfig,
            ILogger<ApiVersionController> logger)
        {
            _versionConfig = versionConfig.Value;
            _logger = logger;
        }

        /// <summary>
        /// 获取当前API版本配置
        /// </summary>
        /// <returns>版本配置信息</returns>
        [HttpGet("config")]
        public IActionResult GetVersionConfig()
        {
            try
            {
                return Ok(new
                {
                    success = true,
                    data = new
                    {
                        defaultVersion = _versionConfig.DefaultVersion,
                        enableVersionSwitching = _versionConfig.EnableVersionSwitching,
                        services = _versionConfig.Services,
                        abTest = _versionConfig.ABTest
                    },
                    message = "获取API版本配置成功"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "获取API版本配置失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "获取API版本配置失败"
                });
            }
        }

        /// <summary>
        /// 获取用户的API版本
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户的API版本配置</returns>
        [HttpGet("user/{userId}")]
        public IActionResult GetUserApiVersions(int userId)
        {
            try
            {
                var userVersions = new
                {
                    userId = userId,
                    versions = new
                    {
                        user = _versionConfig.GetServiceVersion("user", userId),
                        asset = _versionConfig.GetServiceVersion("asset", userId),
                        task = _versionConfig.GetServiceVersion("task", userId),
                        location = _versionConfig.GetServiceVersion("location", userId),
                        fault = _versionConfig.GetServiceVersion("fault", userId),
                        purchase = _versionConfig.GetServiceVersion("purchase", userId)
                    },
                    abTestGroup = GetABTestGroup(userId),
                    timestamp = System.DateTime.UtcNow
                };

                return Ok(new
                {
                    success = true,
                    data = userVersions,
                    message = "获取用户API版本成功"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "获取用户API版本失败: {UserId}", userId);
                return StatusCode(500, new
                {
                    success = false,
                    message = "获取用户API版本失败"
                });
            }
        }

        /// <summary>
        /// 切换服务版本
        /// </summary>
        /// <param name="request">切换请求</param>
        /// <returns>切换结果</returns>
        [HttpPost("switch")]
        public IActionResult SwitchServiceVersion([FromBody] SwitchVersionRequest request)
        {
            try
            {
                if (!_versionConfig.EnableVersionSwitching)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "API版本切换功能已禁用"
                    });
                }

                if (_versionConfig.Services.TryGetValue(request.ServiceName, out var config))
                {
                    config.CurrentVersion = request.TargetVersion;
                    
                    _logger.LogInformation("服务版本切换成功: {ServiceName} -> {Version}", 
                        request.ServiceName, request.TargetVersion);

                    return Ok(new
                    {
                        success = true,
                        data = new
                        {
                            serviceName = request.ServiceName,
                            previousVersion = config.CurrentVersion,
                            currentVersion = request.TargetVersion,
                            switchedAt = System.DateTime.UtcNow
                        },
                        message = "服务版本切换成功"
                    });
                }

                return NotFound(new
                {
                    success = false,
                    message = $"服务 {request.ServiceName} 不存在"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "切换服务版本失败: {ServiceName} -> {Version}", 
                    request.ServiceName, request.TargetVersion);
                return StatusCode(500, new
                {
                    success = false,
                    message = "切换服务版本失败"
                });
            }
        }

        /// <summary>
        /// 启用/禁用A/B测试
        /// </summary>
        /// <param name="request">A/B测试配置请求</param>
        /// <returns>配置结果</returns>
        [HttpPost("abtest")]
        public IActionResult ConfigureABTest([FromBody] ABTestConfigRequest request)
        {
            try
            {
                _versionConfig.ABTest.Enabled = request.Enabled;
                _versionConfig.ABTest.TestName = request.TestName ?? _versionConfig.ABTest.TestName;
                _versionConfig.ABTest.Description = request.Description ?? _versionConfig.ABTest.Description;
                _versionConfig.ABTest.StartTime = request.StartTime ?? _versionConfig.ABTest.StartTime;
                _versionConfig.ABTest.EndTime = request.EndTime ?? _versionConfig.ABTest.EndTime;

                _logger.LogInformation("A/B测试配置更新: {Enabled}", request.Enabled);

                return Ok(new
                {
                    success = true,
                    data = _versionConfig.ABTest,
                    message = "A/B测试配置更新成功"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "配置A/B测试失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "配置A/B测试失败"
                });
            }
        }

        /// <summary>
        /// 获取API版本统计
        /// </summary>
        /// <returns>版本使用统计</returns>
        [HttpGet("statistics")]
        public IActionResult GetVersionStatistics()
        {
            try
            {
                var statistics = new
                {
                    totalServices = _versionConfig.Services.Count,
                    enabledServices = 0,
                    v1Services = 0,
                    v1_1Services = 0,
                    abTestEnabled = _versionConfig.ABTest.Enabled,
                    versionSwitchingEnabled = _versionConfig.EnableVersionSwitching,
                    lastUpdated = System.DateTime.UtcNow
                };

                foreach (var service in _versionConfig.Services.Values)
                {
                    if (service.Enabled) statistics.GetType().GetProperty("enabledServices")?.SetValue(statistics, (int)statistics.GetType().GetProperty("enabledServices")?.GetValue(statistics) + 1);
                    if (service.CurrentVersion == "v1") statistics.GetType().GetProperty("v1Services")?.SetValue(statistics, (int)statistics.GetType().GetProperty("v1Services")?.GetValue(statistics) + 1);
                    if (service.CurrentVersion == "v1.1") statistics.GetType().GetProperty("v1_1Services")?.SetValue(statistics, (int)statistics.GetType().GetProperty("v1_1Services")?.GetValue(statistics) + 1);
                }

                return Ok(new
                {
                    success = true,
                    data = statistics,
                    message = "获取版本统计成功"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "获取版本统计失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "获取版本统计失败"
                });
            }
        }

        /// <summary>
        /// 健康检查
        /// </summary>
        /// <returns>服务状态</returns>
        [HttpGet("health")]
        [AllowAnonymous]
        public IActionResult Health()
        {
            return Ok(new
            {
                status = "healthy",
                service = "ApiVersionService",
                version = "v1.0",
                timestamp = System.DateTime.UtcNow
            });
        }

        /// <summary>
        /// 获取A/B测试分组
        /// </summary>
        private string GetABTestGroup(int userId)
        {
            return (userId % 2 == 0) ? "A" : "B";
        }
    }

    /// <summary>
    /// 切换版本请求
    /// </summary>
    public class SwitchVersionRequest
    {
        public string ServiceName { get; set; }
        public string TargetVersion { get; set; }
    }

    /// <summary>
    /// A/B测试配置请求
    /// </summary>
    public class ABTestConfigRequest
    {
        public bool Enabled { get; set; }
        public string TestName { get; set; }
        public string Description { get; set; }
        public System.DateTime? StartTime { get; set; }
        public System.DateTime? EndTime { get; set; }
    }
}
