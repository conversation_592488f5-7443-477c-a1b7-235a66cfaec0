using System;

namespace ItAssetsSystem.Application.Features.AssetStatistics.Dtos
{
    /// <summary>
    /// 资产趋势数据DTO
    /// </summary>
    public class AssetTrendDataDto
    {
        /// <summary>
        /// 日期
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// 日期标签 (用于图表显示)
        /// </summary>
        public string DateLabel { get; set; }

        /// <summary>
        /// 资产总数
        /// </summary>
        public int TotalAssets { get; set; }

        /// <summary>
        /// 正常资产数
        /// </summary>
        public int NormalAssets { get; set; }

        /// <summary>
        /// 故障资产数
        /// </summary>
        public int FaultAssets { get; set; }

        /// <summary>
        /// 维修中资产数
        /// </summary>
        public int MaintenanceAssets { get; set; }

        /// <summary>
        /// 正常率
        /// </summary>
        public decimal NormalRate { get; set; }

        /// <summary>
        /// 故障率
        /// </summary>
        public decimal FaultRate { get; set; }

        /// <summary>
        /// 新增资产数
        /// </summary>
        public int NewAssets { get; set; }

        /// <summary>
        /// 处置资产数
        /// </summary>
        public int ProcessedAssets { get; set; }
    }
}