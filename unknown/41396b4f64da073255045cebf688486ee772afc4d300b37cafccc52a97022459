import{t,c as n,n as e,g as a,a as i,e as o,m as r,b as s,s as u,d,f as c,h as l,i as h}from"./en-US-BvtvdVHO.js";function m(n,e){const a=+t(n)-+t(e);return a<0?-1:a>0?1:a}function f(n,e){const a=t(n,null==e?void 0:e.in);return+function(n,e){const a=t(n,null==e?void 0:e.in);return a.setHours(23,59,59,999),a}(a,e)==+function(n,e){const a=t(n,null==e?void 0:e.in),i=a.getMonth();return a.setFullYear(a.getFullYear(),i+1,0),a.setHours(23,59,59,999),a}(a,e)}function g(t,n,a){const[i,o,r]=e(null==a?void 0:a.in,t,t,n),s=m(o,r),u=Math.abs(function(t,n,a){const[i,o]=e(null==a?void 0:a.in,t,n);return 12*(i.getFullYear()-o.getFullYear())+(i.getMonth()-o.getMonth())}(o,r));if(u<1)return 0;1===o.getMonth()&&o.getDate()>27&&o.setDate(30),o.setMonth(o.getMonth()-s*u);let d=m(o,r)===-s;f(i)&&1===u&&1===m(i,r)&&(d=!1);const c=s*(u-+d);return 0===c?0:c}function v(n,e,a){const i=function(n,e){return+t(n)-+t(e)}(n,e)/1e3;return(o=null==a?void 0:a.roundingMethod,t=>{const n=(o?Math[o]:Math.trunc)(t);return 0===n?0:n})(i);var o}function M(t,u){return function(t,n,u){const d=i(),c=(null==u?void 0:u.locale)??d.locale??o,l=m(t,n);if(isNaN(l))throw new RangeError("Invalid time value");const h=Object.assign({},u,{addSuffix:null==u?void 0:u.addSuffix,comparison:l}),[f,M]=e(null==u?void 0:u.in,...l>0?[n,t]:[t,n]),b=v(M,f),w=(a(M)-a(f))/1e3,y=Math.round((b-w)/60);let p;if(y<2)return(null==u?void 0:u.includeSeconds)?b<5?c.formatDistance("lessThanXSeconds",5,h):b<10?c.formatDistance("lessThanXSeconds",10,h):b<20?c.formatDistance("lessThanXSeconds",20,h):b<40?c.formatDistance("halfAMinute",0,h):b<60?c.formatDistance("lessThanXMinutes",1,h):c.formatDistance("xMinutes",1,h):0===y?c.formatDistance("lessThanXMinutes",1,h):c.formatDistance("xMinutes",y,h);if(y<45)return c.formatDistance("xMinutes",y,h);if(y<90)return c.formatDistance("aboutXHours",1,h);if(y<r){const t=Math.round(y/60);return c.formatDistance("aboutXHours",t,h)}if(y<2520)return c.formatDistance("xDays",1,h);if(y<s){const t=Math.round(y/r);return c.formatDistance("xDays",t,h)}if(y<2*s)return p=Math.round(y/s),c.formatDistance("aboutXMonths",p,h);if(p=g(M,f),p<12){const t=Math.round(y/s);return c.formatDistance("xMonths",t,h)}{const t=p%12,n=Math.trunc(p/12);return t<3?c.formatDistance("aboutXYears",n,h):t<9?c.formatDistance("overXYears",n,h):c.formatDistance("almostXYears",n+1,h)}}(t,function(t){return n(t,Date.now())}(t),u)}const b={lessThanXSeconds:{one:"不到 1 秒",other:"不到 {{count}} 秒"},xSeconds:{one:"1 秒",other:"{{count}} 秒"},halfAMinute:"半分钟",lessThanXMinutes:{one:"不到 1 分钟",other:"不到 {{count}} 分钟"},xMinutes:{one:"1 分钟",other:"{{count}} 分钟"},xHours:{one:"1 小时",other:"{{count}} 小时"},aboutXHours:{one:"大约 1 小时",other:"大约 {{count}} 小时"},xDays:{one:"1 天",other:"{{count}} 天"},aboutXWeeks:{one:"大约 1 个星期",other:"大约 {{count}} 个星期"},xWeeks:{one:"1 个星期",other:"{{count}} 个星期"},aboutXMonths:{one:"大约 1 个月",other:"大约 {{count}} 个月"},xMonths:{one:"1 个月",other:"{{count}} 个月"},aboutXYears:{one:"大约 1 年",other:"大约 {{count}} 年"},xYears:{one:"1 年",other:"{{count}} 年"},overXYears:{one:"超过 1 年",other:"超过 {{count}} 年"},almostXYears:{one:"将近 1 年",other:"将近 {{count}} 年"}},w={date:d({formats:{full:"y'年'M'月'd'日' EEEE",long:"y'年'M'月'd'日'",medium:"yyyy-MM-dd",short:"yy-MM-dd"},defaultWidth:"full"}),time:d({formats:{full:"zzzz a h:mm:ss",long:"z a h:mm:ss",medium:"a h:mm:ss",short:"a h:mm"},defaultWidth:"full"}),dateTime:d({formats:{full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},defaultWidth:"full"})};function y(t,n,a){const i="eeee p";return function(t,n,a){const[i,o]=e(null==a?void 0:a.in,t,n);return+u(i,a)==+u(o,a)}(t,n,a)?i:t.getTime()>n.getTime()?"'下个'"+i:"'上个'"+i}const p={lastWeek:y,yesterday:"'昨天' p",today:"'今天' p",tomorrow:"'明天' p",nextWeek:y,other:"PP p"},D={code:"zh-CN",formatDistance:(t,n,e)=>{let a;const i=b[t];return a="string"==typeof i?i:1===n?i.one:i.other.replace("{{count}}",String(n)),(null==e?void 0:e.addSuffix)?e.comparison&&e.comparison>0?a+"内":a+"前":a},formatLong:w,formatRelative:(t,n,e,a)=>{const i=p[t];return"function"==typeof i?i(n,e,a):i},localize:{ordinalNumber:(t,n)=>{const e=Number(t);switch(null==n?void 0:n.unit){case"date":return e.toString()+"日";case"hour":return e.toString()+"时";case"minute":return e.toString()+"分";case"second":return e.toString()+"秒";default:return"第 "+e.toString()}},era:c({values:{narrow:["前","公元"],abbreviated:["前","公元"],wide:["公元前","公元"]},defaultWidth:"wide"}),quarter:c({values:{narrow:["1","2","3","4"],abbreviated:["第一季","第二季","第三季","第四季"],wide:["第一季度","第二季度","第三季度","第四季度"]},defaultWidth:"wide",argumentCallback:t=>t-1}),month:c({values:{narrow:["一","二","三","四","五","六","七","八","九","十","十一","十二"],abbreviated:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],wide:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]},defaultWidth:"wide"}),day:c({values:{narrow:["日","一","二","三","四","五","六"],short:["日","一","二","三","四","五","六"],abbreviated:["周日","周一","周二","周三","周四","周五","周六"],wide:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"]},defaultWidth:"wide"}),dayPeriod:c({values:{narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},defaultWidth:"wide",formattingValues:{narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:h({matchPattern:/^(第\s*)?\d+(日|时|分|秒)?/i,parsePattern:/\d+/i,valueCallback:t=>parseInt(t,10)}),era:l({matchPatterns:{narrow:/^(前)/i,abbreviated:/^(前)/i,wide:/^(公元前|公元)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^(前)/i,/^(公元)/i]},defaultParseWidth:"any"}),quarter:l({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^第[一二三四]刻/i,wide:/^第[一二三四]刻钟/i},defaultMatchWidth:"wide",parsePatterns:{any:[/(1|一)/i,/(2|二)/i,/(3|三)/i,/(4|四)/i]},defaultParseWidth:"any",valueCallback:t=>t+1}),month:l({matchPatterns:{narrow:/^(一|二|三|四|五|六|七|八|九|十[二一])/i,abbreviated:/^(一|二|三|四|五|六|七|八|九|十[二一]|\d|1[12])月/i,wide:/^(一|二|三|四|五|六|七|八|九|十[二一])月/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^一/i,/^二/i,/^三/i,/^四/i,/^五/i,/^六/i,/^七/i,/^八/i,/^九/i,/^十(?!(一|二))/i,/^十一/i,/^十二/i],any:[/^一|1/i,/^二|2/i,/^三|3/i,/^四|4/i,/^五|5/i,/^六|6/i,/^七|7/i,/^八|8/i,/^九|9/i,/^十(?!(一|二))|10/i,/^十一|11/i,/^十二|12/i]},defaultParseWidth:"any"}),day:l({matchPatterns:{narrow:/^[一二三四五六日]/i,short:/^[一二三四五六日]/i,abbreviated:/^周[一二三四五六日]/i,wide:/^星期[一二三四五六日]/i},defaultMatchWidth:"wide",parsePatterns:{any:[/日/i,/一/i,/二/i,/三/i,/四/i,/五/i,/六/i]},defaultParseWidth:"any"}),dayPeriod:l({matchPatterns:{any:/^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨|)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^上午?/i,pm:/^下午?/i,midnight:/^午夜/i,noon:/^[中正]午/i,morning:/^早上/i,afternoon:/^下午/i,evening:/^晚上?/i,night:/^凌晨/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:4}};export{M as f,D as z};
