# IT资产管理系统补充需求文档

## 一、系统架构要求
- **技术栈**：Vue3前端 + .NET后端 + MySQL8.0
- **架构**：微内核+插件式设计（核心模块可热插拔）
- **必须实现的插件**：
  - 资产管理插件
  - 任务管理插件
  - 采购库存插件
  - 设备维修插件

## 二、核心功能清单

### 1. 资产管理模块（核心）
| 需求要点 | 具体实现要求 |
|---------|-------------|
| **资产信息** | 资产编号（规则：IT-类型-年月日-序号）、财务编号、品牌、型号 |
| **关联关系** | 资产必须关联到位置表的第五级，自动带出使用部门/人/负责人 |
| **分类管理** | 支持多级资产分类（电脑→笔记本→ThinkPad） |
| **位置管理** | 五级树形结构（国家→省→市→楼栋→机房），支持快速定位 |

### 2. 任务管理模块（含游戏化）
| 需求要点 | 具体实现要求 |
|---------|-------------|
| **任务类型** | 普通任务（默认）+ 周期性任务（每天/周/月定时生成） |
| **责任人** | 多选用户，修改时实时通知（站内信+登录弹窗） |
| **历史追踪** | 完整记录状态变更（示例：2023-11-01 张三将状态改为进行中） |
| **积分系统** | 新建任务+5分，更新进度+3分，每日8点生成排行榜 |

### 3. 采购库存全流程
| 流程节点 | 关键规则 |
|---------|---------|
| **采购单** | 单号格式：PO-年月日-时分秒（如PO-20231101-093000） |
| **入库** | 到货后自动生成库存记录，关联采购单和物料号 |
| **出库** | 库存→资产转换时，自动生成资产编号并扣减库存 |

### 4. 设备维修闭环
| 需求要点 | 业务规则 |
|---------|---------|
| **故障单** | 单号规则：FIX-年月日-序号（如FIX-20231101-001） |
| **返厂跟踪** | 超期未返回自动标红预警，需人工确认完成 |
| **状态同步** | 维修完成自动同步资产状态（库存/报废） |

## 三、特殊交互要求

### 1. 五级位置选择器
- 级联选择组件，支持快速跳转到任意级别
- 选择第五级时自动填充关联的部门/负责人

### 2. 任务看板
```vue
<template>
  <!-- 游戏化任务卡片 -->
  <task-card 
    v-for="task in unclaimedTasks" 
    :key="task.id"
    @claim="handleClaim"
    :points="calculatePoints(task)">
    <progress-bar :value="task.progress"/>
  </task-card>
</template>
```

### 3. 库存快速出库
- 表格行操作："转为资产"按钮
- 批量操作：支持多选出库生成多条资产记录

## 四、数据表关键设计

### 1. 资产主表（assets）
| 字段 | 类型 | 说明 |
|------|------|------|
| asset_no | VARCHAR(20) | 主键，IT-ASSET-2023-001 |
| location_id | INT | 必须关联到locations.level=5的记录 |
| status | ENUM | 库存/使用中/返厂中/报废 |

### 2. 五级位置表（locations）
| 字段 | 类型 | 约束 |
|------|------|------|
| level | TINYINT | 值域1-5，第五级必须关联部门 |
| parent_id | INT | 上级ID，形成树形结构 |

### 3. 任务历史表（task_history）
| 字段 | 示例数据 | 说明 |
|------|----------|------|
| action | "状态变更为进行中" | 记录完整操作语义 |
| snapshot | JSON | 操作时的任务数据快照 |

## 五、业务流程验证

### 1. 采购→入库→出库流程
```mermaid
graph LR
  A[创建采购单PO-20231101-001] --> B[到货后入库]
  B --> C[库存清单显示物料]
  C --> D[出库生成资产IT-ASSET-2023-001]
```

### 2. 故障处理流程
```mermaid
graph TB
  A[报修生成FIX单] --> B{是否返厂?}
  B -->|是| C[返厂跟踪]
  B -->|否| D[现场维修]
  C --> E[超期预警]
```

## 六、注意事项

### 1. 必须实现的接口
- `GET /api/locations/tree` 返回五级位置完整树形结构
- `POST /api/assets/transfer` 处理库存转资产

### 2. 禁止出现的设计
- 扁平化的位置管理（必须五级树形）
- 简单的任务状态变更（必须完整历史追溯）

### 3. 性能要求
- 百级用户量下，所有列表查询响应<500ms
- 支持并发出库操作（库存数量精确扣减）

## 七、交付物清单

### 1. 核心插件
- AssetPlugin.dll（资产管理）
- TaskPlugin.dll（任务+积分系统）

### 2. 数据库脚本
- 包含所有表结构和约束条件

### 3. 前端组件
- LocationCascader.vue（五级选择器）
- TaskBoard.vue（游戏化任务看板）

## 八、中控台核心模块设计

### 整体布局
三屏联动（资产矩阵+任务战报+实时库存），支持深色/浅色主题切换

### 1. 资产监控模块（左屏）

**可视化方案**：
```vue
<template>
  <!-- 三维资产矩阵 -->
  <AssetCube 
    :data="assetMatrix" 
    @click-cube="showDetail"
  />
  
  <!-- 分类统计表格 -->
  <el-table :data="categoryData" class="glowing-table">
    <el-table-column prop="type" label="资产类型">
      <template #default="{row}">
        <div class="icon-wrapper">
          <AssetIcon :type="row.type" /> <!-- 动态SVG图标 -->
          <span>{{ row.type }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column prop="count" label="数量">
      <template #default="{row}">
        <DigitalRoll :value="row.count" /> <!-- 数字滚动效果 -->
      </template>
    </el-table-column>
  </el-table>
</template>
```

**特效实现**：
- **资产矩阵**：WebGL渲染的3D立方体，每个方块代表一类资产，悬浮显示详情
- **发光表格**：CSS `box-shadow` 实现呼吸灯效果
- **数据联动**：点击矩阵方块自动筛选表格数据

### 2. 任务战报模块（中屏）

**游戏化元素**：
```javascript
// 积分排行榜数据结构
const leaderboard = {
  daily: [
    { 
      rank: 1, 
      user: '张三', 
      avatar: '...',
      points: 120,
      badges: ['🏆', '🔥'] // 荣誉徽章
    },
    ...
  ],
  actions: {
    create: [...], // 新建任务TOP3
    update: [...], // 更新任务TOP3
    repair: [...]  // 故障处理TOP3
  }
}
```

**关键组件**：
```vue
<template>
  <!-- 任务领取看板 -->
  <div class="mission-board">
    <h3>待领取任务 <span class="blink">NEW!</span></h3>
    <div 
      v-for="task in unclaimedTasks" 
      class="mission-card"
      @click="claimTask(task)"
    >
      <div class="reward-tag">+{{ task.points }}积分</div>
      <p>{{ task.title }}</p>
    </div>
  </div>

  <!-- 实时排行榜 -->
  <Leaderboard :data="leaderboard" />
</template>
```

**动效设计**：
- **粒子庆祝**：使用Three.js实现彩带喷射效果
- **任务卡片**：悬停时3D翻转动画（CSS transform）
- **数字滚动**：任务统计数变化时有金币掉落音效

### 3. 库存看板模块（右屏）

**数据展示**：
```vue
<template>
  <!-- 库存分类环形图 -->
  <div class="donut-container">
    <AnimatedDonut 
      :data="inventoryData"
      :colors="customColors"
    />
    <div class="center-label">
      <div>总库存</div>
      <div class="big-number">{{ totalCount }}</div>
    </div>
  </div>

  <!-- 低库存预警 -->
  <div class="alert-bar" v-if="hasAlert">
    <icon-warning />
    <marquee>⚠️ 扫码枪库存仅剩2件！</marquee>
  </div>
</template>
```

**交互设计**：
- **环形图**：点击扇区下钻查看详细库存记录
- **预警系统**：库存低于安全值时触发红色脉冲警示灯
- **报表生成**：每天8点自动截图保存当日状态（html2canvas）

## 九、技术实现细节

### 1. 特效实现方案
| 特效 | 技术方案 | 性能优化 |
|------|----------|----------|
| 3D资产矩阵 | Three.js + GPU加速 | 按需渲染 |
| 粒子动画 | Canvas 2D + 对象池 | 最大500粒子 |
| 数据刷新 | WebSocket + 差异更新 | 节流控制 |

### 2. 关键后端接口
```csharp
// 资产矩阵数据接口
[HttpGet("api/dashboard/assets-matrix")]
public IActionResult GetAssetMatrix([FromQuery] int? type) {
  // 返回三维数据：部门×位置×资产类型
  return Ok(new {
    dimensions = ["部门", "位置", "类型"],
    data = dbContext.Assets
      .GroupBy(a => new { a.Department, a.Location, a.Type })
      .Select(g => new {
        keys = [g.Key.Department, g.Key.Location, g.Key.Type],
        count = g.Count()
      })
  });
}
```

### 3. 排行榜计算逻辑
```sql
-- 每日积分统计（08:00~次日08:00）
SELECT 
  user_id,
  SUM(CASE 
    WHEN action = 'create_task' THEN 5
    WHEN action = 'update_task' THEN 3
    WHEN action = 'report_fault' THEN 4
  END) AS points
FROM user_actions
WHERE action_time BETWEEN 
  CONCAT(CURDATE(), ' 08:00:00') AND 
  CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 08:00:00')
GROUP BY user_id
ORDER BY points DESC
LIMIT 10;
```

## 十、晨间战报系统流程
1. **定时任务**（每天07:59启动）
   - 生成排行榜数据
   - 缓存可视化报表快照
2. **用户登录**（08:00后）
   - 检查是否在榜首
   - 触发庆祝动画（带音效）
3. **数据持久化**
   - 将日报存入PDF（使用wkhtmltopdf）
   - 记录到`daily_reports`表

## 十一、扩展性设计
1. **主题切换**：通过CSS变量实现
   ```css
   .dark-mode {
     --primary-bg: #1a1a2e;
     --glow-color: #00f5d4;
   }
   ```
2. **大屏模式**：`/dashboard/fullscreen`路由去除导航栏
3. **移动端适配**：隐藏复杂动画，保留核心数据

## 十二、避坑指南
1. **性能陷阱**：
   - 避免实时计算三维矩阵数据（采用定时缓存）
   - 限制粒子动画的最大数量
2. **数据一致性**：
   - 使用数据库事务处理积分变更
   - 排行榜采用最终一致性（延迟1分钟更新）
3. **浏览器兼容**：
   - 提供WebGL降级方案（2D替代） 