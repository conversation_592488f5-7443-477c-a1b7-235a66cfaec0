// File: Api/V2/Controllers/StatisticsController.cs
// Description: V2统计分析API控制器，提供统一的动态聚合查询接口

using Microsoft.AspNetCore.Mvc;
using MediatR;
using ItAssetsSystem.Application.Features.Statistics.Dtos;
using ItAssetsSystem.Application.Features.Statistics.Queries;
using ItAssetsSystem.Application.Common.Dtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ItAssetsSystem.Api.V2.Controllers;

/// <summary>
/// 统计分析控制器
/// </summary>
[ApiController]
[Route("api/v2/statistics")]
// [Authorize] // 暂时注释掉以便测试
public class StatisticsController : ControllerBase
{
    private readonly ISender _sender;
    private readonly ILogger<StatisticsController> _logger;

    public StatisticsController(ISender sender, ILogger<StatisticsController> logger)
    {
        _sender = sender;
        _logger = logger;
    }

    /// <summary>
    /// 执行动态统计查询
    /// </summary>
    /// <param name="queryRequest">查询请求参数</param>
    /// <returns>统计查询结果</returns>
    [HttpPost("query")]
    [ProducesResponseType(typeof(ApiResponse<DynamicStatisticsResultDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<DynamicStatisticsResultDto>>> ExecuteDynamicQuery(
        [FromBody] DynamicStatisticsQueryDto queryRequest)
    {
        try
        {
            _logger.LogInformation("接收到统计查询请求: Dimension={Dimension}, Metric={Metric}", 
                queryRequest.Dimension, queryRequest.Metric);

            if (!ModelState.IsValid)
            {
                var errorMessage = ModelState.Values
                    .SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .FirstOrDefault();
                return BadRequest(new ApiResponse<object>
                {
                    Success = false,
                    Message = "请求参数验证失败: " + errorMessage
                });
            }

            var query = new DynamicStatisticsQuery(queryRequest);
            var result = await _sender.Send(query);

            // 使用一个通用的方式处理成功或失败的结果
            if (result.IsSuccess)
            {
                return Ok(new ApiResponse<DynamicStatisticsResultDto>
                {
                    Success = true,
                    Data = result.Value,
                    Message = "查询执行成功"
                });
            }
            
            return BadRequest(new ApiResponse<object>
            {
                Success = false,
                Message = "查询执行失败: " + (result.Error?.ToString() ?? "未知错误")
            });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "统计查询参数错误: {Message}", ex.Message);
            return BadRequest(new ApiResponse<object>
            {
                Success = false,
                Message = "查询参数错误: " + ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "统计查询执行失败");
            return StatusCode(500, new ApiResponse<object>
            {
                Success = false,
                Message = "查询执行失败，请稍后重试"
            });
        }
    }

    /// <summary>
    /// 获取支持的查询维度列表
    /// </summary>
    /// <returns>可用的查询维度</returns>
    [HttpGet("dimensions")]
    [ProducesResponseType(typeof(ApiResponse<List<DimensionDefinitionDto>>), StatusCodes.Status200OK)]
    public ActionResult<ApiResponse<List<DimensionDefinitionDto>>> GetAvailableDimensions()
    {
        var dimensions = new List<DimensionDefinitionDto>
        {
            new() {
                Key = "department",
                Name = "部门",
                Description = "按部门分组统计",
                SupportedMetrics = new[] { "count", "value", "averageValue" }
            },
            new() {
                Key = "assetType",
                Name = "资产类型",
                Description = "按资产类型分组统计",
                SupportedMetrics = new[] { "count", "value", "averageValue" }
            },
            new() {
                Key = "location",
                Name = "区域",
                Description = "按区域分组统计",
                SupportedMetrics = new[] { "count", "value", "averageValue" }
            },
            new() {
                Key = "status",
                Name = "状态",
                Description = "按状态分组统计",
                SupportedMetrics = new[] { "count", "value", "averageValue" }
            },
            new() {
                Key = "supplier",
                Name = "供应商",
                Description = "按供应商分组统计(返修模块)",
                SupportedMetrics = new[] { "count", "totalCost", "averageCost" }
            }
        };

        return Ok(new ApiResponse<List<DimensionDefinitionDto>>
        {
            Success = true,
            Data = dimensions,
            Message = "获取维度列表成功"
        });
    }

    /// <summary>
    /// 获取支持的度量指标列表
    /// </summary>
    /// <returns>可用的度量指标</returns>
    [HttpGet("metrics")]
    [ProducesResponseType(typeof(ApiResponse<List<MetricDefinitionDto>>), StatusCodes.Status200OK)]
    public ActionResult<ApiResponse<List<MetricDefinitionDto>>> GetAvailableMetrics()
    {
        var metrics = new List<MetricDefinitionDto>
        {
            new() { 
                Key = "count", 
                Name = "数量", 
                Description = "统计记录总数",
                DataType = "integer"
            },
            new() { 
                Key = "value", 
                Name = "总值", 
                Description = "统计金额总和",
                DataType = "decimal"
            },
            new() { 
                Key = "averageValue", 
                Name = "平均值", 
                Description = "统计金额平均值",
                DataType = "decimal"
            },
            new() { 
                Key = "totalCost", 
                Name = "总费用", 
                Description = "统计总费用",
                DataType = "decimal"
            },
            new() { 
                Key = "averageCost", 
                Name = "平均费用", 
                Description = "统计平均费用",
                DataType = "decimal"
            }
        };

        return Ok(new ApiResponse<List<MetricDefinitionDto>>
        {
            Success = true,
            Data = metrics,
            Message = "获取度量指标列表成功"
        });
    }
}

/// <summary>
/// 维度定义DTO
/// </summary>
public class DimensionDefinitionDto
{
    public string Key { get; set; } = null!;
    public string Name { get; set; } = null!;
    public string Description { get; set; } = null!;
    public string[] SupportedMetrics { get; set; } = Array.Empty<string>();
}

/// <summary>
/// 度量指标定义DTO
/// </summary>
public class MetricDefinitionDto
{
    public string Key { get; set; } = null!;
    public string Name { get; set; } = null!;
    public string Description { get; set; } = null!;
    public string DataType { get; set; } = null!;
}