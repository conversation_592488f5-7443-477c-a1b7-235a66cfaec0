-- 添加班次管理相关表的SQL脚本
-- 执行时间: 2025-06-19

-- 1. 创建班次表
CREATE TABLE IF NOT EXISTS `work_shifts` (
    `shift_id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `shift_name` VARCHAR(50) NOT NULL COMMENT '班次名称',
    `shift_code` VARCHAR(20) NOT NULL UNIQUE COMMENT '班次代码',
    `shift_type` VARCHAR(20) NOT NULL DEFAULT 'Day' COMMENT '班次类型 (Day-白班, Night-夜班, Swing-中班)',
    `start_time` TIME NOT NULL COMMENT '开始时间',
    `end_time` TIME NOT NULL COMMENT '结束时间',
    `task_claim_time` TIME NOT NULL COMMENT '任务领取时间',
    `is_overnight` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否跨天',
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    `description` VARCHAR(500) COMMENT '描述',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by` INT NOT NULL COMMENT '创建用户ID',
    `updated_by` INT COMMENT '更新用户ID',
    INDEX `idx_shift_code` (`shift_code`),
    INDEX `idx_shift_type` (`shift_type`),
    INDEX `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='班次表';

-- 2. 创建用户班次分配表
CREATE TABLE IF NOT EXISTS `user_shift_assignments` (
    `assignment_id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `user_id` INT NOT NULL COMMENT '用户ID',
    `shift_id` BIGINT NOT NULL COMMENT '班次ID',
    `effective_date` DATE NOT NULL COMMENT '生效日期',
    `expiry_date` DATE COMMENT '失效日期',
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    `assignment_type` VARCHAR(20) NOT NULL DEFAULT 'Permanent' COMMENT '分配类型 (Permanent-固定, Temporary-临时, Rotation-轮班)',
    `notes` VARCHAR(500) COMMENT '备注',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by` INT NOT NULL COMMENT '创建用户ID',
    `updated_by` INT COMMENT '更新用户ID',
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT,
    FOREIGN KEY (`shift_id`) REFERENCES `work_shifts`(`shift_id`) ON DELETE RESTRICT,
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_shift_id` (`shift_id`),
    INDEX `idx_effective_date` (`effective_date`),
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_user_shift_active` (`user_id`, `shift_id`, `is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户班次分配表';

-- 3. 创建任务领取记录表
CREATE TABLE IF NOT EXISTS `task_claims` (
    `claim_id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `task_id` BIGINT NOT NULL COMMENT '任务ID',
    `claimed_by` INT NOT NULL COMMENT '领取用户ID',
    `shift_id` BIGINT NOT NULL COMMENT '班次ID',
    `claimed_at` DATETIME NOT NULL COMMENT '领取时间',
    `claim_date` DATE NOT NULL COMMENT '领取日期',
    `claim_status` VARCHAR(20) NOT NULL DEFAULT 'Claimed' COMMENT '领取状态 (Claimed-已领取, Started-已开始, Completed-已完成, Cancelled-已取消)',
    `started_at` DATETIME COMMENT '开始时间',
    `completed_at` DATETIME COMMENT '完成时间',
    `notes` VARCHAR(1000) COMMENT '备注',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (`task_id`) REFERENCES `tasks`(`TaskId`) ON DELETE CASCADE,
    FOREIGN KEY (`claimed_by`) REFERENCES `users`(`id`) ON DELETE RESTRICT,
    FOREIGN KEY (`shift_id`) REFERENCES `work_shifts`(`shift_id`) ON DELETE RESTRICT,
    UNIQUE KEY `uk_task_user_date` (`task_id`, `claimed_by`, `claim_date`),
    INDEX `idx_task_id` (`task_id`),
    INDEX `idx_claimed_by` (`claimed_by`),
    INDEX `idx_shift_id` (`shift_id`),
    INDEX `idx_claim_date` (`claim_date`),
    INDEX `idx_claim_status` (`claim_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务领取记录表';

-- 4. 创建任务提醒配置表
CREATE TABLE IF NOT EXISTS `task_reminders` (
    `reminder_id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `task_id` BIGINT NOT NULL COMMENT '任务ID',
    `reminder_type` VARCHAR(20) NOT NULL DEFAULT 'BeforeDeadline' COMMENT '提醒类型 (BeforeStart-开始前, BeforeDeadline-截止前, Overdue-逾期)',
    `offset_minutes` INT NOT NULL COMMENT '提醒时间偏移（分钟）',
    `reminder_level` VARCHAR(20) NOT NULL DEFAULT 'Warning' COMMENT '提醒级别 (Info-信息, Warning-警告, Critical-严重)',
    `reminder_method` VARCHAR(20) NOT NULL DEFAULT 'System' COMMENT '提醒方式 (System-系统通知, Email-邮件, SMS-短信, All-全部)',
    `is_recurring` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否重复提醒',
    `recurring_interval` INT COMMENT '重复间隔（分钟）',
    `max_occurrences` INT COMMENT '最大重复次数',
    `sent_count` INT NOT NULL DEFAULT 0 COMMENT '已发送次数',
    `next_reminder_time` DATETIME COMMENT '下次提醒时间',
    `last_sent_at` DATETIME COMMENT '最后发送时间',
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by` INT NOT NULL COMMENT '创建用户ID',
    `updated_by` INT COMMENT '更新用户ID',
    FOREIGN KEY (`task_id`) REFERENCES `tasks`(`TaskId`) ON DELETE CASCADE,
    INDEX `idx_task_id` (`task_id`),
    INDEX `idx_next_reminder_time` (`next_reminder_time`),
    INDEX `idx_active_reminder` (`is_active`, `next_reminder_time`),
    INDEX `idx_reminder_type` (`reminder_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务提醒配置表';

-- 5. 为tasks表添加完成用户水印字段
ALTER TABLE `tasks` 
ADD COLUMN `CompletedByUserId` INT COMMENT '完成用户ID (用于水印显示)' AFTER `Points`,
ADD COLUMN `CompletionWatermarkColor` VARCHAR(7) COMMENT '完成用户水印颜色 (十六进制颜色代码)' AFTER `CompletedByUserId`;

-- 6. 插入默认班次数据
INSERT INTO `work_shifts` (`shift_name`, `shift_code`, `shift_type`, `start_time`, `end_time`, `task_claim_time`, `is_overnight`, `description`, `created_by`) VALUES
('白班', 'DAY', 'Day', '08:00:00', '20:00:00', '08:00:00', FALSE, '白班 8:00-20:00', 1),
('夜班', 'NIGHT', 'Night', '20:00:00', '08:00:00', '20:00:00', TRUE, '夜班 20:00-次日8:00', 1);

-- 7. 创建索引优化查询性能
CREATE INDEX `idx_tasks_status_completed` ON `tasks`(`Status`, `CompletedByUserId`) WHERE `Status` IN ('Done', 'Completed');
CREATE INDEX `idx_tasks_completion_watermark` ON `tasks`(`CompletedByUserId`, `CompletionWatermarkColor`) WHERE `CompletedByUserId` IS NOT NULL;

-- 8. 添加注释说明
ALTER TABLE `tasks` MODIFY COLUMN `CompletedByUserId` INT COMMENT '完成用户ID (用于水印显示，关联users表)';
ALTER TABLE `tasks` MODIFY COLUMN `CompletionWatermarkColor` VARCHAR(7) COMMENT '完成用户水印颜色 (十六进制颜色代码，如#FF6B6B)';

-- 执行完成提示
SELECT 'Shift management tables created successfully!' as message;
