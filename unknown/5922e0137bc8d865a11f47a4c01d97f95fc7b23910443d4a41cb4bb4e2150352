using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ItAssetsSystem.Core.Interfaces.Services
{
    /// <summary>
    /// 故障管理服务接口 - 基础解耦
    /// 提供故障相关的核心业务操作
    /// </summary>
    public interface IFaultService
    {
        /// <summary>
        /// 根据ID获取故障信息
        /// </summary>
        /// <param name="id">故障ID</param>
        /// <returns>故障信息</returns>
        Task<FaultDto> GetByIdAsync(int id);

        /// <summary>
        /// 分页获取故障列表
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResult<FaultDto>> GetPagedAsync(FaultQueryDto query);

        /// <summary>
        /// 报告故障
        /// </summary>
        /// <param name="dto">故障报告DTO</param>
        /// <returns>创建的故障信息</returns>
        Task<FaultDto> ReportAsync(ReportFaultDto dto);

        /// <summary>
        /// 更新故障状态
        /// </summary>
        /// <param name="id">故障ID</param>
        /// <param name="status">新状态</param>
        /// <param name="remarks">备注</param>
        /// <returns>更新后的故障信息</returns>
        Task<FaultDto> UpdateStatusAsync(int id, string status, string remarks = null);

        /// <summary>
        /// 分配故障处理人
        /// </summary>
        /// <param name="id">故障ID</param>
        /// <param name="assigneeId">处理人ID</param>
        /// <returns>是否分配成功</returns>
        Task<bool> AssignAsync(int id, int assigneeId);

        /// <summary>
        /// 完成故障处理
        /// </summary>
        /// <param name="id">故障ID</param>
        /// <param name="solution">解决方案</param>
        /// <returns>是否完成成功</returns>
        Task<bool> CompleteAsync(int id, string solution);

        /// <summary>
        /// 检查故障是否存在
        /// </summary>
        /// <param name="id">故障ID</param>
        /// <returns>是否存在</returns>
        Task<bool> ExistsAsync(int id);

        /// <summary>
        /// 获取故障统计信息
        /// </summary>
        /// <returns>故障统计</returns>
        Task<FaultStatisticsDto> GetStatisticsAsync();
    }

    /// <summary>
    /// 故障DTO
    /// </summary>
    public class FaultDto
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string Priority { get; set; }
        public int? AssetId { get; set; }
        public string AssetName { get; set; }
        public int? LocationId { get; set; }
        public string LocationName { get; set; }
        public int ReporterId { get; set; }
        public string ReporterName { get; set; }
        public int? AssigneeId { get; set; }
        public string AssigneeName { get; set; }
        public DateTime ReportedAt { get; set; }
        public DateTime? AssignedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public string Solution { get; set; }
        public string Notes { get; set; }
    }

    /// <summary>
    /// 故障查询DTO
    /// </summary>
    public class FaultQueryDto
    {
        public int PageIndex { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string Keyword { get; set; }
        public string Status { get; set; }
        public string Priority { get; set; }
        public int? AssetId { get; set; }
        public int? LocationId { get; set; }
        public int? ReporterId { get; set; }
        public int? AssigneeId { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string SortBy { get; set; }
        public string SortDirection { get; set; } = "desc";
    }

    /// <summary>
    /// 故障报告DTO
    /// </summary>
    public class ReportFaultDto
    {
        public string Title { get; set; }
        public string Description { get; set; }
        public string Priority { get; set; } = "Medium";
        public int? AssetId { get; set; }
        public int? LocationId { get; set; }
        public int ReporterId { get; set; }
        public string Notes { get; set; }
    }

    /// <summary>
    /// 故障统计DTO
    /// </summary>
    public class FaultStatisticsDto
    {
        public int TotalFaults { get; set; }
        public int OpenFaults { get; set; }
        public int InProgressFaults { get; set; }
        public int ResolvedFaults { get; set; }
        public int ClosedFaults { get; set; }
        public double ResolutionRate { get; set; }
        public double AverageResolutionTime { get; set; }
    }
}
