// File: Domain/Entities/Tasks/TaskReminder.cs
// Description: 任务提醒配置实体

using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Domain.Entities.Tasks
{
    /// <summary>
    /// 任务提醒配置实体
    /// </summary>
    [Table("task_reminders")]
    public class TaskReminder
    {
        /// <summary>
        /// 提醒ID
        /// </summary>
        [Key]
        [Column("reminder_id")]
        public long ReminderId { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        [Required]
        [Column("task_id")]
        public long TaskId { get; set; }

        /// <summary>
        /// 提醒类型 (BeforeStart-开始前, BeforeDeadline-截止前, Overdue-逾期)
        /// </summary>
        [Required]
        [MaxLength(20)]
        [Column("reminder_type")]
        public string ReminderType { get; set; } = "BeforeDeadline";

        /// <summary>
        /// 提醒时间偏移（分钟）
        /// </summary>
        [Required]
        [Column("offset_minutes")]
        public int OffsetMinutes { get; set; }

        /// <summary>
        /// 提醒级别 (Info-信息, Warning-警告, Critical-严重)
        /// </summary>
        [Required]
        [MaxLength(20)]
        [Column("reminder_level")]
        public string ReminderLevel { get; set; } = "Warning";

        /// <summary>
        /// 提醒方式 (System-系统通知, Email-邮件, SMS-短信, All-全部)
        /// </summary>
        [Required]
        [MaxLength(20)]
        [Column("reminder_method")]
        public string ReminderMethod { get; set; } = "System";

        /// <summary>
        /// 是否重复提醒
        /// </summary>
        [Column("is_recurring")]
        public bool IsRecurring { get; set; } = false;

        /// <summary>
        /// 重复间隔（分钟）
        /// </summary>
        [Column("recurring_interval")]
        public int? RecurringInterval { get; set; }

        /// <summary>
        /// 最大重复次数
        /// </summary>
        [Column("max_occurrences")]
        public int? MaxOccurrences { get; set; }

        /// <summary>
        /// 已发送次数
        /// </summary>
        [Column("sent_count")]
        public int SentCount { get; set; } = 0;

        /// <summary>
        /// 下次提醒时间
        /// </summary>
        [Column("next_reminder_time")]
        public DateTime? NextReminderTime { get; set; }

        /// <summary>
        /// 最后发送时间
        /// </summary>
        [Column("last_sent_at")]
        public DateTime? LastSentAt { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [Column("is_active")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 创建用户ID
        /// </summary>
        [Column("created_by")]
        public int CreatedBy { get; set; }

        /// <summary>
        /// 更新用户ID
        /// </summary>
        [Column("updated_by")]
        public int? UpdatedBy { get; set; }

        // 导航属性
        /// <summary>
        /// 任务
        /// </summary>
        [ForeignKey("TaskId")]
        public virtual Task Task { get; set; } = null!;
    }
}
