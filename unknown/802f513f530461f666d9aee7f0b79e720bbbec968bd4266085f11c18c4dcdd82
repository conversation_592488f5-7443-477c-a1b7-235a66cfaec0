# 大型商业级资产分析平台：专业解决方案

我理解您的问题是关于资产类型在分析平台中的最佳展示方式。资产类型作为核心分析维度，应当放在交叉坐标的维度中，以提供更深入的分析能力。

## 优化解决方案

### 资产类型在交叉坐标维度的优势：
1. **多维度分析**：资产类型与其他维度（部门/位置/状态）形成矩阵分析
2. **对比能力**：直观比较不同类型资产在不同部门的分布
3. **趋势洞察**：追踪各类资产价值随时间的变化
4. **资源分配**：识别资产类型与部门需求是否匹配

### 解决方案优化：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业级资产智能分析平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/luxon@3.0.4/build/global/luxon.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-luxon@1.3.1"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #4f46e5;
            --secondary: #8b5cf6;
            --dark: #0f172a;
            --light: #f8fafc;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #3b82f6;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #e2e8f0;
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .dashboard-container {
            max-width: 1800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .glass-card {
            background: rgba(30, 41, 59, 0.7);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .glass-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
            border-color: rgba(79, 70, 229, 0.3);
        }
        
        .chart-container {
            position: relative;
            width: 100%;
            height: 100%;
        }
        
        .kpi-card {
            transition: all 0.3s ease;
            overflow: hidden;
            position: relative;
        }
        
        .kpi-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.25);
        }
        
        .kpi-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary), var(--secondary));
        }
        
        .gradient-text {
            background: linear-gradient(90deg, #8b5cf6, #4f46e5);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .pulse {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7); }
            70% { box-shadow: 0 0 0 8px rgba(59, 130, 246, 0); }
            100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
        }
        
        .glow {
            filter: drop-shadow(0 0 8px rgba(139, 92, 246, 0.6));
        }
        
        .heatmap-cell {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .heatmap-cell:hover {
            transform: scale(1.05);
            z-index: 10;
            box-shadow: 0 0 15px rgba(139, 92, 246, 0.5);
        }
        
        .location-path {
            font-size: 12px;
            color: #94a3b8;
            margin-top: 4px;
        }
        
        .filter-pill {
            display: inline-flex;
            align-items: center;
            padding: 4px 12px;
            background: rgba(139, 92, 246, 0.2);
            border-radius: 20px;
            margin-right: 8px;
            margin-bottom: 8px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .filter-pill:hover {
            background: rgba(139, 92, 246, 0.3);
        }
        
        .filter-pill i {
            margin-left: 6px;
            font-size: 12px;
        }
        
        .dimension-selector {
            transition: all 0.2s;
        }
        
        .dimension-selector.active {
            background: var(--primary);
            color: white;
        }
        
        .asset-type-tag {
            display: inline-block;
            padding: 2px 8px;
            background: rgba(99, 102, 241, 0.2);
            border-radius: 12px;
            font-size: 12px;
            margin-right: 4px;
        }
        
        .drilldown-path {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #94a3b8;
        }
        
        .drilldown-path span {
            cursor: pointer;
            transition: color 0.2s;
        }
        
        .drilldown-path span:hover {
            color: var(--primary);
        }
        
        .drilldown-path i {
            margin: 0 8px;
            font-size: 12px;
        }
        
        .type-distribution-card {
            transition: all 0.3s ease;
        }
        
        .type-distribution-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .type-tab {
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .type-tab.active {
            background: rgba(79, 70, 229, 0.3);
            color: #c7d2fe;
        }
        
        .trend-line {
            height: 3px;
            border-radius: 2px;
            margin-top: 8px;
            background: linear-gradient(90deg, #8b5cf6, #4f46e5);
        }
        
        .cross-analysis-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
        }
        
        .cross-analysis-cell {
            background: rgba(30, 41, 59, 0.5);
            border-radius: 12px;
            padding: 12px;
            text-align: center;
            transition: all 0.3s;
        }
        
        .cross-analysis-cell:hover {
            background: rgba(55, 65, 81, 0.7);
            transform: scale(1.03);
        }
        
        .type-selector-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 10px;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <header class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold"><span class="gradient-text">资产智能分析平台</span></h1>
                <p class="text-slate-400 mt-2">多维度钻取式资产分析与洞察</p>
            </div>
            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    <div class="bg-purple-600 text-white p-2 rounded-lg mr-3">
                        <i class="fas fa-chart-network"></i>
                    </div>
                    <div>
                        <div class="text-sm text-slate-400">当前分析维度</div>
                        <div class="font-bold" id="current-dimension">资产类型</div>
                    </div>
                </div>
                <button id="refresh-btn" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center transition">
                    <i class="fas fa-sync-alt mr-2"></i> 刷新数据
                </button>
                <div class="relative">
                    <div class="bg-gradient-to-br from-purple-500 to-indigo-600 w-10 h-10 rounded-full flex items-center justify-center text-white font-bold cursor-pointer">JD</div>
                    <span class="absolute top-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-slate-900"></span>
                </div>
            </div>
        </header>
        
        <!-- 钻取路径和筛选器 -->
        <div class="glass-card p-5 mb-6">
            <div class="flex justify-between items-center">
                <div class="drilldown-path">
                    <span>全部资产</span>
                    <i class="fas fa-chevron-right"></i>
                    <span>工控设备</span>
                </div>
                
                <div class="flex items-center">
                    <span class="text-slate-400 text-sm mr-3">当前筛选：</span>
                    <div class="filter-pill">
                        部门: 生产部 <i class="fas fa-times"></i>
                    </div>
                    <div class="filter-pill">
                        位置: 制芯车间 <i class="fas fa-times"></i>
                    </div>
                    <div class="filter-pill">
                        状态: 在用 <i class="fas fa-times"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 资产类型主导航 -->
        <div class="glass-card p-5 mb-6 flex overflow-x-auto">
            <div class="type-tab active" data-type="all">
                <div class="flex flex-col items-center">
                    <i class="fas fa-cubes text-xl mb-2"></i>
                    <span>全部资产</span>
                    <div class="trend-line w-full"></div>
                </div>
            </div>
            <div class="type-tab" data-type="industrial">
                <div class="flex flex-col items-center">
                    <i class="fas fa-industry text-xl mb-2"></i>
                    <span>工控设备</span>
                    <div class="trend-line w-0"></div>
                </div>
            </div>
            <div class="type-tab" data-type="servers">
                <div class="flex flex-col items-center">
                    <i class="fas fa-server text-xl mb-2"></i>
                    <span>服务器</span>
                    <div class="trend-line w-0"></div>
                </div>
            </div>
            <div class="type-tab" data-type="computers">
                <div class="flex flex-col items-center">
                    <i class="fas fa-laptop text-xl mb-2"></i>
                    <span>电脑设备</span>
                    <div class="trend-line w-0"></div>
                </div>
            </div>
            <div class="type-tab" data-type="office">
                <div class="flex flex-col items-center">
                    <i class="fas fa-print text-xl mb-2"></i>
                    <span>办公设备</span>
                    <div class="trend-line w-0"></div>
                </div>
            </div>
            <div class="type-tab" data-type="production">
                <div class="flex flex-col items-center">
                    <i class="fas fa-cogs text-xl mb-2"></i>
                    <span>生产设备</span>
                    <div class="trend-line w-0"></div>
                </div>
            </div>
            <div class="type-tab" data-type="network">
                <div class="flex flex-col items-center">
                    <i class="fas fa-network-wired text-xl mb-2"></i>
                    <span>网络设备</span>
                    <div class="trend-line w-0"></div>
                </div>
            </div>
        </div>
        
        <!-- KPI Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="glass-card kpi-card p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-slate-400 text-sm font-medium">资产总数</p>
                        <h3 class="text-3xl font-bold mt-2">1,248</h3>
                        <div class="flex items-center mt-3">
                            <span class="text-green-500 text-sm font-medium flex items-center">
                                <i class="fas fa-arrow-up mr-1"></i> 12.4%
                            </span>
                            <span class="text-slate-500 text-sm ml-2">上月</span>
                        </div>
                    </div>
                    <div class="w-12 h-12 bg-indigo-500/20 rounded-lg flex items-center justify-center">
                        <i class="fas fa-boxes text-indigo-400 text-xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="glass-card kpi-card p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-slate-400 text-sm font-medium">当前类型资产</p>
                        <h3 class="text-3xl font-bold mt-2">248</h3>
                        <div class="flex items-center mt-3">
                            <span class="text-green-500 text-sm font-medium flex items-center">
                                <i class="fas fa-arrow-up mr-1"></i> 8.2%
                            </span>
                            <span class="text-slate-500 text-sm ml-2">上月</span>
                        </div>
                    </div>
                    <div class="w-12 h-12 bg-emerald-500/20 rounded-lg flex items-center justify-center">
                        <i class="fas fa-microchip text-emerald-400 text-xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="glass-card kpi-card p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-slate-400 text-sm font-medium">类型资产总值</p>
                        <h3 class="text-3xl font-bold mt-2">¥687万</h3>
                        <div class="flex items-center mt-3">
                            <span class="text-yellow-500 text-sm font-medium flex items-center">
                                <i class="fas fa-minus mr-1"></i> 2.1%
                            </span>
                            <span class="text-slate-500 text-sm ml-2">上月</span>
                        </div>
                    </div>
                    <div class="w-12 h-12 bg-cyan-500/20 rounded-lg flex items-center justify-center">
                        <i class="fas fa-coins text-cyan-400 text-xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="glass-card kpi-card p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-slate-400 text-sm font-medium">平均使用年限</p>
                        <h3 class="text-3xl font-bold mt-2">4.1年</h3>
                        <div class="flex items-center mt-3">
                            <span class="text-red-500 text-sm font-medium flex items-center">
                                <i class="fas fa-arrow-down mr-1"></i> 0.3年
                            </span>
                            <span class="text-slate-500 text-sm ml-2">上月</span>
                        </div>
                    </div>
                    <div class="w-12 h-12 bg-amber-500/20 rounded-lg flex items-center justify-center">
                        <i class="fas fa-history text-amber-400 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 主分析区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <!-- 左侧图表 -->
            <div class="lg:col-span-2 grid grid-cols-1 gap-6">
                <!-- 资产类型分布图 -->
                <div class="glass-card p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl font-bold">资产类型分布</h2>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 bg-slate-700 hover:bg-slate-600 rounded-lg text-sm transition">数量</button>
                            <button class="px-3 py-1 bg-purple-600 rounded-lg text-sm">价值</button>
                        </div>
                    </div>
                    <div class="chart-container" style="height: 300px;">
                        <canvas id="distributionChart"></canvas>
                    </div>
                </div>
                
                <!-- 部门资产矩阵 -->
                <div class="glass-card p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl font-bold">部门资产矩阵</h2>
                        <div class="text-slate-400 text-sm">
                            <i class="fas fa-info-circle mr-1"></i> 按资产类型和部门分布
                        </div>
                    </div>
                    <div class="grid grid-cols-5 gap-3">
                        <div class="text-center text-sm text-slate-400 p-2">资产类型/部门</div>
                        <div class="text-center text-sm p-2">技术部</div>
                        <div class="text-center text-sm p-2">生产部</div>
                        <div class="text-center text-sm p-2">销售部</div>
                        <div class="text-center text-sm p-2">行政部</div>
                        
                        <div class="text-center text-sm text-slate-400 p-2">工控设备</div>
                        <div class="heatmap-cell bg-slate-800 p-3 rounded-lg text-center cursor-pointer" data-department="tech" data-type="industrial">
                            <div class="font-bold">86</div>
                            <div class="text-xs text-slate-500">¥423万</div>
                        </div>
                        <div class="heatmap-cell bg-slate-800 p-3 rounded-lg text-center cursor-pointer" data-department="production" data-type="industrial">
                            <div class="font-bold">124</div>
                            <div class="text-xs text-slate-500">¥687万</div>
                        </div>
                        <div class="heatmap-cell bg-slate-800 p-3 rounded-lg text-center cursor-pointer" data-department="sales" data-type="industrial">
                            <div class="font-bold">12</div>
                            <div class="text-xs text-slate-500">¥58万</div>
                        </div>
                        <div class="heatmap-cell bg-slate-800 p-3 rounded-lg text-center cursor-pointer" data-department="admin" data-type="industrial">
                            <div class="font-bold">8</div>
                            <div class="text-xs text-slate-500">¥32万</div>
                        </div>
                        
                        <div class="text-center text-sm text-slate-400 p-2">服务器</div>
                        <div class="heatmap-cell bg-slate-800 p-3 rounded-lg text-center cursor-pointer" data-department="tech" data-type="servers">
                            <div class="font-bold">42</div>
                            <div class="text-xs text-slate-500">¥856万</div>
                        </div>
                        <div class="heatmap-cell bg-slate-800 p-3 rounded-lg text-center cursor-pointer" data-department="production" data-type="servers">
                            <div class="font-bold">24</div>
                            <div class="text-xs text-slate-500">¥423万</div>
                        </div>
                        <div class="heatmap-cell bg-slate-800 p-3 rounded-lg text-center cursor-pointer" data-department="sales" data-type="servers">
                            <div class="font-bold">6</div>
                            <div class="text-xs text-slate-500">¥128万</div>
                        </div>
                        <div class="heatmap-cell bg-slate-800 p-3 rounded-lg text-center cursor-pointer" data-department="admin" data-type="servers">
                            <div class="font-bold">3</div>
                            <div class="text-xs text-slate-500">¥64万</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧图表 -->
            <div class="grid grid-cols-1 gap-6">
                <!-- 类型状态分布 -->
                <div class="glass-card p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl font-bold">工控设备状态分布</h2>
                        <div class="text-slate-400 text-sm">
                            <i class="fas fa-sync-alt cursor-pointer hover:text-purple-400"></i>
                        </div>
                    </div>
                    <div class="chart-container" style="height: 200px;">
                        <canvas id="statusChart"></canvas>
                    </div>
                    <div class="grid grid-cols-2 gap-4 mt-6">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-emerald-500 rounded-full mr-2"></div>
                            <span class="text-sm">在用</span>
                            <span class="ml-auto font-medium">198</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-slate-500 rounded-full mr-2"></div>
                            <span class="text-sm">闲置</span>
                            <span class="ml-auto font-medium">24</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-amber-500 rounded-full mr-2"></div>
                            <span class="text-sm">维修中</span>
                            <span class="ml-auto font-medium">18</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-rose-500 rounded-full mr-2"></div>
                            <span class="text-sm">报废</span>
                            <span class="ml-auto font-medium">8</span>
                        </div>
                    </div>
                </div>
                
                <!-- 类型价值趋势 -->
                <div class="glass-card p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl font-bold">工控设备价值趋势</h2>
                        <div class="text-slate-400 text-sm">
                            <i class="fas fa-expand cursor-pointer hover:text-purple-400"></i>
                        </div>
                    </div>
                    <div class="chart-container" style="height: 200px;">
                        <canvas id="valueTrendChart"></canvas>
                    </div>
                </div>
                
                <!-- 类型位置分布 -->
                <div class="glass-card p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl font-bold">位置分布</h2>
                        <div class="text-slate-400 text-sm">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                    </div>
                    <div class="cross-analysis-grid">
                        <div class="cross-analysis-cell">
                            <div class="text-sm text-slate-400">制芯车间</div>
                            <div class="font-bold text-xl mt-1">124</div>
                            <div class="text-xs text-slate-500 mt-1">¥687万</div>
                        </div>
                        <div class="cross-analysis-cell">
                            <div class="text-sm text-slate-400">铸造车间</div>
                            <div class="font-bold text-xl mt-1">86</div>
                            <div class="text-xs text-slate-500 mt-1">¥423万</div>
                        </div>
                        <div class="cross-analysis-cell">
                            <div class="text-sm text-slate-400">研发中心</div>
                            <div class="font-bold text-xl mt-1">24</div>
                            <div class="text-xs text-slate-500 mt-1">¥156万</div>
                        </div>
                        <div class="cross-analysis-cell">
                            <div class="text-sm text-slate-400">数据中心</div>
                            <div class="font-bold text-xl mt-1">14</div>
                            <div class="text-xs text-slate-500 mt-1">¥98万</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 筛选控制面板 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <!-- 资产类型筛选器 -->
            <div class="glass-card p-6">
                <h2 class="text-lg font-semibold mb-4">资产类型筛选</h2>
                <div class="type-selector-grid">
                    <div class="type-distribution-card glass-card p-4 text-center cursor-pointer">
                        <i class="fas fa-industry text-2xl mb-2 text-purple-400"></i>
                        <div class="font-medium">工控设备</div>
                        <div class="text-sm text-slate-500">248</div>
                    </div>
                    <div class="type-distribution-card glass-card p-4 text-center cursor-pointer">
                        <i class="fas fa-server text-2xl mb-2 text-blue-400"></i>
                        <div class="font-medium">服务器</div>
                        <div class="text-sm text-slate-500">86</div>
                    </div>
                    <div class="type-distribution-card glass-card p-4 text-center cursor-pointer">
                        <i class="fas fa-laptop text-2xl mb-2 text-green-400"></i>
                        <div class="font-medium">电脑设备</div>
                        <div class="text-sm text-slate-500">542</div>
                    </div>
                    <div class="type-distribution-card glass-card p-4 text-center cursor-pointer">
                        <i class="fas fa-print text-2xl mb-2 text-amber-400"></i>
                        <div class="font-medium">办公设备</div>
                        <div class="text-sm text-slate-500">287</div>
                    </div>
                    <div class="type-distribution-card glass-card p-4 text-center cursor-pointer">
                        <i class="fas fa-cogs text-2xl mb-2 text-red-400"></i>
                        <div class="font-medium">生产设备</div>
                        <div class="text-sm text-slate-500">98</div>
                    </div>
                    <div class="type-distribution-card glass-card p-4 text-center cursor-pointer">
                        <i class="fas fa-network-wired text-2xl mb-2 text-cyan-400"></i>
                        <div class="font-medium">网络设备</div>
                        <div class="text-sm text-slate-500">87</div>
                    </div>
                </div>
            </div>
            
            <!-- 部门筛选 -->
            <div class="glass-card p-6">
                <h2 class="text-lg font-semibold mb-4">部门分布</h2>
                <div class="space-y-3">
                    <label class="flex items-center justify-between p-3 bg-slate-800/50 rounded-lg cursor-pointer">
                        <div class="flex items-center">
                            <i class="fas fa-industry text-purple-400 mr-3"></i>
                            <span>生产部</span>
                        </div>
                        <div>
                            <span class="font-bold">124</span>
                            <span class="text-sm text-slate-500 ml-2">¥687万</span>
                        </div>
                    </label>
                    <label class="flex items-center justify-between p-3 rounded-lg cursor-pointer hover:bg-slate-800/50">
                        <div class="flex items-center">
                            <i class="fas fa-laptop-code text-blue-400 mr-3"></i>
                            <span>技术部</span>
                        </div>
                        <div>
                            <span class="font-bold">86</span>
                            <span class="text-sm text-slate-500 ml-2">¥423万</span>
                        </div>
                    </label>
                    <label class="flex items-center justify-between p-3 rounded-lg cursor-pointer hover:bg-slate-800/50">
                        <div class="flex items-center">
                            <i class="fas fa-chart-line text-green-400 mr-3"></i>
                            <span>销售部</span>
                        </div>
                        <div>
                            <span class="font-bold">24</span>
                            <span class="text-sm text-slate-500 ml-2">¥156万</span>
                        </div>
                    </label>
                    <label class="flex items-center justify-between p-3 rounded-lg cursor-pointer hover:bg-slate-800/50">
                        <div class="flex items-center">
                            <i class="fas fa-file-contract text-amber-400 mr-3"></i>
                            <span>行政部</span>
                        </div>
                        <div>
                            <span class="font-bold">14</span>
                            <span class="text-sm text-slate-500 ml-2">¥98万</span>
                        </div>
                    </label>
                </div>
            </div>
            
            <!-- 状态筛选 -->
            <div class="glass-card p-6">
                <h2 class="text-lg font-semibold mb-4">状态筛选</h2>
                <div class="space-y-3">
                    <label class="flex items-center justify-between p-3 bg-slate-800/50 rounded-lg cursor-pointer">
                        <div class="flex items-center">
                            <span class="pulse bg-emerald-400"></span>
                            <span>在用</span>
                        </div>
                        <div>
                            <span class="font-bold">198</span>
                            <span class="text-sm text-slate-500 ml-2">¥1,256万</span>
                        </div>
                    </label>
                    <label class="flex items-center justify-between p-3 rounded-lg cursor-pointer hover:bg-slate-800/50">
                        <div class="flex items-center">
                            <span class="pulse bg-slate-400"></span>
                            <span>闲置</span>
                        </div>
                        <div>
                            <span class="font-bold">24</span>
                            <span class="text-sm text-slate-500 ml-2">¥156万</span>
                        </div>
                    </label>
                    <label class="flex items-center justify-between p-3 rounded-lg cursor-pointer hover:bg-slate-800/50">
                        <div class="flex items-center">
                            <span class="pulse bg-amber-400"></span>
                            <span>维修中</span>
                        </div>
                        <div>
                            <span class="font-bold">18</span>
                            <span class="text-sm text-slate-500 ml-2">¥86万</span>
                        </div>
                    </label>
                    <label class="flex items-center justify-between p-3 rounded-lg cursor-pointer hover:bg-slate-800/50">
                        <div class="flex items-center">
                            <span class="pulse bg-rose-400"></span>
                            <span>报废</span>
                        </div>
                        <div>
                            <span class="font-bold">8</span>
                            <span class="text-sm text-slate-500 ml-2">¥32万</span>
                        </div>
                    </label>
                </div>
            </div>
        </div>
        
        <!-- 资产明细表 -->
        <div class="glass-card p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold">工控设备明细</h2>
                <div class="flex space-x-3">
                    <button class="px-4 py-2 bg-slate-700 hover:bg-slate-600 rounded-lg text-sm transition flex items-center">
                        <i class="fas fa-plus mr-2"></i> 添加资产
                    </button>
                    <button class="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg text-sm transition flex items-center">
                        <i class="fas fa-file-export mr-2"></i> 导出数据
                    </button>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-slate-400">
                    <thead class="text-xs text-slate-500 uppercase bg-slate-800/50">
                        <tr>
                            <th scope="col" class="px-6 py-3">资产编号</th>
                            <th scope="col" class="px-6 py-3">名称</th>
                            <th scope="col" class="px-6 py-3">位置</th>
                            <th scope="col" class="px-6 py-3">部门/负责人</th>
                            <th scope="col" class="px-6 py-3">状态</th>
                            <th scope="col" class="px-6 py-3 text-right">价值(¥)</th>
                            <th scope="col" class="px-6 py-3">购置日期</th>
                        </tr>
                    </thead>
                    <tbody id="asset-table-body">
                        <!-- 资产数据将通过JavaScript动态填充 -->
                    </tbody>
                </table>
            </div>
            
            <div class="flex items-center justify-between mt-6">
                <span class="text-sm text-slate-500">显示 <span id="asset-count">5</span> 条，共 248 条资产</span>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 bg-slate-700 hover:bg-slate-600 rounded text-sm transition">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="px-3 py-1 bg-purple-600 rounded text-sm">1</button>
                    <button class="px-3 py-1 bg-slate-700 hover:bg-slate-600 rounded text-sm transition">2</button>
                    <button class="px-3 py-1 bg-slate-700 hover:bg-slate-600 rounded text-sm transition">3</button>
                    <button class="px-3 py-1 bg-slate-700 hover:bg-slate-600 rounded text-sm transition">...</button>
                    <button class="px-3 py-1 bg-slate-700 hover:bg-slate-600 rounded text-sm transition">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <footer class="text-center text-slate-500 text-sm py-6">
            <p>企业级智能资产分析平台 • 数据更新于 <span id="update-time">2025年06月06日 14:28:35</span></p>
            <p class="mt-2">© 2025 企业资产管理解决方案 - 保留所有权利</p>
        </footer>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 模拟API获取的数据
            const assetData = [
                {
                    id: 671,
                    assetCode: "IPC001",
                    assetName: "工控机",
                    assetType: "工控设备",
                    location: "制芯A1",
                    locationPath: "工厂/生产区/制芯车间/制芯A1",
                    department: "生产部",
                    manager: "张工",
                    status: "在用",
                    price: 12500.00,
                    purchasedAt: "2023-04-03"
                },
                {
                    id: 672,
                    assetCode: "PLC-042",
                    assetName: "PLC控制器",
                    assetType: "工控设备",
                    location: "铸造B2",
                    locationPath: "工厂/生产区/铸造车间/铸造B2",
                    department: "生产部",
                    manager: "李工",
                    status: "在用",
                    price: 18500.00,
                    purchasedAt: "2024-03-15"
                },
                {
                    id: 673,
                    assetCode: "HMI-008",
                    assetName: "人机界面",
                    assetType: "工控设备",
                    location: "制芯A3",
                    locationPath: "工厂/生产区/制芯车间/制芯A3",
                    department: "生产部",
                    manager: "王工",
                    status: "维修中",
                    price: 8600.00,
                    purchasedAt: "2022-11-20"
                },
                {
                    id: 674,
                    assetCode: "SENSOR-045",
                    assetName: "工业传感器",
                    assetType: "工控设备",
                    location: "研发实验室",
                    locationPath: "总部/研发楼/创新中心/实验室",
                    department: "研发部",
                    manager: "赵博士",
                    status: "在用",
                    price: 3200.00,
                    purchasedAt: "2024-01-10"
                },
                {
                    id: 675,
                    assetCode: "ROBOT-008",
                    assetName: "机械臂控制器",
                    assetType: "工控设备",
                    location: "装配线C",
                    locationPath: "工厂/装配区/C线",
                    department: "生产部",
                    manager: "张工",
                    status: "在用",
                    price: 28900.00,
                    purchasedAt: "2023-09-05"
                }
            ];
            
            // 渲染资产表格
            function renderAssetTable(assets) {
                const tbody = document.getElementById('asset-table-body');
                tbody.innerHTML = '';
                
                assets.forEach(asset => {
                    // 根据状态设置样式
                    let statusClass, statusIcon, pulseClass;
                    switch(asset.status) {
                        case '在用':
                            statusClass = 'bg-emerald-500/20 text-emerald-400';
                            statusIcon = 'bg-emerald-400';
                            pulseClass = 'pulse bg-emerald-400';
                            break;
                        case '闲置':
                            statusClass = 'bg-slate-500/20 text-slate-400';
                            statusIcon = 'bg-slate-400';
                            pulseClass = 'pulse bg-slate-400';
                            break;
                        case '维修中':
                            statusClass = 'bg-amber-500/20 text-amber-400';
                            statusIcon = 'bg-amber-400';
                            pulseClass = 'pulse bg-amber-400';
                            break;
                        case '报废':
                            statusClass = 'bg-rose-500/20 text-rose-400';
                            statusIcon = 'bg-rose-400';
                            pulseClass = 'pulse bg-rose-400';
                            break;
                        default:
                            statusClass = 'bg-slate-500/20 text-slate-400';
                            statusIcon = 'bg-slate-400';
                            pulseClass = 'pulse bg-slate-400';
                    }
                    
                    const row = document.createElement('tr');
                    row.className = 'border-b border-slate-700 hover:bg-slate-800/50';
                    row.innerHTML = `
                        <td class="px-6 py-4 font-medium text-white">${asset.assetCode}</td>
                        <td class="px-6 py-4">${asset.assetName}</td>
                        <td class="px-6 py-4">
                            <div>${asset.location}</div>
                            <div class="location-path">${asset.locationPath}</div>
                        </td>
                        <td class="px-6 py-4">
                            <div>${asset.department}</div>
                            <div class="location-path">${asset.manager}</div>
                        </td>
                        <td class="px-6 py-4">
                            <span class="status-badge ${statusClass}">
                                <span class="${pulseClass}"></span> ${asset.status}
                            </span>
                        </td>
                        <td class="px-6 py-4 text-right">${asset.price.toLocaleString('zh-CN', {maximumFractionDigits:2})}</td>
                        <td class="px-6 py-4">${asset.purchasedAt}</td>
                    `;
                    tbody.appendChild(row);
                });
                
                document.getElementById('asset-count').textContent = assets.length;
            }
            
            // 初始化图表
            function initCharts() {
                // 资产分布图
                const distCtx = document.getElementById('distributionChart').getContext('2d');
                new Chart(distCtx, {
                    type: 'bar',
                    data: {
                        labels: ['工控设备', '服务器', '电脑设备', '办公设备', '生产设备', '网络设备'],
                        datasets: [{
                            label: '资产数量',
                            data: [248, 86, 542, 287, 98, 87],
                            backgroundColor: 'rgba(99, 102, 241, 0.7)',
                            borderColor: 'rgba(99, 102, 241, 1)',
                            borderWidth: 1,
                            borderRadius: 6
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            x: {
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.05)'
                                },
                                ticks: {
                                    color: '#94a3b8'
                                }
                            },
                            y: {
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.05)'
                                },
                                ticks: {
                                    color: '#94a3b8'
                                },
                                beginAtZero: true
                            }
                        }
                    }
                });
                
                // 资产状态图
                const statusCtx = document.getElementById('statusChart').getContext('2d');
                new Chart(statusCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['在用', '闲置', '维修中', '报废'],
                        datasets: [{
                            data: [79, 10, 7, 4],
                            backgroundColor: [
                                'rgba(16, 185, 129, 0.8)',
                                'rgba(148, 163, 184, 0.8)',
                                'rgba(245, 158, 11, 0.8)',
                                'rgba(239, 68, 68, 0.8)'
                            ],
                            borderColor: [
                                'rgba(16, 185, 129, 1)',
                                'rgba(148, 163, 184, 1)',
                                'rgba(245, 158, 11, 1)',
                                'rgba(239, 68, 68, 1)'
                            ],
                            borderWidth: 1,
                            cutout: '70%'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
                
                // 价值趋势图
                const trendCtx = document.getElementById('valueTrendChart').getContext('2d');
                new Chart(trendCtx, {
                    type: 'line',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                        datasets: [{
                            label: '工控设备总值',
                            data: [520, 580, 620, 650, 670, 687],
                            borderColor: 'rgba(139, 92, 246, 1)',
                            backgroundColor: 'rgba(139, 92, 246, 0.1)',
                            tension: 0.3,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            x: {
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.05)'
                                },
                                ticks: {
                                    color: '#94a3b8'
                                }
                            },
                            y: {
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.05)'
                                },
                                ticks: {
                                    color: '#94a3b8',
                                    callback: function(value) {
                                        return '¥' + value + '万';
                                    }
                                }
                            }
                        }
                    }
                });
            }
            
            // 更新时间
            function updateTimestamp() {
                const now = new Date();
                const formatted = now.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                }).replace(/\//g, '-');
                
                document.getElementById('update-time').textContent = formatted;
            }
            
            // 热力图点击事件
            function setupHeatmapInteractions() {
                const cells = document.querySelectorAll('.heatmap-cell');
                cells.forEach(cell => {
                    cell.addEventListener('click', function() {
                        const department = this.getAttribute('data-department');
                        const type = this.getAttribute('data-type');
                        
                        // 模拟筛选
                        const departmentNames = {
                            'tech': '技术部',
                            'production': '生产部',
                            'sales': '销售部',
                            'admin': '行政部'
                        };
                        
                        const typeNames = {
                            'industrial': '工控设备',
                            'servers': '服务器'
                        };
                        
                        alert(`钻取查看: ${departmentNames[department]} - ${typeNames[type]}`);
                    });
                });
            }
            
            // 资产类型标签事件
            function setupTypeTabs() {
                const tabs = document.querySelectorAll('.type-tab');
                tabs.forEach(tab => {
                    tab.addEventListener('click', function() {
                        // 移除所有active类
                        tabs.forEach(t => {
                            t.classList.remove('active');
                            t.querySelector('.trend-line').style.width = '0';
                        });
                        
                        // 为当前标签添加active类
                        this.classList.add('active');
                        this.querySelector('.trend-line').style.width = '100%';
                        
                        // 更新当前维度显示
                        const type = this.getAttribute('data-type');
                        const typeNames = {
                            'all': '全部资产',
                            'industrial': '工控设备',
                            'servers': '服务器',
                            'computers': '电脑设备',
                            'office': '办公设备',
                            'production': '生产设备',
                            'network': '网络设备'
                        };
                        
                        document.getElementById('current-dimension').textContent = typeNames[type];
                        
                        // 更新钻取路径
                        const drilldown = document.querySelector('.drilldown-path');
                        if (type === 'all') {
                            drilldown.innerHTML = `
                                <span>全部资产</span>
                            `;
                        } else {
                            drilldown.innerHTML = `
                                <span>全部资产</span>
                                <i class="fas fa-chevron-right"></i>
                                <span>${typeNames[type]}</span>
                            `;
                        }
                    });
                });
            }
            
            // 资产类型卡片事件
            function setupTypeCards() {
                const cards = document.querySelectorAll('.type-distribution-card');
                cards.forEach(card => {
                    card.addEventListener('click', function() {
                        // 高亮当前选择的卡片
                        cards.forEach(c => c.classList.remove('bg-purple-600/20'));
                        this.classList.add('bg-purple-600/20');
                        
                        // 更新钻取路径
                        const typeName = this.querySelector('.font-medium').textContent;
                        const drilldown = document.querySelector('.drilldown-path');
                        drilldown.innerHTML = `
                            <span>全部资产</span>
                            <i class="fas fa-chevron-right"></i>
                            <span>${typeName}</span>
                        `;
                    });
                });
            }
            
            // 初始化页面
            renderAssetTable(assetData);
            initCharts();
            updateTimestamp();
            setupHeatmapInteractions();
            setupTypeTabs();
            setupTypeCards();
            
            // 设置定时更新
            setInterval(updateTimestamp, 1000);
            
            // 刷新按钮事件
            document.getElementById('refresh-btn').addEventListener('click', function() {
                this.querySelector('i').classList.add('animate-spin');
                setTimeout(() => {
                    this.querySelector('i').classList.remove('animate-spin');
                    updateTimestamp();
                }, 1000);
            });
        });
    </script>
</body>
</html>
```

## 优化后的资产类型分析设计

### 1. 资产类型作为核心分析维度

在优化后的设计中，资产类型已作为交叉坐标维度的核心元素：

- **主导航栏**：顶部专门的资产类型导航栏，提供快速切换
- **KPI卡片**：专门展示当前类型资产的关键指标
- **部门资产矩阵**：资产类型作为行维度，部门作为列维度
- **类型分布图**：专门展示不同类型资产的分布情况
- **类型状态分析**：展示特定类型资产的状态分布
- **类型价值趋势**：追踪特定类型资产的价值变化
- **类型位置分布**：展示特定类型资产在不同位置的分布

### 2. 资产类型分析的优势

1. **深入洞察**：针对特定类型资产进行深入分析
2. **资源优化**：识别高价值资产类型的分布和状态
3. **预算规划**：基于类型价值趋势进行采购规划
4. **维护策略**：根据类型状态分布优化维护计划
5. **部门对比**：比较不同部门在特定资产类型上的分布

### 3. 实现逻辑

```mermaid
graph TD
    A[资产类型选择] --> B[更新KPI指标]
    A --> C[更新图表数据]
    A --> D[更新明细表格]
    A --> E[更新筛选状态]
    B --> F[显示类型资产数量和总值]
    C --> G[显示类型分布和趋势]
    D --> H[显示类型资产明细]
    E --> I[更新钻取路径和筛选条件]
```

这个优化设计将资产类型置于分析平台的核心位置，提供了更专业、更深入的资产类型分析能力，同时保持了原有的部门负责人关联功能和多维度筛选系统。这个方案是否更合适