using System.Collections.Generic;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Features.Suppliers.Dtos;
using ItAssetsSystem.Application.Common.Dtos;

namespace ItAssetsSystem.Application.Features.Suppliers.Services
{
    /// <summary>
    /// 供应商服务接口
    /// </summary>
    public interface ISupplierService
    {
        /// <summary>
        /// 获取供应商列表（分页）
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>分页结果</returns>
        Task<PaginatedResult<SupplierDto>> GetSuppliersAsync(SupplierQuery query);

        /// <summary>
        /// 根据ID获取供应商详情
        /// </summary>
        /// <param name="id">供应商ID</param>
        /// <returns>供应商详情</returns>
        Task<SupplierDto> GetSupplierByIdAsync(int id);

        /// <summary>
        /// 创建供应商
        /// </summary>
        /// <param name="request">创建请求</param>
        /// <returns>创建的供应商</returns>
        Task<SupplierDto> CreateSupplierAsync(CreateSupplierRequest request);

        /// <summary>
        /// 更新供应商
        /// </summary>
        /// <param name="id">供应商ID</param>
        /// <param name="request">更新请求</param>
        /// <returns>更新后的供应商</returns>
        Task<SupplierDto> UpdateSupplierAsync(int id, UpdateSupplierRequest request);

        /// <summary>
        /// 删除供应商
        /// </summary>
        /// <param name="id">供应商ID</param>
        /// <returns>是否删除成功</returns>
        Task<bool> DeleteSupplierAsync(int id);

        /// <summary>
        /// 获取维修供应商列表
        /// </summary>
        /// <returns>维修供应商列表</returns>
        Task<List<SupplierSimpleDto>> GetMaintenanceSuppliersAsync();

        /// <summary>
        /// 获取采购供应商列表
        /// </summary>
        /// <returns>采购供应商列表</returns>
        Task<List<SupplierSimpleDto>> GetProcurementSuppliersAsync();

        /// <summary>
        /// 获取所有激活的供应商（简化版本，用于下拉选择）
        /// </summary>
        /// <returns>供应商简化列表</returns>
        Task<List<SupplierSimpleDto>> GetActiveSuppliersAsync();

        /// <summary>
        /// 检查供应商编码是否存在
        /// </summary>
        /// <param name="code">供应商编码</param>
        /// <param name="excludeId">排除的ID（用于更新时检查）</param>
        /// <returns>是否存在</returns>
        Task<bool> IsCodeExistsAsync(string code, int? excludeId = null);

        /// <summary>
        /// 检查供应商名称是否存在
        /// </summary>
        /// <param name="name">供应商名称</param>
        /// <param name="excludeId">排除的ID（用于更新时检查）</param>
        /// <returns>是否存在</returns>
        Task<bool> IsNameExistsAsync(string name, int? excludeId = null);
    }
}
