using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Domain.Entities
{
    /// <summary>
    /// 返厂维修明细实体
    /// </summary>
    [Table("repair_order_items")]
    public class RepairOrderItem
    {
        /// <summary>
        /// 明细ID
        /// </summary>
        [Key]
        [Column("id")]
        public long Id { get; set; }
        
        /// <summary>
        /// 返厂单ID
        /// </summary>
        [Column("repair_order_id")]
        public int RepairOrderId { get; set; }
        
        /// <summary>
        /// 返厂单导航属性
        /// </summary>
        [ForeignKey("RepairOrderId")]
        public virtual RepairOrder RepairOrder { get; set; }
        
        /// <summary>
        /// 物品类型
        /// </summary>
        [Column("item_type")]
        public RepairItemType ItemType { get; set; }
        
        /// <summary>
        /// 备件ID（当item_type=1时）
        /// </summary>
        [Column("part_id")]
        public long? PartId { get; set; }
        
        /// <summary>
        /// 备件导航属性
        /// </summary>
        [ForeignKey("PartId")]
        public virtual SparePart Part { get; set; }
        
        /// <summary>
        /// 资产ID（当item_type=2时）
        /// </summary>
        [Column("asset_id")]
        public int? AssetId { get; set; }
        
        /// <summary>
        /// 组件名称
        /// </summary>
        [Column("component_name")]
        [StringLength(200)]
        public string ComponentName { get; set; }
        
        /// <summary>
        /// 序列号
        /// </summary>
        [Column("serial_number")]
        [StringLength(100)]
        public string SerialNumber { get; set; }
        
        /// <summary>
        /// 数量
        /// </summary>
        [Column("quantity")]
        public int Quantity { get; set; } = 1;
        
        /// <summary>
        /// 故障描述
        /// </summary>
        [Column("fault_description")]
        public string FaultDescription { get; set; }
        
        /// <summary>
        /// 维修说明
        /// </summary>
        [Column("repair_description")]
        public string RepairDescription { get; set; }
        
        /// <summary>
        /// 维修前状态
        /// </summary>
        [Column("before_status")]
        [StringLength(50)]
        public string BeforeStatus { get; set; }
        
        /// <summary>
        /// 维修后状态
        /// </summary>
        [Column("after_status")]
        [StringLength(50)]
        public string AfterStatus { get; set; }
        
        /// <summary>
        /// 单项费用
        /// </summary>
        [Column("unit_cost")]
        public decimal? UnitCost { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        [Column("notes")]
        [StringLength(500)]
        public string Notes { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        /// <summary>
        /// 更新时间
        /// </summary>
        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }
    
    /// <summary>
    /// 维修物品类型枚举
    /// </summary>
    public enum RepairItemType
    {
        /// <summary>
        /// 备件
        /// </summary>
        SparePart = 1,
        
        /// <summary>
        /// 资产组件
        /// </summary>
        AssetComponent = 2
    }
}
