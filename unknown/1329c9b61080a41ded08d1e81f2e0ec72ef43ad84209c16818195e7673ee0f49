using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ItAssetsSystem.Core.Interfaces.Services
{
    /// <summary>
    /// 位置管理服务接口 - 基础解耦
    /// 提供位置相关的核心业务操作
    /// </summary>
    public interface ILocationService
    {
        /// <summary>
        /// 根据ID获取位置信息
        /// </summary>
        /// <param name="id">位置ID</param>
        /// <returns>位置信息</returns>
        Task<LocationDto> GetByIdAsync(int id);

        /// <summary>
        /// 分页获取位置列表
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResult<LocationDto>> GetPagedAsync(LocationQueryDto query);

        /// <summary>
        /// 获取位置树形结构
        /// </summary>
        /// <returns>位置树</returns>
        Task<List<LocationTreeDto>> GetTreeAsync();

        /// <summary>
        /// 创建位置
        /// </summary>
        /// <param name="dto">创建位置DTO</param>
        /// <returns>创建的位置信息</returns>
        Task<LocationDto> CreateAsync(CreateLocationDto dto);

        /// <summary>
        /// 更新位置
        /// </summary>
        /// <param name="id">位置ID</param>
        /// <param name="dto">更新位置DTO</param>
        /// <returns>更新后的位置信息</returns>
        Task<LocationDto> UpdateAsync(int id, UpdateLocationDto dto);

        /// <summary>
        /// 删除位置
        /// </summary>
        /// <param name="id">位置ID</param>
        /// <returns>是否删除成功</returns>
        Task<bool> DeleteAsync(int id);

        /// <summary>
        /// 检查位置是否存在
        /// </summary>
        /// <param name="id">位置ID</param>
        /// <returns>是否存在</returns>
        Task<bool> ExistsAsync(int id);
    }

    /// <summary>
    /// 位置DTO
    /// </summary>
    public class LocationDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public int? ParentId { get; set; }
        public string ParentName { get; set; }
        public string Path { get; set; }
        public int Level { get; set; }
        public int? DepartmentId { get; set; }
        public string DepartmentName { get; set; }
        public string LocationType { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public List<LocationDto> Children { get; set; } = new List<LocationDto>();
    }

    /// <summary>
    /// 位置查询DTO
    /// </summary>
    public class LocationQueryDto
    {
        public int PageIndex { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string Keyword { get; set; }
        public int? ParentId { get; set; }
        public int? DepartmentId { get; set; }
        public string LocationType { get; set; }
        public bool? IsActive { get; set; }
        public string SortBy { get; set; }
        public string SortDirection { get; set; } = "asc";
    }

    /// <summary>
    /// 创建位置DTO
    /// </summary>
    public class CreateLocationDto
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public int? ParentId { get; set; }
        public int? DepartmentId { get; set; }
        public string LocationType { get; set; }
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 更新位置DTO
    /// </summary>
    public class UpdateLocationDto
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public int? ParentId { get; set; }
        public int? DepartmentId { get; set; }
        public string LocationType { get; set; }
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// 位置树形结构DTO
    /// </summary>
    public class LocationTreeDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public int? ParentId { get; set; }
        public string Path { get; set; }
        public int Level { get; set; }
        public string LocationType { get; set; }
        public bool IsActive { get; set; }
        public List<LocationTreeDto> Children { get; set; } = new List<LocationTreeDto>();
    }
}
