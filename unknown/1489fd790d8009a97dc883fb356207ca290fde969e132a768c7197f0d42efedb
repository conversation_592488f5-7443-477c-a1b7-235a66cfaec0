using System;
using System.ComponentModel.DataAnnotations;
using ItAssetsSystem.Models.Enums;
using ItAssetsSystem.Application.Common.Dtos;

namespace ItAssetsSystem.Application.Features.Suppliers.Dtos
{
    /// <summary>
    /// 供应商DTO
    /// </summary>
    public class SupplierDto
    {
        /// <summary>
        /// 供应商ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 供应商编码
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 供应商类型
        /// </summary>
        public SupplierType SupplierType { get; set; }

        /// <summary>
        /// 供应商类型显示名称
        /// </summary>
        public string SupplierTypeDisplay { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        public string ContactPerson { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        public string ContactPhone { get; set; }

        /// <summary>
        /// 联系邮箱
        /// </summary>
        public string ContactEmail { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// 创建供应商请求
    /// </summary>
    public class CreateSupplierRequest
    {
        /// <summary>
        /// 供应商名称
        /// </summary>
        [Required(ErrorMessage = "供应商名称不能为空")]
        [StringLength(100, ErrorMessage = "供应商名称长度不能超过100个字符")]
        public string Name { get; set; }

        /// <summary>
        /// 供应商编码
        /// </summary>
        [Required(ErrorMessage = "供应商编码不能为空")]
        [StringLength(50, ErrorMessage = "供应商编码长度不能超过50个字符")]
        public string Code { get; set; }

        /// <summary>
        /// 供应商类型
        /// </summary>
        [Required(ErrorMessage = "供应商类型不能为空")]
        public SupplierType SupplierType { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        [StringLength(50, ErrorMessage = "联系人长度不能超过50个字符")]
        public string ContactPerson { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [StringLength(20, ErrorMessage = "联系电话长度不能超过20个字符")]
        public string ContactPhone { get; set; }

        /// <summary>
        /// 联系邮箱
        /// </summary>
        [StringLength(100, ErrorMessage = "联系邮箱长度不能超过100个字符")]
        public string ContactEmail { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        [StringLength(200, ErrorMessage = "地址长度不能超过200个字符")]
        public string Address { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string Notes { get; set; }
    }

    /// <summary>
    /// 更新供应商请求
    /// </summary>
    public class UpdateSupplierRequest : CreateSupplierRequest
    {
        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 供应商查询参数
    /// </summary>
    public class SupplierQuery : PaginationQuery
    {
        /// <summary>
        /// 供应商名称（模糊搜索）
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 供应商编码（模糊搜索）
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 供应商类型
        /// </summary>
        public SupplierType? SupplierType { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// 联系人（模糊搜索）
        /// </summary>
        public string ContactPerson { get; set; }
    }

    /// <summary>
    /// 简化的供应商DTO（用于下拉选择）
    /// </summary>
    public class SupplierSimpleDto
    {
        /// <summary>
        /// 供应商ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 供应商编码
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 供应商类型
        /// </summary>
        public SupplierType SupplierType { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        public string ContactPerson { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        public string ContactPhone { get; set; }
    }
}
