/**
 * 智能制造监控系统 - 工厂状态管理
 * 使用 Pinia 管理工厂数据、筛选状态和UI状态
 */

import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'
import factoryDataService from '@/services/factoryDataService'

export const useFactoryStore = defineStore('factory', {
  state: () => ({
    // 数据状态
    locations: [],
    departments: [],
    selectedLocationId: null,
    selectedLocationDetails: null,
    
    // UI状态
    isLoading: false,
    viewMode: 'layout', // 'layout' | 'list'
    zoomLevel: 1,
    isFullScreen: false,
    hoveredLocationId: null,
    
    // 筛选状态
    searchTerm: '',
    searchResults: [],
    statusFilters: ['operational', 'warning', 'error', 'idle'],
    filterForm: {
      departmentId: null,
      locationType: null,
      onlyWithAssets: false
    },
    
    // 系统状态
    systemStatus: 'online', // 'online' | 'warning' | 'offline'
    lastUpdate: new Date(),
    connectionStatus: 'connected',
    errorCount: 0,
    
    // 缓存设置
    refreshInterval: 5000,
    autoRefresh: true,
    
    // 显示设置
    showTooltips: true,
    animationEnabled: true,
    highlightCritical: true
  }),

  getters: {
    // 基础统计
    stats: (state) => {
      const total = state.locations.length
      const operational = state.locations.filter(l => l.status === 'operational').length
      const warning = state.locations.filter(l => l.status === 'warning').length
      const error = state.locations.filter(l => l.status === 'error').length
      const idle = state.locations.filter(l => l.status === 'idle').length
      
      return {
        total,
        operational,
        warning,
        error,
        idle,
        operationalPercent: total > 0 ? Math.round(operational / total * 100) : 0
      }
    },

    // 平均效率
    avgEfficiency: (state) => {
      if (state.locations.length === 0) return 0
      const totalEfficiency = state.locations.reduce((sum, loc) => sum + (loc.efficiency || 0), 0)
      return Math.round(totalEfficiency / state.locations.length)
    },

    // 总设备数
    totalAssets: (state) => {
      return state.locations.reduce((sum, loc) => sum + (loc.assetCount || 0), 0)
    },

    // 优先工位（有问题的工位）
    priorityWorkstations: (state) => {
      return state.locations
        .filter(loc => loc.status === 'warning' || loc.status === 'error')
        .sort((a, b) => {
          // 错误状态优先级最高
          if (a.status === 'error' && b.status !== 'error') return -1
          if (b.status === 'error' && a.status !== 'error') return 1
          // 按故障数量排序
          return (b.faultCount || 0) - (a.faultCount || 0)
        })
        .slice(0, 10)
    },

    // 关键工位（标记为重要的）
    criticalWorkstations: (state) => {
      return state.locations.filter(loc => loc.isHighlighted)
    },

    // 筛选后的工位
    filteredLocations: (state) => {
      let filtered = state.locations

      // 状态筛选
      if (state.statusFilters.length < 4) {
        filtered = filtered.filter(loc => state.statusFilters.includes(loc.status))
      }

      // 部门筛选
      if (state.filterForm.departmentId) {
        filtered = filtered.filter(loc => loc.departmentId === state.filterForm.departmentId)
      }

      // 位置类型筛选
      if (state.filterForm.locationType) {
        filtered = filtered.filter(loc => loc.locationType === state.filterForm.locationType)
      }

      // 只显示有设备的位置
      if (state.filterForm.onlyWithAssets) {
        filtered = filtered.filter(loc => (loc.assetCount || 0) > 0)
      }

      // 搜索筛选
      if (state.searchTerm) {
        const term = state.searchTerm.toLowerCase()
        filtered = filtered.filter(loc => 
          (loc.locationName || '').toLowerCase().includes(term) ||
          (loc.locationCode || '').toLowerCase().includes(term) ||
          (loc.departmentName || '').toLowerCase().includes(term)
        )
      }

      return filtered
    },

    // 显示的工位数量
    displayedCount: (state) => {
      return state.filteredLocations?.length || 0
    },

    // 系统状态文本
    systemStatusText: (state) => {
      const statusMap = {
        'online': '正常',
        'warning': '警告',
        'offline': '离线'
      }
      return statusMap[state.systemStatus] || '未知'
    },

    // 是否有活跃筛选
    hasActiveFilters: (state) => {
      return state.statusFilters.length < 4 || 
             state.filterForm.departmentId || 
             state.filterForm.locationType || 
             state.filterForm.onlyWithAssets ||
             state.searchTerm
    },

    // 活跃筛选数量
    activeFilterCount: (state) => {
      let count = 0
      if (state.statusFilters.length < 4) count++
      if (state.filterForm.departmentId) count++
      if (state.filterForm.locationType) count++
      if (state.filterForm.onlyWithAssets) count++
      if (state.searchTerm) count++
      return count
    },

    // 区域统计
    zoneStats: (state) => {
      const zones = {}
      state.locations.forEach(loc => {
        const zoneId = loc.zoneId || 'unknown'
        if (!zones[zoneId]) {
          zones[zoneId] = {
            total: 0,
            operational: 0,
            warning: 0,
            error: 0,
            idle: 0,
            efficiency: 0
          }
        }
        
        zones[zoneId].total++
        zones[zoneId][loc.status]++
        zones[zoneId].efficiency += loc.efficiency || 0
      })

      // 计算平均效率
      Object.keys(zones).forEach(zoneId => {
        if (zones[zoneId].total > 0) {
          zones[zoneId].efficiency = Math.round(zones[zoneId].efficiency / zones[zoneId].total)
        }
      })

      return zones
    },

    // 获取选中工位
    selectedLocation: (state) => {
      return state.locations.find(loc => loc.locationId === state.selectedLocationId)
    },

    // 格式化的更新时间
    formattedTime: (state) => {
      return state.lastUpdate.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }
  },

  actions: {
    // 初始化工厂数据
    async initializeFactory() {
      this.isLoading = true
      try {
        await this.loadFactoryData()
        this.updateSystemStatus()
        this.startAutoRefresh()
        
        // 监听数据服务的更新事件
        this.listenToDataUpdates()
        
        ElMessage.success('工厂数据加载完成')
      } catch (error) {
        console.error('初始化工厂失败:', error)
        ElMessage.error('工厂数据加载失败')
        this.errorCount++
      } finally {
        this.isLoading = false
      }
    },

    // 加载工厂数据
    async loadFactoryData() {
      try {
        this.locations = factoryDataService.locations.value
        this.departments = factoryDataService.departments.value
        this.lastUpdate = factoryDataService.lastUpdate.value
        this.connectionStatus = factoryDataService.connectionStatus.value
      } catch (error) {
        console.error('加载工厂数据失败:', error)
        throw error
      }
    },

    // 刷新数据
    async refreshData() {
      this.isLoading = true
      try {
        await factoryDataService.loadInitialData()
        await this.loadFactoryData()
        this.updateSystemStatus()
        ElMessage.success('数据已刷新')
      } catch (error) {
        console.error('刷新数据失败:', error)
        ElMessage.error('刷新失败')
        this.errorCount++
      } finally {
        this.isLoading = false
      }
    },

    // 更新系统状态
    updateSystemStatus() {
      const stats = this.stats
      
      if (stats.error > 0) {
        this.systemStatus = 'offline'
      } else if (stats.warning > 0) {
        this.systemStatus = 'warning'
      } else {
        this.systemStatus = 'online'
      }
    },

    // 开始自动刷新
    startAutoRefresh() {
      if (!this.autoRefresh) return
      
      setInterval(() => {
        if (!this.isLoading) {
          this.loadFactoryData()
          this.updateSystemStatus()
        }
      }, this.refreshInterval)
    },

    // 监听数据更新事件
    listenToDataUpdates() {
      window.addEventListener('factory-data-updated', (event) => {
        this.locations = event.detail.locations
        this.lastUpdate = event.detail.lastUpdate
        this.updateSystemStatus()
      })
    },

    // 搜索工位
    searchWorkstations(term) {
      this.searchTerm = term
      if (term) {
        this.searchResults = factoryDataService.searchLocations(term)
      } else {
        this.searchResults = []
      }
    },

    // 选择搜索结果
    selectSearchResult(result) {
      this.searchTerm = ''
      this.searchResults = []
      this.selectLocation(result.locationId)
    },

    // 选择工位
    async selectLocation(locationId) {
      this.selectedLocationId = locationId
      
      try {
        // 加载工位详细信息
        this.selectedLocationDetails = await factoryDataService.getWorkstationDetails(locationId)
      } catch (error) {
        console.error('加载工位详情失败:', error)
        ElMessage.error('加载工位详情失败')
      }
    },

    // 清除选择
    clearSelection() {
      this.selectedLocationId = null
      this.selectedLocationDetails = null
    },

    // 设置悬停工位
    setHoveredLocation(locationId) {
      this.hoveredLocationId = locationId
    },

    // 清除悬停
    clearHoveredLocation() {
      this.hoveredLocationId = null
    },

    // 按状态筛选
    filterByStatus(status) {
      this.statusFilters = [status]
      ElMessage.info(`已筛选 ${this.getStatusText(status)} 工位`)
    },

    // 重置筛选
    resetFilters() {
      this.searchTerm = ''
      this.searchResults = []
      this.statusFilters = ['operational', 'warning', 'error', 'idle']
      this.filterForm = {
        departmentId: null,
        locationType: null,
        onlyWithAssets: false
      }
      ElMessage.success('筛选已重置')
    },

    // 应用筛选
    applyFilters(filters) {
      Object.assign(this.filterForm, filters)
      ElMessage.success('筛选条件已应用')
    },

    // 缩放控制
    zoomIn() {
      if (this.zoomLevel < 2) {
        this.zoomLevel = Math.min(2, this.zoomLevel + 0.1)
      }
    },

    zoomOut() {
      if (this.zoomLevel > 0.5) {
        this.zoomLevel = Math.max(0.5, this.zoomLevel - 0.1)
      }
    },

    resetZoom() {
      this.zoomLevel = 1
    },

    // 视图模式切换
    setViewMode(mode) {
      this.viewMode = mode
    },

    // 全屏控制
    toggleFullScreen() {
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen().catch(err => {
          console.error(`全屏模式错误: ${err.message}`)
          ElMessage.error('无法进入全屏模式')
        })
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        }
      }
    },

    setFullScreen(isFullScreen) {
      this.isFullScreen = isFullScreen
    },

    // 设置控制
    updateSettings(settings) {
      Object.assign(this, settings)
    },

    // 工具函数
    getStatusText(status) {
      const statusMap = {
        operational: '正常',
        warning: '警告',
        error: '故障',
        idle: '空闲'
      }
      return statusMap[status] || '未知'
    },

    getStatusTagType(status) {
      const typeMap = {
        operational: 'success',
        warning: 'warning',
        error: 'danger',
        idle: 'info'
      }
      return typeMap[status] || 'info'
    },

    getPriorityText(priority) {
      const priorityMap = {
        1: '高',
        2: '中',
        3: '低'
      }
      return priorityMap[priority] || '未知'
    },

    getPriorityTagType(priority) {
      const typeMap = {
        1: 'danger',
        2: 'warning',
        3: 'success'
      }
      return typeMap[priority] || 'info'
    },

    // 导出数据
    exportData(format = 'excel') {
      const data = {
        locations: this.filteredLocations,
        stats: this.stats,
        timestamp: new Date().toISOString()
      }

      if (format === 'excel') {
        this.exportToExcel(data)
      } else if (format === 'json') {
        this.exportToJson(data)
      }
    },

    exportToExcel(data) {
      // 这里可以使用xlsx库导出Excel
      ElMessage.info('正在导出Excel文件...')
    },

    exportToJson(data) {
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `factory-data-${Date.now()}.json`
      link.click()
      URL.revokeObjectURL(url)
      ElMessage.success('JSON文件已下载')
    },

    // 清理资源
    destroy() {
      factoryDataService.destroy()
    }
  },

  // 持久化配置（可选）
  persist: {
    key: 'factory-store',
    storage: localStorage,
    paths: [
      'viewMode',
      'zoomLevel',
      'filterForm',
      'statusFilters',
      'autoRefresh',
      'showTooltips',
      'animationEnabled',
      'highlightCritical'
    ]
  }
})