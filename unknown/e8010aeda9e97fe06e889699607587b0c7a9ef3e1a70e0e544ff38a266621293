using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using ItAssetsSystem.Core.Interfaces.Services;

namespace ItAssetsSystem.Controllers.V1_1
{
    /// <summary>
    /// 用户控制器 V1.1 - 使用新的服务接口
    /// 与现有V1控制器并行部署，确保向后兼容
    /// </summary>
    [ApiController]
    [Route("api/v1.1/[controller]")]
    public class UserController : ControllerBase
    {
        private readonly IUserService _userService;
        private readonly ILogger<UserController> _logger;

        public UserController(IUserService userService, ILogger<UserController> logger)
        {
            _userService = userService;
            _logger = logger;
        }

        /// <summary>
        /// 用户登录 - 使用新的服务接口
        /// </summary>
        /// <param name="model">登录信息</param>
        /// <returns>登录结果</returns>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login([FromBody] Controllers.LoginModel model)
        {
            _logger.LogInformation("V1.1 用户登录请求: {Username}", model.Username);

            try
            {
                // 转换为接口中的LoginModel
                var serviceModel = new Core.Interfaces.Services.LoginModel
                {
                    Username = model.Username,
                    Password = model.Password
                };

                var result = await _userService.AuthenticateAsync(serviceModel);
                
                if (!result.Success)
                {
                    _logger.LogWarning("V1.1 用户登录失败: {Username}, 原因: {Message}", 
                        model.Username, result.Message);
                    return BadRequest(new { 
                        success = false, 
                        message = result.Message,
                        errorCode = result.ErrorCode
                    });
                }

                _logger.LogInformation("V1.1 用户登录成功: {Username}", model.Username);

                return Ok(new 
                { 
                    success = true,
                    data = new
                    {
                        token = result.Token,
                        user = new
                        {
                            id = result.User.Id,
                            username = result.User.Username,
                            name = result.User.Name,
                            email = result.User.Email,
                            mobile = result.User.Mobile,
                            position = result.User.Position,
                            departmentId = result.User.DepartmentId,
                            departmentName = result.User.DepartmentName,
                            roles = result.User.Roles,
                            isActive = result.User.IsActive,
                            lastLoginAt = result.User.LastLoginAt
                        }
                    },
                    message = result.Message,
                    version = "v1.1"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "V1.1 用户登录异常: {Username}", model.Username);
                return StatusCode(500, new { 
                    success = false, 
                    message = "登录服务异常",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 用户退出登录
        /// </summary>
        /// <returns>退出结果</returns>
        [HttpPost("logout")]
        [AllowAnonymous]
        public IActionResult Logout()
        {
            _logger.LogInformation("V1.1 用户退出登录");
            
            return Ok(new { 
                success = true, 
                message = "退出登录成功",
                version = "v1.1"
            });
        }

        /// <summary>
        /// 根据ID获取用户信息
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <returns>用户信息</returns>
        [HttpGet("{id}")]
        [Authorize]
        public async Task<IActionResult> GetUser(int id)
        {
            try
            {
                var user = await _userService.GetByIdAsync(id);
                if (user == null)
                {
                    return NotFound(new { 
                        success = false, 
                        message = "用户不存在",
                        version = "v1.1"
                    });
                }

                return Ok(new { 
                    success = true, 
                    data = user,
                    version = "v1.1"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "V1.1 获取用户信息异常: {UserId}", id);
                return StatusCode(500, new { 
                    success = false, 
                    message = "获取用户信息失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 根据用户名获取用户信息
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>用户信息</returns>
        [HttpGet("by-username/{username}")]
        [Authorize]
        public async Task<IActionResult> GetUserByUsername(string username)
        {
            try
            {
                var user = await _userService.GetByUsernameAsync(username);
                if (user == null)
                {
                    return NotFound(new { 
                        success = false, 
                        message = "用户不存在",
                        version = "v1.1"
                    });
                }

                return Ok(new { 
                    success = true, 
                    data = user,
                    version = "v1.1"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "V1.1 根据用户名获取用户信息异常: {Username}", username);
                return StatusCode(500, new { 
                    success = false, 
                    message = "获取用户信息失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 获取用户权限列表
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <returns>权限列表</returns>
        [HttpGet("{id}/permissions")]
        [Authorize]
        public async Task<IActionResult> GetUserPermissions(int id)
        {
            try
            {
                var permissions = await _userService.GetUserPermissionsAsync(id);
                return Ok(new { 
                    success = true, 
                    data = permissions,
                    version = "v1.1"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "V1.1 获取用户权限异常: {UserId}", id);
                return StatusCode(500, new { 
                    success = false, 
                    message = "获取用户权限失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 验证Token有效性
        /// </summary>
        /// <param name="token">JWT Token</param>
        /// <returns>验证结果</returns>
        [HttpPost("validate-token")]
        [AllowAnonymous]
        public async Task<IActionResult> ValidateToken([FromBody] ValidateTokenRequest request)
        {
            try
            {
                var isValid = await _userService.ValidateTokenAsync(request.Token);
                return Ok(new { 
                    success = true, 
                    data = new { isValid },
                    version = "v1.1"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "V1.1 Token验证异常");
                return StatusCode(500, new { 
                    success = false, 
                    message = "Token验证失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 健康检查端点
        /// </summary>
        /// <returns>服务状态</returns>
        [HttpGet("health")]
        [AllowAnonymous]
        public IActionResult Health()
        {
            return Ok(new { 
                status = "healthy", 
                version = "v1.1",
                service = "UserService",
                timestamp = System.DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// Token验证请求模型
    /// </summary>
    public class ValidateTokenRequest
    {
        public string Token { get; set; }
    }
}
