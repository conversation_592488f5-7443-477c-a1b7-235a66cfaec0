{
  "success": true,
  "data": {
    "kpiData": {
      "totalAssets": 242,
      "totalValue": 0.00,
      "onlineRate": 100,
      "faultCount": 0,
      "totalTrend": 0,
      "valueTrend": 0,
      "onlineRateTrend": 0,
      "faultTrend": 0
    },
    "typeStatistics": [
      {
        "assetTypeId": 7,
        "assetTypeName": "工控机",
        "assetCount": 82,
        "normalCount": 82,
        "faultCount": 0,
        "maintenanceCount": 0,
        "normalRate": 100,
        "faultRate": 0,
        "percentage": 33.88
      },
      {
        "assetTypeId": 19,
        "assetTypeName": "PDA",
        "assetCount": 71,
        "normalCount": 71,
        "faultCount": 0,
        "maintenanceCount": 0,
        "normalRate": 100,
        "faultRate": 0,
        "percentage": 29.34
      },
      {
        "assetTypeId": 4,
        "assetTypeName": "扫描设备",
        "assetCount": 63,
        "normalCount": 63,
        "faultCount": 0,
        "maintenanceCount": 0,
        "normalRate": 100,
        "faultRate": 0,
        "percentage": 26.03
      },
      {
        "assetTypeId": 16,
        "assetTypeName": "蓝牙打印机",
        "assetCount": 23,
        "normalCount": 23,
        "faultCount": 0,
        "maintenanceCount": 0,
        "normalRate": 100,
        "faultRate": 0,
        "percentage": 9.50
      },
      {
        "assetTypeId": 1,
        "assetTypeName": "电脑设备",
        "assetCount": 3,
        "normalCount": 3,
        "faultCount": 0,
        "maintenanceCount": 0,
        "normalRate": 100,
        "faultRate": 0,
        "percentage": 1.24
      }
    ],
    "regionStatistics": [
      {
        "regionId": 289,
        "regionName": "现场使用",
        "assetCount": 94,
        "normalCount": 94,
        "faultCount": 0,
        "maintenanceCount": 0,
        "normalRate": 100,
        "faultRate": 0,
        "percentage": 39.17
      },
      {
        "regionId": 40,
        "regionName": "A区域",
        "assetCount": 36,
        "normalCount": 36,
        "faultCount": 0,
        "maintenanceCount": 0,
        "normalRate": 100,
        "faultRate": 0,
        "percentage": 15.00
      }
    ],
    "departmentStatistics": [
      {
        "departmentId": 4,
        "departmentName": "营销部",
        "assetCount": 94,
        "normalCount": 94,
        "faultCount": 0,
        "maintenanceCount": 0,
        "normalRate": 100,
        "faultRate": 0,
        "percentage": 100
      }
    ],
    "timeSeriesData": {
      "timeLabels": [
        "00:00",
        "04:00",
        "08:00",
        "12:00",
        "16:00",
        "20:00"
      ],
      "onlineData": [
        193,
        193,
        242,
        242,
        242,
        193
      ],
      "offlineData": [
        0,
        0,
        0,
        0,
        0,
        0
      ],
      "maintenanceData": [
        0,
        0,
        0,
        0,
        0,
        0
      ]
    },
    "valueDistribution": {
      "valueRanges": [
        "0-1万",
        "1-5万",
        "5-10万",
        "10-50万",
        "50万以上"
      ],
      "assetCounts": [
        242,
        0,
        0,
        0,
        0
      ],
      "totalValues": [
        0.00,
        0.00,
        0.00,
        0.00,
        0.00
      ]
    },
    "matrixData": {
      "departments": [
        "IT基础设施",
        "IT安全",
        "IT应用开发",
        "IT运维",
        "IT部门"
      ],
      "assetTypes": [
        "工控机",
        "PDA",
        "扫描设备",
        "蓝牙打印机",
        "电脑设备"
      ],
      "matrixData": [
        [
          0,
          0,
          0,
          0,
          0
        ],
        [
          0,
          0,
          0,
          0,
          0
        ],
        [
          0,
          0,
          0,
          0,
          0
        ],
        [
          0,
          0,
          0,
          0,
          0
        ],
        [
          0,
          0,
          0,
          0,
          0
        ]
      ]
    },
    "statusDistribution": {
      "statusLabels": [
        "在用",
        "闲置",
        "维护中",
        "故障",
        "报废"
      ],
      "statusCounts": [
        242,
        0,
        0,
        0,
        0
      ],
      "statusPercentages": [
        100,
        0,
        0,
        0,
        0
      ]
    },
    "filterOptions": {
      "assetTypes": [
        {
          "assetTypeId": 19,
          "assetTypeName": "PDA",
          "description": "掌上数据终端",
          "isActive": true,
          "assetCount": 71
        },
        {
          "assetTypeId": 11,
          "assetTypeName": "交换机",
          "description": "网络交换机",
          "isActive": true,
          "assetCount": 0
        },
        {
          "assetTypeId": 24,
          "assetTypeName": "其他",
          "description": "默认资产类型-其他",
          "isActive": true,
          "assetCount": 0
        },
        {
          "assetTypeId": 6,
          "assetTypeName": "存储设备",
          "description": "数据存储设备",
          "isActive": true,
          "assetCount": 0
        },
        {
          "assetTypeId": 7,
          "assetTypeName": "工控机",
          "description": "工业控制计算机",
          "isActive": true,
          "assetCount": 82
        },
        {
          "assetTypeId": 3,
          "assetTypeName": "打印设备",
          "description": "打印类设备",
          "isActive": true,
          "assetCount": 0
        },
        {
          "assetTypeId": 4,
          "assetTypeName": "扫描设备",
          "description": "扫描类设备",
          "isActive": true,
          "assetCount": 63
        },
        {
          "assetTypeId": 18,
          "assetTypeName": "扫码枪",
          "description": "扫码枪",
          "isActive": true,
          "assetCount": 0
        },
        {
          "assetTypeId": 10,
          "assetTypeName": "服务器",
          "description": "服务器设备",
          "isActive": true,
          "assetCount": 0
        },
        {
          "assetTypeId": 17,
          "assetTypeName": "条码扫描枪",
          "description": "条码扫描枪",
          "isActive": true,
          "assetCount": 0
        },
        {
          "assetTypeId": 15,
          "assetTypeName": "标签打印机",
          "description": "标签打印机",
          "isActive": true,
          "assetCount": 0
        },
        {
          "assetTypeId": 31,
          "assetTypeName": "测试",
          "description": "",
          "isActive": true,
          "assetCount": 0
        },
        {
          "assetTypeId": 14,
          "assetTypeName": "激光打印机",
          "description": "激光打印机",
          "isActive": true,
          "assetCount": 0
        },
        {
          "assetTypeId": 27,
          "assetTypeName": "电池",
          "description": "扫码枪电池",
          "isActive": true,
          "assetCount": 0
        },
        {
          "assetTypeId": 1,
          "assetTypeName": "电脑设备",
          "description": "计算机类设备",
          "isActive": true,
          "assetCount": 3
        },
        {
          "assetTypeId": 5,
          "assetTypeName": "移动设备",
          "description": "移动终端设备",
          "isActive": true,
          "assetCount": 0
        },
        {
          "assetTypeId": 9,
          "assetTypeName": "笔记本电脑",
          "description": "笔记本电脑",
          "isActive": true,
          "assetCount": 0
        },
        {
          "assetTypeId": 2,
          "assetTypeName": "网络设备",
          "description": "网络通信设备",
          "isActive": true,
          "assetCount": 0
        },
        {
          "assetTypeId": 16,
          "assetTypeName": "蓝牙打印机",
          "description": "蓝牙打印机",
          "isActive": true,
          "assetCount": 23
        },
        {
          "assetTypeId": 12,
          "assetTypeName": "路由器",
          "description": "网络路由器",
          "isActive": true,
          "assetCount": 0
        },
        {
          "assetTypeId": 13,
          "assetTypeName": "防火墙",
          "description": "网络防火墙",
          "isActive": true,
          "assetCount": 0
        }
      ],
      "regions": [
        {
          "regionId": 39,
          "regionName": "A工位",
          "description": "",
          "isActive": true,
          "assetCount": 0
        },
        {
          "regionId": 40,
          "regionName": "B工位制",
          "description": "",
          "isActive": true,
          "assetCount": 0
        }
      ],
      "statuses": [
        {
          "value": 1,
          "label": "在用",
          "color": "#10b981"
        },
        {
          "value": 0,
          "label": "闲置",
          "color": "#f59e0b"
        },
        {
          "value": 2,
          "label": "维护中",
          "color": "#3b82f6"
        },
        {
          "value": 4,
          "label": "故障",
          "color": "#ef4444"
        },
        {
          "value": 3,
          "label": "报废",
          "color": "#6b7280"
        }
      ]
    },
    "updateTime": "2025-06-08T19:25:42.3217816+08:00"
  },
  "message": "获取资产分析工作台数据成功"
}  1.这里返回的很多没有数据  2.设备使用时段分析图表先注释掉 3.在线设备 数量是从 资产表中status = 1得出来的 86% 空闲率
14% 维修中 也是那个字段来的 
5% 然后计算 平均利用率 78%  空闲率的计算逻辑是什么 4.资产价值区间分布应该取资产表中的价格或者  ，取备品备件中的价格这个应该在当时写入资产表，在他俩建立关联时 ，所以现阶段这里可以不显示  但是计算逻辑要正确 5.ID	
资产编号 资产名称 资产类型	部门	位置	状态	价值(万元)	创建时间	负责人   也就是前端资产列表显示的位置时显示的应该是 assets表中的位置字段 。但是我们资产位置时取的确是 这个位置在位置表中path的第四个，但是由于第四个没有关联部门他们的部门是继承自path中的第三个对应的部门，这个核心的点在于位置表中并不是每个位置都制定了部门。而是如果位置指定了部门就取这个位置的部门。没有指定部门就从父位置继承过来直到哪个父位置有部门。6.然后是部门负责人的获得和筛选 资产关联到位置，位置关联到的部门绑定了负责人。这就是这套逻辑 7.所以部门资产矩阵展示的部门应该是部门表中 和位置关联了的部门，如果这个部门没有关联位置，就不展示出来 8
通过这套复杂的关联你是能够得出热力图和矩阵图的  请认真理解这个逻辑梳理出来 思考两遍之后想下前端怎样把后端返回的数据展示出来。 后端都能取到有效的数据