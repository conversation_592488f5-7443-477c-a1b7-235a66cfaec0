using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ItAssetsSystem.Core.Interfaces.Services
{
    /// <summary>
    /// 用户服务接口 - 基础解耦第一步
    /// 提供用户相关的核心业务操作
    /// </summary>
    public interface IUserService
    {
        /// <summary>
        /// 根据ID获取用户信息
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <returns>用户信息</returns>
        Task<UserDto> GetByIdAsync(int id);

        /// <summary>
        /// 根据用户名获取用户信息
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>用户信息</returns>
        Task<UserDto> GetByUsernameAsync(string username);

        /// <summary>
        /// 用户认证
        /// </summary>
        /// <param name="model">登录信息</param>
        /// <returns>认证结果</returns>
        Task<AuthResultDto> AuthenticateAsync(LoginModel model);

        /// <summary>
        /// 验证Token有效性
        /// </summary>
        /// <param name="token">JWT Token</param>
        /// <returns>是否有效</returns>
        Task<bool> ValidateTokenAsync(string token);

        /// <summary>
        /// 获取用户权限列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>权限列表</returns>
        Task<List<string>> GetUserPermissionsAsync(int userId);
    }

    /// <summary>
    /// 认证结果DTO
    /// </summary>
    public class AuthResultDto
    {
        public bool Success { get; set; }
        public string Token { get; set; }
        public UserDto User { get; set; }
        public string Message { get; set; }
        public string ErrorCode { get; set; }
    }

    /// <summary>
    /// 用户DTO
    /// </summary>
    public class UserDto
    {
        public int Id { get; set; }
        public string Username { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string Mobile { get; set; }
        public string Position { get; set; }
        public int? DepartmentId { get; set; }
        public string DepartmentName { get; set; }
        public List<string> Roles { get; set; } = new List<string>();
        public bool IsActive { get; set; }
        public DateTime? LastLoginAt { get; set; }
    }

    /// <summary>
    /// 登录模型（复用现有的）
    /// </summary>
    public class LoginModel
    {
        public string Username { get; set; }
        public string Password { get; set; }
    }
}
