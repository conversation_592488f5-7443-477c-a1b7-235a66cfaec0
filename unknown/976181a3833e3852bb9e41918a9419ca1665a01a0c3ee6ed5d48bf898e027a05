using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Domain.Entities
{
    /// <summary>
    /// 备件状态类型实体
    /// </summary>
    [Table("spare_part_status_types")]
    public class SparePartStatusType
    {
        /// <summary>
        /// 状态类型ID
        /// </summary>
        [Key]
        [Column("id")]
        public int Id { get; set; }
        
        /// <summary>
        /// 状态代码
        /// </summary>
        [Required]
        [Column("code")]
        [StringLength(20)]
        public string Code { get; set; }
        
        /// <summary>
        /// 状态名称
        /// </summary>
        [Required]
        [Column("name")]
        [StringLength(50)]
        public string Name { get; set; }
        
        /// <summary>
        /// 状态分类: Available, Unavailable, InTransit, Reserved
        /// </summary>
        [Required]
        [Column("category")]
        [StringLength(20)]
        public string Category { get; set; }
        
        /// <summary>
        /// 显示颜色
        /// </summary>
        [Required]
        [Column("color")]
        [StringLength(10)]
        public string Color { get; set; }
        
        /// <summary>
        /// 图标
        /// </summary>
        [Column("icon")]
        [StringLength(50)]
        public string? Icon { get; set; }
        
        /// <summary>
        /// 状态描述
        /// </summary>
        [Column("description")]
        [StringLength(200)]
        public string? Description { get; set; }
        
        /// <summary>
        /// 是否启用
        /// </summary>
        [Column("is_active")]
        public bool IsActive { get; set; } = true;
        
        /// <summary>
        /// 排序
        /// </summary>
        [Column("sort_order")]
        public int SortOrder { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        /// <summary>
        /// 更新时间
        /// </summary>
        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
        
        /// <summary>
        /// 库存明细导航属性
        /// </summary>
        public virtual ICollection<SparePartInventory> Inventories { get; set; } = new List<SparePartInventory>();
        

    }
}
