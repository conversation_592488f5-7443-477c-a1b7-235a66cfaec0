-- 为备件表添加物料编号字段
-- 执行时间: 2025-06-02

USE itassets;

-- 添加物料编号字段到spare_parts表
ALTER TABLE `spare_parts` 
ADD COLUMN `material_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物料编号' 
AFTER `code`;

-- 为物料编号字段添加索引
ALTER TABLE `spare_parts` 
ADD INDEX `idx_spare_parts_material_number`(`material_number`) USING BTREE;

-- 更新现有数据，为已有备件生成物料编号（可选）
-- UPDATE `spare_parts` SET `material_number` = CONCAT('MAT-', LPAD(id, 6, '0')) WHERE `material_number` IS NULL;

-- 验证字段添加成功
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'itassets' 
  AND TABLE_NAME = 'spare_parts' 
  AND COLUMN_NAME = 'material_number';
