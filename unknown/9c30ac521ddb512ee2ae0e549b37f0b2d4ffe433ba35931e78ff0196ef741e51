// File: Application/Features/SpareParts/Dtos/SparePartInventoryDtos.cs
// Description: 备品备件库存状态管理相关DTO

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using ItAssetsSystem.Application.Common.Dtos;

namespace ItAssetsSystem.Application.Features.SpareParts.Dtos
{
    /// <summary>
    /// 备件库存汇总DTO
    /// </summary>
    public class SparePartStockSummaryDto
    {
        /// <summary>
        /// 备件ID
        /// </summary>
        public long PartId { get; set; }
        
        /// <summary>
        /// 备件名称
        /// </summary>
        public string PartName { get; set; }
        
        /// <summary>
        /// 备件编码
        /// </summary>
        public string PartCode { get; set; }
        
        /// <summary>
        /// 总数量
        /// </summary>
        public int TotalQuantity { get; set; }
        
        /// <summary>
        /// 可用库存数量
        /// </summary>
        public int AvailableQuantity { get; set; }
        
        /// <summary>
        /// 不可用库存数量
        /// </summary>
        public int UnavailableQuantity { get; set; }
        
        /// <summary>
        /// 在途库存数量
        /// </summary>
        public int InTransitQuantity { get; set; }
        
        /// <summary>
        /// 保留库存数量
        /// </summary>
        public int ReservedQuantity { get; set; }
        
        /// <summary>
        /// 状态明细
        /// </summary>
        public List<StatusBreakdownDto> StatusBreakdown { get; set; } = new List<StatusBreakdownDto>();
    }
    
    /// <summary>
    /// 状态明细DTO
    /// </summary>
    public class StatusBreakdownDto
    {
        /// <summary>
        /// 状态代码
        /// </summary>
        public string StatusCode { get; set; }
        
        /// <summary>
        /// 状态名称
        /// </summary>
        public string StatusName { get; set; }
        
        /// <summary>
        /// 状态分类
        /// </summary>
        public string Category { get; set; }
        
        /// <summary>
        /// 数量
        /// </summary>
        public int Quantity { get; set; }
        
        /// <summary>
        /// 颜色
        /// </summary>
        public string Color { get; set; }
    }
    
    /// <summary>
    /// 备件库存明细DTO
    /// </summary>
    public class SparePartInventoryDto
    {
        /// <summary>
        /// 库存明细ID
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 备件ID
        /// </summary>
        public long PartId { get; set; }
        
        /// <summary>
        /// 库位ID
        /// </summary>
        public long LocationId { get; set; }
        
        /// <summary>
        /// 库位名称
        /// </summary>
        public string LocationName { get; set; }
        
        /// <summary>
        /// 状态ID
        /// </summary>
        public int StatusId { get; set; }
        
        /// <summary>
        /// 状态名称
        /// </summary>
        public string StatusName { get; set; }
        
        /// <summary>
        /// 状态分类
        /// </summary>
        public string StatusCategory { get; set; }
        
        /// <summary>
        /// 状态颜色
        /// </summary>
        public string StatusColor { get; set; }
        
        /// <summary>
        /// 数量
        /// </summary>
        public int Quantity { get; set; }
        
        /// <summary>
        /// 批次号
        /// </summary>
        public string BatchNumber { get; set; }
        
        /// <summary>
        /// 序列号列表
        /// </summary>
        public List<string> SerialNumbers { get; set; } = new List<string>();
        
        /// <summary>
        /// 采购日期
        /// </summary>
        public DateTime? PurchaseDate { get; set; }
        
        /// <summary>
        /// 保修到期日期
        /// </summary>
        public DateTime? WarrantyExpireDate { get; set; }
        
        /// <summary>
        /// 单位成本
        /// </summary>
        public decimal? UnitCost { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public string Notes { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }
    }
    
    /// <summary>
    /// 状态类型DTO
    /// </summary>
    public class SparePartStatusTypeDto
    {
        /// <summary>
        /// 状态ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// 状态代码
        /// </summary>
        public string Code { get; set; }
        
        /// <summary>
        /// 状态名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 状态分类
        /// </summary>
        public string Category { get; set; }
        
        /// <summary>
        /// 显示颜色
        /// </summary>
        public string Color { get; set; }
        
        /// <summary>
        /// 图标
        /// </summary>
        public string Icon { get; set; }
        
        /// <summary>
        /// 状态描述
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; }
        
        /// <summary>
        /// 排序
        /// </summary>
        public int SortOrder { get; set; }
    }
    
    /// <summary>
    /// 库存明细查询参数
    /// </summary>
    public class SparePartInventoryQuery : PaginationQuery
    {
        /// <summary>
        /// 备件ID
        /// </summary>
        public long? PartId { get; set; }
        
        /// <summary>
        /// 库位ID
        /// </summary>
        public long? LocationId { get; set; }
        
        /// <summary>
        /// 状态ID
        /// </summary>
        public int? StatusId { get; set; }
        
        /// <summary>
        /// 状态分类
        /// </summary>
        public string StatusCategory { get; set; }
        
        /// <summary>
        /// 批次号
        /// </summary>
        public string BatchNumber { get; set; }
    }
    
    /// <summary>
    /// 批量状态更新请求
    /// </summary>
    public class BatchStatusUpdateRequest
    {
        /// <summary>
        /// 状态更新项列表
        /// </summary>
        [Required]
        public List<StatusUpdateItem> Updates { get; set; } = new List<StatusUpdateItem>();
    }
    
    /// <summary>
    /// 状态更新项
    /// </summary>
    public class StatusUpdateItem
    {
        /// <summary>
        /// 库存明细ID
        /// </summary>
        [Required]
        public long InventoryId { get; set; }
        
        /// <summary>
        /// 新状态ID
        /// </summary>
        [Required]
        public int NewStatusId { get; set; }
        
        /// <summary>
        /// 变更数量（部分变更时使用）
        /// </summary>
        public int? Quantity { get; set; }
        
        /// <summary>
        /// 变更原因
        /// </summary>
        [Required]
        [StringLength(500)]
        public string Reason { get; set; }
    }
    
    /// <summary>
    /// 批量状态更新结果
    /// </summary>
    public class BatchStatusUpdateResultDto
    {
        /// <summary>
        /// 成功更新的数量
        /// </summary>
        public int SuccessCount { get; set; }
        
        /// <summary>
        /// 失败的数量
        /// </summary>
        public int FailureCount { get; set; }
        
        /// <summary>
        /// 错误信息列表
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();
    }
    
    /// <summary>
    /// 状态变更历史DTO
    /// </summary>
    public class SparePartStatusHistoryDto
    {
        /// <summary>
        /// 历史记录ID
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 库存明细ID
        /// </summary>
        public long InventoryId { get; set; }
        
        /// <summary>
        /// 变更前状态名称
        /// </summary>
        public string FromStatusName { get; set; }
        
        /// <summary>
        /// 变更后状态名称
        /// </summary>
        public string ToStatusName { get; set; }
        
        /// <summary>
        /// 变更数量
        /// </summary>
        public int Quantity { get; set; }
        
        /// <summary>
        /// 变更原因
        /// </summary>
        public string Reason { get; set; }
        
        /// <summary>
        /// 操作人姓名
        /// </summary>
        public string OperatorName { get; set; }
        
        /// <summary>
        /// 变更时间
        /// </summary>
        public DateTime ChangedAt { get; set; }
        
        /// <summary>
        /// 关联返厂单ID
        /// </summary>
        public int? RepairOrderId { get; set; }
    }
    
    /// <summary>
    /// 创建返厂维修单请求
    /// </summary>
    public class CreateRepairOrderRequest
    {
        /// <summary>
        /// 标题
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Title { get; set; }
        
        /// <summary>
        /// 优先级
        /// </summary>
        [Range(1, 5)]
        public int Priority { get; set; } = 2;
        
        /// <summary>
        /// 供应商ID
        /// </summary>
        [Required]
        public int SupplierId { get; set; }
        
        /// <summary>
        /// 关联故障ID
        /// </summary>
        public int? FaultId { get; set; }
        
        /// <summary>
        /// 选中的库存明细ID列表
        /// </summary>
        [Required]
        public List<long> InventoryIds { get; set; } = new List<long>();
        
        /// <summary>
        /// 故障描述
        /// </summary>
        [StringLength(1000)]
        public string FaultDescription { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string Notes { get; set; }
    }
    
    /// <summary>
    /// 创建返厂维修单结果
    /// </summary>
    public class CreateRepairOrderResultDto
    {
        /// <summary>
        /// 返厂单ID
        /// </summary>
        public int RepairOrderId { get; set; }
        
        /// <summary>
        /// 返厂单号
        /// </summary>
        public string OrderNumber { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
    }
}
