using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Application.Features.Suppliers.Services;
using ItAssetsSystem.Application.Features.Suppliers.Dtos;

namespace ItAssetsSystem.Api.V2.Controllers
{
    /// <summary>
    /// 供应商管理控制器
    /// </summary>
    [ApiController]
    [Route("api/v2/suppliers")]
    // [Authorize]  // 临时注释掉授权要求，方便测试
    public class SuppliersController : ControllerBase
    {
        private readonly ISupplierService _supplierService;
        private readonly ILogger<SuppliersController> _logger;

        public SuppliersController(
            ISupplierService supplierService,
            ILogger<SuppliersController> logger)
        {
            _supplierService = supplierService;
            _logger = logger;
        }

        /// <summary>
        /// 获取供应商列表
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>供应商列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetSuppliers([FromQuery] SupplierQuery query)
        {
            try
            {
                var result = await _supplierService.GetSuppliersAsync(query);
                return Ok(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取供应商列表失败");
                return Ok(new { success = false, message = "获取供应商列表失败" });
            }
        }

        /// <summary>
        /// 获取供应商详情
        /// </summary>
        /// <param name="id">供应商ID</param>
        /// <returns>供应商详情</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetSupplier(int id)
        {
            try
            {
                var result = await _supplierService.GetSupplierByIdAsync(id);
                if (result == null)
                {
                    return Ok(new { success = false, message = $"供应商ID {id} 不存在" });
                }
                return Ok(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取供应商详情失败，ID: {SupplierId}", id);
                return Ok(new { success = false, message = "获取供应商详情失败" });
            }
        }

        /// <summary>
        /// 创建供应商
        /// </summary>
        /// <param name="request">创建请求</param>
        /// <returns>创建的供应商</returns>
        [HttpPost]
        public async Task<IActionResult> CreateSupplier([FromBody] CreateSupplierRequest request)
        {
            try
            {
                var result = await _supplierService.CreateSupplierAsync(request);
                return Ok(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建供应商失败");
                return Ok(new { success = false, message = "创建供应商失败: " + ex.Message });
            }
        }

        /// <summary>
        /// 更新供应商
        /// </summary>
        /// <param name="id">供应商ID</param>
        /// <param name="request">更新请求</param>
        /// <returns>更新后的供应商</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateSupplier(int id, [FromBody] UpdateSupplierRequest request)
        {
            try
            {
                var result = await _supplierService.UpdateSupplierAsync(id, request);
                if (result == null)
                {
                    return Ok(new { success = false, message = $"供应商ID {id} 不存在" });
                }
                return Ok(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新供应商失败，ID: {SupplierId}", id);
                return Ok(new { success = false, message = "更新供应商失败: " + ex.Message });
            }
        }

        /// <summary>
        /// 删除供应商
        /// </summary>
        /// <param name="id">供应商ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteSupplier(int id)
        {
            try
            {
                var result = await _supplierService.DeleteSupplierAsync(id);
                if (!result)
                {
                    return Ok(new { success = false, message = $"供应商ID {id} 不存在" });
                }
                return Ok(new { success = true, message = "删除成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除供应商失败，ID: {SupplierId}", id);
                return Ok(new { success = false, message = "删除供应商失败: " + ex.Message });
            }
        }

        /// <summary>
        /// 获取维修供应商列表
        /// </summary>
        /// <returns>维修供应商列表</returns>
        [HttpGet("maintenance")]
        public async Task<IActionResult> GetMaintenanceSuppliers()
        {
            try
            {
                var result = await _supplierService.GetMaintenanceSuppliersAsync();
                return Ok(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取维修供应商列表失败");
                return Ok(new { success = false, message = "获取维修供应商列表失败" });
            }
        }

        /// <summary>
        /// 获取采购供应商列表
        /// </summary>
        /// <returns>采购供应商列表</returns>
        [HttpGet("procurement")]
        public async Task<IActionResult> GetProcurementSuppliers()
        {
            try
            {
                var result = await _supplierService.GetProcurementSuppliersAsync();
                return Ok(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取采购供应商列表失败");
                return Ok(new { success = false, message = "获取采购供应商列表失败" });
            }
        }
    }
}
