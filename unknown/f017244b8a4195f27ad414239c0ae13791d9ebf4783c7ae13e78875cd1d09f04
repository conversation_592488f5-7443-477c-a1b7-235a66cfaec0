// File: Application/Features/Statistics/Queries/DynamicStatisticsQuery.cs
// Description: 动态统计查询命令，使用MediatR模式实现CQRS架构

using MediatR;
using ItAssetsSystem.Application.Features.Statistics.Dtos;
using ItAssetsSystem.Application.Models;

namespace ItAssetsSystem.Application.Features.Statistics.Queries;

/// <summary>
/// 动态统计查询命令
/// </summary>
public class DynamicStatisticsQuery : IRequest<Result<DynamicStatisticsResultDto>>
{
    public DynamicStatisticsQueryDto QueryRequest { get; set; } = null!;

    public DynamicStatisticsQuery(DynamicStatisticsQueryDto queryRequest)
    {
        QueryRequest = queryRequest;
    }
}