namespace ItAssetsSystem.Application.Features.AssetStatistics.Dtos
{
    /// <summary>
    /// 资产类型DTO
    /// </summary>
    public class AssetTypeDto
    {
        /// <summary>
        /// 资产类型ID
        /// </summary>
        public int AssetTypeId { get; set; }

        /// <summary>
        /// 资产类型名称
        /// </summary>
        public string AssetTypeName { get; set; }

        /// <summary>
        /// 资产类型描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 资产数量
        /// </summary>
        public int AssetCount { get; set; }
    }
}