using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ItAssetsSystem.Models.Entities;

namespace ItAssetsSystem.Domain.Entities
{
    /// <summary>
    /// 备件状态变更历史实体
    /// </summary>
    [Table("spare_part_status_histories")]
    public class SparePartStatusHistory
    {
        /// <summary>
        /// 历史记录ID
        /// </summary>
        [Key]
        [Column("id")]
        public long Id { get; set; }
        
        /// <summary>
        /// 库存明细ID
        /// </summary>
        [Column("inventory_id")]
        public long InventoryId { get; set; }
        
        /// <summary>
        /// 库存明细导航属性
        /// </summary>
        [ForeignKey("InventoryId")]
        public virtual SparePartInventory Inventory { get; set; }
        
        /// <summary>
        /// 变更前状态ID
        /// </summary>
        [Column("from_status_id")]
        public int? FromStatusId { get; set; }
        
        /// <summary>
        /// 变更前状态导航属性
        /// </summary>
        public virtual SparePartStatusType? FromStatus { get; set; }

        /// <summary>
        /// 变更后状态ID
        /// </summary>
        [Column("to_status_id")]
        public int ToStatusId { get; set; }

        /// <summary>
        /// 变更后状态导航属性
        /// </summary>
        public virtual SparePartStatusType ToStatus { get; set; }
        
        /// <summary>
        /// 变更数量
        /// </summary>
        [Column("quantity")]
        public int Quantity { get; set; }
        
        /// <summary>
        /// 变更原因
        /// </summary>
        [Required]
        [Column("reason")]
        [StringLength(200)]
        public string Reason { get; set; }
        
        /// <summary>
        /// 操作人ID
        /// </summary>
        [Column("operator_id")]
        public int OperatorId { get; set; }
        
        /// <summary>
        /// 操作人导航属性
        /// </summary>
        [ForeignKey("OperatorId")]
        public virtual User Operator { get; set; }
        
        /// <summary>
        /// 关联交易记录ID
        /// </summary>
        [Column("transaction_id")]
        public long? TransactionId { get; set; }

        /// <summary>
        /// 关联交易记录导航属性
        /// </summary>
        [ForeignKey("TransactionId")]
        public virtual SparePartTransaction Transaction { get; set; }

        /// <summary>
        /// 关联返厂单ID
        /// </summary>
        [Column("repair_order_id")]
        public int? RepairOrderId { get; set; }
        
        /// <summary>
        /// 关联返厂单导航属性
        /// </summary>
        [ForeignKey("RepairOrderId")]
        public virtual RepairOrder RepairOrder { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }
}
