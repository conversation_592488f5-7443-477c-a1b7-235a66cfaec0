// File: Application/Features/Tasks/Dtos/TaskClaimDto.cs
// Description: 任务领取相关DTO

using System;
using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Application.Features.Tasks.Dtos
{
    /// <summary>
    /// 任务领取记录DTO
    /// </summary>
    public class TaskClaimDto
    {
        /// <summary>
        /// 领取记录ID
        /// </summary>
        public long ClaimId { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        public long TaskId { get; set; }

        /// <summary>
        /// 领取用户ID
        /// </summary>
        public int ClaimedBy { get; set; }

        /// <summary>
        /// 班次ID
        /// </summary>
        public long ShiftId { get; set; }

        /// <summary>
        /// 领取时间
        /// </summary>
        public DateTime ClaimedAt { get; set; }

        /// <summary>
        /// 领取日期
        /// </summary>
        public DateTime ClaimDate { get; set; }

        /// <summary>
        /// 领取状态
        /// </summary>
        public string ClaimStatus { get; set; } = string.Empty;

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartedAt { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        public DateTime? CompletedAt { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? TaskName { get; set; }

        /// <summary>
        /// 领取用户名
        /// </summary>
        public string? ClaimedByUserName { get; set; }

        /// <summary>
        /// 班次名称
        /// </summary>
        public string? ShiftName { get; set; }
    }

    /// <summary>
    /// 创建任务领取DTO
    /// </summary>
    public class CreateTaskClaimDto
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        [Required(ErrorMessage = "任务ID不能为空")]
        public long TaskId { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string? Notes { get; set; }
    }

    /// <summary>
    /// 更新任务领取状态DTO
    /// </summary>
    public class UpdateTaskClaimStatusDto
    {
        /// <summary>
        /// 领取状态 (Claimed-已领取, Started-已开始, Completed-已完成, Cancelled-已取消)
        /// </summary>
        [Required(ErrorMessage = "领取状态不能为空")]
        public string ClaimStatus { get; set; } = string.Empty;

        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string? Notes { get; set; }
    }

    /// <summary>
    /// 任务领取统计DTO
    /// </summary>
    public class TaskClaimStatisticsDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 班次ID
        /// </summary>
        public long ShiftId { get; set; }

        /// <summary>
        /// 班次名称
        /// </summary>
        public string ShiftName { get; set; } = string.Empty;

        /// <summary>
        /// 统计日期
        /// </summary>
        public DateTime StatisticsDate { get; set; }

        /// <summary>
        /// 已领取任务数
        /// </summary>
        public int ClaimedTasksCount { get; set; }

        /// <summary>
        /// 已开始任务数
        /// </summary>
        public int StartedTasksCount { get; set; }

        /// <summary>
        /// 已完成任务数
        /// </summary>
        public int CompletedTasksCount { get; set; }

        /// <summary>
        /// 已取消任务数
        /// </summary>
        public int CancelledTasksCount { get; set; }

        /// <summary>
        /// 未领取任务数
        /// </summary>
        public int UnclaimedTasksCount { get; set; }

        /// <summary>
        /// 完成率
        /// </summary>
        public decimal CompletionRate { get; set; }
    }

    /// <summary>
    /// 班次任务统计查询DTO
    /// </summary>
    public class ShiftTaskStatisticsQueryDto
    {
        /// <summary>
        /// 统计日期
        /// </summary>
        public DateTime? StatisticsDate { get; set; }

        /// <summary>
        /// 班次ID
        /// </summary>
        public long? ShiftId { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public int? UserId { get; set; }
    }
}
