using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ItAssetsSystem.Core.Interfaces.Services
{
    /// <summary>
    /// 资产服务接口 - 基础解耦
    /// 提供资产相关的核心业务操作
    /// </summary>
    public interface IAssetService
    {
        /// <summary>
        /// 根据ID获取资产信息
        /// </summary>
        /// <param name="id">资产ID</param>
        /// <returns>资产信息</returns>
        Task<AssetDto> GetByIdAsync(int id);

        /// <summary>
        /// 分页获取资产列表
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResult<AssetDto>> GetPagedAsync(AssetQueryDto query);

        /// <summary>
        /// 创建资产
        /// </summary>
        /// <param name="dto">创建资产DTO</param>
        /// <returns>创建的资产信息</returns>
        Task<AssetDto> CreateAsync(CreateAssetDto dto);

        /// <summary>
        /// 更新资产
        /// </summary>
        /// <param name="id">资产ID</param>
        /// <param name="dto">更新资产DTO</param>
        /// <returns>更新后的资产信息</returns>
        Task<AssetDto> UpdateAsync(int id, UpdateAssetDto dto);

        /// <summary>
        /// 删除资产
        /// </summary>
        /// <param name="id">资产ID</param>
        /// <returns>是否删除成功</returns>
        Task<bool> DeleteAsync(int id);

        /// <summary>
        /// 根据资产编码获取资产
        /// </summary>
        /// <param name="assetCode">资产编码</param>
        /// <returns>资产信息</returns>
        Task<AssetDto> GetByCodeAsync(string assetCode);

        /// <summary>
        /// 检查资产是否存在
        /// </summary>
        /// <param name="id">资产ID</param>
        /// <returns>是否存在</returns>
        Task<bool> ExistsAsync(int id);
    }

    /// <summary>
    /// 资产DTO
    /// </summary>
    public class AssetDto
    {
        public int Id { get; set; }
        public string AssetCode { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public int AssetTypeId { get; set; }
        public string AssetTypeName { get; set; }
        public int? LocationId { get; set; }
        public string LocationName { get; set; }
        public int? DepartmentId { get; set; }
        public string DepartmentName { get; set; }
        public decimal? Price { get; set; }
        public DateTime? PurchaseDate { get; set; }
        public string Status { get; set; }
        public string Notes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int? CreatedBy { get; set; }
        public string CreatedByName { get; set; }
    }

    /// <summary>
    /// 资产查询DTO
    /// </summary>
    public class AssetQueryDto
    {
        public int PageIndex { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string Keyword { get; set; }
        public int? AssetTypeId { get; set; }
        public int? LocationId { get; set; }
        public int? DepartmentId { get; set; }
        public string Status { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string SortBy { get; set; }
        public string SortDirection { get; set; } = "asc";
    }

    /// <summary>
    /// 创建资产DTO
    /// </summary>
    public class CreateAssetDto
    {
        public string AssetCode { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public int AssetTypeId { get; set; }
        public int? LocationId { get; set; }
        public int? DepartmentId { get; set; }
        public decimal? Price { get; set; }
        public DateTime? PurchaseDate { get; set; }
        public string Status { get; set; }
        public string Notes { get; set; }
    }

    /// <summary>
    /// 更新资产DTO
    /// </summary>
    public class UpdateAssetDto
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public int? AssetTypeId { get; set; }
        public int? LocationId { get; set; }
        public int? DepartmentId { get; set; }
        public decimal? Price { get; set; }
        public DateTime? PurchaseDate { get; set; }
        public string Status { get; set; }
        public string Notes { get; set; }
    }

    /// <summary>
    /// 分页结果
    /// </summary>
    public class PagedResult<T>
    {
        public List<T> Items { get; set; } = new List<T>();
        public int TotalCount { get; set; }
        public int PageIndex { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => PageIndex > 1;
        public bool HasNextPage => PageIndex < TotalPages;
    }
}
