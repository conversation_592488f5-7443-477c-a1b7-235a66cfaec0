// File: Domain/Entities/Tasks/WorkShift.cs
// Description: 班次实体

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Domain.Entities.Tasks
{
    /// <summary>
    /// 班次实体
    /// </summary>
    [Table("work_shifts")]
    public class WorkShift
    {
        /// <summary>
        /// 班次ID
        /// </summary>
        [Key]
        [Column("shift_id")]
        public long ShiftId { get; set; }

        /// <summary>
        /// 班次名称
        /// </summary>
        [Required]
        [MaxLength(50)]
        [Column("shift_name")]
        public string ShiftName { get; set; } = string.Empty;

        /// <summary>
        /// 班次代码
        /// </summary>
        [Required]
        [MaxLength(20)]
        [Column("shift_code")]
        public string ShiftCode { get; set; } = string.Empty;

        /// <summary>
        /// 班次类型 (Day-白班, Night-夜班, Swing-中班)
        /// </summary>
        [Required]
        [MaxLength(20)]
        [Column("shift_type")]
        public string ShiftType { get; set; } = "Day";

        /// <summary>
        /// 开始时间
        /// </summary>
        [Required]
        [Column("start_time")]
        public TimeSpan StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        [Required]
        [Column("end_time")]
        public TimeSpan EndTime { get; set; }

        /// <summary>
        /// 任务领取时间
        /// </summary>
        [Required]
        [Column("task_claim_time")]
        public TimeSpan TaskClaimTime { get; set; }

        /// <summary>
        /// 是否跨天
        /// </summary>
        [Column("is_overnight")]
        public bool IsOvernight { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [Column("is_active")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 描述
        /// </summary>
        [MaxLength(500)]
        [Column("description")]
        public string? Description { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 创建用户ID
        /// </summary>
        [Column("created_by")]
        public int CreatedBy { get; set; }

        /// <summary>
        /// 更新用户ID
        /// </summary>
        [Column("updated_by")]
        public int? UpdatedBy { get; set; }

        // 导航属性
        /// <summary>
        /// 用户班次分配
        /// </summary>
        public virtual ICollection<UserShiftAssignment> UserShiftAssignments { get; set; } = new List<UserShiftAssignment>();

        /// <summary>
        /// 任务领取记录
        /// </summary>
        public virtual ICollection<TaskClaim> TaskClaims { get; set; } = new List<TaskClaim>();
    }
}
