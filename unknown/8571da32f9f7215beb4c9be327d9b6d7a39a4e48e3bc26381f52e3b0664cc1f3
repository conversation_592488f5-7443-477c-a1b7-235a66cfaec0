// File: Application/Features/Tasks/Services/TaskReminderService.cs
// Description: 任务提醒服务

using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Domain.Entities.Tasks;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Core.Abstractions;
using TaskEntity = ItAssetsSystem.Domain.Entities.Tasks.Task;

namespace ItAssetsSystem.Application.Features.Tasks.Services
{
    /// <summary>
    /// 任务提醒服务
    /// </summary>
    public class TaskReminderService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<TaskReminderService> _logger;
        private readonly INotificationService _notificationService;

        // 行业最佳实践的提醒配置
        private readonly Dictionary<string, List<ReminderConfig>> _industryBestPractices = new()
        {
            ["Critical"] = new List<ReminderConfig>
            {
                new() { Type = "BeforeDeadline", OffsetMinutes = 1440, Level = "Warning" }, // 1天前
                new() { Type = "BeforeDeadline", OffsetMinutes = 480, Level = "Warning" },  // 8小时前
                new() { Type = "BeforeDeadline", OffsetMinutes = 120, Level = "Critical" }, // 2小时前
                new() { Type = "BeforeDeadline", OffsetMinutes = 30, Level = "Critical" },  // 30分钟前
                new() { Type = "Overdue", OffsetMinutes = 0, Level = "Critical", IsRecurring = true, RecurringInterval = 60 } // 逾期后每小时
            },
            ["High"] = new List<ReminderConfig>
            {
                new() { Type = "BeforeDeadline", OffsetMinutes = 2880, Level = "Info" },    // 2天前
                new() { Type = "BeforeDeadline", OffsetMinutes = 1440, Level = "Warning" }, // 1天前
                new() { Type = "BeforeDeadline", OffsetMinutes = 240, Level = "Warning" },  // 4小时前
                new() { Type = "BeforeDeadline", OffsetMinutes = 60, Level = "Critical" },  // 1小时前
                new() { Type = "Overdue", OffsetMinutes = 0, Level = "Critical", IsRecurring = true, RecurringInterval = 120 } // 逾期后每2小时
            },
            ["Medium"] = new List<ReminderConfig>
            {
                new() { Type = "BeforeDeadline", OffsetMinutes = 4320, Level = "Info" },    // 3天前
                new() { Type = "BeforeDeadline", OffsetMinutes = 1440, Level = "Warning" }, // 1天前
                new() { Type = "BeforeDeadline", OffsetMinutes = 480, Level = "Warning" },  // 8小时前
                new() { Type = "Overdue", OffsetMinutes = 0, Level = "Warning", IsRecurring = true, RecurringInterval = 480 } // 逾期后每8小时
            },
            ["Low"] = new List<ReminderConfig>
            {
                new() { Type = "BeforeDeadline", OffsetMinutes = 7200, Level = "Info" },    // 5天前
                new() { Type = "BeforeDeadline", OffsetMinutes = 1440, Level = "Info" },    // 1天前
                new() { Type = "Overdue", OffsetMinutes = 0, Level = "Info", IsRecurring = true, RecurringInterval = 1440 } // 逾期后每天
            }
        };

        public TaskReminderService(
            AppDbContext context, 
            ILogger<TaskReminderService> logger,
            INotificationService notificationService)
        {
            _context = context;
            _logger = logger;
            _notificationService = notificationService;
        }

        /// <summary>
        /// 为任务创建行业最佳实践的提醒配置
        /// </summary>
        public async System.Threading.Tasks.Task<ApiResponse<bool>> CreateTaskRemindersAsync(long taskId, string priority, int currentUserId)
        {
            try
            {
                var task = await _context.Tasks.FirstOrDefaultAsync(t => t.TaskId == taskId);
                if (task == null)
                {
                    return ApiResponse<bool>.CreateFail("任务不存在");
                }

                if (!_industryBestPractices.ContainsKey(priority))
                {
                    priority = "Medium"; // 默认使用中等优先级配置
                }

                var configs = _industryBestPractices[priority];
                var reminders = new List<TaskReminder>();

                foreach (var config in configs)
                {
                    var reminder = new TaskReminder
                    {
                        TaskId = taskId,
                        ReminderType = config.Type,
                        OffsetMinutes = config.OffsetMinutes,
                        ReminderLevel = config.Level,
                        ReminderMethod = "System", // 默认系统通知
                        IsRecurring = config.IsRecurring,
                        RecurringInterval = config.RecurringInterval,
                        MaxOccurrences = config.IsRecurring ? 10 : 1, // 重复提醒最多10次
                        IsActive = true,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now,
                        CreatedBy = currentUserId
                    };

                    // 计算下次提醒时间
                    reminder.NextReminderTime = CalculateNextReminderTime(task, reminder);
                    
                    if (reminder.NextReminderTime.HasValue)
                    {
                        reminders.Add(reminder);
                    }
                }

                if (reminders.Any())
                {
                    _context.TaskReminders.AddRange(reminders);
                    await _context.SaveChangesAsync();
                }

                _logger.LogInformation("为任务 {TaskId} 创建了 {Count} 个提醒配置", taskId, reminders.Count);
                return ApiResponse<bool>.CreateSuccess(true, $"成功创建 {reminders.Count} 个提醒配置");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建任务提醒配置时发生错误");
                return ApiResponse<bool>.CreateFail("创建任务提醒配置失败: " + ex.Message);
            }
        }

        /// <summary>
        /// 处理到期的提醒
        /// </summary>
        public async System.Threading.Tasks.Task<int> ProcessDueRemindersAsync()
        {
            try
            {
                var now = DateTime.Now;
                var dueReminders = await _context.TaskReminders
                    .Include(r => r.Task)
                    .Where(r => r.IsActive 
                             && r.NextReminderTime.HasValue 
                             && r.NextReminderTime <= now
                             && (r.MaxOccurrences == null || r.SentCount < r.MaxOccurrences))
                    .ToListAsync();

                var processedCount = 0;

                foreach (var reminder in dueReminders)
                {
                    try
                    {
                        // 发送提醒通知
                        await SendReminderNotificationAsync(reminder);

                        // 更新提醒记录
                        reminder.SentCount++;
                        reminder.LastSentAt = now;

                        // 计算下次提醒时间
                        if (reminder.IsRecurring && 
                            (reminder.MaxOccurrences == null || reminder.SentCount < reminder.MaxOccurrences))
                        {
                            reminder.NextReminderTime = now.AddMinutes(reminder.RecurringInterval ?? 60);
                        }
                        else
                        {
                            reminder.NextReminderTime = null;
                            reminder.IsActive = false;
                        }

                        reminder.UpdatedAt = now;
                        processedCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "处理提醒 {ReminderId} 时发生错误", reminder.ReminderId);
                    }
                }

                if (processedCount > 0)
                {
                    await _context.SaveChangesAsync();
                }

                _logger.LogInformation("处理了 {Count} 个到期提醒", processedCount);
                return processedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理到期提醒时发生错误");
                return 0;
            }
        }

        /// <summary>
        /// 发送提醒通知
        /// </summary>
        private async System.Threading.Tasks.Task SendReminderNotificationAsync(TaskReminder reminder)
        {
            var task = reminder.Task;
            var message = GenerateReminderMessage(reminder, task);

            // 获取任务相关用户
            var userIds = new List<int>();
            if (task.AssigneeUserId.HasValue)
            {
                userIds.Add(task.AssigneeUserId.Value);
            }

            // 获取任务协作者
            var assignees = await _context.TaskAssignees
                .Where(a => a.TaskId == task.TaskId)
                .Select(a => a.UserId)
                .ToListAsync();
            userIds.AddRange(assignees);

            userIds = userIds.Distinct().ToList();

            // 发送通知
            foreach (var userId in userIds)
            {
                await _notificationService.CreateNotificationAsync(
                    userId,
                    $"任务提醒 - {reminder.ReminderLevel}",
                    message,
                    "TaskReminder",
                    "Task",
                    task.TaskId,
                    null,
                    reminder.ReminderLevel == "Critical" ? "High" : "Normal"
                );
            }
        }

        /// <summary>
        /// 生成提醒消息
        /// </summary>
        private string GenerateReminderMessage(TaskReminder reminder, TaskEntity task)
        {
            var message = reminder.ReminderType switch
            {
                "BeforeStart" => $"任务 '{task.Name}' 即将开始",
                "BeforeDeadline" => $"任务 '{task.Name}' 即将到期",
                "Overdue" => $"任务 '{task.Name}' 已逾期",
                _ => $"任务 '{task.Name}' 需要关注"
            };

            if (task.PlanEndDate.HasValue)
            {
                var timeInfo = reminder.ReminderType switch
                {
                    "BeforeDeadline" => $"，截止时间：{task.PlanEndDate:yyyy-MM-dd HH:mm}",
                    "Overdue" => $"，已逾期 {(DateTime.Now - task.PlanEndDate.Value).Days} 天",
                    _ => ""
                };
                message += timeInfo;
            }

            return message;
        }

        /// <summary>
        /// 计算下次提醒时间
        /// </summary>
        private DateTime? CalculateNextReminderTime(TaskEntity task, TaskReminder reminder)
        {
            var now = DateTime.Now;

            return reminder.ReminderType switch
            {
                "BeforeStart" when task.PlanStartDate.HasValue => 
                    task.PlanStartDate.Value.AddMinutes(-reminder.OffsetMinutes),
                "BeforeDeadline" when task.PlanEndDate.HasValue => 
                    task.PlanEndDate.Value.AddMinutes(-reminder.OffsetMinutes),
                "Overdue" when task.PlanEndDate.HasValue && task.PlanEndDate < now => 
                    now.AddMinutes(reminder.OffsetMinutes),
                _ => null
            };
        }

        /// <summary>
        /// 获取任务的提醒配置
        /// </summary>
        public async System.Threading.Tasks.Task<ApiResponse<List<TaskReminderDto>>> GetTaskRemindersAsync(long taskId)
        {
            try
            {
                var reminders = await _context.TaskReminders
                    .Where(r => r.TaskId == taskId)
                    .OrderBy(r => r.ReminderType)
                    .ThenBy(r => r.OffsetMinutes)
                    .Select(r => new TaskReminderDto
                    {
                        ReminderId = r.ReminderId,
                        TaskId = r.TaskId,
                        ReminderType = r.ReminderType,
                        OffsetMinutes = r.OffsetMinutes,
                        ReminderLevel = r.ReminderLevel,
                        ReminderMethod = r.ReminderMethod,
                        IsRecurring = r.IsRecurring,
                        RecurringInterval = r.RecurringInterval,
                        MaxOccurrences = r.MaxOccurrences,
                        SentCount = r.SentCount,
                        NextReminderTime = r.NextReminderTime,
                        LastSentAt = r.LastSentAt,
                        IsActive = r.IsActive
                    })
                    .ToListAsync();

                return ApiResponse<List<TaskReminderDto>>.CreateSuccess(reminders, "获取任务提醒配置成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务提醒配置时发生错误");
                return ApiResponse<List<TaskReminderDto>>.CreateFail("获取任务提醒配置失败: " + ex.Message);
            }
        }
    }

    /// <summary>
    /// 提醒配置
    /// </summary>
    public class ReminderConfig
    {
        public string Type { get; set; } = string.Empty;
        public int OffsetMinutes { get; set; }
        public string Level { get; set; } = string.Empty;
        public bool IsRecurring { get; set; }
        public int? RecurringInterval { get; set; }
    }

    /// <summary>
    /// 任务提醒DTO
    /// </summary>
    public class TaskReminderDto
    {
        public long ReminderId { get; set; }
        public long TaskId { get; set; }
        public string ReminderType { get; set; } = string.Empty;
        public int OffsetMinutes { get; set; }
        public string ReminderLevel { get; set; } = string.Empty;
        public string ReminderMethod { get; set; } = string.Empty;
        public bool IsRecurring { get; set; }
        public int? RecurringInterval { get; set; }
        public int? MaxOccurrences { get; set; }
        public int SentCount { get; set; }
        public DateTime? NextReminderTime { get; set; }
        public DateTime? LastSentAt { get; set; }
        public bool IsActive { get; set; }
    }
}
