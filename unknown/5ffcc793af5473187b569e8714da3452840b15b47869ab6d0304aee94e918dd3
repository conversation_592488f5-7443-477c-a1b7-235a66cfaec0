using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Core.Monitoring
{
    /// <summary>
    /// 性能监控服务 - 用于基础解耦过程中的性能对比
    /// </summary>
    public interface IPerformanceMonitor
    {
        Task<T> MonitorAsync<T>(string operationName, Func<Task<T>> operation, string version = "v1");
        Task<T> MonitorAndCompareAsync<T>(string operationName, Func<Task<T>> operation, string version = "v1");
        PerformanceReport GetPerformanceReport(string operationName);
        void SetBaseline(string operationName, double baselineMs);
    }

    /// <summary>
    /// 性能监控服务实现
    /// </summary>
    public class PerformanceMonitor : IPerformanceMonitor
    {
        private readonly ILogger<PerformanceMonitor> _logger;
        private readonly IMemoryCache _cache;

        // 性能基准线（毫秒）
        private static readonly Dictionary<string, double> BaselineMetrics = new()
        {
            { "UserLogin", 200.0 },      // 登录响应时间基准 200ms
            { "AssetList", 500.0 },      // 资产列表查询基准 500ms
            { "AssetGetById", 50.0 },    // 资产查询基准 50ms
            { "AssetGetPaged", 300.0 },  // 资产分页查询基准 300ms
            { "AssetCreate", 200.0 },    // 资产创建基准 200ms
            { "AssetUpdate", 150.0 },    // 资产更新基准 150ms
            { "AssetDelete", 100.0 },    // 资产删除基准 100ms
            { "TaskGetById", 50.0 },     // 任务查询基准 50ms
            { "TaskGetPaged", 400.0 },   // 任务分页查询基准 400ms
            { "TaskCreate", 300.0 },     // 任务创建基准 300ms
            { "TaskUpdate", 200.0 },     // 任务更新基准 200ms
            { "TaskAssign", 100.0 },     // 任务分配基准 100ms
            { "TaskComplete", 150.0 },   // 任务完成基准 150ms
            { "TaskExists", 30.0 },      // 任务存在检查基准 30ms
            { "DatabaseQuery", 100.0 },  // 数据库查询基准 100ms
            { "UserGetById", 50.0 },     // 用户查询基准 50ms
            { "UserAuthenticate", 150.0 } // 用户认证基准 150ms
        };

        public PerformanceMonitor(ILogger<PerformanceMonitor> logger, IMemoryCache cache)
        {
            _logger = logger;
            _cache = cache;
        }

        /// <summary>
        /// 监控操作性能
        /// </summary>
        public async Task<T> MonitorAsync<T>(string operationName, Func<Task<T>> operation, string version = "v1")
        {
            var stopwatch = Stopwatch.StartNew();
            var startTime = DateTime.UtcNow;

            try
            {
                var result = await operation();
                stopwatch.Stop();

                var duration = stopwatch.ElapsedMilliseconds;
                RecordPerformanceMetric(operationName, version, duration, true);

                _logger.LogInformation("操作完成: {Operation} {Version} 耗时 {Duration}ms", 
                    operationName, version, duration);

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                RecordPerformanceMetric(operationName, version, stopwatch.ElapsedMilliseconds, false);
                
                _logger.LogError(ex, "操作失败: {Operation} {Version} 在 {Duration}ms 后失败", 
                    operationName, version, stopwatch.ElapsedMilliseconds);
                throw;
            }
        }

        /// <summary>
        /// 监控并对比性能
        /// </summary>
        public async Task<T> MonitorAndCompareAsync<T>(string operationName, Func<Task<T>> operation, string version = "v1")
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                var result = await operation();
                stopwatch.Stop();

                var duration = stopwatch.ElapsedMilliseconds;
                var baseline = BaselineMetrics.GetValueOrDefault(operationName, 1000.0);

                // 记录性能数据
                RecordPerformanceMetric(operationName, version, duration, true);

                // 性能对比分析
                var performanceRatio = duration / baseline;
                
                if (performanceRatio > 1.5)
                {
                    _logger.LogWarning("⚠️ 性能下降警告: {Operation} {Version} 耗时 {Duration}ms，基准 {Baseline}ms，下降 {Ratio:P0}", 
                        operationName, version, duration, baseline, performanceRatio - 1);
                }
                else if (performanceRatio > 1.2)
                {
                    _logger.LogInformation("📊 性能轻微下降: {Operation} {Version} 耗时 {Duration}ms，基准 {Baseline}ms，下降 {Ratio:P0}", 
                        operationName, version, duration, baseline, performanceRatio - 1);
                }
                else if (performanceRatio < 0.8)
                {
                    _logger.LogInformation("🚀 性能提升: {Operation} {Version} 耗时 {Duration}ms，基准 {Baseline}ms，提升 {Ratio:P0}", 
                        operationName, version, duration, baseline, 1 - performanceRatio);
                }
                else
                {
                    _logger.LogInformation("✅ 性能正常: {Operation} {Version} 耗时 {Duration}ms，基准 {Baseline}ms", 
                        operationName, version, duration, baseline);
                }

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                RecordPerformanceMetric(operationName, version, stopwatch.ElapsedMilliseconds, false);
                
                _logger.LogError(ex, "❌ 操作失败: {Operation} {Version} 在 {Duration}ms 后失败", 
                    operationName, version, stopwatch.ElapsedMilliseconds);
                throw;
            }
        }

        /// <summary>
        /// 记录性能指标
        /// </summary>
        private void RecordPerformanceMetric(string operation, string version, long duration, bool success)
        {
            var key = $"perf_{operation}_{version}";
            var metrics = _cache.GetOrCreate(key, _ => new List<PerformanceMetric>());
            
            metrics.Add(new PerformanceMetric
            {
                Timestamp = DateTime.UtcNow,
                Duration = duration,
                Version = version,
                Success = success,
                Baseline = BaselineMetrics.GetValueOrDefault(operation, 1000.0)
            });

            // 只保留最近100条记录
            if (metrics.Count > 100)
            {
                metrics.RemoveAt(0);
            }

            _cache.Set(key, metrics, TimeSpan.FromHours(2));
        }

        /// <summary>
        /// 获取性能报告
        /// </summary>
        public PerformanceReport GetPerformanceReport(string operationName)
        {
            var report = new PerformanceReport
            {
                OperationName = operationName,
                GeneratedAt = DateTime.UtcNow
            };

            // 获取不同版本的性能数据
            foreach (var version in new[] { "v1", "v1.1", "v2" })
            {
                var key = $"perf_{operationName}_{version}";
                if (_cache.TryGetValue(key, out List<PerformanceMetric> metrics))
                {
                    var successfulMetrics = metrics.Where(m => m.Success).ToList();

                    var versionReport = new VersionPerformanceReport
                    {
                        Version = version,
                        TotalRequests = metrics.Count,
                        SuccessfulRequests = metrics.Count(m => m.Success),
                        FailedRequests = metrics.Count(m => !m.Success),
                        AverageDuration = successfulMetrics.Any() ? successfulMetrics.Average(m => m.Duration) : 0,
                        MinDuration = successfulMetrics.Any() ? successfulMetrics.Min(m => m.Duration) : 0,
                        MaxDuration = successfulMetrics.Any() ? successfulMetrics.Max(m => m.Duration) : 0,
                        Baseline = metrics.FirstOrDefault()?.Baseline ?? 0
                    };

                    versionReport.SuccessRate = versionReport.TotalRequests > 0 
                        ? (double)versionReport.SuccessfulRequests / versionReport.TotalRequests * 100 
                        : 0;

                    versionReport.PerformanceRatio = versionReport.Baseline > 0 
                        ? versionReport.AverageDuration / versionReport.Baseline 
                        : 1;

                    report.VersionReports.Add(versionReport);
                }
            }

            return report;
        }

        /// <summary>
        /// 设置性能基准线
        /// </summary>
        public void SetBaseline(string operationName, double baselineMs)
        {
            BaselineMetrics[operationName] = baselineMs;
            _logger.LogInformation("设置性能基准: {Operation} = {Baseline}ms", operationName, baselineMs);
        }
    }

    /// <summary>
    /// 性能指标
    /// </summary>
    public class PerformanceMetric
    {
        public DateTime Timestamp { get; set; }
        public long Duration { get; set; }
        public string Version { get; set; }
        public bool Success { get; set; }
        public double Baseline { get; set; }
    }

    /// <summary>
    /// 性能报告
    /// </summary>
    public class PerformanceReport
    {
        public string OperationName { get; set; }
        public DateTime GeneratedAt { get; set; }
        public List<VersionPerformanceReport> VersionReports { get; set; } = new();
    }

    /// <summary>
    /// 版本性能报告
    /// </summary>
    public class VersionPerformanceReport
    {
        public string Version { get; set; }
        public int TotalRequests { get; set; }
        public int SuccessfulRequests { get; set; }
        public int FailedRequests { get; set; }
        public double SuccessRate { get; set; }
        public double AverageDuration { get; set; }
        public long MinDuration { get; set; }
        public long MaxDuration { get; set; }
        public double Baseline { get; set; }
        public double PerformanceRatio { get; set; }
    }
}
