// File: Domain/Entities/Tasks/UserShiftAssignment.cs
// Description: 用户班次分配实体

using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Domain.Entities.Tasks
{
    /// <summary>
    /// 用户班次分配实体
    /// </summary>
    [Table("user_shift_assignments")]
    public class UserShiftAssignment
    {
        /// <summary>
        /// 分配ID
        /// </summary>
        [Key]
        [Column("assignment_id")]
        public long AssignmentId { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        [Required]
        [Column("user_id")]
        public int UserId { get; set; }

        /// <summary>
        /// 班次ID
        /// </summary>
        [Required]
        [Column("shift_id")]
        public long ShiftId { get; set; }

        /// <summary>
        /// 生效日期
        /// </summary>
        [Required]
        [Column("effective_date")]
        public DateTime EffectiveDate { get; set; }

        /// <summary>
        /// 失效日期
        /// </summary>
        [Column("expiry_date")]
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [Column("is_active")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 分配类型 (Permanent-固定, Temporary-临时, Rotation-轮班)
        /// </summary>
        [Required]
        [MaxLength(20)]
        [Column("assignment_type")]
        public string AssignmentType { get; set; } = "Permanent";

        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(500)]
        [Column("notes")]
        public string? Notes { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 创建用户ID
        /// </summary>
        [Column("created_by")]
        public int CreatedBy { get; set; }

        /// <summary>
        /// 更新用户ID
        /// </summary>
        [Column("updated_by")]
        public int? UpdatedBy { get; set; }

        // 导航属性
        /// <summary>
        /// 用户
        /// </summary>
        [ForeignKey("UserId")]
        public virtual Models.Entities.User User { get; set; } = null!;

        /// <summary>
        /// 班次
        /// </summary>
        [ForeignKey("ShiftId")]
        public virtual WorkShift WorkShift { get; set; } = null!;
    }
}
