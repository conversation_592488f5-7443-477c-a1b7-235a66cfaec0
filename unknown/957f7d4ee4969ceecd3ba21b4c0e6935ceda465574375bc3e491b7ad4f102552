# 位置部门继承功能使用指南

## 1. 新接口概述

### 功能说明
位置部门继承功能解决了"某个位置的资产属于哪个部门"的统计需求，通过CTE递归查询实现完整的位置层级部门继承逻辑。

### 核心概念
- **直接分配部门**: 位置直接设置的部门
- **继承部门**: 从父级位置继承的部门
- **有效部门**: 优先使用直接分配，若无则使用继承部门

## 2. API接口使用

### 基础路径
```
/api/v2/locationdepartmentinheritance
```

### 接口列表

#### 2.1 获取所有位置的部门继承信息
```http
GET /api/v2/locationdepartmentinheritance
```

**响应示例:**
```json
{
  "success": true,
  "data": [
    {
      "locationId": 1,
      "locationName": "生产车间A",
      "locationPath": "工厂 > 生产区 > 生产车间A",
      "directDepartmentId": 5,
      "directDepartmentName": "生产部",
      "inheritedDepartmentId": 5,
      "inheritedDepartmentName": "生产部",
      "effectiveDepartmentId": 5,
      "effectiveDepartmentName": "生产部",
      "assetCount": 15,
      "assets": [...]
    }
  ],
  "message": "成功获取 50 个位置的部门继承信息"
}
```

#### 2.2 获取指定位置的部门继承信息
```http
GET /api/v2/locationdepartmentinheritance/{locationId}
```

#### 2.3 获取部门位置统计
```http
GET /api/v2/locationdepartmentinheritance/department-stats
```

**响应示例:**
```json
{
  "success": true,
  "data": [
    {
      "departmentId": 5,
      "departmentName": "生产部",
      "directLocationCount": 3,
      "inheritedLocationCount": 7,
      "totalLocationCount": 10,
      "directAssetCount": 25,
      "inheritedAssetCount": 40,
      "totalAssetCount": 65
    }
  ]
}
```

#### 2.4 获取部门管理的所有位置
```http
GET /api/v2/locationdepartmentinheritance/by-department/{departmentId}
```

## 3. 前端集成方式

### 3.1 API封装 (已创建)
```javascript
// frontend/src/api/locationDepartmentInheritance.js
import locationDepartmentInheritanceApi from '@/api/locationDepartmentInheritance'

// 获取位置部门继承信息
const response = await locationDepartmentInheritanceApi.getLocationDepartmentInheritanceById(locationId)
```

### 3.2 组件集成 (已创建)
```vue
<!-- frontend/src/views/locations/components/DepartmentInheritancePanel.vue -->
<DepartmentInheritancePanel 
  :location-id="selectedLocationId"
  @asset-click="handleAssetClick"
  @department-click="handleDepartmentClick"
/>
```

### 3.3 在现有页面中使用

#### 在位置关联页面 (relations.vue) 中添加部门继承面板:

```vue
<template>
  <div class="location-relations-container">
    <!-- 现有内容 -->
    
    <!-- 新增：部门继承信息面板 -->
    <el-card class="department-inheritance-card" v-if="selectedLocation">
      <DepartmentInheritancePanel 
        :location-id="selectedLocation"
        @asset-click="handleAssetClick"
        @department-click="handleDepartmentClick"
      />
    </el-card>
  </div>
</template>

<script setup>
import DepartmentInheritancePanel from './components/DepartmentInheritancePanel.vue'

// 处理资产点击
const handleAssetClick = (assetId) => {
  // 跳转到资产详情或其他处理
  console.log('查看资产:', assetId)
}

// 处理部门点击
const handleDepartmentClick = ({ departmentId, locations }) => {
  // 显示部门管理的位置列表
  console.log('部门位置:', departmentId, locations)
}
</script>
```

## 4. 与现有功能的结合

### 4.1 资产管理模块集成
在资产列表页面显示资产所属位置的有效部门：

```javascript
// 获取资产时同时获取位置部门信息
const enrichAssetsWithDepartment = async (assets) => {
  const locationIds = [...new Set(assets.map(asset => asset.locationId))]
  const departmentInfos = {}
  
  for (const locationId of locationIds) {
    const response = await locationDepartmentInheritanceApi.getLocationDepartmentInheritanceById(locationId)
    if (response.success) {
      departmentInfos[locationId] = response.data
    }
  }
  
  return assets.map(asset => ({
    ...asset,
    effectiveDepartment: departmentInfos[asset.locationId]?.effectiveDepartmentName || '未分配'
  }))
}
```

### 4.2 部门管理模块集成
在部门详情页面显示该部门管理的位置和资产统计：

```vue
<template>
  <div class="department-detail">
    <!-- 部门基本信息 -->
    
    <!-- 部门位置统计 -->
    <el-card>
      <template #header>位置管理统计</template>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-statistic title="直接管理位置" :value="departmentStats.directLocationCount" />
        </el-col>
        <el-col :span="8">
          <el-statistic title="继承管理位置" :value="departmentStats.inheritedLocationCount" />
        </el-col>
        <el-col :span="8">
          <el-statistic title="总管理资产" :value="departmentStats.totalAssetCount" />
        </el-col>
      </el-row>
      
      <!-- 位置列表 -->
      <el-button @click="loadDepartmentLocations">查看管理的所有位置</el-button>
    </el-card>
  </div>
</template>
```

### 4.3 仪表盘统计集成
在系统仪表盘中添加部门资产分布图表：

```javascript
// 获取部门资产分布数据
const getDepartmentAssetDistribution = async () => {
  const response = await locationDepartmentInheritanceApi.getDepartmentLocationStats()
  if (response.success) {
    return response.data.map(dept => ({
      name: dept.departmentName,
      value: dept.totalAssetCount,
      directAssets: dept.directAssetCount,
      inheritedAssets: dept.inheritedAssetCount
    }))
  }
  return []
}
```

## 5. 实际应用场景

### 5.1 资产盘点
按部门进行资产盘点时，可以准确获取每个部门负责的所有资产（包括继承的）：

```javascript
// 生成部门资产盘点清单
const generateDepartmentInventory = async (departmentId) => {
  const response = await locationDepartmentInheritanceApi.getLocationsByDepartment(departmentId)
  if (response.success) {
    const allAssets = response.data.flatMap(location => location.assets)
    return {
      departmentId,
      locations: response.data,
      totalAssets: allAssets.length,
      assetDetails: allAssets
    }
  }
}
```

### 5.2 设备维护责任分配
根据位置的有效部门分配维护责任：

```javascript
// 为故障工单分配负责部门
const assignMaintenanceDepartment = async (faultRecord) => {
  if (faultRecord.locationId) {
    const response = await locationDepartmentInheritanceApi.getLocationDepartmentInheritanceById(faultRecord.locationId)
    if (response.success && response.data.effectiveDepartmentId) {
      return {
        ...faultRecord,
        responsibleDepartmentId: response.data.effectiveDepartmentId,
        responsibleDepartmentName: response.data.effectiveDepartmentName
      }
    }
  }
  return faultRecord
}
```

### 5.3 报表统计
生成按部门分组的资产统计报表：

```javascript
// 生成部门资产统计报表
const generateDepartmentAssetReport = async () => {
  const response = await locationDepartmentInheritanceApi.getDepartmentLocationStats()
  if (response.success) {
    return response.data.map(dept => ({
      部门名称: dept.departmentName,
      直接管理位置: dept.directLocationCount,
      继承管理位置: dept.inheritedLocationCount,
      总管理位置: dept.totalLocationCount,
      直接管理资产: dept.directAssetCount,
      继承管理资产: dept.inheritedAssetCount,
      总管理资产: dept.totalAssetCount
    }))
  }
}
```

## 6. 注意事项

### 6.1 权限控制
新接口已集成JWT认证，确保只有登录用户才能访问。

### 6.2 性能考虑
- CTE递归查询在大数据量时可能较慢，建议添加适当的缓存机制
- 部门统计信息可以考虑定时计算并缓存

### 6.3 数据一致性
- 只读取核心表数据，不修改原有数据结构
- 部门继承逻辑完全基于现有的locations.department_id和locations.parent_id字段

### 6.4 错误处理
所有API都包含完整的错误处理和日志记录，便于问题排查。

## 7. 扩展建议

### 7.1 缓存优化
```javascript
// 添加Redis缓存
const getCachedLocationDepartmentInfo = async (locationId) => {
  const cacheKey = `location_dept_${locationId}`
  let cached = await redis.get(cacheKey)
  
  if (!cached) {
    const response = await locationDepartmentInheritanceApi.getLocationDepartmentInheritanceById(locationId)
    if (response.success) {
      await redis.setex(cacheKey, 3600, JSON.stringify(response.data)) // 缓存1小时
      return response.data
    }
  }
  
  return JSON.parse(cached)
}
```

### 7.2 实时更新
当位置的department_id发生变化时，可以通过SignalR推送更新：

```javascript
// 监听位置部门变更
connection.on('LocationDepartmentChanged', (locationId) => {
  // 刷新相关组件数据
  refreshLocationDepartmentInfo(locationId)
})
```

这个新功能完全独立于原有系统，可以逐步集成到各个模块中，提供强大的位置部门继承查询能力。