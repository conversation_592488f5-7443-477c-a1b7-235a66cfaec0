// File: Api/V2/Controllers/SparePartInventoryController.cs
// Description: 备品备件库存状态管理API控制器

using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Features.SpareParts.Services;
using ItAssetsSystem.Application.Features.SpareParts.Dtos;
using ItAssetsSystem.Application.Common.Dtos;
using Microsoft.AspNetCore.Authorization;
using System;
using System.Collections.Generic;

namespace ItAssetsSystem.Api.V2.Controllers
{
    /// <summary>
    /// 备品备件库存状态管理API控制器
    /// </summary>
    [ApiController]
    [Route("api/v2/spare-part-inventory")]
    // [Authorize] // 临时注释掉授权要求，方便测试
    public class SparePartInventoryController : ControllerBase
    {
        private readonly ISparePartInventoryService _inventoryService;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public SparePartInventoryController(ISparePartInventoryService inventoryService)
        {
            _inventoryService = inventoryService;
        }
        
        /// <summary>
        /// 获取备件库存汇总
        /// </summary>
        /// <param name="partId">备件ID</param>
        /// <returns>库存汇总信息</returns>
        [HttpGet("{partId}/stock-summary")]
        public async Task<IActionResult> GetStockSummary(long partId)
        {
            var result = await _inventoryService.GetStockSummaryAsync(partId);
            if (result == null)
            {
                return NotFound($"备件ID {partId} 不存在");
            }
            return Ok(result);
        }
        
        /// <summary>
        /// 获取备件库存明细列表
        /// </summary>
        /// <param name="partId">备件ID</param>
        /// <param name="query">查询参数</param>
        /// <returns>库存明细列表</returns>
        [HttpGet("{partId}/inventories")]
        public async Task<IActionResult> GetInventories(long partId, [FromQuery] SparePartInventoryQuery query)
        {
            query.PartId = partId;
            var result = await _inventoryService.GetInventoriesAsync(query);
            return Ok(result);
        }
        
        /// <summary>
        /// 获取所有状态类型
        /// </summary>
        /// <returns>状态类型列表</returns>
        [HttpGet("status-types")]
        public async Task<IActionResult> GetStatusTypes()
        {
            var result = await _inventoryService.GetStatusTypesAsync();
            return Ok(result);
        }
        
        /// <summary>
        /// 批量更新库存状态
        /// </summary>
        /// <param name="request">状态更新请求</param>
        /// <returns>更新结果</returns>
        [HttpPost("status/batch-update")]
        public async Task<IActionResult> BatchUpdateStatus([FromBody] BatchStatusUpdateRequest request)
        {
            var result = await _inventoryService.BatchUpdateStatusAsync(request);
            return Ok(result);
        }
        
        /// <summary>
        /// 获取库存状态变更历史
        /// </summary>
        /// <param name="inventoryId">库存明细ID</param>
        /// <param name="query">分页参数</param>
        /// <returns>状态变更历史</returns>
        [HttpGet("inventories/{inventoryId}/status-history")]
        public async Task<IActionResult> GetStatusHistory(long inventoryId, [FromQuery] PaginationQuery query)
        {
            var result = await _inventoryService.GetStatusHistoryAsync(inventoryId, query);
            return Ok(result);
        }
        
        /// <summary>
        /// 创建返厂维修单
        /// </summary>
        /// <param name="request">返厂维修单创建请求</param>
        /// <returns>创建结果</returns>
        [HttpPost("repair-orders")]
        public async Task<IActionResult> CreateRepairOrder([FromBody] CreateRepairOrderRequest request)
        {
            var result = await _inventoryService.CreateRepairOrderAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 状态调整
        /// </summary>
        /// <param name="request">状态调整请求</param>
        /// <returns>调整结果</returns>
        [HttpPost("status/adjust")]
        public async Task<IActionResult> AdjustStatus([FromBody] StatusAdjustmentRequest request)
        {
            var result = await _inventoryService.AdjustStatusAsync(request);
            return Ok(result);
        }
    }
}
