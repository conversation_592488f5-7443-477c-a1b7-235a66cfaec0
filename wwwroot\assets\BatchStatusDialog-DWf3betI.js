import{aV as e,r as a,c as t,a8 as l,_ as s,aW as n,aX as r,aY as o,aZ as i,ac as u,a_ as d,a$ as c,ab as p,ao as m,aM as v,B as g,an as y,aN as f,a as k,b as h,o as w,a9 as b,d as I,as as _,e as V,t as U,w as D,A as T,i as C,Y as S,F as E,h as x,p as A,af as N,b0 as P,a7 as z,b1 as L,b2 as M,am as B,aq as $,aO as F,j as O,a5 as H,f as j,ar as q,a1 as R,m as K,aP as Q,aS as W}from"./index-C7OOw0MO.js";import{U as Y,a as G}from"./UserAvatarStack-CUKN1R7u.js";import{a as X,b as Z}from"./format-DfhXadVZ.js";import{U as J}from"./UserSelect-C-1BVWPu.js";import{t as ee}from"./task-BTGSy_AK.js";const ae=e("taskEnhanced",(()=>{const e=a([]),s=a([]),n=a({total:0,completed:0,inProgress:0,overdue:0,todo:0}),r=a(!1),o=a(null),i=a([]),u=a({status:"",priority:"",assigneeId:"",assetId:"",locationId:"",dateRange:[]}),d=a(""),c=a({pageNumber:1,pageSize:20,total:0}),p=t((()=>{let a=[...e.value];if(d.value){const e=d.value.toLowerCase();a=a.filter((a=>{var t,l,s;return(null==(t=a.name)?void 0:t.toLowerCase().includes(e))||(null==(l=a.description)?void 0:l.toLowerCase().includes(e))||(null==(s=a.assigneeUserName)?void 0:s.toLowerCase().includes(e))}))}if(u.value.status&&(a=a.filter((e=>e.status===u.value.status))),u.value.priority&&(a=a.filter((e=>e.priority===u.value.priority))),u.value.assigneeId&&(a=a.filter((e=>e.assigneeUserId===u.value.assigneeId))),u.value.assetId&&(a=a.filter((e=>e.assetId===u.value.assetId))),u.value.locationId&&(a=a.filter((e=>e.locationId===u.value.locationId))),u.value.dateRange&&2===u.value.dateRange.length){const[e,t]=u.value.dateRange;a=a.filter((a=>{const l=new Date(a.planEndDate);return l>=e&&l<=t}))}return a})),m=t((()=>{const e={Todo:[],InProgress:[],Done:[],Cancelled:[],Overdue:[]};return p.value.forEach((a=>{var t;a.isOverdue&&"Done"!==a.status?e.Overdue.push(a):null==(t=e[a.status])||t.push(a)})),e})),v=t((()=>e.value.filter((e=>e.isOverdue&&"Done"!==e.status)).length)),g=t((()=>{const a=new Date,t=new Date(a.getTime()+2592e5);return e.value.filter((e=>e.planEndDate&&"Done"!==e.status&&new Date(e.planEndDate)<=t&&new Date(e.planEndDate)>=a)).sort(((e,a)=>new Date(e.planEndDate)-new Date(a.planEndDate)))})),y=async(a={})=>{r.value=!0;try{const t={pageNumber:c.value.pageNumber,pageSize:c.value.pageSize,searchTerm:d.value,...u.value,...a},l=await ee.getTaskList(t);if(l.success)return e.value=l.data||[],w(),l;throw new Error(l.message||"获取任务列表失败")}catch(t){throw l.error(t.message||"获取任务列表失败"),t}finally{r.value=!1}},f=async a=>{var t,l,s;try{const t=await ee.deleteTask(a);if(t.success){const t=e.value.findIndex((e=>e.taskId===a));if(-1!==t){const l=e.value[t];e.value.splice(t,1),w(),b({type:"warning",title:"任务删除成功",message:`任务 "${l.name}" 已删除`,taskId:a})}return!0}throw new Error(t.message||"删除任务失败")}catch(n){throw 404===(null==(t=null==n?void 0:n.response)?void 0:t.status)?("undefined"!=typeof window&&window.ElMessage&&window.ElMessage.error("任务已被删除或不存在"),await y()):"undefined"!=typeof window&&window.ElMessage&&window.ElMessage.error("删除任务失败: "+((null==(s=null==(l=n.response)?void 0:l.data)?void 0:s.message)||n.message)),n}},k=async(a,t,l="")=>{try{const s=await ee.updateTaskStatus(a,t,l);if(s.success){const l=e.value.findIndex((e=>e.taskId===a));return-1!==l&&(e.value[l]=s.data),w(),b({type:"info",title:"任务状态更新",message:`任务状态已更新为 "${t}"`,taskId:a}),s.data}throw new Error(s.message||"更新任务状态失败")}catch(s){throw s}},h=async(a,t,l="")=>{try{const s=await ee.assignTask(a,t,l);if(s.success){const t=e.value.findIndex((e=>e.taskId===a));return-1!==t&&(e.value[t]=s.data),b({type:"info",title:"任务分配成功",message:`任务已分配给 "${s.data.assigneeUserName}"`,taskId:a}),s.data}throw new Error(s.message||"任务分配失败")}catch(s){throw s}},w=()=>{const a={total:e.value.length,completed:0,inProgress:0,overdue:0,todo:0};e.value.forEach((e=>{switch(e.status){case"Done":a.completed++;break;case"InProgress":a.inProgress++;break;case"Todo":a.todo++}e.isOverdue&&"Done"!==e.status&&a.overdue++})),n.value=a},b=e=>{const a={id:Date.now(),timestamp:(new Date).toISOString(),read:!1,...e};i.value.unshift(a),i.value.length>100&&(i.value=i.value.slice(0,50))},I=()=>{u.value={status:"",priority:"",assigneeId:"",assetId:"",locationId:"",dateRange:[]}};return{tasks:e,periodicSchedules:s,taskStats:n,loading:r,currentTask:o,notifications:i,filters:u,searchQuery:d,pagination:c,filteredTasks:p,tasksByStatus:m,overdueTasksCount:v,upcomingDeadlines:g,fetchTasks:y,getTaskById:async e=>{var a,t,l;try{const a=await ee.getTaskById(e);if(a.success&&a.data)return o.value=a.data,a.data;throw new Error(a.message||"获取任务详情失败")}catch(s){throw 404===(null==(a=null==s?void 0:s.response)?void 0:a.status)?"undefined"!=typeof window&&window.ElMessage&&window.ElMessage.error("任务已被删除或不存在"):"undefined"!=typeof window&&window.ElMessage&&window.ElMessage.error("获取任务详情失败: "+((null==(l=null==(t=s.response)?void 0:t.data)?void 0:l.message)||s.message)),s}},createTask:async a=>{try{const t=await ee.createTask(a);if(t.success)return e.value.unshift(t.data),w(),b({type:"success",title:"任务创建成功",message:`任务 "${a.name}" 已创建`,taskId:t.data.taskId}),t.data;throw new Error(t.message||"创建任务失败")}catch(t){throw t}},updateTask:async(a,t)=>{var l,s;try{let n,r;if("object"==typeof a&&null!==a){const{taskId:e,...t}=a;n=e,r=t}else n=a,r=t||{};if(!n||0===n||"0"===n)throw new Error("任务ID不能为空或为0");const i=await ee.updateTask(n,r);if(i.success){const a=e.value.findIndex((e=>e.taskId===n));return-1!==a&&(e.value[a]=i.data),(null==(l=o.value)?void 0:l.taskId)===n&&(o.value=i.data),w(),b({type:"info",title:"任务更新成功",message:`任务 "${(null==(s=i.data)?void 0:s.name)||"未命名任务"}" 已更新`,taskId:n}),i.data}throw new Error(i.message||"更新任务失败")}catch(n){throw n}},deleteTask:f,updateTaskStatus:k,updateTaskProgress:async(a,t,l="")=>{try{const s=await ee.updateTaskProgress(a,t,l);if(s.success){const t=e.value.findIndex((e=>e.taskId===a));return-1!==t&&(e.value[t]=s.data),w(),s.data}throw new Error(s.message||"更新任务进度失败")}catch(s){throw s}},assignTask:h,completeTask:async(a,t="")=>{try{const l=await ee.completeTask(a,t);if(l.success){const t=e.value.findIndex((e=>e.taskId===a));return-1!==t&&(e.value[t]=l.data),w(),b({type:"success",title:"任务完成",message:`任务 "${l.data.name}" 已完成`,taskId:a}),l.data}throw new Error(l.message||"完成任务失败")}catch(l){throw l}},batchUpdateStatus:async(e,a)=>{try{const t=e.map((e=>k(e,a)));return await Promise.all(t),b({type:"success",title:"批量状态更新成功",message:`${e.length} 个任务状态已更新为 "${a}"`}),!0}catch(t){throw t}},batchAssignTasks:async(e,a)=>{try{const t=e.map((e=>h(e,a)));return await Promise.all(t),b({type:"success",title:"批量分配成功",message:`${e.length} 个任务已成功分配`}),!0}catch(t){throw t}},batchDeleteTasks:async e=>{try{const a=e.map((e=>f(e)));return await Promise.all(a),b({type:"warning",title:"批量删除成功",message:`${e.length} 个任务已删除`}),!0}catch(a){throw a}},getTaskComments:async e=>{try{const a=await ee.getTaskComments(e);if(a.success)return a.data||[];throw new Error(a.message||"获取评论失败")}catch(a){throw a}},addComment:async(a,t,l=[])=>{try{const s=await ee.addComment(a,{content:t,mentionedUserIds:l});if(s.success){const t=e.value.findIndex((e=>e.taskId===a));return-1!==t&&e.value[t].commentCount++,s.data}throw new Error(s.message||"添加评论失败")}catch(s){throw s}},getTaskAttachments:async e=>{try{const a=await ee.getTaskAttachments(e);if(a.success)return a.data||[];throw new Error(a.message||"获取附件失败")}catch(a){throw a}},addAttachment:async(a,t,l="")=>{try{const s=new FormData;s.append("file",t),l&&s.append("description",l);const n=await ee.addAttachment(a,s);if(n.success){const t=e.value.findIndex((e=>e.taskId===a));return-1!==t&&e.value[t].attachmentCount++,n.data}throw new Error(n.message||"上传附件失败")}catch(s){throw s}},deleteAttachment:async e=>{try{const a=await ee.deleteAttachment(e);if(a.success)return!0;throw new Error(a.message||"删除附件失败")}catch(a){throw a}},getTaskHistory:async e=>{try{const a=await ee.getTaskHistory(e);if(a.success)return a.data||[];throw new Error(a.message||"获取历史记录失败")}catch(a){throw a}},getPeriodicSchedules:async(e={})=>{var a;try{const t=await ee.getPeriodicSchedules(e);if(t.success)return s.value=(null==(a=t.data)?void 0:a.items)||[],t.data;throw new Error(t.message||"获取周期性任务计划失败")}catch(t){throw t}},createPeriodicSchedule:async e=>{try{const a=await ee.createPeriodicSchedule(e);if(a.success)return s.value.unshift(a.data),a.data;throw new Error(a.message||"创建周期性任务计划失败")}catch(a){throw a}},addNotification:b,markNotificationAsRead:e=>{const a=i.value.find((a=>a.id===e));a&&(a.read=!0)},clearAllNotifications:()=>{i.value=[]},setFilters:e=>{u.value={...u.value,...e}},clearFilters:I,setSearchQuery:e=>{d.value=e},setPagination:e=>{c.value={...c.value,...e}},$reset:()=>{e.value=[],s.value=[],n.value={total:0,completed:0,inProgress:0,overdue:0,todo:0},r.value=!1,o.value=null,i.value=[],I(),d.value="",c.value={pageNumber:1,pageSize:20,total:0}}}})),te={name:"EnhancedTaskCard",components:{More:f,Edit:y,User:g,DocumentCopy:v,Delete:m,Check:p,VideoPlay:c,VideoPause:d,Calendar:u,Flag:i,Paperclip:o,ChatLineRound:r,Medal:n,UserAvatar:G,UserAvatarStack:Y},props:{task:{type:Object,required:!0},selected:{type:Boolean,default:!1},selectable:{type:Boolean,default:!0},maxCollaboratorsVisible:{type:Number,default:3}},emits:["click","select","quickAction","statusChange"],setup(e,{emit:a}){const l=t((()=>e.task.collaborators?e.task.collaborators:e.task.assignees?e.task.assignees.filter((e=>"Primary"!==e.role)):[])),s=t((()=>l.value.slice(0,e.maxCollaboratorsVisible))),n=t((()=>Math.max(0,l.value.length-e.maxCollaboratorsVisible))),r=t((()=>!(!e.task.planEndDate||"Done"===e.task.status)&&new Date(e.task.planEndDate)<new Date)),o=t((()=>{if(!e.task.planEndDate||"Done"===e.task.status)return!1;const a=new Date(e.task.planEndDate),t=new Date,l=Math.ceil((a-t)/864e5);return l<=2&&l>=0}));return{collaborators:l,visibleCollaborators:s,hiddenCollaboratorsCount:n,isOverdue:r,isDueSoon:o,formatDueDate:e=>X(e),truncateText:(e,a)=>!e||e.length<=a?e:e.substring(0,a)+"...",getPriorityTagType:e=>({Low:"info",Medium:"warning",High:"danger"}[e]||"info"),getPriorityText:e=>({Low:"低",Medium:"中",High:"高"}[e]||e),getTaskTypeText:e=>({Periodic:"周期任务",Emergency:"紧急任务",Maintenance:"维护任务"}[e]||e),getProgressColor:e=>e>=80?"#67c23a":e>=50?"#e6a23c":e>=20?"#409eff":"#f56c6c",handleQuickAction:t=>{a("quickAction",{action:t,task:e.task})},quickStatusChange:t=>{a("statusChange",{taskId:e.task.taskId,oldStatus:e.task.status,newStatus:t,task:e.task})},getAllAssignees:e=>{var a,t;if(!e)return[];const s=[],n=new Set;if(e.assigneeUserId){const l=e.assigneeAvatarUrl||e.assigneeAvatar||(null==(a=e.assignee)?void 0:a.avatarUrl)||(null==(t=e.assignee)?void 0:t.avatar)||"";s.push({id:e.assigneeUserId,userId:e.assigneeUserId,name:e.assigneeUserName||"未知用户",userName:e.assigneeUserName||"未知用户",avatarUrl:l,role:"Primary"}),n.add(e.assigneeUserId)}return e.assignees&&Array.isArray(e.assignees)&&e.assignees.length>0?e.assignees.forEach((a=>{var t,l;if(!a)return;const r=a.userId||a.id;if(!r||n.has(r))return;const o=a.avatarUrl||a.avatar||(null==(t=a.user)?void 0:t.avatarUrl)||(null==(l=a.user)?void 0:l.avatar)||"";s.push({id:r,userId:r,name:a.userName||a.name||"未知用户",userName:a.userName||a.name||"未知用户",avatarUrl:o,role:a.role||(r===e.assigneeUserId?"Primary":"Collaborator")}),n.add(r)})):l.value&&l.value.length>0?l.value.forEach((e=>{var a,t;if(!e)return;const l=e.userId||e.id;if(!l||n.has(l))return;const r=e.avatarUrl||e.avatar||e.userAvatarUrl||(null==(a=e.user)?void 0:a.avatarUrl)||(null==(t=e.user)?void 0:t.avatar)||"";s.push({id:l,userId:l,name:e.userName||e.name||"未知用户",userName:e.userName||e.name||"未知用户",avatarUrl:r,role:"Collaborator"}),n.add(l)})):e.participants&&Array.isArray(e.participants)&&e.participants.length>0&&e.participants.forEach((a=>{var t,l;if(!a)return;const r=a.id||a.userId;if(!r||n.has(r)||r===e.assigneeUserId)return;const o=a.avatarUrl||a.avatar||(null==(t=a.user)?void 0:t.avatarUrl)||(null==(l=a.user)?void 0:l.avatar)||"";s.push({id:r,userId:r,name:a.name||a.userName||"未知用户",userName:a.name||a.userName||"未知用户",avatarUrl:o,role:"Collaborator"}),n.add(r)})),s.length,s}}}},le={class:"task-header"},se={class:"task-title-row"},ne=["title"],re={class:"task-actions"},oe={key:0,class:"task-description"},ie={class:"assignee-section"},ue={key:1,class:"task-progress"},de={class:"progress-header"},ce={class:"progress-value"},pe={class:"task-meta"},me={class:"meta-row"},ve={key:0,class:"due-date"},ge={key:1,class:"task-type"},ye={class:"engagement-stats"},fe={key:0,class:"stat-item"},ke={key:1,class:"stat-item"},he={key:2,class:"stat-item points"},we={class:"quick-status-actions"};const be=s(te,[["render",function(e,a,t,l,s,n){var r,o;const i=k("el-checkbox"),u=k("el-tag"),d=k("More"),c=k("el-icon"),p=k("el-button"),m=k("Edit"),v=k("el-dropdown-item"),g=k("User"),y=k("DocumentCopy"),f=k("Delete"),E=k("el-dropdown-menu"),x=k("el-dropdown"),A=k("UserAvatarStack"),N=k("el-progress"),P=k("Calendar"),z=k("Flag"),L=k("Paperclip"),M=k("ChatLineRound"),B=k("Medal"),$=k("Check"),F=k("VideoPlay"),O=k("VideoPause"),H=k("el-button-group");return w(),h("div",{class:C(["enhanced-task-card",[`priority-${null==(r=t.task.priority)?void 0:r.toLowerCase()}`,`status-${null==(o=t.task.status)?void 0:o.toLowerCase()}`,{selected:t.selected,overdue:l.isOverdue,"due-soon":l.isDueSoon}]]),onClick:a[5]||(a[5]=a=>e.$emit("click",t.task))},[t.selectable?(w(),h("div",{key:0,class:"task-select",onClick:a[1]||(a[1]=_((()=>{}),["stop"]))},[V(i,{"model-value":t.selected,onChange:a[0]||(a[0]=a=>e.$emit("select",t.task.taskId,a))},null,8,["model-value"])])):b("",!0),I("div",le,[I("div",se,[I("h4",{class:"task-title",title:t.task.name},U(t.task.name),9,ne),I("div",re,[V(u,{type:l.getPriorityTagType(t.task.priority),size:"small",class:"priority-tag"},{default:D((()=>[T(U(l.getPriorityText(t.task.priority)),1)])),_:1},8,["type"]),V(x,{onCommand:l.handleQuickAction,trigger:"click"},{dropdown:D((()=>[V(E,null,{default:D((()=>[V(v,{command:"edit"},{default:D((()=>[V(c,null,{default:D((()=>[V(m)])),_:1}),a[6]||(a[6]=T(" 编辑 "))])),_:1}),V(v,{command:"assign"},{default:D((()=>[V(c,null,{default:D((()=>[V(g)])),_:1}),a[7]||(a[7]=T(" 重新分配 "))])),_:1}),V(v,{command:"clone"},{default:D((()=>[V(c,null,{default:D((()=>[V(y)])),_:1}),a[8]||(a[8]=T(" 克隆任务 "))])),_:1}),V(v,{command:"delete",divided:""},{default:D((()=>[V(c,null,{default:D((()=>[V(f)])),_:1}),a[9]||(a[9]=T(" 删除 "))])),_:1})])),_:1})])),default:D((()=>[V(p,{type:"text",size:"small",class:"action-btn"},{default:D((()=>[V(c,null,{default:D((()=>[V(d)])),_:1})])),_:1})])),_:1},8,["onCommand"])])]),t.task.description?(w(),h("p",oe,U(l.truncateText(t.task.description,80)),1)):b("",!0)]),I("div",ie,[V(A,{users:l.getAllAssignees(t.task),"is-main-user-primary":!0,"max-users":4,"avatar-size":"8",overlap:-12,class:"small"},null,8,["users"])]),void 0!==t.task.progress?(w(),h("div",ue,[I("div",de,[a[10]||(a[10]=I("span",{class:"progress-label"},"进度",-1)),I("span",ce,U(t.task.progress)+"%",1)]),V(N,{percentage:t.task.progress,"stroke-width":4,"show-text":!1,color:l.getProgressColor(t.task.progress)},null,8,["percentage","color"])])):b("",!0),I("div",pe,[I("div",me,[t.task.planEndDate?(w(),h("div",ve,[V(c,null,{default:D((()=>[V(P)])),_:1}),I("span",{class:C({"overdue-text":l.isOverdue,"due-soon-text":l.isDueSoon})},U(l.formatDueDate(t.task.planEndDate)),3)])):b("",!0),t.task.taskType&&"Normal"!==t.task.taskType?(w(),h("div",ge,[V(c,null,{default:D((()=>[V(z)])),_:1}),I("span",null,U(l.getTaskTypeText(t.task.taskType)),1)])):b("",!0)]),I("div",ye,[t.task.attachmentCount>0?(w(),h("div",fe,[V(c,null,{default:D((()=>[V(L)])),_:1}),I("span",null,U(t.task.attachmentCount),1)])):b("",!0),t.task.commentCount>0?(w(),h("div",ke,[V(c,null,{default:D((()=>[V(M)])),_:1}),I("span",null,U(t.task.commentCount),1)])):b("",!0),t.task.points>0?(w(),h("div",he,[V(c,null,{default:D((()=>[V(B)])),_:1}),I("span",null,U(t.task.points),1)])):b("",!0)])]),I("div",we,[V(H,{size:"small"},{default:D((()=>["Done"!==t.task.status?(w(),S(p,{key:0,onClick:a[2]||(a[2]=_((e=>l.quickStatusChange("Done")),["stop"])),type:"success",size:"small"},{default:D((()=>[V(c,null,{default:D((()=>[V($)])),_:1}),a[11]||(a[11]=T(" 完成 "))])),_:1})):b("",!0),"Todo"===t.task.status?(w(),S(p,{key:1,onClick:a[3]||(a[3]=_((e=>l.quickStatusChange("InProgress")),["stop"])),type:"primary",size:"small"},{default:D((()=>[V(c,null,{default:D((()=>[V(F)])),_:1}),a[12]||(a[12]=T(" 开始 "))])),_:1})):b("",!0),"InProgress"===t.task.status?(w(),S(p,{key:2,onClick:a[4]||(a[4]=_((e=>l.quickStatusChange("Todo")),["stop"])),type:"warning",size:"small"},{default:D((()=>[V(c,null,{default:D((()=>[V(O)])),_:1}),a[13]||(a[13]=T(" 暂停 "))])),_:1})):b("",!0)])),_:1})])],2)}],["__scopeId","data-v-9700241d"]]),Ie={name:"TaskSelect",props:{modelValue:{type:[String,Number,Array],default:null},multiple:{type:Boolean,default:!1},placeholder:{type:String,default:"请选择任务"},excludeCompleted:{type:Boolean,default:!1}},emits:["update:modelValue","change"],setup(e,{emit:t}){const l=a(e.modelValue),s=a([]),n=a(!1);return A((()=>e.modelValue),(e=>{l.value=e})),{selectedValue:l,tasks:s,loading:n,searchTasks:async a=>{var t;if(a){n.value=!0;try{const l={search:a,pageSize:20};e.excludeCompleted&&(l.excludeStatus="Completed");const n=await ee.getTasks(l);s.value=(null==(t=n.data)?void 0:t.items)||[]}catch(l){s.value=[]}finally{n.value=!1}}else s.value=[]},handleChange:e=>{t("update:modelValue",e),t("change",e)},getPriorityType:e=>({High:"danger",Medium:"warning",Low:"success"}[e]||"info"),getPriorityLabel:e=>({High:"高",Medium:"中",Low:"低"}[e]||e),getStatusType:e=>({Pending:"info",InProgress:"warning",Completed:"success",Cancelled:"danger"}[e]||"info"),getStatusLabel:e=>({Pending:"待处理",InProgress:"进行中",Completed:"已完成",Cancelled:"已取消"}[e]||e),formatDate:e=>e?new Date(e).toLocaleDateString("zh-CN"):""}}},_e={class:"task-option"},Ve={class:"task-info"},Ue={class:"task-title"},De={class:"task-meta"},Te={key:0,class:"task-assignee"},Ce={key:1,class:"task-date"};const Se=s(Ie,[["render",function(e,a,t,l,s,n){const r=k("el-tag"),o=k("el-option"),i=k("el-select");return w(),S(i,{modelValue:l.selectedValue,"onUpdate:modelValue":a[0]||(a[0]=e=>l.selectedValue=e),placeholder:t.placeholder,multiple:t.multiple,filterable:!0,remote:!0,"remote-method":l.searchTasks,loading:l.loading,clearable:!0,onChange:l.handleChange},{default:D((()=>[(w(!0),h(E,null,x(l.tasks,(e=>(w(),S(o,{key:e.id,label:e.title,value:e.id},{default:D((()=>[I("div",_e,[I("div",Ve,[I("span",Ue,U(e.title),1),V(r,{type:l.getPriorityType(e.priority),size:"small"},{default:D((()=>[T(U(l.getPriorityLabel(e.priority)),1)])),_:2},1032,["type"]),V(r,{type:l.getStatusType(e.status),size:"small"},{default:D((()=>[T(U(l.getStatusLabel(e.status)),1)])),_:2},1032,["type"])]),I("div",De,[e.assigneeName?(w(),h("span",Te,U(e.assigneeName),1)):b("",!0),e.dueDate?(w(),h("span",Ce,U(l.formatDate(e.dueDate)),1)):b("",!0)])])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue","placeholder","multiple","remote-method","loading","onChange"])}],["__scopeId","data-v-d1e68401"]]),Ee={class:"tag-input"},xe={class:"tags"},Ae=["onClick"],Ne=s({__name:"TagInput",props:{modelValue:{type:Array,default:()=>[]}},emits:["update:modelValue"],setup(e,{emit:t}){const l=e,s=t,n=a("");function r(){const e=n.value.trim();e&&!l.modelValue.includes(e)&&s("update:modelValue",[...l.modelValue,e]),n.value=""}return(a,t)=>(w(),h("div",Ee,[I("div",xe,[(w(!0),h(E,null,x(e.modelValue,((e,a)=>(w(),h("span",{key:a,class:"tag"},[T(U(e)+" ",1),I("span",{class:"remove",onClick:e=>function(e){const a=[...l.modelValue];a.splice(e,1),s("update:modelValue",a)}(a)},"×",8,Ae)])))),128)),N(I("input",{"onUpdate:modelValue":t[0]||(t[0]=e=>n.value=e),onKeyup:z(r,["enter"]),onBlur:r,placeholder:"添加标签",class:"tag-input-box"},null,544),[[P,n.value]])])]))}},[["__scopeId","data-v-6a2723e7"]]),Pe={class:"task-comments"},ze={class:"comments-header"},Le={key:0,class:"comments-list"},Me={class:"comment-user"},Be={class:"comment-meta"},$e={class:"comment-header"},Fe={class:"user-name"},Oe={class:"comment-time"},He={class:"comment-content"},je={key:0,class:"edit-indicator"},qe={key:1,class:"empty-comments"},Re={class:"add-comment"},Ke={class:"comment-actions"},Qe=s({__name:"TaskComments",props:{taskId:{type:[String,Number],required:!0},comments:{type:Array,default:()=>[]}},emits:["add-comment","load-comments"],setup(e,{emit:s}){const n=e,r=s,o=a(""),i=a(!1),u=t((()=>n.comments||[])),d=e=>{if(!e)return"";const a=new Date(e),t=new Date-a;return t<6e4?"刚刚":t<36e5?`${Math.floor(t/6e4)}分钟前`:t<864e5?`${Math.floor(t/36e5)}小时前`:a.toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})};async function c(){if(o.value.trim()){i.value=!0;try{await ee.addComment(n.taskId,{content:o.value}),r("add-comment",{content:o.value}),o.value="",l.success("评论发表成功")}catch(e){l.error("评论发表失败: "+(e.message||"未知错误"))}finally{i.value=!1}}else l.warning("请输入评论内容")}return A((()=>n.taskId),(async function(){if(n.taskId)try{r("load-comments")}catch(e){}}),{immediate:!0}),(e,a)=>{const t=k("el-avatar"),l=k("el-input"),s=k("el-button");return w(),h("div",Pe,[I("div",ze,[I("h4",null,"评论 ("+U(u.value.length)+")",1)]),u.value.length?(w(),h("div",Le,[(w(!0),h(E,null,x(u.value,(e=>(w(),h("div",{key:e.commentId,class:"comment-item"},[I("div",Me,[V(t,{src:e.userAvatarUrl,size:32,class:"user-avatar"},{default:D((()=>[T(U(e.userName?e.userName.charAt(0).toUpperCase():"?"),1)])),_:2},1032,["src"]),I("div",Be,[I("div",$e,[I("span",Fe,U(e.userName||"未知用户"),1),I("span",Oe,U(d(e.creationTimestamp)),1)])])]),I("div",He,U(e.content),1),e.isEdited?(w(),h("div",je,"已编辑")):b("",!0)])))),128))])):(w(),h("div",qe,a[1]||(a[1]=[I("div",{class:"empty-icon"},"💬",-1),I("div",{class:"empty-text"},"暂无评论",-1)]))),I("div",Re,[V(l,{modelValue:o.value,"onUpdate:modelValue":a[0]||(a[0]=e=>o.value=e),type:"textarea",rows:3,placeholder:"写下你的评论...",maxlength:"2000","show-word-limit":"",onKeyup:z(_(c,["ctrl"]),["enter"])},null,8,["modelValue","onKeyup"]),I("div",Ke,[V(s,{type:"primary",size:"small",onClick:c,loading:i.value},{default:D((()=>a[2]||(a[2]=[T(" 发表评论 ")]))),_:1},8,["loading"]),a[3]||(a[3]=I("div",{class:"comment-tip"},"Ctrl + Enter 快速发送",-1))])])])}}},[["__scopeId","data-v-3a2e032e"]]),We={name:"FileUpload",components:{Plus:B,UploadFilled:M,Document:L},props:{modelValue:{type:Array,default:()=>[]},multiple:{type:Boolean,default:!0},accept:{type:String,default:"*"},limit:{type:Number,default:10},maxSize:{type:Number,default:10485760},autoUpload:{type:Boolean,default:!0},showFileList:{type:Boolean,default:!1},drag:{type:Boolean,default:!1},buttonText:{type:String,default:"选择文件"},tipText:{type:String,default:""},uploadPath:{type:String,default:"tasks"}},emits:["update:modelValue","change","upload-success","upload-error"],setup(e,{emit:s}){const n=O(),r=a(),o=a(!1),i=a([]),u=t((()=>"/api/files/upload")),d=t((()=>({Authorization:`Bearer ${n.token}`}))),c=t((()=>({path:e.uploadPath}))),p=t((()=>!!e.tipText)),m=t((()=>e.modelValue||[])),v=e=>{if(!e)return"0 B";const a=["B","KB","MB","GB"];let t=0;for(;e>=1024&&t<a.length-1;)e/=1024,t++;return`${e.toFixed(1)} ${a[t]}`};return A((()=>e.modelValue),(e=>{i.value=(e||[]).map((e=>({name:e.name,url:e.url,uid:e.uid||e.id})))}),{immediate:!0}),{uploadRef:r,uploading:o,fileList:i,uploadUrl:u,uploadHeaders:d,uploadData:c,showTip:p,previewFiles:m,beforeUpload:a=>a.size>e.maxSize?(l.error(`文件大小不能超过 ${v(e.maxSize)}`),!1):(o.value=!0,!0),onSuccess:(a,t)=>{var n,r;o.value=!1;const i={id:(null==(n=a.data)?void 0:n.id)||Date.now(),name:t.name,size:t.size,url:null==(r=a.data)?void 0:r.url,type:t.type,uid:t.uid},u=[...e.modelValue||[],i];s("update:modelValue",u),s("change",u),s("upload-success",i,a),l.success("文件上传成功")},onError:(e,a)=>{o.value=!1,s("upload-error",e,a),l.error("文件上传失败")},onRemove:a=>{const t=(e.modelValue||[]).filter((e=>e.uid!==a.uid));s("update:modelValue",t),s("change",t)},onExceed:()=>{l.warning(`最多只能上传 ${e.limit} 个文件`)},removeFile:a=>{const t=(e.modelValue||[]).filter((e=>e.id&&e.id!==a.id||e.uid&&e.uid!==a.uid));s("update:modelValue",t),s("change",t)},previewFile:e=>{e.url&&window.open(e.url,"_blank")},downloadFile:e=>{if(e.url){const a=document.createElement("a");a.href=e.url,a.download=e.name,a.click()}},formatFileSize:v}}},Ye={class:"file-upload"},Ge={class:"el-upload__tip"},Xe={key:0,class:"file-preview"},Ze={class:"file-info"},Je={class:"file-name"},ea={class:"file-size"},aa={class:"file-actions"};const ta=s(We,[["render",function(e,a,t,l,s,n){const r=k("upload-filled"),o=k("el-icon"),i=k("plus"),u=k("el-button"),d=k("el-upload"),c=k("document");return w(),h("div",Ye,[V(d,{ref:"uploadRef",action:l.uploadUrl,headers:l.uploadHeaders,data:l.uploadData,multiple:t.multiple,accept:t.accept,limit:t.limit,"file-list":l.fileList,"before-upload":l.beforeUpload,"on-success":l.onSuccess,"on-error":l.onError,"on-remove":l.onRemove,"on-exceed":l.onExceed,"auto-upload":t.autoUpload,"show-file-list":t.showFileList,drag:t.drag,class:"upload-component"},$({default:D((()=>[t.drag?(w(),h(E,{key:0},[V(o,{class:"el-icon--upload"},{default:D((()=>[V(r)])),_:1}),a[0]||(a[0]=I("div",{class:"el-upload__text"},[T(" 将文件拖拽到此处，或"),I("em",null,"点击上传")],-1))],64)):(w(),S(u,{key:1,type:"primary",loading:l.uploading},{default:D((()=>[V(o,null,{default:D((()=>[V(i)])),_:1}),T(" "+U(t.buttonText),1)])),_:1},8,["loading"]))])),_:2},[l.showTip?{name:"tip",fn:D((()=>[I("div",Ge,[F(e.$slots,"tip",{},(()=>[T(U(t.tipText),1)]),!0)])])),key:"0"}:void 0]),1032,["action","headers","data","multiple","accept","limit","file-list","before-upload","on-success","on-error","on-remove","on-exceed","auto-upload","show-file-list","drag"]),l.previewFiles.length>0?(w(),h("div",Xe,[(w(!0),h(E,null,x(l.previewFiles,(e=>(w(),h("div",{key:e.uid||e.id,class:"file-item"},[I("div",Ze,[V(o,{class:"file-icon"},{default:D((()=>[V(c)])),_:1}),I("span",Je,U(e.name),1),I("span",ea,U(l.formatFileSize(e.size)),1)]),I("div",aa,[e.url?(w(),S(u,{key:0,type:"text",size:"small",onClick:a=>l.previewFile(e)},{default:D((()=>a[1]||(a[1]=[T(" 预览 ")]))),_:2},1032,["onClick"])):b("",!0),e.url?(w(),S(u,{key:1,type:"text",size:"small",onClick:a=>l.downloadFile(e)},{default:D((()=>a[2]||(a[2]=[T(" 下载 ")]))),_:2},1032,["onClick"])):b("",!0),V(u,{type:"text",size:"small",onClick:a=>l.removeFile(e)},{default:D((()=>a[3]||(a[3]=[T(" 删除 ")]))),_:2},1032,["onClick"])])])))),128))])):b("",!0)])}],["__scopeId","data-v-bc4ec572"]]),la={class:"task-attachments"},sa={key:0,class:"empty"},na=s({__name:"TaskAttachments",props:{taskId:{type:[String,Number],required:!0}},setup(e){const t=e,l=a([]);return A((()=>t.taskId),(e=>{l.value=[]})),(a,t)=>(w(),h("div",la,[t[1]||(t[1]=I("h3",null,"任务附件",-1)),V(ta,{modelValue:l.value,"onUpdate:modelValue":t[0]||(t[0]=e=>l.value=e),uploadPath:`tasks/${e.taskId}/attachments`,"show-file-list":!0,multiple:!0,"button-text":"上传附件","tip-text":"支持多文件上传，单个文件不超过10MB"},null,8,["modelValue","uploadPath"]),0===l.value.length?(w(),h("div",sa,"暂无附件")):b("",!0)]))}},[["__scopeId","data-v-0a3a4afd"]]),ra={class:"task-history"},oa={class:"history-header"},ia={key:0,class:"history-list"},ua={class:"history-user"},da={class:"history-meta"},ca={class:"history-info"},pa={class:"user-name"},ma={class:"action-type"},va={class:"history-time"},ga={class:"history-description"},ya={key:0,class:"field-changes"},fa={class:"field-name"},ka={key:0,class:"old-value"},ha={key:1,class:"arrow"},wa={key:2,class:"new-value"},ba={key:1,class:"empty-history"},Ia=s({__name:"TaskHistory",props:{taskId:{type:[String,Number],required:!0},history:{type:Array,default:()=>[]}},emits:["load-history"],setup(e,{emit:a}){const l=e,s=e=>{if(!e)return"";const a=new Date(e),t=new Date-a;return t<6e4?"刚刚":t<36e5?`${Math.floor(t/6e4)}分钟前`:t<864e5?`${Math.floor(t/36e5)}小时前`:a.toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})},n=t((()=>l.history||[]));return(e,a)=>{const t=k("el-avatar");return w(),h("div",ra,[I("div",oa,[I("h4",null,"操作历史 ("+U(n.value.length)+")",1)]),n.value.length?(w(),h("div",ia,[(w(!0),h(E,null,x(n.value,(e=>(w(),h("div",{key:e.taskHistoryId,class:"history-item"},[I("div",ua,[V(t,{src:e.userAvatarUrl,size:32,class:"user-avatar"},{default:D((()=>[T(U(e.userName?e.userName.charAt(0).toUpperCase():"?"),1)])),_:2},1032,["src"]),I("div",da,[I("div",ca,[I("span",pa,U(e.userName||"系统"),1),I("span",ma,U(e.formattedActionType||e.actionType),1),I("span",va,U(s(e.timestamp)),1)]),I("div",ga,U(e.description),1),e.fieldName?(w(),h("div",ya,[I("span",fa,U(e.fieldName)+":",1),e.oldValue?(w(),h("span",ka,U(e.oldValue),1)):b("",!0),e.oldValue&&e.newValue?(w(),h("span",ha," → ")):b("",!0),e.newValue?(w(),h("span",wa,U(e.newValue),1)):b("",!0)])):b("",!0)])])])))),128))])):(w(),h("div",ba,a[0]||(a[0]=[I("div",{class:"empty-icon"},"📋",-1),I("div",{class:"empty-text"},"暂无历史记录",-1)])))])}}},[["__scopeId","data-v-1d006189"]]),_a={key:0,class:"loading-container"},Va={key:1,class:"task-detail-content"},Ua={class:"task-header"},Da={class:"task-title-section"},Ta={class:"task-meta-info"},Ca={key:0,class:"pdca-badge"},Sa={key:1,class:"overdue-indicator"},Ea={class:"quick-actions"},xa={class:"task-main-content"},Aa={class:"task-details-panel"},Na={class:"task-details"},Pa={class:"detail-section"},za={key:0,class:"description-display"},La=["innerHTML"],Ma={class:"detail-section"},Ba={class:"relation-grid"},$a={class:"relation-item"},Fa={key:0},Oa={key:1,class:"empty-value"},Ha={class:"relation-item"},ja={class:"detail-section"},qa={class:"time-grid"},Ra={class:"time-item"},Ka={key:0},Qa={class:"time-item"},Wa={class:"time-item"},Ya={class:"time-item"},Ga={class:"detail-section"},Xa={class:"progress-section"},Za={class:"progress-display"},Ja={class:"progress-controls"},et={class:"progress-text"},at={key:0,class:"points-display"},tt={class:"task-properties-panel"},lt={class:"properties-card"},st={class:"property-item"},nt={key:0,class:"property-value"},rt={key:1,class:"empty-value"},ot={class:"property-item"},it={key:0,class:"property-value"},ut={class:"property-item"},dt={key:0,class:"property-value"},ct={class:"property-item"},pt={key:0,class:"property-value"},mt={class:"property-item"},vt={class:"tags-display"},gt={class:"creation-info"},yt={class:"info-item"},ft={class:"creator-name"},kt={class:"info-item"},ht={class:"info-item"},wt={class:"dialog-footer"},bt={class:"footer-left"},It={class:"footer-right"},_t=s({__name:"TaskDetailDialog",props:{modelValue:{type:Boolean,default:!1},task:{type:Object,default:null}},emits:["update:modelValue","save","delete","close"],setup(e,{emit:s}){const n=e,r=s,o=ae(),i=t({get:()=>n.modelValue,set:e=>r("update:modelValue",e)}),u=a(!1),d=a(!1),c=a(!1),p=a(!1),m=a("details"),v=a(null),g=H({}),y=H({}),f=a(!1),_=a(""),N=a(null),P=a([]),L=a([]),M=a([]),B=a(["Todo","InProgress","Done","Cancelled"]),$=a(["Low","Medium","High","Urgent"]),F=a(["Normal","Periodic","PDCA"]),O=t((()=>v.value&&(v.value.assigneeUserId===De()||v.value.creatorUserId===De())));A((()=>n.task),(e=>{e&&K(e)}),{immediate:!0}),A((()=>n.modelValue),(e=>{e&&n.task&&(K(n.task),pe(),me(),ve())}));const K=async e=>{if(e){u.value=!0;try{if("object"==typeof e&&e.taskId)v.value={...e};else{const a="number"==typeof e?e:e.taskId,t=await o.getTaskById(a);if(!t)return void r("close");v.value=t}Q()}catch(a){l.error("加载任务详情失败: "+a.message),v.value=null,r("close")}finally{u.value=!1}}},Q=()=>{if(!v.value)return;const e=Te(v.value).map((e=>e.userId)),a={name:v.value.name,description:v.value.description,status:v.value.status,priority:v.value.priority,taskType:v.value.taskType,assigneeUserId:v.value.assigneeUserId,assigneeUserIds:e.length>0?e:[v.value.assigneeUserId].filter(Boolean),planStartDate:v.value.planStartDate?new Date(v.value.planStartDate):null,planEndDate:v.value.planEndDate?new Date(v.value.planEndDate):null,progress:v.value.progress,parentTaskId:v.value.parentTaskId,tagList:[...v.value.tagList||[]],pdcaStage:v.value.pdcaStage,points:v.value.points};Object.assign(g,a),Object.assign(y,a)},W=()=>{p.value&&Object.assign(g,y),p.value=!p.value},X=async()=>{if(v.value&&v.value.taskId){d.value=!0;try{const e=v.value.taskId;if(0===e||"0"===e)throw new Error("任务ID不能为0");let a=null,t=[];Array.isArray(g.assigneeUserIds)&&g.assigneeUserIds.length>0&&(a=g.assigneeUserIds[0],t=g.assigneeUserIds.slice(1));const s={name:g.name,description:g.description,status:g.status,priority:g.priority,taskType:g.taskType,assigneeUserId:a,collaboratorUserIds:t,planStartDate:g.planStartDate?new Date(g.planStartDate).toISOString():null,planEndDate:g.planEndDate?new Date(g.planEndDate).toISOString():null,progress:g.progress,parentTaskId:g.parentTaskId,pdcaStage:g.pdcaStage,points:g.points,tagList:g.tagList},n=await ee.updateTask(e,s);if(!n.success)throw new Error(n.message||"保存失败");v.value=n.data,Q(),p.value=!1,l.success("任务更新成功"),r("save",n.data)}catch(e){l.error("保存失败: "+e.message)}finally{d.value=!1}}else l.error("保存失败: 任务ID不能为空")},te=async()=>{if(v.value){c.value=!0;try{const e=await o.completeTask(v.value.taskId);v.value=e,l.success("任务已完成"),r("save",e)}catch(e){l.error("完成任务失败: "+e.message)}finally{c.value=!1}}},le=async e=>{switch(e){case"duplicate":await se();break;case"convert":await ne();break;case"archive":await re();break;case"delete":await oe()}},se=async()=>{try{const e={...g,name:v.value.name+" (副本)",status:"Todo",progress:0,actualStartDate:null,actualEndDate:null};delete e.taskId,await o.createTask(e),l.success("任务复制成功")}catch(e){l.error("复制任务失败: "+e.message)}},ne=async()=>{l.info("转换为模板功能开发中...")},re=async()=>{try{await o.updateTaskStatus(v.value.taskId,"Archived"),l.success("任务已归档"),be()}catch(e){l.error("归档失败: "+e.message)}},oe=async()=>{try{await R.confirm("确定要删除这个任务吗？此操作不可恢复。","确认删除",{type:"warning"}),await o.deleteTask(v.value.taskId),l.success("任务删除成功"),r("delete",v.value.taskId),be()}catch(e){"cancel"!==e&&l.error("删除失败: "+e.message)}},ie=()=>{f.value=!0,_.value=v.value.name,q((()=>{var e;null==(e=N.value)||e.focus()}))},ue=async()=>{if(_.value.trim()&&_.value!==v.value.name)try{const e=v.value.taskId;if(!e||0===e||"0"===e)throw new Error("任务ID不能为空或为0");const a=await o.updateTask({taskId:e,name:_.value.trim()});v.value.name=a.name,l.success("标题更新成功")}catch(e){l.error("更新标题失败: "+e.message)}f.value=!1},de=()=>{f.value=!1,_.value=v.value.name},ce=async e=>{if(O.value)try{await o.updateTaskProgress(v.value.taskId,e),v.value.progress=e}catch(a){l.error("更新进度失败: "+a.message),g.progress=v.value.progress}},pe=async()=>{if(v.value)try{P.value=await o.getTaskComments(v.value.taskId)}catch(e){}},me=async()=>{if(v.value)try{L.value=await o.getTaskAttachments(v.value.taskId)}catch(e){}},ve=async()=>{var e;if(!(null==(e=n.task)?void 0:e.taskId))return;const a=await ee.getTaskHistory(n.task.taskId);M.value=a.data||[]},ge=async e=>{try{const a=await o.addComment(v.value.taskId,e.content,e.mentionedUserIds);P.value.unshift(a),v.value.commentCount++}catch(a){l.error("添加评论失败: "+a.message)}},ye=async(e,a)=>{try{const t=await o.addAttachment(v.value.taskId,e,a);L.value.unshift(t),v.value.attachmentCount++}catch(t){l.error("上传附件失败: "+t.message)}},fe=async e=>{try{await o.deleteAttachment(e),L.value=L.value.filter((a=>a.attachmentId!==e)),v.value.attachmentCount--,l.success("附件删除成功")}catch(a){l.error("删除附件失败: "+a.message)}},ke=()=>{const e=`${window.location.origin}/tasks/${v.value.taskId}`;navigator.clipboard.writeText(e).then((()=>{l.success("链接已复制到剪贴板")}))},he=()=>{v.value.parentTaskId&&r("open-task",v.value.parentTaskId)},we=()=>{r("show-subtasks",v.value.taskId)},be=()=>{p.value=!1,f.value=!1,m.value="details",r("close")},Ie=e=>({Todo:"",InProgress:"warning",Done:"success",Cancelled:"info"}[e]||""),_e=e=>({High:"danger",Medium:"warning",Low:"success",Urgent:"danger"}[e]||""),Ve=e=>({High:"高",Medium:"中",Low:"低",Urgent:"紧急"}[e]||e),Ue=e=>({Todo:"待处理",InProgress:"进行中",Done:"已完成",Cancelled:"已取消"}[e]||e),De=()=>1,Te=e=>{if(!e)return[];if(e.assignees&&e.assignees.length>0)return e.assignees.map((e=>({id:e.userId,userId:e.userId,name:e.userName||"未知用户",userName:e.userName||"未知用户",avatarUrl:e.avatarUrl||"",role:e.role||("Responsible"===e.assignmentType?"Primary":"Collaborator"),assignmentType:e.assignmentType})));const a=[];return e.assigneeUserId&&a.push({id:e.assigneeUserId,userId:e.assigneeUserId,name:e.assigneeUserName||"未知用户",userName:e.assigneeUserName||"未知用户",avatarUrl:e.assigneeAvatarUrl||"",role:"Primary"}),e.participants&&e.participants.length>0&&e.participants.forEach((t=>{t&&(t.id||t.userId)!==e.assigneeUserId&&a.push({id:t.id||t.userId,userId:t.id||t.userId,name:t.name||t.userName||"未知用户",userName:t.name||t.userName||"未知用户",avatarUrl:t.avatarUrl||"",role:"Collaborator"})})),a};return(e,a)=>{const t=k("el-skeleton"),l=k("el-tag"),s=k("WarningFilled"),n=k("el-icon"),r=k("Edit"),o=k("el-input"),y=k("Check"),A=k("el-button"),H=k("ArrowDown"),q=k("el-dropdown-item"),R=k("el-dropdown-menu"),K=k("el-dropdown"),Q=k("el-button-group"),ee=k("el-link"),ae=k("el-badge"),se=k("el-date-picker"),ne=k("el-slider"),re=k("el-progress"),oe=k("Star"),De=k("el-tab-pane"),Ce=k("el-tabs"),Ee=k("el-option"),xe=k("el-select"),Ae=k("Link"),Pe=k("el-dialog");return w(),S(Pe,{modelValue:i.value,"onUpdate:modelValue":a[13]||(a[13]=e=>i.value=e),title:p.value?"编辑任务":"任务详情",width:"70%","close-on-click-modal":!1,"destroy-on-close":!0,class:"task-detail-dialog",onClose:be},{footer:D((()=>[I("div",wt,[I("div",bt,[v.value&&v.value.taskId?(w(),S(A,{key:0,type:"info",text:"",onClick:ke},{default:D((()=>[V(n,null,{default:D((()=>[V(Ae)])),_:1}),a[45]||(a[45]=T(" 复制链接 "))])),_:1})):b("",!0)]),I("div",It,[V(A,{onClick:be},{default:D((()=>a[46]||(a[46]=[T("取消")]))),_:1}),p.value?(w(),S(A,{key:0,type:"primary",onClick:X,loading:d.value},{default:D((()=>a[47]||(a[47]=[T(" 保存更改 ")]))),_:1},8,["loading"])):b("",!0)])])])),default:D((()=>[u.value?(w(),h("div",_a,[V(t,{rows:8,animated:""})])):v.value?(w(),h("div",Va,[I("div",Ua,[I("div",Da,[I("div",Ta,[V(l,{type:Ie(v.value.status),size:"large",class:"status-tag"},{default:D((()=>[T(U(Ue(v.value.status)),1)])),_:1},8,["type"]),V(l,{type:_e(v.value.priority),size:"small",round:"",class:"priority-tag"},{default:D((()=>[T(U(Ve(v.value.priority)),1)])),_:1},8,["type"]),"PDCA"===v.value.taskType?(w(),h("span",Ca," PDCA-"+U(v.value.pdcaStage),1)):b("",!0),v.value.isOverdue?(w(),h("span",Sa,[V(n,null,{default:D((()=>[V(s)])),_:1}),a[14]||(a[14]=T(" 已逾期 "))])):b("",!0)]),f.value?(w(),S(o,{key:1,modelValue:_.value,"onUpdate:modelValue":a[0]||(a[0]=e=>_.value=e),size:"large",onBlur:ue,onKeyup:[z(ue,["enter"]),z(de,["esc"])],ref_key:"titleInput",ref:N,class:"title-input"},null,8,["modelValue"])):(w(),h("h2",{key:0,class:"task-title",onDblclick:ie},[T(U(v.value.name)+" ",1),V(n,{class:"edit-icon"},{default:D((()=>[V(r)])),_:1})],32))]),I("div",Ea,[V(Q,null,{default:D((()=>["Done"!==v.value.status?(w(),S(A,{key:0,type:"success",onClick:te,loading:c.value},{default:D((()=>[V(n,null,{default:D((()=>[V(y)])),_:1}),a[15]||(a[15]=T(" 完成任务 "))])),_:1},8,["loading"])):b("",!0),V(A,{onClick:W},{default:D((()=>[V(n,null,{default:D((()=>[V(r)])),_:1}),T(" "+U(p.value?"取消编辑":"编辑"),1)])),_:1}),V(K,{onCommand:le},{dropdown:D((()=>[V(R,null,{default:D((()=>[V(q,{command:"duplicate"},{default:D((()=>a[17]||(a[17]=[T("复制任务")]))),_:1}),V(q,{command:"convert"},{default:D((()=>a[18]||(a[18]=[T("转换为模板")]))),_:1}),V(q,{command:"archive"},{default:D((()=>a[19]||(a[19]=[T("归档")]))),_:1}),V(q,{command:"delete",divided:""},{default:D((()=>a[20]||(a[20]=[I("span",{style:{color:"var(--el-color-danger)"}},"删除任务",-1)]))),_:1})])),_:1})])),default:D((()=>[V(A,null,{default:D((()=>[a[16]||(a[16]=T(" 更多操作")),V(n,{class:"el-icon--right"},{default:D((()=>[V(H)])),_:1})])),_:1})])),_:1})])),_:1})])]),I("div",xa,[I("div",Aa,[V(Ce,{modelValue:m.value,"onUpdate:modelValue":a[7]||(a[7]=e=>m.value=e),class:"task-tabs"},{default:D((()=>[V(De,{label:"详情",name:"details"},{default:D((()=>{return[I("div",Na,[I("div",Pa,[a[21]||(a[21]=I("h4",{class:"section-title"},"任务描述",-1)),p.value?(w(),S(o,{key:1,modelValue:g.description,"onUpdate:modelValue":a[2]||(a[2]=e=>g.description=e),type:"textarea",rows:6,placeholder:"输入任务描述，支持Markdown格式...",class:"description-editor"},null,8,["modelValue"])):(w(),h("div",za,[v.value.description?(w(),h("div",{key:0,innerHTML:(t=v.value.description,(null==t?void 0:t.replace(/\n/g,"<br>"))||"")},null,8,La)):(w(),h("div",{key:1,class:"empty-description",onClick:a[1]||(a[1]=e=>p.value=!0)}," 点击添加描述... "))]))]),I("div",Ma,[a[25]||(a[25]=I("h4",{class:"section-title"},"关联信息",-1)),I("div",Ba,[I("div",$a,[a[22]||(a[22]=I("label",null,"父任务:",-1)),p.value?(w(),S(Se,{key:1,modelValue:g.parentTaskId,"onUpdate:modelValue":a[3]||(a[3]=e=>g.parentTaskId=e)},null,8,["modelValue"])):(w(),h("div",Fa,[v.value.parentTaskName?(w(),S(ee,{key:0,type:"primary",onClick:he},{default:D((()=>[T(U(v.value.parentTaskName),1)])),_:1})):(w(),h("span",Oa,"无"))]))]),I("div",Ha,[a[24]||(a[24]=I("label",null,"子任务:",-1)),I("div",null,[V(ae,{value:v.value.subTaskCount,class:"item"},{default:D((()=>[V(A,{size:"small",text:"",onClick:we},{default:D((()=>a[23]||(a[23]=[T(" 查看子任务 ")]))),_:1})])),_:1},8,["value"])])])])]),I("div",ja,[a[30]||(a[30]=I("h4",{class:"section-title"},"时间信息",-1)),I("div",qa,[I("div",Ra,[a[26]||(a[26]=I("label",null,"计划开始:",-1)),p.value?(w(),S(se,{key:1,modelValue:g.planStartDate,"onUpdate:modelValue":a[4]||(a[4]=e=>g.planStartDate=e),type:"datetime",placeholder:"选择开始时间",size:"small"},null,8,["modelValue"])):(w(),h("div",Ka,U(j(Z)(v.value.planStartDate)||"未设置"),1))]),I("div",Qa,[a[27]||(a[27]=I("label",null,"计划结束:",-1)),p.value?(w(),S(se,{key:1,modelValue:g.planEndDate,"onUpdate:modelValue":a[5]||(a[5]=e=>g.planEndDate=e),type:"datetime",placeholder:"选择结束时间",size:"small"},null,8,["modelValue"])):(w(),h("div",{key:0,class:C({"overdue-date":v.value.isOverdue})},U(j(Z)(v.value.planEndDate)||"未设置"),3))]),I("div",Wa,[a[28]||(a[28]=I("label",null,"实际开始:",-1)),I("div",null,U(j(Z)(v.value.actualStartDate)||"未开始"),1)]),I("div",Ya,[a[29]||(a[29]=I("label",null,"实际结束:",-1)),I("div",null,U(j(Z)(v.value.actualEndDate)||"未完成"),1)])])]),I("div",Ga,[a[32]||(a[32]=I("h4",{class:"section-title"},"进度跟踪",-1)),I("div",Xa,[I("div",Za,[a[31]||(a[31]=I("span",{class:"progress-label"},"完成进度:",-1)),I("div",Ja,[p.value||O.value?(w(),S(ne,{key:0,modelValue:g.progress,"onUpdate:modelValue":a[6]||(a[6]=e=>g.progress=e),disabled:!p.value&&!O.value,onChange:ce,class:"progress-slider"},null,8,["modelValue","disabled"])):(w(),S(re,{key:1,percentage:v.value.progress,"stroke-width":12,color:(e=v.value.progress,e>=80?"#67c23a":e>=50?"#e6a23c":"#f56c6c")},null,8,["percentage","color"])),I("span",et,U(v.value.progress)+"%",1)])]),v.value.points>0?(w(),h("div",at,[V(n,null,{default:D((()=>[V(oe)])),_:1}),I("span",null,"完成可获得 "+U(v.value.points)+" 积分",1)])):b("",!0)])])])];var e,t})),_:1}),V(De,{name:"comments"},{label:D((()=>[a[33]||(a[33]=T(" 评论 ")),v.value.commentCount>0?(w(),S(ae,{key:0,value:v.value.commentCount,class:"item"},null,8,["value"])):b("",!0)])),default:D((()=>[V(Qe,{"task-id":v.value.taskId,comments:P.value,onAddComment:ge,onLoadComments:pe},null,8,["task-id","comments"])])),_:1}),V(De,{name:"attachments"},{label:D((()=>[a[34]||(a[34]=T(" 附件 ")),v.value.attachmentCount>0?(w(),S(ae,{key:0,value:v.value.attachmentCount,class:"item"},null,8,["value"])):b("",!0)])),default:D((()=>[V(na,{"task-id":v.value.taskId,attachments:L.value,onUploadAttachment:ye,onDeleteAttachment:fe,onLoadAttachments:me},null,8,["task-id","attachments"])])),_:1}),V(De,{label:"历史",name:"history"},{default:D((()=>[V(Ia,{"task-id":v.value.taskId,history:M.value,onLoadHistory:ve},null,8,["task-id","history"])])),_:1})])),_:1},8,["modelValue"])]),I("div",tt,[I("div",lt,[a[44]||(a[44]=I("h4",{class:"panel-title"},"任务属性",-1)),I("div",st,[a[35]||(a[35]=I("label",{class:"property-label"},"负责人",-1)),p.value?(w(),S(J,{key:1,modelValue:g.assigneeUserIds,"onUpdate:modelValue":a[8]||(a[8]=e=>g.assigneeUserIds=e),multiple:!0},null,8,["modelValue"])):(w(),h("div",nt,[Te(v.value).length>0?(w(),S(Y,{key:0,users:Te(v.value),"is-main-user-primary":!0,"max-users":5,"show-details":!0,"avatar-size":"24",overlap:8},null,8,["users"])):(w(),h("span",rt,"未分配"))]))]),I("div",ot,[a[36]||(a[36]=I("label",{class:"property-label"},"状态",-1)),p.value?(w(),S(xe,{key:1,modelValue:g.status,"onUpdate:modelValue":a[9]||(a[9]=e=>g.status=e),size:"small"},{default:D((()=>[(w(!0),h(E,null,x(B.value,(e=>(w(),S(Ee,{key:e,label:Ue(e),value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])):(w(),h("div",it,[V(l,{type:Ie(v.value.status)},{default:D((()=>[T(U(Ue(v.value.status)),1)])),_:1},8,["type"])]))]),I("div",ut,[a[37]||(a[37]=I("label",{class:"property-label"},"优先级",-1)),p.value?(w(),S(xe,{key:1,modelValue:g.priority,"onUpdate:modelValue":a[10]||(a[10]=e=>g.priority=e),size:"small"},{default:D((()=>[(w(!0),h(E,null,x($.value,(e=>(w(),S(Ee,{key:e,label:Ve(e),value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])):(w(),h("div",dt,[V(l,{type:_e(v.value.priority),size:"small"},{default:D((()=>[T(U(Ve(v.value.priority)),1)])),_:1},8,["type"])]))]),I("div",ct,[a[38]||(a[38]=I("label",{class:"property-label"},"类型",-1)),p.value?(w(),S(xe,{key:1,modelValue:g.taskType,"onUpdate:modelValue":a[11]||(a[11]=e=>g.taskType=e),size:"small"},{default:D((()=>[(w(!0),h(E,null,x(F.value,(e=>(w(),S(Ee,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])):(w(),h("div",pt,[V(l,{type:"info",size:"small"},{default:D((()=>[T(U(v.value.taskType),1)])),_:1})]))]),I("div",mt,[a[39]||(a[39]=I("label",{class:"property-label"},"标签",-1)),I("div",vt,[p.value?(w(),S(Ne,{key:1,modelValue:g.tagList,"onUpdate:modelValue":a[12]||(a[12]=e=>g.tagList=e)},null,8,["modelValue"])):(w(!0),h(E,{key:0},x(v.value.tagList,(e=>(w(),S(l,{key:e,size:"small",closable:"",onClose:a=>(async e=>{if(!p.value)return;const a=g.tagList.indexOf(e);a>-1&&g.tagList.splice(a,1)})(e)},{default:D((()=>[T(U(e),1)])),_:2},1032,["onClose"])))),128))])]),I("div",gt,[a[43]||(a[43]=I("h4",{class:"panel-title"},"创建信息",-1)),I("div",yt,[a[40]||(a[40]=I("span",{class:"info-label"},"创建者:",-1)),V(G,{"user-id":v.value.creatorUserId,"user-name":v.value.creatorUserName,"avatar-url":v.value.creatorUserAvatarUrl,size:"small"},null,8,["user-id","user-name","avatar-url"]),I("span",ft,U(v.value.creatorUserName),1)]),I("div",kt,[a[41]||(a[41]=I("span",{class:"info-label"},"创建时间:",-1)),I("span",null,U(j(Z)(v.value.creationTimestamp)),1)]),I("div",ht,[a[42]||(a[42]=I("span",{class:"info-label"},"最后更新:",-1)),I("span",null,U(j(Z)(v.value.lastUpdatedTimestamp)),1)])])])])])])):b("",!0)])),_:1},8,["modelValue","title"])}}},[["__scopeId","data-v-d3d588e9"]]),Vt={name:"TaskFormDialog",props:{visible:{type:Boolean,default:!1},isEdit:{type:Boolean,default:!1},formData:{type:Object,default:()=>({})}},setup(e,{emit:t}){const s=a(e.visible),n=a(null),r=O(),o=a(!1),i=a([]),u=H({name:"",description:"",priority:"Medium",assigneeUserIds:[],planStartDate:"",planEndDate:"",taskType:"Normal"});A((()=>e.visible),(e=>{s.value=e})),A(s,(e=>{e||t("update:visible",!1)})),A((()=>e.formData),(a=>{(a=>{if(!a){if(!e.isEdit){const e=new Date,a=new Date;a.setMonth(e.getMonth()+1),u.planStartDate=e,u.planEndDate=a}return}const t={...a},l=[];if(t.assigneeUserId&&l.push(t.assigneeUserId),t.collaboratorUserIds&&Array.isArray(t.collaboratorUserIds))l.push(...t.collaboratorUserIds);else if(t.assignees&&Array.isArray(t.assignees)){const e=t.assignees.filter((e=>"Participant"===e.assignmentType)).map((e=>e.userId||e.id));l.push(...e)}t.assigneeUserIds=[...new Set(l)],t.planStartDate&&(t.planStartDate=new Date(t.planStartDate)),t.planEndDate&&(t.planEndDate=new Date(t.planEndDate)),Object.assign(u,t)})(a)}),{immediate:!0,deep:!0});return K((()=>{if((async()=>{o.value=!0;try{const e=await Q.getUserList();e&&e.data?i.value=e.data.map((e=>{if(!e)return null;const a={id:e.id||e.userId||e.ID||0,name:e.name||e.userName||e.username||e.displayName||"未知用户",department:e.department||e.departmentName||"",avatarUrl:e.avatarUrl||""};return a.id&&0!==a.id?a:null})).filter((e=>null!==e&&e.id>0)):i.value=[]}catch(e){l.error("获取用户列表失败，使用测试数据"),i.value=[{id:1,name:"张三",department:"部门A",avatarUrl:"https://example.com/avatar1.jpg"},{id:2,name:"李四",department:"部门B",avatarUrl:"https://example.com/avatar2.jpg"},{id:3,name:"王五",department:"部门C",avatarUrl:"https://example.com/avatar3.jpg"}]}finally{o.value=!1}})(),!e.isEdit){r.userInfo&&r.userInfo.id&&(u.assigneeUserIds=[r.userInfo.id]);const e=new Date,a=new Date;a.setMonth(e.getMonth()+1),u.planStartDate=e,u.planEndDate=a}})),{dialogVisible:s,formRef:n,form:u,rules:{name:[{required:!0,message:"请输入任务名称",trigger:"blur"}],priority:[{required:!0,message:"请选择优先级",trigger:"change"}]},users:i,loading:o,handleClose:()=>{s.value=!1,t("close")},handleSubmit:()=>{n.value&&n.value.validate((e=>{if(e){const e=u.assigneeUserIds||[],a=e.length>0?e[0]:null,l=e.length>1?e.slice(1):[],s={name:u.name,description:u.description,assigneeUserId:a,collaboratorUserIds:l,priority:u.priority,planStartDate:u.planStartDate?new Date(u.planStartDate).toISOString():null,planEndDate:u.planEndDate?new Date(u.planEndDate).toISOString():null,status:u.status||"Todo",taskType:u.taskType||"Normal",points:u.points||0};t("submit",s)}}))}}}},Ut={class:"user-option"},Dt={class:"user-dept"},Tt={slot:"footer",class:"dialog-footer"};const Ct=s(Vt,[["render",function(e,a,t,l,s,n){const r=k("el-input"),o=k("el-form-item"),i=k("el-option"),u=k("el-select"),d=k("el-avatar"),c=k("el-date-picker"),p=k("el-form"),m=k("el-button"),v=k("el-dialog");return w(),S(v,{modelValue:l.dialogVisible,"onUpdate:modelValue":a[7]||(a[7]=e=>l.dialogVisible=e),title:t.isEdit?"编辑任务":"新建任务",width:"600px",onClose:l.handleClose},{default:D((()=>[V(p,{model:l.form,rules:l.rules,ref:"formRef","label-width":"100px"},{default:D((()=>[V(o,{label:"任务名称",prop:"name"},{default:D((()=>[V(r,{modelValue:l.form.name,"onUpdate:modelValue":a[0]||(a[0]=e=>l.form.name=e),placeholder:"请输入任务名称"},null,8,["modelValue"])])),_:1}),V(o,{label:"描述",prop:"description"},{default:D((()=>[V(r,{type:"textarea",modelValue:l.form.description,"onUpdate:modelValue":a[1]||(a[1]=e=>l.form.description=e),placeholder:"请输入描述"},null,8,["modelValue"])])),_:1}),V(o,{label:"优先级",prop:"priority"},{default:D((()=>[V(u,{modelValue:l.form.priority,"onUpdate:modelValue":a[2]||(a[2]=e=>l.form.priority=e),placeholder:"请选择优先级"},{default:D((()=>[V(i,{label:"高",value:"High"}),V(i,{label:"中",value:"Medium"}),V(i,{label:"低",value:"Low"})])),_:1},8,["modelValue"])])),_:1}),V(o,{label:"负责人",prop:"assigneeUserIds"},{default:D((()=>[V(u,{modelValue:l.form.assigneeUserIds,"onUpdate:modelValue":a[3]||(a[3]=e=>l.form.assigneeUserIds=e),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",filterable:"",placeholder:"请选择负责人(多选)",loading:l.loading,style:{width:"100%"}},{default:D((()=>[(w(!0),h(E,null,x(l.users,(e=>(w(),S(i,{key:e.id,label:e.name,value:e.id},{default:D((()=>[I("div",Ut,[e.avatarUrl?(w(),S(d,{key:0,size:24,src:e.avatarUrl},{default:D((()=>[T(U(e.name.substring(0,1)),1)])),_:2},1032,["src"])):b("",!0),I("span",null,U(e.name),1),I("span",Dt,U(e.department),1)])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),V(o,{label:"计划开始",prop:"planStartDate"},{default:D((()=>[V(c,{modelValue:l.form.planStartDate,"onUpdate:modelValue":a[4]||(a[4]=e=>l.form.planStartDate=e),type:"date",placeholder:"选择日期"},null,8,["modelValue"])])),_:1}),V(o,{label:"计划结束",prop:"planEndDate"},{default:D((()=>[V(c,{modelValue:l.form.planEndDate,"onUpdate:modelValue":a[5]||(a[5]=e=>l.form.planEndDate=e),type:"date",placeholder:"选择日期"},null,8,["modelValue"])])),_:1}),V(o,{label:"任务类型",prop:"taskType"},{default:D((()=>[V(u,{modelValue:l.form.taskType,"onUpdate:modelValue":a[6]||(a[6]=e=>l.form.taskType=e),placeholder:"请选择任务类型"},{default:D((()=>[V(i,{label:"普通",value:"Normal"}),V(i,{label:"周期",value:"Periodic"}),V(i,{label:"PDCA",value:"PDCA"})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model","rules"]),I("div",Tt,[V(m,{onClick:l.handleClose},{default:D((()=>a[8]||(a[8]=[T("取消")]))),_:1},8,["onClick"]),V(m,{type:"primary",onClick:l.handleSubmit},{default:D((()=>[T(U(t.isEdit?"保存":"创建"),1)])),_:1},8,["onClick"])])])),_:1},8,["modelValue","title","onClose"])}],["__scopeId","data-v-80012505"]]),St={class:"task-preview-section"},Et={class:"section-title"},xt={class:"task-preview-list"},At={class:"task-info"},Nt={class:"task-name"},Pt={class:"current-assignee"},zt={key:1,class:"no-assignee"},Lt={key:0,class:"more-tasks"},Mt={class:"assign-section"},Bt={key:0,class:"single-assign"},$t={key:0,class:"user-preview"},Ft={class:"user-info"},Ot={class:"user-name"},Ht={class:"user-workload"},jt={key:1,class:"multiple-assign"},qt={key:0,class:"distribution-preview"},Rt={class:"distribution-list"},Kt={class:"distribution-info"},Qt={class:"user-name"},Wt={class:"task-count"},Yt={class:"advanced-options"},Gt={class:"options-content"},Xt={class:"reason-section"},Zt={key:0,class:"operation-preview"},Jt={class:"preview-content"},el={class:"preview-summary"},al={key:0,class:"notification-info"},tl={class:"dialog-footer"},ll={class:"footer-info"},sl={class:"task-count-info"},nl={class:"footer-actions"},rl=s({__name:"BatchAssignDialog",props:{modelValue:{type:Boolean,default:!1},selectedTasks:{type:Array,default:()=>[]}},emits:["update:modelValue","assign","close"],setup(e,{emit:s}){const n=e,r=s,o=O(),i=t({get:()=>n.modelValue,set:e=>r("update:modelValue",e)}),u=a("single"),d=a(null),c=a([]),p=a(!0),m=a(!0),v=a(""),g=a(!1),y=a([]),f=a(null),_=t((()=>n.selectedTasks.slice(0,5))),C=t((()=>"single"===u.value?null!==d.value:c.value.length>0)),N=t((()=>C.value));A((()=>d.value),(e=>{f.value=e?y.value.find((a=>a.id===e)):null})),A((()=>n.modelValue),(e=>{e&&(z(),P())})),K((()=>{P()}));const P=async()=>{try{y.value=await o.getUsers()}catch(e){l.error("加载用户列表失败")}},z=()=>{u.value="single",d.value=null,c.value=[],v.value="",p.value=!0,m.value=!0,f.value=null},L=async()=>{var e;if(C.value)try{await R.confirm(`确定要将 ${n.selectedTasks.length} 个任务${u.value,"分配给"}${"single"===u.value?null==(e=f.value)?void 0:e.name:c.value.length+"个用户"}吗？`,"确认批量分配",{type:"warning"}),g.value=!0;const a={taskIds:n.selectedTasks.map((e=>e.taskId)),assignMode:u.value,userIds:"single"===u.value?[d.value]:c.value,notifyAssignees:p.value,keepParticipants:m.value,reason:v.value};r("assign",a),M()}catch(a){"cancel"!==a&&l.error("批量分配失败: "+a.message)}finally{g.value=!1}else l.warning("请选择负责人")},M=()=>{r("close")},B=e=>{const a=n.selectedTasks.length,t=c.value.length,l=Math.floor(a/t);return e<a%t?l+1:l},$=e=>{const a=y.value.find((a=>a.id===e));return(null==a?void 0:a.name)||"未知用户"},F=e=>{const a=y.value.find((a=>a.id===e));return W(null==a?void 0:a.avatar)},H=e=>({High:"高",Medium:"中",Low:"低",Urgent:"紧急"}[e]||e);return(a,t)=>{const l=k("el-tag"),s=k("el-radio"),n=k("el-radio-group"),r=k("el-checkbox"),o=k("el-input"),y=k("el-collapse-item"),A=k("el-collapse"),P=k("User"),z=k("el-icon"),O=k("Bell"),j=k("el-button"),q=k("el-dialog");return w(),S(q,{modelValue:i.value,"onUpdate:modelValue":t[6]||(t[6]=e=>i.value=e),title:"批量分配任务",width:"500px","close-on-click-modal":!1,class:"batch-assign-dialog",onClose:M},{footer:D((()=>[I("div",tl,[I("div",ll,[I("span",sl," 共 "+U(e.selectedTasks.length)+" 个任务 ",1)]),I("div",nl,[V(j,{onClick:M},{default:D((()=>t[17]||(t[17]=[T("取消")]))),_:1}),V(j,{type:"primary",onClick:L,disabled:!C.value,loading:g.value},{default:D((()=>t[18]||(t[18]=[T(" 确认分配 ")]))),_:1},8,["disabled","loading"])])])])),default:D((()=>{var a;return[I("div",St,[I("h4",Et," 将要分配的任务 ("+U(e.selectedTasks.length)+"个) ",1),I("div",xt,[(w(!0),h(E,null,x(_.value,(e=>{return w(),h("div",{key:e.taskId,class:"task-preview-item"},[I("div",At,[I("span",Nt,U(e.name),1),V(l,{type:(a=e.priority,{High:"danger",Medium:"warning",Low:"success",Urgent:"danger"}[a]||""),size:"small",class:"priority-tag"},{default:D((()=>[T(U(H(e.priority)),1)])),_:2},1032,["type"])]),I("div",Pt,[t[7]||(t[7]=I("span",{class:"label"},"当前负责人:",-1)),e.assigneeUserId?(w(),S(G,{key:0,"user-id":e.assigneeUserId,"user-name":e.assigneeUserName,"avatar-url":F(e.assigneeUserId),size:"mini"},null,8,["user-id","user-name","avatar-url"])):(w(),h("span",zt,"未分配"))])]);var a})),128)),e.selectedTasks.length>5?(w(),h("div",Lt," 还有 "+U(e.selectedTasks.length-5)+" 个任务... ",1)):b("",!0)])]),I("div",Mt,[t[11]||(t[11]=I("h4",{class:"section-title"},"选择新的负责人",-1)),V(n,{modelValue:u.value,"onUpdate:modelValue":t[0]||(t[0]=e=>u.value=e),class:"assign-mode"},{default:D((()=>[V(s,{value:"single"},{default:D((()=>t[8]||(t[8]=[T("分配给单个用户")]))),_:1}),V(s,{value:"distribute"},{default:D((()=>t[9]||(t[9]=[T("平均分配给多个用户")]))),_:1})])),_:1},8,["modelValue"]),"single"===u.value?(w(),h("div",Bt,[V(J,{modelValue:d.value,"onUpdate:modelValue":t[1]||(t[1]=e=>d.value=e),placeholder:"选择负责人",filterable:!0,class:"user-select"},null,8,["modelValue"]),f.value?(w(),h("div",$t,[V(G,{"user-id":f.value.id,"user-name":f.value.name,"avatar-url":F(f.value.id),size:"small"},null,8,["user-id","user-name","avatar-url"]),I("div",Ft,[I("div",Ot,U(f.value.name),1),I("div",Ht," 当前任务: "+U(f.value.currentTasks||0)+"个 ",1)])])):b("",!0)])):(w(),h("div",jt,[V(J,{modelValue:c.value,"onUpdate:modelValue":t[2]||(t[2]=e=>c.value=e),placeholder:"选择多个负责人",multiple:!0,filterable:!0,class:"user-select"},null,8,["modelValue"]),c.value.length>0?(w(),h("div",qt,[t[10]||(t[10]=I("h5",null,"分配预览:",-1)),I("div",Rt,[(w(!0),h(E,null,x(c.value,((e,a)=>(w(),h("div",{key:e,class:"distribution-item"},[V(G,{"user-id":e,"user-name":$(e),"avatar-url":F(e),size:"small"},null,8,["user-id","user-name","avatar-url"]),I("div",Kt,[I("div",Qt,U($(e)),1),I("div",Wt," 将分配 "+U(B(a))+" 个任务 ",1)])])))),128))])])):b("",!0)]))]),I("div",Yt,[V(A,null,{default:D((()=>[V(y,{title:"高级选项",name:"advanced"},{default:D((()=>[I("div",Gt,[V(r,{modelValue:p.value,"onUpdate:modelValue":t[3]||(t[3]=e=>p.value=e)},{default:D((()=>t[12]||(t[12]=[T(" 通知新的负责人 ")]))),_:1},8,["modelValue"]),V(r,{modelValue:m.value,"onUpdate:modelValue":t[4]||(t[4]=e=>m.value=e)},{default:D((()=>t[13]||(t[13]=[T(" 保留原有参与者 ")]))),_:1},8,["modelValue"]),I("div",Xt,[t[14]||(t[14]=I("label",{class:"reason-label"},"分配原因:",-1)),V(o,{modelValue:v.value,"onUpdate:modelValue":t[5]||(t[5]=e=>v.value=e),type:"textarea",rows:2,placeholder:"输入分配原因（可选）",maxlength:"200","show-word-limit":""},null,8,["modelValue"])])])])),_:1})])),_:1})]),N.value?(w(),h("div",Zt,[t[16]||(t[16]=I("h4",{class:"section-title"},"操作预览",-1)),I("div",Jt,[I("div",el,[V(z,{class:"preview-icon"},{default:D((()=>[V(P)])),_:1}),I("span",null," 将 "+U(e.selectedTasks.length)+" 个任务 "+U((u.value,"分配给"))+" "+U("single"===u.value?null==(a=f.value)?void 0:a.name:c.value.length+"个用户"),1)]),p.value?(w(),h("div",al,[V(z,{class:"info-icon"},{default:D((()=>[V(O)])),_:1}),t[15]||(t[15]=I("span",null,"将向新负责人发送通知",-1))])):b("",!0)])])):b("",!0)]})),_:1},8,["modelValue"])}}},[["__scopeId","data-v-9da0bc33"]]),ol={class:"batch-status-dialog"},il={class:"dialog-info"},ul={class:"status-option"},dl={class:"affected-tasks"},cl={class:"task-list"},pl={class:"dialog-footer"},ml=s({__name:"BatchStatusDialog",props:{visible:{type:Boolean,default:!1},taskIds:{type:Array,default:()=>[]}},emits:["update:visible","success"],setup(e,{emit:t}){const s=e,n=t,r=a(!1),o=a(!1),i=ae(),u=H({status:"",remarks:""}),d=[{value:"Todo",label:"待办",tagType:"info"},{value:"InProgress",label:"进行中",tagType:"warning"},{value:"Done",label:"已完成",tagType:"success"},{value:"Cancelled",label:"已取消",tagType:"danger"}];A((()=>s.visible),(e=>{r.value=e,e&&c()})),A(r,(e=>{n("update:visible",e)}));const c=()=>{u.status="",u.remarks=""},p=e=>{const a=i.getTaskById(e);return a?a.name||a.title:null},m=async()=>{if(u.status){o.value=!0;try{const e=await ee.batchUpdateStatus(s.taskIds,u.status,u.remarks);e.success?(l.success("批量更新状态成功"),n("success"),r.value=!1,await i.fetchTasks()):l.error(e.message||"操作失败")}catch(e){l.error(e.message||"更新状态失败，请稍后重试")}finally{o.value=!1}}else l.warning("请选择要更新的状态")};return(a,t)=>{const l=k("el-tag"),s=k("el-option"),n=k("el-select"),i=k("el-form-item"),c=k("el-input"),v=k("el-form"),g=k("el-scrollbar"),y=k("el-button"),f=k("el-dialog");return w(),S(f,{modelValue:r.value,"onUpdate:modelValue":t[3]||(t[3]=e=>r.value=e),title:"批量更新任务状态",width:"500px","destroy-on-close":""},{footer:D((()=>[I("div",pl,[V(y,{onClick:t[2]||(t[2]=e=>r.value=!1)},{default:D((()=>t[7]||(t[7]=[T("取消")]))),_:1}),V(y,{type:"primary",onClick:m,loading:o.value},{default:D((()=>t[8]||(t[8]=[T(" 确认更新 ")]))),_:1},8,["loading"])])])),default:D((()=>[I("div",ol,[I("p",il,[t[4]||(t[4]=T(" 您选择了 ")),I("strong",null,U(e.taskIds.length),1),t[5]||(t[5]=T(" 个任务，请选择要更新的状态： "))]),V(v,{model:u,"label-width":"80px"},{default:D((()=>[V(i,{label:"状态"},{default:D((()=>[V(n,{modelValue:u.status,"onUpdate:modelValue":t[0]||(t[0]=e=>u.status=e),placeholder:"选择任务状态",style:{width:"100%"}},{default:D((()=>[(w(),h(E,null,x(d,(e=>V(s,{key:e.value,label:e.label,value:e.value},{default:D((()=>[I("div",ul,[V(l,{type:e.tagType,size:"small",effect:"light"},{default:D((()=>[T(U(e.label),1)])),_:2},1032,["type"])])])),_:2},1032,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),V(i,{label:"备注"},{default:D((()=>[V(c,{modelValue:u.remarks,"onUpdate:modelValue":t[1]||(t[1]=e=>u.remarks=e),type:"textarea",rows:3,placeholder:"请输入更新备注（可选）"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"]),I("div",dl,[t[6]||(t[6]=I("p",null,"受影响的任务：",-1)),V(g,{height:"120px"},{default:D((()=>[I("div",cl,[(w(!0),h(E,null,x(e.taskIds,(e=>(w(),h("div",{key:e,class:"task-item"},[V(l,{size:"small",effect:"plain"},{default:D((()=>[T(U(p(e)||`任务ID: ${e}`),1)])),_:2},1024)])))),128))])])),_:1})])])])),_:1},8,["modelValue"])}}},[["__scopeId","data-v-68621b98"]]);export{rl as B,be as E,_t as T,Ct as a,ml as b,ae as u};
