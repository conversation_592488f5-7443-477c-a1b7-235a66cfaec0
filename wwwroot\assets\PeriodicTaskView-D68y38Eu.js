import{aj as e,_ as l,r as a,a5 as t,c as r,p as s,Y as n,o as u,w as d,e as i,d as o,a as c,a9 as p,A as m,t as f,a8 as h,ar as y,b as v,m as g,ag as b,f as _,aT as k,a7 as V,v as w,R as D,af as T,aA as O,an as C,ao as x,a1 as U}from"./index-C7OOw0MO.js";import{U as E}from"./UserSelect-C-1BVWPu.js";const $={getPeriodicSchedules:(l={})=>e({url:"/v2/periodic-schedules",method:"get",params:l}),getPeriodicSchedule:l=>e({url:`/v2/periodic-schedules/${l}`,method:"get"}),createPeriodicSchedule:l=>e({url:"/v2/periodic-schedules",method:"post",data:l}),updatePeriodicSchedule:(l,a)=>e({url:`/v2/periodic-schedules/${l}`,method:"put",data:a}),deletePeriodicSchedule:l=>e({url:`/v2/periodic-schedules/${l}`,method:"delete"}),enablePeriodicSchedule:(l,a)=>e({url:`/v2/periodic-schedules/${l}/enable`,method:"patch",params:{isEnabled:a}})},I={class:"form-section"},M={class:"form-section"},Y={class:"form-section"},S={class:"form-section"},A={class:"dialog-footer"},P=l({__name:"PeriodicTaskDialog",props:{modelValue:{type:Boolean,default:!1},schedule:{type:Object,default:null},isEdit:{type:Boolean,default:!1}},emits:["update:modelValue","close","submit"],setup(e,{emit:l}){const v=e,g=l,b=a(),_=a(!1),k=a([]),V=t({name:"",description:"",taskTitle:"",taskDescription:"",taskType:"Regular",priority:"Medium",recurrenceType:"Daily",recurrenceInterval:1,daysOfWeek:null,dayOfMonth:null,monthOfYear:null,cronExpression:"",startDate:"",endDate:"",durationHours:1,maxOccurrences:null,defaultAssigneeUserId:[],isEnabled:!0}),w={name:[{required:!0,message:"请输入计划名称",trigger:"blur"},{min:2,max:100,message:"长度在 2 到 100 个字符",trigger:"blur"}],taskTitle:[{required:!0,message:"请输入任务标题",trigger:"blur"},{min:2,max:200,message:"长度在 2 到 200 个字符",trigger:"blur"}],taskType:[{required:!0,message:"请选择任务类型",trigger:"change"}],priority:[{required:!0,message:"请选择优先级",trigger:"change"}],recurrenceType:[{required:!0,message:"请选择重复类型",trigger:"change"}],recurrenceInterval:[{required:!0,message:"请输入重复间隔",trigger:"blur"},{type:"number",min:1,max:100,message:"间隔必须在 1 到 100 之间",trigger:"blur"}],startDate:[{required:!0,message:"请选择开始日期",trigger:"change"}],cronExpression:[{validator:(e,l,a)=>{"CustomCron"!==V.recurrenceType||l?a():a(new Error("请输入Cron表达式"))},trigger:"blur"}],daysOfWeek:[{validator:(e,l,a)=>{"Weekly"===V.recurrenceType&&0===k.value.length?a(new Error("请至少选择一个星期")):a()},trigger:"change"}]},D=r({get:()=>v.modelValue,set:e=>g("update:modelValue",e)});s((()=>v.schedule),(e=>{if(e&&v.isEdit)if(Object.assign(V,{name:e.name||"",description:e.description||"",taskTitle:e.taskTemplateTitle||e.taskTitle||"",taskDescription:e.taskTemplateDescription||e.taskDescription||"",taskType:e.taskType||"Regular",priority:e.defaultPriority||e.priority||"Medium",recurrenceType:e.recurrenceType||"Daily",recurrenceInterval:e.recurrenceInterval||1,dayOfMonth:e.dayOfMonth,monthOfYear:e.monthOfYear,cronExpression:e.cronExpression||"",startDate:e.startDate||"",endDate:e.endDate?e.endDate.split("T")[0]:"",durationHours:e.defaultDurationDays?24*e.defaultDurationDays:e.durationHours||1,maxOccurrences:e.totalOccurrences||e.maxOccurrences,defaultAssigneeUserId:e.defaultAssigneeUserIds&&e.defaultAssigneeUserIds.length>0?e.defaultAssigneeUserIds:e.defaultAssigneeUserId?[e.defaultAssigneeUserId]:[],isEnabled:"Active"===e.status&&!1!==e.isEnabled}),e.daysOfWeek)try{k.value=JSON.parse(e.daysOfWeek)}catch(l){k.value=[]}else k.value=[]}),{immediate:!0}),s((()=>v.modelValue),(e=>{e&&!v.isEdit&&T()})),s(k,(e=>{V.daysOfWeek=e.length>0?JSON.stringify(e):null}),{deep:!0});const T=()=>{Object.assign(V,{name:"",description:"",taskTitle:"",taskDescription:"",taskType:"Regular",priority:"Medium",recurrenceType:"Daily",recurrenceInterval:1,daysOfWeek:null,dayOfMonth:null,monthOfYear:null,cronExpression:"",startDate:O(),endDate:"",durationHours:1,maxOccurrences:null,defaultAssigneeUserId:[],isEnabled:!0}),k.value=[],y((()=>{var e;null==(e=b.value)||e.clearValidate()}))},O=()=>{const e=new Date;return e.setMinutes(0,0,0),e.toISOString().slice(0,19)},C=()=>{V.recurrenceInterval=1,V.daysOfWeek=null,V.dayOfMonth=null,V.monthOfYear=null,V.cronExpression="",k.value=[]},x=()=>{g("close")},U=async()=>{try{let e;await b.value.validate(),_.value=!0,e="CustomCron"===V.recurrenceType?V.cronExpression:((e,l,a,t,r,s)=>{if(!a)return"0 0 0 1 1 ? *";const n=new Date(a),u=n.getMinutes(),d=n.getHours(),i=n.getDate(),o=n.getMonth()+1;switch(e){case"Shift":return`${u} 8,20 * * *`;case"Daily":return 1===l?`${u} ${d} * * *`:`${u} ${d} */${l} * *`;case"Weekly":return t&&t.length>0?`${u} ${d} * * ${t.join(",")}`:`${u} ${d} * * ${n.getDay()}`;case"Monthly":return r?`${u} ${d} ${r} * *`:`${u} ${d} ${i} * *`;case"Quarterly":return r?`${u} ${d} ${r} 1,4,7,10 *`:`${u} ${d} 1 1,4,7,10 *`;case"Yearly":return s&&r?`${u} ${d} ${r} ${s} *`:`${u} ${d} ${i} ${o} *`;default:return"0 0 0 1 1 ? *"}})(V.recurrenceType,V.recurrenceInterval,V.startDate,V.daysOfWeek,V.dayOfMonth,V.monthOfYear);const l={name:V.name,description:V.description||null,templateTaskId:0,taskTemplateTitle:V.taskTitle,taskTemplateDescription:V.taskDescription||null,recurrenceType:V.recurrenceType,recurrenceInterval:"CustomCron"!==V.recurrenceType?V.recurrenceInterval:null,daysOfWeek:"Weekly"===V.recurrenceType?V.daysOfWeek:null,dayOfMonth:["Monthly","Yearly"].includes(V.recurrenceType)?V.dayOfMonth:null,monthOfYear:"Yearly"===V.recurrenceType?V.monthOfYear:null,cronExpression:e,startDate:V.startDate,endDate:V.endDate?`${V.endDate}T23:59:59`:null,totalOccurrences:V.maxOccurrences||null,defaultAssigneeUserId:Array.isArray(V.defaultAssigneeUserId)&&V.defaultAssigneeUserId.length>0?V.defaultAssigneeUserId[0]:null,defaultAssigneeUserIds:Array.isArray(V.defaultAssigneeUserId)&&V.defaultAssigneeUserId.length>0?V.defaultAssigneeUserId:[],defaultPriority:V.priority,defaultDurationDays:V.durationHours?Math.ceil(V.durationHours/24):1,defaultAssetId:null,defaultLocationId:null};g("submit",l)}catch(e){h.error("请检查表单填写是否正确")}finally{_.value=!1}};return(l,a)=>{const t=c("el-input"),r=c("el-form-item"),s=c("el-col"),h=c("el-row"),y=c("el-option"),v=c("el-select"),g=c("el-input-number"),T=c("el-checkbox"),O=c("el-checkbox-group"),$=c("el-date-picker"),P=c("el-form"),W=c("el-button"),z=c("el-dialog");return u(),n(z,{modelValue:D.value,"onUpdate:modelValue":a[19]||(a[19]=e=>D.value=e),title:e.isEdit?"编辑周期性任务":"创建周期性任务",width:"900px","max-height":"80vh",onClose:x,"close-on-click-modal":!1},{footer:d((()=>[o("div",A,[i(W,{onClick:x},{default:d((()=>a[38]||(a[38]=[m("取消")]))),_:1}),i(W,{type:"primary",onClick:U,loading:_.value},{default:d((()=>[m(f(e.isEdit?"保存":"创建"),1)])),_:1},8,["loading"])])])),default:d((()=>[i(P,{ref_key:"formRef",ref:b,model:V,rules:w,"label-width":"120px",class:"periodic-form"},{default:d((()=>[o("div",I,[a[20]||(a[20]=o("h4",{class:"section-title"},"📋 基本信息",-1)),i(h,{gutter:20},{default:d((()=>[i(s,{span:12},{default:d((()=>[i(r,{label:"计划名称",prop:"name"},{default:d((()=>[i(t,{modelValue:V.name,"onUpdate:modelValue":a[0]||(a[0]=e=>V.name=e),placeholder:"请输入计划名称",maxlength:"100"},null,8,["modelValue"])])),_:1})])),_:1}),i(s,{span:12},{default:d((()=>[i(r,{label:"任务标题",prop:"taskTitle"},{default:d((()=>[i(t,{modelValue:V.taskTitle,"onUpdate:modelValue":a[1]||(a[1]=e=>V.taskTitle=e),placeholder:"请输入任务标题模板",maxlength:"200"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(r,{label:"描述",prop:"description"},{default:d((()=>[i(t,{modelValue:V.description,"onUpdate:modelValue":a[2]||(a[2]=e=>V.description=e),type:"textarea",rows:2,placeholder:"请输入计划描述（可选）",maxlength:"500"},null,8,["modelValue"])])),_:1})]),o("div",M,[a[21]||(a[21]=o("h4",{class:"section-title"},"📝 任务信息",-1)),i(h,{gutter:20},{default:d((()=>[i(s,{span:8},{default:d((()=>[i(r,{label:"任务类型",prop:"taskType"},{default:d((()=>[i(v,{modelValue:V.taskType,"onUpdate:modelValue":a[3]||(a[3]=e=>V.taskType=e),placeholder:"请选择任务类型",style:{width:"100%"}},{default:d((()=>[i(y,{label:"常规任务",value:"Regular"}),i(y,{label:"维护任务",value:"Maintenance"}),i(y,{label:"检查任务",value:"Inspection"}),i(y,{label:"PDCA任务",value:"PDCA"})])),_:1},8,["modelValue"])])),_:1})])),_:1}),i(s,{span:8},{default:d((()=>[i(r,{label:"优先级",prop:"priority"},{default:d((()=>[i(v,{modelValue:V.priority,"onUpdate:modelValue":a[4]||(a[4]=e=>V.priority=e),placeholder:"请选择优先级",style:{width:"100%"}},{default:d((()=>[i(y,{label:"🔴 高",value:"High"}),i(y,{label:"🟡 中",value:"Medium"}),i(y,{label:"🟢 低",value:"Low"})])),_:1},8,["modelValue"])])),_:1})])),_:1}),i(s,{span:8},{default:d((()=>[i(r,{label:"默认负责人"},{default:d((()=>[i(E,{modelValue:V.defaultAssigneeUserId,"onUpdate:modelValue":a[5]||(a[5]=e=>V.defaultAssigneeUserId=e),placeholder:"选择默认负责人",multiple:!0,"show-workload":!1},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(r,{label:"任务描述",prop:"taskDescription"},{default:d((()=>[i(t,{modelValue:V.taskDescription,"onUpdate:modelValue":a[6]||(a[6]=e=>V.taskDescription=e),type:"textarea",rows:2,placeholder:"请输入任务描述模板（可选）",maxlength:"1000"},null,8,["modelValue"])])),_:1})]),o("div",Y,[a[30]||(a[30]=o("h4",{class:"section-title"},"🔄 重复规则",-1)),i(h,{gutter:20},{default:d((()=>[i(s,{span:12},{default:d((()=>[i(r,{label:"重复类型",prop:"recurrenceType"},{default:d((()=>[i(v,{modelValue:V.recurrenceType,"onUpdate:modelValue":a[7]||(a[7]=e=>V.recurrenceType=e),placeholder:"请选择重复类型",style:{width:"100%"},onChange:C},{default:d((()=>[i(y,{label:"每班(12小时)",value:"Shift"}),i(y,{label:"每日",value:"Daily"}),i(y,{label:"每周",value:"Weekly"}),i(y,{label:"每月",value:"Monthly"}),i(y,{label:"每季度",value:"Quarterly"}),i(y,{label:"每年",value:"Yearly"}),i(y,{label:"自定义Cron",value:"CustomCron"})])),_:1},8,["modelValue"])])),_:1})])),_:1}),"CustomCron"!==V.recurrenceType?(u(),n(s,{key:0,span:12},{default:d((()=>[i(r,{label:"重复间隔",prop:"recurrenceInterval"},{default:d((()=>[i(g,{modelValue:V.recurrenceInterval,"onUpdate:modelValue":a[8]||(a[8]=e=>V.recurrenceInterval=e),min:1,max:100,placeholder:"间隔",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})):p("",!0)])),_:1}),"Weekly"===V.recurrenceType?(u(),n(r,{key:0,label:"星期",prop:"daysOfWeek"},{default:d((()=>[i(O,{modelValue:k.value,"onUpdate:modelValue":a[9]||(a[9]=e=>k.value=e)},{default:d((()=>[i(T,{label:0},{default:d((()=>a[22]||(a[22]=[m("周日")]))),_:1}),i(T,{label:1},{default:d((()=>a[23]||(a[23]=[m("周一")]))),_:1}),i(T,{label:2},{default:d((()=>a[24]||(a[24]=[m("周二")]))),_:1}),i(T,{label:3},{default:d((()=>a[25]||(a[25]=[m("周三")]))),_:1}),i(T,{label:4},{default:d((()=>a[26]||(a[26]=[m("周四")]))),_:1}),i(T,{label:5},{default:d((()=>a[27]||(a[27]=[m("周五")]))),_:1}),i(T,{label:6},{default:d((()=>a[28]||(a[28]=[m("周六")]))),_:1})])),_:1},8,["modelValue"])])),_:1})):p("",!0),"Monthly"===V.recurrenceType?(u(),n(r,{key:1,label:"日期",prop:"dayOfMonth"},{default:d((()=>[i(g,{modelValue:V.dayOfMonth,"onUpdate:modelValue":a[10]||(a[10]=e=>V.dayOfMonth=e),min:1,max:31,placeholder:"每月第几日",style:{width:"100%"}},null,8,["modelValue"])])),_:1})):p("",!0),"Yearly"===V.recurrenceType?(u(),n(h,{key:2,gutter:20},{default:d((()=>[i(s,{span:12},{default:d((()=>[i(r,{label:"月份",prop:"monthOfYear"},{default:d((()=>[i(g,{modelValue:V.monthOfYear,"onUpdate:modelValue":a[11]||(a[11]=e=>V.monthOfYear=e),min:1,max:12,placeholder:"第几月",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),i(s,{span:12},{default:d((()=>[i(r,{label:"日期",prop:"dayOfMonth"},{default:d((()=>[i(g,{modelValue:V.dayOfMonth,"onUpdate:modelValue":a[12]||(a[12]=e=>V.dayOfMonth=e),min:1,max:31,placeholder:"第几日",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})):p("",!0),"CustomCron"===V.recurrenceType?(u(),n(r,{key:3,label:"Cron表达式",prop:"cronExpression"},{default:d((()=>[i(t,{modelValue:V.cronExpression,"onUpdate:modelValue":a[13]||(a[13]=e=>V.cronExpression=e),placeholder:"例如: 0 9 * * 1-5 (工作日上午9点)"},null,8,["modelValue"]),a[29]||(a[29]=o("div",{class:"cron-hint"},[m(" 格式: 秒 分 时 日 月 星期"),o("br"),m(" 例如: 0 9 * * 1-5 表示工作日上午9点 ")],-1))])),_:1})):p("",!0)]),o("div",S,[a[37]||(a[37]=o("h4",{class:"section-title"},"⏰ 时间设置",-1)),i(h,{gutter:20},{default:d((()=>[i(s,{span:12},{default:d((()=>[i(r,{label:"开始日期时间",prop:"startDate"},{default:d((()=>[i($,{modelValue:V.startDate,"onUpdate:modelValue":a[14]||(a[14]=e=>V.startDate=e),type:"datetime",placeholder:"选择开始日期和执行时间",format:"YYYY-MM-DD HH:mm","value-format":"YYYY-MM-DDTHH:mm:ss",style:{width:"100%"}},null,8,["modelValue"]),a[31]||(a[31]=o("div",{class:"time-hint"}," 设置任务的开始日期和每日执行时间 ",-1))])),_:1})])),_:1}),i(s,{span:12},{default:d((()=>[i(r,{label:"结束日期（可选）",prop:"endDate"},{default:d((()=>[i($,{modelValue:V.endDate,"onUpdate:modelValue":a[15]||(a[15]=e=>V.endDate=e),type:"date",placeholder:"选择结束日期（可选）",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"]),a[32]||(a[32]=o("div",{class:"time-hint"}," 不设置则永久执行 ",-1))])),_:1})])),_:1})])),_:1}),i(h,{gutter:20},{default:d((()=>[i(s,{span:8},{default:d((()=>[i(r,{label:"任务持续时间"},{default:d((()=>[i(g,{modelValue:V.durationHours,"onUpdate:modelValue":a[16]||(a[16]=e=>V.durationHours=e),min:.5,max:720,step:.5,placeholder:"小时",style:{width:"100%"}},null,8,["modelValue"]),a[33]||(a[33]=o("div",{class:"time-hint"}," 单个任务的预计持续时间 ",-1))])),_:1})])),_:1}),i(s,{span:8},{default:d((()=>[i(r,{label:"最大生成任务数"},{default:d((()=>[i(g,{modelValue:V.maxOccurrences,"onUpdate:modelValue":a[17]||(a[17]=e=>V.maxOccurrences=e),min:1,max:1e3,placeholder:"最大生成任务数量",style:{width:"100%"}},null,8,["modelValue"]),a[34]||(a[34]=o("div",{class:"time-hint"}," 限制总共生成的任务数量 ",-1))])),_:1})])),_:1}),i(s,{span:8},{default:d((()=>[i(r,null,{default:d((()=>[i(T,{modelValue:V.isEnabled,"onUpdate:modelValue":a[18]||(a[18]=e=>V.isEnabled=e)},{default:d((()=>a[35]||(a[35]=[m("创建后立即启用")]))),_:1},8,["modelValue"]),a[36]||(a[36]=o("div",{class:"time-hint"}," 启用后将按计划自动生成任务 ",-1))])),_:1})])),_:1})])),_:1})])])),_:1},8,["model"])])),_:1},8,["modelValue","title"])}}},[["__scopeId","data-v-ec68ed1e"]]),W={key:0,class:"schedule-detail"},z={class:"detail-grid"},H={class:"detail-item"},j={class:"detail-item"},q={class:"detail-item"},L={class:"detail-item"},R={class:"detail-item"},G={class:"detail-item"},N={class:"detail-item"},B={class:"detail-item"},J={class:"dialog-footer"},Q=l({__name:"PeriodicTaskDetailDialog",props:{modelValue:{type:Boolean,default:!1},schedule:{type:Object,default:null}},emits:["update:modelValue","close","edit","delete"],setup(e,{emit:l}){const a=e,t=l,s=r({get:()=>a.modelValue,set:e=>t("update:modelValue",e)}),h=()=>{t("close")},y=()=>{t("edit",a.schedule)},g=()=>{t("delete",a.schedule)},b=e=>({Active:"活动中",Paused:"已暂停",Completed:"已完成",Expired:"已过期",Error:"错误"}[e]||e),_=e=>{if(!e)return"";return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",hour12:!1})};return(l,a)=>{const t=c("el-tag"),r=c("el-button"),k=c("el-dialog");return u(),n(k,{modelValue:s.value,"onUpdate:modelValue":a[0]||(a[0]=e=>s.value=e),title:"周期性任务详情",width:"800px",onClose:h},{footer:d((()=>[o("div",J,[i(r,{onClick:h},{default:d((()=>a[9]||(a[9]=[m("关闭")]))),_:1}),i(r,{type:"primary",onClick:y},{default:d((()=>a[10]||(a[10]=[m("编辑")]))),_:1}),i(r,{type:"danger",onClick:g},{default:d((()=>a[11]||(a[11]=[m("删除")]))),_:1})])])),default:d((()=>{return[e.schedule?(u(),v("div",W,[o("div",z,[o("div",H,[a[1]||(a[1]=o("label",null,"计划名称:",-1)),o("span",null,f(e.schedule.name),1)]),o("div",j,[a[2]||(a[2]=o("label",null,"描述:",-1)),o("span",null,f(e.schedule.description||"无"),1)]),o("div",q,[a[3]||(a[3]=o("label",null,"重复类型:",-1)),o("span",null,f((r=e.schedule.recurrenceType,{Daily:"每日",Weekly:"每周",Monthly:"每月",Yearly:"每年",CustomCron:"自定义"}[r]||r)),1)]),o("div",L,[a[4]||(a[4]=o("label",null,"状态:",-1)),i(t,{type:(l=e.schedule.status,{Active:"success",Paused:"warning",Completed:"info",Expired:"danger",Error:"danger"}[l]||"")},{default:d((()=>[m(f(b(e.schedule.status)),1)])),_:1},8,["type"])]),o("div",R,[a[5]||(a[5]=o("label",null,"启用状态:",-1)),i(t,{type:e.schedule.isEnabled?"success":"info"},{default:d((()=>[m(f(e.schedule.isEnabled?"已启用":"已禁用"),1)])),_:1},8,["type"])]),o("div",G,[a[6]||(a[6]=o("label",null,"下次执行:",-1)),o("span",null,f(_(e.schedule.nextGenerationTime)||"未设置"),1)]),o("div",N,[a[7]||(a[7]=o("label",null,"已生成次数:",-1)),o("span",null,f(e.schedule.occurrencesGenerated),1)]),o("div",B,[a[8]||(a[8]=o("label",null,"创建时间:",-1)),o("span",null,f(_(e.schedule.creationTimestamp)),1)])])])):p("",!0)];var l,r})),_:1},8,["modelValue"])}}},[["__scopeId","data-v-7ab32b18"]]),K={class:"periodic-task-view"},F={class:"page-header"},X={class:"header-right"},Z={class:"card-header"},ee={class:"filter-grid"},le={class:"stats-grid"},ae={class:"stat-content"},te={class:"stat-info"},re={class:"stat-number"},se={class:"stat-content"},ne={class:"stat-info"},ue={class:"stat-number"},de={class:"stat-content"},ie={class:"stat-info"},oe={class:"stat-number"},ce={class:"table-header"},pe={class:"schedule-name-cell"},me={class:"schedule-title"},fe={key:0,class:"schedule-desc"},he={class:"recurrence-cell"},ye={class:"recurrence-header"},ve={class:"execution-time"},ge={class:"recurrence-desc"},be={key:0,class:"next-time"},_e={key:1,class:"no-time"},ke={class:"generated-count"},Ve={class:"action-buttons"},we={class:"pagination-wrapper"},De=l({__name:"PeriodicTaskView",setup(e){const l=a(!1),s=a([]),y=a(""),E=t({status:"",recurrenceType:""}),I=t({currentPage:1,pageSize:20}),M=a(!1),Y=a(!1),S=a(null),A=a(null),W=a(!1),z=r((()=>{let e=s.value||[];if(E.status&&(e=e.filter((e=>e.status===E.status))),E.recurrenceType&&(e=e.filter((e=>e.recurrenceType===E.recurrenceType))),y.value){const l=y.value.toLowerCase();e=e.filter((e=>{var a,t;return(null==(a=e.name)?void 0:a.toLowerCase().includes(l))||(null==(t=e.description)?void 0:t.toLowerCase().includes(l))}))}return e})),H=r((()=>{const e=(I.currentPage-1)*I.pageSize,l=e+I.pageSize;return z.value.slice(e,l)})),j=r((()=>{const e=s.value||[];return{total:e.length,active:e.filter((e=>"Active"===e.status&&e.isEnabled)).length,paused:e.filter((e=>"Paused"===e.status||!e.isEnabled)).length}})),q=async()=>{l.value=!0;try{const e=await $.getPeriodicSchedules({pageIndex:1,pageSize:1e3});e.success?s.value=e.data.items||[]:h.error("加载周期性任务计划失败: "+e.message)}catch(e){h.error("加载失败: "+e.message)}finally{l.value=!1}},L=()=>{I.currentPage=1},R=()=>{Object.assign(E,{status:"",recurrenceType:""}),y.value="",I.currentPage=1},G=e=>{I.pageSize=e,I.currentPage=1},N=e=>{I.currentPage=e},B=e=>{S.value={...e},W.value=!0,M.value=!0,Y.value=!1},J=async e=>{try{await U.confirm(`确定要删除周期性任务计划"${e.name}"吗？`,"确认删除",{type:"warning"});const l=await $.deletePeriodicSchedule(e.id);l.success?(h.success("删除成功"),await q()):h.error("删除失败: "+l.message)}catch(l){"cancel"!==l&&h.error("删除失败: "+l.message)}},De=()=>{M.value=!1,S.value=null,W.value=!1},Te=async e=>{var l,a;try{let l;l=W.value?await $.updatePeriodicSchedule(S.value.id,e):await $.createPeriodicSchedule(e),l.success?(h.success(W.value?"更新成功":"创建成功"),De(),await q()):h.error((W.value?"更新":"创建")+"失败: "+l.message)}catch(t){h.error("操作失败: "+((null==(a=null==(l=t.response)?void 0:l.data)?void 0:a.message)||t.message))}},Oe=e=>({Daily:"每日",Weekly:"每周",Monthly:"每月",Yearly:"每年",CustomCron:"自定义"}[e]||e),Ce=e=>({Active:"活动中",Paused:"已暂停",Completed:"已完成",Expired:"已过期",Error:"错误"}[e]||e),xe=e=>{if(!e.recurrenceType)return"";let l="";switch(e.recurrenceType){case"Daily":l=e.recurrenceInterval>1?`每${e.recurrenceInterval}天`:"每天";break;case"Weekly":if(l=e.recurrenceInterval>1?`每${e.recurrenceInterval}周`:"每周",e.daysOfWeek)try{const a=JSON.parse(e.daysOfWeek),t=["日","一","二","三","四","五","六"];l+=` (${a.map((e=>t[e])).join("、")})`}catch(a){}break;case"Monthly":l=e.recurrenceInterval>1?`每${e.recurrenceInterval}月`:"每月",e.dayOfMonth&&(l+=`${e.dayOfMonth}号`);break;case"Yearly":l="每年",e.monthOfYear&&(l+=`${e.monthOfYear}月`),e.dayOfMonth&&(l+=`${e.dayOfMonth}号`);break;case"CustomCron":l=e.cronExpression||"自定义"}return l},Ue=e=>{if(!e)return"";return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",hour12:!1})},Ee=e=>{if(!e.startDate)return"";const l=new Date(e.startDate);return`${l.getHours().toString().padStart(2,"0")}:${l.getMinutes().toString().padStart(2,"0")}`};return g((()=>{q()})),(e,a)=>{const t=c("el-icon"),r=c("el-button"),s=c("el-option"),g=c("el-select"),U=c("el-input"),$e=c("el-card"),Ie=c("el-table-column"),Me=c("el-tag"),Ye=c("el-switch"),Se=c("el-table"),Ae=c("el-pagination"),Pe=b("loading");return u(),v("div",K,[o("div",F,[a[10]||(a[10]=o("div",{class:"header-left"},[o("h1",null,"🔄 周期性任务管理"),o("p",{class:"subtitle"},"管理定期执行的任务计划")],-1)),o("div",X,[i(r,{type:"primary",onClick:a[0]||(a[0]=e=>M.value=!0)},{default:d((()=>[i(t,null,{default:d((()=>[i(_(k))])),_:1}),a[9]||(a[9]=m(" 创建周期性任务 "))])),_:1})])]),i($e,{class:"filter-card",shadow:"hover"},{header:d((()=>[o("div",Z,[a[12]||(a[12]=o("span",null,"🔍 筛选",-1)),i(r,{link:"",onClick:R,size:"small"},{default:d((()=>[i(t,null,{default:d((()=>[i(_(D))])),_:1}),a[11]||(a[11]=m(" 重置 "))])),_:1})])])),default:d((()=>[o("div",ee,[i(g,{modelValue:E.status,"onUpdate:modelValue":a[1]||(a[1]=e=>E.status=e),placeholder:"状态",clearable:"",class:"filter-item"},{default:d((()=>[i(s,{label:"全部状态",value:""}),i(s,{label:"🟢 活动中",value:"Active"}),i(s,{label:"⏸️ 已暂停",value:"Paused"}),i(s,{label:"✅ 已完成",value:"Completed"}),i(s,{label:"⏰ 已过期",value:"Expired"}),i(s,{label:"❌ 错误",value:"Error"})])),_:1},8,["modelValue"]),i(g,{modelValue:E.recurrenceType,"onUpdate:modelValue":a[2]||(a[2]=e=>E.recurrenceType=e),placeholder:"重复类型",clearable:"",class:"filter-item"},{default:d((()=>[i(s,{label:"全部类型",value:""}),i(s,{label:"每日",value:"Daily"}),i(s,{label:"每周",value:"Weekly"}),i(s,{label:"每月",value:"Monthly"}),i(s,{label:"每年",value:"Yearly"}),i(s,{label:"自定义",value:"CustomCron"})])),_:1},8,["modelValue"]),i(U,{modelValue:y.value,"onUpdate:modelValue":a[3]||(a[3]=e=>y.value=e),placeholder:"🔍 搜索计划名称...",clearable:"",class:"search-input",onKeyup:V(L,["enter"])},{prefix:d((()=>[i(t,null,{default:d((()=>[i(_(w))])),_:1})])),_:1},8,["modelValue"]),i(r,{type:"primary",onClick:L,loading:l.value,class:"search-btn"},{default:d((()=>[i(t,null,{default:d((()=>[i(_(w))])),_:1}),a[13]||(a[13]=m(" 搜索 "))])),_:1},8,["loading"])])])),_:1}),o("div",le,[i($e,{class:"stat-card active",shadow:"hover"},{default:d((()=>[o("div",ae,[a[15]||(a[15]=o("div",{class:"stat-icon"},"🟢",-1)),o("div",te,[o("div",re,f(j.value.active),1),a[14]||(a[14]=o("div",{class:"stat-label"},"活动中",-1))])])])),_:1}),i($e,{class:"stat-card paused",shadow:"hover"},{default:d((()=>[o("div",se,[a[17]||(a[17]=o("div",{class:"stat-icon"},"⏸️",-1)),o("div",ne,[o("div",ue,f(j.value.paused),1),a[16]||(a[16]=o("div",{class:"stat-label"},"已暂停",-1))])])])),_:1}),i($e,{class:"stat-card total",shadow:"hover"},{default:d((()=>[o("div",de,[a[19]||(a[19]=o("div",{class:"stat-icon"},"📊",-1)),o("div",ie,[o("div",oe,f(j.value.total),1),a[18]||(a[18]=o("div",{class:"stat-label"},"总计划",-1))])])])),_:1})]),i($e,{class:"table-card",shadow:"hover"},{header:d((()=>[o("div",ce,[o("span",null,"📋 周期性任务计划 ("+f(z.value.length)+")",1)])])),default:d((()=>[T((u(),n(Se,{data:H.value,"row-key":"id",class:"periodic-table"},{default:d((()=>[i(Ie,{label:"计划名称","min-width":"200"},{default:d((({row:e})=>{return[o("div",pe,[o("div",me,f(e.name),1),e.description?(u(),v("div",fe,f((l=e.description,a=50,!l||l.length<=a?l:l.substring(0,a)+"...")),1)):p("",!0)])];var l,a})),_:1}),i(Ie,{label:"重复规则",width:"250"},{default:d((({row:e})=>{return[o("div",he,[o("div",ye,[i(Me,{type:(l=e.recurrenceType,{Daily:"success",Weekly:"warning",Monthly:"info",Yearly:"danger",CustomCron:""}[l]||""),size:"small"},{default:d((()=>[m(f(Oe(e.recurrenceType)),1)])),_:2},1032,["type"]),o("span",ve,f(Ee(e)),1)]),o("div",ge,f(e.recurrenceDescription||xe(e)),1)])];var l})),_:1}),i(Ie,{label:"状态",width:"120",align:"center"},{default:d((({row:e})=>{return[i(Me,{type:(l=e.status,{Active:"success",Paused:"warning",Completed:"info",Expired:"danger",Error:"danger"}[l]||""),size:"small"},{default:d((()=>[m(f(Ce(e.status)),1)])),_:2},1032,["type"])];var l})),_:1}),i(Ie,{label:"启用状态",width:"100",align:"center"},{default:d((({row:e})=>[i(Ye,{modelValue:e.isEnabled,"onUpdate:modelValue":l=>e.isEnabled=l,onChange:l=>(async e=>{e.switching=!0;try{const l=await $.enablePeriodicSchedule(e.id,e.isEnabled);l.success?(h.success(e.isEnabled?"计划已启用":"计划已禁用"),await q()):(e.isEnabled=!e.isEnabled,h.error("操作失败: "+l.message))}catch(l){e.isEnabled=!e.isEnabled,h.error("操作失败: "+l.message)}finally{e.switching=!1}})(e),loading:e.switching,"active-text":"","inactive-text":""},null,8,["modelValue","onUpdate:modelValue","onChange","loading"])])),_:1}),i(Ie,{label:"下次执行",width:"150"},{default:d((({row:e})=>[e.nextGenerationTime?(u(),v("div",be,f(Ue(e.nextGenerationTime)),1)):(u(),v("span",_e,"未设置"))])),_:1}),i(Ie,{label:"已生成",width:"80",align:"center"},{default:d((({row:e})=>[o("span",ke,f(e.occurrencesGenerated),1)])),_:1}),i(Ie,{label:"操作",width:"180",align:"center",fixed:"right"},{default:d((({row:e})=>[o("div",Ve,[i(r,{link:"",size:"small",onClick:l=>{return a=e,A.value=a,void(Y.value=!0);var a}},{default:d((()=>[i(t,null,{default:d((()=>[i(_(O))])),_:1}),a[20]||(a[20]=m(" 详情 "))])),_:2},1032,["onClick"]),i(r,{link:"",size:"small",onClick:l=>B(e)},{default:d((()=>[i(t,null,{default:d((()=>[i(_(C))])),_:1}),a[21]||(a[21]=m(" 编辑 "))])),_:2},1032,["onClick"]),i(r,{link:"",size:"small",type:"danger",onClick:l=>J(e)},{default:d((()=>[i(t,null,{default:d((()=>[i(_(x))])),_:1}),a[22]||(a[22]=m(" 删除 "))])),_:2},1032,["onClick"])])])),_:1})])),_:1},8,["data"])),[[Pe,l.value]]),o("div",we,[i(Ae,{"current-page":I.currentPage,"onUpdate:currentPage":a[4]||(a[4]=e=>I.currentPage=e),"page-size":I.pageSize,"onUpdate:pageSize":a[5]||(a[5]=e=>I.pageSize=e),"page-sizes":[10,20,50,100],disabled:l.value,background:!0,layout:"total, sizes, prev, pager, next, jumper",total:z.value.length,onSizeChange:G,onCurrentChange:N},null,8,["current-page","page-size","disabled","total"])])])),_:1}),i(P,{modelValue:M.value,"onUpdate:modelValue":a[6]||(a[6]=e=>M.value=e),schedule:S.value,"is-edit":W.value,onClose:De,onSubmit:Te},null,8,["modelValue","schedule","is-edit"]),i(Q,{modelValue:Y.value,"onUpdate:modelValue":a[7]||(a[7]=e=>Y.value=e),schedule:A.value,onClose:a[8]||(a[8]=e=>Y.value=!1),onEdit:B,onDelete:J},null,8,["modelValue","schedule"])])}}},[["__scopeId","data-v-64b875b4"]]);export{De as default};
