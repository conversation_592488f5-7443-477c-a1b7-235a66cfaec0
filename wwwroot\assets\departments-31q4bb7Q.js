import{_ as e,r as a,m as l,a8 as t,b as d,d as u,e as n,w as r,f as o,am as s,bf as i,bg as p,a as c,ag as m,o as v,A as g,af as f,Y as h,t as y,a9 as b,an as I,ao as _,F as w,h as V,ar as C,a1 as M}from"./index-C7OOw0MO.js";import{d as k,p as x}from"./personnel-BRBfHBnU.js";import"./system-9jEcQzSp.js";const U={class:"department-management-container"},N={class:"page-header"},A={class:"page-actions"},D={class:"personnel-option"},L={class:"personnel-code"},P={class:"personnel-option"},$={class:"personnel-code"},j=e({__name:"departments",setup(e){const j=a(!1),z=a(!1),R=a(null),T=a([]),q=a([]),B=a(!1),E=a(""),S=a(!1),Y=a({id:void 0,name:"",code:"",parentId:null,managerId:null,deputyManagerId:null,phone:"",email:"",description:"",sort:0,status:"active"}),F=a({name:[{required:!0,message:"部门名称不能为空",trigger:"blur"}],code:[{required:!0,message:"部门编码不能为空",trigger:"blur"}]}),G=a(null);l((()=>{H(),J()}));const H=async()=>{j.value=!0;try{const e=await k.getDepartmentList();e.data&&e.data.success?T.value=K(e.data.data):t.error("获取部门数据失败")}catch(e){t.error("获取部门列表失败: "+(e.message||"服务器错误"))}finally{j.value=!1}},J=async()=>{z.value=!0;try{const e=await x.getPersonnelList({});e.data&&e.data.success?q.value=e.data.data:t.error("获取人员数据失败")}catch(e){t.error("获取人员数据失败: "+(e.message||"服务器错误"))}finally{z.value=!1}return Promise.resolve()},K=e=>{if(!e||!Array.isArray(e))return[];const a=[],l={};return e.forEach((e=>{l[e.id]={...e,id:Number(e.id),parentId:e.parentId?Number(e.parentId):null,managerId:e.managerId?Number(e.managerId):null,deputyManagerId:void 0!==e.deputyManagerId?Number(e.deputyManagerId):null,manager:e.manager||"",phone:e.phone||"",email:e.email||"",userCount:e.userCount||0,sort:e.sort||0,status:e.isActive?"active":"disabled",children:[]}})),e.forEach((e=>{if(e.parentId){const t=l[e.parentId];t?t.children.push(l[e.id]):a.push(l[e.id])}else a.push(l[e.id])})),a},O=()=>{R.value.expandAllRows()},Q=()=>{R.value.collapseAllRows()},W=()=>{E.value="新建部门",Y.value={id:void 0,name:"",code:"",parentId:null,managerId:null,deputyManagerId:null,phone:"",email:"",description:"",sort:0,status:"active"},B.value=!0,z.value=!0,x.getPersonnelList().then((e=>{e.data&&e.data.success?q.value=e.data.data:t.error("获取人员数据失败"),z.value=!1})).catch((e=>{t.error("获取人员数据失败: "+(e.message||"服务器错误")),z.value=!1})),C((()=>{G.value&&G.value.clearValidate()}))},X=async()=>{var e;if(G.value)try{await G.value.validate(),S.value=!0;const a={name:Y.value.name,code:Y.value.code,description:Y.value.description||"",parentId:Y.value.parentId,managerId:Y.value.managerId,deputyManagerId:Y.value.deputyManagerId,status:Y.value.status};let l;l=Y.value.id?await k.updateDepartment(Y.value.id,a):await k.createDepartment(a),l.data&&l.data.success?(t.success(Y.value.id?"部门更新成功":"部门创建成功"),B.value=!1,H()):t.error((null==(e=l.data)?void 0:e.message)||(Y.value.id?"部门更新失败":"部门创建失败"))}catch(a){t.error("操作失败: "+(a.message||"服务器错误"))}finally{S.value=!1}},Z=()=>{B.value=!1},ee=e=>{if(!e)return"";const a=Number(e);if(!q.value||0===q.value.length)return`ID: ${a}`;let l=q.value.find((e=>e.id===a));return l||(l=q.value.find((e=>String(e.id)===String(a)))),l?l.name:`未知(ID:${a})`};return(e,a)=>{const l=c("el-button"),J=c("el-table-column"),K=c("el-tag"),ae=c("el-table"),le=c("el-card"),te=c("el-input"),de=c("el-form-item"),ue=c("el-option"),ne=c("el-select"),re=c("el-input-number"),oe=c("el-form"),se=c("el-dialog"),ie=m("loading");return v(),d("div",U,[u("div",N,[a[13]||(a[13]=u("h2",{class:"page-title"},"部门管理",-1)),u("div",A,[n(l,{type:"primary",onClick:W,icon:o(s)},{default:r((()=>a[10]||(a[10]=[g(" 新建部门 ")]))),_:1},8,["icon"]),n(l,{type:"success",onClick:O,icon:o(i)},{default:r((()=>a[11]||(a[11]=[g(" 展开所有 ")]))),_:1},8,["icon"]),n(l,{type:"info",onClick:Q,icon:o(p)},{default:r((()=>a[12]||(a[12]=[g(" 折叠所有 ")]))),_:1},8,["icon"])])]),n(le,{class:"data-card"},{default:r((()=>[f((v(),h(ae,{ref_key:"departmentTable",ref:R,data:T.value,"row-key":"id",border:"","default-expand-all":"","tree-props":{children:"children",hasChildren:"hasChildren"}},{default:r((()=>[n(J,{prop:"name",label:"部门名称","min-width":"180"}),n(J,{prop:"code",label:"部门编码",width:"150"}),n(J,{label:"部门经理",width:"120"},{default:r((e=>[u("span",null,y(ee(e.row.managerId)),1)])),_:1}),n(J,{label:"部门主任",width:"120"},{default:r((e=>[u("span",null,y(ee(e.row.deputyManagerId)),1)])),_:1}),n(J,{prop:"phone",label:"联系电话",width:"150"}),n(J,{prop:"email",label:"邮箱","min-width":"200","show-overflow-tooltip":""}),n(J,{prop:"userCount",label:"用户数",width:"100",align:"center"}),n(J,{prop:"sort",label:"排序",width:"80",align:"center"}),n(J,{prop:"status",label:"状态",width:"100",align:"center"},{default:r((e=>["active"===e.row.status?(v(),h(K,{key:0,type:"success"},{default:r((()=>a[14]||(a[14]=[g("正常")]))),_:1})):"disabled"===e.row.status?(v(),h(K,{key:1,type:"info"},{default:r((()=>a[15]||(a[15]=[g("禁用")]))),_:1})):b("",!0)])),_:1}),n(J,{label:"操作",width:"250",fixed:"right"},{default:r((e=>[n(l,{type:"text",size:"small",onClick:a=>{return l=e.row,E.value=`添加"${l.name}"的子部门`,Y.value={id:void 0,name:"",code:"",parentId:l.id,managerId:null,deputyManagerId:null,phone:"",email:"",description:"",sort:0,status:"active"},B.value=!0,z.value=!0,x.getPersonnelList().then((e=>{e.data&&e.data.success?q.value=e.data.data:t.error("获取人员数据失败"),z.value=!1})).catch((e=>{t.error("获取人员数据失败: "+(e.message||"服务器错误")),z.value=!1})),void C((()=>{G.value&&G.value.clearValidate()}));var l},icon:o(s)},{default:r((()=>a[16]||(a[16]=[g(" 添加下级 ")]))),_:2},1032,["onClick","icon"]),n(l,{type:"text",size:"small",onClick:a=>(e=>{E.value=`编辑部门"${e.name}"`,B.value=!0;const a={...e,deputyManagerId:void 0!==e.deputyManagerId?e.deputyManagerId:null};Y.value={...a,managerId:null,deputyManagerId:null},z.value=!0,x.getPersonnelList().then((e=>{e.data&&e.data.success?(q.value=e.data.data,C((()=>{Y.value.managerId=a.managerId?Number(a.managerId):null,Y.value.deputyManagerId=a.deputyManagerId?Number(a.deputyManagerId):null}))):t.error("获取人员数据失败"),z.value=!1})).catch((e=>{t.error("获取人员数据失败: "+(e.message||"服务器错误")),z.value=!1})),C((()=>{G.value&&G.value.clearValidate()}))})(e.row),icon:o(I)},{default:r((()=>a[17]||(a[17]=[g(" 编辑 ")]))),_:2},1032,["onClick","icon"]),n(l,{type:"text",size:"small",onClick:a=>{var l;(l=e.row).children&&l.children.length>0?t.warning("该部门下有子部门，请先删除子部门"):l.userCount>0?t.warning("该部门下有用户，不能直接删除"):M.confirm(`确定要删除部门"${l.name}"吗？`,"删除部门",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}).then((async()=>{var e;try{j.value=!0;const a=await k.deleteDepartment(l.id);a.data&&a.data.success?(t.success("部门已删除"),H()):t.error((null==(e=a.data)?void 0:e.message)||"删除部门失败")}catch(a){t.error("删除部门失败: "+(a.message||"服务器错误"))}finally{j.value=!1}})).catch((()=>{}))},icon:o(_)},{default:r((()=>a[18]||(a[18]=[g(" 删除 ")]))),_:2},1032,["onClick","icon"])])),_:1})])),_:1},8,["data"])),[[ie,j.value]])])),_:1}),n(se,{modelValue:B.value,"onUpdate:modelValue":a[9]||(a[9]=e=>B.value=e),title:E.value,width:"50%","before-close":Z},{footer:r((()=>[n(l,{onClick:Z},{default:r((()=>a[19]||(a[19]=[g("取消")]))),_:1}),n(l,{type:"primary",loading:S.value,onClick:X},{default:r((()=>a[20]||(a[20]=[g("确定")]))),_:1},8,["loading"])])),default:r((()=>[n(oe,{ref_key:"formRef",ref:G,model:Y.value,rules:F.value,"label-width":"120px"},{default:r((()=>[n(de,{label:"部门名称",prop:"name"},{default:r((()=>[n(te,{modelValue:Y.value.name,"onUpdate:modelValue":a[0]||(a[0]=e=>Y.value.name=e)},null,8,["modelValue"])])),_:1}),n(de,{label:"部门编码",prop:"code"},{default:r((()=>[n(te,{modelValue:Y.value.code,"onUpdate:modelValue":a[1]||(a[1]=e=>Y.value.code=e)},null,8,["modelValue"])])),_:1}),n(de,{label:"部门经理",prop:"managerId"},{default:r((()=>[n(ne,{modelValue:Y.value.managerId,"onUpdate:modelValue":a[2]||(a[2]=e=>Y.value.managerId=e),filterable:"",clearable:"",placeholder:"请选择部门经理",loading:z.value},{default:r((()=>[(v(!0),d(w,null,V(q.value,(e=>(v(),h(ue,{key:e.id,label:e.name,value:e.id},{default:r((()=>[u("div",D,[u("span",null,y(e.name),1),u("span",L,"("+y(e.employeeCode)+")",1)])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),n(de,{label:"部门主任",prop:"deputyManagerId"},{default:r((()=>[n(ne,{modelValue:Y.value.deputyManagerId,"onUpdate:modelValue":a[3]||(a[3]=e=>Y.value.deputyManagerId=e),filterable:"",clearable:"",placeholder:"请选择部门主任",loading:z.value},{default:r((()=>[(v(!0),d(w,null,V(q.value,(e=>(v(),h(ue,{key:e.id,label:e.name,value:e.id},{default:r((()=>[u("div",P,[u("span",null,y(e.name),1),u("span",$,"("+y(e.employeeCode)+")",1)])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),n(de,{label:"联系电话",prop:"phone"},{default:r((()=>[n(te,{modelValue:Y.value.phone,"onUpdate:modelValue":a[4]||(a[4]=e=>Y.value.phone=e)},null,8,["modelValue"])])),_:1}),n(de,{label:"邮箱",prop:"email"},{default:r((()=>[n(te,{modelValue:Y.value.email,"onUpdate:modelValue":a[5]||(a[5]=e=>Y.value.email=e)},null,8,["modelValue"])])),_:1}),n(de,{label:"描述",prop:"description"},{default:r((()=>[n(te,{modelValue:Y.value.description,"onUpdate:modelValue":a[6]||(a[6]=e=>Y.value.description=e),type:"textarea"},null,8,["modelValue"])])),_:1}),n(de,{label:"排序",prop:"sort"},{default:r((()=>[n(re,{modelValue:Y.value.sort,"onUpdate:modelValue":a[7]||(a[7]=e=>Y.value.sort=e),min:0,max:100},null,8,["modelValue"])])),_:1}),n(de,{label:"状态",prop:"status"},{default:r((()=>[n(ne,{modelValue:Y.value.status,"onUpdate:modelValue":a[8]||(a[8]=e=>Y.value.status=e),placeholder:"请选择状态"},{default:r((()=>[n(ue,{label:"正常",value:"active"}),n(ue,{label:"禁用",value:"disabled"})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"])])}}},[["__scopeId","data-v-1417501b"]]);export{j as default};
