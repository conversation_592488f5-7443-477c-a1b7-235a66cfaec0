<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>采购单重复物品测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .test-data {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-data h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 采购单重复物品测试</h1>
        <p>测试同一个采购单中添加相同物品时的处理逻辑</p>

        <div class="test-section">
            <h3>1. 测试数据准备</h3>
            <div class="test-data">
                <h4>测试场景A：完全相同的物品</h4>
                <p>物品1: 联想ThinkPad X1, 规格: i7-1165G7, 16GB, 512GB, 数量: 2, 单价: 9950</p>
                <p>物品2: 联想ThinkPad X1, 规格: i7-1165G7, 16GB, 512GB, 数量: 1, 单价: 9950</p>
                <p><strong>预期:</strong> 后端应该合并为一个物品，总数量3</p>
            </div>
            
            <div class="test-data">
                <h4>测试场景B：名称相同但规格不同</h4>
                <p>物品1: 联想ThinkPad X1, 规格: i7-1165G7, 16GB, 512GB, 数量: 2, 单价: 9950</p>
                <p>物品2: 联想ThinkPad X1, 规格: i5-1135G7, 8GB, 256GB, 数量: 1, 单价: 7950</p>
                <p><strong>预期:</strong> 应该保持为两个独立物品</p>
            </div>
            
            <button class="btn" onclick="testScenarioA()">测试场景A</button>
            <button class="btn" onclick="testScenarioB()">测试场景B</button>
            <div id="testResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. 前端重复检测测试</h3>
            <p>测试前端在提交前是否检测和处理重复物品</p>
            <button class="btn" onclick="testFrontendDuplicateDetection()">测试前端重复检测</button>
            <div id="frontendResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. 后端处理逻辑测试</h3>
            <p>直接调用后端API测试重复物品处理</p>
            <button class="btn" onclick="testBackendDuplicateHandling()">测试后端处理</button>
            <div id="backendResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. 数据库验证</h3>
            <p>验证创建的采购单在数据库中的实际存储情况</p>
            <button class="btn" onclick="verifyDatabaseData()">验证数据库数据</button>
            <div id="dbResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 测试场景A：完全相同的物品
        async function testScenarioA() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '正在测试场景A：完全相同的物品...';

            const testData = {
                supplierId: 1,
                requesterId: 1,
                notes: "测试场景A - 重复物品合并",
                items: [
                    {
                        itemName: "联想ThinkPad X1",
                        itemCode: "TP-X1-001",
                        specification: "i7-1165G7, 16GB, 512GB",
                        assetTypeId: 1,
                        unitPrice: 9950.00,
                        quantity: 2,
                        notes: "第一个物品"
                    },
                    {
                        itemName: "联想ThinkPad X1",
                        itemCode: "TP-X1-001",
                        specification: "i7-1165G7, 16GB, 512GB",
                        assetTypeId: 1,
                        unitPrice: 9950.00,
                        quantity: 1,
                        notes: "重复的物品"
                    }
                ]
            };

            try {
                const response = await fetch('http://localhost:5001/api/v2/Purchase', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });

                const result = await response.json();
                
                let output = `场景A测试结果:\n\n`;
                output += `请求数据:\n${JSON.stringify(testData, null, 2)}\n\n`;
                output += `响应状态: ${response.status}\n`;
                output += `响应数据:\n${JSON.stringify(result, null, 2)}\n\n`;

                if (result.success && result.data) {
                    // 获取创建的采购单详情
                    const detailResponse = await fetch(`http://localhost:5001/api/v2/Purchase/${result.data.id}`);
                    const detailResult = await detailResponse.json();
                    
                    output += `创建的采购单详情:\n${JSON.stringify(detailResult, null, 2)}\n\n`;
                    
                    // 分析结果
                    if (detailResult.success && detailResult.data.items) {
                        const items = detailResult.data.items;
                        output += `分析结果:\n`;
                        output += `- 物品数量: ${items.length}\n`;
                        
                        if (items.length === 1) {
                            output += `✅ 正确: 重复物品已合并\n`;
                            output += `- 合并后数量: ${items[0].quantity}\n`;
                            output += `- 预期数量: 3 (2+1)\n`;
                            if (items[0].quantity === 3) {
                                output += `✅ 数量合并正确\n`;
                            } else {
                                output += `❌ 数量合并错误\n`;
                            }
                        } else if (items.length === 2) {
                            output += `❌ 错误: 重复物品未合并，仍有${items.length}个物品\n`;
                            items.forEach((item, index) => {
                                output += `  物品${index + 1}: ${item.itemName}, 数量: ${item.quantity}\n`;
                            });
                        } else {
                            output += `⚠️ 异常: 物品数量为${items.length}，超出预期\n`;
                        }
                    }
                }

                resultDiv.textContent = output;
                resultDiv.className = 'result ' + (result.success ? 'success' : 'error');
            } catch (error) {
                resultDiv.textContent = `测试失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 测试场景B：名称相同但规格不同
        async function testScenarioB() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '正在测试场景B：名称相同但规格不同...';

            const testData = {
                supplierId: 1,
                requesterId: 1,
                notes: "测试场景B - 不同规格物品",
                items: [
                    {
                        itemName: "联想ThinkPad X1",
                        itemCode: "TP-X1-HIGH",
                        specification: "i7-1165G7, 16GB, 512GB",
                        assetTypeId: 1,
                        unitPrice: 9950.00,
                        quantity: 2,
                        notes: "高配版本"
                    },
                    {
                        itemName: "联想ThinkPad X1",
                        itemCode: "TP-X1-LOW",
                        specification: "i5-1135G7, 8GB, 256GB",
                        assetTypeId: 1,
                        unitPrice: 7950.00,
                        quantity: 1,
                        notes: "标准版本"
                    }
                ]
            };

            try {
                const response = await fetch('http://localhost:5001/api/v2/Purchase', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });

                const result = await response.json();
                
                let output = `场景B测试结果:\n\n`;
                output += `请求数据:\n${JSON.stringify(testData, null, 2)}\n\n`;
                output += `响应状态: ${response.status}\n`;
                output += `响应数据:\n${JSON.stringify(result, null, 2)}\n\n`;

                if (result.success && result.data) {
                    // 获取创建的采购单详情
                    const detailResponse = await fetch(`http://localhost:5001/api/v2/Purchase/${result.data.id}`);
                    const detailResult = await detailResponse.json();
                    
                    output += `创建的采购单详情:\n${JSON.stringify(detailResult, null, 2)}\n\n`;
                    
                    // 分析结果
                    if (detailResult.success && detailResult.data.items) {
                        const items = detailResult.data.items;
                        output += `分析结果:\n`;
                        output += `- 物品数量: ${items.length}\n`;
                        
                        if (items.length === 2) {
                            output += `✅ 正确: 不同规格物品保持独立\n`;
                            items.forEach((item, index) => {
                                output += `  物品${index + 1}: ${item.itemName}\n`;
                                output += `    规格: ${item.specification}\n`;
                                output += `    数量: ${item.quantity}\n`;
                                output += `    单价: ${item.unitPrice}\n`;
                            });
                        } else {
                            output += `❌ 错误: 不同规格物品被错误合并或处理\n`;
                        }
                    }
                }

                resultDiv.textContent = output;
                resultDiv.className = 'result ' + (result.success ? 'success' : 'error');
            } catch (error) {
                resultDiv.textContent = `测试失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 测试前端重复检测
        function testFrontendDuplicateDetection() {
            const resultDiv = document.getElementById('frontendResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';

            // 模拟前端数据处理逻辑
            const items = [
                { name: "联想ThinkPad X1", specification: "i7-1165G7, 16GB, 512GB", quantity: 2, unitPrice: 9950 },
                { name: "联想ThinkPad X1", specification: "i7-1165G7, 16GB, 512GB", quantity: 1, unitPrice: 9950 },
                { name: "戴尔XPS 13", specification: "i5-1135G7, 8GB, 256GB", quantity: 1, unitPrice: 7500 }
            ];

            let output = `前端重复检测测试:\n\n`;
            output += `原始物品列表:\n`;
            items.forEach((item, index) => {
                output += `${index + 1}. ${item.name} - ${item.specification} - 数量:${item.quantity} - 单价:${item.unitPrice}\n`;
            });

            // 检测重复逻辑
            const duplicateGroups = {};
            items.forEach((item, index) => {
                const key = `${item.name}|${item.specification}|${item.unitPrice}`;
                if (!duplicateGroups[key]) {
                    duplicateGroups[key] = [];
                }
                duplicateGroups[key].push({ ...item, originalIndex: index });
            });

            output += `\n重复检测结果:\n`;
            let hasDuplicates = false;
            Object.keys(duplicateGroups).forEach(key => {
                const group = duplicateGroups[key];
                if (group.length > 1) {
                    hasDuplicates = true;
                    output += `发现重复物品: ${group[0].name}\n`;
                    output += `  重复次数: ${group.length}\n`;
                    output += `  总数量: ${group.reduce((sum, item) => sum + item.quantity, 0)}\n`;
                    group.forEach((item, index) => {
                        output += `    第${index + 1}次: 数量${item.quantity} (原索引${item.originalIndex})\n`;
                    });
                }
            });

            if (!hasDuplicates) {
                output += `未发现重复物品\n`;
            }

            // 合并重复物品
            const mergedItems = [];
            Object.keys(duplicateGroups).forEach(key => {
                const group = duplicateGroups[key];
                const mergedItem = {
                    name: group[0].name,
                    specification: group[0].specification,
                    unitPrice: group[0].unitPrice,
                    quantity: group.reduce((sum, item) => sum + item.quantity, 0)
                };
                mergedItems.push(mergedItem);
            });

            output += `\n合并后的物品列表:\n`;
            mergedItems.forEach((item, index) => {
                output += `${index + 1}. ${item.name} - ${item.specification} - 数量:${item.quantity} - 单价:${item.unitPrice}\n`;
            });

            output += `\n结论:\n`;
            output += `- 原始物品数: ${items.length}\n`;
            output += `- 合并后物品数: ${mergedItems.length}\n`;
            output += `- 是否有重复: ${hasDuplicates ? '是' : '否'}\n`;

            resultDiv.textContent = output;
            resultDiv.className = 'result info';
        }

        // 测试后端处理逻辑
        async function testBackendDuplicateHandling() {
            const resultDiv = document.getElementById('backendResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '正在测试后端重复处理逻辑...';

            // 这里需要检查后端代码是否有重复处理逻辑
            let output = `后端重复处理逻辑分析:\n\n`;
            
            output += `根据代码分析:\n`;
            output += `1. PurchaseService.CreatePurchaseOrderAsync方法:\n`;
            output += `   - 直接遍历request.Items添加到数据库\n`;
            output += `   - 没有重复检测和合并逻辑\n`;
            output += `   - 每个item都会创建独立的PurchaseItem记录\n\n`;
            
            output += `2. 数据库层面:\n`;
            output += `   - PurchaseItem表没有唯一约束防止重复\n`;
            output += `   - 允许同一采购单下有多个相同物品记录\n\n`;
            
            output += `3. 结论:\n`;
            output += `   ❌ 后端当前没有重复物品检测和合并逻辑\n`;
            output += `   ❌ 重复物品会被创建为独立记录\n`;
            output += `   ⚠️ 这可能导致数据冗余和管理困难\n\n`;
            
            output += `4. 建议修复:\n`;
            output += `   - 在CreatePurchaseOrderAsync中添加重复检测\n`;
            output += `   - 合并相同物品的数量\n`;
            output += `   - 或在前端提交前进行合并处理\n`;

            resultDiv.textContent = output;
            resultDiv.className = 'result warning';
        }

        // 验证数据库数据
        async function verifyDatabaseData() {
            const resultDiv = document.getElementById('dbResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '正在验证数据库数据...';

            try {
                // 获取最近创建的采购单
                const response = await fetch('http://localhost:5001/api/v2/Purchase?pageIndex=1&pageSize=5');
                const result = await response.json();
                
                let output = `数据库验证结果:\n\n`;
                
                if (result.success && result.data && result.data.items) {
                    output += `最近的采购单:\n`;
                    
                    for (const order of result.data.items.slice(0, 3)) {
                        output += `\n采购单 ${order.id} (${order.orderCode}):\n`;
                        
                        // 获取详情
                        const detailResponse = await fetch(`http://localhost:5001/api/v2/Purchase/${order.id}`);
                        const detailResult = await detailResponse.json();
                        
                        if (detailResult.success && detailResult.data.items) {
                            output += `  物品数量: ${detailResult.data.items.length}\n`;
                            
                            // 检查是否有重复物品
                            const itemGroups = {};
                            detailResult.data.items.forEach(item => {
                                const key = `${item.itemName}|${item.specification}`;
                                if (!itemGroups[key]) {
                                    itemGroups[key] = [];
                                }
                                itemGroups[key].push(item);
                            });
                            
                            let hasDuplicates = false;
                            Object.keys(itemGroups).forEach(key => {
                                const group = itemGroups[key];
                                if (group.length > 1) {
                                    hasDuplicates = true;
                                    output += `  ⚠️ 发现重复物品: ${group[0].itemName}\n`;
                                    group.forEach((item, index) => {
                                        output += `    记录${index + 1}: 数量${item.quantity}, ID${item.id}\n`;
                                    });
                                }
                            });
                            
                            if (!hasDuplicates) {
                                output += `  ✅ 无重复物品\n`;
                            }
                        }
                    }
                } else {
                    output += `无法获取采购单数据\n`;
                }

                resultDiv.textContent = output;
                resultDiv.className = 'result info';
            } catch (error) {
                resultDiv.textContent = `验证失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
