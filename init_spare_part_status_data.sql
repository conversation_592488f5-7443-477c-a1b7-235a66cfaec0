-- 初始化备品备件状态管理测试数据
USE itassets;

-- 1. 插入状态类型数据
INSERT IGNORE INTO spare_part_status_types (id, code, name, category, color, icon, description, is_active, sort_order, created_at, updated_at) VALUES
-- 可用状态
(1, 'NEW', '新品', 'Available', '#67c23a', 'CircleCheck', '全新未使用的备件', 1, 1, NOW(), NOW()),
(2, 'GOOD', '良好', 'Available', '#67c23a', 'CircleCheck', '状态良好可正常使用', 1, 2, NOW(), NOW()),
(3, 'REFURBISHED', '翻新', 'Available', '#67c23a', 'CircleCheck', '维修后状态良好', 1, 3, NOW(), NOW()),

-- 不可用状态
(4, 'FAULTY', '故障', 'Unavailable', '#f56c6c', 'Warning', '有故障需要维修', 1, 4, NOW(), NOW()),
(5, 'DAMAGED', '损坏', 'Unavailable', '#f56c6c', 'Warning', '严重损坏', 1, 5, NOW(), NOW()),
(6, 'SCRAPPED', '报废', 'Unavailable', '#f56c6c', 'Warning', '无法修复已报废', 1, 6, NOW(), NOW()),

-- 在途状态
(7, 'UNDER_REPAIR', '返厂中', 'InTransit', '#e6a23c', 'Van', '送厂维修中', 1, 7, NOW(), NOW()),
(8, 'REPAIRING', '维修中', 'InTransit', '#e6a23c', 'Van', '正在维修', 1, 8, NOW(), NOW()),
(9, 'PENDING_INSPECTION', '待检验', 'InTransit', '#e6a23c', 'Van', '维修完成待检验', 1, 9, NOW(), NOW()),

-- 预留状态
(10, 'ALLOCATED', '已分配', 'Reserved', '#909399', 'Lock', '已分配给设备', 1, 10, NOW(), NOW()),
(11, 'RESERVED', '预留', 'Reserved', '#909399', 'Lock', '为特定用途预留', 1, 11, NOW(), NOW());

-- 2. 插入测试备件数据（如果不存在）
INSERT IGNORE INTO spare_parts (id, code, material_number, name, specification, brand, unit, type_id, location_id, min_stock, warning_threshold, stock_quantity, price, notes, created_at, updated_at) VALUES
(1, 'SP001', 'MAT001', '电机轴承', '6205-2RS', 'SKF', '个', 1, 1, 5, 10, 0, 150.00, '高速电机专用轴承', NOW(), NOW()),
(2, 'SP002', 'MAT002', '变频器模块', 'IGBT-300A', 'ABB', '个', 2, 1, 2, 5, 0, 2500.00, '变频器功率模块', NOW(), NOW()),
(3, 'SP003', 'MAT003', '传感器', 'PT100', 'Siemens', '个', 3, 2, 10, 20, 0, 80.00, '温度传感器', NOW(), NOW());

-- 3. 插入库存明细数据
INSERT IGNORE INTO spare_part_inventories (id, part_id, location_id, status_id, quantity, batch_number, serial_numbers, purchase_date, warranty_expire_date, unit_cost, notes, created_at, updated_at) VALUES
-- 电机轴承库存
(1, 1, 1, 1, 8, 'BATCH001', '["SN001", "SN002", "SN003", "SN004", "SN005", "SN006", "SN007", "SN008"]', '2024-01-15', '2026-01-15', 150.00, '新采购批次', NOW(), NOW()),
(2, 1, 1, 2, 5, 'BATCH002', '["SN009", "SN010", "SN011", "SN012", "SN013"]', '2023-06-10', '2025-06-10', 145.00, '库存良好', NOW(), NOW()),
(3, 1, 2, 4, 2, 'BATCH003', '["SN014", "SN015"]', '2023-03-20', '2025-03-20', 140.00, '发现故障需维修', NOW(), NOW()),

-- 变频器模块库存
(4, 2, 1, 1, 3, 'BATCH004', '["VFD001", "VFD002", "VFD003"]', '2024-02-20', '2027-02-20', 2500.00, '新品库存', NOW(), NOW()),
(5, 2, 1, 7, 1, 'BATCH005', '["VFD004"]', '2023-12-15', '2026-12-15', 2450.00, '返厂维修中', NOW(), NOW()),
(6, 2, 2, 10, 1, 'BATCH006', '["VFD005"]', '2023-11-10', '2026-11-10', 2400.00, '已分配给设备A', NOW(), NOW()),

-- 传感器库存
(7, 3, 2, 1, 15, 'BATCH007', '[]', '2024-03-01', '2026-03-01', 80.00, '批量采购', NOW(), NOW()),
(8, 3, 2, 2, 8, 'BATCH008', '[]', '2023-09-15', '2025-09-15', 75.00, '状态良好', NOW(), NOW()),
(9, 3, 1, 5, 3, 'BATCH009', '[]', '2023-05-20', '2025-05-20', 70.00, '损坏待处理', NOW(), NOW());

-- 4. 插入状态变更历史记录
INSERT IGNORE INTO spare_part_status_histories (id, inventory_id, from_status_id, to_status_id, quantity, reason, operator_id, created_at) VALUES
(1, 3, 2, 4, 2, '设备运行中发现异响，检查后确认轴承故障', 1, '2024-06-15 10:30:00'),
(2, 5, 1, 7, 1, '变频器模块出现过热保护，送厂检修', 1, '2024-06-10 14:20:00'),
(3, 6, 1, 10, 1, '分配给生产线A设备维护使用', 1, '2024-06-05 09:15:00'),
(4, 9, 2, 5, 3, '传感器外壳破损，无法正常工作', 1, '2024-06-01 16:45:00');

-- 5. 更新备件主表的库存数量
UPDATE spare_parts sp SET stock_quantity = (
    SELECT COALESCE(SUM(spi.quantity), 0) 
    FROM spare_part_inventories spi 
    WHERE spi.part_id = sp.id
) WHERE sp.id IN (1, 2, 3);

-- 6. 插入供应商数据（如果不存在）
INSERT IGNORE INTO suppliers (id, code, name, contact_person, phone, email, address, notes, is_active, created_at, updated_at) VALUES
(1, 'SUP001', 'SKF轴承有限公司', '张经理', '021-12345678', '<EMAIL>', '上海市浦东新区', '轴承专业供应商', 1, NOW(), NOW()),
(2, 'SUP002', 'ABB电气设备公司', '李工程师', '010-87654321', '<EMAIL>', '北京市朝阳区', '变频器设备供应商', 1, NOW(), NOW()),
(3, 'SUP003', '西门子自动化', '王总监', '0755-11223344', '<EMAIL>', '深圳市南山区', '传感器设备供应商', 1, NOW(), NOW());

-- 7. 插入备件类型数据（如果不存在）
INSERT IGNORE INTO spare_part_types (id, code, name, description, parent_id, level, sort_order, is_active, created_at, updated_at) VALUES
(1, 'MECH', '机械件', '机械传动相关备件', NULL, 1, 1, 1, NOW(), NOW()),
(2, 'ELEC', '电气件', '电气控制相关备件', NULL, 1, 2, 1, NOW(), NOW()),
(3, 'INST', '仪表件', '仪表检测相关备件', NULL, 1, 3, 1, NOW(), NOW());

-- 8. 插入库位数据（如果不存在）
INSERT IGNORE INTO spare_part_locations (id, code, name, area, description, is_active, created_at, updated_at) VALUES
(1, 'WH-A01', '仓库A区-01', 'A', '主仓库A区第1排', 1, NOW(), NOW()),
(2, 'WH-A02', '仓库A区-02', 'A', '主仓库A区第2排', 1, NOW(), NOW()),
(3, 'WH-B01', '仓库B区-01', 'B', '主仓库B区第1排', 1, NOW(), NOW());

-- 查询验证数据
SELECT '=== 状态类型数据 ===' as info;
SELECT id, code, name, category, color, is_active FROM spare_part_status_types ORDER BY sort_order;

SELECT '=== 备件库存汇总 ===' as info;
SELECT 
    sp.code,
    sp.name,
    sp.stock_quantity as total_stock,
    COUNT(spi.id) as inventory_records,
    SUM(CASE WHEN sst.category = 'Available' THEN spi.quantity ELSE 0 END) as available_qty,
    SUM(CASE WHEN sst.category = 'Unavailable' THEN spi.quantity ELSE 0 END) as unavailable_qty,
    SUM(CASE WHEN sst.category = 'InTransit' THEN spi.quantity ELSE 0 END) as in_transit_qty,
    SUM(CASE WHEN sst.category = 'Reserved' THEN spi.quantity ELSE 0 END) as reserved_qty
FROM spare_parts sp
LEFT JOIN spare_part_inventories spi ON sp.id = spi.part_id
LEFT JOIN spare_part_status_types sst ON spi.status_id = sst.id
WHERE sp.id IN (1, 2, 3)
GROUP BY sp.id, sp.code, sp.name, sp.stock_quantity
ORDER BY sp.id;

SELECT '=== 库存明细数据 ===' as info;
SELECT 
    sp.name as part_name,
    spl.name as location_name,
    sst.name as status_name,
    sst.category,
    spi.quantity,
    spi.batch_number
FROM spare_part_inventories spi
JOIN spare_parts sp ON spi.part_id = sp.id
JOIN spare_part_locations spl ON spi.location_id = spl.id
JOIN spare_part_status_types sst ON spi.status_id = sst.id
ORDER BY sp.id, spi.id;
