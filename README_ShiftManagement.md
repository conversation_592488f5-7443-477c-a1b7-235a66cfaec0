# IT资产管理系统 - 班次管理和任务调度模块

## 🎯 项目概述

本模块为IT资产管理系统新增了完整的班次管理和任务调度功能，实现了基于班次的任务分配、领取、跟踪和统计分析。系统采用行业最佳实践，支持多班次运作、智能提醒和可视化统计。

## ✨ 核心特性

### 🕐 班次管理
- **多班次支持**: 白班、夜班、中班等灵活配置
- **跨天班次**: 完美支持夜班等跨天工作模式
- **智能调度**: 基于班次的任务自动分配和提醒

### 👥 用户分配
- **灵活分配**: 支持固定、临时、轮班等多种分配模式
- **时间控制**: 精确的生效和失效时间管理
- **多班次兼容**: 用户可同时分配到多个班次

### 📋 任务领取
- **按时领取**: 严格按照班次时间控制任务领取
- **防重复机制**: 智能防止重复领取同一任务
- **状态跟踪**: 完整的任务生命周期管理

### 🎨 完成水印
- **彩色标识**: 每个用户拥有独特的颜色标识
- **视觉识别**: 已完成任务显示完成者的彩色水印
- **快速定位**: 便于管理者快速识别任务完成情况

### ⏰ 智能提醒
- **行业标准**: 基于任务优先级的多级提醒策略
- **多种类型**: 开始前、截止前、逾期等全方位提醒
- **重复机制**: 确保重要任务不被遗漏

### 📊 统计分析
- **实时统计**: 班次任务完成情况实时展示
- **多维分析**: 按用户、班次、时间等多维度统计
- **可视化展示**: 直观的图表和进度条显示

## 🏗️ 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   API控制器     │    │   业务服务      │
│                 │    │                 │    │                 │
│ • 班次统计页面  │◄──►│ • WorkShift     │◄──►│ • WorkShiftSvc  │
│ • 任务领取界面  │    │ • TaskClaim     │    │ • TaskClaimSvc  │
│ • 实时刷新      │    │ • Statistics    │    │ • WatermarkSvc  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                ▲                        ▲
                                │                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   后台服务      │    │   数据访问      │    │   数据库        │
│                 │    │                 │    │                 │
│ • 定时任务生成  │    │ • Entity        │    │ • work_shifts   │
│ • 提醒处理      │    │ • Framework     │    │ • task_claims   │
│ • 自动调度      │    │ • Core          │    │ • task_reminders│
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📁 文件结构

```
├── Application/Features/Tasks/Services/
│   ├── WorkShiftService.cs           # 班次管理服务
│   ├── TaskClaimService.cs           # 任务领取服务
│   ├── TaskWatermarkService.cs       # 任务水印服务
│   └── TaskReminderService.cs        # 任务提醒服务
├── Application/Features/Tasks/Dtos/
│   ├── WorkShiftDto.cs               # 班次相关DTO
│   └── TaskClaimDto.cs               # 任务领取相关DTO
├── Domain/Entities/Tasks/
│   ├── WorkShift.cs                  # 班次实体
│   ├── UserShiftAssignment.cs        # 用户班次分配实体
│   ├── TaskClaim.cs                  # 任务领取记录实体
│   └── TaskReminder.cs               # 任务提醒实体
├── Api/V2/
│   └── WorkShiftController.cs        # 班次管理API控制器
├── Controllers/
│   └── TasksViewController.cs        # 任务视图控制器
├── Views/Tasks/
│   └── ShiftStatistics.cshtml        # 班次统计页面
├── wwwroot/js/
│   └── shift-statistics.js           # 前端JavaScript
├── Infrastructure/Services/
│   └── TaskClaimGenerationService.cs # 后台服务
├── Database/Migrations/
│   └── AddShiftManagementTables.sql  # 数据库迁移脚本
├── Tests/
│   └── ShiftManagementApiTests.http  # API测试脚本
└── Documentation/
    └── ShiftManagementSystem.md      # 详细文档
```

## 🚀 快速开始

### 1. 数据库初始化
系统启动时会自动执行数据库初始化，创建必要的表结构：
- `work_shifts` - 班次表
- `user_shift_assignments` - 用户班次分配表
- `task_claims` - 任务领取记录表
- `task_reminders` - 任务提醒配置表

### 2. 默认配置
系统会自动创建默认班次：
- **白班**: 8:00-20:00，任务领取时间8:00
- **夜班**: 20:00-次日8:00，任务领取时间20:00

### 3. 访问界面
启动系统后，访问以下页面：
- 班次统计: `/TasksView/ShiftStatistics`
- API文档: `/swagger`

## 📋 使用流程

### 管理员操作
1. **配置班次**: 根据实际需要创建和配置班次
2. **分配用户**: 将用户分配到相应的班次
3. **监控统计**: 通过统计页面监控任务完成情况

### 用户操作
1. **查看班次**: 登录后查看自己的当前班次
2. **领取任务**: 在班次任务领取时间内领取任务
3. **执行任务**: 更新任务状态（开始→完成）
4. **查看统计**: 查看个人和团队的任务完成情况

## 🔧 配置说明

### 班次配置
```json
{
  "shiftName": "白班",
  "shiftCode": "DAY",
  "shiftType": "Day",
  "startTime": "08:00:00",
  "endTime": "20:00:00",
  "taskClaimTime": "08:00:00",
  "isOvernight": false
}
```

### 提醒配置
系统内置了基于任务优先级的提醒策略：
- **Critical**: 1天前→8小时前→2小时前→30分钟前→逾期每小时
- **High**: 2天前→1天前→4小时前→1小时前→逾期每2小时
- **Medium**: 3天前→1天前→8小时前→逾期每8小时
- **Low**: 5天前→1天前→逾期每天

## 🧪 测试

### API测试
使用提供的HTTP测试文件：
```bash
# 使用VS Code REST Client扩展
# 打开 Tests/ShiftManagementApiTests.http
# 逐个执行测试用例
```

### 功能测试
1. **班次管理测试**: 创建、查询、分配班次
2. **任务领取测试**: 领取、更新、完成任务
3. **统计功能测试**: 查看各种统计数据
4. **提醒功能测试**: 验证提醒触发机制

## 📈 性能优化

### 已实现的优化
- **数据库索引**: 为关键查询字段添加索引
- **缓存机制**: 统计数据支持缓存
- **批量操作**: 优化批量数据处理
- **异步处理**: 后台服务异步处理提醒

### 监控指标
- 任务领取响应时间
- 统计查询性能
- 后台服务执行频率
- 数据库连接池使用情况

## 🔒 安全考虑

- **权限控制**: 用户只能操作自己的任务
- **数据验证**: 严格的输入验证和业务规则检查
- **审计日志**: 完整的操作日志记录
- **防重复提交**: 防止重复操作和数据不一致

## 🚧 未来规划

### 短期计划
- [ ] 移动端适配
- [ ] 更丰富的统计图表
- [ ] 导出功能
- [ ] 消息推送集成

### 长期计划
- [ ] AI智能调度
- [ ] 预测性分析
- [ ] 工作流引擎集成
- [ ] 多租户支持

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 支持

如有问题或建议，请：
- 创建 Issue
- 发送邮件至项目维护者
- 查看详细文档: `Documentation/ShiftManagementSystem.md`

---

**版本**: 1.0.0  
**发布日期**: 2025-06-19  
**兼容性**: .NET 6.0+, MySQL 8.0+
