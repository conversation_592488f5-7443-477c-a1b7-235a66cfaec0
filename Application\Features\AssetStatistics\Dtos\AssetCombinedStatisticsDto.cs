using System.Collections.Generic;

namespace ItAssetsSystem.Application.Features.AssetStatistics.Dtos
{
    /// <summary>
    /// 资产组合统计DTO
    /// </summary>
    public class AssetCombinedStatisticsDto
    {
        /// <summary>
        /// 区域和类型交叉统计
        /// </summary>
        public List<RegionTypeStatisticsDto> RegionTypeStatistics { get; set; } = new List<RegionTypeStatisticsDto>();

        /// <summary>
        /// 部门和类型交叉统计
        /// </summary>
        public List<DepartmentTypeStatisticsDto> DepartmentTypeStatistics { get; set; } = new List<DepartmentTypeStatisticsDto>();

        /// <summary>
        /// 状态分布统计
        /// </summary>
        public StatusDistributionDto StatusDistribution { get; set; }
    }

    /// <summary>
    /// 区域类型统计DTO
    /// </summary>
    public class RegionTypeStatisticsDto
    {
        /// <summary>
        /// 区域ID
        /// </summary>
        public int RegionId { get; set; }

        /// <summary>
        /// 区域名称
        /// </summary>
        public string RegionName { get; set; }

        /// <summary>
        /// 资产类型ID
        /// </summary>
        public int AssetTypeId { get; set; }

        /// <summary>
        /// 资产类型名称
        /// </summary>
        public string AssetTypeName { get; set; }

        /// <summary>
        /// 资产数量
        /// </summary>
        public int AssetCount { get; set; }

        /// <summary>
        /// 正常数量
        /// </summary>
        public int NormalCount { get; set; }

        /// <summary>
        /// 故障数量
        /// </summary>
        public int FaultCount { get; set; }
    }

    /// <summary>
    /// 部门类型统计DTO
    /// </summary>
    public class DepartmentTypeStatisticsDto
    {
        /// <summary>
        /// 部门ID
        /// </summary>
        public int DepartmentId { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        public string DepartmentName { get; set; }

        /// <summary>
        /// 资产类型ID
        /// </summary>
        public int AssetTypeId { get; set; }

        /// <summary>
        /// 资产类型名称
        /// </summary>
        public string AssetTypeName { get; set; }

        /// <summary>
        /// 资产数量
        /// </summary>
        public int AssetCount { get; set; }

        /// <summary>
        /// 正常数量
        /// </summary>
        public int NormalCount { get; set; }

        /// <summary>
        /// 故障数量
        /// </summary>
        public int FaultCount { get; set; }
    }

    /// <summary>
    /// 状态分布统计DTO
    /// </summary>
    public class StatusDistributionDto
    {
        /// <summary>
        /// 正常资产数量
        /// </summary>
        public int NormalCount { get; set; }

        /// <summary>
        /// 故障资产数量
        /// </summary>
        public int FaultCount { get; set; }

        /// <summary>
        /// 维修中资产数量
        /// </summary>
        public int MaintenanceCount { get; set; }

        /// <summary>
        /// 待处理资产数量
        /// </summary>
        public int PendingCount { get; set; }

        /// <summary>
        /// 报废资产数量
        /// </summary>
        public int ScrapCount { get; set; }
    }
}