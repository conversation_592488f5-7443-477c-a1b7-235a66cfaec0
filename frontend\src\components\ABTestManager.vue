<template>
  <div class="ab-test-manager">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>A/B测试管理</span>
          <el-button 
            type="primary" 
            size="small" 
            @click="showCreateDialog"
          >
            创建测试
          </el-button>
        </div>
      </template>

      <!-- 当前测试状态 -->
      <div class="current-test" v-if="currentTest">
        <h4>当前运行的A/B测试</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="测试名称">
            {{ currentTest.testName }}
          </el-descriptions-item>
          <el-descriptions-item label="测试描述">
            {{ currentTest.description }}
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">
            {{ formatTime(currentTest.startTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="结束时间">
            {{ formatTime(currentTest.endTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="测试状态">
            <el-tag :type="getTestStatusType(currentTest.enabled)">
              {{ currentTest.enabled ? '运行中' : '已停止' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="操作">
            <el-button 
              v-if="currentTest.enabled"
              type="warning" 
              size="mini" 
              @click="stopTest"
            >
              停止测试
            </el-button>
            <el-button 
              v-else
              type="success" 
              size="mini" 
              @click="startTest"
            >
              启动测试
            </el-button>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 测试结果分析 -->
      <div class="test-results" v-if="testResults">
        <h4>A/B测试结果分析</h4>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-card class="group-card">
              <template #header>
                <span>A组 (V1版本)</span>
              </template>
              <div class="group-stats">
                <div class="stat-item">
                  <span class="label">用户数量:</span>
                  <span class="value">{{ testResults.groupA.userCount }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">平均响应时间:</span>
                  <span class="value">{{ testResults.groupA.avgResponseTime.toFixed(2) }}ms</span>
                </div>
                <div class="stat-item">
                  <span class="label">成功率:</span>
                  <span class="value">{{ testResults.groupA.successRate.toFixed(1) }}%</span>
                </div>
                <div class="stat-item">
                  <span class="label">用户满意度:</span>
                  <span class="value">{{ testResults.groupA.satisfaction.toFixed(1) }}/5</span>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="group-card">
              <template #header>
                <span>B组 (V1.1版本)</span>
              </template>
              <div class="group-stats">
                <div class="stat-item">
                  <span class="label">用户数量:</span>
                  <span class="value">{{ testResults.groupB.userCount }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">平均响应时间:</span>
                  <span class="value">{{ testResults.groupB.avgResponseTime.toFixed(2) }}ms</span>
                </div>
                <div class="stat-item">
                  <span class="label">成功率:</span>
                  <span class="value">{{ testResults.groupB.successRate.toFixed(1) }}%</span>
                </div>
                <div class="stat-item">
                  <span class="label">用户满意度:</span>
                  <span class="value">{{ testResults.groupB.satisfaction.toFixed(1) }}/5</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 统计显著性分析 -->
        <div class="significance-analysis">
          <h5>统计显著性分析</h5>
          <el-table :data="significanceData" size="small">
            <el-table-column prop="metric" label="指标" width="150" />
            <el-table-column prop="improvement" label="B组相对A组改进" width="150">
              <template #default="{ row }">
                <span :class="getImprovementClass(row.improvementValue)">
                  {{ row.improvement }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="pValue" label="P值" width="100" />
            <el-table-column prop="significance" label="显著性">
              <template #default="{ row }">
                <el-tag :type="getSignificanceType(row.pValue)">
                  {{ getSignificanceText(row.pValue) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="confidence" label="置信度" />
          </el-table>
        </div>

        <!-- 测试结论 -->
        <div class="test-conclusion">
          <h5>测试结论</h5>
          <el-alert
            :title="testResults.conclusion.title"
            :description="testResults.conclusion.description"
            :type="testResults.conclusion.type"
            show-icon
            :closable="false"
          />
          <div class="recommendation" v-if="testResults.recommendation">
            <h6>推荐建议:</h6>
            <p>{{ testResults.recommendation }}</p>
          </div>
        </div>
      </div>

      <!-- 无测试提示 -->
      <div v-if="!currentTest && !testResults" class="no-test">
        <el-empty description="暂无A/B测试">
          <el-button type="primary" @click="showCreateDialog">创建A/B测试</el-button>
        </el-empty>
      </div>
    </el-card>

    <!-- 创建测试对话框 -->
    <el-dialog
      v-model="createDialogVisible"
      title="创建A/B测试"
      width="500px"
    >
      <el-form :model="testForm" :rules="testRules" ref="testFormRef" label-width="100px">
        <el-form-item label="测试名称" prop="testName">
          <el-input v-model="testForm.testName" placeholder="请输入测试名称" />
        </el-form-item>
        <el-form-item label="测试描述" prop="description">
          <el-input 
            v-model="testForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入测试描述"
          />
        </el-form-item>
        <el-form-item label="测试时长" prop="duration">
          <el-select v-model="testForm.duration" placeholder="选择测试时长">
            <el-option label="1小时" :value="1" />
            <el-option label="6小时" :value="6" />
            <el-option label="1天" :value="24" />
            <el-option label="3天" :value="72" />
            <el-option label="7天" :value="168" />
          </el-select>
        </el-form-item>
        <el-form-item label="测试服务">
          <el-checkbox-group v-model="testForm.services">
            <el-checkbox label="user">用户服务</el-checkbox>
            <el-checkbox label="asset">资产服务</el-checkbox>
            <el-checkbox label="task">任务服务</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="createTest"
          :loading="creating"
        >
          创建测试
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { useApiAdapter } from '@/plugins/api-adapter'

export default {
  name: 'ABTestManager',
  setup() {
    const { apiAdapter, enableABTest } = useApiAdapter()
    return { apiAdapter, enableABTest }
  },
  data() {
    return {
      currentTest: null,
      testResults: null,
      createDialogVisible: false,
      creating: false,
      testForm: {
        testName: '',
        description: '',
        duration: 24,
        services: ['user', 'asset', 'task']
      },
      testRules: {
        testName: [
          { required: true, message: '请输入测试名称', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入测试描述', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    significanceData() {
      if (!this.testResults) return []
      
      return [
        {
          metric: '响应时间',
          improvement: this.formatImprovement(this.testResults.improvements.responseTime),
          improvementValue: this.testResults.improvements.responseTime,
          pValue: this.testResults.significance.responseTime.pValue,
          confidence: this.testResults.significance.responseTime.confidence
        },
        {
          metric: '成功率',
          improvement: this.formatImprovement(this.testResults.improvements.successRate),
          improvementValue: this.testResults.improvements.successRate,
          pValue: this.testResults.significance.successRate.pValue,
          confidence: this.testResults.significance.successRate.confidence
        },
        {
          metric: '用户满意度',
          improvement: this.formatImprovement(this.testResults.improvements.satisfaction),
          improvementValue: this.testResults.improvements.satisfaction,
          pValue: this.testResults.significance.satisfaction.pValue,
          confidence: this.testResults.significance.satisfaction.confidence
        }
      ]
    }
  },
  async mounted() {
    await this.loadTestData()
  },
  methods: {
    async loadTestData() {
      try {
        // 模拟加载A/B测试数据
        // 实际应该从后端API获取
        this.currentTest = {
          testName: 'API版本性能对比测试',
          description: 'V1 vs V1.1 版本性能和用户体验对比',
          enabled: true,
          startTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          endTime: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000)
        }

        // 模拟测试结果数据
        this.testResults = {
          groupA: {
            userCount: 150,
            avgResponseTime: 245.6,
            successRate: 94.2,
            satisfaction: 3.8
          },
          groupB: {
            userCount: 148,
            avgResponseTime: 198.3,
            successRate: 97.1,
            satisfaction: 4.2
          },
          improvements: {
            responseTime: 19.3,
            successRate: 3.1,
            satisfaction: 10.5
          },
          significance: {
            responseTime: { pValue: 0.003, confidence: '99.7%' },
            successRate: { pValue: 0.021, confidence: '97.9%' },
            satisfaction: { pValue: 0.001, confidence: '99.9%' }
          },
          conclusion: {
            title: 'V1.1版本显著优于V1版本',
            description: 'V1.1版本在响应时间、成功率和用户满意度方面都有显著改进，建议全面切换到V1.1版本。',
            type: 'success'
          },
          recommendation: '基于测试结果，建议在1周内逐步将所有用户迁移到V1.1版本，预计可以提升19.3%的响应性能和10.5%的用户满意度。'
        }
      } catch (error) {
        console.error('加载测试数据失败:', error)
      }
    },

    showCreateDialog() {
      this.createDialogVisible = true
    },

    async createTest() {
      try {
        await this.$refs.testFormRef.validate()
        
        this.creating = true
        
        const endTime = new Date(Date.now() + this.testForm.duration * 60 * 60 * 1000)
        
        const success = await this.enableABTest({
          testName: this.testForm.testName,
          description: this.testForm.description,
          startTime: new Date(),
          endTime: endTime
        })
        
        if (success) {
          this.$message.success('A/B测试创建成功')
          this.createDialogVisible = false
          await this.loadTestData()
        } else {
          this.$message.error('A/B测试创建失败')
        }
      } catch (error) {
        this.$message.error('创建测试失败: ' + error.message)
      } finally {
        this.creating = false
      }
    },

    async startTest() {
      try {
        const success = await this.enableABTest({ enabled: true })
        if (success) {
          this.currentTest.enabled = true
          this.$message.success('A/B测试已启动')
        }
      } catch (error) {
        this.$message.error('启动测试失败: ' + error.message)
      }
    },

    async stopTest() {
      try {
        const success = await this.enableABTest({ enabled: false })
        if (success) {
          this.currentTest.enabled = false
          this.$message.success('A/B测试已停止')
        }
      } catch (error) {
        this.$message.error('停止测试失败: ' + error.message)
      }
    },

    // 格式化方法
    formatTime(timestamp) {
      if (!timestamp) return '-'
      return new Date(timestamp).toLocaleString()
    },

    formatImprovement(value) {
      if (value > 0) {
        return `+${value.toFixed(1)}%`
      } else if (value < 0) {
        return `${value.toFixed(1)}%`
      }
      return '0%'
    },

    getTestStatusType(enabled) {
      return enabled ? 'success' : 'warning'
    },

    getImprovementClass(value) {
      if (value > 5) return 'improvement-positive'
      if (value < -5) return 'improvement-negative'
      return 'improvement-neutral'
    },

    getSignificanceType(pValue) {
      if (pValue < 0.01) return 'success'
      if (pValue < 0.05) return 'warning'
      return 'danger'
    },

    getSignificanceText(pValue) {
      if (pValue < 0.01) return '高度显著'
      if (pValue < 0.05) return '显著'
      return '不显著'
    }
  }
}
</script>

<style scoped>
.ab-test-manager {
  padding: 20px;
}

.test-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-test,
.test-results {
  margin-bottom: 30px;
}

.group-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
}

.stat-item .label {
  color: #606266;
}

.stat-item .value {
  font-weight: bold;
  color: #303133;
}

.significance-analysis {
  margin: 24px 0;
}

.test-conclusion {
  margin-top: 24px;
}

.recommendation {
  margin-top: 16px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.recommendation h6 {
  margin-bottom: 8px;
  color: #303133;
}

.improvement-positive { color: #67c23a; }
.improvement-negative { color: #f56c6c; }
.improvement-neutral { color: #909399; }

.no-test {
  text-align: center;
  padding: 40px;
}

h4, h5, h6 {
  margin-bottom: 16px;
  color: #303133;
  border-bottom: 2px solid #e4e7ed;
  padding-bottom: 8px;
}
</style>
