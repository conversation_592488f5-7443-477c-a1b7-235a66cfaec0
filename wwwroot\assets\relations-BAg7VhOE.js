import{_ as e,r as a,a5 as l,p as t,Y as n,o,w as u,e as d,a as s,d as r,t as i,b as c,h as p,F as v,A as m,a8 as f,c as y,a9 as g,f as w,as as h,ao as b,v as _,m as k,al as C,at as V,au as I,av as x,aw as U,B as T,ar as z,ag as L,a7 as S,ax as D,af as N,z as $,a1 as P}from"./index-C7OOw0MO.js";import{l as E}from"./location-CmcDmm4Y.js";import{f as B}from"./asset-CPgk_3Sp.js";import{d as A,p as j}from"./personnel-BRBfHBnU.js";import"./system-9jEcQzSp.js";const M={class:"location-name"},Y={class:"department-option"},F={class:"department-code"},R={class:"dialog-footer"},K=e({__name:"SetDepartmentDialog",props:{visible:{type:Boolean,default:!1},locationId:{type:[Number,String],default:""},locationName:{type:String,default:""},currentDepartmentId:{type:[Number,String],default:null}},emits:["update:visible","success","close"],setup(e,{emit:y}){const g=e,w=y,h=a(!1),b=l({departmentId:null,remarks:""}),_=a(!1),k=a(!1),C=a([]);t((()=>g.visible),(e=>{h.value=e,e&&(V(),I())})),t((()=>h.value),(e=>{w("update:visible",e)}));const V=()=>{b.departmentId=g.currentDepartmentId||null,b.remarks=""},I=async()=>{_.value=!0;try{const e=await A.getDepartmentList();e.data&&e.data.success?C.value=e.data.data||[]:f.error("获取部门列表失败")}catch(e){f.error("获取部门列表失败")}finally{_.value=!1}},x=async()=>{if(b.departmentId)if(g.locationId){k.value=!0;try{const e=await A.updateLocationDepartment(g.locationId,{departmentId:b.departmentId,remarks:b.remarks});e.data&&e.data.success?(f.success("设置部门成功"),h.value=!1,w("success")):f.error("设置部门失败: "+(e.data&&e.data.message||"未知错误"))}catch(e){f.error("设置部门失败: "+(e.message||"未知错误"))}finally{k.value=!1}}else f.warning("位置ID无效");else f.warning("请选择部门")},U=()=>{w("close")};return(a,l)=>{const t=s("el-form-item"),f=s("el-option"),y=s("el-select"),g=s("el-input"),w=s("el-form"),V=s("el-button"),I=s("el-dialog");return o(),n(I,{modelValue:h.value,"onUpdate:modelValue":l[3]||(l[3]=e=>h.value=e),title:"设置使用部门",width:"500px","close-on-click-modal":!1,"destroy-on-close":!0,onClosed:U},{footer:u((()=>[r("div",R,[d(V,{onClick:l[2]||(l[2]=e=>h.value=!1)},{default:u((()=>l[4]||(l[4]=[m("取 消")]))),_:1}),d(V,{type:"primary",onClick:x,loading:k.value},{default:u((()=>l[5]||(l[5]=[m("确 定")]))),_:1},8,["loading"])])])),default:u((()=>[d(w,{model:b,"label-width":"100px","label-position":"right"},{default:u((()=>[d(t,{label:"当前位置"},{default:u((()=>[r("span",M,i(e.locationName),1)])),_:1}),d(t,{label:"选择部门",required:""},{default:u((()=>[d(y,{modelValue:b.departmentId,"onUpdate:modelValue":l[0]||(l[0]=e=>b.departmentId=e),placeholder:"请选择部门",style:{width:"100%"},filterable:"",loading:_.value},{default:u((()=>[(o(!0),c(v,null,p(C.value,(e=>(o(),n(f,{key:e.id,label:e.name,value:e.id},{default:u((()=>[r("span",Y,i(e.name),1),r("span",F,i(e.code),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),d(t,{label:"备注"},{default:u((()=>[d(g,{modelValue:b.remarks,"onUpdate:modelValue":l[1]||(l[1]=e=>b.remarks=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])}}},[["__scopeId","data-v-7aa50006"]]),q={class:"set-users-container"},O={class:"location-info"},G={class:"location-info-item"},H={class:"location-info-value"},J={key:0,class:"location-info-item"},Q={class:"location-info-value"},W={key:0,class:"current-users-section"},X={class:"current-users-title"},Z={class:"filter-row"},ee={class:"personnel-option"},ae={class:"personnel-name"},le={class:"personnel-code"},te={class:"personnel-dept"},ne={class:"dialog-footer"},oe=e({__name:"SetLocationUsersDialog",props:{visible:{type:Boolean,default:!1},locationId:{type:[Number,String],default:""},locationName:{type:String,default:""},userType:{type:String,default:"user"},departmentInfo:{type:String,default:""}},emits:["update:visible","success","close"],setup(e,{emit:k}){const C=e,V=k,I=a(!1),x=y((()=>"manager"===C.userType?"设置位置管理员":"设置位置使用人")),U=y((()=>"manager"===C.userType?"管理员":"使用人")),T=l({departmentId:null,keyword:"",selectedUsers:[],replaceExisting:!1}),z=a(!1),L=a(!1),S=a([]),D=a([]),N=a([]);t((()=>C.visible),(e=>{I.value=e,e&&($(),P(),B(),M())})),t((()=>I.value),(e=>{V("update:visible",e)}));const $=()=>{T.departmentId=null,T.keyword="",T.selectedUsers=[],T.replaceExisting=!1},P=async()=>{try{const e=await A.getDepartmentList();e&&e.data&&e.data.success?S.value=e.data.data||[]:(f.error("获取部门列表失败"),S.value=[])}catch(e){f.error("获取部门列表失败: "+(e.message||"服务器错误")),S.value=[]}},B=async()=>{z.value=!0;try{const e={};T.departmentId&&(e.departmentId=T.departmentId),T.keyword&&(e.keyword=T.keyword);const a=await j.getPersonnelList(e);a&&a.data&&a.data.success?D.value=a.data.data||[]:(f.error("获取人员列表失败"),D.value=[])}catch(e){f.error("获取人员列表失败: "+(e.message||"服务器错误")),D.value=[]}finally{z.value=!1}},M=async()=>{if(C.locationId)try{const e=await E.getLocationUsers(C.locationId);if(e&&e.data&&e.data.success){const a="manager"===C.userType?1:0;N.value=(e.data.data||[]).filter((e=>e.userType===a))}else f.error("获取位置关联人员失败"),N.value=[]}catch(e){f.error("获取位置关联人员失败: "+(e.message||"服务器错误")),N.value=[]}},Y=()=>{B()},F=()=>{B()},R=async()=>{var e;if(0!==T.selectedUsers.length)if(C.locationId){L.value=!0;try{const a=T.selectedUsers.map((e=>({personnelId:e,userType:"manager"===C.userType?1:0}))),l=await E.setLocationUsers(C.locationId,a,T.replaceExisting);l&&l.data&&l.data.success?(f.success(`设置${U.value}成功`),I.value=!1,V("success")):f.error((null==(e=null==l?void 0:l.data)?void 0:e.message)||`设置${U.value}失败`)}catch(a){f.error(`设置${U.value}失败: `+(a.message||"服务器错误"))}finally{L.value=!1}}else f.warning("位置ID无效");else f.warning(`请选择${U.value}`)},K=()=>{V("close")};return(a,l)=>{const t=s("el-table-column"),y=s("el-button"),k=s("el-table"),V=s("el-option"),$=s("el-select"),P=s("el-input"),B=s("el-form-item"),A=s("el-checkbox"),j=s("el-form"),oe=s("el-dialog");return o(),n(oe,{modelValue:I.value,"onUpdate:modelValue":l[5]||(l[5]=e=>I.value=e),title:x.value,width:"600px","close-on-click-modal":!1,"destroy-on-close":!0,onClosed:K},{footer:u((()=>[r("div",ne,[d(y,{onClick:l[4]||(l[4]=e=>I.value=!1)},{default:u((()=>l[8]||(l[8]=[m("取 消")]))),_:1}),d(y,{type:"primary",onClick:R,loading:L.value},{default:u((()=>l[9]||(l[9]=[m("确 定")]))),_:1},8,["loading"])])])),default:u((()=>[r("div",q,[r("div",O,[r("div",G,[l[6]||(l[6]=r("span",{class:"location-info-label"},"当前位置：",-1)),r("span",H,i(e.locationName),1)]),e.departmentInfo?(o(),c("div",J,[l[7]||(l[7]=r("span",{class:"location-info-label"},"使用部门：",-1)),r("span",Q,i(e.departmentInfo),1)])):g("",!0)]),N.value.length>0?(o(),c("div",W,[r("div",X,"当前"+i(U.value)+"列表",1),d(k,{data:N.value,size:"small",border:"",style:{width:"100%"}},{default:u((()=>[d(t,{type:"index",label:"序号",width:"60"}),d(t,{prop:"name",label:"姓名",width:"120"}),d(t,{prop:"employeeCode",label:"工号",width:"120"}),d(t,{prop:"departmentName",label:"部门"}),d(t,{label:"操作",width:"100"},{default:u((e=>[d(y,{type:"danger",size:"small",circle:"",onClick:h((a=>(async e=>{var a;try{const l=await E.removeLocationUser(C.locationId,e.id);l&&l.data&&l.data.success?(f.success("移除成功"),await M()):f.error((null==(a=null==l?void 0:l.data)?void 0:a.message)||"移除用户失败")}catch(l){f.error("移除用户失败: "+(l.message||"服务器错误"))}})(e.row)),["stop"]),icon:w(b)},null,8,["onClick","icon"])])),_:1})])),_:1},8,["data"])])):g("",!0),d(j,{model:T,"label-width":"100px",class:"user-form"},{default:u((()=>[d(B,{label:"人员筛选"},{default:u((()=>[r("div",Z,[d($,{modelValue:T.departmentId,"onUpdate:modelValue":l[0]||(l[0]=e=>T.departmentId=e),placeholder:"选择部门",clearable:"",style:{width:"220px","margin-right":"10px"},onChange:Y},{default:u((()=>[(o(!0),c(v,null,p(S.value,(e=>(o(),n(V,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),d(P,{modelValue:T.keyword,"onUpdate:modelValue":l[1]||(l[1]=e=>T.keyword=e),placeholder:"姓名/工号",clearable:"",style:{width:"220px"}},{append:u((()=>[d(y,{icon:w(_),onClick:F},null,8,["icon"])])),_:1},8,["modelValue"])])])),_:1}),d(B,{label:U.value},{default:u((()=>[d($,{modelValue:T.selectedUsers,"onUpdate:modelValue":l[2]||(l[2]=e=>T.selectedUsers=e),multiple:"",filterable:"",placeholder:"请选择人员",style:{width:"100%"},loading:z.value},{default:u((()=>[(o(!0),c(v,null,p(D.value,(e=>(o(),n(V,{key:e.id,label:`${e.name} ${e.employeeCode?"("+e.employeeCode+")":""}`,value:e.id},{default:u((()=>[r("div",ee,[r("div",null,[r("span",ae,i(e.name),1),r("span",le,i(e.employeeCode),1)]),r("span",te,i(e.departmentName),1)])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1},8,["label"]),d(B,null,{default:u((()=>[d(A,{modelValue:T.replaceExisting,"onUpdate:modelValue":l[3]||(l[3]=e=>T.replaceExisting=e)},{default:u((()=>[m("替换当前"+i(U.value),1)])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])])])),_:1},8,["modelValue","title"])}}},[["__scopeId","data-v-4dd2c6d3"]]),ue={class:"location-relations-container"},de={class:"page-header"},se={class:"page-actions"},re={class:"relation-content"},ie={class:"filters"},ce={class:"location-view-switch"},pe={class:"card-header"},ve={class:"header-info"},me={key:0},fe={key:1},ye={key:0,class:"location-path"},ge={key:0,class:"tree-view-container"},we={class:"tree-container"},he={class:"location-tree-node"},be={class:"node-content"},_e={class:"location-name"},ke={class:"location-type"},Ce={key:0,class:"location-detail-panel"},Ve={class:"detail-header"},Ie={class:"location-path"},xe={key:0,class:"location-actions"},Ue={key:0},Te={key:0},ze={key:1},Le={key:0},Se={key:0},De={key:1},Ne={key:0,class:"personnel-info"},$e={key:1,class:"flat-view"},Pe={key:0},Ee={key:0},Be={key:2},Ae={key:1},je={class:"pagination-container"},Me={class:"relate-dialog-content"},Ye={class:"filters"},Fe={class:"dialog-pagination"},Re={class:"dialog-footer"},Ke={key:0},qe={key:1},Oe={class:"dialog-footer"},Ge={class:"personnel-option"},He={class:"personnel-name"},Je={key:0,class:"personnel-code"},Qe={key:0,class:"personnel-dept"},We={key:0},Xe={class:"dialog-footer"},Ze=e({__name:"relations",setup(e){const y=a(450),b=a("tree");a([]);const j=a(null),M=a("未选择"),Y=a([]),F=a(null),R=a([]),q={children:"children",label:"name"},O=a(null),G=a([]),H=a([]),J=a(!1),Q=a(""),W=a(null),X=a("info"),Z=a([]),ee=a(""),ae=a(null),le=a([]),te=a(!1),ne=a([]),Ze=l({currentPage:1,pageSize:10,total:0}),ea=a(""),aa=a(""),la=a([]),ta=a(""),na=a(null),oa=a(!1),ua=a([]),da=a(!1),sa=a([]),ra=a(""),ia=a(""),ca=a("primary"),pa=l({currentPage:1,pageSize:10,total:0}),va=a(!1),ma=a(!1),fa=l({departmentId:null}),ya={departmentId:[{required:!0,message:"请选择部门",trigger:"change"}]},ga=a(!1),wa=a(!1),ha=l({selectedUsers:[],replaceExisting:!1}),ba=[{label:"在用",value:"in_use"},{label:"闲置",value:"idle"},{label:"维修中",value:"repairing"},{label:"借用",value:"borrowed"},{label:"报废",value:"scrapped"}],_a=a([]),ka=a([]),Ca=a([]),Va=a(""),Ia=a(!1),xa=a("user"),Ua=a(!1),Ta=(e=null)=>{var a;e||F.value?(e&&(F.value=e,j.value=e.id),Pa(),fa.departmentId=(null==(a=F.value)?void 0:a.defaultDepartmentId)||null,fa.departmentId&&za(),Ua.value=!0):f.warning("请先选择位置")},za=()=>{if(Va.value="",!fa.departmentId)return;const e=_a.value.find((e=>e.id===fa.departmentId));if(e&&e.managerId){const a=ka.value.find((a=>a.id===e.managerId));Va.value=a?`${a.name}${a.employeeCode?" ("+a.employeeCode+")":""}`:`ID: ${e.managerId}`}},La=async()=>{F.value&&await fetchLocationDetail(F.value.id),f.success("位置部门设置成功")},Sa=()=>{F.value&&fetchLocationDetail(F.value.id)},Da=async(e=null)=>{const a=e||F.value;!a||3!==a.type&&4!==a.type?f.warning("只能为工序或工位类型的位置设置管理员"):(e&&(F.value=e,j.value=e.id),xa.value="manager",Ia.value=!0)},Na=async(e=null)=>{const a=e||F.value;!a||3!==a.type&&4!==a.type?f.warning("只能为工序或工位类型的位置设置使用人"):(e&&(F.value=e,j.value=e.id),xa.value="user",Ia.value=!0)},$a=(e,a)=>{if(!e)return!0;const l=e.toLowerCase(),t=a.name&&a.name.toLowerCase().includes(l),n=a.code&&a.code.toLowerCase().includes(l),o=ja(a.type).toLowerCase().includes(l);return t||n||o},Pa=async()=>{var e;try{const a=await A.getDepartmentList();if(!(a&&a.data&&a.data.success))throw Z.value=[],new Error((null==(e=null==a?void 0:a.data)?void 0:e.message)||"获取部门列表失败");Z.value=a.data.data||[]}catch(a){throw Z.value=[],a}};t((()=>fa.departmentId),(e=>{e?za():Va.value=""}));const Ea=async()=>{try{te.value=!0;const e=await E.getLocationTree();e&&e.success?(R.value=e.data||[],z((()=>{W.value&&R.value.length>0&&R.value.forEach((e=>{W.value.store&&W.value.store.nodesMap&&W.value.store.nodesMap[e.id]&&(W.value.store.nodesMap[e.id].expanded=!0)}))}))):(f.error("获取位置树形数据失败，请刷新重试"),R.value=[])}catch(e){f.error("加载位置树数据失败"),R.value=[]}finally{te.value=!1}},Ba=()=>{var e;null==(e=W.value)||e.filter(Q.value)},Aa=async(e,a)=>{O.value=e,F.value=e,M.value=e.fullPath||e.name,window._locationClickTimer&&clearTimeout(window._locationClickTimer),window._locationClickTimer=setTimeout((async()=>{J.value=!0;try{3===e.type||4===e.type?await ll(e.id):G.value=[],await el(e.id)}catch(a){}finally{J.value=!1}}),300)},ja=e=>({0:"工厂",1:"车间",2:"产线",3:"工序",4:"工位"}[e]||"未知"),Ma=e=>({0:"danger",1:"warning",2:"primary",3:"success",4:"info"}[e]||""),Ya=async()=>{var e,a;te.value=!0;try{const l={pageIndex:Ze.currentPage,pageSize:Ze.pageSize};ta.value&&(l.type=ta.value),na.value&&(l.parentId=na.value),aa.value&&(l.keyword=aa.value);const t=await E.searchLocations(l);t&&t.success?(locationList.value=(null==(e=t.data)?void 0:e.items)||[],Ze.total=(null==(a=t.data)?void 0:a.total)||0):(f.error((null==t?void 0:t.message)||"获取位置列表失败"),locationList.value=[],Ze.total=0)}catch(l){f.error("获取位置列表失败，请检查网络连接"),locationList.value=[],Ze.total=0}finally{te.value=!1}},Fa=()=>{ta.value="",na.value=null,aa.value="",Ze.currentPage=1,Ya()},Ra=e=>{j.value=e.id,F.value=e,M.value=e.fullPath||e.name,3!==e.type&&4!==e.type||ll(e.id),Oa()},Ka=e=>{Ze.pageSize=e,"flat"===b.value?Ya():Oa()},qa=e=>{Ze.currentPage=e,"flat"===b.value?Ya():Oa()},Oa=async()=>{if(!j.value&&!O.value)return le.value=[],void(Ze.total=0);te.value=!0;try{const e=j.value||(O.value?O.value.id:null);if(!e)return le.value=[],void(Ze.total=0);const a={locationId:e,pageSize:Ze.pageSize,pageIndex:Ze.currentPage,keyword:aa.value||"",status:ea.value||null,startDate:la.value&&la.value[0]?la.value[0]:null,endDate:la.value&&la.value[1]?la.value[1]:null,departmentId:ee.value||null},l=await E.getLocationAssets(a);l&&l.success?(le.value=l.data.items||[],Ze.total=l.data.total||0):(le.value=[],Ze.total=0)}catch(e){f.error("获取资产列表失败"),le.value=[],Ze.total=0}finally{te.value=!1}},Ga=async e=>{if(F.value=null,!e)return j.value=null,M.value="未选择",void Ha(!1);j.value=e;try{te.value=!0;const a=await E.getLocationDetail(e);if(a.success)if(F.value=a.data,M.value=a.data.path?a.data.path.replace(/,/g," > "):a.data.name,a.data.defaultDepartmentId){ee.value=a.data.defaultDepartmentId;const e=Z.value.find((e=>e.id===a.data.defaultDepartmentId));if(e)ae.value=e;else{await Pa();const e=Z.value.find((e=>e.id===a.data.defaultDepartmentId));e&&(ae.value=e)}}else ee.value=null,ae.value=null}catch(a){f.error("获取位置详情失败")}finally{te.value=!1}ea.value=null,aa.value="",la.value=null,Oa()},Ha=(e=!0)=>{e&&(j.value=null),ee.value=null,ea.value=null,aa.value="",la.value=null,Oa()},Ja=()=>{j.value?(oa.value=!0,ra.value="",ia.value=null,ca.value="primary",sa.value=[],pa.currentPage=1,Wa()):f.warning("请先选择位置")},Qa=()=>{if(0===ne.value.length)return void f.warning("请选择要解除关联的资产");const e=ne.value.map((e=>e.name||e.assetCode)).join(", ");P.confirm(`确认解除资产 [${e}] 与当前位置的关联关系吗?`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{te.value=!0;const e=ne.value.map((e=>B.changeAssetLocation(e.id,{newLocationId:0,reason:"批量解除关联",notes:"从位置关联管理页面批量解除"}))),a=await Promise.allSettled(e),l=a.filter((e=>{var a;return"fulfilled"===e.status&&(null==(a=e.value)?void 0:a.success)})).length,t=a.length-l;0===t?f.success(`成功解除 ${l} 个资产的关联`):0===l?f.error("解除关联失败，请重试"):f.warning(`部分资产解除关联成功，${l} 成功，${t} 失败`),Oa()}catch(e){f.error("批量解除关联失败，请检查网络连接")}finally{te.value=!1,ne.value=[]}})).catch((()=>{}))},Wa=async()=>{await(async()=>{var e,a;if(j.value){da.value=!0;try{const l={locationId:j.value,unrelated:!0,pageIndex:pa.currentPage,pageSize:pa.pageSize,keyword:ra.value,assetTypeId:ia.value},t=await B.getAssetList(l);t.success?(ua.value=((null==(e=t.data)?void 0:e.items)||[]).map((e=>({id:e.id,code:e.assetCode,name:e.name,type:e.assetTypeId,typeName:e.assetTypeName,sn:e.serialNumber||"-",status:e.status||0,statusText:Za(e.status||0)}))),pa.total=(null==(a=t.data)?void 0:a.total)||0):(ua.value=[],pa.total=0,f.error(t.message||"获取未关联资产失败"))}catch(l){f.error("获取未关联资产列表失败"),ua.value=[],pa.total=0}finally{da.value=!1}}else f.warning("请先选择位置")})()},Xa=async()=>{if(0!==sa.value.length)if(ca.value)if(j.value)try{await P.confirm(`确认将选中的 ${sa.value.length} 个资产关联到 "${M.value}" 吗？`,"确认关联",{confirmButtonText:"确认",cancelButtonText:"取消",type:"info"}),da.value=!0;const e=sa.value.map((e=>e.id)),a=await E.relateAssets(j.value,{assetIds:e,relationType:ca.value,notes:"从位置关联管理页面关联"});a.success?(f.success(`成功关联 ${e.length} 个资产到 ${M.value}`),oa.value=!1,sa.value=[],Oa()):f.error(a.message||"关联资产失败")}catch(e){f.error("关联资产失败: "+(e.message||"未知错误"))}finally{da.value=!1}else f.warning("请先选择位置");else f.warning("请选择关联类型");else f.warning("请选择要关联的资产")},Za=e=>({0:"未知",1:"在用",2:"闲置",3:"维修中",4:"借用",5:"报废"}[e]||"未知"),el=async e=>{J.value=!0;try{const a={locationId:e,pageSize:100,pageIndex:1},l=await E.getLocationAssets(a);l.success?H.value=l.data.items||[]:H.value=[]}catch(a){H.value=[]}finally{J.value=!1}},al=e=>({0:"闲置",1:"在用",2:"维修中",3:"报废"}[e]||"未知"),ll=async e=>{try{const a=await E.getLocationUsers(e);a&&a.data&&a.data.success?G.value=a.data.data||[]:G.value=[]}catch(a){G.value=[]}},tl=async()=>{var e;if(fa.departmentId){ma.value=!0;try{const a=await E.setLocationDepartment(F.value.id,fa.departmentId);a.data&&a.data.success?(f.success("关联部门成功"),va.value=!1,await fetchLocationDetail(F.value.id),await fetchLocationUsers(F.value.id)):f.error((null==(e=a.data)?void 0:e.message)||"关联部门失败")}catch(a){f.error("关联部门失败: "+a.message)}finally{ma.value=!1}}else f.warning("请选择部门")},nl=async()=>{var e;if(0!==ha.selectedUsers.length){wa.value=!0;try{const a=ha.selectedUsers.map((e=>({personnelId:e,userType:"manager"===xa.value?1:0}))),l=await E.setLocationUsers(F.value.id,a,ha.replaceExisting);l.data&&l.data.success?(f.success(`设置${"manager"===xa.value?"管理员":"使用人"}成功`),ga.value=!1,await fetchLocationUsers(F.value.id)):f.error((null==(e=l.data)?void 0:e.message)||`设置${"manager"===xa.value?"管理员":"使用人"}失败`)}catch(a){f.error(`设置${"manager"===xa.value?"管理员":"使用人"}失败: `+a.message)}finally{wa.value=!1}}else f.warning("请选择人员")};return k((()=>{(async()=>{te.value=!0;try{try{await Pa()}catch(e){f.warning("部门数据加载失败，部分功能可能受限")}try{await fetchLocationOptions()}catch(e){f.warning("位置选项加载失败，部分功能可能受限")}"tree"===b.value?await Ea():await Ya()}catch(e){f.error("初始化数据失败，请刷新页面重试")}finally{te.value=!1}})()})),C((()=>{window._locationRelationsTimer&&(clearTimeout(window._locationRelationsTimer),window._locationRelationsTimer=null)})),(e,a)=>{var l,t,k,C,z,B;const A=s("el-button"),le=s("el-radio-button"),za=s("el-radio-group"),Pa=s("el-col"),Ea=s("el-row"),Za=s("el-option"),ll=s("el-select"),ol=s("el-form-item"),ul=s("el-input"),dl=s("el-date-picker"),sl=s("el-card"),rl=s("el-tag"),il=s("el-skeleton"),cl=s("el-tree"),pl=s("el-scrollbar"),vl=s("el-descriptions-item"),ml=s("el-descriptions"),fl=s("el-alert"),yl=s("el-tab-pane"),gl=s("el-empty"),wl=s("el-table-column"),hl=s("el-table"),bl=s("el-tabs"),_l=s("el-form"),kl=s("el-icon"),Cl=s("el-dropdown-item"),Vl=s("el-dropdown-menu"),Il=s("el-dropdown"),xl=s("el-pagination"),Ul=s("el-dialog"),Tl=s("el-radio"),zl=L("loading");return o(),c(v,null,[r("div",ue,[r("div",de,[a[36]||(a[36]=r("h2",{class:"page-title"},"位置关联",-1)),r("div",se,[d(A,{type:"primary",onClick:Ja,icon:w(V)},{default:u((()=>a[31]||(a[31]=[m("关联资产")]))),_:1},8,["icon"]),d(A,{type:"success",onClick:Ta,icon:w(I)},{default:u((()=>a[32]||(a[32]=[m("关联部门")]))),_:1},8,["icon"]),d(A,{type:"danger",onClick:Qa,icon:w(x),disabled:0===ne.value.length},{default:u((()=>a[33]||(a[33]=[m(" 解除关联 ")]))),_:1},8,["icon","disabled"]),!F.value||3!==F.value.type&&4!==F.value.type?g("",!0):(o(),n(A,{key:0,type:"warning",onClick:Da,icon:w(U)},{default:u((()=>a[34]||(a[34]=[m(" 设置管理员 ")]))),_:1},8,["icon"])),!F.value||3!==F.value.type&&4!==F.value.type?g("",!0):(o(),n(A,{key:1,type:"info",onClick:Na,icon:w(T)},{default:u((()=>a[35]||(a[35]=[m(" 设置使用人 ")]))),_:1},8,["icon"]))])]),r("div",re,[d(sl,{class:"filter-card"},{default:u((()=>[r("div",ie,[d(Ea,{gutter:16},{default:u((()=>[d(Pa,{span:24},{default:u((()=>[r("div",ce,[d(za,{modelValue:b.value,"onUpdate:modelValue":a[0]||(a[0]=e=>b.value=e),size:"small"},{default:u((()=>[d(le,{value:"tree"},{default:u((()=>a[37]||(a[37]=[m("树形层级视图")]))),_:1}),d(le,{value:"flat"},{default:u((()=>a[38]||(a[38]=[m("扁平列表视图")]))),_:1})])),_:1},8,["modelValue"])])])),_:1})])),_:1}),"flat"===b.value?(o(),n(Ea,{key:0,gutter:16},{default:u((()=>[d(Pa,{span:8},{default:u((()=>[d(ol,{label:"位置选择"},{default:u((()=>[d(ll,{modelValue:j.value,"onUpdate:modelValue":a[1]||(a[1]=e=>j.value=e),filterable:"",placeholder:"请选择位置",style:{width:"100%"},onChange:Ga},{default:u((()=>[(o(!0),c(v,null,p(Y.value,(e=>(o(),n(Za,{key:e.id,label:e.fullName,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1}),d(Pa,{span:8},{default:u((()=>[d(ol,{label:"部门"},{default:u((()=>[d(ll,{modelValue:ee.value,"onUpdate:modelValue":a[2]||(a[2]=e=>ee.value=e),placeholder:"全部部门",clearable:"",style:{width:"100%"},onChange:Oa},{default:u((()=>[(o(!0),c(v,null,p(Z.value,(e=>(o(),n(Za,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1}),d(Pa,{span:8},{default:u((()=>[d(ol,{label:"资产状态"},{default:u((()=>[d(ll,{modelValue:ea.value,"onUpdate:modelValue":a[3]||(a[3]=e=>ea.value=e),placeholder:"全部状态",clearable:"",style:{width:"100%"},onChange:Oa},{default:u((()=>[(o(),c(v,null,p(ba,(e=>d(Za,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1})):g("",!0),"flat"===b.value?(o(),n(Ea,{key:1,gutter:16},{default:u((()=>[d(Pa,{span:8},{default:u((()=>[d(ol,{label:"关键字"},{default:u((()=>[d(ul,{modelValue:aa.value,"onUpdate:modelValue":a[4]||(a[4]=e=>aa.value=e),placeholder:"搜索资产名称/编号/SN",clearable:"",onKeyup:S(Oa,["enter"])},{append:u((()=>[d(A,{icon:w(_),onClick:Oa},null,8,["icon"])])),_:1},8,["modelValue"])])),_:1})])),_:1}),d(Pa,{span:8},{default:u((()=>[d(ol,{label:"最后更新时间"},{default:u((()=>[d(dl,{modelValue:la.value,"onUpdate:modelValue":a[5]||(a[5]=e=>la.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",onChange:Oa,style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),d(Pa,{span:8,class:"text-right"},{default:u((()=>[d(A,{type:"primary",onClick:Oa,icon:w(_)},{default:u((()=>a[39]||(a[39]=[m("搜索")]))),_:1},8,["icon"]),d(A,{onClick:Ha,icon:w(D)},{default:u((()=>a[40]||(a[40]=[m("重置")]))),_:1},8,["icon"])])),_:1})])),_:1})):g("",!0),"tree"===b.value?(o(),n(Ea,{key:2,gutter:16},{default:u((()=>[d(Pa,{span:24},{default:u((()=>[d(ol,{label:"位置层级查询"},{default:u((()=>[d(ul,{modelValue:Q.value,"onUpdate:modelValue":a[6]||(a[6]=e=>Q.value=e),placeholder:"搜索位置名称",clearable:"",onKeyup:S(Ba,["enter"])},{append:u((()=>[d(A,{icon:w(_),onClick:Ba},null,8,["icon"])])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1})):g("",!0)])])),_:1}),d(sl,{class:"data-card"},{header:u((()=>[r("div",pe,[r("div",ve,["flat"===b.value?(o(),c("span",me,"位置列表")):(o(),c("span",fe,"位置层级结构")),ae.value&&"flat"===b.value?(o(),n(rl,{key:2,type:"success",class:"department-tag"},{default:u((()=>[m(i(ae.value.name),1)])),_:1})):g("",!0)]),"flat"===b.value?(o(),c("span",ye,"当前位置："+i(M.value),1)):g("",!0)])])),default:u((()=>["tree"===b.value?(o(),c("div",ge,[d(Ea,{gutter:20},{default:u((()=>[d(Pa,{span:8},{default:u((()=>[r("div",we,[a[41]||(a[41]=r("div",{class:"tree-title"},"位置层级结构",-1)),d(pl,{height:"calc(100vh - 350px)",style:{border:"1px solid #EBEEF5"}},{default:u((()=>[te.value?(o(),n(il,{key:0,loading:te.value,animated:"",rows:10},null,8,["loading"])):N((o(),n(cl,{key:1,ref_key:"locationTree",ref:W,data:R.value,props:q,"node-key":"id","highlight-current":"","default-expand-all":!1,"expand-on-click-node":!1,onNodeClick:Aa,"filter-node-method":$a},{default:u((({node:e,data:a})=>[r("div",he,[r("div",be,[r("span",_e,i(a.name),1),r("span",ke,[d(rl,{size:"small",type:Ma(a.type)},{default:u((()=>[m(i(ja(a.type)),1)])),_:2},1032,["type"])])])])])),_:1},8,["data"])),[[zl,te.value]])])),_:1})])])),_:1}),d(Pa,{span:16},{default:u((()=>[O.value?(o(),c("div",Ce,[r("div",Ve,[r("h3",null,i(O.value.name),1),r("div",Ie,i(M.value),1),3===O.value.type||4===O.value.type?(o(),c("div",xe,[d(A,{type:"primary",size:"small",onClick:a[7]||(a[7]=e=>Da(O.value))},{default:u((()=>a[42]||(a[42]=[m(" 设置管理员 ")]))),_:1}),d(A,{type:"success",size:"small",onClick:a[8]||(a[8]=e=>Na(O.value))},{default:u((()=>a[43]||(a[43]=[m(" 设置使用人 ")]))),_:1})])):g("",!0)]),d(bl,{modelValue:X.value,"onUpdate:modelValue":a[9]||(a[9]=e=>X.value=e)},{default:u((()=>[d(yl,{label:"位置信息",name:"info"},{default:u((()=>[d(ml,{column:2,border:""},{default:u((()=>[d(vl,{label:"位置名称"},{default:u((()=>[m(i(O.value.name),1)])),_:1}),d(vl,{label:"位置类型"},{default:u((()=>[d(rl,{type:Ma(O.value.type)},{default:u((()=>[m(i(ja(O.value.type)),1)])),_:1},8,["type"])])),_:1}),d(vl,{label:"上级位置"},{default:u((()=>[m(i(O.value.parentName||"-"),1)])),_:1}),d(vl,{label:"使用部门"},{default:u((()=>[m(i(O.value.departmentName||"-"),1)])),_:1}),d(vl,{label:"管理人员",span:2},{default:u((()=>[G.value.length>0?(o(),c("div",Ue,[(o(!0),c(v,null,p(G.value.filter((e=>1===e.userType)),(e=>(o(),n(rl,{key:e.id,type:"warning",style:{"margin-right":"5px","margin-bottom":"5px"}},{default:u((()=>[m(i(e.name||e.username),1)])),_:2},1024)))),128)),G.value.some((e=>1===e.userType))?g("",!0):(o(),c("span",Te,"未设置"))])):(o(),c("span",ze,"未设置"))])),_:1}),d(vl,{label:"使用人员",span:2},{default:u((()=>[G.value.length>0?(o(),c("div",Le,[(o(!0),c(v,null,p(G.value.filter((e=>0===e.userType)),(e=>(o(),n(rl,{key:e.id,type:"info",style:{"margin-right":"5px","margin-bottom":"5px"}},{default:u((()=>[m(i(e.name||e.username),1)])),_:2},1024)))),128)),G.value.some((e=>0===e.userType))?g("",!0):(o(),c("span",Se,"未设置"))])):(o(),c("span",De,"未设置"))])),_:1}),d(vl,{label:"备注信息",span:2},{default:u((()=>[m(i(O.value.remark||"-"),1)])),_:1})])),_:1}),3===O.value.type||4===O.value.type?(o(),c("div",Ne,[d(fl,{title:"位置人员管理说明",type:"info",closable:!1,style:{margin:"20px 0"}},{default:u((()=>a[44]||(a[44]=[r("div",{class:"info-content"},[r("p",null,"1. 工序和工位可以设置管理员和使用人员"),r("p",null,"2. 管理员负责此位置的资产和人员管理"),r("p",null,"3. 使用人员是日常使用此位置资产的人员"),r("p",null,"4. 人员信息来自系统用户管理，请确保先添加相关人员")],-1)]))),_:1})])):g("",!0)])),_:1}),d(yl,{label:"关联资产",name:"assets"},{default:u((()=>[N((o(),c("div",null,[0===H.value.length?(o(),n(gl,{key:0,description:"暂无关联资产"})):(o(),n(hl,{key:1,data:H.value,style:{width:"100%"},border:"",stripe:"",size:"small"},{default:u((()=>[d(wl,{type:"index",label:"序号",width:"60",align:"center"}),d(wl,{prop:"assetCode",label:"资产编号",width:"120"}),d(wl,{prop:"name",label:"资产名称",width:"150"}),d(wl,{prop:"assetTypeName",label:"资产类型",width:"120"}),d(wl,{prop:"model",label:"规格型号",width:"120"}),d(wl,{label:"状态",width:"80"},{default:u((({row:e})=>{return[d(rl,{type:(a=e.status,{0:"info",1:"success",2:"warning",3:"danger"}[a]||"info")},{default:u((()=>[m(i(al(e.status)),1)])),_:2},1032,["type"])];var a})),_:1}),d(wl,{label:"操作",width:"150",fixed:"right"},{default:u((({row:e})=>[d(A,{link:"",onClick:a=>{return l=e,void P.alert(`资产 ${l.name} 的详细信息`,"资产详情",{confirmButtonText:"确定"});var l}},{default:u((()=>a[45]||(a[45]=[m("查看")]))),_:2},1032,["onClick"]),d(A,{link:"",class:"danger-text",onClick:a=>{return l=e,void P.confirm(`确定要解除资产 ${l.name} 与当前位置的关联吗？`,"解除关联",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{const e=await E.unrelateAssets(O.value.id,[l.id]);e.success?(f.success("解除关联成功"),el(O.value.id)):f.error(e.message||"解除关联失败")}catch(e){f.error("解除关联失败")}})).catch((()=>{}));var l}},{default:u((()=>a[46]||(a[46]=[m("解除关联")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"]))])),[[zl,J.value]])])),_:1})])),_:1},8,["modelValue"])])):(o(),n(gl,{key:1,description:"请选择左侧位置",style:{"margin-top":"100px"}}))])),_:1})])),_:1})])):g("",!0),"flat"===b.value?(o(),c("div",$e,[d(_l,{inline:!0,class:"location-filters"},{default:u((()=>[d(ol,{label:"位置类型"},{default:u((()=>[d(ll,{modelValue:ta.value,"onUpdate:modelValue":a[10]||(a[10]=e=>ta.value=e),clearable:"",placeholder:"全部类型",style:{width:"150px"}},{default:u((()=>[d(Za,{label:"全部",value:""}),d(Za,{label:"工厂",value:1}),d(Za,{label:"产线",value:2}),d(Za,{label:"工序",value:3}),d(Za,{label:"工位",value:4})])),_:1},8,["modelValue"])])),_:1}),d(ol,{label:"上级位置"},{default:u((()=>[d(ll,{modelValue:na.value,"onUpdate:modelValue":a[11]||(a[11]=e=>na.value=e),filterable:"",clearable:"",placeholder:"请选择",style:{width:"200px"}},{default:u((()=>[(o(!0),c(v,null,p(e.parentOptions,(e=>(o(),n(Za,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),d(ol,{label:"关键字"},{default:u((()=>[d(ul,{modelValue:aa.value,"onUpdate:modelValue":a[12]||(a[12]=e=>aa.value=e),placeholder:"位置名称/编码",style:{width:"200px"}},null,8,["modelValue"])])),_:1}),d(ol,null,{default:u((()=>[d(A,{type:"primary",onClick:Ya},{default:u((()=>a[47]||(a[47]=[m("搜索")]))),_:1}),d(A,{onClick:Fa},{default:u((()=>a[48]||(a[48]=[m("重置")]))),_:1})])),_:1})])),_:1}),N((o(),n(hl,{ref:"locationTable",data:e.locationList,style:{width:"100%"},onRowClick:Ra,height:y.value,border:""},{default:u((()=>[d(wl,{type:"selection",width:"50"}),d(wl,{prop:"code",label:"位置编码",width:"120"}),d(wl,{prop:"name",label:"位置名称","min-width":"150"}),d(wl,{prop:"type",label:"位置类型",width:"100"},{default:u((e=>[d(rl,{size:"small",type:Ma(e.row.type)},{default:u((()=>[m(i(ja(e.row.type)),1)])),_:2},1032,["type"])])),_:1}),d(wl,{prop:"parentName",label:"上级位置","min-width":"150"}),d(wl,{prop:"departmentName",label:"使用部门","min-width":"120"},{default:u((e=>[e.row.departmentName?(o(),c("span",Pe,i(e.row.departmentName),1)):(o(),n(A,{key:1,type:"primary",link:"",onClick:h((a=>Ta(e.row)),["stop"])},{default:u((()=>a[49]||(a[49]=[m("设置部门")]))),_:2},1032,["onClick"]))])),_:1}),d(wl,{prop:"managerName",label:"负责人",width:"120"},{default:u((e=>[e.row.managerName?(o(),c("span",Ee,i(e.row.managerName),1)):3===e.row.type||4===e.row.type?(o(),n(A,{key:1,type:"warning",link:"",onClick:h((a=>Da(e.row)),["stop"])},{default:u((()=>a[50]||(a[50]=[m("设置负责人")]))),_:2},1032,["onClick"])):(o(),c("span",Be,"-"))])),_:1}),d(wl,{label:"使用人",width:"100"},{default:u((e=>[3===e.row.type||4===e.row.type?(o(),n(A,{key:0,type:"info",link:"",onClick:h((a=>(async e=>{try{F.value=e,usersDialogVisible.value=!0,locationUsersLoading.value=!0;const a=await E.getLocationUsers(e.id);a.success?Ca.value=a.data||[]:(Ca.value=[],f.error(a.message||"获取使用人列表失败"))}catch(a){Ca.value=[],f.error("获取使用人列表失败")}finally{locationUsersLoading.value=!1}})(e.row)),["stop"])},{default:u((()=>[m(i(e.row.userCount||0)+"人 ",1)])),_:2},1032,["onClick"])):(o(),c("span",Ae,"-"))])),_:1}),d(wl,{label:"操作",fixed:"right",width:"220"},{default:u((e=>[d(A,{size:"small",type:"primary",onClick:h((a=>Ja(e.row)),["stop"])},{default:u((()=>a[51]||(a[51]=[m("关联资产")]))),_:2},1032,["onClick"]),d(Il,{onCommand:a=>((e,a)=>{switch(e){case"setDepartment":Ta(a);break;case"setManager":Da(a);break;case"setUsers":Na(a)}})(a,e.row),trigger:"click"},{dropdown:u((()=>[d(Vl,null,{default:u((()=>[d(Cl,{command:"setDepartment"},{default:u((()=>a[53]||(a[53]=[m("设置部门")]))),_:1}),3===e.row.type||4===e.row.type?(o(),n(Cl,{key:0,command:"setManager"},{default:u((()=>a[54]||(a[54]=[m("设置负责人")]))),_:1})):g("",!0),3===e.row.type||4===e.row.type?(o(),n(Cl,{key:1,command:"setUsers"},{default:u((()=>a[55]||(a[55]=[m("设置使用人")]))),_:1})):g("",!0)])),_:2},1024)])),default:u((()=>[d(A,{size:"small",type:"primary"},{default:u((()=>[a[52]||(a[52]=m(" 更多")),d(kl,{class:"el-icon--right"},{default:u((()=>[d(w($))])),_:1})])),_:1})])),_:2},1032,["onCommand"])])),_:1})])),_:1},8,["data","height"])),[[zl,te.value]]),r("div",je,[d(xl,{"current-page":Ze.currentPage,"onUpdate:currentPage":a[13]||(a[13]=e=>Ze.currentPage=e),"page-size":Ze.pageSize,"onUpdate:pageSize":a[14]||(a[14]=e=>Ze.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:Ze.total,background:"",onSizeChange:Ka,onCurrentChange:qa},null,8,["current-page","page-size","total"])])])):g("",!0)])),_:1})]),d(Ul,{title:"关联资产",modelValue:oa.value,"onUpdate:modelValue":a[21]||(a[21]=e=>oa.value=e),width:"900px","append-to-body":""},{footer:u((()=>[r("div",Re,[d(A,{onClick:a[20]||(a[20]=e=>oa.value=!1)},{default:u((()=>a[56]||(a[56]=[m("取 消")]))),_:1}),d(A,{type:"primary",onClick:Xa,disabled:0===sa.value.length||!ca.value},{default:u((()=>a[57]||(a[57]=[m(" 确认关联 ")]))),_:1},8,["disabled"])])])),default:u((()=>[r("div",Me,[r("div",Ye,[d(Ea,{gutter:16},{default:u((()=>[d(Pa,{span:8},{default:u((()=>[d(ul,{modelValue:ra.value,"onUpdate:modelValue":a[15]||(a[15]=e=>ra.value=e),placeholder:"搜索资产名称/编号/SN",clearable:"",onKeyup:S(Wa,["enter"])},{append:u((()=>[d(A,{icon:w(_),onClick:Wa},null,8,["icon"])])),_:1},8,["modelValue"])])),_:1}),d(Pa,{span:8},{default:u((()=>[d(ll,{modelValue:ia.value,"onUpdate:modelValue":a[16]||(a[16]=e=>ia.value=e),placeholder:"资产类型",clearable:"",style:{width:"100%"},onChange:Wa},{default:u((()=>[(o(!0),c(v,null,p(e.assetTypes,(e=>(o(),n(Za,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),d(Pa,{span:8},{default:u((()=>[d(ll,{modelValue:ca.value,"onUpdate:modelValue":a[17]||(a[17]=e=>ca.value=e),placeholder:"关联类型",style:{width:"100%"}},{default:u((()=>[d(Za,{label:"主要位置",value:"primary"}),d(Za,{label:"次要位置",value:"secondary"}),d(Za,{label:"临时位置",value:"temporary"})])),_:1},8,["modelValue"])])),_:1})])),_:1})]),N((o(),n(hl,{ref:"unrelatedAssetTable",data:ua.value,style:{width:"100%","margin-top":"16px"},onSelectionChange:e.handleUnrelatedSelectionChange,height:"400px",border:""},{default:u((()=>[d(wl,{type:"selection",width:"55"}),d(wl,{prop:"code",label:"资产编号",width:"120"}),d(wl,{prop:"name",label:"资产名称",width:"150"}),d(wl,{prop:"type",label:"资产类型",width:"120"},{default:u((e=>{return[d(rl,{size:"small",type:(a=e.row.type,{laptop:"",desktop:"success",server:"danger",monitor:"warning",printer:"info",network:"primary",other:"info"}[a]||"info")},{default:u((()=>[m(i(e.row.typeName),1)])),_:2},1032,["type"])];var a})),_:1}),d(wl,{prop:"sn",label:"序列号",width:"150"}),d(wl,{prop:"status",label:"状态",width:"100"},{default:u((e=>{return[d(rl,{size:"small",type:(a=e.row.status,{in_use:"success",idle:"info",repairing:"warning",borrowed:"primary",scrapped:"danger"}[a]||"info")},{default:u((()=>[m(i(e.row.statusText),1)])),_:2},1032,["type"])];var a})),_:1}),d(wl,{prop:"department",label:"所属部门",width:"150"}),d(wl,{prop:"user",label:"责任人",width:"120"}),d(wl,{prop:"currentLocation",label:"当前位置",width:"180"})])),_:1},8,["data","onSelectionChange"])),[[zl,da.value]]),r("div",Fe,[d(xl,{"current-page":pa.currentPage,"onUpdate:currentPage":a[18]||(a[18]=e=>pa.currentPage=e),"page-size":pa.pageSize,"onUpdate:pageSize":a[19]||(a[19]=e=>pa.pageSize=e),"page-sizes":[10,20,50],background:!0,layout:"total, sizes, prev, pager, next",total:pa.total,onSizeChange:e.handleUnrelatedSizeChange,onCurrentChange:e.handleUnrelatedCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])])])),_:1},8,["modelValue"]),d(Ul,{modelValue:va.value,"onUpdate:modelValue":a[24]||(a[24]=e=>va.value=e),title:"关联部门",width:"500px"},{footer:u((()=>[r("span",Oe,[d(A,{onClick:a[23]||(a[23]=e=>va.value=!1)},{default:u((()=>a[60]||(a[60]=[m("取消")]))),_:1}),d(A,{type:"primary",onClick:tl,loading:ma.value},{default:u((()=>a[61]||(a[61]=[m(" 确定 ")]))),_:1},8,["loading"])])])),default:u((()=>[d(_l,{ref:"departmentForm",model:fa,rules:ya,"label-width":"100px"},{default:u((()=>[d(ol,{label:"选择部门",prop:"departmentId"},{default:u((()=>[d(ll,{modelValue:fa.departmentId,"onUpdate:modelValue":a[22]||(a[22]=e=>fa.departmentId=e),filterable:"",placeholder:"请选择部门",style:{width:"100%"}},{default:u((()=>[(o(!0),c(v,null,p(_a.value,(e=>(o(),n(Za,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),fa.departmentId?(o(),n(fl,{key:0,type:"info","show-icon":"",closable:!1},{default:u((()=>[r("div",null,[a[59]||(a[59]=r("p",null,"设置部门后，系统将自动关联该部门的负责人作为位置管理员。",-1)),Va.value?(o(),c("p",Ke,[a[58]||(a[58]=m(" 当前选择部门负责人: ")),r("strong",null,i(Va.value),1)])):(o(),c("p",qe," 当前选择部门未设置负责人 "))])])),_:1})):g("",!0)])),_:1},8,["model"])])),_:1},8,["modelValue"]),d(Ul,{modelValue:ga.value,"onUpdate:modelValue":a[28]||(a[28]=e=>ga.value=e),title:""+("manager"===xa.value?"设置管理员":"设置使用人"),width:"600px"},{footer:u((()=>[r("span",Xe,[d(A,{onClick:a[27]||(a[27]=e=>ga.value=!1)},{default:u((()=>a[66]||(a[66]=[m("取消")]))),_:1}),d(A,{type:"primary",onClick:nl,loading:wa.value},{default:u((()=>a[67]||(a[67]=[m(" 确定 ")]))),_:1},8,["loading"])])])),default:u((()=>[d(_l,{ref:"userForm",model:ha,"label-width":"100px"},{default:u((()=>[d(ol,{label:"选择人员"},{default:u((()=>[d(ll,{modelValue:ha.selectedUsers,"onUpdate:modelValue":a[25]||(a[25]=e=>ha.selectedUsers=e),filterable:"",multiple:"",placeholder:"请选择人员",style:{width:"100%"}},{default:u((()=>[(o(!0),c(v,null,p(ka.value,(e=>(o(),n(Za,{key:e.id,label:`${e.name}${e.employeeCode?" ("+e.employeeCode+")":""}`,value:e.id},{default:u((()=>[r("div",Ge,[r("div",null,[r("span",He,i(e.name),1),e.employeeCode?(o(),c("span",Je,"("+i(e.employeeCode)+")",1)):g("",!0)]),e.departmentName?(o(),c("span",Qe,i(e.departmentName),1)):g("",!0)])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),d(ol,{label:"操作模式"},{default:u((()=>[d(za,{modelValue:ha.replaceExisting,"onUpdate:modelValue":a[26]||(a[26]=e=>ha.replaceExisting=e)},{default:u((()=>[d(Tl,{label:!0},{default:u((()=>a[62]||(a[62]=[m("替换现有人员")]))),_:1}),d(Tl,{label:!1},{default:u((()=>a[63]||(a[63]=[m("追加新人员")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),Ca.value.length>0?(o(),c("div",We,[a[65]||(a[65]=r("div",{class:"current-users-title"},"当前关联人员:",-1)),d(hl,{data:Ca.value,size:"small",border:"",style:{width:"100%"}},{default:u((()=>[d(wl,{label:"姓名",prop:"name"}),d(wl,{label:"工号",prop:"employeeCode"}),d(wl,{label:"类型"},{default:u((e=>[d(rl,{type:1===e.row.userType?"success":"info"},{default:u((()=>[m(i(1===e.row.userType?"管理员":"使用人"),1)])),_:2},1032,["type"])])),_:1}),d(wl,{label:"操作",width:"80"},{default:u((e=>[d(A,{type:"danger",size:"small",text:"",onClick:a=>(async e=>{var a;try{await P.confirm(`确定要移除 ${e.name} 吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const l=await E.removeLocationUser(F.value.id,e.id);l.data&&l.data.success?(f.success("移除成功"),await fetchLocationUsers(F.value.id)):f.error((null==(a=l.data)?void 0:a.message)||"移除失败")}catch(l){"cancel"!==l&&f.error("移除关联人员失败: "+l.message)}})(e.row)},{default:u((()=>a[64]||(a[64]=[m(" 移除 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])])):g("",!0)])),_:1},8,["model"])])),_:1},8,["modelValue","title"])]),Ua.value?(o(),n(K,{key:0,visible:Ua.value,"onUpdate:visible":a[29]||(a[29]=e=>Ua.value=e),"location-id":null==(l=F.value)?void 0:l.id,"location-name":null==(t=F.value)?void 0:t.name,"current-department-id":null==(k=F.value)?void 0:k.defaultDepartmentId,onSuccess:La},null,8,["visible","location-id","location-name","current-department-id"])):g("",!0),d(oe,{visible:Ia.value,"onUpdate:visible":a[30]||(a[30]=e=>Ia.value=e),"location-id":null==(C=F.value)?void 0:C.id,"location-name":null==(z=F.value)?void 0:z.name,"user-type":xa.value,"department-info":null==(B=F.value)?void 0:B.defaultDepartmentName,onSuccess:Sa},null,8,["visible","location-id","location-name","user-type","department-info"])],64)}}},[["__scopeId","data-v-63e22cfb"]]);export{Ze as default};
