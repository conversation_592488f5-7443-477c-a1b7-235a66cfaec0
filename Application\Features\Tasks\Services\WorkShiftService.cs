// File: Application/Features/Tasks/Services/WorkShiftService.cs
// Description: 班次管理服务

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Domain.Entities.Tasks;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.Tasks.Dtos;

namespace ItAssetsSystem.Application.Features.Tasks.Services
{
    /// <summary>
    /// 班次管理服务
    /// </summary>
    public class WorkShiftService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<WorkShiftService> _logger;

        public WorkShiftService(AppDbContext context, ILogger<WorkShiftService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有班次
        /// </summary>
        public async Task<ApiResponse<List<WorkShiftDto>>> GetAllShiftsAsync()
        {
            try
            {
                var shifts = await _context.WorkShifts
                    .Where(s => s.IsActive)
                    .OrderBy(s => s.StartTime)
                    .Select(s => new WorkShiftDto
                    {
                        ShiftId = s.ShiftId,
                        ShiftName = s.ShiftName,
                        ShiftCode = s.ShiftCode,
                        ShiftType = s.ShiftType,
                        StartTime = s.StartTime,
                        EndTime = s.EndTime,
                        TaskClaimTime = s.TaskClaimTime,
                        IsOvernight = s.IsOvernight,
                        IsActive = s.IsActive,
                        Description = s.Description
                    })
                    .ToListAsync();

                return ApiResponse<List<WorkShiftDto>>.CreateSuccess(shifts, "获取班次列表成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取班次列表时发生错误");
                return ApiResponse<List<WorkShiftDto>>.CreateFail("获取班次列表失败: " + ex.Message);
            }
        }

        /// <summary>
        /// 创建班次
        /// </summary>
        public async Task<ApiResponse<WorkShiftDto>> CreateShiftAsync(CreateWorkShiftDto request, int currentUserId)
        {
            try
            {
                // 检查班次代码是否已存在
                var existingShift = await _context.WorkShifts
                    .FirstOrDefaultAsync(s => s.ShiftCode == request.ShiftCode);

                if (existingShift != null)
                {
                    return ApiResponse<WorkShiftDto>.CreateFail("班次代码已存在");
                }

                var shift = new WorkShift
                {
                    ShiftName = request.ShiftName,
                    ShiftCode = request.ShiftCode,
                    ShiftType = request.ShiftType,
                    StartTime = request.StartTime,
                    EndTime = request.EndTime,
                    TaskClaimTime = request.TaskClaimTime,
                    IsOvernight = request.IsOvernight,
                    Description = request.Description,
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now,
                    CreatedBy = currentUserId
                };

                _context.WorkShifts.Add(shift);
                await _context.SaveChangesAsync();

                var shiftDto = new WorkShiftDto
                {
                    ShiftId = shift.ShiftId,
                    ShiftName = shift.ShiftName,
                    ShiftCode = shift.ShiftCode,
                    ShiftType = shift.ShiftType,
                    StartTime = shift.StartTime,
                    EndTime = shift.EndTime,
                    TaskClaimTime = shift.TaskClaimTime,
                    IsOvernight = shift.IsOvernight,
                    IsActive = shift.IsActive,
                    Description = shift.Description
                };

                return ApiResponse<WorkShiftDto>.CreateSuccess(shiftDto, "创建班次成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建班次时发生错误");
                return ApiResponse<WorkShiftDto>.CreateFail("创建班次失败: " + ex.Message);
            }
        }

        /// <summary>
        /// 分配用户到班次
        /// </summary>
        public async Task<ApiResponse<UserShiftAssignmentDto>> AssignUserToShiftAsync(CreateUserShiftAssignmentDto request, int currentUserId)
        {
            try
            {
                // 检查用户和班次是否存在
                var userExists = await _context.Users.AnyAsync(u => u.Id == request.UserId);
                if (!userExists)
                {
                    return ApiResponse<UserShiftAssignmentDto>.CreateFail("用户不存在");
                }

                var shiftExists = await _context.WorkShifts.AnyAsync(s => s.ShiftId == request.ShiftId);
                if (!shiftExists)
                {
                    return ApiResponse<UserShiftAssignmentDto>.CreateFail("班次不存在");
                }

                // 检查是否已有有效的分配
                var existingAssignment = await _context.UserShiftAssignments
                    .FirstOrDefaultAsync(a => a.UserId == request.UserId 
                                           && a.ShiftId == request.ShiftId 
                                           && a.IsActive 
                                           && (a.ExpiryDate == null || a.ExpiryDate > DateTime.Now));

                if (existingAssignment != null)
                {
                    return ApiResponse<UserShiftAssignmentDto>.CreateFail("用户已分配到该班次");
                }

                var assignment = new UserShiftAssignment
                {
                    UserId = request.UserId,
                    ShiftId = request.ShiftId,
                    EffectiveDate = request.EffectiveDate,
                    ExpiryDate = request.ExpiryDate,
                    AssignmentType = request.AssignmentType,
                    Notes = request.Notes,
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now,
                    CreatedBy = currentUserId
                };

                _context.UserShiftAssignments.Add(assignment);
                await _context.SaveChangesAsync();

                var assignmentDto = new UserShiftAssignmentDto
                {
                    AssignmentId = assignment.AssignmentId,
                    UserId = assignment.UserId,
                    ShiftId = assignment.ShiftId,
                    EffectiveDate = assignment.EffectiveDate,
                    ExpiryDate = assignment.ExpiryDate,
                    AssignmentType = assignment.AssignmentType,
                    Notes = assignment.Notes,
                    IsActive = assignment.IsActive
                };

                return ApiResponse<UserShiftAssignmentDto>.CreateSuccess(assignmentDto, "分配用户到班次成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分配用户到班次时发生错误");
                return ApiResponse<UserShiftAssignmentDto>.CreateFail("分配用户到班次失败: " + ex.Message);
            }
        }

        /// <summary>
        /// 获取用户当前班次
        /// </summary>
        public async Task<ApiResponse<WorkShiftDto?>> GetUserCurrentShiftAsync(int userId)
        {
            try
            {
                var now = DateTime.Now;
                var assignment = await _context.UserShiftAssignments
                    .Include(a => a.WorkShift)
                    .FirstOrDefaultAsync(a => a.UserId == userId
                                           && a.IsActive
                                           && a.EffectiveDate <= now
                                           && (a.ExpiryDate == null || a.ExpiryDate > now));

                if (assignment?.WorkShift == null)
                {
                    // 如果用户未分配班次，自动分配默认白班
                    _logger.LogInformation("用户 {UserId} 未分配班次，自动分配默认白班", userId);
                    var defaultShift = await GetDefaultShiftAsync();
                    if (defaultShift != null)
                    {
                        await AssignUserToShiftAsync(userId, defaultShift.ShiftId);
                        return ApiResponse<WorkShiftDto?>.CreateSuccess(defaultShift, "已自动分配默认班次");
                    }
                    return ApiResponse<WorkShiftDto?>.CreateFail("用户未分配班次且无可用默认班次");
                }

                var shiftDto = new WorkShiftDto
                {
                    ShiftId = assignment.WorkShift.ShiftId,
                    ShiftName = assignment.WorkShift.ShiftName,
                    ShiftCode = assignment.WorkShift.ShiftCode,
                    ShiftType = assignment.WorkShift.ShiftType,
                    StartTime = assignment.WorkShift.StartTime,
                    EndTime = assignment.WorkShift.EndTime,
                    TaskClaimTime = assignment.WorkShift.TaskClaimTime,
                    IsOvernight = assignment.WorkShift.IsOvernight,
                    IsActive = assignment.WorkShift.IsActive,
                    Description = assignment.WorkShift.Description
                };

                return ApiResponse<WorkShiftDto?>.CreateSuccess(shiftDto, "获取用户当前班次成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户当前班次时发生错误");
                return ApiResponse<WorkShiftDto?>.CreateFail("获取用户当前班次失败: " + ex.Message);
            }
        }

        /// <summary>
        /// 获取当前时间应该生成任务领取按钮的班次
        /// </summary>
        public async Task<List<WorkShift>> GetShiftsForTaskClaimGenerationAsync()
        {
            try
            {
                var now = DateTime.Now;
                var currentTime = now.TimeOfDay;

                var shifts = await _context.WorkShifts
                    .Where(s => s.IsActive)
                    .ToListAsync();

                var shiftsToGenerate = new List<WorkShift>();

                foreach (var shift in shifts)
                {
                    // 检查是否到了该班次的任务领取时间
                    if (IsTimeForTaskClaim(currentTime, shift.TaskClaimTime, shift.IsOvernight))
                    {
                        shiftsToGenerate.Add(shift);
                    }
                }

                return shiftsToGenerate;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取需要生成任务领取的班次时发生错误");
                return new List<WorkShift>();
            }
        }

        /// <summary>
        /// 检查是否到了任务领取时间
        /// </summary>
        private bool IsTimeForTaskClaim(TimeSpan currentTime, TimeSpan claimTime, bool isOvernight)
        {
            // 允许5分钟的误差
            var tolerance = TimeSpan.FromMinutes(5);
            
            if (isOvernight)
            {
                // 跨天班次的处理逻辑
                if (claimTime < TimeSpan.FromHours(12)) // 如果领取时间在上午，说明是第二天
                {
                    return Math.Abs((currentTime - claimTime).TotalMinutes) <= tolerance.TotalMinutes;
                }
                else // 如果领取时间在下午/晚上，说明是当天
                {
                    return Math.Abs((currentTime - claimTime).TotalMinutes) <= tolerance.TotalMinutes;
                }
            }
            else
            {
                // 普通班次
                return Math.Abs((currentTime - claimTime).TotalMinutes) <= tolerance.TotalMinutes;
            }
        }

        /// <summary>
        /// 获取默认班次（白班）
        /// </summary>
        private async Task<WorkShiftDto?> GetDefaultShiftAsync()
        {
            try
            {
                var defaultShift = await _context.WorkShifts
                    .Where(s => s.IsActive && s.ShiftCode == "DAY")
                    .FirstOrDefaultAsync();

                if (defaultShift == null)
                {
                    // 如果没有白班，获取第一个可用班次
                    defaultShift = await _context.WorkShifts
                        .Where(s => s.IsActive)
                        .FirstOrDefaultAsync();
                }

                if (defaultShift == null)
                {
                    return null;
                }

                return new WorkShiftDto
                {
                    ShiftId = defaultShift.ShiftId,
                    ShiftName = defaultShift.ShiftName,
                    ShiftCode = defaultShift.ShiftCode,
                    ShiftType = defaultShift.ShiftType,
                    StartTime = defaultShift.StartTime,
                    EndTime = defaultShift.EndTime,
                    TaskClaimTime = defaultShift.TaskClaimTime,
                    IsOvernight = defaultShift.IsOvernight,
                    IsActive = defaultShift.IsActive,
                    Description = defaultShift.Description
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取默认班次时发生错误");
                return null;
            }
        }

        /// <summary>
        /// 自动为用户分配班次
        /// </summary>
        private async Task<bool> AssignUserToShiftAsync(int userId, long shiftId)
        {
            try
            {
                var assignment = new UserShiftAssignment
                {
                    UserId = userId,
                    ShiftId = shiftId,
                    EffectiveDate = DateTime.Today,
                    ExpiryDate = null, // 永久有效
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now,
                    CreatedBy = userId, // 系统自动分配
                    UpdatedBy = userId
                };

                _context.UserShiftAssignments.Add(assignment);
                await _context.SaveChangesAsync();

                _logger.LogInformation("已为用户 {UserId} 自动分配班次 {ShiftId}", userId, shiftId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "为用户 {UserId} 分配班次 {ShiftId} 时发生错误", userId, shiftId);
                return false;
            }
        }

        /// <summary>
        /// 获取班次分配的用户列表
        /// </summary>
        public async Task<ApiResponse<List<UserShiftAssignmentDto>>> GetShiftAssignmentsAsync(long shiftId)
        {
            try
            {
                var assignments = await _context.UserShiftAssignments
                    .Include(a => a.User)
                    .Include(a => a.WorkShift)
                    .Where(a => a.ShiftId == shiftId && a.IsActive)
                    .OrderBy(a => a.EffectiveDate)
                    .ToListAsync();

                var assignmentDtos = assignments.Select(a => new UserShiftAssignmentDto
                {
                    AssignmentId = a.AssignmentId,
                    UserId = a.UserId,
                    UserName = a.User?.Name ?? "未知用户",
                    ShiftId = a.ShiftId,
                    ShiftName = a.WorkShift?.ShiftName ?? "未知班次",
                    EffectiveDate = a.EffectiveDate,
                    ExpiryDate = a.ExpiryDate,
                    IsActive = a.IsActive,
                    AssignmentType = a.AssignmentType,
                    Notes = a.Notes
                }).ToList();

                return ApiResponse<List<UserShiftAssignmentDto>>.CreateSuccess(assignmentDtos, "获取班次分配用户列表成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取班次 {ShiftId} 分配用户列表时发生错误", shiftId);
                return ApiResponse<List<UserShiftAssignmentDto>>.CreateFail("获取班次分配用户列表失败");
            }
        }

        /// <summary>
        /// 移除用户班次分配
        /// </summary>
        public async Task<ApiResponse<object>> RemoveUserShiftAssignmentAsync(long assignmentId, int currentUserId)
        {
            try
            {
                var assignment = await _context.UserShiftAssignments
                    .FirstOrDefaultAsync(a => a.AssignmentId == assignmentId);

                if (assignment == null)
                {
                    return ApiResponse<object>.CreateFail("班次分配记录不存在");
                }

                // 软删除：设置为非活跃状态
                assignment.IsActive = false;
                assignment.UpdatedAt = DateTime.Now;
                assignment.UpdatedBy = currentUserId;

                await _context.SaveChangesAsync();

                _logger.LogInformation("用户 {CurrentUserId} 移除了班次分配 {AssignmentId}", currentUserId, assignmentId);
                return ApiResponse<object>.CreateSuccess(null, "移除班次分配成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移除班次分配 {AssignmentId} 时发生错误", assignmentId);
                return ApiResponse<object>.CreateFail("移除班次分配失败");
            }
        }
    }
}
