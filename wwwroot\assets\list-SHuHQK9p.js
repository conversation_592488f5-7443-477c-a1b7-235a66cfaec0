import{aj as e,_ as a,r as t,a5 as l,m as r,a8 as o,b as s,d as n,e as u,w as i,f as c,am as d,V as p,a as m,ag as v,o as h,A as g,F as y,h as w,v as b,ax as f,af as P,Y as $,t as _,a9 as V,aA as x,ab as S,aF as k,K as z,P as C,i as A,a1 as Q}from"./index-C7OOw0MO.js";const I="/v2/purchase",U={getPurchaseList:a=>e.get(I,a),getPurchaseById:a=>e.get(`${I}/${a}`),createPurchase:a=>e.post(I,a),updatePurchase:(a,t)=>e.put(`${I}/${a}`,t),deletePurchase:a=>e.delete(`${I}/${a}`),getSupplierList:a=>e.get("/suppliers",a),getSupplierById:a=>e.get(`/suppliers/${a}`),createSupplier:a=>e.post("/suppliers",a),updateSupplier:(a,t)=>e.put(`/suppliers/${a}`,t),deleteSupplier:a=>e.delete(`/suppliers/${a}`),submitPurchaseForApproval:a=>e.put(`${I}/${a}/submit`),approvePurchase:(a,t)=>e.put(`${I}/${a}/approve`,t),rejectPurchase:(a,t)=>e.put(`${I}/${a}/reject`,t),confirmPurchaseOrder:(a,t)=>e.put(`${I}/${a}/order`,t),confirmPurchaseReceived:(a,t)=>e.put(`${I}/${a}/receive`,t),confirmPurchaseStorage:(a,t)=>e.put(`${I}/${a}/storage`,t),completePurchase:a=>e.put(`${I}/${a}/complete`),cancelPurchase:(a,t)=>e.put(`${I}/${a}/cancel`,t),getPurchaseApprovalHistory:(a,t)=>e.get(`${I}/${a}/approval-history`,t),uploadPurchaseAttachment:(a,t)=>e.upload(`${I}/${a}/attachments`,t),getPurchaseAttachments:(a,t)=>e.get(`${I}/${a}/attachments`,t),deletePurchaseAttachment:(a,t)=>e.delete(`${I}/${a}/attachments/${t}`),exportPurchases:a=>e.download(`${I}/export`,a,"purchases.xlsx"),getPurchaseStatistics:a=>e.get(`${I}/statistics`,a),getSuppliersV2:()=>e.get(`${I}/suppliers`),getPurchasableAssetTypes:()=>e.get(`${I}/asset-types`),processDeliveredItems:(a,t)=>e.post(`${I}/${a}/process-items`,t),approvePurchase:a=>e.put(`${I}/${a}/approve`),rejectPurchase:(a,t)=>e.put(`${I}/${a}/reject`,t),completePurchase:(a,t)=>e.put(`${I}/${a}/complete`,t)},L={class:"purchase-list-container"},T={class:"page-header"},j={class:"page-actions"},B={class:"filter-container"},q={class:"pagination-container"},R={class:"receive-dialog-content"},Y={class:"order-info"},D={class:"items-conversion"},F={class:"conversion-tips"},M={class:"dialog-footer"},H=a({__name:"list",setup(e){const a=t(!1),I=t("all"),H=t([]),K=t(null);t([{id:1,name:"联想"},{id:2,name:"惠普"},{id:3,name:"戴尔"},{id:4,name:"华为"}]);const O=l({currentPage:1,pageSize:10,total:0}),E=l({code:"",name:"",type:"",status:"",timeRange:[]}),G=[{label:"新设备",value:"new_device"},{label:"更换设备",value:"replacement"},{label:"配件",value:"accessories"},{label:"软件",value:"software"},{label:"服务",value:"service"}],J=[{label:"待审批",value:"pending"},{label:"已审批",value:"approved"},{label:"已采购",value:"purchased"},{label:"已入库",value:"received"},{label:"已拒绝",value:"rejected"},{label:"已取消",value:"cancelled"}];r((()=>{N()}));const N=async()=>{var e,t;a.value=!0;const l={page:O.currentPage,pageSize:O.pageSize,code:E.code,name:E.name,type:E.type,status:"all"===I.value?E.status:I.value,startTime:null==(e=E.timeRange)?void 0:e[0],endTime:null==(t=E.timeRange)?void 0:t[1]};try{const e=await U.getPurchaseList(l);e.success?(H.value=e.data.items||e.data||[],O.total=e.data.total||e.data.length||0):(o.error(e.message||"获取采购列表失败"),H.value=[],O.total=0)}catch(r){o.error("获取采购列表失败"),H.value=[],O.total=0}finally{a.value=!1}},W=()=>{O.currentPage=1,N()},X=()=>{O.currentPage=1,N()},Z=()=>{E.code="",E.name="",E.type="",E.status="",E.timeRange=[],O.currentPage=1,N()},ee=e=>{O.pageSize=e,N()},ae=e=>{O.currentPage=e,N()},te=t(!1),le=t(null),re=l({items:[]}),oe=e=>{le.value=e,re.items=e.items.map((e=>({...e,toAssetQuantity:0,toSparePartQuantity:e.quantity,assetLocationId:null,sparePartLocationId:null}))),te.value=!0},se=async()=>{try{for(const t of re.items){if(t.toAssetQuantity+t.toSparePartQuantity!==t.quantity)return void o.warning(`${t.name} 的转化数量总和必须等于采购数量`);if(t.toAssetQuantity>0&&!t.assetLocationId)return void o.warning(`${t.name} 转为资产时必须选择资产位置`);if(t.toSparePartQuantity>0&&!t.sparePartLocationId)return void o.warning(`${t.name} 转为备件时必须选择备件库位`)}const e=re.items.map((e=>({purchaseItemId:e.id,toSparePartQuantity:e.toSparePartQuantity,sparePartLocationId:e.sparePartLocationId,toAssetQuantity:e.toAssetQuantity,assetLocationId:e.assetLocationId}))),a=await U.processDeliveredItems(le.value.id,e);a.success?(o.success("入库转化成功"),te.value=!1,N()):o.error(a.message||"入库转化失败")}catch(e){o.error("入库转化失败："+e.message)}},ne=()=>{o.success("开始导出数据，请稍候...")},ue=()=>{o.info("打开新建采购页面")},ie=e=>({new_device:"新设备",replacement:"更换设备",accessories:"配件",software:"软件",service:"服务"}[e]||"未知"),ce=e=>({pending:"待审批",approved:"已审批",purchased:"已采购",received:"已入库",rejected:"已拒绝",cancelled:"已取消"}[e]||"未知");return(e,t)=>{const l=m("el-button"),r=m("el-input"),de=m("el-form-item"),pe=m("el-option"),me=m("el-select"),ve=m("el-date-picker"),he=m("el-form"),ge=m("el-card"),ye=m("el-tab-pane"),we=m("el-tabs"),be=m("el-table-column"),fe=m("el-tag"),Pe=m("el-table"),$e=m("el-pagination"),_e=m("el-input-number"),Ve=m("el-alert"),xe=m("el-dialog"),Se=v("loading");return h(),s("div",L,[n("div",T,[t[12]||(t[12]=n("h2",{class:"page-title"},"采购列表",-1)),n("div",j,[u(l,{type:"primary",onClick:ue,icon:c(d)},{default:i((()=>t[10]||(t[10]=[g(" 新建采购 ")]))),_:1},8,["icon"]),u(l,{type:"primary",onClick:ne,icon:c(p)},{default:i((()=>t[11]||(t[11]=[g(" 导出数据 ")]))),_:1},8,["icon"])])]),u(ge,{class:"filter-card"},{default:i((()=>[n("div",B,[u(he,{inline:!0,model:E,class:"filter-form"},{default:i((()=>[u(de,{label:"采购单号"},{default:i((()=>[u(r,{modelValue:E.code,"onUpdate:modelValue":t[0]||(t[0]=e=>E.code=e),placeholder:"采购单号",clearable:""},null,8,["modelValue"])])),_:1}),u(de,{label:"采购名称"},{default:i((()=>[u(r,{modelValue:E.name,"onUpdate:modelValue":t[1]||(t[1]=e=>E.name=e),placeholder:"采购名称",clearable:""},null,8,["modelValue"])])),_:1}),u(de,{label:"采购类型"},{default:i((()=>[u(me,{modelValue:E.type,"onUpdate:modelValue":t[2]||(t[2]=e=>E.type=e),placeholder:"全部类型",clearable:""},{default:i((()=>[(h(),s(y,null,w(G,(e=>u(pe,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),u(de,{label:"状态"},{default:i((()=>[u(me,{modelValue:E.status,"onUpdate:modelValue":t[3]||(t[3]=e=>E.status=e),placeholder:"全部状态",clearable:""},{default:i((()=>[(h(),s(y,null,w(J,(e=>u(pe,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),u(de,{label:"采购时间"},{default:i((()=>[u(ve,{modelValue:E.timeRange,"onUpdate:modelValue":t[4]||(t[4]=e=>E.timeRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),u(de,null,{default:i((()=>[u(l,{type:"primary",onClick:X,icon:c(b)},{default:i((()=>t[13]||(t[13]=[g(" 搜索 ")]))),_:1},8,["icon"]),u(l,{onClick:Z,icon:c(f)},{default:i((()=>t[14]||(t[14]=[g(" 重置 ")]))),_:1},8,["icon"])])),_:1})])),_:1},8,["model"])])])),_:1}),u(ge,{class:"data-card"},{default:i((()=>[u(we,{modelValue:I.value,"onUpdate:modelValue":t[5]||(t[5]=e=>I.value=e),onTabClick:W},{default:i((()=>[u(ye,{label:"全部",name:"all"}),u(ye,{label:"待审批",name:"pending"}),u(ye,{label:"待采购",name:"approved"}),u(ye,{label:"已采购",name:"purchased"}),u(ye,{label:"已入库",name:"received"})])),_:1},8,["modelValue"]),P((h(),$(Pe,{ref_key:"purchaseTable",ref:K,data:H.value,border:"",style:{width:"100%"}},{default:i((()=>[u(be,{prop:"code",label:"采购单号",width:"150",sortable:""}),u(be,{prop:"name",label:"采购名称","min-width":"180","show-overflow-tooltip":""}),u(be,{prop:"type",label:"类型",width:"100"},{default:i((e=>{return[u(fe,{type:(a=e.row.type,{new_device:"primary",replacement:"success",accessories:"info",software:"warning",service:""}[a]||""),size:"small"},{default:i((()=>[g(_(ie(e.row.type)),1)])),_:2},1032,["type"])];var a})),_:1}),u(be,{prop:"status",label:"状态",width:"120"},{default:i((e=>{return[u(fe,{type:(a=e.row.status,{pending:"info",approved:"success",purchased:"warning",received:"primary",rejected:"danger",cancelled:"danger"}[a]||""),size:"small"},{default:i((()=>[g(_(ce(e.row.status)),1)])),_:2},1032,["type"])];var a})),_:1}),u(be,{prop:"amount",label:"金额",width:"120"},{default:i((e=>[g(" ￥"+_(e.row.amount.toLocaleString()),1)])),_:1}),u(be,{prop:"quantity",label:"数量",width:"80"},{default:i((e=>[g(_(e.row.quantity)+"件 ",1)])),_:1}),u(be,{prop:"applicant",label:"申请人",width:"100"}),u(be,{prop:"applyTime",label:"申请时间",width:"180",sortable:""}),u(be,{prop:"approver",label:"审批人",width:"100"}),u(be,{prop:"purchaseTime",label:"采购时间",width:"180",sortable:""}),u(be,{prop:"vendor",label:"供应商",width:"150","show-overflow-tooltip":""}),u(be,{prop:"receiveTime",label:"入库时间",width:"180",sortable:""}),u(be,{label:"操作",width:"220",fixed:"right"},{default:i((e=>[u(l,{type:"primary",text:"",size:"small",onClick:a=>{return t=e.row,void o.info(`查看采购单详情：${t.code}`);var t},icon:c(x)},{default:i((()=>t[15]||(t[15]=[g(" 详情 ")]))),_:2},1032,["onClick","icon"]),"pending"===e.row.status?(h(),s(y,{key:0},[u(l,{type:"success",text:"",size:"small",onClick:a=>{return t=e.row,void Q.confirm(`确认审批通过采购单"${t.name}"吗？`,"审批确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{const e=await U.approvePurchase(t.id);e.success?(o.success("审批通过成功"),N()):o.error(e.message||"审批失败")}catch(e){o.error("审批失败")}})).catch((()=>{}));var t},icon:c(S)},{default:i((()=>t[16]||(t[16]=[g(" 审批 ")]))),_:2},1032,["onClick","icon"]),u(l,{type:"danger",text:"",size:"small",onClick:a=>{return t=e.row,void Q.prompt("请输入拒绝原因","拒绝理由",{confirmButtonText:"确定",cancelButtonText:"取消",inputType:"textarea",inputPlaceholder:"请输入拒绝理由..."}).then((async({value:e})=>{if(e)try{const a=await U.rejectPurchase(t.id,{reason:e});a.success?(o.success("已拒绝该采购申请"),N()):o.error(a.message||"拒绝失败")}catch(a){o.error("拒绝失败")}else o.warning("请输入拒绝理由")})).catch((()=>{}));var t},icon:c(k)},{default:i((()=>t[17]||(t[17]=[g(" 拒绝 ")]))),_:2},1032,["onClick","icon"])],64)):V("",!0),"approved"===e.row.status?(h(),$(l,{key:1,type:"success",text:"",size:"small",onClick:a=>{return t=e.row,void Q.prompt("请输入供应商信息","完成采购",{confirmButtonText:"确定",cancelButtonText:"取消",inputPlaceholder:"请输入供应商名称"}).then((async({value:e})=>{if(e)try{const a=await U.completePurchase(t.id,{vendor:e});a.success?(o.success("采购完成"),N()):o.error(a.message||"完成采购失败")}catch(a){o.error("完成采购失败")}else o.warning("请输入供应商信息")})).catch((()=>{}));var t},icon:c(z)},{default:i((()=>t[18]||(t[18]=[g(" 完成采购 ")]))),_:2},1032,["onClick","icon"])):V("",!0),"purchased"===e.row.status?(h(),$(l,{key:2,type:"warning",text:"",size:"small",onClick:a=>{return t=e.row,void oe(t);var t},icon:c(C)},{default:i((()=>t[19]||(t[19]=[g(" 入库 ")]))),_:2},1032,["onClick","icon"])):V("",!0)])),_:1})])),_:1},8,["data"])),[[Se,a.value]]),n("div",q,[u($e,{"current-page":O.currentPage,"onUpdate:currentPage":t[6]||(t[6]=e=>O.currentPage=e),"page-size":O.pageSize,"onUpdate:pageSize":t[7]||(t[7]=e=>O.pageSize=e),"page-sizes":[10,20,50,100],background:!0,layout:"total, sizes, prev, pager, next, jumper",total:O.total,onSizeChange:ee,onCurrentChange:ae},null,8,["current-page","page-size","total"])])])),_:1}),u(xe,{modelValue:te.value,"onUpdate:modelValue":t[9]||(t[9]=e=>te.value=e),title:"采购物品入库转化",width:"80%","close-on-click-modal":!1},{footer:i((()=>[n("span",M,[u(l,{onClick:t[8]||(t[8]=e=>te.value=!1)},{default:i((()=>t[28]||(t[28]=[g("取消")]))),_:1}),u(l,{type:"primary",onClick:se},{default:i((()=>t[29]||(t[29]=[g("确认入库")]))),_:1})])])),default:i((()=>{var e,a,l;return[n("div",R,[n("div",Y,[t[23]||(t[23]=n("h4",null,"采购单信息",-1)),n("p",null,[t[20]||(t[20]=n("strong",null,"采购单号：",-1)),g(_(null==(e=le.value)?void 0:e.code),1)]),n("p",null,[t[21]||(t[21]=n("strong",null,"采购名称：",-1)),g(_(null==(a=le.value)?void 0:a.name),1)]),n("p",null,[t[22]||(t[22]=n("strong",null,"供应商：",-1)),g(_(null==(l=le.value)?void 0:l.vendor),1)])]),n("div",D,[t[26]||(t[26]=n("h4",null,"物品转化设置",-1)),u(Pe,{data:re.items,border:"",style:{width:"100%"}},{default:i((()=>[u(be,{prop:"name",label:"物品名称",width:"150"}),u(be,{prop:"model",label:"型号规格",width:"150"}),u(be,{prop:"quantity",label:"采购数量",width:"100",align:"center"}),u(be,{label:"转为资产",width:"200",align:"center"},{default:i((e=>[u(_e,{modelValue:e.row.toAssetQuantity,"onUpdate:modelValue":a=>e.row.toAssetQuantity=a,min:0,max:e.row.quantity,size:"small",style:{width:"80px"}},null,8,["modelValue","onUpdate:modelValue","max"]),t[24]||(t[24]=n("br",null,null,-1)),u(me,{modelValue:e.row.assetLocationId,"onUpdate:modelValue":a=>e.row.assetLocationId=a,placeholder:"选择位置",size:"small",style:{width:"100%","margin-top":"5px"},disabled:0===e.row.toAssetQuantity},{default:i((()=>[u(pe,{label:"办公室A",value:1}),u(pe,{label:"办公室B",value:2}),u(pe,{label:"会议室",value:3}),u(pe,{label:"机房",value:4})])),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])])),_:1}),u(be,{label:"转为备件",width:"200",align:"center"},{default:i((e=>[u(_e,{modelValue:e.row.toSparePartQuantity,"onUpdate:modelValue":a=>e.row.toSparePartQuantity=a,min:0,max:e.row.quantity,size:"small",style:{width:"80px"}},null,8,["modelValue","onUpdate:modelValue","max"]),t[25]||(t[25]=n("br",null,null,-1)),u(me,{modelValue:e.row.sparePartLocationId,"onUpdate:modelValue":a=>e.row.sparePartLocationId=a,placeholder:"选择库位",size:"small",style:{width:"100%","margin-top":"5px"},disabled:0===e.row.toSparePartQuantity},{default:i((()=>[u(pe,{label:"备件库A区",value:1}),u(pe,{label:"备件库B区",value:2}),u(pe,{label:"备件库C区",value:3})])),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])])),_:1}),u(be,{label:"剩余数量",width:"100",align:"center"},{default:i((e=>[n("span",{class:A({"text-danger":e.row.quantity-e.row.toAssetQuantity-e.row.toSparePartQuantity!=0})},_(e.row.quantity-e.row.toAssetQuantity-e.row.toSparePartQuantity),3)])),_:1})])),_:1},8,["data"])]),n("div",F,[u(Ve,{title:"转化说明",type:"info",closable:!1,"show-icon":""},{default:i((()=>t[27]||(t[27]=[n("ul",null,[n("li",null,"每个物品的转化数量总和必须等于采购数量"),n("li",null,"转为资产：物品将作为固定资产进行管理，生成资产编号"),n("li",null,"转为备件：物品将进入备品备件库存，用于维修和更换"),n("li",null,"可以同时转化为资产和备件，按需分配数量")],-1)]))),_:1})])])]})),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-5e16a067"]]);export{H as default};
