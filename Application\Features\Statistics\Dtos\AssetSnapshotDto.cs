using System;
using System.Collections.Generic;

namespace ItAssetsSystem.Application.Features.Statistics.Dtos
{
    /// <summary>
    /// Asset snapshot data transfer object
    /// </summary>
    public class AssetSnapshotDto
    {
        /// <summary>
        /// Snapshot ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// Date of the snapshot
        /// </summary>
        public DateTime SnapshotDate { get; set; }

        /// <summary>
        /// Total asset count
        /// </summary>
        public int TotalAssets { get; set; }

        /// <summary>
        /// Assets by status
        /// </summary>
        public Dictionary<string, int> AssetsByStatus { get; set; } = new();

        /// <summary>
        /// Assets by type
        /// </summary>
        public Dictionary<string, int> AssetsByType { get; set; } = new();

        /// <summary>
        /// Assets by department
        /// </summary>
        public Dictionary<string, int> AssetsByDepartment { get; set; } = new();

        /// <summary>
        /// Total asset value
        /// </summary>
        public decimal TotalValue { get; set; }

        /// <summary>
        /// Average asset value
        /// </summary>
        public decimal AverageValue { get; set; }

        /// <summary>
        /// Additional snapshot metadata
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();
    }
}