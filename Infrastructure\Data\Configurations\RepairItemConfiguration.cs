// File: Infrastructure/Data/Configurations/RepairItemConfiguration.cs
// Description: RepairItem实体配置

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ItAssetsSystem.Domain.Entities;

namespace ItAssetsSystem.Infrastructure.Data.Configurations;

/// <summary>
/// RepairItem实体配置
/// </summary>
public class RepairItemConfiguration : IEntityTypeConfiguration<RepairItem>
{
    public void Configure(EntityTypeBuilder<RepairItem> builder)
    {
        builder.ToTable("RepairItems");
        builder.HasKey(e => e.Id);
        
        builder.Property(e => e.Id)
            .HasColumnName("Id")
            .ValueGeneratedOnAdd();
            
        builder.Property(e => e.RepairOrderId)
            .HasColumnName("RepairOrderId")
            .IsRequired();
            
        builder.Property(e => e.AssetId)
            .HasColumnName("AssetId")
            .IsRequired();
            
        builder.Property(e => e.FaultRecordId)
            .HasColumnName("FaultRecordId");
            
        builder.Property(e => e.Description)
            .HasColumnName("Description")
            .HasMaxLength(500);
            
        builder.Property(e => e.RepairCost)
            .HasColumnName("RepairCost")
            .HasColumnType("decimal(18,2)");
            
        builder.Property(e => e.RepairStatus)
            .HasColumnName("RepairStatus")
            .IsRequired();
            
        builder.Property(e => e.RepairResult)
            .HasColumnName("RepairResult")
            .HasMaxLength(500);

        // 索引
        builder.HasIndex(e => e.RepairOrderId)
            .HasDatabaseName("IX_RepairItems_RepairOrderId");
            
        builder.HasIndex(e => e.AssetId)
            .HasDatabaseName("IX_RepairItems_AssetId");
            
        builder.HasIndex(e => e.FaultRecordId)
            .HasDatabaseName("IX_RepairItems_FaultRecordId");
            
        builder.HasIndex(e => e.RepairStatus)
            .HasDatabaseName("IX_RepairItems_RepairStatus");

        // 关系配置
        builder.HasOne(e => e.RepairOrder)
            .WithMany(ro => ro.RepairItems)
            .HasForeignKey(e => e.RepairOrderId)
            .OnDelete(DeleteBehavior.Cascade)
            .HasConstraintName("FK_RepairItems_RepairOrders_RepairOrderId");

        builder.HasOne(e => e.Asset)
            .WithMany()
            .HasForeignKey(e => e.AssetId)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("FK_RepairItems_Assets_AssetId");

        builder.HasOne(e => e.FaultRecord)
            .WithMany()
            .HasForeignKey(e => e.FaultRecordId)
            .OnDelete(DeleteBehavior.SetNull)
            .HasConstraintName("FK_RepairItems_FaultRecords_FaultRecordId");
    }
}
