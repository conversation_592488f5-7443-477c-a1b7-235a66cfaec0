import{_ as a,j as e,r as l,p as t,m as n,b as s,d as u,e as o,f as r,w as c,bn as v,A as d,t as i,bo as p,o as m,a9 as g}from"./index-C7OOw0MO.js";const f={class:"avatar-test-page"},_={key:0},y={key:1},A={key:2},b={key:0,src:"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"},I={class:"debug-info"},S=a({__name:"AvatarTestPage",setup(a){const S=e(),k=l(""),U=l(void 0),h=l(document.location.origin),T=()=>{const a=S.computedAvatarUrl;k.value=a};t((()=>S.computedAvatarUrl),(a=>{k.value=a}),{immediate:!0}),n((async()=>{if(!S.isLogin&&S.token)try{await S.getInfo()}catch(a){}T()}));const L=()=>{T()};return(a,e)=>{var l;return m(),s("div",f,[e[11]||(e[11]=u("h1",null,"用户头像测试页面",-1)),e[12]||(e[12]=u("p",null,"此页面专门用于测试用户头像的显示功能。",-1)),r(S).pending?(m(),s("div",_,e[0]||(e[0]=[u("p",null,"正在加载用户信息...",-1)]))):r(S).isLogin?(m(),s("div",A,[e[9]||(e[9]=u("h2",null,"头像预览:",-1)),o(r(v),{size:150,src:k.value,class:"test-avatar"},{default:c((()=>[k.value?g("",!0):(m(),s("img",b))])),_:1},8,["src"]),u("div",I,[e[8]||(e[8]=u("h3",null,"调试信息:",-1)),u("p",null,[e[2]||(e[2]=u("code",null,"userStore.userInfo.value?.avatar",-1)),d(": "+i(null==(l=r(S).userInfo.value)?void 0:l.avatar),1)]),u("p",null,[e[3]||(e[3]=u("code",null,"userStore.avatar.value",-1)),d(": "+i(r(S).avatar.value),1)]),u("p",null,[e[4]||(e[4]=u("code",null,"userStore.computedAvatarUrl",-1)),d(" (直接从 store 获取): "+i(r(S).computedAvatarUrl),1)]),u("p",null,[e[5]||(e[5]=u("code",null,"avatarUrlToDisplay",-1)),d(" (在组件中计算得到): "+i(k.value),1)]),u("p",null,[e[6]||(e[6]=u("code",null,"VITE_STATIC_FILES_BASE_URL",-1)),d(": "+i(U.value),1)]),u("p",null,[e[7]||(e[7]=u("code",null,"document.location.origin",-1)),d(": "+i(h.value),1)])])])):(m(),s("div",y,e[1]||(e[1]=[u("p",null,"用户未登录，无法显示头像。",-1)]))),o(r(p),{onClick:L,style:{"margin-top":"20px"}},{default:c((()=>e[10]||(e[10]=[d("强制刷新Store信息")]))),_:1})])}}},[["__scopeId","data-v-32ea68a0"]]);export{S as default};
