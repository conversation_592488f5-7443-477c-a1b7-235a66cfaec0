using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ItAssetsSystem.Services.Interfaces;
using ItAssetsSystem.Models.DTOs.Gamification;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Core.Services;

namespace ItAssetsSystem.Api.V2.Controllers
{
    /// <summary>
    /// 游戏化系统API控制器
    /// </summary>
    [ApiController]
    [Route("api/v2/gamification")]
    [Authorize]
    public class GamificationController : ControllerBase
    {
        private readonly IGamificationService _gamificationService;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<GamificationController> _logger;

        public GamificationController(
            IGamificationService gamificationService,
            ICurrentUserService currentUserService,
            ILogger<GamificationController> logger)
        {
            _gamificationService = gamificationService;
            _currentUserService = currentUserService;
            _logger = logger;
        }

        /// <summary>
        /// 获取当前用户游戏化统计信息
        /// </summary>
        /// <returns>用户游戏化统计信息</returns>
        [HttpGet("stats")]
        public async Task<IActionResult> GetUserStats()
        {
            try
            {
                var currentUserId = _currentUserService.GetCurrentUserId();
                if (!currentUserId.HasValue)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                var stats = await _gamificationService.GetUserStatsAsync(currentUserId.Value);
                if (stats == null)
                {
                    return NotFound(ApiResponse<object>.CreateFail("用户游戏化数据不存在"));
                }

                return Ok(ApiResponse<GamificationUserStatsDto>.CreateSuccess(stats, "获取用户游戏化统计信息成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户游戏化统计信息时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("获取用户游戏化统计信息失败"));
            }
        }

        /// <summary>
        /// 获取指定用户游戏化统计信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户游戏化统计信息</returns>
        [HttpGet("stats/{userId}")]
        public async Task<IActionResult> GetUserStatsById(int userId)
        {
            try
            {
                var stats = await _gamificationService.GetUserStatsAsync(userId);
                if (stats == null)
                {
                    return NotFound(ApiResponse<object>.CreateFail("用户游戏化数据不存在"));
                }

                return Ok(ApiResponse<GamificationUserStatsDto>.CreateSuccess(stats, "获取用户游戏化统计信息成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户 {UserId} 游戏化统计信息时发生错误", userId);
                return StatusCode(500, ApiResponse<object>.CreateFail("获取用户游戏化统计信息失败"));
            }
        }

        /// <summary>
        /// 获取用户每日任务统计
        /// </summary>
        /// <param name="date">日期 (可选，默认今天)</param>
        /// <returns>每日任务统计</returns>
        [HttpGet("daily-stats")]
        public async Task<IActionResult> GetDailyTaskStats([FromQuery] DateTime? date = null)
        {
            try
            {
                var currentUserId = _currentUserService.GetCurrentUserId();
                if (!currentUserId.HasValue)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                var targetDate = date ?? DateTime.Today;
                var stats = await _gamificationService.GetDailyTaskStatsAsync(currentUserId.Value, targetDate);

                return Ok(ApiResponse<DailyTaskStatsDto>.CreateSuccess(stats, "获取每日任务统计成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取每日任务统计时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("获取每日任务统计失败"));
            }
        }

        /// <summary>
        /// 获取排行榜
        /// </summary>
        /// <param name="type">排行榜类型 (Weekly=1, Monthly=2, AllTime=3)</param>
        /// <param name="topN">前N名 (默认10)</param>
        /// <returns>排行榜列表</returns>
        [HttpGet("leaderboard")]
        public async Task<IActionResult> GetLeaderboard([FromQuery] LeaderboardType type = LeaderboardType.Weekly, [FromQuery] int topN = 10)
        {
            try
            {
                var leaderboard = await _gamificationService.GetTopLeaderboardAsync(type, topN);
                return Ok(ApiResponse<List<UserLeaderboardDto>>.CreateSuccess(leaderboard, "获取排行榜成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取排行榜时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("获取排行榜失败"));
            }
        }

        /// <summary>
        /// 获取当前用户在排行榜中的位置
        /// </summary>
        /// <param name="type">排行榜类型</param>
        /// <returns>用户排行榜信息</returns>
        [HttpGet("my-rank")]
        public async Task<IActionResult> GetMyRank([FromQuery] LeaderboardType type = LeaderboardType.Weekly)
        {
            try
            {
                var currentUserId = _currentUserService.GetCurrentUserId();
                if (!currentUserId.HasValue)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                var userRank = await _gamificationService.GetUserLeaderboardAsync(currentUserId.Value, type);
                if (userRank == null)
                {
                    return NotFound(ApiResponse<object>.CreateFail("用户排行榜信息不存在"));
                }

                return Ok(ApiResponse<UserLeaderboardDto>.CreateSuccess(userRank, "获取用户排行榜信息成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户排行榜信息时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("获取用户排行榜信息失败"));
            }
        }

        /// <summary>
        /// 初始化用户游戏化数据
        /// </summary>
        /// <returns>初始化结果</returns>
        [HttpPost("initialize")]
        public async Task<IActionResult> InitializeUserStats()
        {
            try
            {
                var currentUserId = _currentUserService.GetCurrentUserId();
                if (!currentUserId.HasValue)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                var stats = await _gamificationService.InitializeUserStatsAsync(currentUserId.Value);
                return Ok(ApiResponse<GamificationUserStatsDto>.CreateSuccess(stats, "用户游戏化数据初始化成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化用户游戏化数据时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("初始化用户游戏化数据失败"));
            }
        }

        /// <summary>
        /// 手动触发任务领取奖励 (测试用)
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>奖励结果</returns>
        [HttpPost("rewards/claim/{taskId}")]
        public async Task<IActionResult> TriggerClaimReward(long taskId)
        {
            try
            {
                var currentUserId = _currentUserService.GetCurrentUserId();
                if (!currentUserId.HasValue)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                var reward = await _gamificationService.ClaimTaskRewardAsync(currentUserId.Value, taskId);
                return Ok(ApiResponse<GamificationRewardDto>.CreateSuccess(reward, "任务领取奖励处理完成"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理任务领取奖励时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("处理任务领取奖励失败"));
            }
        }

        /// <summary>
        /// 手动触发任务完成奖励 (测试用)
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="isOnTime">是否按时完成</param>
        /// <returns>奖励结果</returns>
        [HttpPost("rewards/complete/{taskId}")]
        public async Task<IActionResult> TriggerCompleteReward(long taskId, [FromQuery] bool isOnTime = false)
        {
            try
            {
                var currentUserId = _currentUserService.GetCurrentUserId();
                if (!currentUserId.HasValue)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                var reward = await _gamificationService.CompleteTaskRewardAsync(currentUserId.Value, taskId, isOnTime);
                return Ok(ApiResponse<GamificationRewardDto>.CreateSuccess(reward, "任务完成奖励处理完成"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理任务完成奖励时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("处理任务完成奖励失败"));
            }
        }

        /// <summary>
        /// 获取周统计汇总 - 所有人按周的任务创建、领取、完成数量
        /// </summary>
        /// <param name="weekOffset">周偏移量 (0=本周, -1=上周, 1=下周)</param>
        /// <returns>周统计汇总</returns>
        [HttpGet("weekly-stats")]
        public async Task<IActionResult> GetWeeklyStats([FromQuery] int weekOffset = 0)
        {
            try
            {
                var weeklyStats = await _gamificationService.GetWeeklyStatsAsync(weekOffset);
                return Ok(ApiResponse<List<WeeklyTaskStatsDto>>.CreateSuccess(weeklyStats, "获取周统计汇总成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取周统计汇总时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("获取周统计汇总失败"));
            }
        }

        /// <summary>
        /// 获取用户周统计详情
        /// </summary>
        /// <param name="userId">用户ID (可选，默认当前用户)</param>
        /// <param name="weekOffset">周偏移量 (0=本周, -1=上周, 1=下周)</param>
        /// <returns>用户周统计详情</returns>
        [HttpGet("weekly-stats/user")]
        public async Task<IActionResult> GetUserWeeklyStats([FromQuery] int? userId = null, [FromQuery] int weekOffset = 0)
        {
            try
            {
                var targetUserId = userId ?? _currentUserService.GetCurrentUserId();
                if (!targetUserId.HasValue)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                var userWeeklyStats = await _gamificationService.GetUserWeeklyStatsAsync(targetUserId.Value, weekOffset);
                if (userWeeklyStats == null)
                {
                    return NotFound(ApiResponse<object>.CreateFail("用户周统计数据不存在"));
                }

                return Ok(ApiResponse<WeeklyTaskStatsDto>.CreateSuccess(userWeeklyStats, "获取用户周统计详情成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户周统计详情时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("获取用户周统计详情失败"));
            }
        }
    }
}
