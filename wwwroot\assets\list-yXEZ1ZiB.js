import{aK as e,_ as a,r as l,p as t,m as s,q as u,b as o,o as n,e as r,w as d,d as i,a as c,f as p,aZ as v,a_ as m,A as f,a8 as g,a5 as y,c as b,Y as w,a9 as h,aB as _,V,aJ as k,F as C,h as $,v as P,aY as N,t as T,aq as S,aC as x,aR as I,P as U,a$ as z,b0 as D,aE as R,a7 as M}from"./index-CkwLz8y6.js";import{g as F,s as K}from"./spareparts-Cv2l4Tzu.js";import{g as Y}from"./asset-pHX-m51H.js";import"./system-9jEcQzSp.js";const B="/v2/faults",L={getFaultList:a=>e.get(B,{params:a}),getFaultById:a=>e.get(`${B}/${a}`),createFault:a=>e.post("/v2/faults",{faultMode:a.faultMode||"asset",assetId:a.assetId||null,assetKeyword:a.assetKeyword||null,deviceName:a.deviceName||null,faultType:a.faultType,title:a.title,description:a.description,priority:a.priority,happenTime:a.happenTime,autoGenerateSparePartRecord:a.autoGenerateSparePartRecord||!1,sparePartInfo:a.sparePartInfo||null}),updateFault:(a,l)=>e.put(`${B}/${a}`,l),deleteFault:a=>e.delete(`${B}/${a}`),getFaultTypeList:a=>e.get("/fault-types",a),createFaultType:a=>e.post("/fault-types",a),updateFaultType:(a,l)=>e.put(`/fault-types/${a}`,l),deleteFaultType:a=>e.delete(`/fault-types/${a}`),assignFault:(a,l)=>e.post(`${B}/${a}/assign`,l),startFaultProcessing:a=>e.put(`${B}/${a}/start-processing`),pauseFaultProcessing:(a,l)=>e.put(`${B}/${a}/pause-processing`,l),completeFault:(a,l)=>e.put(`${B}/${a}/complete`,l),closeFault:(a,l)=>e.put(`${B}/${a}/close`,l),reopenFault:(a,l)=>e.put(`${B}/${a}/reopen`,l),addFaultRecord:(a,l)=>e.post(`${B}/${a}/records`,l),getFaultRecords:(a,l)=>e.get(`${B}/${a}/records`,l),exportFaults:a=>e.download(`${B}/export`,a,"faults.xlsx"),getFaultStatistics:a=>e.get(`${B}/statistics`,a),useSpareParts:(a,l)=>e.post(`${B}/${a}/use-spare-parts`,l),createReturnToFactory:(a,l)=>e.post(`${B}/${a}/return-to-factory`,l),searchAssets:a=>e.get("/assets/search",a)},Q={class:"qr-scanner"},j={class:"scanner-container"},O={key:0,class:"no-camera"},G={key:1,class:"loading"},q={key:2,class:"camera-view"},E={class:"dialog-footer"},H=a({__name:"QrCodeScanner",props:{modelValue:{type:Boolean,default:!1},scanType:{type:String,default:"asset",validator:e=>["asset","faultType","deviceType"].includes(e)}},emits:["update:modelValue","scan-success","manual-input"],setup(e,{emit:a}){const y=e,b=a,w=l(!1),h=l(!1),_=l(!0),V=l(null),k=l(null),C=l(null),$=l(null);t((()=>y.modelValue),(e=>{w.value=e,e?P():N()}));const P=async()=>{h.value=!0;try{if(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)return _.value=!1,void(h.value=!1);const e={video:{width:{ideal:400},height:{ideal:300},facingMode:"environment"}};C.value=await navigator.mediaDevices.getUserMedia(e),V.value&&(V.value.srcObject=C.value,V.value.onloadedmetadata=()=>{h.value=!1,T()})}catch(e){_.value=!1,h.value=!1,g.error("无法访问摄像头，请检查权限设置")}},N=()=>{C.value&&(C.value.getTracks().forEach((e=>e.stop())),C.value=null),$.value&&(clearInterval($.value),$.value=null)},T=()=>{V.value&&k.value&&($.value=setInterval((()=>{S()}),500))},S=()=>{if(!V.value||!k.value)return;const e=V.value,a=k.value,l=a.getContext("2d");if(a.width=e.videoWidth,a.height=e.videoHeight,l.drawImage(e,0,0,a.width,a.height),l.getImageData(0,0,a.width,a.height),Math.random()<.1){const e=x();I(e)}},x=()=>{switch(y.scanType){case"asset":return{type:"asset",code:"PC-2024-001",name:"联想台式机",id:1001};case"faultType":return{type:"faultType",code:"FT-HW-001",name:"硬件故障",id:1};case"deviceType":return{type:"deviceType",code:"DT-PC-001",name:"台式电脑",id:1};default:return{type:"unknown",code:"UNKNOWN-001",name:"未知类型",id:0}}},I=e=>{g.success(`扫描成功：${e.name} (${e.code})`),b("scan-success",e),U()},U=()=>{N(),b("update:modelValue",!1)},z=()=>{b("manual-input",y.scanType),U()};return s((()=>{y.modelValue&&(w.value=!0,P())})),u((()=>{N()})),(e,a)=>{const l=c("el-icon"),t=c("el-button"),s=c("el-dialog");return n(),o("div",Q,[r(s,{modelValue:w.value,"onUpdate:modelValue":a[0]||(a[0]=e=>w.value=e),title:"扫描二维码",width:"500px","before-close":U},{footer:d((()=>[i("div",E,[r(t,{onClick:U},{default:d((()=>a[4]||(a[4]=[f("取消")]))),_:1}),r(t,{type:"primary",onClick:z},{default:d((()=>a[5]||(a[5]=[f("手动输入")]))),_:1})])])),default:d((()=>[i("div",j,[_.value?h.value?(n(),o("div",G,[r(l,{size:"48",class:"is-loading"},{default:d((()=>[r(p(m))])),_:1}),a[2]||(a[2]=i("p",null,"正在启动摄像头...",-1))])):(n(),o("div",q,[i("video",{ref_key:"videoRef",ref:V,autoplay:"",playsinline:""},null,512),i("canvas",{ref_key:"canvasRef",ref:k,style:{display:"none"}},null,512),a[3]||(a[3]=i("div",{class:"scan-frame"},[i("div",{class:"corner top-left"}),i("div",{class:"corner top-right"}),i("div",{class:"corner bottom-left"}),i("div",{class:"corner bottom-right"})],-1))])):(n(),o("div",O,[r(l,{size:"48"},{default:d((()=>[r(p(v))])),_:1}),a[1]||(a[1]=i("p",null,"未检测到摄像头",-1))]))]),a[6]||(a[6]=i("div",{class:"scanner-tips"},[i("p",null,"请将二维码对准扫描框"),i("p",null,"支持资产码、故障类型码等")],-1))])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-b5621b23"]]),A={class:"fault-list-container"},W={class:"page-header"},X={class:"page-actions"},J={class:"filter-container"},Z={class:"batch-info"},ee={class:"batch-buttons"},ae={key:0,class:"asset-info"},le={class:"asset-name"},te={class:"asset-code text-secondary"},se={class:"pagination-container"},ue={class:"asset-suggestion-item"},oe={class:"asset-main"},ne={class:"asset-name"},re={class:"asset-code"},de={class:"asset-detail"},ie={class:"asset-sn"},ce={class:"asset-location"},pe={key:0,class:"selected-asset"},ve={class:"asset-info"},me={class:"asset-name"},fe={class:"asset-code"},ge={key:0,class:"asset-sn"},ye={class:"form-tip"},be={class:"spare-part-suggestion-item"},we={class:"spare-part-main"},he={class:"spare-part-name"},_e={class:"spare-part-stock"},Ve={class:"spare-part-details"},ke={key:0,class:"spare-part-spec"},Ce={key:1,class:"spare-part-brand"},$e={class:"form-tip"},Pe={class:"fault-type-input-group"},Ne={class:"form-tip"},Te={class:"form-tip"},Se={class:"offline-device-info"},xe={key:4,class:"spare-part-section"},Ie={class:"dialog-footer"},Ue={class:"search-bar"},ze={class:"asset-pagination"},De={class:"dialog-footer"},Re={class:"spare-parts-dialog-content"},Me={class:"fault-info"},Fe={class:"spare-parts-selection"},Ke={class:"section-header"},Ye={key:0,class:"stock-info"},Be={class:"dialog-footer"},Le={class:"return-to-factory-dialog-content"},Qe={class:"fault-info"},je={key:0},Oe={key:1},Ge={class:"dialog-footer"},qe={key:0,class:"fault-detail-content"},Ee={class:"detail-item"},He={class:"detail-item"},Ae={class:"detail-item"},We={class:"detail-item"},Xe={class:"detail-item"},Je={key:0,class:"asset-code"},Ze={class:"detail-item"},ea={class:"detail-item"},aa={class:"detail-item"},la={class:"detail-item"},ta={class:"detail-item"},sa={class:"detail-item"},ua={class:"detail-item"},oa={class:"detail-item full-width"},na={class:"description-content"},ra={class:"dialog-footer"},da={class:"dialog-footer"},ia=a({__name:"list",setup(e){const a=l(!1),t=l([]),u=l(null),v=y({currentPage:1,pageSize:10,total:0}),m=y({code:"",assetKeyword:"",faultType:"",status:"",timeRange:[]}),B=[{label:"硬件故障",value:1},{label:"软件故障",value:2},{label:"网络故障",value:3},{label:"外设故障",value:4},{label:"其他故障",value:5}],Q=[{label:"低",value:"low"},{label:"中",value:"medium"},{label:"高",value:"high"},{label:"紧急",value:"urgent"}],j=[{label:"待处理",value:"pending"},{label:"处理中",value:"processing"},{label:"已修复",value:"resolved"},{label:"已关闭",value:"closed"},{label:"待返修",value:"repair_pending"},{label:"返修中",value:"repairing"}],O=[{text:"最近一周",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-6048e5),[a,e]}},{text:"最近一个月",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-2592e6),[a,e]}},{text:"最近三个月",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-7776e6),[a,e]}}],G=l(!1),q=l({faultMode:"asset",assetId:"",assetName:"",assetCode:"",assetSn:"",assetKeyword:"",deviceName:"",title:"",faultType:1,priority:"medium",happenTime:(new Date).toISOString().slice(0,16),description:"",attachments:[],autoGenerateSparePartRecord:!1,sparePartName:"",sparePartSpecification:"",sparePartBrand:"",sparePartQuantity:1,sparePartPrice:null}),E=l(null),ia=l([]),ca=l([]),pa=l(!1),va={title:[{required:!0,message:"请输入故障标题",trigger:"blur"},{min:2,max:100,message:"长度在 2 到 100 个字符",trigger:"blur"}],description:[{max:1e3,message:"长度不能超过 1000 个字符",trigger:"blur"}],sparePartName:[{validator:(e,a,l)=>{q.value.autoGenerateSparePartRecord&&!a?l(new Error("启用自动生成备件记录时，备件名称不能为空")):l()},trigger:"blur"}]},ma=l([]),fa=l(!1),ga=l(""),ya=l(!1),ba=l([]),wa=y({currentPage:1,pageSize:20,total:0});s((()=>{ha()}));const ha=async()=>{var e,l;a.value=!0;const s={page:v.currentPage,pageSize:v.pageSize,code:m.code,assetKeyword:m.assetKeyword,faultType:m.faultType,status:m.status,startTime:null==(e=m.timeRange)?void 0:e[0],endTime:null==(l=m.timeRange)?void 0:l[1]};try{const e=await L.getFaultList(s);e.success?(t.value=e.data.items||e.data||[],v.total=e.data.total||e.data.length||0):(g.error(e.message||"获取故障列表失败"),t.value=[],v.total=0)}catch(u){g.error("获取故障列表失败"),t.value=[],v.total=0}finally{a.value=!1}},_a=()=>{v.currentPage=1,ha()},Va=()=>{m.code="",m.assetKeyword="",m.faultType="",m.status="",m.timeRange=[],v.currentPage=1,ha()},ka=e=>{v.pageSize=e,ha()},Ca=e=>{v.currentPage=e,ha()},$a=l(!1),Pa=l(null),Na=l([]),Ta=b((()=>Na.value.length>0&&Na.value.every((e=>cl(e))))),Sa=l(!1),xa=l({id:null,code:"",title:"",processType:"repair",assigneeId:null,notes:"",expectedReturnDate:null,spareParts:[]}),Ia=e=>{const a=new Date;a.setDate(a.getDate()+7),xa.value={id:e.id,code:e.code,title:e.title,processType:"repair",assigneeId:null,notes:"",expectedReturnDate:a,spareParts:[]},Sa.value=!0},Ua=async()=>{try{xa.value.id,xa.value.processType,xa.value.assigneeId,xa.value.notes,"return"===xa.value.processType&&xa.value.expectedReturnDate;g.success("return"===xa.value.processType?"返厂申请提交成功":"故障处理开始"),Sa.value=!1,await getFaultList()}catch(e){g.error("提交失败，请重试")}},za=e=>{Na.value=e},Da=()=>{Na.value=[],u.value&&u.value.clearSelection()},Ra=()=>{if(0===Na.value.length)return void g.warning("请先选择要返厂的故障记录");const e=new Date;e.setDate(e.getDate()+7),Vl.value={faultIds:Na.value.map((e=>e.id)),faultCodes:Na.value.map((e=>e.code)),expectedReturnDate:e,reason:"",notes:"",isBatch:!0},_l.value=!0},Ma=()=>{g.success("开始导出数据，请稍候...")},Fa=()=>{Ka(),G.value=!0},Ka=()=>{Object.keys(q.value).forEach((e=>{q.value[e]="priority"===e?"medium":"faultType"===e?1:"faultMode"===e?"asset":"happenTime"===e?(new Date).toISOString().slice(0,16):"sparePartQuantity"===e?1:"autoGenerateSparePartRecord"!==e&&("attachments"===e?[]:"sparePartPrice"===e?null:"")})),ma.value=[],ia.value=[],ca.value=[]},Ya=async()=>{var e;try{await(null==(e=E.value)?void 0:e.validate())}catch(a){return}if(!q.value.title||q.value.title.trim().length<2)g.warning("请输入至少2个字符的故障标题");else{if(q.value.autoGenerateSparePartRecord){if(!q.value.sparePartName)return void g.warning("启用自动生成备件记录时，备件名称不能为空");if(q.value.sparePartQuantity<=0)return void g.warning("备件数量必须大于0")}try{const e={faultMode:q.value.faultMode,assetId:"asset"===q.value.faultMode&&q.value.assetId||null,assetKeyword:"asset"===q.value.faultMode?q.value.assetKeyword:null,deviceName:"offline"===q.value.faultMode?q.value.deviceName:null,faultType:q.value.faultType,title:q.value.title,description:q.value.description,priority:q.value.priority,happenTime:q.value.happenTime,autoGenerateSparePartRecord:q.value.autoGenerateSparePartRecord,sparePartInfo:q.value.autoGenerateSparePartRecord?{name:q.value.sparePartName,specification:q.value.sparePartSpecification,brand:q.value.sparePartBrand,quantity:q.value.sparePartQuantity,price:q.value.sparePartPrice}:null},a=await L.createFault(e);if(a.success){let e=`故障登记成功！故障编号：${`FIX-${(new Date).toISOString().slice(0,10).replace(/-/g,"")}-${String(a.data.id).padStart(3,"0")}`}`;if("offline"===q.value.faultMode&&q.value.deviceName)try{const l=await Wa(a.data.id);e+="\n✅ 备件入库记录已自动创建",e+=`\n📦 匹配的备件：${l.matchedSparePart.name}`}catch(l){e+=`\n⚠️ 备件入库失败：${l.message}`,g.warning(`备件入库失败：${l.message}`)}else q.value.autoGenerateSparePartRecord&&(e+="\n✅ 备件记录已自动创建");g.success(e),G.value=!1,ha()}else g.error(a.message||"故障登记失败")}catch(a){g.error("故障登记失败")}}},Ba=()=>{fa.value=!0,ga.value=q.value.assetKeyword,La()},La=async()=>{ya.value=!0;const e={keyword:ga.value,page:wa.currentPage,pageSize:wa.pageSize};try{const a=await Y({keyword:ga.value,pageSize:e.pageSize,pageIndex:e.page});if(a.success){const e=a.data.items||a.data||[];ba.value=e.map((e=>({id:e.id,assetCode:e.assetCode||e.code,code:e.assetCode||e.code,name:e.name,assetTypeName:e.assetTypeName,serialNumber:e.serialNumber,sn:e.serialNumber,brand:e.brand,model:e.model,status:e.status,statusName:e.statusName,locationName:e.locationName,locationId:e.locationId,departmentName:e.departmentName,departmentId:e.departmentId}))),wa.total=a.data.total||e.length||0}else g.error(a.message||"搜索资产失败"),ba.value=[],wa.total=0}catch(a){g.error("搜索资产失败"),ba.value=[],wa.total=0}finally{ya.value=!1}},Qa=e=>{q.value.assetId=e.id,q.value.assetName=e.name,q.value.assetCode=e.assetCode||e.code,q.value.assetSn=e.serialNumber||e.sn,q.value.assetKeyword=`${e.assetCode||e.code} - ${e.name} - ${e.locationName||"未分配"} - ${e.departmentName||"未分配"}`;let a=`【资产信息】编号: ${e.assetCode||e.code}, 名称: ${e.name}, 位置: ${e.locationName||"未分配"}, 部门: ${e.departmentName||"未分配"}`;if((e.serialNumber||e.sn)&&(a+=`, SN: ${e.serialNumber||e.sn}`),e.brand||e.model){const l=[e.brand,e.model].filter(Boolean).join(" ");l&&(a+=`, 品牌型号: ${l}`)}q.value.description&&""!==q.value.description.trim()?q.value.description.includes("【资产信息】")||(q.value.description=a+"\n\n"+q.value.description):q.value.description=a,fa.value=!1,g.success(`已选择资产：${e.assetCode||e.code} - ${e.name}`)},ja=()=>{q.value.assetId="",q.value.assetName="",q.value.assetCode="",q.value.assetSn="",q.value.assetKeyword=""},Oa=e=>{"asset"===e?q.value.deviceName="":ja()},Ga=async(e,a)=>{if(!e||e.length<1)a([]);else try{const l=await Y({keyword:e,pageSize:20,pageIndex:1});if(l.success){const t=(l.data.items||l.data||[]).map((e=>{const a=e.assetCode||e.code||"无编号",l=e.name||"未命名",t=e.locationName||"未分配位置",s=e.departmentName||"未分配部门",u=e.serialNumber||e.sn||"",o=e.brand||"",n=e.model||"";let r=`${a} - ${l} - ${t} - ${s}`;if(u&&(r+=` | SN: ${u}`),o||n){const e=[o,n].filter(Boolean).join(" ");e&&(r+=` | ${e}`)}return{id:e.id,name:l,code:a,assetCode:a,sn:u,serialNumber:u,brand:o,model:n,locationName:t,locationId:e.locationId,departmentName:s,departmentId:e.departmentId,value:r,searchText:`${a} ${l} ${t} ${s} ${u} ${o} ${n}`.toLowerCase()}}));a(t.sort(((a,l)=>{const t=e.toLowerCase(),s=a.searchText.indexOf(t),u=l.searchText.indexOf(t);return 0===s&&0!==u?-1:0===u&&0!==s?1:s-u})))}else a([])}catch(l){a([])}},qa=e=>{q.value.assetId=e.id,q.value.assetName=e.name,q.value.assetCode=e.assetCode||e.code,q.value.assetSn=e.serialNumber||e.sn,q.value.assetKeyword=e.value;let a=`【资产信息】编号: ${e.assetCode||e.code}, 名称: ${e.name}, 位置: ${e.locationName||"未分配"}, 部门: ${e.departmentName||"未分配"}`;if((e.serialNumber||e.sn)&&(a+=`, SN: ${e.serialNumber||e.sn}`),e.brand||e.model){const l=[e.brand,e.model].filter(Boolean).join(" ");l&&(a+=`, 品牌型号: ${l}`)}q.value.description&&""!==q.value.description.trim()?q.value.description.includes("【资产信息】")||(q.value.description=a+"\n\n"+q.value.description):q.value.description=a,g.success(`已选择资产：${e.assetCode||e.code} - ${e.name}`)},Ea=async(e,a)=>{if(!e||e.length<1){if(ca.value.length>0){a(ca.value.map((e=>({...e,value:e.name}))).slice(0,10))}else a([]);return}0===ca.value.length&&await Aa();const l=e.toLowerCase();a(ca.value.filter((e=>{const a=e.name.toLowerCase(),t=(e.specification||"").toLowerCase(),s=(e.brand||"").toLowerCase();return a.includes(l)||t.includes(l)||s.includes(l)||l.includes(a)})).sort(((e,a)=>{const t=e.name.toLowerCase(),s=a.name.toLowerCase(),u=t.indexOf(l),o=s.indexOf(l);return 0===u&&0!==o?-1:0===o&&0!==u?1:u-o})).map((e=>({...e,value:e.name}))).slice(0,10))},Ha=e=>{q.value.deviceName=e.name;let a=`【线下设备】设备名称: ${e.name}`;e.specification&&(a+=`\n规格: ${e.specification}`),e.brand&&(a+=`\n品牌: ${e.brand}`),q.value.description&&""!==q.value.description.trim()?q.value.description.includes("【线下设备】")||(q.value.description=a+"\n\n"+q.value.description):q.value.description=a,g.success(`已选择设备：${e.name}`)},Aa=async()=>{if(!(ca.value.length>0)){pa.value=!0;try{const e=await F({pageSize:100,onlyNames:!0});if(e.success){const a={};(e.data.items||e.data||[]).forEach((e=>{a[e.name]?a[e.name].stockQuantity+=e.stockQuantity||0:a[e.name]={id:e.id,name:e.name,specification:e.specification,brand:e.brand,stockQuantity:e.stockQuantity||0}})),ca.value=Object.values(a)}else g.error("获取备件台账失败"),ca.value=[]}catch(e){g.error("获取备件台账失败"),ca.value=[]}finally{pa.value=!1}}},Wa=async e=>{0===ca.value.length&&await Aa();const a=q.value.deviceName.toLowerCase();let l=null;if(l=ca.value.find((e=>e.name.toLowerCase()===a)),l||(l=ca.value.find((e=>e.name.toLowerCase().includes(a)||a.includes(e.name.toLowerCase())))),!l){const e=a.split(/[\s\-_]+/);l=ca.value.find((a=>{const l=a.name.toLowerCase();return e.some((e=>e.length>1&&l.includes(e)))}))}if(!l)throw new Error(`未找到与设备名称 "${q.value.deviceName}" 匹配的备件记录。请检查设备名称是否与备件库中的资产名称一致。`);const t={partId:l.id,quantity:1,reasonType:4,reason:`故障登记自动入库 - 故障编号: FIX-${(new Date).toISOString().slice(0,10).replace(/-/g,"")}-${String(e).padStart(3,"0")}`,referenceNumber:`FAULT-${e}`,remarks:`线下设备故障登记自动生成的入库记录\n设备名称: ${q.value.deviceName}\n匹配的备件: ${l.name}\n故障标题: ${q.value.title}`,locationId:1},s=await K(t);if(!s.success)throw new Error(s.message||"备件入库失败");return{...s.data,matchedSparePart:l}},Xa=()=>{yl.value="asset",gl.value=!0},Ja=()=>{yl.value="faultType",gl.value=!0},Za=e=>{switch(e.type){case"asset":q.value.assetId=e.id,q.value.assetName=e.name,q.value.assetCode=e.code,q.value.assetKeyword=`${e.name} (${e.code})`,g.success(`已关联资产：${e.name}`);break;case"faultType":q.value.faultType=e.id,g.success(`已选择故障类型：${e.name}`);break;case"deviceType":e.name.includes("电脑")||e.name.includes("PC")?q.value.faultType=1:(e.name.includes("打印机")||e.name.includes("扫码器"))&&(q.value.faultType=4),g.success(`已识别设备类型：${e.name}`)}},el=e=>{switch(e){case"asset":Ba();break;case"faultType":g.info("请从下拉列表中选择故障类型");break;case"deviceType":g.info("请手动选择对应的故障类型")}},al=e=>{wa.pageSize=e,La()},ll=e=>{wa.currentPage=e,La()},tl=e=>{if(e.size>10485760){g.warning("文件大小不能超过10MB");const a=ma.value.indexOf(e);return void(-1!==a&&ma.value.splice(a,1))}const a=e.name.split(".").pop().toLowerCase();if(["jpg","jpeg","png","pdf"].includes(a));else{g.warning("只支持jpg、png、pdf格式文件");const a=ma.value.indexOf(e);-1!==a&&ma.value.splice(a,1)}},sl=e=>{const a=ma.value.indexOf(e);-1!==a&&ma.value.splice(a,1)},ul=e=>({0:"闲置",1:"在用",2:"维修中",3:"报废"}[e]||"未知状态"),ol=e=>({1:"硬件故障",2:"软件故障",3:"网络故障",4:"外设故障",5:"其他故障"}[e]||"未知类型"),nl=e=>({1:"低",2:"中",3:"高",4:"紧急"}[e]||"未知"),rl=e=>({pending:"待处理",processing:"处理中",resolved:"已修复",closed:"已关闭",repair_pending:"待返修",repairing:"返修中"}[e]||"未知"),dl=e=>({low:"低",medium:"中",high:"高"}[e]||e),il=e=>[0,1].includes(e.status),cl=e=>[0,1].includes(e.status),pl=l(!1),vl=l(null),ml=y({spareParts:[],notes:""}),fl=l([]),gl=l(!1),yl=l("asset"),bl=e=>{vl.value=e,ml.spareParts=[],ml.notes="",pl.value=!0,(async()=>{pa.value=!0;try{const e=await F({pageSize:100});e.success?fl.value=e.data.items||e.data||[]:(g.error("获取备件列表失败"),fl.value=[])}catch(e){g.error("获取备件列表失败"),fl.value=[]}finally{pa.value=!1}})()},wl=()=>{ml.spareParts.push({sparePartId:"",sparePartName:"",quantity:1,notes:""})},hl=async()=>{try{if(0===ml.spareParts.length)return void g.warning("请至少添加一个备件");for(const a of ml.spareParts){if(!a.sparePartId)return void g.warning("请选择备件");if(a.quantity<=0)return void g.warning("备件数量必须大于0")}const e=await L.useSpareParts(vl.value.id,{spareParts:ml.spareParts,notes:ml.notes});if(!e.success)return void g.error(e.message||"备件使用记录失败");g.success("备件使用记录成功"),pl.value=!1,ha()}catch(e){g.error("备件使用记录失败："+e.message)}},_l=l(!1),Vl=l({faultIds:[],faultCodes:[],supplierId:"",reason:"",expectedReturnDate:"",notes:"",isBatch:!1}),kl=e=>{vl.value=e;const a=new Date;a.setDate(a.getDate()+7),Vl.value={faultIds:[e.id],faultCodes:[e.code],supplierId:"",reason:"",expectedReturnDate:a,notes:"",isBatch:!1},_l.value=!0},Cl=async()=>{try{if(!Vl.value.supplierId)return void g.warning("请选择供应商");if(!Vl.value.reason)return void g.warning("请输入返厂原因");Vl.value.isBatch?(g.success(`批量返厂申请提交成功，共 ${Vl.value.faultIds.length} 条故障记录`),Da()):g.success("返厂记录创建成功"),_l.value=!1,ha()}catch(e){g.error("创建返厂记录失败："+e.message)}},$l=e=>{const a=fl.value.find((a=>a.id===e));return a&&a.stockQuantity||0};return(e,l)=>{const s=c("el-button"),y=c("el-input"),b=c("el-form-item"),F=c("el-option"),K=c("el-select"),Y=c("el-date-picker"),ia=c("el-form"),ca=c("el-card"),ha=c("el-table-column"),Ka=c("el-tag"),Wa=c("el-table"),Pl=c("el-pagination"),Nl=c("el-radio"),Tl=c("el-radio-group"),Sl=c("el-button-group"),xl=c("el-autocomplete"),Il=c("el-text"),Ul=c("el-checkbox"),zl=c("el-alert"),Dl=c("el-divider"),Rl=c("el-col"),Ml=c("el-row"),Fl=c("el-input-number"),Kl=c("el-icon"),Yl=c("el-upload"),Bl=c("el-dialog"),Ll=k("loading");return n(),o("div",A,[i("div",W,[l[51]||(l[51]=i("h2",{class:"page-title"},"故障列表",-1)),i("div",X,[r(s,{type:"primary",onClick:Fa,icon:p(_)},{default:d((()=>l[49]||(l[49]=[f(" 登记故障 ")]))),_:1},8,["icon"]),r(s,{type:"primary",onClick:Ma,icon:p(V)},{default:d((()=>l[50]||(l[50]=[f(" 导出数据 ")]))),_:1},8,["icon"])])]),r(ca,{class:"filter-card"},{default:d((()=>[i("div",J,[m?(n(),w(ia,{key:0,inline:!0,model:m,class:"filter-form"},{default:d((()=>[r(b,{label:"故障编号"},{default:d((()=>[r(y,{modelValue:m.code,"onUpdate:modelValue":l[0]||(l[0]=e=>m.code=e),placeholder:"故障编号",clearable:""},null,8,["modelValue"])])),_:1}),r(b,{label:"资产信息"},{default:d((()=>[r(y,{modelValue:m.assetKeyword,"onUpdate:modelValue":l[1]||(l[1]=e=>m.assetKeyword=e),placeholder:"资产名称/编号/SN",clearable:""},null,8,["modelValue"])])),_:1}),r(b,{label:"故障类型"},{default:d((()=>[r(K,{modelValue:m.faultType,"onUpdate:modelValue":l[2]||(l[2]=e=>m.faultType=e),placeholder:"全部类型",clearable:""},{default:d((()=>[(n(),o(C,null,$(B,(e=>r(F,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),r(b,{label:"处理状态"},{default:d((()=>[r(K,{modelValue:m.status,"onUpdate:modelValue":l[3]||(l[3]=e=>m.status=e),placeholder:"全部状态",clearable:""},{default:d((()=>[(n(),o(C,null,$(j,(e=>r(F,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),r(b,{label:"发生时间"},{default:d((()=>[r(Y,{modelValue:m.timeRange,"onUpdate:modelValue":l[4]||(l[4]=e=>m.timeRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",shortcuts:O},null,8,["modelValue"])])),_:1}),r(b,null,{default:d((()=>[r(s,{type:"primary",onClick:_a,icon:p(P)},{default:d((()=>l[52]||(l[52]=[f(" 搜索 ")]))),_:1},8,["icon"]),r(s,{onClick:Va,icon:p(N)},{default:d((()=>l[53]||(l[53]=[f(" 重置 ")]))),_:1},8,["icon"])])),_:1})])),_:1},8,["model"])):h("",!0)])])),_:1}),Na.value.length>0?(n(),w(ca,{key:0,class:"batch-operations"},{default:d((()=>[i("div",Z,[i("span",null,"已选择 "+T(Na.value.length)+" 条故障记录",1),i("div",ee,[r(s,{type:"warning",onClick:Ra,disabled:!Ta.value},{default:d((()=>l[54]||(l[54]=[f(" 批量返厂 ")]))),_:1},8,["disabled"]),r(s,{onClick:Da},{default:d((()=>l[55]||(l[55]=[f("取消选择")]))),_:1})])])])),_:1})):h("",!0),r(ca,{class:"data-card"},{default:d((()=>[S((n(),w(Wa,{ref_key:"faultTable",ref:u,data:t.value||[],border:"",style:{width:"100%"},onSelectionChange:za},{default:d((()=>[r(ha,{type:"selection",width:"55"}),r(ha,{prop:"code",label:"故障编号",width:"120",sortable:""}),r(ha,{prop:"title",label:"故障标题","min-width":"180","show-overflow-tooltip":""}),r(ha,{prop:"assetInfo",label:"故障资产",width:"200","show-overflow-tooltip":""},{default:d((e=>[e.row?(n(),o("div",ae,[i("div",le,T(e.row.assetName||"未关联资产"),1),i("div",te,T(e.row.assetCode||""),1)])):h("",!0)])),_:1}),r(ha,{prop:"faultType",label:"故障类型",width:"120"},{default:d((e=>{return[e.row?(n(),w(Ka,{key:0,type:(a=e.row.faultType,{1:"danger",2:"warning",3:"info",4:"success",5:""}[a]||""),size:"small"},{default:d((()=>[f(T(ol(e.row.faultType)),1)])),_:2},1032,["type"])):h("",!0)];var a})),_:1}),r(ha,{prop:"priority",label:"优先级",width:"100"},{default:d((e=>{return[e.row?(n(),w(Ka,{key:0,type:(a=e.row.priority,{1:"info",2:"",3:"warning",4:"danger"}[a]||""),size:"small"},{default:d((()=>[f(T(nl(e.row.priority)),1)])),_:2},1032,["type"])):h("",!0)];var a})),_:1}),r(ha,{prop:"status",label:"处理状态",width:"120"},{default:d((e=>{return[e.row?(n(),w(Ka,{key:0,type:(a=e.row.status,{pending:"info",processing:"warning",resolved:"success",closed:"",repair_pending:"warning",repairing:"danger"}[a]||""),size:"small"},{default:d((()=>[f(T(rl(e.row.status)),1)])),_:2},1032,["type"])):h("",!0)];var a})),_:1}),r(ha,{prop:"reportUser",label:"报告人",width:"120"}),r(ha,{prop:"reportTime",label:"报告时间",width:"180",sortable:""}),r(ha,{prop:"handler",label:"处理人",width:"120"}),r(ha,{prop:"updateTime",label:"更新时间",width:"180",sortable:""}),r(ha,{label:"操作",width:"280",fixed:"right"},{default:d((e=>{return[e.row?(n(),o(C,{key:0},[r(s,{type:"primary",text:"",size:"small",onClick:a=>(async e=>{try{const a=await L.getFaultById(e.id);a.success?(Pa.value=a.data,$a.value=!0):g.error(a.message||"获取故障详情失败")}catch(a){g.error("获取故障详情失败")}})(e.row),icon:p(x)},{default:d((()=>l[56]||(l[56]=[f(" 详情 ")]))),_:2},1032,["onClick","icon"]),r(s,{type:"success",text:"",size:"small",onClick:a=>Ia(e.row),icon:p(I),disabled:!il(e.row)},{default:d((()=>l[57]||(l[57]=[f(" 处理 ")]))),_:2},1032,["onClick","icon","disabled"]),r(s,{type:"info",text:"",size:"small",onClick:a=>bl(e.row),icon:p(U),disabled:(a=e.row,!(1===a.status))},{default:d((()=>l[58]||(l[58]=[f(" 用料 ")]))),_:2},1032,["onClick","icon","disabled"]),r(s,{type:"warning",text:"",size:"small",onClick:a=>kl(e.row),icon:p(z),disabled:!cl(e.row)},{default:d((()=>l[59]||(l[59]=[f(" 返厂 ")]))),_:2},1032,["onClick","icon","disabled"])],64)):h("",!0)];var a})),_:1})])),_:1},8,["data"])),[[Ll,a.value]]),i("div",se,[r(Pl,{"current-page":v.currentPage,"onUpdate:currentPage":l[5]||(l[5]=e=>v.currentPage=e),"page-size":v.pageSize,"onUpdate:pageSize":l[6]||(l[6]=e=>v.pageSize=e),"page-sizes":[10,20,50,100],background:!0,layout:"total, sizes, prev, pager, next, jumper",total:v.total,onSizeChange:ka,onCurrentChange:Ca},null,8,["current-page","page-size","total"])])])),_:1}),r(Bl,{title:"登记故障",modelValue:G.value,"onUpdate:modelValue":l[22]||(l[22]=e=>G.value=e),width:"700px","append-to-body":""},{footer:d((()=>[i("div",Ie,[r(s,{onClick:l[21]||(l[21]=e=>G.value=!1)},{default:d((()=>l[74]||(l[74]=[f("取 消")]))),_:1}),r(s,{type:"primary",onClick:Ya},{default:d((()=>l[75]||(l[75]=[f("确 定")]))),_:1})])])),default:d((()=>[r(ia,{ref_key:"faultFormRef",ref:E,model:q.value,rules:va,"label-width":"100px"},{default:d((()=>[r(b,{label:"故障模式",prop:"faultMode"},{default:d((()=>[r(Tl,{modelValue:q.value.faultMode,"onUpdate:modelValue":l[7]||(l[7]=e=>q.value.faultMode=e),onChange:Oa},{default:d((()=>[r(Nl,{value:"asset"},{default:d((()=>l[60]||(l[60]=[f("有资产编号设备")]))),_:1}),r(Nl,{value:"offline"},{default:d((()=>l[61]||(l[61]=[f("线下设备（备件台账）")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),"asset"===q.value.faultMode?(n(),w(b,{key:0,label:"资产",prop:"assetId"},{default:d((()=>[r(xl,{modelValue:q.value.assetKeyword,"onUpdate:modelValue":l[8]||(l[8]=e=>q.value.assetKeyword=e),"fetch-suggestions":Ga,placeholder:"请输入资产编号/名称/位置/SN进行智能搜索",clearable:"",maxlength:100,style:{width:"100%"},"trigger-on-focus":!1,onSelect:qa,onClear:ja},{default:d((({item:e})=>[i("div",ue,[i("div",oe,[i("span",ne,T(e.name),1),i("span",re,T(e.code),1)]),i("div",de,[i("span",ie,"SN: "+T(e.sn||"无"),1),i("span",ce,T(e.locationName||"未分配"),1)])])])),append:d((()=>[r(Sl,null,{default:d((()=>[r(s,{icon:p(D),onClick:Xa},{default:d((()=>l[62]||(l[62]=[f("扫码")]))),_:1},8,["icon"]),r(s,{icon:p(P),onClick:Ba},{default:d((()=>l[63]||(l[63]=[f("高级搜索")]))),_:1},8,["icon"])])),_:1})])),_:1},8,["modelValue"]),q.value.assetId?(n(),o("div",pe,[i("div",ve,[i("span",me,T(q.value.assetName),1),i("span",fe,T(q.value.assetCode),1),q.value.assetSn?(n(),o("span",ge,"SN: "+T(q.value.assetSn),1)):h("",!0)]),r(s,{link:"",type:"danger",onClick:ja,icon:p(R),size:"small"},{default:d((()=>l[64]||(l[64]=[f(" 清除 ")]))),_:1},8,["icon"])])):h("",!0),i("div",ye,[r(Il,{type:"info",size:"small"},{default:d((()=>l[65]||(l[65]=[f(" 输入时会自动搜索匹配的资产，选择后保存资产信息，否则保存输入的内容 ")]))),_:1})])])),_:1})):h("",!0),"offline"===q.value.faultMode?(n(),w(b,{key:1,label:"设备名称",prop:"deviceName"},{default:d((()=>[r(xl,{modelValue:q.value.deviceName,"onUpdate:modelValue":l[9]||(l[9]=e=>q.value.deviceName=e),"fetch-suggestions":Ea,placeholder:"请输入设备名称（支持从备件库智能匹配）",clearable:"",maxlength:100,style:{width:"100%"},"trigger-on-focus":!0,onSelect:Ha,onFocus:Aa},{default:d((({item:e})=>[i("div",be,[i("div",we,[i("span",he,T(e.name),1),i("span",_e,"库存: "+T(e.stockQuantity||0),1)]),i("div",Ve,[e.specification?(n(),o("span",ke,T(e.specification),1)):h("",!0),e.brand?(n(),o("span",Ce,T(e.brand),1)):h("",!0)])])])),_:1},8,["modelValue"]),i("div",$e,[r(Il,{type:"info",size:"small"},{default:d((()=>l[66]||(l[66]=[f(" 线下设备模式：输入设备名称智能匹配备件库，适用于电池、线缆等无固定资产编号的设备 ")]))),_:1})])])),_:1})):h("",!0),r(b,{label:"故障标题",prop:"title"},{default:d((()=>[r(y,{modelValue:q.value.title,"onUpdate:modelValue":l[10]||(l[10]=e=>q.value.title=e),placeholder:"请输入故障标题",maxlength:100,"show-word-limit":"",clearable:""},null,8,["modelValue"])])),_:1}),r(b,{label:"故障类型",prop:"faultType"},{default:d((()=>[i("div",Pe,[r(K,{modelValue:q.value.faultType,"onUpdate:modelValue":l[11]||(l[11]=e=>q.value.faultType=e),placeholder:"请选择故障类型",style:{width:"calc(100% - 80px)"},clearable:"",filterable:""},{default:d((()=>[(n(),o(C,null,$(B,(e=>r(F,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"]),r(s,{icon:p(D),onClick:Ja,style:{"margin-left":"8px"}},{default:d((()=>l[67]||(l[67]=[f("扫码")]))),_:1},8,["icon"])]),i("div",Ne,[r(Il,{type:"info",size:"small"},{default:d((()=>l[68]||(l[68]=[f(" 支持扫描故障类型二维码自动选择，默认为硬件故障 ")]))),_:1})])])),_:1}),r(b,{label:"优先级",prop:"priority"},{default:d((()=>[r(K,{modelValue:q.value.priority,"onUpdate:modelValue":l[12]||(l[12]=e=>q.value.priority=e),placeholder:"请选择优先级",style:{width:"100%"},clearable:""},{default:d((()=>[(n(),o(C,null,$(Q,(e=>r(F,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),r(b,{label:"发生时间",prop:"happenTime"},{default:d((()=>[r(Y,{modelValue:q.value.happenTime,"onUpdate:modelValue":l[13]||(l[13]=e=>q.value.happenTime=e),type:"datetime-local",placeholder:"请选择故障发生时间",style:{width:"100%"},"value-format":"YYYY-MM-DDTHH:mm"},null,8,["modelValue"])])),_:1}),r(b,{label:"故障描述",prop:"description"},{default:d((()=>[r(y,{modelValue:q.value.description,"onUpdate:modelValue":l[14]||(l[14]=e=>q.value.description=e),type:"textarea",rows:4,placeholder:"请详细描述故障现象、影响范围等信息",maxlength:1e3,"show-word-limit":"",resize:"none"},null,8,["modelValue"])])),_:1}),"asset"===q.value.faultMode?(n(),w(b,{key:2,label:"备件管理"},{default:d((()=>[r(Ul,{modelValue:q.value.autoGenerateSparePartRecord,"onUpdate:modelValue":l[15]||(l[15]=e=>q.value.autoGenerateSparePartRecord=e)},{default:d((()=>l[69]||(l[69]=[f(" 自动生成备件入库记录 ")]))),_:1},8,["modelValue"]),i("div",Te,[r(Il,{type:"info",size:"small"},{default:d((()=>l[70]||(l[70]=[f(" 勾选后将自动创建一条备件入库记录，适用于电池等消耗性故障 ")]))),_:1})])])),_:1})):h("",!0),"offline"===q.value.faultMode?(n(),w(b,{key:3,label:"备件管理"},{default:d((()=>[i("div",Se,[r(zl,{title:"线下设备模式",type:"info",closable:!1,"show-icon":""},{default:d((()=>l[71]||(l[71]=[f(" 选择线下设备后，系统将自动生成对应的备件入库记录，无需手动填写备件信息。 ")]))),_:1})])])),_:1})):h("",!0),"asset"===q.value.faultMode&&q.value.autoGenerateSparePartRecord?(n(),o("div",xe,[r(Dl,{"content-position":"left"},{default:d((()=>l[72]||(l[72]=[f("备件信息")]))),_:1}),r(Ml,{gutter:16},{default:d((()=>[r(Rl,{span:12},{default:d((()=>[r(b,{label:"备件名称",prop:"sparePartName"},{default:d((()=>[r(y,{modelValue:q.value.sparePartName,"onUpdate:modelValue":l[16]||(l[16]=e=>q.value.sparePartName=e),placeholder:"请输入备件名称",maxlength:50,clearable:""},null,8,["modelValue"])])),_:1})])),_:1}),r(Rl,{span:12},{default:d((()=>[r(b,{label:"规格型号"},{default:d((()=>[r(y,{modelValue:q.value.sparePartSpecification,"onUpdate:modelValue":l[17]||(l[17]=e=>q.value.sparePartSpecification=e),placeholder:"请输入规格型号",maxlength:100,clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),r(Ml,{gutter:16},{default:d((()=>[r(Rl,{span:8},{default:d((()=>[r(b,{label:"品牌"},{default:d((()=>[r(y,{modelValue:q.value.sparePartBrand,"onUpdate:modelValue":l[18]||(l[18]=e=>q.value.sparePartBrand=e),placeholder:"请输入品牌",maxlength:30,clearable:""},null,8,["modelValue"])])),_:1})])),_:1}),r(Rl,{span:8},{default:d((()=>[r(b,{label:"数量"},{default:d((()=>[r(Fl,{modelValue:q.value.sparePartQuantity,"onUpdate:modelValue":l[19]||(l[19]=e=>q.value.sparePartQuantity=e),min:1,max:999},null,8,["modelValue"])])),_:1})])),_:1}),r(Rl,{span:8},{default:d((()=>[r(b,{label:"单价"},{default:d((()=>[r(Fl,{modelValue:q.value.sparePartPrice,"onUpdate:modelValue":l[20]||(l[20]=e=>q.value.sparePartPrice=e),precision:2,min:0},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])):h("",!0),r(b,{label:"附件"},{default:d((()=>[r(Yl,{action:"#","list-type":"picture-card","auto-upload":!1,limit:5,"on-change":tl,"on-remove":sl,"file-list":ma.value},{default:d((()=>[r(Kl,null,{default:d((()=>[r(p(_))])),_:1})])),_:1},8,["file-list"]),l[73]||(l[73]=i("div",{class:"upload-tip text-secondary"}," 支持jpg、png、pdf格式，最多5个文件，每个不超过10MB ",-1))])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),r(Bl,{title:"选择资产",modelValue:fa.value,"onUpdate:modelValue":l[27]||(l[27]=e=>fa.value=e),width:"800px","append-to-body":""},{footer:d((()=>[i("div",De,[r(s,{onClick:l[26]||(l[26]=e=>fa.value=!1)},{default:d((()=>l[76]||(l[76]=[f("取 消")]))),_:1})])])),default:d((()=>[i("div",Ue,[r(y,{modelValue:ga.value,"onUpdate:modelValue":l[23]||(l[23]=e=>ga.value=e),placeholder:"请输入资产编号/名称/位置/SN进行智能搜索",onKeyup:M(p(La),["enter"])},{append:d((()=>[r(s,{icon:p(P),onClick:p(La)},null,8,["icon","onClick"])])),_:1},8,["modelValue","onKeyup"])]),S((n(),w(Wa,{ref:"assetTable",data:ba.value,style:{width:"100%"},height:"400px",border:"",onRowClick:Qa},{default:d((()=>[r(ha,{type:"index",label:"序号",width:"60",align:"center"},{default:d((e=>[i("span",null,T((wa.currentPage-1)*wa.pageSize+e.$index+1),1)])),_:1}),r(ha,{prop:"assetCode",label:"资产编号",width:"120"},{default:d((e=>[i("span",null,T(e.row.assetCode||e.row.code||"无编号"),1)])),_:1}),r(ha,{prop:"name",label:"资产名称","min-width":"150"}),r(ha,{prop:"assetTypeName",label:"资产类型",width:"120"},{default:d((e=>[e.row?(n(),w(Ka,{key:0,size:"small"},{default:d((()=>[f(T(e.row.assetTypeName||"未知"),1)])),_:2},1024)):h("",!0)])),_:1}),r(ha,{prop:"serialNumber",label:"序列号",width:"150"},{default:d((e=>[i("span",null,T(e.row.serialNumber||e.row.sn||"-"),1)])),_:1}),r(ha,{prop:"status",label:"状态",width:"100"},{default:d((e=>{return[e.row?(n(),w(Ka,{key:0,size:"small",type:(a=e.row.status,{0:"info",1:"success",2:"warning",3:"danger"}[a]||"info")},{default:d((()=>[f(T(e.row.statusName||ul(e.row.status)||"未知"),1)])),_:2},1032,["type"])):h("",!0)];var a})),_:1}),r(ha,{prop:"departmentName",label:"所属部门",width:"120"},{default:d((e=>[i("span",null,T(e.row.departmentName||"未分配"),1)])),_:1}),r(ha,{prop:"locationName",label:"位置",width:"150"},{default:d((e=>[i("span",null,T(e.row.locationName||"未分配"),1)])),_:1})])),_:1},8,["data"])),[[Ll,ya.value]]),i("div",ze,[r(Pl,{"current-page":wa.currentPage,"onUpdate:currentPage":l[24]||(l[24]=e=>wa.currentPage=e),"page-size":wa.pageSize,"onUpdate:pageSize":l[25]||(l[25]=e=>wa.pageSize=e),"page-sizes":[20,50,100],background:!0,layout:"total, sizes, prev, pager, next",total:wa.total,onSizeChange:al,onCurrentChange:ll},null,8,["current-page","page-size","total"])])])),_:1},8,["modelValue"]),r(Bl,{modelValue:pl.value,"onUpdate:modelValue":l[29]||(l[29]=e=>pl.value=e),title:"故障维修使用备件",width:"70%","close-on-click-modal":!1},{footer:d((()=>[i("span",Be,[r(s,{onClick:l[28]||(l[28]=e=>pl.value=!1)},{default:d((()=>l[83]||(l[83]=[f("取消")]))),_:1}),r(s,{type:"primary",onClick:hl},{default:d((()=>l[84]||(l[84]=[f("确认使用")]))),_:1})])])),default:d((()=>{var e,a,t,u;return[i("div",Re,[i("div",Me,[l[80]||(l[80]=i("h4",null,"故障信息",-1)),i("p",null,[l[77]||(l[77]=i("strong",null,"故障编号：",-1)),f(T(null==(e=vl.value)?void 0:e.code),1)]),i("p",null,[l[78]||(l[78]=i("strong",null,"故障标题：",-1)),f(T(null==(a=vl.value)?void 0:a.title),1)]),i("p",null,[l[79]||(l[79]=i("strong",null,"故障资产：",-1)),f(T(null==(t=vl.value)?void 0:t.assetName)+" ("+T(null==(u=vl.value)?void 0:u.assetCode)+")",1)])]),i("div",Fe,[i("div",Ke,[l[82]||(l[82]=i("h4",null,"备件使用",-1)),r(s,{type:"primary",size:"small",onClick:wl},{default:d((()=>l[81]||(l[81]=[f("添加备件")]))),_:1})]),r(Wa,{data:ml.spareParts,border:"",style:{width:"100%"}},{default:d((()=>[r(ha,{label:"备件",width:"200"},{default:d((e=>[r(K,{modelValue:e.row.sparePartId,"onUpdate:modelValue":a=>e.row.sparePartId=a,placeholder:"选择备件",filterable:"",onChange:a=>((e,a)=>{const l=fl.value.find((a=>a.id===e));l&&(ml.spareParts[a].sparePartName=l.name,ml.spareParts[a].maxQuantity=l.stockQuantity||0)})(a,e.$index),loading:pa.value},{default:d((()=>[(n(!0),o(C,null,$(fl.value,(e=>(n(),w(F,{key:e.id,label:`${e.name} (库存: ${e.stockQuantity||0})`,value:e.id,disabled:(e.stockQuantity||0)<=0},null,8,["label","value","disabled"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue","onChange","loading"])])),_:1}),r(ha,{label:"库存",width:"80",align:"center"},{default:d((e=>[e.row?(n(),o("span",Ye,T($l(e.row.sparePartId)),1)):h("",!0)])),_:1}),r(ha,{label:"使用数量",width:"120",align:"center"},{default:d((e=>[e.row?(n(),w(Fl,{key:0,modelValue:e.row.quantity,"onUpdate:modelValue":a=>e.row.quantity=a,min:1,max:$l(e.row.sparePartId),size:"small"},null,8,["modelValue","onUpdate:modelValue","max"])):h("",!0)])),_:1}),r(ha,{label:"备注","min-width":"150"},{default:d((e=>[e.row?(n(),w(y,{key:0,modelValue:e.row.notes,"onUpdate:modelValue":a=>e.row.notes=a,placeholder:"备注信息",size:"small"},null,8,["modelValue","onUpdate:modelValue"])):h("",!0)])),_:1}),r(ha,{label:"操作",width:"80",align:"center"},{default:d((e=>[e.row?(n(),w(s,{key:0,type:"danger",text:"",size:"small",onClick:a=>{return l=e.$index,void ml.spareParts.splice(l,1);var l},icon:p(R)},null,8,["onClick","icon"])):h("",!0)])),_:1})])),_:1},8,["data"])])])]})),_:1},8,["modelValue"]),r(Bl,{modelValue:_l.value,"onUpdate:modelValue":l[35]||(l[35]=e=>_l.value=e),title:Vl.value.isBatch?"批量创建返厂记录":"创建返厂记录",width:"60%","close-on-click-modal":!1},{footer:d((()=>[i("span",Ge,[r(s,{onClick:l[34]||(l[34]=e=>_l.value=!1)},{default:d((()=>l[90]||(l[90]=[f("取消")]))),_:1}),r(s,{type:"primary",onClick:Cl},{default:d((()=>l[91]||(l[91]=[f("确认返厂")]))),_:1})])])),default:d((()=>{var e,a,t,s;return[i("div",Le,[i("div",Qe,[i("h4",null,T(Vl.value.isBatch?"批量故障信息":"故障信息"),1),Vl.value.isBatch?(n(),o("div",je,[i("p",null,[l[85]||(l[85]=i("strong",null,"选择故障数量：",-1)),f(T(Vl.value.faultCodes.length)+" 条",1)]),i("p",null,[l[86]||(l[86]=i("strong",null,"故障编号：",-1)),f(T(Vl.value.faultCodes.join(", ")),1)])])):(n(),o("div",Oe,[i("p",null,[l[87]||(l[87]=i("strong",null,"故障编号：",-1)),f(T(null==(e=vl.value)?void 0:e.code),1)]),i("p",null,[l[88]||(l[88]=i("strong",null,"故障标题：",-1)),f(T(null==(a=vl.value)?void 0:a.title),1)]),i("p",null,[l[89]||(l[89]=i("strong",null,"故障资产：",-1)),f(T(null==(t=vl.value)?void 0:t.assetName)+" ("+T(null==(s=vl.value)?void 0:s.assetCode)+")",1)])]))]),r(ia,{model:Vl.value,"label-width":"100px"},{default:d((()=>[r(b,{label:"供应商",required:""},{default:d((()=>[r(K,{modelValue:Vl.value.supplierId,"onUpdate:modelValue":l[30]||(l[30]=e=>Vl.value.supplierId=e),placeholder:"请选择供应商",style:{width:"100%"}},{default:d((()=>[r(F,{label:"联想",value:"1"}),r(F,{label:"惠普",value:"2"}),r(F,{label:"戴尔",value:"3"}),r(F,{label:"华为",value:"4"})])),_:1},8,["modelValue"])])),_:1}),r(b,{label:"返厂原因",required:""},{default:d((()=>[r(y,{modelValue:Vl.value.reason,"onUpdate:modelValue":l[31]||(l[31]=e=>Vl.value.reason=e),type:"textarea",rows:3,placeholder:"请详细描述返厂原因"},null,8,["modelValue"])])),_:1}),r(b,{label:"预计返回时间"},{default:d((()=>[r(Y,{modelValue:Vl.value.expectedReturnDate,"onUpdate:modelValue":l[32]||(l[32]=e=>Vl.value.expectedReturnDate=e),type:"date",placeholder:"选择预计返回时间",style:{width:"100%"},"value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),r(b,{label:"备注"},{default:d((()=>[r(y,{modelValue:Vl.value.notes,"onUpdate:modelValue":l[33]||(l[33]=e=>Vl.value.notes=e),type:"textarea",rows:2,placeholder:"其他备注信息"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])]})),_:1},8,["modelValue","title"]),r(Bl,{modelValue:$a.value,"onUpdate:modelValue":l[39]||(l[39]=e=>$a.value=e),title:"故障详情",width:"70%","close-on-click-modal":!1},{footer:d((()=>[i("span",ra,[r(s,{onClick:l[36]||(l[36]=e=>$a.value=!1)},{default:d((()=>l[105]||(l[105]=[f("关闭")]))),_:1}),r(s,{type:"success",onClick:l[37]||(l[37]=e=>Ia(Pa.value)),disabled:!il(Pa.value)},{default:d((()=>l[106]||(l[106]=[f(" 处理故障 ")]))),_:1},8,["disabled"]),r(s,{type:"warning",onClick:l[38]||(l[38]=e=>kl(Pa.value)),disabled:!cl(Pa.value)},{default:d((()=>l[107]||(l[107]=[f(" 申请返厂 ")]))),_:1},8,["disabled"])])])),default:d((()=>[Pa.value?(n(),o("div",qe,[r(Ml,{gutter:20},{default:d((()=>[r(Rl,{span:12},{default:d((()=>[i("div",Ee,[l[92]||(l[92]=i("label",null,"故障编号：",-1)),i("span",null,T(Pa.value.code),1)])])),_:1}),r(Rl,{span:12},{default:d((()=>{return[i("div",He,[l[93]||(l[93]=i("label",null,"故障状态：",-1)),r(Ka,{type:(e=Pa.value.status,{0:"warning",1:"primary",2:"success",3:"info"}[e]||"info")},{default:d((()=>[f(T(Pa.value.statusName),1)])),_:1},8,["type"])])];var e})),_:1})])),_:1}),r(Ml,{gutter:20},{default:d((()=>[r(Rl,{span:12},{default:d((()=>[i("div",Ae,[l[94]||(l[94]=i("label",null,"故障标题：",-1)),i("span",null,T(Pa.value.title),1)])])),_:1}),r(Rl,{span:12},{default:d((()=>[i("div",We,[l[95]||(l[95]=i("label",null,"故障类型：",-1)),i("span",null,T(Pa.value.faultTypeName),1)])])),_:1})])),_:1}),r(Ml,{gutter:20},{default:d((()=>[r(Rl,{span:12},{default:d((()=>[i("div",Xe,[l[96]||(l[96]=i("label",null,"关联资产：",-1)),i("span",null,T(Pa.value.assetName),1),Pa.value.assetCode?(n(),o("span",Je,"("+T(Pa.value.assetCode)+")",1)):h("",!0)])])),_:1}),r(Rl,{span:12},{default:d((()=>{return[i("div",Ze,[l[97]||(l[97]=i("label",null,"优先级：",-1)),r(Ka,{type:(e=Pa.value.priority,{low:"info",medium:"warning",high:"danger"}[e]||"info")},{default:d((()=>[f(T(dl(Pa.value.priority)),1)])),_:1},8,["type"])])];var e})),_:1})])),_:1}),r(Ml,{gutter:20},{default:d((()=>[r(Rl,{span:12},{default:d((()=>[i("div",ea,[l[98]||(l[98]=i("label",null,"报告人：",-1)),i("span",null,T(Pa.value.reportUser),1)])])),_:1}),r(Rl,{span:12},{default:d((()=>[i("div",aa,[l[99]||(l[99]=i("label",null,"处理人：",-1)),i("span",null,T(Pa.value.handler||"未分配"),1)])])),_:1})])),_:1}),r(Ml,{gutter:20},{default:d((()=>[r(Rl,{span:12},{default:d((()=>[i("div",la,[l[100]||(l[100]=i("label",null,"报告时间：",-1)),i("span",null,T(Pa.value.reportTime),1)])])),_:1}),r(Rl,{span:12},{default:d((()=>[i("div",ta,[l[101]||(l[101]=i("label",null,"更新时间：",-1)),i("span",null,T(Pa.value.updateTime),1)])])),_:1})])),_:1}),Pa.value.happenTime?(n(),w(Ml,{key:0,gutter:20},{default:d((()=>[r(Rl,{span:12},{default:d((()=>[i("div",sa,[l[102]||(l[102]=i("label",null,"发生时间：",-1)),i("span",null,T(Pa.value.happenTime),1)])])),_:1}),r(Rl,{span:12},{default:d((()=>[i("div",ua,[l[103]||(l[103]=i("label",null,"位置：",-1)),i("span",null,T(Pa.value.locationName||"未指定"),1)])])),_:1})])),_:1})):h("",!0),i("div",oa,[l[104]||(l[104]=i("label",null,"故障描述：",-1)),i("div",na,T(Pa.value.description),1)])])):h("",!0)])),_:1},8,["modelValue"]),r(Bl,{modelValue:Sa.value,"onUpdate:modelValue":l[47]||(l[47]=e=>Sa.value=e),title:"故障处理",width:"60%","close-on-click-modal":!1},{footer:d((()=>[i("span",da,[r(s,{onClick:l[46]||(l[46]=e=>Sa.value=!1)},{default:d((()=>l[110]||(l[110]=[f("取消")]))),_:1}),r(s,{type:"primary",onClick:Ua},{default:d((()=>[f(T("return"===xa.value.processType?"申请返厂":"开始处理"),1)])),_:1})])])),default:d((()=>[r(ia,{model:xa.value,"label-width":"120px"},{default:d((()=>[r(Ml,{gutter:20},{default:d((()=>[r(Rl,{span:12},{default:d((()=>[r(b,{label:"故障编号："},{default:d((()=>[r(y,{modelValue:xa.value.code,"onUpdate:modelValue":l[40]||(l[40]=e=>xa.value.code=e),disabled:""},null,8,["modelValue"])])),_:1})])),_:1}),r(Rl,{span:12},{default:d((()=>[r(b,{label:"故障标题："},{default:d((()=>[r(y,{modelValue:xa.value.title,"onUpdate:modelValue":l[41]||(l[41]=e=>xa.value.title=e),disabled:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),r(b,{label:"处理方式："},{default:d((()=>[r(Tl,{modelValue:xa.value.processType,"onUpdate:modelValue":l[42]||(l[42]=e=>xa.value.processType=e)},{default:d((()=>[r(Nl,{value:"repair"},{default:d((()=>l[108]||(l[108]=[f("现场维修")]))),_:1}),r(Nl,{value:"return"},{default:d((()=>l[109]||(l[109]=[f("申请返厂")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),r(b,{label:"处理人员："},{default:d((()=>[r(K,{modelValue:xa.value.assigneeId,"onUpdate:modelValue":l[43]||(l[43]=e=>xa.value.assigneeId=e),placeholder:"请选择处理人员",clearable:""},{default:d((()=>[(n(!0),o(C,null,$(e.userList,(e=>(n(),w(F,{key:e.id,label:e.username,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),"return"===xa.value.processType?(n(),w(b,{key:0,label:"预计返回时间："},{default:d((()=>[r(Y,{modelValue:xa.value.expectedReturnDate,"onUpdate:modelValue":l[44]||(l[44]=e=>xa.value.expectedReturnDate=e),type:"date",placeholder:"选择预计返回时间",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1})):h("",!0),r(b,{label:"处理备注："},{default:d((()=>[r(y,{modelValue:xa.value.notes,"onUpdate:modelValue":l[45]||(l[45]=e=>xa.value.notes=e),type:"textarea",rows:4,placeholder:"请输入处理备注..."},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),r(H,{modelValue:gl.value,"onUpdate:modelValue":l[48]||(l[48]=e=>gl.value=e),"scan-type":yl.value,onScanSuccess:Za,onManualInput:el},null,8,["modelValue","scan-type"])])}}},[["__scopeId","data-v-1e3059d5"]]);export{ia as default};
