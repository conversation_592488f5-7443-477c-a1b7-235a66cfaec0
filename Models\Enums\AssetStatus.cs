namespace ItAssetsSystem.Models.Enums
{
    /// <summary>
    /// 资产状态枚举
    /// </summary>
    public enum AssetStatus
    {
        /// <summary>
        /// 闲置
        /// </summary>
        Idle = 0,

        /// <summary>
        /// 在用（正常）
        /// </summary>
        InUse = 1,

        /// <summary>
        /// 维修中
        /// </summary>
        UnderMaintenance = 2,

        /// <summary>
        /// 报废
        /// </summary>
        Scrapped = 3,

        /// <summary>
        /// 故障（待修）
        /// </summary>
        Faulty = 4
    }
}