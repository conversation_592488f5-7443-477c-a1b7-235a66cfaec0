#!/usr/bin/env python3
"""
PyInstaller 构建配置脚本
用于将 AugmentCode-Free 打包成可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"🧹 清理目录: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 清理 .spec 文件
    for spec_file in Path('.').glob('*.spec'):
        print(f"🧹 清理文件: {spec_file}")
        spec_file.unlink()

def install_dependencies():
    """安装依赖"""
    print("📦 安装依赖...")
    subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)

def build_executable():
    """构建可执行文件"""
    print("🔨 开始构建可执行文件...")
    
    # PyInstaller 命令参数
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",  # 打包成单个文件
        "--windowed",  # Windows下隐藏控制台窗口（可选）
        "--name=AugmentCode-Free",  # 可执行文件名称
        "--distpath=dist",  # 输出目录
        "--workpath=build",  # 工作目录
        "--specpath=.",  # spec文件位置
        "--clean",  # 清理临时文件
        "--noconfirm",  # 不询问覆盖
        # 隐藏导入（防止反编译时看到模块结构）
        "--hidden-import=augment_tools_core",
        "--hidden-import=augment_tools_core.cli",
        "--hidden-import=augment_tools_core.common_utils",
        "--hidden-import=augment_tools_core.database_manager",
        "--hidden-import=augment_tools_core.telemetry_manager",
        "--hidden-import=click",
        "--hidden-import=colorama",
        # 添加数据文件
        "--add-data=README.md:.",
        "main.py"  # 入口文件
    ]
    
    # 在Windows上移除--windowed参数，因为这是一个CLI工具
    if "--windowed" in cmd:
        cmd.remove("--windowed")
    
    print(f"执行命令: {' '.join(cmd)}")
    subprocess.run(cmd, check=True)

def create_advanced_spec():
    """创建高级spec文件以获得更好的保护"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[('README.md', '.')],
    hiddenimports=[
        'augment_tools_core',
        'augment_tools_core.cli',
        'augment_tools_core.common_utils', 
        'augment_tools_core.database_manager',
        'augment_tools_core.telemetry_manager',
        'click',
        'colorama'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AugmentCode-Free',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # 使用UPX压缩（如果可用）
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 保持控制台，因为这是CLI工具
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('AugmentCode-Free.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("📝 创建了高级spec文件: AugmentCode-Free.spec")

def build_with_spec():
    """使用spec文件构建"""
    print("🔨 使用spec文件构建...")
    cmd = [sys.executable, "-m", "PyInstaller", "AugmentCode-Free.spec", "--clean", "--noconfirm"]
    subprocess.run(cmd, check=True)

def main():
    """主函数"""
    print("🚀 AugmentCode-Free 打包工具")
    print("=" * 50)
    
    try:
        # 1. 清理构建目录
        clean_build_dirs()
        
        # 2. 安装依赖
        install_dependencies()
        
        # 3. 创建高级spec文件
        create_advanced_spec()
        
        # 4. 使用spec文件构建
        build_with_spec()
        
        print("\n✅ 构建完成！")
        print(f"📁 可执行文件位置: {Path('dist').absolute()}")
        
        # 显示构建结果
        dist_dir = Path('dist')
        if dist_dir.exists():
            print("\n📋 构建产物:")
            for file in dist_dir.iterdir():
                size = file.stat().st_size / (1024 * 1024)  # MB
                print(f"  - {file.name} ({size:.1f} MB)")
        
        print("\n🎉 打包完成！现在您可以分发 dist/ 目录中的可执行文件，源码已完全隐藏。")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
