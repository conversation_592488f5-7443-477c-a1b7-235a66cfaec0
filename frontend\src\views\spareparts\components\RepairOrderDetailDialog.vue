<template>
  <el-dialog
    :model-value="props.modelValue"
    @update:model-value="emit('update:modelValue', $event)"
    title="返厂维修单详情"
    width="80%"
    :before-close="handleClose"
  >
    <div v-loading="loading" class="repair-order-detail">
      <div v-if="orderDetail" class="detail-content">
        <!-- 基本信息 -->
        <el-card class="info-card" style="margin-bottom: 16px;">
          <template #header>
            <span class="card-title">📋 基本信息</span>
          </template>
          <el-row :gutter="16">
            <el-col :span="8">
              <div class="info-item">
                <label>返厂单号：</label>
                <span>{{ orderDetail.orderNumber }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>维修类型：</label>
                <span>{{ orderDetail.typeName }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>优先级：</label>
                <el-tag :type="getPriorityTagType(orderDetail.priority)" size="small">
                  {{ orderDetail.priorityName }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="16" style="margin-top: 16px;">
            <el-col :span="8">
              <div class="info-item">
                <label>状态：</label>
                <el-tag :type="getStatusTagType(orderDetail.status)" size="small">
                  {{ orderDetail.statusName }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>申请人：</label>
                <span>{{ orderDetail.requesterName }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>供应商：</label>
                <span>{{ orderDetail.supplierName }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="16" style="margin-top: 16px;">
            <el-col :span="24">
              <div class="info-item">
                <label>维修标题：</label>
                <span>{{ orderDetail.title }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="16" style="margin-top: 16px;">
            <el-col :span="24">
              <div class="info-item">
                <label>维修描述：</label>
                <p>{{ orderDetail.description || '-' }}</p>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 时间信息 -->
        <el-card class="info-card" style="margin-bottom: 16px;">
          <template #header>
            <span class="card-title">⏰ 时间信息</span>
          </template>
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="info-item">
                <label>创建时间：</label>
                <span>{{ formatDateTime(orderDetail.createdAt) }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>发货时间：</label>
                <span>{{ orderDetail.shipDate ? formatDateTime(orderDetail.shipDate) : '-' }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>预计返回：</label>
                <span>{{ orderDetail.expectedReturnDate ? formatDateTime(orderDetail.expectedReturnDate) : '-' }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>实际返回：</label>
                <span>{{ orderDetail.actualReturnDate ? formatDateTime(orderDetail.actualReturnDate) : '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 费用信息 -->
        <el-card class="info-card" style="margin-bottom: 16px;">
          <template #header>
            <span class="card-title">💰 费用信息</span>
          </template>
          <el-row :gutter="16">
            <el-col :span="8">
              <div class="info-item">
                <label>预估费用：</label>
                <span>{{ orderDetail.estimatedCost ? `¥${formatAmount(orderDetail.estimatedCost)}` : '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>实际费用：</label>
                <span>{{ orderDetail.totalCost ? `¥${formatAmount(orderDetail.totalCost)}` : '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>费用差异：</label>
                <span v-if="orderDetail.estimatedCost && orderDetail.totalCost" 
                      :class="getCostDifferenceClass(orderDetail)">
                  ¥{{ formatAmount(orderDetail.totalCost - orderDetail.estimatedCost) }}
                </span>
                <span v-else>-</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 维修物品列表 -->
        <el-card class="info-card">
          <template #header>
            <span class="card-title">📦 维修物品</span>
          </template>
          <el-table :data="orderDetail.items || []" border style="width: 100%">
            <el-table-column prop="partName" label="备件名称" min-width="150" />
            <el-table-column prop="partCode" label="备件编码" width="120" />
            <el-table-column prop="quantity" label="数量" width="80" align="center" />
            <el-table-column prop="faultDescription" label="故障描述" min-width="200" />
            <el-table-column prop="repairResult" label="维修结果" min-width="150" />
            <el-table-column prop="cost" label="费用" width="100" align="right">
              <template #default="{ row }">
                {{ row.cost ? `¥${formatAmount(row.cost)}` : '-' }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button v-if="orderDetail && orderDetail.status === 1" type="primary" @click="handleApprove">
          审核通过
        </el-button>
        <el-button v-if="orderDetail && orderDetail.status === 2" type="warning" @click="handleShip">
          确认发货
        </el-button>
        <el-button v-if="orderDetail && orderDetail.status === 4" type="success" @click="handleComplete">
          完成维修
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  orderId: {
    type: Number,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'refresh'])

// 响应式数据
const loading = ref(false)
const orderDetail = ref(null)

// 计算属性和方法
const formatAmount = (amount) => {
  if (amount == null || amount === '') return '0.00'
  return Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const formatDateTime = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN')
}

const getPriorityTagType = (priority) => {
  const typeMap = {
    1: 'danger',  // 紧急
    2: 'warning', // 高
    3: 'info',    // 中
    4: 'success'  // 低
  }
  return typeMap[priority] || 'info'
}

const getStatusTagType = (status) => {
  const typeMap = {
    1: 'warning',  // 待审核
    2: 'info',     // 已审核
    3: 'primary',  // 已发货
    4: 'warning',  // 维修中
    5: 'success',  // 已完成
    6: 'danger'    // 已取消
  }
  return typeMap[status] || 'info'
}

const getCostDifferenceClass = (order) => {
  const diff = order.totalCost - order.estimatedCost
  if (diff > 0) return 'cost-over'
  if (diff < 0) return 'cost-under'
  return 'cost-equal'
}

const handleClose = () => {
  emit('update:modelValue', false)
}

const handleApprove = () => {
  // TODO: 实现审核逻辑
  ElMessage.success('审核成功')
  emit('refresh')
  handleClose()
}

const handleShip = () => {
  // TODO: 实现发货逻辑
  ElMessage.success('发货成功')
  emit('refresh')
  handleClose()
}

const handleComplete = () => {
  // TODO: 实现完成维修逻辑
  ElMessage.success('维修完成')
  emit('refresh')
  handleClose()
}

const loadOrderDetail = async () => {
  if (!props.orderId) return
  
  loading.value = true
  try {
    // TODO: 从API获取返厂单详情
    // const response = await repairOrderApi.getRepairOrderDetail(props.orderId)
    // orderDetail.value = response.data
    
    // 模拟数据
    orderDetail.value = {
      id: props.orderId,
      orderNumber: 'RF20240618001',
      typeName: '备件维修',
      priority: 2,
      priorityName: '高',
      status: 1,
      statusName: '待审核',
      title: '变频器模块故障维修',
      description: '变频器模块出现过热保护，需要返厂检修',
      requesterName: '张三',
      supplierName: 'ABB电气设备公司',
      createdAt: new Date(),
      estimatedCost: 2500.00,
      items: [
        {
          partName: '变频器模块',
          partCode: 'SP002',
          quantity: 1,
          faultDescription: '过热保护',
          repairResult: '',
          cost: null
        }
      ]
    }
  } catch (error) {
    console.error('获取返厂单详情失败:', error)
    ElMessage.error('获取返厂单详情失败')
  } finally {
    loading.value = false
  }
}

// 监听器
watch(() => props.modelValue, (newVal) => {
  if (newVal && props.orderId) {
    loadOrderDetail()
  }
})

watch(() => props.orderId, (newVal) => {
  if (newVal && props.modelValue) {
    loadOrderDetail()
  }
})
</script>

<style scoped>
.repair-order-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.info-card {
  border-radius: 8px;
}

.card-title {
  font-weight: 600;
  font-size: 16px;
}

.info-item {
  margin-bottom: 8px;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
}

.cost-over {
  color: #f56c6c;
}

.cost-under {
  color: #67c23a;
}

.cost-equal {
  color: #909399;
}
</style>
