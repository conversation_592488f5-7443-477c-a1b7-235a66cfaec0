using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ItAssetsSystem.Core.Interfaces.Services
{
    /// <summary>
    /// 任务服务接口 - 基础解耦
    /// 提供任务相关的核心业务操作
    /// </summary>
    public interface ITaskService
    {
        /// <summary>
        /// 根据ID获取任务信息
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <returns>任务信息</returns>
        Task<TaskDto> GetByIdAsync(long id);

        /// <summary>
        /// 分页获取任务列表
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResult<TaskDto>> GetPagedAsync(TaskQueryDto query);

        /// <summary>
        /// 创建任务
        /// </summary>
        /// <param name="dto">创建任务DTO</param>
        /// <returns>创建的任务信息</returns>
        Task<TaskDto> CreateAsync(CreateTaskDto dto);

        /// <summary>
        /// 更新任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="dto">更新任务DTO</param>
        /// <returns>更新后的任务信息</returns>
        Task<TaskDto> UpdateAsync(long id, UpdateTaskDto dto);

        /// <summary>
        /// 完成任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>是否完成成功</returns>
        Task<bool> CompleteAsync(long taskId);

        /// <summary>
        /// 分配任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="assigneeId">被分配人ID</param>
        /// <returns>是否分配成功</returns>
        Task<bool> AssignAsync(long taskId, int assigneeId);

        /// <summary>
        /// 删除任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <returns>是否删除成功</returns>
        Task<bool> DeleteAsync(long id);

        /// <summary>
        /// 获取用户的任务统计
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>任务统计信息</returns>
        Task<TaskStatisticsDto> GetUserTaskStatisticsAsync(int userId);

        #region 周期性任务管理

        /// <summary>
        /// 分页获取周期性任务计划列表
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResult<PeriodicTaskScheduleDto>> GetPeriodicSchedulesPagedAsync(PeriodicTaskScheduleQueryDto query);

        /// <summary>
        /// 根据ID获取周期性任务计划详情
        /// </summary>
        /// <param name="scheduleId">计划ID</param>
        /// <returns>计划详情</returns>
        Task<PeriodicTaskScheduleDto> GetPeriodicScheduleByIdAsync(long scheduleId);

        /// <summary>
        /// 创建周期性任务计划
        /// </summary>
        /// <param name="dto">创建计划DTO</param>
        /// <returns>创建的计划信息</returns>
        Task<PeriodicTaskScheduleDto> CreatePeriodicScheduleAsync(CreatePeriodicTaskScheduleDto dto);

        /// <summary>
        /// 更新周期性任务计划
        /// </summary>
        /// <param name="scheduleId">计划ID</param>
        /// <param name="dto">更新计划DTO</param>
        /// <returns>更新后的计划信息</returns>
        Task<PeriodicTaskScheduleDto> UpdatePeriodicScheduleAsync(long scheduleId, UpdatePeriodicTaskScheduleDto dto);

        /// <summary>
        /// 删除周期性任务计划
        /// </summary>
        /// <param name="scheduleId">计划ID</param>
        /// <returns>是否删除成功</returns>
        Task<bool> DeletePeriodicScheduleAsync(long scheduleId);

        /// <summary>
        /// 启用/禁用周期性任务计划
        /// </summary>
        /// <param name="scheduleId">计划ID</param>
        /// <param name="isEnabled">是否启用</param>
        /// <returns>是否操作成功</returns>
        Task<bool> EnablePeriodicScheduleAsync(long scheduleId, bool isEnabled);

        #endregion
    }

    /// <summary>
    /// 任务DTO
    /// </summary>
    public class TaskDto
    {
        public long Id { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string Priority { get; set; }
        public int? AssigneeId { get; set; }
        public string AssigneeName { get; set; }
        public int CreatedBy { get; set; }
        public string CreatedByName { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public DateTime? DueDate { get; set; }
        public DateTime? CompletedAt { get; set; }
        public string Notes { get; set; }
        public int? RelatedAssetId { get; set; }
        public string RelatedAssetName { get; set; }
        public List<TaskCommentDto> Comments { get; set; } = new List<TaskCommentDto>();
    }

    /// <summary>
    /// 任务查询DTO
    /// </summary>
    public class TaskQueryDto
    {
        public int PageIndex { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string Keyword { get; set; }
        public string Status { get; set; }
        public string Priority { get; set; }
        public int? AssigneeId { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string SortBy { get; set; }
        public string SortDirection { get; set; } = "desc";
    }

    /// <summary>
    /// 创建任务DTO
    /// </summary>
    public class CreateTaskDto
    {
        public string Title { get; set; }
        public string Description { get; set; }
        public string Priority { get; set; }
        public int? AssigneeId { get; set; }
        public DateTime? DueDate { get; set; }
        public string Notes { get; set; }
        public int? RelatedAssetId { get; set; }
    }

    /// <summary>
    /// 更新任务DTO
    /// </summary>
    public class UpdateTaskDto
    {
        public string Title { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string Priority { get; set; }
        public int? AssigneeId { get; set; }
        public DateTime? DueDate { get; set; }
        public string Notes { get; set; }
        public int? RelatedAssetId { get; set; }
    }

    /// <summary>
    /// 任务评论DTO
    /// </summary>
    public class TaskCommentDto
    {
        public long Id { get; set; }
        public long TaskId { get; set; }
        public string Content { get; set; }
        public int CreatedBy { get; set; }
        public string CreatedByName { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// 任务统计DTO
    /// </summary>
    public class TaskStatisticsDto
    {
        public int TotalTasks { get; set; }
        public int PendingTasks { get; set; }
        public int InProgressTasks { get; set; }
        public int CompletedTasks { get; set; }
        public int OverdueTasks { get; set; }
        public double CompletionRate { get; set; }
    }

    /// <summary>
    /// 周期性任务计划DTO
    /// </summary>
    public class PeriodicTaskScheduleDto
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string RecurrenceType { get; set; }
        public string CronExpression { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? NextGenerationTime { get; set; }
        public int OccurrencesGenerated { get; set; }
        public int? TotalOccurrences { get; set; }
        public int CreatorUserId { get; set; }
        public string CreatorUserName { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public List<int> DefaultAssigneeUserIds { get; set; } = new List<int>();
        public List<string> DefaultAssigneeUserNames { get; set; } = new List<string>();
    }

    /// <summary>
    /// 周期性任务计划查询DTO
    /// </summary>
    public class PeriodicTaskScheduleQueryDto
    {
        public int PageIndex { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string Keyword { get; set; }
        public string Status { get; set; }
        public string RecurrenceType { get; set; }
        public int? CreatorUserId { get; set; }
        public string SortBy { get; set; }
        public string SortDirection { get; set; } = "desc";
    }

    /// <summary>
    /// 创建周期性任务计划DTO
    /// </summary>
    public class CreatePeriodicTaskScheduleDto
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string RecurrenceType { get; set; }
        public string CronExpression { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int? TotalOccurrences { get; set; }
        public List<int> DefaultAssigneeUserIds { get; set; } = new List<int>();

        // 模板任务信息
        public string TaskName { get; set; }
        public string TaskDescription { get; set; }
        public string TaskPriority { get; set; } = "Medium";
        public string TaskType { get; set; } = "Periodic";
        public int? Points { get; set; }
        public int? AssetId { get; set; }
        public int? LocationId { get; set; }
    }

    /// <summary>
    /// 更新周期性任务计划DTO
    /// </summary>
    public class UpdatePeriodicTaskScheduleDto
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string RecurrenceType { get; set; }
        public string CronExpression { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int? TotalOccurrences { get; set; }
        public List<int> DefaultAssigneeUserIds { get; set; } = new List<int>();
    }
}
