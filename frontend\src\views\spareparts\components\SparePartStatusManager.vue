<template>
  <div class="spare-part-status-manager">
    <!-- 状态汇总卡片 -->
    <div class="status-summary-cards">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="status-card available">
            <div class="status-info">
              <div class="status-icon">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="status-details">
                <div class="status-count">{{ stockSummary.availableQuantity }}</div>
                <div class="status-label">可用库存</div>
                <div class="status-breakdown">
                  <span class="breakdown-item">新品: {{ getStatusQuantity('NEW') }}</span>
                  <span class="breakdown-item">良好: {{ getStatusQuantity('GOOD') }}</span>
                  <span class="breakdown-item">翻新: {{ getStatusQuantity('REFURBISHED') }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="status-card unavailable">
            <div class="status-info">
              <div class="status-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="status-details">
                <div class="status-count">{{ stockSummary.unavailableQuantity }}</div>
                <div class="status-label">不可用库存</div>
                <div class="status-breakdown">
                  <span class="breakdown-item">故障: {{ getStatusQuantity('FAULTY') }}</span>
                  <span class="breakdown-item">损坏: {{ getStatusQuantity('DAMAGED') }}</span>
                  <span class="breakdown-item">报废: {{ getStatusQuantity('SCRAPPED') }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="status-card in-transit">
            <div class="status-info">
              <div class="status-icon">
                <el-icon><Van /></el-icon>
              </div>
              <div class="status-details">
                <div class="status-count">{{ stockSummary.inTransitQuantity }}</div>
                <div class="status-label">在途库存</div>
                <div class="status-breakdown">
                  <span class="breakdown-item">返厂中: {{ getStatusQuantity('UNDER_REPAIR') }}</span>
                  <span class="breakdown-item">维修中: {{ getStatusQuantity('REPAIRING') }}</span>
                  <span class="breakdown-item">待检验: {{ getStatusQuantity('PENDING_INSPECTION') }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="status-card reserved">
            <div class="status-info">
              <div class="status-icon">
                <el-icon><Lock /></el-icon>
              </div>
              <div class="status-details">
                <div class="status-count">{{ stockSummary.reservedQuantity }}</div>
                <div class="status-label">预留库存</div>
                <div class="status-breakdown">
                  <span class="breakdown-item">已分配: {{ getStatusQuantity('ALLOCATED') }}</span>
                  <span class="breakdown-item">预留: {{ getStatusQuantity('RESERVED') }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 库存明细表格 -->
    <el-card class="inventory-table-card" style="margin-top: 16px;">
      <template #header>
        <div class="card-header">
          <span class="card-title">📦 库存明细</span>
          <div class="header-actions">
            <el-select v-model="filterStatus" placeholder="筛选状态" clearable size="small" style="width: 150px; margin-right: 8px;">
              <el-option v-for="status in statusTypes" :key="status.id" :label="status.name" :value="status.id">
                <span :style="{ color: status.color }">{{ status.name }}</span>
              </el-option>
            </el-select>
            <el-select v-model="filterLocation" placeholder="筛选库位" clearable size="small" style="width: 150px; margin-right: 8px;">
              <el-option v-for="location in locations" :key="location.id" :label="location.name" :value="location.id" />
            </el-select>
            <el-button type="primary" size="small" @click="handleStatusAdjustment">状态调整</el-button>
            <el-button type="success" size="small" @click="handleStatusChange">批量变更</el-button>
            <el-button type="warning" size="small" @click="handleRepairOrder">返厂维修</el-button>
          </div>
        </div>
      </template>
      
      <el-table
        ref="inventoryTable"
        :data="filteredInventories"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="locationName" label="库位" width="120" />
        <el-table-column prop="statusName" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :color="row.statusColor" effect="dark" size="small">
              {{ row.statusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="数量" width="80" align="center">
          <template #default="{ row }">
            <span class="quantity-display">{{ row.quantity }} {{ stockSummary.unit }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="batchNumber" label="批次号" width="120" />
        <el-table-column prop="serialNumbers" label="序列号" min-width="150">
          <template #default="{ row }">
            <div v-if="row.serialNumbers && row.serialNumbers.length > 0" class="serial-numbers">
              <el-tag v-for="(serial, index) in row.serialNumbers.slice(0, 2)" :key="index" size="small" style="margin-right: 4px;">
                {{ serial }}
              </el-tag>
              <span v-if="row.serialNumbers.length > 2" class="more-serials">
                +{{ row.serialNumbers.length - 2 }}个
              </span>
            </div>
            <span v-else class="no-serial">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="purchaseDate" label="采购日期" width="100">
          <template #default="{ row }">
            {{ row.purchaseDate ? formatDate(row.purchaseDate) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="warrantyExpireDate" label="保修到期" width="100">
          <template #default="{ row }">
            <span v-if="row.warrantyExpireDate" :class="getWarrantyClass(row.warrantyExpireDate)">
              {{ formatDate(row.warrantyExpireDate) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="unitCost" label="单价" width="100" align="right">
          <template #default="{ row }">
            {{ row.unitCost ? `¥${row.unitCost.toFixed(2)}` : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="notes" label="备注" min-width="120" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleViewHistory(row)">历史</el-button>
            <el-button type="text" size="small" @click="handleEditInventory(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 状态调整对话框 -->
    <StatusAdjustmentDialog
      v-model:visible="statusAdjustmentDialogVisible"
      :part-id="props.partId"
      :current-status-breakdown="stockSummary.statusBreakdown"
      :status-types="statusTypes"
      :unit="stockSummary.unit || '个'"
      @submit="handleStatusAdjustmentSubmit"
    />

    <!-- 批量状态变更对话框 -->
    <el-dialog v-model="statusChangeDialogVisible" title="批量状态变更" width="600px">
      <el-form ref="statusChangeFormRef" :model="statusChangeForm" :rules="statusChangeRules" label-width="100px">
        <el-form-item label="选中项目">
          <div class="selected-items">
            <div v-for="item in selectedInventories" :key="item.id" class="selected-item">
              <span>{{ item.locationName }} - {{ item.statusName }} - {{ item.quantity }}{{ stockSummary.unit }}</span>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="新状态" prop="newStatusId">
          <el-select v-model="statusChangeForm.newStatusId" placeholder="请选择新状态" style="width: 100%">
            <el-option v-for="status in statusTypes" :key="status.id" :label="status.name" :value="status.id">
              <span :style="{ color: status.color }">{{ status.name }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="变更原因" prop="reason">
          <el-input v-model="statusChangeForm.reason" type="textarea" :rows="3" placeholder="请输入状态变更原因" />
        </el-form-item>
        <el-form-item label="关联返厂单">
          <el-select v-model="statusChangeForm.repairOrderId" placeholder="选择关联返厂单（可选）" clearable style="width: 100%">
            <el-option v-for="order in repairOrders" :key="order.id" :label="order.orderNumber" :value="order.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="statusChangeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitStatusChange">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 返厂维修对话框 -->
    <el-dialog v-model="repairOrderDialogVisible" title="创建返厂维修单" width="800px">
      <el-form ref="repairOrderFormRef" :model="repairOrderForm" :rules="repairOrderRules" label-width="100px">
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="维修类型" prop="type">
              <el-select v-model="repairOrderForm.type" placeholder="请选择维修类型" style="width: 100%">
                <el-option label="故障维修" :value="1" />
                <el-option label="备件维修" :value="2" />
                <el-option label="预防性维修" :value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="repairOrderForm.priority" placeholder="请选择优先级" style="width: 100%">
                <el-option label="紧急" :value="1" />
                <el-option label="高" :value="2" />
                <el-option label="中" :value="3" />
                <el-option label="低" :value="4" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="维修标题" prop="title">
          <el-input v-model="repairOrderForm.title" placeholder="请输入维修标题" />
        </el-form-item>
        <el-form-item label="维修描述" prop="description">
          <el-input v-model="repairOrderForm.description" type="textarea" :rows="3" placeholder="请输入维修描述" />
        </el-form-item>
        <el-form-item label="关联故障">
          <el-select v-model="repairOrderForm.faultId" placeholder="选择关联故障（可选）" clearable style="width: 100%">
            <el-option v-for="fault in faults" :key="fault.id" :label="fault.title" :value="fault.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="维修供应商">
          <el-select v-model="repairOrderForm.supplierId" placeholder="选择维修供应商" style="width: 100%">
            <el-option v-for="supplier in suppliers" :key="supplier.id" :label="supplier.name" :value="supplier.id" />
          </el-select>
        </el-form-item>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="预估费用">
              <el-input-number v-model="repairOrderForm.estimatedCost" :min="0" :precision="2" placeholder="预估费用" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预估天数">
              <el-input-number v-model="repairOrderForm.estimatedDays" :min="1" placeholder="预估维修天数" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注">
          <el-input v-model="repairOrderForm.notes" type="textarea" :rows="2" placeholder="请输入备注" />
        </el-form-item>
        
        <!-- 维修物品列表 -->
        <el-form-item label="维修物品">
          <div class="repair-items">
            <div v-for="item in selectedInventories" :key="item.id" class="repair-item">
              <div class="item-info">
                <span class="item-name">{{ stockSummary.partName }}</span>
                <span class="item-location">{{ item.locationName }}</span>
                <span class="item-status">{{ item.statusName }}</span>
                <span class="item-quantity">{{ item.quantity }}{{ stockSummary.unit }}</span>
              </div>
              <div class="item-details">
                <el-input 
                  v-model="item.faultDescription" 
                  placeholder="故障描述" 
                  size="small" 
                  style="margin-bottom: 4px;"
                />
                <el-input 
                  v-model="item.serialNumber" 
                  placeholder="序列号（可选）" 
                  size="small"
                />
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="repairOrderDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitRepairOrder">创建返厂单</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { CircleCheck, Warning, Van, Lock } from '@element-plus/icons-vue'
import StatusAdjustmentDialog from './StatusAdjustmentDialog.vue'
import {
  getSparePartStockSummary,
  getSparePartInventories,
  getSparePartStatusTypes,
  batchUpdateSparePartStatus,
  createRepairOrder,
  getSparePartLocations,
  adjustSparePartStatus
} from '@/api/spareparts'

// Props
const props = defineProps({
  partId: {
    type: Number,
    required: true
  }
})

// 响应式数据
const stockSummary = ref({
  partId: 0,
  partCode: '',
  partName: '',
  unit: '',
  totalQuantity: 0,
  availableQuantity: 0,
  unavailableQuantity: 0,
  inTransitQuantity: 0,
  reservedQuantity: 0,
  statusBreakdown: []
})

const inventories = ref([])
const statusTypes = ref([])
const locations = ref([])
const repairOrders = ref([])
const faults = ref([])
const suppliers = ref([])

const filterStatus = ref(null)
const filterLocation = ref(null)
const selectedInventories = ref([])

const statusAdjustmentDialogVisible = ref(false)
const statusChangeDialogVisible = ref(false)
const repairOrderDialogVisible = ref(false)

// 表单数据
const statusChangeForm = reactive({
  newStatusId: null,
  reason: '',
  repairOrderId: null
})

const repairOrderForm = reactive({
  type: 2, // 默认备件维修
  title: '',
  description: '',
  priority: 3, // 默认中等优先级
  faultId: null,
  supplierId: null,
  estimatedCost: null,
  estimatedDays: null,
  notes: ''
})

// 表单验证规则
const statusChangeRules = {
  newStatusId: [{ required: true, message: '请选择新状态', trigger: 'change' }],
  reason: [{ required: true, message: '请输入变更原因', trigger: 'blur' }]
}

const repairOrderRules = {
  type: [{ required: true, message: '请选择维修类型', trigger: 'change' }],
  title: [{ required: true, message: '请输入维修标题', trigger: 'blur' }],
  description: [{ required: true, message: '请输入维修描述', trigger: 'blur' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }]
}

// 计算属性
const filteredInventories = computed(() => {
  let result = inventories.value
  
  if (filterStatus.value) {
    result = result.filter(item => item.statusId === filterStatus.value)
  }
  
  if (filterLocation.value) {
    result = result.filter(item => item.locationId === filterLocation.value)
  }
  
  return result
})

// 方法
const getStatusQuantity = (statusCode) => {
  const status = stockSummary.value.statusBreakdown.find(s => s.statusCode === statusCode)
  return status ? status.quantity : 0
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

const getWarrantyClass = (expireDate) => {
  const now = new Date()
  const expire = new Date(expireDate)
  const diffDays = Math.ceil((expire - now) / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) return 'warranty-expired'
  if (diffDays < 30) return 'warranty-warning'
  return 'warranty-normal'
}

const handleSelectionChange = (selection) => {
  selectedInventories.value = selection
}

const handleStatusAdjustment = () => {
  statusAdjustmentDialogVisible.value = true
}

const handleStatusChange = () => {
  if (selectedInventories.value.length === 0) {
    ElMessage.warning('请先选择要变更状态的库存项目')
    return
  }
  statusChangeDialogVisible.value = true
}

const handleRepairOrder = () => {
  if (selectedInventories.value.length === 0) {
    ElMessage.warning('请先选择要返厂维修的库存项目')
    return
  }

  // 检查选中的项目是否包含故障状态的备件
  const hasFaultyItems = selectedInventories.value.some(item =>
    item.statusCategory === 'Unavailable' &&
    (item.statusName.includes('故障') || item.statusName.includes('损坏'))
  )

  if (!hasFaultyItems) {
    ElMessageBox.confirm(
      '通常返厂维修的是故障或损坏的备件，您选择的项目中没有此类状态。是否继续？',
      '确认返厂',
      {
        confirmButtonText: '继续',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      repairOrderDialogVisible.value = true
    }).catch(() => {
      // 用户取消
    })
  } else {
    repairOrderDialogVisible.value = true
  }
}

const handleStatusAdjustmentSubmit = async (adjustmentData) => {
  try {
    const response = await adjustSparePartStatus(adjustmentData)

    if (response.success) {
      ElMessage.success('状态调整成功')
      statusAdjustmentDialogVisible.value = false

      // 重新加载数据
      await loadStockSummary()
      await loadInventories()
    } else {
      ElMessage.error('状态调整失败: ' + response.message)
    }
  } catch (error) {
    console.error('状态调整失败:', error)
    ElMessage.error('状态调整失败')
  }
}

const submitStatusChange = async () => {
  try {
    // 构建批量更新请求
    const updates = selectedInventories.value.map(item => ({
      inventoryId: item.id,
      newStatusId: statusChangeForm.newStatusId,
      reason: statusChangeForm.reason
    }))

    const response = await batchUpdateSparePartStatus({ updates })

    if (response.success) {
      ElMessage.success(`状态变更成功，共更新 ${response.data.successCount} 项`)
      statusChangeDialogVisible.value = false

      // 重新加载数据
      await loadStockSummary()
      await loadInventories()

      // 清空选择
      selectedInventories.value = []

      // 重置表单
      statusChangeForm.newStatusId = null
      statusChangeForm.reason = ''
      statusChangeForm.repairOrderId = null
    } else {
      ElMessage.error('状态变更失败: ' + response.message)
    }
  } catch (error) {
    console.error('状态变更失败:', error)
    ElMessage.error('状态变更失败')
  }
}

const submitRepairOrder = async () => {
  try {
    const requestData = {
      title: repairOrderForm.title,
      priority: repairOrderForm.priority,
      supplierId: repairOrderForm.supplierId,
      faultId: repairOrderForm.faultId,
      inventoryIds: selectedInventories.value.map(item => item.id),
      faultDescription: repairOrderForm.description,
      notes: repairOrderForm.notes
    }

    const response = await createRepairOrder(requestData)

    if (response.success) {
      ElMessage.success(`返厂维修单创建成功，单号: ${response.data.orderNumber}`)
      repairOrderDialogVisible.value = false

      // 重新加载数据
      await loadStockSummary()
      await loadInventories()

      // 清空选择
      selectedInventories.value = []

      // 重置表单
      Object.assign(repairOrderForm, {
        type: 2,
        title: '',
        description: '',
        priority: 3,
        faultId: null,
        supplierId: null,
        estimatedCost: null,
        estimatedDays: null,
        notes: ''
      })
    } else {
      ElMessage.error('创建返厂维修单失败: ' + response.message)
    }
  } catch (error) {
    console.error('创建返厂维修单失败:', error)
    ElMessage.error('创建返厂维修单失败')
  }
}

const handleViewHistory = (row) => {
  // TODO: 显示状态变更历史
  ElMessage.info('查看状态变更历史')
}

const handleEditInventory = (row) => {
  // TODO: 编辑库存明细
  ElMessage.info('编辑库存明细')
}

// 生命周期
onMounted(async () => {
  // 先加载状态类型和库位，再加载库存数据
  await Promise.all([loadStatusTypes(), loadLocations()])
  await loadStockSummary()
  await loadInventories()
})

const loadStockSummary = async () => {
  try {
    const response = await getSparePartStockSummary(props.partId)
    if (response.success) {
      stockSummary.value = response.data

      // 确保所有状态类型都在statusBreakdown中显示，即使数量为0
      if (statusTypes.value.length > 0) {
        const existingStatusCodes = stockSummary.value.statusBreakdown.map(s => s.statusCode)
        statusTypes.value.forEach(statusType => {
          if (!existingStatusCodes.includes(statusType.code)) {
            stockSummary.value.statusBreakdown.push({
              statusCode: statusType.code,
              statusName: statusType.name,
              category: statusType.category,
              quantity: 0,
              color: statusType.color
            })
          }
        })

        // 按状态类型的排序顺序重新排列
        stockSummary.value.statusBreakdown.sort((a, b) => {
          const aIndex = statusTypes.value.findIndex(st => st.code === a.statusCode)
          const bIndex = statusTypes.value.findIndex(st => st.code === b.statusCode)
          return aIndex - bIndex
        })
      }
    } else {
      ElMessage.error('加载库存汇总失败: ' + response.message)
    }
  } catch (error) {
    console.error('加载库存汇总失败:', error)
    ElMessage.error('加载库存汇总失败')
  }
}

const loadInventories = async () => {
  try {
    const response = await getSparePartInventories(props.partId, {
      pageIndex: 1,
      pageSize: 1000 // 加载所有数据
    })
    if (response.success) {
      inventories.value = response.data.items || []
    } else {
      ElMessage.error('加载库存明细失败: ' + response.message)
    }
  } catch (error) {
    console.error('加载库存明细失败:', error)
    ElMessage.error('加载库存明细失败')
  }
}

const loadStatusTypes = async () => {
  try {
    const response = await getSparePartStatusTypes()
    if (response.success) {
      statusTypes.value = response.data || []
    } else {
      ElMessage.error('加载状态类型失败: ' + response.message)
    }
  } catch (error) {
    console.error('加载状态类型失败:', error)
    ElMessage.error('加载状态类型失败')
  }
}

const loadLocations = async () => {
  try {
    const response = await getSparePartLocations()
    if (response.success) {
      locations.value = response.data || []
    } else {
      ElMessage.error('加载库位数据失败: ' + response.message)
    }
  } catch (error) {
    console.error('加载库位数据失败:', error)
    ElMessage.error('加载库位数据失败')
  }
}

// 监听partId变化
watch(() => props.partId, async (newPartId) => {
  if (newPartId) {
    await loadStockSummary()
    await loadInventories()
  }
})
</script>

<style scoped>
.spare-part-status-manager {
  padding: 16px;
}

/* 状态汇总卡片样式 */
.status-summary-cards {
  margin-bottom: 16px;
}

.status-card {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-card.available {
  border-left: 4px solid #67c23a;
}

.status-card.unavailable {
  border-left: 4px solid #f56c6c;
}

.status-card.in-transit {
  border-left: 4px solid #e6a23c;
}

.status-card.reserved {
  border-left: 4px solid #909399;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-icon {
  font-size: 32px;
  opacity: 0.8;
}

.status-card.available .status-icon {
  color: #67c23a;
}

.status-card.unavailable .status-icon {
  color: #f56c6c;
}

.status-card.in-transit .status-icon {
  color: #e6a23c;
}

.status-card.reserved .status-icon {
  color: #909399;
}

.status-details {
  flex: 1;
}

.status-count {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.status-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.status-breakdown {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.breakdown-item {
  font-size: 12px;
  color: #909399;
}

/* 库存明细表格样式 */
.inventory-table-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-weight: 600;
  font-size: 16px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.quantity-display {
  font-weight: 600;
  color: #303133;
}

.serial-numbers {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.more-serials {
  font-size: 12px;
  color: #909399;
}

.no-serial {
  color: #c0c4cc;
}

/* 保修期样式 */
.warranty-normal {
  color: #67c23a;
}

.warranty-warning {
  color: #e6a23c;
  font-weight: 600;
}

.warranty-expired {
  color: #f56c6c;
  font-weight: 600;
}

/* 对话框样式 */
.selected-items {
  max-height: 120px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px;
  background: #f5f7fa;
}

.selected-item {
  padding: 4px 8px;
  margin-bottom: 4px;
  background: white;
  border-radius: 4px;
  font-size: 14px;
  color: #606266;
}

.selected-item:last-child {
  margin-bottom: 0;
}

.repair-items {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}

.repair-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  border-bottom: 1px solid #ebeef5;
}

.repair-item:last-child {
  border-bottom: none;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.item-name {
  font-weight: 600;
  color: #303133;
}

.item-location,
.item-status,
.item-quantity {
  font-size: 12px;
  color: #909399;
}

.item-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
