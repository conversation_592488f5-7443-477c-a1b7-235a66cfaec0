-- 检查字段是否存在
SELECT COUNT(*) as field_exists FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = 'itassets' AND TABLE_NAME = 'suppliers' AND COLUMN_NAME = 'supplier_type';

-- 添加供应商类型字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'itassets' AND TABLE_NAME = 'suppliers' AND COLUMN_NAME = 'supplier_type') > 0,
    'SELECT "Field already exists" as result',
    'ALTER TABLE suppliers ADD COLUMN supplier_type INT NOT NULL DEFAULT 1 COMMENT "供应商类型：1=采购，2=维修，3=采购+维修"'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 更新现有数据
UPDATE suppliers SET supplier_type = 1 WHERE supplier_type IS NULL OR supplier_type = 0;

-- 查看结果
SELECT Id, Name, Code, supplier_type FROM suppliers LIMIT 5;
