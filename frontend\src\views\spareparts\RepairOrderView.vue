<template>
  <div class="repair-order-view">
    <div class="page-header">
      <h2>返厂维修管理</h2>
      <el-button type="primary" @click="handleCreateOrder">新建返厂单</el-button>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="stat-card pending">
            <div class="stat-info">
              <div class="stat-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-details">
                <div class="stat-count">{{ stats.pendingCount }}</div>
                <div class="stat-label">待审核</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card in-repair">
            <div class="stat-info">
              <div class="stat-icon">
                <el-icon><Tools /></el-icon>
              </div>
              <div class="stat-details">
                <div class="stat-count">{{ stats.inRepairCount }}</div>
                <div class="stat-label">维修中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card completed">
            <div class="stat-info">
              <div class="stat-icon">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-details">
                <div class="stat-count">{{ stats.completedCount }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card total-cost">
            <div class="stat-info">
              <div class="stat-icon">
                <el-icon><Money /></el-icon>
              </div>
              <div class="stat-details">
                <div class="stat-count">¥{{ formatAmount(stats.totalCost) }}</div>
                <div class="stat-label">总费用</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="返厂单号">
          <el-input v-model="queryParams.orderNumber" placeholder="返厂单号" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="维修类型">
          <el-select v-model="queryParams.type" placeholder="维修类型" clearable>
            <el-option label="故障维修" :value="1" />
            <el-option label="备件维修" :value="2" />
            <el-option label="预防性维修" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="状态" clearable>
            <el-option label="待审核" :value="1" />
            <el-option label="已审核" :value="2" />
            <el-option label="已发货" :value="3" />
            <el-option label="维修中" :value="4" />
            <el-option label="已完成" :value="5" />
            <el-option label="已取消" :value="6" />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级">
          <el-select v-model="queryParams.priority" placeholder="优先级" clearable>
            <el-option label="紧急" :value="1" />
            <el-option label="高" :value="2" />
            <el-option label="中" :value="3" />
            <el-option label="低" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="申请人">
          <el-select v-model="queryParams.requesterId" placeholder="申请人" clearable filterable>
            <el-option v-for="user in users" :key="user.id" :label="user.name" :value="user.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="供应商">
          <el-select v-model="queryParams.supplierId" placeholder="供应商" clearable>
            <el-option v-for="supplier in suppliers" :key="supplier.id" :label="supplier.name" :value="supplier.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleDateRangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button type="success" @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <el-table
      v-loading="loading"
      :data="repairOrderList"
      border
      style="width: 100%"
      @sort-change="handleSortChange"
    >
      <el-table-column prop="orderNumber" label="返厂单号" width="150" sortable="custom">
        <template #default="{ row }">
          <el-link type="primary" @click="handleViewDetail(row)">{{ row.orderNumber }}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="typeName" label="维修类型" width="100" />
      <el-table-column prop="title" label="维修标题" min-width="200" show-overflow-tooltip />
      <el-table-column prop="priorityName" label="优先级" width="80">
        <template #default="{ row }">
          <el-tag :type="getPriorityTagType(row.priority)" size="small">
            {{ row.priorityName }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="statusName" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusTagType(row.status)" size="small">
            {{ row.statusName }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="requesterName" label="申请人" width="100" />
      <el-table-column prop="supplierName" label="供应商" width="120" />
      <el-table-column prop="itemCount" label="物品数量" width="80" align="center">
        <template #default="{ row }">
          <el-tag size="small" type="info">{{ row.itemCount }}项</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="estimatedCost" label="预估费用" width="100" align="right">
        <template #default="{ row }">
          {{ row.estimatedCost ? `¥${formatAmount(row.estimatedCost)}` : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="totalCost" label="实际费用" width="100" align="right">
        <template #default="{ row }">
          {{ row.totalCost ? `¥${formatAmount(row.totalCost)}` : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="shipDate" label="发货日期" width="100">
        <template #default="{ row }">
          {{ row.shipDate ? formatDate(row.shipDate) : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="expectedReturnDate" label="预计返回" width="100">
        <template #default="{ row }">
          {{ row.expectedReturnDate ? formatDate(row.expectedReturnDate) : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="actualReturnDate" label="实际返回" width="100">
        <template #default="{ row }">
          <span v-if="row.actualReturnDate" :class="getReturnDateClass(row)">
            {{ formatDate(row.actualReturnDate) }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="createdAt" label="创建时间" width="100">
        <template #default="{ row }">
          {{ formatDate(row.createdAt) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button type="info" size="small" @click="handleViewDetail(row)">详情</el-button>
          <el-button 
            v-if="row.status === 1" 
            type="primary" 
            size="small" 
            @click="handleApprove(row)"
          >
            审核
          </el-button>
          <el-button 
            v-if="row.status === 2" 
            type="warning" 
            size="small" 
            @click="handleShip(row)"
          >
            发货
          </el-button>
          <el-button 
            v-if="row.status === 4" 
            type="success" 
            size="small" 
            @click="handleComplete(row)"
          >
            完成
          </el-button>
          <el-button 
            v-if="[1, 2].includes(row.status)" 
            type="danger" 
            size="small" 
            @click="handleCancel(row)"
          >
            取消
          </el-button>
          <el-button type="text" size="small" @click="handlePrint(row)">打印</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        v-model:currentPage="queryParams.pageIndex"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 返厂单详情对话框 -->
    <RepairOrderDetailDialog
      v-model="detailDialogVisible"
      :order-id="currentOrderId"
      @refresh="fetchRepairOrderList"
    />

    <!-- 创建返厂单对话框 -->
    <RepairOrderDialog
      v-model:visible="createDialogVisible"
      :order-data="currentRepairOrder"
      :mode="dialogMode"
      @submit="handleCreateSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Clock, Tools, CircleCheck, Money } from '@element-plus/icons-vue'
import RepairOrderDetailDialog from './components/RepairOrderDetailDialog.vue'
import RepairOrderDialog from './components/RepairOrderDialog.vue'

// 响应式数据
const loading = ref(false)
const repairOrderList = ref([])
const total = ref(0)
const users = ref([])
const suppliers = ref([])
const dateRange = ref([])

const detailDialogVisible = ref(false)
const createDialogVisible = ref(false)
const currentOrderId = ref(null)
const currentRepairOrder = ref({})
const dialogMode = ref('create')

// 统计数据
const stats = ref({
  pendingCount: 0,
  inRepairCount: 0,
  completedCount: 0,
  totalCost: 0
})

// 查询参数
const queryParams = reactive({
  pageIndex: 1,
  pageSize: 10,
  orderNumber: '',
  type: null,
  status: null,
  priority: null,
  requesterId: null,
  supplierId: null,
  startDate: null,
  endDate: null,
  sortBy: '',
  sortOrder: ''
})

// 方法
const formatAmount = (amount) => {
  if (amount == null || amount === '') return '0.00'
  return Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-CN')
}

const getPriorityTagType = (priority) => {
  const typeMap = {
    1: 'danger',  // 紧急
    2: 'warning', // 高
    3: 'info',    // 中
    4: 'success'  // 低
  }
  return typeMap[priority] || 'info'
}

const getStatusTagType = (status) => {
  const typeMap = {
    1: 'warning',  // 待审核
    2: 'info',     // 已审核
    3: 'primary',  // 已发货
    4: 'warning',  // 维修中
    5: 'success',  // 已完成
    6: 'danger'    // 已取消
  }
  return typeMap[status] || 'info'
}

const getReturnDateClass = (row) => {
  if (!row.expectedReturnDate || !row.actualReturnDate) return ''
  
  const expected = new Date(row.expectedReturnDate)
  const actual = new Date(row.actualReturnDate)
  
  if (actual > expected) return 'return-late'
  if (actual <= expected) return 'return-ontime'
  return ''
}

const handleQuery = () => {
  queryParams.pageIndex = 1
  fetchRepairOrderList()
}

const resetQuery = () => {
  Object.keys(queryParams).forEach(key => {
    if (['pageIndex', 'pageSize'].includes(key)) return
    if (key === 'pageIndex') queryParams[key] = 1
    else if (key === 'pageSize') queryParams[key] = 10
    else queryParams[key] = key.includes('Date') ? null : ''
  })
  dateRange.value = []
  handleQuery()
}

const handleDateRangeChange = (dates) => {
  if (dates && dates.length === 2) {
    queryParams.startDate = dates[0]
    queryParams.endDate = dates[1]
  } else {
    queryParams.startDate = null
    queryParams.endDate = null
  }
}

const handleSortChange = (column) => {
  if (column.prop && column.order) {
    queryParams.sortBy = column.prop
    queryParams.sortOrder = column.order === 'ascending' ? 'asc' : 'desc'
  } else {
    queryParams.sortBy = ''
    queryParams.sortOrder = ''
  }
  fetchRepairOrderList()
}

const handleSizeChange = (size) => {
  queryParams.pageSize = size
  fetchRepairOrderList()
}

const handleCurrentChange = (page) => {
  queryParams.pageIndex = page
  fetchRepairOrderList()
}

const handleCreateOrder = () => {
  console.log('创建维修单按钮被点击')
  currentRepairOrder.value = {}
  dialogMode.value = 'create'
  createDialogVisible.value = true
  console.log('对话框状态:', createDialogVisible.value)
}

const handleViewDetail = (row) => {
  currentOrderId.value = row.id
  detailDialogVisible.value = true
}

const handleApprove = (row) => {
  ElMessageBox.confirm(`确认审核返厂单 "${row.orderNumber}" 吗？`, '确认审核', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // TODO: 实现审核逻辑
    ElMessage.success('审核成功')
    fetchRepairOrderList()
  }).catch(() => {})
}

const handleShip = (row) => {
  ElMessageBox.confirm(`确认发货返厂单 "${row.orderNumber}" 吗？`, '确认发货', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // TODO: 实现发货逻辑
    ElMessage.success('发货成功')
    fetchRepairOrderList()
  }).catch(() => {})
}

const handleComplete = (row) => {
  currentOrderId.value = row.id
  // TODO: 打开完成维修对话框
  ElMessage.info('打开完成维修对话框')
}

const handleCancel = (row) => {
  ElMessageBox.confirm(`确认取消返厂单 "${row.orderNumber}" 吗？`, '确认取消', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // TODO: 实现取消逻辑
    ElMessage.success('取消成功')
    fetchRepairOrderList()
  }).catch(() => {})
}

const handlePrint = (row) => {
  // TODO: 实现打印逻辑
  ElMessage.info('打印返厂单')
}

const handleExport = () => {
  // TODO: 实现导出逻辑
  ElMessage.info('导出返厂单列表')
}

const handleCreateSuccess = () => {
  fetchRepairOrderList()
  loadStats()
}

const fetchRepairOrderList = async () => {
  loading.value = true
  try {
    // TODO: 从API获取返厂单列表
    // const response = await repairOrderApi.getRepairOrders(queryParams)
    // repairOrderList.value = response.data.items
    // total.value = response.data.total
    
    // 模拟数据
    repairOrderList.value = []
    total.value = 0
  } catch (error) {
    console.error('获取返厂单列表失败:', error)
    ElMessage.error('获取返厂单列表失败')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    // TODO: 从API获取统计数据
    // const response = await repairOrderApi.getStats()
    // stats.value = response.data
    
    // 模拟数据
    stats.value = {
      pendingCount: 5,
      inRepairCount: 12,
      completedCount: 48,
      totalCost: 125600.50
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

const loadUsers = async () => {
  try {
    // TODO: 从API获取用户列表
    users.value = []
  } catch (error) {
    console.error('获取用户列表失败:', error)
  }
}

const loadSuppliers = async () => {
  try {
    // TODO: 从API获取供应商列表
    suppliers.value = []
  } catch (error) {
    console.error('获取供应商列表失败:', error)
  }
}

// 生命周期
onMounted(() => {
  fetchRepairOrderList()
  loadStats()
  loadUsers()
  loadSuppliers()
})
</script>

<style scoped>
.repair-order-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

/* 统计卡片样式 */
.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card.pending {
  border-left: 4px solid #e6a23c;
}

.stat-card.in-repair {
  border-left: 4px solid #409eff;
}

.stat-card.completed {
  border-left: 4px solid #67c23a;
}

.stat-card.total-cost {
  border-left: 4px solid #909399;
}

.stat-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-icon {
  font-size: 32px;
  opacity: 0.8;
}

.stat-card.pending .stat-icon {
  color: #e6a23c;
}

.stat-card.in-repair .stat-icon {
  color: #409eff;
}

.stat-card.completed .stat-icon {
  color: #67c23a;
}

.stat-card.total-cost .stat-icon {
  color: #909399;
}

.stat-details {
  flex: 1;
}

.stat-count {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

/* 筛选区域样式 */
.filter-container {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 返回日期样式 */
.return-ontime {
  color: #67c23a;
  font-weight: 600;
}

.return-late {
  color: #f56c6c;
  font-weight: 600;
}
</style>
