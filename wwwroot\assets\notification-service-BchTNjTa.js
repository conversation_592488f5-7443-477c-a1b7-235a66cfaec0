import{l as e,a4 as t,bd as n}from"./index-C7OOw0MO.js";class o extends Error{constructor(e,t){const n=new.target.prototype;super(`${e}: Status code '${t}'`),this.statusCode=t,this.__proto__=n}}class s extends Error{constructor(e="A timeout occurred."){const t=new.target.prototype;super(e),this.__proto__=t}}class r extends Error{constructor(e="An abort occurred."){const t=new.target.prototype;super(e),this.__proto__=t}}class i extends Error{constructor(e,t){const n=new.target.prototype;super(e),this.transport=t,this.errorType="UnsupportedTransportError",this.__proto__=n}}class c extends Error{constructor(e,t){const n=new.target.prototype;super(e),this.transport=t,this.errorType="DisabledTransportError",this.__proto__=n}}class a extends Error{constructor(e,t){const n=new.target.prototype;super(e),this.transport=t,this.errorType="FailedToStartTransportError",this.__proto__=n}}class h extends Error{constructor(e){const t=new.target.prototype;super(e),this.errorType="FailedToNegotiateWithServerError",this.__proto__=t}}class l extends Error{constructor(e,t){const n=new.target.prototype;super(e),this.innerErrors=t,this.__proto__=n}}class g{constructor(e,t,n){this.statusCode=e,this.statusText=t,this.content=n}}class u{get(e,t){return this.send({...t,method:"GET",url:e})}post(e,t){return this.send({...t,method:"POST",url:e})}delete(e,t){return this.send({...t,method:"DELETE",url:e})}getCookieString(e){return""}}var d,_;(_=d||(d={}))[_.Trace=0]="Trace",_[_.Debug=1]="Debug",_[_.Information=2]="Information",_[_.Warning=3]="Warning",_[_.Error=4]="Error",_[_.Critical=5]="Critical",_[_.None=6]="None";class p{constructor(){}log(e,t){}}p.instance=new p;class f{static isRequired(e,t){if(null==e)throw new Error(`The '${t}' argument is required.`)}static isNotEmpty(e,t){if(!e||e.match(/^\s*$/))throw new Error(`The '${t}' argument should not be empty.`)}static isIn(e,t,n){if(!(e in t))throw new Error(`Unknown ${n} value: ${e}.`)}}class m{static get isBrowser(){return!m.isNode&&"object"==typeof window&&"object"==typeof window.document}static get isWebWorker(){return!m.isNode&&"object"==typeof self&&"importScripts"in self}static get isReactNative(){return!m.isNode&&"object"==typeof window&&void 0===window.document}static get isNode(){return"undefined"!=typeof process&&process.release&&"node"===process.release.name}}function w(e,t){let n="";return v(e)?(n=`Binary data of length ${e.byteLength}`,t&&(n+=`. Content: '${function(e){const t=new Uint8Array(e);let n="";return t.forEach((e=>{n+=`0x${e<16?"0":""}${e.toString(16)} `})),n.substr(0,n.length-1)}(e)}'`)):"string"==typeof e&&(n=`String data of length ${e.length}`,t&&(n+=`. Content: '${e}'`)),n}function v(e){return e&&"undefined"!=typeof ArrayBuffer&&(e instanceof ArrayBuffer||e.constructor&&"ArrayBuffer"===e.constructor.name)}async function b(e,t,n,o,s,r){const i={},[c,a]=C();i[c]=a,e.log(d.Trace,`(${t} transport) sending data. ${w(s,r.logMessageContent)}.`);const h=v(s)?"arraybuffer":"text",l=await n.post(o,{content:s,headers:{...i,...r.headers},responseType:h,timeout:r.timeout,withCredentials:r.withCredentials});e.log(d.Trace,`(${t} transport) request complete. Response status: ${l.statusCode}.`)}class y{constructor(e,t){this._subject=e,this._observer=t}dispose(){const e=this._subject.observers.indexOf(this._observer);e>-1&&this._subject.observers.splice(e,1),0===this._subject.observers.length&&this._subject.cancelCallback&&this._subject.cancelCallback().catch((e=>{}))}}class S{constructor(e){this._minLevel=e,this.out=console}log(e,t){if(e>=this._minLevel){const n=`[${(new Date).toISOString()}] ${d[e]}: ${t}`;switch(e){case d.Critical:case d.Error:this.out.error(n);break;case d.Warning:this.out.warn(n);break;case d.Information:this.out.info(n);break;default:this.out.log(n)}}}}function C(){let e="X-SignalR-User-Agent";return m.isNode&&(e="User-Agent"),[e,k("8.0.7",I(),E(),T())]}function k(e,t,n,o){let s="Microsoft SignalR/";const r=e.split(".");return s+=`${r[0]}.${r[1]}`,s+=` (${e}; `,s+=t&&""!==t?`${t}; `:"Unknown OS; ",s+=`${n}`,s+=o?`; ${o}`:"; Unknown Runtime Version",s+=")",s}function I(){if(!m.isNode)return"";switch(process.platform){case"win32":return"Windows NT";case"darwin":return"macOS";case"linux":return"Linux";default:return process.platform}}function T(){if(m.isNode)return process.versions.node}function E(){return m.isNode?"NodeJS":"Browser"}function P(e){return e.stack?e.stack:e.message?e.message:`${e}`}class R extends u{constructor(e){if(super(),this._logger=e,"undefined"==typeof fetch||m.isNode){const e="function"==typeof __webpack_require__?__non_webpack_require__:require;this._jar=new(e("tough-cookie").CookieJar),"undefined"==typeof fetch?this._fetchType=e("node-fetch"):this._fetchType=fetch,this._fetchType=e("fetch-cookie")(this._fetchType,this._jar)}else this._fetchType=fetch.bind(function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("could not find global")}());if("undefined"==typeof AbortController){const e="function"==typeof __webpack_require__?__non_webpack_require__:require;this._abortControllerType=e("abort-controller")}else this._abortControllerType=AbortController}async send(e){if(e.abortSignal&&e.abortSignal.aborted)throw new r;if(!e.method)throw new Error("No method defined.");if(!e.url)throw new Error("No url defined.");const t=new this._abortControllerType;let n;e.abortSignal&&(e.abortSignal.onabort=()=>{t.abort(),n=new r});let i,c=null;if(e.timeout){const o=e.timeout;c=setTimeout((()=>{t.abort(),this._logger.log(d.Warning,"Timeout from HTTP request."),n=new s}),o)}""===e.content&&(e.content=void 0),e.content&&(e.headers=e.headers||{},v(e.content)?e.headers["Content-Type"]="application/octet-stream":e.headers["Content-Type"]="text/plain;charset=UTF-8");try{i=await this._fetchType(e.url,{body:e.content,cache:"no-cache",credentials:!0===e.withCredentials?"include":"same-origin",headers:{"X-Requested-With":"XMLHttpRequest",...e.headers},method:e.method,mode:"cors",redirect:"follow",signal:t.signal})}catch(l){if(n)throw n;throw this._logger.log(d.Warning,`Error from HTTP request. ${l}.`),l}finally{c&&clearTimeout(c),e.abortSignal&&(e.abortSignal.onabort=null)}if(!i.ok){const e=await $(i,"text");throw new o(e||i.statusText,i.status)}const a=$(i,e.responseType),h=await a;return new g(i.status,i.statusText,h)}getCookieString(e){let t="";return m.isNode&&this._jar&&this._jar.getCookies(e,((e,n)=>t=n.join("; "))),t}}function $(e,t){let n;switch(t){case"arraybuffer":n=e.arrayBuffer();break;case"text":default:n=e.text();break;case"blob":case"document":case"json":throw new Error(`${t} is not supported.`)}return n}class D extends u{constructor(e){super(),this._logger=e}send(e){return e.abortSignal&&e.abortSignal.aborted?Promise.reject(new r):e.method?e.url?new Promise(((t,n)=>{const i=new XMLHttpRequest;i.open(e.method,e.url,!0),i.withCredentials=void 0===e.withCredentials||e.withCredentials,i.setRequestHeader("X-Requested-With","XMLHttpRequest"),""===e.content&&(e.content=void 0),e.content&&(v(e.content)?i.setRequestHeader("Content-Type","application/octet-stream"):i.setRequestHeader("Content-Type","text/plain;charset=UTF-8"));const c=e.headers;c&&Object.keys(c).forEach((e=>{i.setRequestHeader(e,c[e])})),e.responseType&&(i.responseType=e.responseType),e.abortSignal&&(e.abortSignal.onabort=()=>{i.abort(),n(new r)}),e.timeout&&(i.timeout=e.timeout),i.onload=()=>{e.abortSignal&&(e.abortSignal.onabort=null),i.status>=200&&i.status<300?t(new g(i.status,i.statusText,i.response||i.responseText)):n(new o(i.response||i.responseText||i.statusText,i.status))},i.onerror=()=>{this._logger.log(d.Warning,`Error from HTTP request. ${i.status}: ${i.statusText}.`),n(new o(i.statusText,i.status))},i.ontimeout=()=>{this._logger.log(d.Warning,"Timeout from HTTP request."),n(new s)},i.send(e.content)})):Promise.reject(new Error("No url defined.")):Promise.reject(new Error("No method defined."))}}class q extends u{constructor(e){if(super(),"undefined"!=typeof fetch||m.isNode)this._httpClient=new R(e);else{if("undefined"==typeof XMLHttpRequest)throw new Error("No usable HttpClient found.");this._httpClient=new D(e)}}send(e){return e.abortSignal&&e.abortSignal.aborted?Promise.reject(new r):e.method?e.url?this._httpClient.send(e):Promise.reject(new Error("No url defined.")):Promise.reject(new Error("No method defined."))}getCookieString(e){return this._httpClient.getCookieString(e)}}class M{static write(e){return`${e}${M.RecordSeparator}`}static parse(e){if(e[e.length-1]!==M.RecordSeparator)throw new Error("Message is incomplete.");const t=e.split(M.RecordSeparator);return t.pop(),t}}M.RecordSeparatorCode=30,M.RecordSeparator=String.fromCharCode(M.RecordSeparatorCode);class x{writeHandshakeRequest(e){return M.write(JSON.stringify(e))}parseHandshakeResponse(e){let t,n;if(v(e)){const o=new Uint8Array(e),s=o.indexOf(M.RecordSeparatorCode);if(-1===s)throw new Error("Message is incomplete.");const r=s+1;t=String.fromCharCode.apply(null,Array.prototype.slice.call(o.slice(0,r))),n=o.byteLength>r?o.slice(r).buffer:null}else{const o=e,s=o.indexOf(M.RecordSeparator);if(-1===s)throw new Error("Message is incomplete.");const r=s+1;t=o.substring(0,r),n=o.length>r?o.substring(r):null}const o=M.parse(t),s=JSON.parse(o[0]);if(s.type)throw new Error("Expected a handshake response from the server.");return[n,s]}}var N,H;(H=N||(N={}))[H.Invocation=1]="Invocation",H[H.StreamItem=2]="StreamItem",H[H.Completion=3]="Completion",H[H.StreamInvocation=4]="StreamInvocation",H[H.CancelInvocation=5]="CancelInvocation",H[H.Ping=6]="Ping",H[H.Close=7]="Close",H[H.Ack=8]="Ack",H[H.Sequence=9]="Sequence";class A{constructor(){this.observers=[]}next(e){for(const t of this.observers)t.next(e)}error(e){for(const t of this.observers)t.error&&t.error(e)}complete(){for(const e of this.observers)e.complete&&e.complete()}subscribe(e){return this.observers.push(e),new y(this,e)}}class W{constructor(e,t,n){this._bufferSize=1e5,this._messages=[],this._totalMessageCount=0,this._waitForSequenceMessage=!1,this._nextReceivingSequenceId=1,this._latestReceivedSequenceId=0,this._bufferedByteCount=0,this._reconnectInProgress=!1,this._protocol=e,this._connection=t,this._bufferSize=n}async _send(e){const t=this._protocol.writeMessage(e);let n=Promise.resolve();if(this._isInvocationMessage(e)){this._totalMessageCount++;let e=()=>{},o=()=>{};v(t)?this._bufferedByteCount+=t.byteLength:this._bufferedByteCount+=t.length,this._bufferedByteCount>=this._bufferSize&&(n=new Promise(((t,n)=>{e=t,o=n}))),this._messages.push(new L(t,this._totalMessageCount,e,o))}try{this._reconnectInProgress||await this._connection.send(t)}catch{this._disconnected()}await n}_ack(e){let t=-1;for(let n=0;n<this._messages.length;n++){const o=this._messages[n];if(o._id<=e.sequenceId)t=n,v(o._message)?this._bufferedByteCount-=o._message.byteLength:this._bufferedByteCount-=o._message.length,o._resolver();else{if(!(this._bufferedByteCount<this._bufferSize))break;o._resolver()}}-1!==t&&(this._messages=this._messages.slice(t+1))}_shouldProcessMessage(e){if(this._waitForSequenceMessage)return e.type===N.Sequence&&(this._waitForSequenceMessage=!1,!0);if(!this._isInvocationMessage(e))return!0;const t=this._nextReceivingSequenceId;return this._nextReceivingSequenceId++,t<=this._latestReceivedSequenceId?(t===this._latestReceivedSequenceId&&this._ackTimer(),!1):(this._latestReceivedSequenceId=t,this._ackTimer(),!0)}_resetSequence(e){e.sequenceId>this._nextReceivingSequenceId?this._connection.stop(new Error("Sequence ID greater than amount of messages we've received.")):this._nextReceivingSequenceId=e.sequenceId}_disconnected(){this._reconnectInProgress=!0,this._waitForSequenceMessage=!0}async _resend(){const e=0!==this._messages.length?this._messages[0]._id:this._totalMessageCount+1;await this._connection.send(this._protocol.writeMessage({type:N.Sequence,sequenceId:e}));const t=this._messages;for(const n of t)await this._connection.send(n._message);this._reconnectInProgress=!1}_dispose(e){null!=e||(e=new Error("Unable to reconnect to server."));for(const t of this._messages)t._rejector(e)}_isInvocationMessage(e){switch(e.type){case N.Invocation:case N.StreamItem:case N.Completion:case N.StreamInvocation:case N.CancelInvocation:return!0;case N.Close:case N.Sequence:case N.Ping:case N.Ack:return!1}}_ackTimer(){void 0===this._ackTimerHandle&&(this._ackTimerHandle=setTimeout((async()=>{try{this._reconnectInProgress||await this._connection.send(this._protocol.writeMessage({type:N.Ack,sequenceId:this._latestReceivedSequenceId}))}catch{}clearTimeout(this._ackTimerHandle),this._ackTimerHandle=void 0}),1e3))}}class L{constructor(e,t,n,o){this._message=e,this._id=t,this._resolver=n,this._rejector=o}}var j,B;(B=j||(j={})).Disconnected="Disconnected",B.Connecting="Connecting",B.Connected="Connected",B.Disconnecting="Disconnecting",B.Reconnecting="Reconnecting";class U{static create(e,t,n,o,s,r,i){return new U(e,t,n,o,s,r,i)}constructor(e,t,n,o,s,r,i){this._nextKeepAlive=0,this._freezeEventListener=()=>{this._logger.log(d.Warning,"The page is being frozen, this will likely lead to the connection being closed and messages being lost. For more information see the docs at https://learn.microsoft.com/aspnet/core/signalr/javascript-client#bsleep")},f.isRequired(e,"connection"),f.isRequired(t,"logger"),f.isRequired(n,"protocol"),this.serverTimeoutInMilliseconds=null!=s?s:3e4,this.keepAliveIntervalInMilliseconds=null!=r?r:15e3,this._statefulReconnectBufferSize=null!=i?i:1e5,this._logger=t,this._protocol=n,this.connection=e,this._reconnectPolicy=o,this._handshakeProtocol=new x,this.connection.onreceive=e=>this._processIncomingData(e),this.connection.onclose=e=>this._connectionClosed(e),this._callbacks={},this._methods={},this._closedCallbacks=[],this._reconnectingCallbacks=[],this._reconnectedCallbacks=[],this._invocationId=0,this._receivedHandshakeResponse=!1,this._connectionState=j.Disconnected,this._connectionStarted=!1,this._cachedPingMessage=this._protocol.writeMessage({type:N.Ping})}get state(){return this._connectionState}get connectionId(){return this.connection&&this.connection.connectionId||null}get baseUrl(){return this.connection.baseUrl||""}set baseUrl(e){if(this._connectionState!==j.Disconnected&&this._connectionState!==j.Reconnecting)throw new Error("The HubConnection must be in the Disconnected or Reconnecting state to change the url.");if(!e)throw new Error("The HubConnection url must be a valid url.");this.connection.baseUrl=e}start(){return this._startPromise=this._startWithStateTransitions(),this._startPromise}async _startWithStateTransitions(){if(this._connectionState!==j.Disconnected)return Promise.reject(new Error("Cannot start a HubConnection that is not in the 'Disconnected' state."));this._connectionState=j.Connecting,this._logger.log(d.Debug,"Starting HubConnection.");try{await this._startInternal(),m.isBrowser&&window.document.addEventListener("freeze",this._freezeEventListener),this._connectionState=j.Connected,this._connectionStarted=!0,this._logger.log(d.Debug,"HubConnection connected successfully.")}catch(e){return this._connectionState=j.Disconnected,this._logger.log(d.Debug,`HubConnection failed to start successfully because of error '${e}'.`),Promise.reject(e)}}async _startInternal(){this._stopDuringStartError=void 0,this._receivedHandshakeResponse=!1;const e=new Promise(((e,t)=>{this._handshakeResolver=e,this._handshakeRejecter=t}));await this.connection.start(this._protocol.transferFormat);try{let t=this._protocol.version;this.connection.features.reconnect||(t=1);const n={protocol:this._protocol.name,version:t};if(this._logger.log(d.Debug,"Sending handshake request."),await this._sendMessage(this._handshakeProtocol.writeHandshakeRequest(n)),this._logger.log(d.Information,`Using HubProtocol '${this._protocol.name}'.`),this._cleanupTimeout(),this._resetTimeoutPeriod(),this._resetKeepAliveInterval(),await e,this._stopDuringStartError)throw this._stopDuringStartError;(this.connection.features.reconnect||!1)&&(this._messageBuffer=new W(this._protocol,this.connection,this._statefulReconnectBufferSize),this.connection.features.disconnected=this._messageBuffer._disconnected.bind(this._messageBuffer),this.connection.features.resend=()=>{if(this._messageBuffer)return this._messageBuffer._resend()}),this.connection.features.inherentKeepAlive||await this._sendMessage(this._cachedPingMessage)}catch(t){throw this._logger.log(d.Debug,`Hub handshake failed with error '${t}' during start(). Stopping HubConnection.`),this._cleanupTimeout(),this._cleanupPingTimer(),await this.connection.stop(t),t}}async stop(){const e=this._startPromise;this.connection.features.reconnect=!1,this._stopPromise=this._stopInternal(),await this._stopPromise;try{await e}catch(t){}}_stopInternal(e){if(this._connectionState===j.Disconnected)return this._logger.log(d.Debug,`Call to HubConnection.stop(${e}) ignored because it is already in the disconnected state.`),Promise.resolve();if(this._connectionState===j.Disconnecting)return this._logger.log(d.Debug,`Call to HttpConnection.stop(${e}) ignored because the connection is already in the disconnecting state.`),this._stopPromise;const t=this._connectionState;return this._connectionState=j.Disconnecting,this._logger.log(d.Debug,"Stopping HubConnection."),this._reconnectDelayHandle?(this._logger.log(d.Debug,"Connection stopped during reconnect delay. Done reconnecting."),clearTimeout(this._reconnectDelayHandle),this._reconnectDelayHandle=void 0,this._completeClose(),Promise.resolve()):(t===j.Connected&&this._sendCloseMessage(),this._cleanupTimeout(),this._cleanupPingTimer(),this._stopDuringStartError=e||new r("The connection was stopped before the hub handshake could complete."),this.connection.stop(e))}async _sendCloseMessage(){try{await this._sendWithProtocol(this._createCloseMessage())}catch{}}stream(e,...t){const[n,o]=this._replaceStreamingParams(t),s=this._createStreamInvocation(e,t,o);let r;const i=new A;return i.cancelCallback=()=>{const e=this._createCancelInvocation(s.invocationId);return delete this._callbacks[s.invocationId],r.then((()=>this._sendWithProtocol(e)))},this._callbacks[s.invocationId]=(e,t)=>{t?i.error(t):e&&(e.type===N.Completion?e.error?i.error(new Error(e.error)):i.complete():i.next(e.item))},r=this._sendWithProtocol(s).catch((e=>{i.error(e),delete this._callbacks[s.invocationId]})),this._launchStreams(n,r),i}_sendMessage(e){return this._resetKeepAliveInterval(),this.connection.send(e)}_sendWithProtocol(e){return this._messageBuffer?this._messageBuffer._send(e):this._sendMessage(this._protocol.writeMessage(e))}send(e,...t){const[n,o]=this._replaceStreamingParams(t),s=this._sendWithProtocol(this._createInvocation(e,t,!0,o));return this._launchStreams(n,s),s}invoke(e,...t){const[n,o]=this._replaceStreamingParams(t),s=this._createInvocation(e,t,!1,o);return new Promise(((e,t)=>{this._callbacks[s.invocationId]=(n,o)=>{o?t(o):n&&(n.type===N.Completion?n.error?t(new Error(n.error)):e(n.result):t(new Error(`Unexpected message type: ${n.type}`)))};const o=this._sendWithProtocol(s).catch((e=>{t(e),delete this._callbacks[s.invocationId]}));this._launchStreams(n,o)}))}on(e,t){e&&t&&(e=e.toLowerCase(),this._methods[e]||(this._methods[e]=[]),-1===this._methods[e].indexOf(t)&&this._methods[e].push(t))}off(e,t){if(!e)return;e=e.toLowerCase();const n=this._methods[e];if(n)if(t){const o=n.indexOf(t);-1!==o&&(n.splice(o,1),0===n.length&&delete this._methods[e])}else delete this._methods[e]}onclose(e){e&&this._closedCallbacks.push(e)}onreconnecting(e){e&&this._reconnectingCallbacks.push(e)}onreconnected(e){e&&this._reconnectedCallbacks.push(e)}_processIncomingData(e){if(this._cleanupTimeout(),this._receivedHandshakeResponse||(e=this._processHandshakeResponse(e),this._receivedHandshakeResponse=!0),e){const n=this._protocol.parseMessages(e,this._logger);for(const e of n)if(!this._messageBuffer||this._messageBuffer._shouldProcessMessage(e))switch(e.type){case N.Invocation:this._invokeClientMethod(e).catch((e=>{this._logger.log(d.Error,`Invoke client method threw error: ${P(e)}`)}));break;case N.StreamItem:case N.Completion:{const n=this._callbacks[e.invocationId];if(n){e.type===N.Completion&&delete this._callbacks[e.invocationId];try{n(e)}catch(t){this._logger.log(d.Error,`Stream callback threw error: ${P(t)}`)}}break}case N.Ping:break;case N.Close:{this._logger.log(d.Information,"Close message received from server.");const t=e.error?new Error("Server returned an error on close: "+e.error):void 0;!0===e.allowReconnect?this.connection.stop(t):this._stopPromise=this._stopInternal(t);break}case N.Ack:this._messageBuffer&&this._messageBuffer._ack(e);break;case N.Sequence:this._messageBuffer&&this._messageBuffer._resetSequence(e);break;default:this._logger.log(d.Warning,`Invalid message type: ${e.type}.`)}}this._resetTimeoutPeriod()}_processHandshakeResponse(e){let t,n;try{[n,t]=this._handshakeProtocol.parseHandshakeResponse(e)}catch(o){const e="Error parsing handshake response: "+o;this._logger.log(d.Error,e);const t=new Error(e);throw this._handshakeRejecter(t),t}if(t.error){const e="Server returned handshake error: "+t.error;this._logger.log(d.Error,e);const n=new Error(e);throw this._handshakeRejecter(n),n}return this._logger.log(d.Debug,"Server handshake complete."),this._handshakeResolver(),n}_resetKeepAliveInterval(){this.connection.features.inherentKeepAlive||(this._nextKeepAlive=(new Date).getTime()+this.keepAliveIntervalInMilliseconds,this._cleanupPingTimer())}_resetTimeoutPeriod(){if(!(this.connection.features&&this.connection.features.inherentKeepAlive||(this._timeoutHandle=setTimeout((()=>this.serverTimeout()),this.serverTimeoutInMilliseconds),void 0!==this._pingServerHandle))){let e=this._nextKeepAlive-(new Date).getTime();e<0&&(e=0),this._pingServerHandle=setTimeout((async()=>{if(this._connectionState===j.Connected)try{await this._sendMessage(this._cachedPingMessage)}catch{this._cleanupPingTimer()}}),e)}}serverTimeout(){this.connection.stop(new Error("Server timeout elapsed without receiving a message from the server."))}async _invokeClientMethod(e){const t=e.target.toLowerCase(),n=this._methods[t];if(!n)return this._logger.log(d.Warning,`No client method with the name '${t}' found.`),void(e.invocationId&&(this._logger.log(d.Warning,`No result given for '${t}' method and invocation ID '${e.invocationId}'.`),await this._sendWithProtocol(this._createCompletionMessage(e.invocationId,"Client didn't provide a result.",null))));const o=n.slice(),s=!!e.invocationId;let r,i,c;for(const h of o)try{const n=r;r=await h.apply(this,e.arguments),s&&r&&n&&(this._logger.log(d.Error,`Multiple results provided for '${t}'. Sending error to server.`),c=this._createCompletionMessage(e.invocationId,"Client provided multiple results.",null)),i=void 0}catch(a){i=a,this._logger.log(d.Error,`A callback for the method '${t}' threw error '${a}'.`)}c?await this._sendWithProtocol(c):s?(i?c=this._createCompletionMessage(e.invocationId,`${i}`,null):void 0!==r?c=this._createCompletionMessage(e.invocationId,null,r):(this._logger.log(d.Warning,`No result given for '${t}' method and invocation ID '${e.invocationId}'.`),c=this._createCompletionMessage(e.invocationId,"Client didn't provide a result.",null)),await this._sendWithProtocol(c)):r&&this._logger.log(d.Error,`Result given for '${t}' method but server is not expecting a result.`)}_connectionClosed(e){this._logger.log(d.Debug,`HubConnection.connectionClosed(${e}) called while in state ${this._connectionState}.`),this._stopDuringStartError=this._stopDuringStartError||e||new r("The underlying connection was closed before the hub handshake could complete."),this._handshakeResolver&&this._handshakeResolver(),this._cancelCallbacksWithError(e||new Error("Invocation canceled due to the underlying connection being closed.")),this._cleanupTimeout(),this._cleanupPingTimer(),this._connectionState===j.Disconnecting?this._completeClose(e):this._connectionState===j.Connected&&this._reconnectPolicy?this._reconnect(e):this._connectionState===j.Connected&&this._completeClose(e)}_completeClose(e){if(this._connectionStarted){this._connectionState=j.Disconnected,this._connectionStarted=!1,this._messageBuffer&&(this._messageBuffer._dispose(null!=e?e:new Error("Connection closed.")),this._messageBuffer=void 0),m.isBrowser&&window.document.removeEventListener("freeze",this._freezeEventListener);try{this._closedCallbacks.forEach((t=>t.apply(this,[e])))}catch(t){this._logger.log(d.Error,`An onclose callback called with error '${e}' threw error '${t}'.`)}}}async _reconnect(e){const t=Date.now();let n=0,o=void 0!==e?e:new Error("Attempting to reconnect due to a unknown error."),s=this._getNextRetryDelay(n++,0,o);if(null===s)return this._logger.log(d.Debug,"Connection not reconnecting because the IRetryPolicy returned null on the first reconnect attempt."),void this._completeClose(e);if(this._connectionState=j.Reconnecting,e?this._logger.log(d.Information,`Connection reconnecting because of error '${e}'.`):this._logger.log(d.Information,"Connection reconnecting."),0!==this._reconnectingCallbacks.length){try{this._reconnectingCallbacks.forEach((t=>t.apply(this,[e])))}catch(r){this._logger.log(d.Error,`An onreconnecting callback called with error '${e}' threw error '${r}'.`)}if(this._connectionState!==j.Reconnecting)return void this._logger.log(d.Debug,"Connection left the reconnecting state in onreconnecting callback. Done reconnecting.")}for(;null!==s;){if(this._logger.log(d.Information,`Reconnect attempt number ${n} will start in ${s} ms.`),await new Promise((e=>{this._reconnectDelayHandle=setTimeout(e,s)})),this._reconnectDelayHandle=void 0,this._connectionState!==j.Reconnecting)return void this._logger.log(d.Debug,"Connection left the reconnecting state during reconnect delay. Done reconnecting.");try{if(await this._startInternal(),this._connectionState=j.Connected,this._logger.log(d.Information,"HubConnection reconnected successfully."),0!==this._reconnectedCallbacks.length)try{this._reconnectedCallbacks.forEach((e=>e.apply(this,[this.connection.connectionId])))}catch(r){this._logger.log(d.Error,`An onreconnected callback called with connectionId '${this.connection.connectionId}; threw error '${r}'.`)}return}catch(r){if(this._logger.log(d.Information,`Reconnect attempt failed because of error '${r}'.`),this._connectionState!==j.Reconnecting)return this._logger.log(d.Debug,`Connection moved to the '${this._connectionState}' from the reconnecting state during reconnect attempt. Done reconnecting.`),void(this._connectionState===j.Disconnecting&&this._completeClose());o=r instanceof Error?r:new Error(r.toString()),s=this._getNextRetryDelay(n++,Date.now()-t,o)}}this._logger.log(d.Information,`Reconnect retries have been exhausted after ${Date.now()-t} ms and ${n} failed attempts. Connection disconnecting.`),this._completeClose()}_getNextRetryDelay(e,t,n){try{return this._reconnectPolicy.nextRetryDelayInMilliseconds({elapsedMilliseconds:t,previousRetryCount:e,retryReason:n})}catch(o){return this._logger.log(d.Error,`IRetryPolicy.nextRetryDelayInMilliseconds(${e}, ${t}) threw error '${o}'.`),null}}_cancelCallbacksWithError(e){const t=this._callbacks;this._callbacks={},Object.keys(t).forEach((n=>{const o=t[n];try{o(null,e)}catch(s){this._logger.log(d.Error,`Stream 'error' callback called with '${e}' threw error: ${P(s)}`)}}))}_cleanupPingTimer(){this._pingServerHandle&&(clearTimeout(this._pingServerHandle),this._pingServerHandle=void 0)}_cleanupTimeout(){this._timeoutHandle&&clearTimeout(this._timeoutHandle)}_createInvocation(e,t,n,o){if(n)return 0!==o.length?{arguments:t,streamIds:o,target:e,type:N.Invocation}:{arguments:t,target:e,type:N.Invocation};{const n=this._invocationId;return this._invocationId++,0!==o.length?{arguments:t,invocationId:n.toString(),streamIds:o,target:e,type:N.Invocation}:{arguments:t,invocationId:n.toString(),target:e,type:N.Invocation}}}_launchStreams(e,t){if(0!==e.length){t||(t=Promise.resolve());for(const n in e)e[n].subscribe({complete:()=>{t=t.then((()=>this._sendWithProtocol(this._createCompletionMessage(n))))},error:e=>{let o;o=e instanceof Error?e.message:e&&e.toString?e.toString():"Unknown error",t=t.then((()=>this._sendWithProtocol(this._createCompletionMessage(n,o))))},next:e=>{t=t.then((()=>this._sendWithProtocol(this._createStreamItemMessage(n,e))))}})}}_replaceStreamingParams(e){const t=[],n=[];for(let o=0;o<e.length;o++){const s=e[o];if(this._isObservable(s)){const r=this._invocationId;this._invocationId++,t[r]=s,n.push(r.toString()),e.splice(o,1)}}return[t,n]}_isObservable(e){return e&&e.subscribe&&"function"==typeof e.subscribe}_createStreamInvocation(e,t,n){const o=this._invocationId;return this._invocationId++,0!==n.length?{arguments:t,invocationId:o.toString(),streamIds:n,target:e,type:N.StreamInvocation}:{arguments:t,invocationId:o.toString(),target:e,type:N.StreamInvocation}}_createCancelInvocation(e){return{invocationId:e,type:N.CancelInvocation}}_createStreamItemMessage(e,t){return{invocationId:e,item:t,type:N.StreamItem}}_createCompletionMessage(e,t,n){return t?{error:t,invocationId:e,type:N.Completion}:{invocationId:e,result:n,type:N.Completion}}_createCloseMessage(){return{type:N.Close}}}const F=[0,2e3,1e4,3e4,null];class O{constructor(e){this._retryDelays=void 0!==e?[...e,null]:F}nextRetryDelayInMilliseconds(e){return this._retryDelays[e.previousRetryCount]}}class z{}z.Authorization="Authorization",z.Cookie="Cookie";class K extends u{constructor(e,t){super(),this._innerClient=e,this._accessTokenFactory=t}async send(e){let t=!0;this._accessTokenFactory&&(!this._accessToken||e.url&&e.url.indexOf("/negotiate?")>0)&&(t=!1,this._accessToken=await this._accessTokenFactory()),this._setAuthorizationHeader(e);const n=await this._innerClient.send(e);return t&&401===n.statusCode&&this._accessTokenFactory?(this._accessToken=await this._accessTokenFactory(),this._setAuthorizationHeader(e),await this._innerClient.send(e)):n}_setAuthorizationHeader(e){e.headers||(e.headers={}),this._accessToken?e.headers[z.Authorization]=`Bearer ${this._accessToken}`:this._accessTokenFactory&&e.headers[z.Authorization]&&delete e.headers[z.Authorization]}getCookieString(e){return this._innerClient.getCookieString(e)}}var X,J,V,Q;(J=X||(X={}))[J.None=0]="None",J[J.WebSockets=1]="WebSockets",J[J.ServerSentEvents=2]="ServerSentEvents",J[J.LongPolling=4]="LongPolling",(Q=V||(V={}))[Q.Text=1]="Text",Q[Q.Binary=2]="Binary";let G=class{constructor(){this._isAborted=!1,this.onabort=null}abort(){this._isAborted||(this._isAborted=!0,this.onabort&&this.onabort())}get signal(){return this}get aborted(){return this._isAborted}};class Y{get pollAborted(){return this._pollAbort.aborted}constructor(e,t,n){this._httpClient=e,this._logger=t,this._pollAbort=new G,this._options=n,this._running=!1,this.onreceive=null,this.onclose=null}async connect(e,t){if(f.isRequired(e,"url"),f.isRequired(t,"transferFormat"),f.isIn(t,V,"transferFormat"),this._url=e,this._logger.log(d.Trace,"(LongPolling transport) Connecting."),t===V.Binary&&"undefined"!=typeof XMLHttpRequest&&"string"!=typeof(new XMLHttpRequest).responseType)throw new Error("Binary protocols over XmlHttpRequest not implementing advanced features are not supported.");const[n,s]=C(),r={[n]:s,...this._options.headers},i={abortSignal:this._pollAbort.signal,headers:r,timeout:1e5,withCredentials:this._options.withCredentials};t===V.Binary&&(i.responseType="arraybuffer");const c=`${e}&_=${Date.now()}`;this._logger.log(d.Trace,`(LongPolling transport) polling: ${c}.`);const a=await this._httpClient.get(c,i);200!==a.statusCode?(this._logger.log(d.Error,`(LongPolling transport) Unexpected response code: ${a.statusCode}.`),this._closeError=new o(a.statusText||"",a.statusCode),this._running=!1):this._running=!0,this._receiving=this._poll(this._url,i)}async _poll(e,t){try{for(;this._running;)try{const n=`${e}&_=${Date.now()}`;this._logger.log(d.Trace,`(LongPolling transport) polling: ${n}.`);const s=await this._httpClient.get(n,t);204===s.statusCode?(this._logger.log(d.Information,"(LongPolling transport) Poll terminated by server."),this._running=!1):200!==s.statusCode?(this._logger.log(d.Error,`(LongPolling transport) Unexpected response code: ${s.statusCode}.`),this._closeError=new o(s.statusText||"",s.statusCode),this._running=!1):s.content?(this._logger.log(d.Trace,`(LongPolling transport) data received. ${w(s.content,this._options.logMessageContent)}.`),this.onreceive&&this.onreceive(s.content)):this._logger.log(d.Trace,"(LongPolling transport) Poll timed out, reissuing.")}catch(n){this._running?n instanceof s?this._logger.log(d.Trace,"(LongPolling transport) Poll timed out, reissuing."):(this._closeError=n,this._running=!1):this._logger.log(d.Trace,`(LongPolling transport) Poll errored after shutdown: ${n.message}`)}}finally{this._logger.log(d.Trace,"(LongPolling transport) Polling complete."),this.pollAborted||this._raiseOnClose()}}async send(e){return this._running?b(this._logger,"LongPolling",this._httpClient,this._url,e,this._options):Promise.reject(new Error("Cannot send until the transport is connected"))}async stop(){this._logger.log(d.Trace,"(LongPolling transport) Stopping polling."),this._running=!1,this._pollAbort.abort();try{await this._receiving,this._logger.log(d.Trace,`(LongPolling transport) sending DELETE request to ${this._url}.`);const t={},[n,s]=C();t[n]=s;const r={headers:{...t,...this._options.headers},timeout:this._options.timeout,withCredentials:this._options.withCredentials};let i;try{await this._httpClient.delete(this._url,r)}catch(e){i=e}i?i instanceof o&&(404===i.statusCode?this._logger.log(d.Trace,"(LongPolling transport) A 404 response was returned from sending a DELETE request."):this._logger.log(d.Trace,`(LongPolling transport) Error sending a DELETE request: ${i}`)):this._logger.log(d.Trace,"(LongPolling transport) DELETE request accepted.")}finally{this._logger.log(d.Trace,"(LongPolling transport) Stop finished."),this._raiseOnClose()}}_raiseOnClose(){if(this.onclose){let e="(LongPolling transport) Firing onclose event.";this._closeError&&(e+=" Error: "+this._closeError),this._logger.log(d.Trace,e),this.onclose(this._closeError)}}}class Z{constructor(e,t,n,o){this._httpClient=e,this._accessToken=t,this._logger=n,this._options=o,this.onreceive=null,this.onclose=null}async connect(e,t){return f.isRequired(e,"url"),f.isRequired(t,"transferFormat"),f.isIn(t,V,"transferFormat"),this._logger.log(d.Trace,"(SSE transport) Connecting."),this._url=e,this._accessToken&&(e+=(e.indexOf("?")<0?"?":"&")+`access_token=${encodeURIComponent(this._accessToken)}`),new Promise(((n,o)=>{let s,r=!1;if(t===V.Text){if(m.isBrowser||m.isWebWorker)s=new this._options.EventSource(e,{withCredentials:this._options.withCredentials});else{const t=this._httpClient.getCookieString(e),n={};n.Cookie=t;const[o,r]=C();n[o]=r,s=new this._options.EventSource(e,{withCredentials:this._options.withCredentials,headers:{...n,...this._options.headers}})}try{s.onmessage=e=>{if(this.onreceive)try{this._logger.log(d.Trace,`(SSE transport) data received. ${w(e.data,this._options.logMessageContent)}.`),this.onreceive(e.data)}catch(t){return void this._close(t)}},s.onerror=e=>{r?this._close():o(new Error("EventSource failed to connect. The connection could not be found on the server, either the connection ID is not present on the server, or a proxy is refusing/buffering the connection. If you have multiple servers check that sticky sessions are enabled."))},s.onopen=()=>{this._logger.log(d.Information,`SSE connected to ${this._url}`),this._eventSource=s,r=!0,n()}}catch(i){return void o(i)}}else o(new Error("The Server-Sent Events transport only supports the 'Text' transfer format"))}))}async send(e){return this._eventSource?b(this._logger,"SSE",this._httpClient,this._url,e,this._options):Promise.reject(new Error("Cannot send until the transport is connected"))}stop(){return this._close(),Promise.resolve()}_close(e){this._eventSource&&(this._eventSource.close(),this._eventSource=void 0,this.onclose&&this.onclose(e))}}class ee{constructor(e,t,n,o,s,r){this._logger=n,this._accessTokenFactory=t,this._logMessageContent=o,this._webSocketConstructor=s,this._httpClient=e,this.onreceive=null,this.onclose=null,this._headers=r}async connect(e,t){let n;return f.isRequired(e,"url"),f.isRequired(t,"transferFormat"),f.isIn(t,V,"transferFormat"),this._logger.log(d.Trace,"(WebSockets transport) Connecting."),this._accessTokenFactory&&(n=await this._accessTokenFactory()),new Promise(((o,s)=>{let r;e=e.replace(/^http/,"ws");const i=this._httpClient.getCookieString(e);let c=!1;if(m.isNode||m.isReactNative){const t={},[o,s]=C();t[o]=s,n&&(t[z.Authorization]=`Bearer ${n}`),i&&(t[z.Cookie]=i),r=new this._webSocketConstructor(e,void 0,{headers:{...t,...this._headers}})}else n&&(e+=(e.indexOf("?")<0?"?":"&")+`access_token=${encodeURIComponent(n)}`);r||(r=new this._webSocketConstructor(e)),t===V.Binary&&(r.binaryType="arraybuffer"),r.onopen=t=>{this._logger.log(d.Information,`WebSocket connected to ${e}.`),this._webSocket=r,c=!0,o()},r.onerror=e=>{let t=null;t="undefined"!=typeof ErrorEvent&&e instanceof ErrorEvent?e.error:"There was an error with the transport",this._logger.log(d.Information,`(WebSockets transport) ${t}.`)},r.onmessage=e=>{if(this._logger.log(d.Trace,`(WebSockets transport) data received. ${w(e.data,this._logMessageContent)}.`),this.onreceive)try{this.onreceive(e.data)}catch(t){return void this._close(t)}},r.onclose=e=>{if(c)this._close(e);else{let t=null;t="undefined"!=typeof ErrorEvent&&e instanceof ErrorEvent?e.error:"WebSocket failed to connect. The connection could not be found on the server, either the endpoint may not be a SignalR endpoint, the connection ID is not present on the server, or there is a proxy blocking WebSockets. If you have multiple servers check that sticky sessions are enabled.",s(new Error(t))}}}))}send(e){return this._webSocket&&this._webSocket.readyState===this._webSocketConstructor.OPEN?(this._logger.log(d.Trace,`(WebSockets transport) sending data. ${w(e,this._logMessageContent)}.`),this._webSocket.send(e),Promise.resolve()):Promise.reject("WebSocket is not in the OPEN state")}stop(){return this._webSocket&&this._close(void 0),Promise.resolve()}_close(e){this._webSocket&&(this._webSocket.onclose=()=>{},this._webSocket.onmessage=()=>{},this._webSocket.onerror=()=>{},this._webSocket.close(),this._webSocket=void 0),this._logger.log(d.Trace,"(WebSockets transport) socket closed."),this.onclose&&(!this._isCloseEvent(e)||!1!==e.wasClean&&1e3===e.code?e instanceof Error?this.onclose(e):this.onclose():this.onclose(new Error(`WebSocket closed with status code: ${e.code} (${e.reason||"no reason given"}).`)))}_isCloseEvent(e){return e&&"boolean"==typeof e.wasClean&&"number"==typeof e.code}}class te{constructor(e,t={}){var n;if(this._stopPromiseResolver=()=>{},this.features={},this._negotiateVersion=1,f.isRequired(e,"url"),this._logger=void 0===(n=t.logger)?new S(d.Information):null===n?p.instance:void 0!==n.log?n:new S(n),this.baseUrl=this._resolveUrl(e),(t=t||{}).logMessageContent=void 0!==t.logMessageContent&&t.logMessageContent,"boolean"!=typeof t.withCredentials&&void 0!==t.withCredentials)throw new Error("withCredentials option was not a 'boolean' or 'undefined' value");t.withCredentials=void 0===t.withCredentials||t.withCredentials,t.timeout=void 0===t.timeout?1e5:t.timeout;let o=null,s=null;if(m.isNode&&"undefined"!=typeof require){const e="function"==typeof __webpack_require__?__non_webpack_require__:require;o=e("ws"),s=e("eventsource")}m.isNode||"undefined"==typeof WebSocket||t.WebSocket?m.isNode&&!t.WebSocket&&o&&(t.WebSocket=o):t.WebSocket=WebSocket,m.isNode||"undefined"==typeof EventSource||t.EventSource?m.isNode&&!t.EventSource&&void 0!==s&&(t.EventSource=s):t.EventSource=EventSource,this._httpClient=new K(t.httpClient||new q(this._logger),t.accessTokenFactory),this._connectionState="Disconnected",this._connectionStarted=!1,this._options=t,this.onreceive=null,this.onclose=null}async start(e){if(e=e||V.Binary,f.isIn(e,V,"transferFormat"),this._logger.log(d.Debug,`Starting connection with transfer format '${V[e]}'.`),"Disconnected"!==this._connectionState)return Promise.reject(new Error("Cannot start an HttpConnection that is not in the 'Disconnected' state."));if(this._connectionState="Connecting",this._startInternalPromise=this._startInternal(e),await this._startInternalPromise,"Disconnecting"===this._connectionState){const e="Failed to start the HttpConnection before stop() was called.";return this._logger.log(d.Error,e),await this._stopPromise,Promise.reject(new r(e))}if("Connected"!==this._connectionState){const e="HttpConnection.startInternal completed gracefully but didn't enter the connection into the connected state!";return this._logger.log(d.Error,e),Promise.reject(new r(e))}this._connectionStarted=!0}send(e){return"Connected"!==this._connectionState?Promise.reject(new Error("Cannot send data if the connection is not in the 'Connected' State.")):(this._sendQueue||(this._sendQueue=new ne(this.transport)),this._sendQueue.send(e))}async stop(e){return"Disconnected"===this._connectionState?(this._logger.log(d.Debug,`Call to HttpConnection.stop(${e}) ignored because the connection is already in the disconnected state.`),Promise.resolve()):"Disconnecting"===this._connectionState?(this._logger.log(d.Debug,`Call to HttpConnection.stop(${e}) ignored because the connection is already in the disconnecting state.`),this._stopPromise):(this._connectionState="Disconnecting",this._stopPromise=new Promise((e=>{this._stopPromiseResolver=e})),await this._stopInternal(e),void(await this._stopPromise))}async _stopInternal(e){this._stopError=e;try{await this._startInternalPromise}catch(t){}if(this.transport){try{await this.transport.stop()}catch(t){this._logger.log(d.Error,`HttpConnection.transport.stop() threw error '${t}'.`),this._stopConnection()}this.transport=void 0}else this._logger.log(d.Debug,"HttpConnection.transport is undefined in HttpConnection.stop() because start() failed.")}async _startInternal(e){let t=this.baseUrl;this._accessTokenFactory=this._options.accessTokenFactory,this._httpClient._accessTokenFactory=this._accessTokenFactory;try{if(this._options.skipNegotiation){if(this._options.transport!==X.WebSockets)throw new Error("Negotiation can only be skipped when using the WebSocket transport directly.");this.transport=this._constructTransport(X.WebSockets),await this._startTransport(t,e)}else{let n=null,o=0;do{if(n=await this._getNegotiationResponse(t),"Disconnecting"===this._connectionState||"Disconnected"===this._connectionState)throw new r("The connection was stopped during negotiation.");if(n.error)throw new Error(n.error);if(n.ProtocolVersion)throw new Error("Detected a connection attempt to an ASP.NET SignalR Server. This client only supports connecting to an ASP.NET Core SignalR Server. See https://aka.ms/signalr-core-differences for details.");if(n.url&&(t=n.url),n.accessToken){const e=n.accessToken;this._accessTokenFactory=()=>e,this._httpClient._accessToken=e,this._httpClient._accessTokenFactory=void 0}o++}while(n.url&&o<100);if(100===o&&n.url)throw new Error("Negotiate redirection limit exceeded.");await this._createTransport(t,this._options.transport,n,e)}this.transport instanceof Y&&(this.features.inherentKeepAlive=!0),"Connecting"===this._connectionState&&(this._logger.log(d.Debug,"The HttpConnection connected successfully."),this._connectionState="Connected")}catch(n){return this._logger.log(d.Error,"Failed to start the connection: "+n),this._connectionState="Disconnected",this.transport=void 0,this._stopPromiseResolver(),Promise.reject(n)}}async _getNegotiationResponse(e){const t={},[n,s]=C();t[n]=s;const r=this._resolveNegotiateUrl(e);this._logger.log(d.Debug,`Sending negotiation request: ${r}.`);try{const e=await this._httpClient.post(r,{content:"",headers:{...t,...this._options.headers},timeout:this._options.timeout,withCredentials:this._options.withCredentials});if(200!==e.statusCode)return Promise.reject(new Error(`Unexpected status code returned from negotiate '${e.statusCode}'`));const n=JSON.parse(e.content);return(!n.negotiateVersion||n.negotiateVersion<1)&&(n.connectionToken=n.connectionId),n.useStatefulReconnect&&!0!==this._options._useStatefulReconnect?Promise.reject(new h("Client didn't negotiate Stateful Reconnect but the server did.")):n}catch(i){let e="Failed to complete negotiation with the server: "+i;return i instanceof o&&404===i.statusCode&&(e+=" Either this is not a SignalR endpoint or there is a proxy blocking the connection."),this._logger.log(d.Error,e),Promise.reject(new h(e))}}_createConnectUrl(e,t){return t?e+(-1===e.indexOf("?")?"?":"&")+`id=${t}`:e}async _createTransport(e,t,n,o){let s=this._createConnectUrl(e,n.connectionToken);if(this._isITransport(t))return this._logger.log(d.Debug,"Connection was provided an instance of ITransport, using that directly."),this.transport=t,await this._startTransport(s,o),void(this.connectionId=n.connectionId);const i=[],c=n.availableTransports||[];let h=n;for(const l of c){const n=this._resolveTransportOrError(l,t,o,!0===(null==h?void 0:h.useStatefulReconnect));if(n instanceof Error)i.push(`${l.transport} failed:`),i.push(n);else if(this._isITransport(n)){if(this.transport=n,!h){try{h=await this._getNegotiationResponse(e)}catch(g){return Promise.reject(g)}s=this._createConnectUrl(e,h.connectionToken)}try{return await this._startTransport(s,o),void(this.connectionId=h.connectionId)}catch(g){if(this._logger.log(d.Error,`Failed to start the transport '${l.transport}': ${g}`),h=void 0,i.push(new a(`${l.transport} failed: ${g}`,X[l.transport])),"Connecting"!==this._connectionState){const e="Failed to select transport before stop() was called.";return this._logger.log(d.Debug,e),Promise.reject(new r(e))}}}}return i.length>0?Promise.reject(new l(`Unable to connect to the server with any of the available transports. ${i.join(" ")}`,i)):Promise.reject(new Error("None of the transports supported by the client are supported by the server."))}_constructTransport(e){switch(e){case X.WebSockets:if(!this._options.WebSocket)throw new Error("'WebSocket' is not supported in your environment.");return new ee(this._httpClient,this._accessTokenFactory,this._logger,this._options.logMessageContent,this._options.WebSocket,this._options.headers||{});case X.ServerSentEvents:if(!this._options.EventSource)throw new Error("'EventSource' is not supported in your environment.");return new Z(this._httpClient,this._httpClient._accessToken,this._logger,this._options);case X.LongPolling:return new Y(this._httpClient,this._logger,this._options);default:throw new Error(`Unknown transport: ${e}.`)}}_startTransport(e,t){return this.transport.onreceive=this.onreceive,this.features.reconnect?this.transport.onclose=async n=>{let o=!1;if(this.features.reconnect){try{this.features.disconnected(),await this.transport.connect(e,t),await this.features.resend()}catch{o=!0}o&&this._stopConnection(n)}else this._stopConnection(n)}:this.transport.onclose=e=>this._stopConnection(e),this.transport.connect(e,t)}_resolveTransportOrError(e,t,n,o){const s=X[e.transport];if(null==s)return this._logger.log(d.Debug,`Skipping transport '${e.transport}' because it is not supported by this client.`),new Error(`Skipping transport '${e.transport}' because it is not supported by this client.`);if(!function(e,t){return!e||!!(t&e)}(t,s))return this._logger.log(d.Debug,`Skipping transport '${X[s]}' because it was disabled by the client.`),new c(`'${X[s]}' is disabled by the client.`,s);if(!(e.transferFormats.map((e=>V[e])).indexOf(n)>=0))return this._logger.log(d.Debug,`Skipping transport '${X[s]}' because it does not support the requested transfer format '${V[n]}'.`),new Error(`'${X[s]}' does not support ${V[n]}.`);if(s===X.WebSockets&&!this._options.WebSocket||s===X.ServerSentEvents&&!this._options.EventSource)return this._logger.log(d.Debug,`Skipping transport '${X[s]}' because it is not supported in your environment.'`),new i(`'${X[s]}' is not supported in your environment.`,s);this._logger.log(d.Debug,`Selecting transport '${X[s]}'.`);try{return this.features.reconnect=s===X.WebSockets?o:void 0,this._constructTransport(s)}catch(r){return r}}_isITransport(e){return e&&"object"==typeof e&&"connect"in e}_stopConnection(e){if(this._logger.log(d.Debug,`HttpConnection.stopConnection(${e}) called while in state ${this._connectionState}.`),this.transport=void 0,e=this._stopError||e,this._stopError=void 0,"Disconnected"!==this._connectionState){if("Connecting"===this._connectionState)throw this._logger.log(d.Warning,`Call to HttpConnection.stopConnection(${e}) was ignored because the connection is still in the connecting state.`),new Error(`HttpConnection.stopConnection(${e}) was called while the connection is still in the connecting state.`);if("Disconnecting"===this._connectionState&&this._stopPromiseResolver(),e?this._logger.log(d.Error,`Connection disconnected with error '${e}'.`):this._logger.log(d.Information,"Connection disconnected."),this._sendQueue&&(this._sendQueue.stop().catch((e=>{this._logger.log(d.Error,`TransportSendQueue.stop() threw error '${e}'.`)})),this._sendQueue=void 0),this.connectionId=void 0,this._connectionState="Disconnected",this._connectionStarted){this._connectionStarted=!1;try{this.onclose&&this.onclose(e)}catch(t){this._logger.log(d.Error,`HttpConnection.onclose(${e}) threw error '${t}'.`)}}}else this._logger.log(d.Debug,`Call to HttpConnection.stopConnection(${e}) was ignored because the connection is already in the disconnected state.`)}_resolveUrl(e){if(0===e.lastIndexOf("https://",0)||0===e.lastIndexOf("http://",0))return e;if(!m.isBrowser)throw new Error(`Cannot resolve '${e}'.`);const t=window.document.createElement("a");return t.href=e,this._logger.log(d.Information,`Normalizing '${e}' to '${t.href}'.`),t.href}_resolveNegotiateUrl(e){const t=new URL(e);t.pathname.endsWith("/")?t.pathname+="negotiate":t.pathname+="/negotiate";const n=new URLSearchParams(t.searchParams);return n.has("negotiateVersion")||n.append("negotiateVersion",this._negotiateVersion.toString()),n.has("useStatefulReconnect")?"true"===n.get("useStatefulReconnect")&&(this._options._useStatefulReconnect=!0):!0===this._options._useStatefulReconnect&&n.append("useStatefulReconnect","true"),t.search=n.toString(),t.toString()}}class ne{constructor(e){this._transport=e,this._buffer=[],this._executing=!0,this._sendBufferedData=new oe,this._transportResult=new oe,this._sendLoopPromise=this._sendLoop()}send(e){return this._bufferData(e),this._transportResult||(this._transportResult=new oe),this._transportResult.promise}stop(){return this._executing=!1,this._sendBufferedData.resolve(),this._sendLoopPromise}_bufferData(e){if(this._buffer.length&&typeof this._buffer[0]!=typeof e)throw new Error(`Expected data to be of type ${typeof this._buffer} but was of type ${typeof e}`);this._buffer.push(e),this._sendBufferedData.resolve()}async _sendLoop(){for(;;){if(await this._sendBufferedData.promise,!this._executing){this._transportResult&&this._transportResult.reject("Connection stopped.");break}this._sendBufferedData=new oe;const t=this._transportResult;this._transportResult=void 0;const n="string"==typeof this._buffer[0]?this._buffer.join(""):ne._concatBuffers(this._buffer);this._buffer.length=0;try{await this._transport.send(n),t.resolve()}catch(e){t.reject(e)}}}static _concatBuffers(e){const t=e.map((e=>e.byteLength)).reduce(((e,t)=>e+t)),n=new Uint8Array(t);let o=0;for(const s of e)n.set(new Uint8Array(s),o),o+=s.byteLength;return n.buffer}}class oe{constructor(){this.promise=new Promise(((e,t)=>[this._resolver,this._rejecter]=[e,t]))}resolve(){this._resolver()}reject(e){this._rejecter(e)}}class se{constructor(){this.name="json",this.version=2,this.transferFormat=V.Text}parseMessages(e,t){if("string"!=typeof e)throw new Error("Invalid input for JSON hub protocol. Expected a string.");if(!e)return[];null===t&&(t=p.instance);const n=M.parse(e),o=[];for(const s of n){const e=JSON.parse(s);if("number"!=typeof e.type)throw new Error("Invalid payload.");switch(e.type){case N.Invocation:this._isInvocationMessage(e);break;case N.StreamItem:this._isStreamItemMessage(e);break;case N.Completion:this._isCompletionMessage(e);break;case N.Ping:case N.Close:break;case N.Ack:this._isAckMessage(e);break;case N.Sequence:this._isSequenceMessage(e);break;default:t.log(d.Information,"Unknown message type '"+e.type+"' ignored.");continue}o.push(e)}return o}writeMessage(e){return M.write(JSON.stringify(e))}_isInvocationMessage(e){this._assertNotEmptyString(e.target,"Invalid payload for Invocation message."),void 0!==e.invocationId&&this._assertNotEmptyString(e.invocationId,"Invalid payload for Invocation message.")}_isStreamItemMessage(e){if(this._assertNotEmptyString(e.invocationId,"Invalid payload for StreamItem message."),void 0===e.item)throw new Error("Invalid payload for StreamItem message.")}_isCompletionMessage(e){if(e.result&&e.error)throw new Error("Invalid payload for Completion message.");!e.result&&e.error&&this._assertNotEmptyString(e.error,"Invalid payload for Completion message."),this._assertNotEmptyString(e.invocationId,"Invalid payload for Completion message.")}_isAckMessage(e){if("number"!=typeof e.sequenceId)throw new Error("Invalid SequenceId for Ack message.")}_isSequenceMessage(e){if("number"!=typeof e.sequenceId)throw new Error("Invalid SequenceId for Sequence message.")}_assertNotEmptyString(e,t){if("string"!=typeof e||""===e)throw new Error(t)}}const re={trace:d.Trace,debug:d.Debug,info:d.Information,information:d.Information,warn:d.Warning,warning:d.Warning,error:d.Error,critical:d.Critical,none:d.None};class ie{configureLogging(e){if(f.isRequired(e,"logging"),void 0!==e.log)this.logger=e;else if("string"==typeof e){const t=function(e){const t=re[e.toLowerCase()];if(void 0!==t)return t;throw new Error(`Unknown log level: ${e}`)}(e);this.logger=new S(t)}else this.logger=new S(e);return this}withUrl(e,t){return f.isRequired(e,"url"),f.isNotEmpty(e,"url"),this.url=e,this.httpConnectionOptions="object"==typeof t?{...this.httpConnectionOptions,...t}:{...this.httpConnectionOptions,transport:t},this}withHubProtocol(e){return f.isRequired(e,"protocol"),this.protocol=e,this}withAutomaticReconnect(e){if(this.reconnectPolicy)throw new Error("A reconnectPolicy has already been set.");return e?Array.isArray(e)?this.reconnectPolicy=new O(e):this.reconnectPolicy=e:this.reconnectPolicy=new O,this}withServerTimeout(e){return f.isRequired(e,"milliseconds"),this._serverTimeoutInMilliseconds=e,this}withKeepAliveInterval(e){return f.isRequired(e,"milliseconds"),this._keepAliveIntervalInMilliseconds=e,this}withStatefulReconnect(e){return void 0===this.httpConnectionOptions&&(this.httpConnectionOptions={}),this.httpConnectionOptions._useStatefulReconnect=!0,this._statefulReconnectBufferSize=null==e?void 0:e.bufferSize,this}build(){const e=this.httpConnectionOptions||{};if(void 0===e.logger&&(e.logger=this.logger),!this.url)throw new Error("The 'HubConnectionBuilder.withUrl' method must be called before building the connection.");const t=new te(this.url,e);return U.create(t,this.logger||p.instance,this.protocol||new se,this.reconnectPolicy,this._serverTimeoutInMilliseconds,this._keepAliveIntervalInMilliseconds,this._statefulReconnectBufferSize)}}let ce=null,ae=!1,he=0;const le={get isConnected(){return ae&&null!==ce},async initConnection(e){if(!ce)try{ce=(new ie).withUrl(`/hubs/notification?userId=${e}`,{skipNegotiation:!0,transport:X.WebSockets}).withAutomaticReconnect([0,2e3,5e3,1e4,15e3]).configureLogging(d.Information).build(),ce.onclose((async e=>{ae=!1,await this.attemptReconnect()})),this.registerHandlers(),await ce.start(),ae=!0,he=0;try{await ce.invoke("TestConnection")}catch(t){}if(e)try{await ce.invoke("UserOnline",e)}catch(t){}}catch(t){ae=!1,await this.attemptReconnect()}},async attemptReconnect(){if(!(he>=5)){he++;try{await ce.start(),ae=!0,he=0}catch(e){ae=!1,setTimeout((()=>this.attemptReconnect()),5e3)}}},registerHandlers(){ce&&(ce.on("ReceiveNotification",(t=>{try{const n=e(),o={id:t.id,notificationId:t.id,type:t.type,title:t.title,content:t.message,message:t.message,timestamp:t.timestamp,createdAt:t.timestamp,creationTimestamp:t.timestamp,taskId:t.taskId,resourceId:t.taskId,resourceType:t.taskId?"Task":void 0,referenceId:t.taskId,referenceType:t.taskId?"Task":void 0,read:t.read||!1,isRead:t.read||!1};n.addNotification(o),this.showNotification(t)}catch(n){}})),ce.on("TestEvent",(e=>{n({title:"测试通知",message:e,type:"info"})})))},showNotification(e){try{const t=e.title||"系统通知",o=e.message||e.content||"",s=this.getNotificationType(e.type);n({title:t,message:o,type:s,duration:4500,onClick:()=>{this.handleNotificationClick(e)}})}catch(t){}},getNotificationType(e){switch(e){case"TaskAssigned":case"TaskMention":return"success";case"TaskOverdue":return"error";case"TaskStatusChanged":case"TaskContentChanged":return"warning";default:return"info"}},handleNotificationClick(n){try{e().markAsRead(n.id),n.taskId?t.push(`/main/tasks/detail/${n.taskId}`):n.referenceId&&"Task"===n.referenceType&&t.push(`/main/tasks/detail/${n.referenceId}`)}catch(o){}},async sendTestNotification(e){try{if(!ce||!ae)return;const e={id:Date.now(),type:"Test",title:"测试通知",message:`这是一条测试通知 (${(new Date).toLocaleString()})`,timestamp:new Date,read:!1};this.showNotification(e)}catch(t){}},async disconnect(){if(ce&&ae)try{await ce.stop(),ae=!1,ce=null}catch(e){}}};export{le as n};
