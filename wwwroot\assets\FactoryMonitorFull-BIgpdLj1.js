import{_ as a,r as t,c as o,m as e,b as s,d as n,ax as l,e as i,w as r,n as d,F as u,h as c,a8 as v,a as p,o as h,f as g,ay as f,R as x,az as y,i as m,Y as b,ab as w,J as k,af as z,aA as C}from"./index-CkwLz8y6.js";const M={class:"factory-monitor-full"},_={class:"top-status-bar"},$={class:"zoom-controls"},S=["onClick"],I={class:"status-icon"},j=a({__name:"FactoryMonitorFull",setup(a){const j=t(null),E=t([]),R=t(1),W=t(null);t(!1),t(0),t(0),t(null),o((()=>E.value.length)),o((()=>{var a,t;return(null==(t=null==(a=j.value)?void 0:a.zones)?void 0:t.length)||0}));const A=o((()=>({transform:`scale(${R.value})`,transformOrigin:"top left"}))),F=o((()=>{var a;return(null==(a=j.value)?void 0:a.canvas)?{width:j.value.canvas.width+"px",height:j.value.canvas.height+"px",backgroundColor:j.value.canvas.backgroundColor||"#1e293b",border:"2px solid #e5e7eb",borderRadius:"8px",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)",position:"relative"}:{}})),N=a=>{if(!a||!a.zones||!Array.isArray(a.zones))throw new Error("配置文件格式错误：缺少zones数组");let t,o=0,e=0,s=1/0,n=1/0;if(a.zones.forEach((a=>{"number"==typeof a.x&&"number"==typeof a.y&&"number"==typeof a.width&&"number"==typeof a.height&&(s=Math.min(s,a.x),n=Math.min(n,a.y),o=Math.max(o,a.x+a.width),e=Math.max(e,a.y+a.height))})),a.canvas&&a.canvas.width&&a.canvas.height)t={width:a.canvas.width,height:a.canvas.height,backgroundColor:a.canvas.backgroundColor||"#fafafa"};else{const a=100;t={width:Math.max(2e3,o+a),height:Math.max(1200,e+a),backgroundColor:"#fafafa"}}return{name:a.name||"工厂布局监控",canvas:t,zones:a.zones.map((a=>({id:a.id,name:a.name||`区域${a.id}`,color:a.color||"#3b82f6",position:{x:a.x,y:a.y,width:a.width,height:a.height},layout:{rows:a.rows||1,cols:a.cols||1},startWorkstation:a.startWorkstation||1})))}},T=()=>{if(!j.value||!j.value.zones)return[];const a=[],t=["operational","warning","error","idle"],o=[.7,.15,.1,.05];return j.value.zones.forEach((e=>{const{rows:s,cols:n}=e.layout,l=e.startWorkstation;for(let i=0;i<s;i++)for(let s=0;s<n;s++){const r=l+i*n+s,d=Math.random();let u="operational",c=0;for(let a=0;a<o.length;a++)if(c+=o[a],d<c){u=t[a];break}const v={locationId:r,locationName:`${e.name}-工位${r.toString().padStart(3,"0")}`,locationCode:`WS${r.toString().padStart(3,"0")}`,zoneId:e.id,zoneName:e.name,status:u,efficiency:Math.floor(31*Math.random())+70,assetCount:Math.floor(6*Math.random())+2,taskCount:Math.floor(9*Math.random())+1,row:i,col:s,position:{zoneX:e.position.x,zoneY:e.position.y,relativeRow:i,relativeCol:s}};a.push(v)}})),a},Y=()=>({name:"默认工厂布局",canvas:{width:1e3,height:600,backgroundColor:"#1e293b"},zones:[{id:1,name:"默认区域",color:"#3b82f6",position:{x:100,y:100,width:400,height:300},layout:{rows:3,cols:4},startWorkstation:1}]}),D=a=>({position:"absolute",left:a.position.x+"px",top:a.position.y+"px",width:a.position.width+"px",height:a.position.height+"px",border:`2px solid ${a.color}`,borderRadius:"8px",backgroundColor:`${a.color}15`,boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)",transition:"all 0.3s ease"}),J=a=>({display:"grid",gridTemplateColumns:`repeat(${a.layout.cols}, 1fr)`,gridTemplateRows:`repeat(${a.layout.rows}, 1fr)`,gap:"2px",padding:"4px",width:"100%",height:"100%"}),O=a=>E.value.filter((t=>t.zoneId===a.id)).sort(((t,o)=>t.row*a.layout.cols+t.col-(o.row*a.layout.cols+o.col))),P=a=>[`status-${a.status}`],X=()=>{R.value<2&&(R.value=Math.min(2,R.value+.1))},q=()=>{R.value>.5&&(R.value=Math.max(.5,R.value-.1))},B=()=>{R.value=1};return e((()=>{(async()=>{try{const t=["/analyresport/factory-layout-1748964326771.json","/analyresport/factory-layout-1748945213262.json","/analyresport/DP.json"];let o=null,e=null;for(const s of t)try{const a=await fetch(s);if(a.ok){o=await a.json(),e=s;break}}catch(a){continue}if(!o)throw new Error("所有配置文件加载失败，使用默认配置");j.value=N(o),E.value=T(),v.success(`配置加载成功: ${e.split("/").pop()}`)}catch(t){v.error(`加载布局配置失败: ${t.message}`),j.value=Y(),E.value=T()}})()})),(a,t)=>{var o;const e=p("el-icon"),v=p("el-button"),E=p("el-button-group");return h(),s("div",M,[n("div",_,[t[0]||(t[0]=n("div",{class:"status-info"},null,-1)),n("div",$,[i(E,{size:"small"},{default:r((()=>[i(v,{onClick:q,disabled:R.value<=.5},{default:r((()=>[i(e,null,{default:r((()=>[i(g(f))])),_:1})])),_:1},8,["disabled"]),i(v,{onClick:B},{default:r((()=>[i(e,null,{default:r((()=>[i(g(x))])),_:1})])),_:1}),i(v,{onClick:X,disabled:R.value>=2},{default:r((()=>[i(e,null,{default:r((()=>[i(g(y))])),_:1})])),_:1},8,["disabled"])])),_:1})])]),n("div",{class:"factory-layout-container",style:d(A.value)},[n("div",{class:"factory-canvas",style:d(F.value)},[n("div",{class:"grid-background-layer",style:d({position:"absolute",top:"0",left:"0",width:"100%",height:"100%",pointerEvents:"none",zIndex:1,backgroundImage:"\n      linear-gradient(to right, #d1d5db 1px, transparent 1px),\n      linear-gradient(to bottom, #d1d5db 1px, transparent 1px),\n      linear-gradient(to right, #e5e7eb 1px, transparent 1px),\n      linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)\n    ",backgroundSize:"\n      100px 100px,\n      100px 100px,\n      20px 20px,\n      20px 20px\n    ",opacity:.3})},null,4),(h(!0),s(u,null,c((null==(o=j.value)?void 0:o.zones)||[],(a=>(h(),s("div",{key:a.id,class:"zone-container",style:d(D(a))},[n("div",{class:"workstation-grid",style:d(J(a))},[(h(!0),s(u,null,c(O(a),(a=>(h(),s("div",{key:a.locationId,class:m(["workstation-cell",P(a)]),onClick:t=>(a=>{W.value=a})(a)},[n("div",I,["operational"===a.status?(h(),b(e,{key:0},{default:r((()=>[i(g(w))])),_:1})):"warning"===a.status?(h(),b(e,{key:1},{default:r((()=>[i(g(k))])),_:1})):"error"===a.status?(h(),b(e,{key:2},{default:r((()=>[i(g(z))])),_:1})):(h(),b(e,{key:3},{default:r((()=>[i(g(C))])),_:1}))])],10,S)))),128))],4)],4)))),128))],4)],4),t[1]||(t[1]=l('<div class="status-legend" data-v-92699d59><div class="legend-items" data-v-92699d59><div class="legend-item" data-v-92699d59><div class="legend-dot operational" data-v-92699d59></div></div><div class="legend-item" data-v-92699d59><div class="legend-dot warning" data-v-92699d59></div></div><div class="legend-item" data-v-92699d59><div class="legend-dot error" data-v-92699d59></div></div><div class="legend-item" data-v-92699d59><div class="legend-dot idle" data-v-92699d59></div></div></div></div>',1))])}}},[["__scopeId","data-v-92699d59"]]);export{j as default};
