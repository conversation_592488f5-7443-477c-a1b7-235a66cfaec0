using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Core.Interfaces.Services;
using ItAssetsSystem.Application.Features.Tasks.Services;
using ItAssetsSystem.Application.Features.Tasks.Dtos;
using ItAssetsSystem.Core.Monitoring;

namespace ItAssetsSystem.Application.Services.Adapters
{
    /// <summary>
    /// 任务服务适配器 - 基础解耦
    /// 包装现有的任务业务逻辑，提供统一的服务接口
    /// </summary>
    public class TaskServiceAdapter : Core.Interfaces.Services.ITaskService
    {
        private readonly Application.Features.Tasks.Services.ITaskService _taskService;
        private readonly ILogger<TaskServiceAdapter> _logger;
        private readonly IPerformanceMonitor _performanceMonitor;

        public TaskServiceAdapter(
            Application.Features.Tasks.Services.ITaskService taskService,
            ILogger<TaskServiceAdapter> logger,
            IPerformanceMonitor performanceMonitor = null)
        {
            _taskService = taskService;
            _logger = logger;
            _performanceMonitor = performanceMonitor;
        }

        /// <summary>
        /// 根据ID获取任务信息
        /// </summary>
        public async Task<Core.Interfaces.Services.TaskDto> GetByIdAsync(long id)
        {
            if (_performanceMonitor != null)
            {
                return await _performanceMonitor.MonitorAndCompareAsync("TaskGetById", 
                    () => GetByIdInternalAsync(id), "v1.1");
            }
            
            return await GetByIdInternalAsync(id);
        }

        /// <summary>
        /// 内部获取任务信息实现
        /// </summary>
        private async Task<Core.Interfaces.Services.TaskDto> GetByIdInternalAsync(long id)
        {
            try
            {
                var response = await _taskService.GetTaskByIdAsync(id, true, true, true);
                if (!response.Success || response.Data == null)
                {
                    return null;
                }

                return MapToInterfaceTaskDto(response.Data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务信息失败: {TaskId}", id);
                return null;
            }
        }

        /// <summary>
        /// 分页获取任务列表
        /// </summary>
        public async Task<Core.Interfaces.Services.PagedResult<Core.Interfaces.Services.TaskDto>> GetPagedAsync(Core.Interfaces.Services.TaskQueryDto query)
        {
            if (_performanceMonitor != null)
            {
                return await _performanceMonitor.MonitorAndCompareAsync("TaskGetPaged", 
                    () => GetPagedInternalAsync(query), "v1.1");
            }
            
            return await GetPagedInternalAsync(query);
        }

        /// <summary>
        /// 内部分页查询实现
        /// </summary>
        private async Task<Core.Interfaces.Services.PagedResult<Core.Interfaces.Services.TaskDto>> GetPagedInternalAsync(Core.Interfaces.Services.TaskQueryDto query)
        {
            try
            {
                // 转换查询参数
                var internalQuery = new TaskQueryParametersDto
                {
                    PageNumber = query.PageIndex,
                    PageSize = query.PageSize,
                    SearchTerm = query.Keyword ?? string.Empty,
                    Status = query.Status ?? string.Empty,
                    Priority = query.Priority ?? string.Empty,
                    AssigneeUserId = query.AssigneeId,
                    CreatorUserId = query.CreatedBy,
                    FromDate = query.StartDate,
                    ToDate = query.EndDate,
                    SortBy = query.SortBy ?? "CreationTimestamp",
                    SortDirection = query.SortDirection ?? "desc"
                };

                var response = await _taskService.GetTasksAsync(internalQuery);
                if (!response.Success || response.Data == null)
                {
                    return new Core.Interfaces.Services.PagedResult<Core.Interfaces.Services.TaskDto>
                    {
                        Items = new List<Core.Interfaces.Services.TaskDto>(),
                        TotalCount = 0,
                        PageIndex = query.PageIndex,
                        PageSize = query.PageSize
                    };
                }

                var taskDtos = response.Data.Select(MapToInterfaceTaskDto).ToList();

                return new Core.Interfaces.Services.PagedResult<Core.Interfaces.Services.TaskDto>
                {
                    Items = taskDtos,
                    TotalCount = taskDtos.Count, // 简化处理，实际应该从分页结果获取
                    PageIndex = query.PageIndex,
                    PageSize = query.PageSize
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分页获取任务列表失败");
                return new Core.Interfaces.Services.PagedResult<Core.Interfaces.Services.TaskDto>
                {
                    Items = new List<Core.Interfaces.Services.TaskDto>(),
                    TotalCount = 0,
                    PageIndex = query.PageIndex,
                    PageSize = query.PageSize
                };
            }
        }

        /// <summary>
        /// 创建任务
        /// </summary>
        public async Task<Core.Interfaces.Services.TaskDto> CreateAsync(Core.Interfaces.Services.CreateTaskDto dto)
        {
            if (_performanceMonitor != null)
            {
                return await _performanceMonitor.MonitorAndCompareAsync("TaskCreate", 
                    () => CreateInternalAsync(dto), "v1.1");
            }
            
            return await CreateInternalAsync(dto);
        }

        /// <summary>
        /// 内部创建任务实现
        /// </summary>
        private async Task<Core.Interfaces.Services.TaskDto> CreateInternalAsync(Core.Interfaces.Services.CreateTaskDto dto)
        {
            try
            {
                // 转换创建任务DTO
                var internalDto = new CreateTaskRequestDto
                {
                    Name = dto.Title,
                    Description = dto.Description,
                    Priority = dto.Priority ?? "Medium",
                    PlanEndDate = dto.DueDate,
                    AssigneeUserId = dto.AssigneeId,
                    AssetId = dto.RelatedAssetId
                };

                var response = await _taskService.CreateTaskAsync(internalDto, 1); // 使用默认用户ID
                if (!response.Success || response.Data == null)
                {
                    throw new InvalidOperationException(response.Error ?? "创建任务失败");
                }

                _logger.LogInformation("任务创建成功: {TaskName}", dto.Title);
                return MapToInterfaceTaskDto(response.Data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建任务失败: {TaskName}", dto.Title);
                throw;
            }
        }

        /// <summary>
        /// 更新任务
        /// </summary>
        public async Task<Core.Interfaces.Services.TaskDto> UpdateAsync(long id, Core.Interfaces.Services.UpdateTaskDto dto)
        {
            if (_performanceMonitor != null)
            {
                return await _performanceMonitor.MonitorAndCompareAsync("TaskUpdate", 
                    () => UpdateInternalAsync(id, dto), "v1.1");
            }
            
            return await UpdateInternalAsync(id, dto);
        }

        /// <summary>
        /// 内部更新任务实现
        /// </summary>
        private async Task<Core.Interfaces.Services.TaskDto> UpdateInternalAsync(long id, Core.Interfaces.Services.UpdateTaskDto dto)
        {
            try
            {
                // 转换更新任务DTO
                var internalDto = new UpdateTaskRequestDto
                {
                    Name = dto.Title,
                    Description = dto.Description,
                    Priority = dto.Priority,
                    Status = dto.Status,
                    PlanEndDate = dto.DueDate,
                    AssigneeUserId = dto.AssigneeId,
                    AssetId = dto.RelatedAssetId
                };

                var response = await _taskService.UpdateTaskAsync(id, internalDto, 1); // 使用默认用户ID
                if (!response.Success || response.Data == null)
                {
                    throw new InvalidOperationException(response.Error ?? "更新任务失败");
                }

                _logger.LogInformation("任务更新成功: {TaskId}", id);
                return MapToInterfaceTaskDto(response.Data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务失败: {TaskId}", id);
                throw;
            }
        }

        /// <summary>
        /// 完成任务
        /// </summary>
        public async Task<bool> CompleteAsync(long taskId)
        {
            if (_performanceMonitor != null)
            {
                return await _performanceMonitor.MonitorAndCompareAsync("TaskComplete", 
                    () => CompleteInternalAsync(taskId), "v1.1");
            }
            
            return await CompleteInternalAsync(taskId);
        }

        /// <summary>
        /// 内部完成任务实现
        /// </summary>
        private async Task<bool> CompleteInternalAsync(long taskId)
        {
            try
            {
                var response = await _taskService.CompleteTaskAsync(taskId, null, 1); // 使用默认用户ID
                if (response.Success)
                {
                    _logger.LogInformation("任务完成成功: {TaskId}", taskId);
                }
                return response.Success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "完成任务失败: {TaskId}", taskId);
                return false;
            }
        }

        /// <summary>
        /// 分配任务
        /// </summary>
        public async Task<bool> AssignAsync(long taskId, int assigneeId)
        {
            if (_performanceMonitor != null)
            {
                return await _performanceMonitor.MonitorAndCompareAsync("TaskAssign", 
                    () => AssignInternalAsync(taskId, assigneeId), "v1.1");
            }
            
            return await AssignInternalAsync(taskId, assigneeId);
        }

        /// <summary>
        /// 内部分配任务实现
        /// </summary>
        private async Task<bool> AssignInternalAsync(long taskId, int assigneeId)
        {
            try
            {
                var response = await _taskService.AssignTaskAsync(taskId, assigneeId, null, 1); // 使用默认用户ID
                if (response.Success)
                {
                    _logger.LogInformation("任务分配成功: TaskId={TaskId}, AssigneeId={AssigneeId}", taskId, assigneeId);
                }
                return response.Success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分配任务失败: TaskId={TaskId}, AssigneeId={AssigneeId}", taskId, assigneeId);
                return false;
            }
        }

        /// <summary>
        /// 删除任务
        /// </summary>
        public async Task<bool> DeleteAsync(long id)
        {
            if (_performanceMonitor != null)
            {
                return await _performanceMonitor.MonitorAndCompareAsync("TaskDelete", 
                    () => DeleteInternalAsync(id), "v1.1");
            }
            
            return await DeleteInternalAsync(id);
        }

        /// <summary>
        /// 内部删除任务实现
        /// </summary>
        private async Task<bool> DeleteInternalAsync(long id)
        {
            try
            {
                var response = await _taskService.DeleteTaskAsync(id, 1); // 使用默认用户ID
                if (response.Success)
                {
                    _logger.LogInformation("任务删除成功: {TaskId}", id);
                }
                return response.Success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除任务失败: {TaskId}", id);
                return false;
            }
        }

        /// <summary>
        /// 获取用户的任务统计
        /// </summary>
        public async Task<Core.Interfaces.Services.TaskStatisticsDto> GetUserTaskStatisticsAsync(int userId)
        {
            if (_performanceMonitor != null)
            {
                return await _performanceMonitor.MonitorAndCompareAsync("TaskUserStatistics",
                    () => GetUserTaskStatisticsInternalAsync(userId), "v1.1");
            }

            return await GetUserTaskStatisticsInternalAsync(userId);
        }

        /// <summary>
        /// 内部获取用户任务统计实现
        /// </summary>
        private async Task<Core.Interfaces.Services.TaskStatisticsDto> GetUserTaskStatisticsInternalAsync(int userId)
        {
            try
            {
                // 使用查询获取用户的所有任务
                var query = new TaskQueryParametersDto
                {
                    PageNumber = 1,
                    PageSize = 1000, // 获取足够多的任务用于统计
                    AssigneeUserId = userId
                };

                var response = await _taskService.GetTasksAsync(query);
                if (!response.Success || response.Data == null)
                {
                    return new Core.Interfaces.Services.TaskStatisticsDto();
                }

                var tasks = response.Data;
                var totalTasks = tasks.Count;
                var pendingTasks = tasks.Count(t => t.Status == "Pending");
                var inProgressTasks = tasks.Count(t => t.Status == "InProgress");
                var completedTasks = tasks.Count(t => t.Status == "Completed");
                var overdueTasks = tasks.Count(t => t.PlanEndDate.HasValue && t.PlanEndDate < DateTime.Now && t.Status != "Completed");

                return new Core.Interfaces.Services.TaskStatisticsDto
                {
                    TotalTasks = totalTasks,
                    PendingTasks = pendingTasks,
                    InProgressTasks = inProgressTasks,
                    CompletedTasks = completedTasks,
                    OverdueTasks = overdueTasks,
                    CompletionRate = totalTasks > 0 ? (double)completedTasks / totalTasks * 100 : 0
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户任务统计失败: {UserId}", userId);
                return new Core.Interfaces.Services.TaskStatisticsDto();
            }
        }

        #region 周期性任务管理

        /// <summary>
        /// 分页获取周期性任务计划列表
        /// </summary>
        public async Task<Core.Interfaces.Services.PagedResult<Core.Interfaces.Services.PeriodicTaskScheduleDto>> GetPeriodicSchedulesPagedAsync(Core.Interfaces.Services.PeriodicTaskScheduleQueryDto query)
        {
            if (_performanceMonitor != null)
            {
                return await _performanceMonitor.MonitorAndCompareAsync("PeriodicScheduleGetPaged",
                    () => GetPeriodicSchedulesPagedInternalAsync(query), "v1.1");
            }

            return await GetPeriodicSchedulesPagedInternalAsync(query);
        }

        /// <summary>
        /// 内部分页获取周期性任务计划实现
        /// </summary>
        private async Task<Core.Interfaces.Services.PagedResult<Core.Interfaces.Services.PeriodicTaskScheduleDto>> GetPeriodicSchedulesPagedInternalAsync(Core.Interfaces.Services.PeriodicTaskScheduleQueryDto query)
        {
            try
            {
                // 转换查询参数
                var internalQuery = new PeriodicTaskScheduleQueryParametersDto
                {
                    PageNumber = query.PageIndex,
                    PageSize = query.PageSize,
                    SearchTerm = query.Keyword ?? string.Empty,
                    Status = query.Status ?? string.Empty,
                    RecurrenceType = query.RecurrenceType ?? string.Empty,
                    CreatorUserId = query.CreatorUserId,
                    SortBy = query.SortBy ?? "CreationTimestamp",
                    SortDirection = query.SortDirection ?? "desc"
                };

                var response = await _taskService.GetPeriodicSchedulesPagedAsync(internalQuery);
                if (!response.Success || response.Data == null)
                {
                    return new Core.Interfaces.Services.PagedResult<Core.Interfaces.Services.PeriodicTaskScheduleDto>
                    {
                        Items = new List<Core.Interfaces.Services.PeriodicTaskScheduleDto>(),
                        TotalCount = 0,
                        PageIndex = query.PageIndex,
                        PageSize = query.PageSize
                    };
                }

                var scheduleDtos = response.Data.Items.Select(MapToInterfacePeriodicScheduleDto).ToList();

                return new Core.Interfaces.Services.PagedResult<Core.Interfaces.Services.PeriodicTaskScheduleDto>
                {
                    Items = scheduleDtos,
                    TotalCount = response.Data.TotalCount,
                    PageIndex = response.Data.PageIndex,
                    PageSize = response.Data.PageSize
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分页获取周期性任务计划失败");
                return new Core.Interfaces.Services.PagedResult<Core.Interfaces.Services.PeriodicTaskScheduleDto>
                {
                    Items = new List<Core.Interfaces.Services.PeriodicTaskScheduleDto>(),
                    TotalCount = 0,
                    PageIndex = query.PageIndex,
                    PageSize = query.PageSize
                };
            }
        }

        /// <summary>
        /// 根据ID获取周期性任务计划详情
        /// </summary>
        public async Task<Core.Interfaces.Services.PeriodicTaskScheduleDto> GetPeriodicScheduleByIdAsync(long scheduleId)
        {
            if (_performanceMonitor != null)
            {
                return await _performanceMonitor.MonitorAndCompareAsync("PeriodicScheduleGetById",
                    () => GetPeriodicScheduleByIdInternalAsync(scheduleId), "v1.1");
            }

            return await GetPeriodicScheduleByIdInternalAsync(scheduleId);
        }

        /// <summary>
        /// 内部获取周期性任务计划详情实现
        /// </summary>
        private async Task<Core.Interfaces.Services.PeriodicTaskScheduleDto> GetPeriodicScheduleByIdInternalAsync(long scheduleId)
        {
            try
            {
                var response = await _taskService.GetPeriodicScheduleByIdAsync(scheduleId);
                if (!response.Success || response.Data == null)
                {
                    return null;
                }

                return MapToInterfacePeriodicScheduleDto(response.Data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取周期性任务计划详情失败: {ScheduleId}", scheduleId);
                return null;
            }
        }

        /// <summary>
        /// 创建周期性任务计划
        /// </summary>
        public async Task<Core.Interfaces.Services.PeriodicTaskScheduleDto> CreatePeriodicScheduleAsync(Core.Interfaces.Services.CreatePeriodicTaskScheduleDto dto)
        {
            if (_performanceMonitor != null)
            {
                return await _performanceMonitor.MonitorAndCompareAsync("PeriodicScheduleCreate",
                    () => CreatePeriodicScheduleInternalAsync(dto), "v1.1");
            }

            return await CreatePeriodicScheduleInternalAsync(dto);
        }

        /// <summary>
        /// 内部创建周期性任务计划实现
        /// </summary>
        private async Task<Core.Interfaces.Services.PeriodicTaskScheduleDto> CreatePeriodicScheduleInternalAsync(Core.Interfaces.Services.CreatePeriodicTaskScheduleDto dto)
        {
            try
            {
                // 转换创建周期性任务计划DTO
                var internalDto = new CreatePeriodicTaskScheduleRequestDto
                {
                    Name = dto.Name,
                    Description = dto.Description,
                    TemplateTaskId = 0, // 将创建新的模板任务
                    TaskTemplateTitle = dto.TaskName,
                    TaskTemplateDescription = dto.TaskDescription,
                    RecurrenceType = dto.RecurrenceType,
                    CronExpression = dto.CronExpression ?? "0 0 * * *", // 默认每天执行
                    StartDate = dto.StartDate ?? DateTime.Now,
                    EndDate = dto.EndDate,
                    TotalOccurrences = dto.TotalOccurrences,
                    DefaultAssigneeUserIds = dto.DefaultAssigneeUserIds
                };

                var response = await _taskService.CreatePeriodicScheduleAsync(internalDto, 1); // 使用默认用户ID
                if (!response.Success || response.Data == null)
                {
                    throw new InvalidOperationException(response.Error ?? "创建周期性任务计划失败");
                }

                _logger.LogInformation("周期性任务计划创建成功: {ScheduleName}", dto.Name);
                return MapToInterfacePeriodicScheduleDto(response.Data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建周期性任务计划失败: {ScheduleName}", dto.Name);
                throw;
            }
        }

        /// <summary>
        /// 更新周期性任务计划
        /// </summary>
        public async Task<Core.Interfaces.Services.PeriodicTaskScheduleDto> UpdatePeriodicScheduleAsync(long scheduleId, Core.Interfaces.Services.UpdatePeriodicTaskScheduleDto dto)
        {
            if (_performanceMonitor != null)
            {
                return await _performanceMonitor.MonitorAndCompareAsync("PeriodicScheduleUpdate",
                    () => UpdatePeriodicScheduleInternalAsync(scheduleId, dto), "v1.1");
            }

            return await UpdatePeriodicScheduleInternalAsync(scheduleId, dto);
        }

        /// <summary>
        /// 内部更新周期性任务计划实现
        /// </summary>
        private async Task<Core.Interfaces.Services.PeriodicTaskScheduleDto> UpdatePeriodicScheduleInternalAsync(long scheduleId, Core.Interfaces.Services.UpdatePeriodicTaskScheduleDto dto)
        {
            try
            {
                // 转换更新周期性任务计划DTO
                var internalDto = new UpdatePeriodicTaskScheduleRequestDto
                {
                    Name = dto.Name,
                    Description = dto.Description,
                    RecurrenceType = dto.RecurrenceType,
                    CronExpression = dto.CronExpression,
                    StartDate = dto.StartDate,
                    EndDate = dto.EndDate,
                    DefaultAssigneeUserIds = dto.DefaultAssigneeUserIds
                };

                var response = await _taskService.UpdatePeriodicScheduleAsync(scheduleId, internalDto, 1); // 使用默认用户ID
                if (!response.Success || response.Data == null)
                {
                    throw new InvalidOperationException(response.Error ?? "更新周期性任务计划失败");
                }

                _logger.LogInformation("周期性任务计划更新成功: {ScheduleId}", scheduleId);
                return MapToInterfacePeriodicScheduleDto(response.Data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新周期性任务计划失败: {ScheduleId}", scheduleId);
                throw;
            }
        }

        /// <summary>
        /// 删除周期性任务计划
        /// </summary>
        public async Task<bool> DeletePeriodicScheduleAsync(long scheduleId)
        {
            if (_performanceMonitor != null)
            {
                return await _performanceMonitor.MonitorAndCompareAsync("PeriodicScheduleDelete",
                    () => DeletePeriodicScheduleInternalAsync(scheduleId), "v1.1");
            }

            return await DeletePeriodicScheduleInternalAsync(scheduleId);
        }

        /// <summary>
        /// 内部删除周期性任务计划实现
        /// </summary>
        private async Task<bool> DeletePeriodicScheduleInternalAsync(long scheduleId)
        {
            try
            {
                var response = await _taskService.DeletePeriodicScheduleAsync(scheduleId, 1); // 使用默认用户ID
                if (response.Success)
                {
                    _logger.LogInformation("周期性任务计划删除成功: {ScheduleId}", scheduleId);
                }
                return response.Success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除周期性任务计划失败: {ScheduleId}", scheduleId);
                return false;
            }
        }

        /// <summary>
        /// 启用/禁用周期性任务计划
        /// </summary>
        public async Task<bool> EnablePeriodicScheduleAsync(long scheduleId, bool isEnabled)
        {
            if (_performanceMonitor != null)
            {
                return await _performanceMonitor.MonitorAndCompareAsync("PeriodicScheduleEnable",
                    () => EnablePeriodicScheduleInternalAsync(scheduleId, isEnabled), "v1.1");
            }

            return await EnablePeriodicScheduleInternalAsync(scheduleId, isEnabled);
        }

        /// <summary>
        /// 内部启用/禁用周期性任务计划实现
        /// </summary>
        private async Task<bool> EnablePeriodicScheduleInternalAsync(long scheduleId, bool isEnabled)
        {
            try
            {
                var response = await _taskService.EnablePeriodicScheduleAsync(scheduleId, isEnabled, 1); // 使用默认用户ID
                if (response.Success)
                {
                    _logger.LogInformation("周期性任务计划状态更新成功: ScheduleId={ScheduleId}, Enabled={Enabled}", scheduleId, isEnabled);
                }
                return response.Success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新周期性任务计划状态失败: ScheduleId={ScheduleId}, Enabled={Enabled}", scheduleId, isEnabled);
                return false;
            }
        }

        #endregion

        /// <summary>
        /// 映射内部TaskDto到接口TaskDto
        /// </summary>
        private Core.Interfaces.Services.TaskDto MapToInterfaceTaskDto(Application.Features.Tasks.Dtos.TaskDto internalDto)
        {
            return new Core.Interfaces.Services.TaskDto
            {
                Id = internalDto.TaskId,
                Title = internalDto.Name,
                Description = internalDto.Description,
                Status = internalDto.Status,
                Priority = internalDto.Priority,
                AssigneeId = internalDto.AssigneeUserId,
                AssigneeName = internalDto.AssigneeUserName,
                CreatedBy = internalDto.CreatorUserId,
                CreatedByName = internalDto.CreatorUserName,
                CreatedAt = internalDto.CreationTimestamp,
                UpdatedAt = internalDto.LastUpdatedTimestamp,
                DueDate = internalDto.PlanEndDate,
                CompletedAt = internalDto.ActualEndDate,
                Notes = internalDto.Description, // 使用Description作为Notes
                RelatedAssetId = internalDto.AssetId,
                RelatedAssetName = internalDto.AssetName,
                Comments = internalDto.Comments?.Select(c => new Core.Interfaces.Services.TaskCommentDto
                {
                    Id = c.CommentId,
                    TaskId = internalDto.TaskId,
                    Content = c.Content,
                    CreatedBy = c.UserId,
                    CreatedByName = c.UserName,
                    CreatedAt = c.CreationTimestamp
                }).ToList() ?? new List<Core.Interfaces.Services.TaskCommentDto>()
            };
        }

        /// <summary>
        /// 映射内部PeriodicTaskScheduleDto到接口PeriodicTaskScheduleDto
        /// </summary>
        private Core.Interfaces.Services.PeriodicTaskScheduleDto MapToInterfacePeriodicScheduleDto(Application.Features.Tasks.Dtos.PeriodicTaskScheduleDto internalDto)
        {
            return new Core.Interfaces.Services.PeriodicTaskScheduleDto
            {
                Id = internalDto.Id,
                Name = internalDto.Name,
                Description = internalDto.Description,
                Status = internalDto.Status,
                RecurrenceType = internalDto.RecurrenceType,
                CronExpression = internalDto.CronExpression,
                StartDate = internalDto.StartDate,
                EndDate = internalDto.EndDate,
                NextGenerationTime = internalDto.NextGenerationTime,
                OccurrencesGenerated = internalDto.OccurrencesGenerated,
                TotalOccurrences = internalDto.TotalOccurrences,
                CreatorUserId = internalDto.CreatorUserId,
                CreatorUserName = internalDto.CreatorUserName,
                CreatedAt = internalDto.CreationTimestamp,
                UpdatedAt = internalDto.LastUpdatedTimestamp,
                DefaultAssigneeUserIds = internalDto.DefaultAssigneeUserIds ?? new List<int>(),
                DefaultAssigneeUserNames = new List<string>() // 暂时使用空列表，实际需要从用户服务获取
            };
        }
    }
}
