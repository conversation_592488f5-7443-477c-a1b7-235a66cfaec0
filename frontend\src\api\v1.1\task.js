/**
 * 任务API接口 V1.1 - 新接口化版本
 * 文件路径: src/api/v1.1/task.js
 * 功能描述: 基于新的统一服务接口的任务管理API，包含周期性任务
 */

import request from '@/utils/request'

/**
 * 任务API V1.1
 */
const taskApiV1_1 = {
  /**
   * 获取任务列表 (V1.1)
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  getTaskList(params) {
    const apiParams = {
      pageIndex: params.pageIndex || params.pageNumber || 1,
      pageSize: params.pageSize || 20,
      keyword: params.keyword || params.searchTerm || '',
      status: params.status || '',
      priority: params.priority || '',
      assigneeId: params.assigneeId || params.assigneeUserId,
      createdBy: params.createdBy || params.creatorUserId,
      startDate: params.startDate,
      endDate: params.endDate,
      sortBy: params.sortBy || 'CreatedAt',
      sortDirection: params.sortDirection || 'desc'
    }
    
    console.log('V1.1 获取任务列表，参数:', apiParams)
    return request.get('/api/v1.1/task', { params: apiParams })
  },

  /**
   * 获取任务详情 (V1.1)
   * @param {number|string} id 任务ID
   * @returns {Promise}
   */
  getTaskById(id) {
    console.log('V1.1 获取任务详情，ID:', id)
    return request.get(`/api/v1.1/task/${id}`)
  },

  /**
   * 创建任务 (V1.1)
   * @param {Object} data 任务数据
   * @returns {Promise}
   */
  createTask(data) {
    const apiData = {
      title: data.title || data.name,
      description: data.description || '',
      priority: data.priority || 'Medium',
      status: data.status || 'Pending',
      assigneeId: data.assigneeId || data.assigneeUserId,
      dueDate: data.dueDate || data.planEndDate,
      relatedAssetId: data.relatedAssetId || data.assetId,
      notes: data.notes || data.description
    }
    
    console.log('V1.1 创建任务，数据:', apiData)
    return request.post('/api/v1.1/task', apiData)
  },

  /**
   * 更新任务 (V1.1)
   * @param {number|string} id 任务ID
   * @param {Object} data 任务数据
   * @returns {Promise}
   */
  updateTask(id, data) {
    const apiData = {
      title: data.title || data.name,
      description: data.description || '',
      priority: data.priority,
      status: data.status,
      assigneeId: data.assigneeId || data.assigneeUserId,
      dueDate: data.dueDate || data.planEndDate,
      relatedAssetId: data.relatedAssetId || data.assetId,
      notes: data.notes || data.description
    }
    
    console.log('V1.1 更新任务，ID:', id, '数据:', apiData)
    return request.put(`/api/v1.1/task/${id}`, apiData)
  },

  /**
   * 分配任务 (V1.1)
   * @param {number|string} id 任务ID
   * @param {Object} data 分配数据
   * @returns {Promise}
   */
  assignTask(id, data) {
    const apiData = {
      assigneeId: data.assigneeId || data.assigneeUserId
    }
    
    console.log('V1.1 分配任务，ID:', id, '数据:', apiData)
    return request.post(`/api/v1.1/task/${id}/assign`, apiData)
  },

  /**
   * 完成任务 (V1.1)
   * @param {number|string} id 任务ID
   * @returns {Promise}
   */
  completeTask(id) {
    console.log('V1.1 完成任务，ID:', id)
    return request.post(`/api/v1.1/task/${id}/complete`)
  },

  /**
   * 删除任务 (V1.1)
   * @param {number|string} id 任务ID
   * @returns {Promise}
   */
  deleteTask(id) {
    console.log('V1.1 删除任务，ID:', id)
    return request.delete(`/api/v1.1/task/${id}`)
  },

  /**
   * 获取用户任务统计 (V1.1)
   * @param {number|string} userId 用户ID
   * @returns {Promise}
   */
  getUserTaskStatistics(userId) {
    console.log('V1.1 获取用户任务统计，用户ID:', userId)
    return request.get(`/api/v1.1/task/statistics/${userId}`)
  },

  // ========== 周期性任务管理 ==========

  /**
   * 获取周期性任务计划列表 (V1.1)
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  getPeriodicSchedules(params) {
    const apiParams = {
      pageIndex: params.pageIndex || params.pageNumber || 1,
      pageSize: params.pageSize || 20,
      keyword: params.keyword || params.searchTerm || '',
      status: params.status || '',
      recurrenceType: params.recurrenceType || '',
      creatorUserId: params.creatorUserId,
      sortBy: params.sortBy || 'CreatedAt',
      sortDirection: params.sortDirection || 'desc'
    }
    
    console.log('V1.1 获取周期性任务计划列表，参数:', apiParams)
    return request.get('/api/v1.1/task/periodic-schedules', { params: apiParams })
  },

  /**
   * 获取周期性任务计划详情 (V1.1)
   * @param {number|string} id 计划ID
   * @returns {Promise}
   */
  getPeriodicScheduleById(id) {
    console.log('V1.1 获取周期性任务计划详情，ID:', id)
    return request.get(`/api/v1.1/task/periodic-schedules/${id}`)
  },

  /**
   * 创建周期性任务计划 (V1.1)
   * @param {Object} data 计划数据
   * @returns {Promise}
   */
  createPeriodicSchedule(data) {
    const apiData = {
      name: data.name,
      description: data.description || '',
      recurrenceType: data.recurrenceType,
      cronExpression: data.cronExpression,
      startDate: data.startDate,
      endDate: data.endDate,
      totalOccurrences: data.totalOccurrences,
      defaultAssigneeUserIds: data.defaultAssigneeUserIds || [],
      taskName: data.taskName,
      taskDescription: data.taskDescription || '',
      taskPriority: data.taskPriority || 'Medium',
      taskType: data.taskType || 'Periodic',
      points: data.points,
      assetId: data.assetId,
      locationId: data.locationId
    }
    
    console.log('V1.1 创建周期性任务计划，数据:', apiData)
    return request.post('/api/v1.1/task/periodic-schedules', apiData)
  },

  /**
   * 更新周期性任务计划 (V1.1)
   * @param {number|string} id 计划ID
   * @param {Object} data 计划数据
   * @returns {Promise}
   */
  updatePeriodicSchedule(id, data) {
    const apiData = {
      name: data.name,
      description: data.description,
      status: data.status,
      recurrenceType: data.recurrenceType,
      cronExpression: data.cronExpression,
      startDate: data.startDate,
      endDate: data.endDate,
      totalOccurrences: data.totalOccurrences,
      defaultAssigneeUserIds: data.defaultAssigneeUserIds || []
    }
    
    console.log('V1.1 更新周期性任务计划，ID:', id, '数据:', apiData)
    return request.put(`/api/v1.1/task/periodic-schedules/${id}`, apiData)
  },

  /**
   * 删除周期性任务计划 (V1.1)
   * @param {number|string} id 计划ID
   * @returns {Promise}
   */
  deletePeriodicSchedule(id) {
    console.log('V1.1 删除周期性任务计划，ID:', id)
    return request.delete(`/api/v1.1/task/periodic-schedules/${id}`)
  },

  /**
   * 启用/禁用周期性任务计划 (V1.1)
   * @param {number|string} id 计划ID
   * @param {boolean} enabled 是否启用
   * @returns {Promise}
   */
  togglePeriodicSchedule(id, enabled) {
    const apiData = { enabled }
    
    console.log('V1.1 切换周期性任务计划状态，ID:', id, '启用:', enabled)
    return request.post(`/api/v1.1/task/periodic-schedules/${id}/toggle`, apiData)
  },

  /**
   * 健康检查 (V1.1)
   * @returns {Promise}
   */
  healthCheck() {
    return request.get('/api/v1.1/task/health')
  }
}

// 兼容性方法 - 保持与V1/V2 API相同的方法名
taskApiV1_1.getTasks = taskApiV1_1.getTaskList
taskApiV1_1.getTaskDetail = taskApiV1_1.getTaskById
taskApiV1_1.updateTaskStatus = (id, statusData) => {
  return taskApiV1_1.updateTask(id, { status: statusData.status })
}
taskApiV1_1.updateTaskProgress = (id, progressData) => {
  return taskApiV1_1.updateTask(id, { progress: progressData.progress })
}

export default taskApiV1_1
