// File: Core/Initialization/TaskTablesInitializer.cs
// Description: 任务相关表初始化器

using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Infrastructure.Data;

namespace ItAssetsSystem.Core.Initialization
{
    /// <summary>
    /// 任务相关表初始化器
    /// </summary>
    public class TaskTablesInitializer
    {
        private readonly AppDbContext _context;
        private readonly ILogger<TaskTablesInitializer> _logger;

        public TaskTablesInitializer(AppDbContext context, ILogger<TaskTablesInitializer> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 初始化任务相关表
        /// </summary>
        public async Task InitializeAsync()
        {
            try
            {
                _logger.LogInformation("开始初始化任务相关表...");

                // 创建task_claims表
                await CreateTaskClaimsTableAsync();

                // 创建task_reminders表
                await CreateTaskRemindersTableAsync();

                // 创建user_shift_assignments表
                await CreateUserShiftAssignmentsTableAsync();

                _logger.LogInformation("任务相关表初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化任务相关表时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 创建task_claims表
        /// </summary>
        private async Task CreateTaskClaimsTableAsync()
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS `task_claims` (
                    `claim_id` BIGINT AUTO_INCREMENT PRIMARY KEY,
                    `task_id` BIGINT NOT NULL COMMENT '任务ID',
                    `claimed_by` INT NOT NULL COMMENT '领取用户ID',
                    `shift_id` BIGINT NOT NULL COMMENT '班次ID',
                    `claimed_at` DATETIME NOT NULL COMMENT '领取时间',
                    `claim_date` DATE NOT NULL COMMENT '领取日期',
                    `claim_status` VARCHAR(20) NOT NULL DEFAULT 'Claimed' COMMENT '领取状态',
                    `started_at` DATETIME NULL COMMENT '开始时间',
                    `completed_at` DATETIME NULL COMMENT '完成时间',
                    `notes` VARCHAR(1000) NULL COMMENT '备注',
                    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    
                    INDEX `ix_task_claims_task_id` (`task_id`),
                    INDEX `ix_task_claims_claimed_by` (`claimed_by`),
                    INDEX `ix_task_claims_shift_id` (`shift_id`),
                    INDEX `ix_task_claims_claim_date` (`claim_date`),
                    UNIQUE INDEX `ix_task_claims_unique` (`task_id`, `claimed_by`, `claim_date`),
                    
                    FOREIGN KEY (`task_id`) REFERENCES `Tasks` (`TaskId`) ON DELETE CASCADE,
                    FOREIGN KEY (`claimed_by`) REFERENCES `Users` (`Id`) ON DELETE RESTRICT,
                    FOREIGN KEY (`shift_id`) REFERENCES `work_shifts` (`shift_id`) ON DELETE RESTRICT
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            ";

            await _context.Database.ExecuteSqlRawAsync(sql);
            _logger.LogInformation("task_claims表创建完成");
        }

        /// <summary>
        /// 创建task_reminders表
        /// </summary>
        private async Task CreateTaskRemindersTableAsync()
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS `task_reminders` (
                    `reminder_id` BIGINT AUTO_INCREMENT PRIMARY KEY,
                    `task_id` BIGINT NOT NULL COMMENT '任务ID',
                    `reminder_type` VARCHAR(20) NOT NULL COMMENT '提醒类型',
                    `offset_minutes` INT NOT NULL COMMENT '提前分钟数',
                    `reminder_level` VARCHAR(20) NOT NULL COMMENT '提醒级别',
                    `reminder_method` VARCHAR(20) NOT NULL COMMENT '提醒方式',
                    `is_recurring` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否重复',
                    `recurring_interval` INT NULL COMMENT '重复间隔(分钟)',
                    `max_occurrences` INT NULL COMMENT '最大发送次数',
                    `sent_count` INT NOT NULL DEFAULT 0 COMMENT '已发送次数',
                    `next_reminder_time` DATETIME NULL COMMENT '下次提醒时间',
                    `last_sent_at` DATETIME NULL COMMENT '最后发送时间',
                    `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
                    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    `created_by` INT NOT NULL COMMENT '创建用户ID',
                    `updated_by` INT NULL COMMENT '更新用户ID',
                    
                    INDEX `ix_task_reminders_task_id` (`task_id`),
                    INDEX `ix_task_reminders_next_reminder_time` (`next_reminder_time`),
                    INDEX `ix_task_reminders_active_next` (`is_active`, `next_reminder_time`),
                    
                    FOREIGN KEY (`task_id`) REFERENCES `Tasks` (`TaskId`) ON DELETE CASCADE,
                    FOREIGN KEY (`created_by`) REFERENCES `Users` (`Id`) ON DELETE RESTRICT,
                    FOREIGN KEY (`updated_by`) REFERENCES `Users` (`Id`) ON DELETE RESTRICT
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            ";

            await _context.Database.ExecuteSqlRawAsync(sql);
            _logger.LogInformation("task_reminders表创建完成");
        }

        /// <summary>
        /// 创建user_shift_assignments表
        /// </summary>
        private async Task CreateUserShiftAssignmentsTableAsync()
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS `user_shift_assignments` (
                    `assignment_id` BIGINT AUTO_INCREMENT PRIMARY KEY,
                    `user_id` INT NOT NULL COMMENT '用户ID',
                    `shift_id` BIGINT NOT NULL COMMENT '班次ID',
                    `effective_date` DATE NOT NULL COMMENT '生效日期',
                    `expiry_date` DATE NULL COMMENT '失效日期',
                    `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
                    `assignment_type` VARCHAR(20) NOT NULL DEFAULT 'Permanent' COMMENT '分配类型',
                    `notes` VARCHAR(500) NULL COMMENT '备注',
                    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    `created_by` INT NOT NULL COMMENT '创建用户ID',
                    `updated_by` INT NULL COMMENT '更新用户ID',

                    INDEX `ix_user_shift_assignments_user_id` (`user_id`),
                    INDEX `ix_user_shift_assignments_shift_id` (`shift_id`),
                    INDEX `ix_user_shift_assignments_effective_date` (`effective_date`),
                    INDEX `ix_user_shift_assignments_active` (`is_active`),
                    UNIQUE INDEX `ix_user_shift_assignments_unique` (`user_id`, `shift_id`, `effective_date`),

                    FOREIGN KEY (`user_id`) REFERENCES `Users` (`Id`) ON DELETE CASCADE,
                    FOREIGN KEY (`shift_id`) REFERENCES `work_shifts` (`shift_id`) ON DELETE CASCADE,
                    FOREIGN KEY (`created_by`) REFERENCES `Users` (`Id`) ON DELETE RESTRICT,
                    FOREIGN KEY (`updated_by`) REFERENCES `Users` (`Id`) ON DELETE RESTRICT
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            ";

            await _context.Database.ExecuteSqlRawAsync(sql);
            _logger.LogInformation("user_shift_assignments表创建完成");
        }
    }
}
