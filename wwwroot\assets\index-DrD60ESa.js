import{_ as a,j as e,r as s,c as l,p as t,a5 as d,m as i,s as c,a8 as n,b as o,d as u,t as r,F as v,e as p,a9 as m,f as h,w as f,A as g,a as _,u as k,o as y,aa as w,ab as C,ac as b,ad as x,Y as T,h as z,i as S}from"./index-C7OOw0MO.js";import{t as L}from"./task-BTGSy_AK.js";import{u as I}from"./gamification-CzHSNMa2.js";import{s as P}from"./system-9jEcQzSp.js";const j={class:"dashboard-container"},F={class:"debug-info",style:{"margin-bottom":"10px",padding:"8px","background-color":"#f5f7fa","border-radius":"4px","font-size":"12px"}},N={class:"welcome-section"},$={class:"welcome-info"},D={class:"welcome-title"},O={class:"welcome-subtitle"},Y={key:0,class:"user-game-info"},A={class:"level-badge"},B={class:"score-info"},H={class:"score-value"},U={class:"level-progress-text"},V={class:"card-header"},q={class:"card-content"},E={class:"card-value"},G={class:"card-header"},J={class:"card-content"},K={class:"card-value"},M={class:"card-header"},Q={class:"card-content"},R={class:"card-value"},W={class:"card-actions"},X={class:"card-header"},Z={class:"card-content"},aa={class:"card-value"},ea={class:"card-header"},sa={class:"game-metrics-content"},la={class:"metric-item"},ta={class:"metric-icon task-completion"},da={class:"metric-info"},ia={class:"metric-value"},ca={class:"metric-item"},na={class:"metric-icon task-streak"},oa={class:"metric-info"},ua={class:"metric-value"},ra={class:"metric-item"},va={class:"metric-icon task-items"},pa={class:"metric-info"},ma={class:"metric-value"},ha={class:"card-header"},fa={class:"chart-placeholder"},ga={key:0,class:"chart-loading"},_a={key:1,class:"chart-empty"},ka={class:"card-header"},ya={class:"chart-placeholder"},wa={key:0,class:"chart-loading"},Ca={key:1,class:"chart-empty"},ba={class:"card-header"},xa={key:0,class:"task-list"},Ta={key:1,class:"loading-placeholder"},za={class:"card-header"},Sa={key:0,class:"notification-list"},La={class:"notification-content"},Ia={class:"notification-title"},Pa={class:"notification-time"},ja={key:1,class:"loading-placeholder"},Fa={key:1,class:"simple-dashboard"},Na={class:"simple-card-content"},$a={class:"simple-value"},Da=a({__name:"index",setup(a){const Da=k(),Oa=c(),Ya=e(),Aa=I(),Ba=s(!0),Ha=l((()=>Oa.path)),Ua=l((()=>Ha.value.includes("/main/dashboard")||"/main"===Ha.value));t(Oa,(a=>{}),{immediate:!0});const Va=d([{title:"资产总数",value:"123"},{title:"待处理故障",value:"5"},{title:"待办任务",value:"8"}]),qa=()=>{Da.push("/main/dashboard")},Ea=d({assetCount:0,pendingFaultCount:0,pendingTaskCount:0,pendingPurchaseCount:0}),Ga=s([]),Ja=s([]),Ka=l((()=>{const a=(new Date).getHours();return a<6?"凌晨好":a<9?"早上好":a<12?"上午好":a<14?"中午好":a<18?"下午好":"晚上好"})),Ma=(new Date).toISOString().split("T")[0],Qa=()=>{Da.push("/task/list")},Ra=l((()=>({level:Aa.level,score:Aa.score,levelProgress:Aa.currentLevelProgress,nextLevelScore:Aa.pointsToNextLevel}))),Wa=s({completedCount:0,completionStreak:0,itemCount:0}),Xa=()=>{Da.push("/main/tasks/board")},Za=()=>{Da.push("/main/tasks/board")},ae=()=>{Da.push("/main/tasks/board")};return i((()=>{var a;(async()=>{var a;Ba.value=!0;try{const e=null==(a=Ya.userInfo)?void 0:a.id,s=[];s.push(L.getTaskList({status:"in-progress",pageSize:1}).catch((a=>null))),s.push(L.getTaskList({status:"unstarted",pageSize:1}).catch((a=>null))),e?s.push(L.getTaskList({assigneeId:e,pageSize:5,sortBy:"creationTimestamp",sortOrder:"desc"}).catch((a=>null))):s.push(Promise.resolve(null)),s.push(L.getTaskList({status:"completed",pageSize:1}).catch((a=>null)));const[l,t,d,i]=await Promise.all(s);let c=0;(null==l?void 0:l.totalCount)&&(c+=l.totalCount),(null==l?void 0:l.total)&&(c+=l.total),(null==t?void 0:t.totalCount)&&(c+=t.totalCount),(null==t?void 0:t.total)&&(c+=t.total),Ea.pendingTaskCount=c,(null==d?void 0:d.items)?Ga.value=d.items:(null==d?void 0:d.list)?Ga.value=d.list:Ga.value=[];let o=0;(null==i?void 0:i.totalCount)&&(o+=i.totalCount),(null==i?void 0:i.total)&&(o+=i.total),Wa.value.completedCount=o,Wa.value.completionStreak=0,Wa.value.itemCount=Aa.inventory.length,Ua.value&&n.success("仪表盘数据加载完成")}catch(e){n.error("仪表盘数据加载失败"),Ea.pendingTaskCount=0,Ga.value=[],Wa.value.completedCount=0}finally{Ba.value=!1}})(),n({message:`${Ka.value}，${(null==(a=Ya.userInfo)?void 0:a.name)||"用户"}！欢迎使用${P.appName||"IT资产管理系统"}`,type:"success",duration:3e3})})),(a,e)=>{var s;const l=_("el-tag"),t=_("el-tooltip"),d=_("el-icon"),i=_("el-progress"),c=_("el-card"),n=_("el-col"),k=_("el-button"),L=_("el-row"),I=_("el-skeleton"),Da=_("el-empty"),Oa=_("el-table-column"),Aa=_("el-table"),ee=_("el-badge"),se=_("el-result");return y(),o("div",j,[u("div",F,[u("p",null,"当前路由路径: "+r(Ha.value),1),u("p",null,"匹配结果: "+r(Ua.value?"主仪表盘":"简易仪表盘"),1)]),Ua.value?(y(),o(v,{key:0},[u("div",N,[u("div",$,[u("h2",D,r(Ka.value)+"，"+r((null==(s=h(Ya).userInfo)?void 0:s.name)||"用户"),1),u("p",O,"今天是 "+r(h(Ma))+"，欢迎使用"+r(h(P).appName||"IT资产管理系统"),1)]),Ra.value?(y(),o("div",Y,[u("div",A,[p(t,{content:"当前等级"},{default:f((()=>[p(l,{size:"large",effect:"dark",class:"level-tag"},{default:f((()=>[g("LV."+r(Ra.value.level),1)])),_:1})])),_:1})]),u("div",B,[u("div",H,[p(d,null,{default:f((()=>[p(h(w))])),_:1}),g(" "+r(Ra.value.score)+" 积分 ",1)]),p(i,{percentage:Ra.value.levelProgress,"stroke-width":8,"show-text":!1,class:"level-progress"},null,8,["percentage"]),u("div",U," 距离下一级还需 "+r(Ra.value.nextLevelScore)+" 积分 ",1)])])):m("",!0)]),p(L,{gutter:20,class:"data-overview"},{default:f((()=>[p(n,{xs:24,sm:12,md:6},{default:f((()=>[p(c,{shadow:"hover",class:"data-card"},{header:f((()=>[u("div",V,[e[1]||(e[1]=u("span",null,"资产总数",-1)),p(l,{size:"small"},{default:f((()=>e[0]||(e[0]=[g("总计")]))),_:1})])])),default:f((()=>[u("div",q,[u("div",E,r(Ea.assetCount),1)])])),_:1})])),_:1}),p(n,{xs:24,sm:12,md:6},{default:f((()=>[p(c,{shadow:"hover",class:"data-card"},{header:f((()=>[u("div",G,[e[3]||(e[3]=u("span",null,"待处理故障",-1)),p(l,{size:"small",type:"warning"},{default:f((()=>e[2]||(e[2]=[g("待处理")]))),_:1})])])),default:f((()=>[u("div",J,[u("div",K,r(Ea.pendingFaultCount),1)])])),_:1})])),_:1}),p(n,{xs:24,sm:12,md:6},{default:f((()=>[p(c,{shadow:"hover",class:"data-card task-card-highlight"},{header:f((()=>[u("div",M,[e[5]||(e[5]=u("span",null,"待办任务",-1)),p(l,{size:"small",type:"info"},{default:f((()=>e[4]||(e[4]=[g("进行中")]))),_:1})])])),default:f((()=>[u("div",Q,[u("div",R,r(Ea.pendingTaskCount),1),u("div",W,[p(k,{type:"text",onClick:Xa},{default:f((()=>e[6]||(e[6]=[g("查看任务看板")]))),_:1})])])])),_:1})])),_:1}),p(n,{xs:24,sm:12,md:6},{default:f((()=>[p(c,{shadow:"hover",class:"data-card"},{header:f((()=>[u("div",X,[e[8]||(e[8]=u("span",null,"待审批采购",-1)),p(l,{size:"small",type:"success"},{default:f((()=>e[7]||(e[7]=[g("审批中")]))),_:1})])])),default:f((()=>[u("div",Z,[u("div",aa,r(Ea.pendingPurchaseCount),1)])])),_:1})])),_:1})])),_:1}),p(L,{gutter:20,class:"game-metrics-section"},{default:f((()=>[p(n,{xs:24},{default:f((()=>[p(c,{shadow:"hover",class:"game-metrics-card"},{header:f((()=>[u("div",ea,[e[10]||(e[10]=u("span",null,"任务成就指标",-1)),p(k,{type:"primary",link:"",onClick:Za},{default:f((()=>e[9]||(e[9]=[g("查看排行榜")]))),_:1})])])),default:f((()=>[u("div",sa,[p(L,{gutter:30},{default:f((()=>[p(n,{xs:24,sm:8},{default:f((()=>[u("div",la,[u("div",ta,[p(d,null,{default:f((()=>[p(h(C))])),_:1})]),u("div",da,[u("div",ia,r(Wa.value.completedCount||0),1),e[11]||(e[11]=u("div",{class:"metric-label"},"已完成任务",-1))])])])),_:1}),p(n,{xs:24,sm:8},{default:f((()=>[u("div",ca,[u("div",na,[p(d,null,{default:f((()=>[p(h(b))])),_:1})]),u("div",oa,[u("div",ua,r(Wa.value.completionStreak||0),1),e[12]||(e[12]=u("div",{class:"metric-label"},"连续完成天数",-1))])])])),_:1}),p(n,{xs:24,sm:8},{default:f((()=>[u("div",ra,[u("div",va,[p(d,null,{default:f((()=>[p(h(x))])),_:1})]),u("div",pa,[u("div",ma,r(Wa.value.itemCount||0),1),e[14]||(e[14]=u("div",{class:"metric-label"},"拥有道具数",-1)),Wa.value.itemCount>0?(y(),T(k,{key:0,type:"success",size:"small",onClick:ae},{default:f((()=>e[13]||(e[13]=[g("查看道具")]))),_:1})):m("",!0)])])])),_:1})])),_:1})])])),_:1})])),_:1})])),_:1}),p(L,{gutter:20,class:"chart-section"},{default:f((()=>[p(n,{xs:24,md:12},{default:f((()=>[p(c,{shadow:"hover",class:"chart-card"},{header:f((()=>[u("div",ha,[e[16]||(e[16]=u("span",null,"资产分类统计",-1)),p(k,{type:"link"},{default:f((()=>e[15]||(e[15]=[g("详情")]))),_:1})])])),default:f((()=>[u("div",fa,[Ba.value?(y(),o("div",ga,[p(I,{animated:"",rows:5})])):(y(),o("div",_a,[p(Da,{description:"资产分类数据加载中"})]))])])),_:1})])),_:1}),p(n,{xs:24,md:12},{default:f((()=>[p(c,{shadow:"hover",class:"chart-card"},{header:f((()=>[u("div",ka,[e[18]||(e[18]=u("span",null,"故障趋势分析",-1)),p(k,{type:"link"},{default:f((()=>e[17]||(e[17]=[g("详情")]))),_:1})])])),default:f((()=>[u("div",ya,[Ba.value?(y(),o("div",wa,[p(I,{animated:"",rows:5})])):(y(),o("div",Ca,[p(Da,{description:"故障趋势数据加载中"})]))])])),_:1})])),_:1})])),_:1}),p(L,{gutter:20,class:"task-notification-section"},{default:f((()=>[p(n,{xs:24,lg:16},{default:f((()=>[p(c,{shadow:"hover",class:"task-card"},{header:f((()=>[u("div",ba,[e[20]||(e[20]=u("span",null,"我的任务",-1)),p(k,{type:"link",onClick:Qa},{default:f((()=>e[19]||(e[19]=[g("查看更多")]))),_:1})])])),default:f((()=>[!Ba.value&&Ga.value.length>0?(y(),o("div",xa,[p(Aa,{data:Ga.value,style:{width:"100%"},"show-header":!1},{default:f((()=>[p(Oa,{width:"40"},{default:f((a=>{return[p(l,{type:(e=a.row.status,{unstarted:"info","in-progress":"primary",completed:"success"}[e]||"info"),size:"small",effect:"plain"},null,8,["type"])];var e})),_:1}),p(Oa,{prop:"title",label:"任务标题"}),p(Oa,{prop:"deadline",label:"截止日期",width:"120"}),p(Oa,{width:"80"},{default:f((()=>[p(k,{type:"text"},{default:f((()=>e[21]||(e[21]=[g("处理")]))),_:1})])),_:1})])),_:1},8,["data"])])):Ba.value?(y(),o("div",Ta,[p(I,{animated:"",rows:3})])):(y(),T(Da,{key:2,description:"暂无任务"}))])),_:1})])),_:1}),p(n,{xs:24,lg:8},{default:f((()=>[p(c,{shadow:"hover",class:"notification-card"},{header:f((()=>[u("div",za,[e[23]||(e[23]=u("span",null,"系统通知",-1)),p(k,{type:"link"},{default:f((()=>e[22]||(e[22]=[g("全部已读")]))),_:1})])])),default:f((()=>[!Ba.value&&Ja.value.length>0?(y(),o("div",Sa,[(y(!0),o(v,null,z(Ja.value,((a,e)=>(y(),o("div",{key:e,class:S(["notification-item",{"is-unread":!a.read}])},[p(ee,{"is-dot":"",hidden:a.read,class:"notification-badge"},{default:f((()=>[u("div",La,[u("p",Ia,r(a.title),1),u("p",Pa,r(a.time),1)])])),_:2},1032,["hidden"])],2)))),128))])):Ba.value?(y(),o("div",ja,[p(I,{animated:"",rows:5})])):(y(),T(Da,{key:2,description:"暂无通知"}))])),_:1})])),_:1})])),_:1})],64)):(y(),o("div",Fa,[p(se,{icon:"success",title:"仪表盘加载成功","sub-title":"您正在查看简化版仪表盘，缺少完整菜单结构"},{extra:f((()=>[p(k,{type:"primary",onClick:qa},{default:f((()=>e[24]||(e[24]=[g("进入完整仪表盘")]))),_:1})])),_:1}),p(L,{gutter:20,class:"simple-data-cards"},{default:f((()=>[(y(!0),o(v,null,z(Va,((a,e)=>(y(),T(n,{span:8,key:e},{default:f((()=>[p(c,{shadow:"hover"},{default:f((()=>[u("div",Na,[u("h3",null,r(a.title),1),u("p",$a,r(a.value),1)])])),_:2},1024)])),_:2},1024)))),128))])),_:1})]))])}}},[["__scopeId","data-v-5b1f5a1b"]]);export{Da as default};
