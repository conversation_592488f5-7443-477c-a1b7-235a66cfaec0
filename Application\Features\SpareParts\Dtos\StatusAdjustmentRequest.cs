// File: Application/Features/SpareParts/Dtos/StatusAdjustmentRequest.cs
// Description: 状态调整请求数据传输对象

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Application.Features.SpareParts.Dtos
{
    /// <summary>
    /// 状态调整请求DTO
    /// </summary>
    public class StatusAdjustmentRequest
    {
        /// <summary>
        /// 备件ID
        /// </summary>
        [Required(ErrorMessage = "备件ID不能为空")]
        public long PartId { get; set; }
        
        /// <summary>
        /// 调整项目列表
        /// </summary>
        [Required(ErrorMessage = "调整项目不能为空")]
        public List<StatusAdjustmentItem> Adjustments { get; set; } = new List<StatusAdjustmentItem>();
        
        /// <summary>
        /// 调整原因
        /// </summary>
        [Required(ErrorMessage = "调整原因不能为空")]
        [StringLength(500, ErrorMessage = "调整原因长度不能超过500个字符")]
        public string Reason { get; set; }
        
        /// <summary>
        /// 总数量变化
        /// </summary>
        public int TotalChange { get; set; }
    }

    /// <summary>
    /// 状态调整项目DTO
    /// </summary>
    public class StatusAdjustmentItem
    {
        /// <summary>
        /// 状态ID
        /// </summary>
        [Required(ErrorMessage = "状态ID不能为空")]
        public long StatusId { get; set; }
        
        /// <summary>
        /// 状态代码
        /// </summary>
        public string StatusCode { get; set; }
        
        /// <summary>
        /// 状态名称
        /// </summary>
        public string StatusName { get; set; }
        
        /// <summary>
        /// 调整前数量
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "调整前数量不能为负数")]
        public int FromQuantity { get; set; }
        
        /// <summary>
        /// 调整后数量
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "调整后数量不能为负数")]
        public int ToQuantity { get; set; }
        
        /// <summary>
        /// 变化数量
        /// </summary>
        public int ChangeQuantity { get; set; }
    }

    /// <summary>
    /// 状态调整结果DTO
    /// </summary>
    public class StatusAdjustmentResultDto
    {
        /// <summary>
        /// 调整是否成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 调整记录ID
        /// </summary>
        public long AdjustmentId { get; set; }
        
        /// <summary>
        /// 调整时间
        /// </summary>
        public DateTime AdjustmentTime { get; set; }
        
        /// <summary>
        /// 成功调整的项目数
        /// </summary>
        public int SuccessCount { get; set; }
        
        /// <summary>
        /// 失败的项目数
        /// </summary>
        public int FailureCount { get; set; }
        
        /// <summary>
        /// 错误信息列表
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();
        
        /// <summary>
        /// 调整后的库存汇总
        /// </summary>
        public SparePartStockSummaryDto UpdatedSummary { get; set; }
    }
}
