<template>
  <div class="supplier-list-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>供应商管理</h2>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        新增供应商
      </el-button>
    </div>

    <!-- 搜索筛选区域 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="queryParams" inline>
        <el-form-item label="供应商名称">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入供应商名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="供应商编码">
          <el-input
            v-model="queryParams.code"
            placeholder="请输入供应商编码"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="供应商类型">
          <el-select
            v-model="queryParams.supplierType"
            placeholder="请选择供应商类型"
            clearable
            style="width: 180px"
          >
            <el-option label="采购供应商" :value="1" />
            <el-option label="维修供应商" :value="2" />
            <el-option label="采购+维修供应商" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="queryParams.isActive"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="激活" :value="true" />
            <el-option label="停用" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><RefreshRight /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="supplierList"
        border
        style="width: 100%"
      >
        <el-table-column prop="code" label="供应商编码" width="120" />
        <el-table-column prop="name" label="供应商名称" width="200" />
        <el-table-column prop="supplierTypeDisplay" label="供应商类型" width="150">
          <template #default="scope">
            <el-tag :type="getSupplierTypeTagType(scope.row.supplierType)">
              {{ scope.row.supplierTypeDisplay }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="contactPerson" label="联系人" width="120" />
        <el-table-column prop="contactPhone" label="联系电话" width="140" />
        <el-table-column prop="contactEmail" label="联系邮箱" width="180" />
        <el-table-column prop="address" label="地址" min-width="200" show-overflow-tooltip />
        <el-table-column prop="isActive" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.isActive ? 'success' : 'danger'">
              {{ scope.row.isActive ? '激活' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="160">
          <template #default="scope">
            {{ formatDate(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageIndex"
          v-model:page-size="queryParams.pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogMode === 'create' ? '新增供应商' : '编辑供应商'"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="供应商名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入供应商名称" />
        </el-form-item>
        <el-form-item label="供应商编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入供应商编码" />
        </el-form-item>
        <el-form-item label="供应商类型" prop="supplierType">
          <el-select v-model="form.supplierType" placeholder="请选择供应商类型" style="width: 100%">
            <el-option label="采购供应商" :value="1" />
            <el-option label="维修供应商" :value="2" />
            <el-option label="采购+维修供应商" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="联系人" prop="contactPerson">
          <el-input v-model="form.contactPerson" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系电话" prop="contactPhone">
          <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="联系邮箱" prop="contactEmail">
          <el-input v-model="form.contactEmail" placeholder="请输入联系邮箱" />
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input
            v-model="form.address"
            type="textarea"
            :rows="3"
            placeholder="请输入地址"
          />
        </el-form-item>
        <el-form-item label="备注" prop="notes">
          <el-input
            v-model="form.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
        <el-form-item v-if="dialogMode === 'edit'" label="状态" prop="isActive">
          <el-switch
            v-model="form.isActive"
            active-text="激活"
            inactive-text="停用"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, RefreshRight } from '@element-plus/icons-vue'
import { getSuppliers, deleteSupplier, createSupplier, updateSupplier } from '@/api/spareparts'

// 响应式数据
const loading = ref(false)
const supplierList = ref([])
const total = ref(0)
const dialogVisible = ref(false)
const dialogMode = ref('create') // 'create' | 'edit'
const currentSupplier = ref({})
const formRef = ref()

// 表单数据
const form = reactive({
  name: '',
  code: '',
  supplierType: 1,
  contactPerson: '',
  contactPhone: '',
  contactEmail: '',
  address: '',
  notes: '',
  isActive: true
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入供应商名称', trigger: 'blur' },
    { max: 100, message: '供应商名称长度不能超过100个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入供应商编码', trigger: 'blur' },
    { max: 50, message: '供应商编码长度不能超过50个字符', trigger: 'blur' }
  ],
  supplierType: [
    { required: true, message: '请选择供应商类型', trigger: 'change' }
  ],
  contactPerson: [
    { max: 50, message: '联系人长度不能超过50个字符', trigger: 'blur' }
  ],
  contactPhone: [
    { max: 20, message: '联系电话长度不能超过20个字符', trigger: 'blur' }
  ],
  contactEmail: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
    { max: 100, message: '联系邮箱长度不能超过100个字符', trigger: 'blur' }
  ],
  address: [
    { max: 200, message: '地址长度不能超过200个字符', trigger: 'blur' }
  ],
  notes: [
    { max: 500, message: '备注长度不能超过500个字符', trigger: 'blur' }
  ]
}

// 查询参数
const queryParams = reactive({
  pageIndex: 1,
  pageSize: 10,
  name: '',
  code: '',
  supplierType: null,
  isActive: null
})

// 方法
const fetchSupplierList = async () => {
  loading.value = true
  try {
    const response = await getSuppliers(queryParams)
    if (response.success) {
      supplierList.value = response.data.items || []
      total.value = response.data.totalCount || 0
    } else {
      ElMessage.error(response.message || '获取供应商列表失败')
    }
  } catch (error) {
    console.error('获取供应商列表失败:', error)
    ElMessage.error('获取供应商列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  queryParams.pageIndex = 1
  fetchSupplierList()
}

const handleReset = () => {
  Object.assign(queryParams, {
    pageIndex: 1,
    pageSize: 10,
    name: '',
    code: '',
    supplierType: null,
    isActive: null
  })
  fetchSupplierList()
}

const handleAdd = () => {
  dialogMode.value = 'create'
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row) => {
  dialogMode.value = 'edit'
  currentSupplier.value = { ...row }
  Object.assign(form, {
    name: row.name,
    code: row.code,
    supplierType: row.supplierType,
    contactPerson: row.contactPerson || '',
    contactPhone: row.contactPhone || '',
    contactEmail: row.contactEmail || '',
    address: row.address || '',
    notes: row.notes || '',
    isActive: row.isActive
  })
  dialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除供应商"${row.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await deleteSupplier(row.id)
    if (response.success) {
      ElMessage.success('删除成功')
      fetchSupplierList()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除供应商失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleSizeChange = (size) => {
  queryParams.pageSize = size
  queryParams.pageIndex = 1
  fetchSupplierList()
}

const handleCurrentChange = (page) => {
  queryParams.pageIndex = page
  fetchSupplierList()
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    let response
    if (dialogMode.value === 'create') {
      response = await createSupplier(form)
    } else {
      response = await updateSupplier(currentSupplier.value.id, form)
    }

    if (response.success) {
      ElMessage.success(dialogMode.value === 'create' ? '创建成功' : '更新成功')
      dialogVisible.value = false
      fetchSupplierList()
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('提交表单失败:', error)
    ElMessage.error('操作失败')
  }
}

const handleDialogClose = () => {
  resetForm()
}

const resetForm = () => {
  Object.assign(form, {
    name: '',
    code: '',
    supplierType: 1,
    contactPerson: '',
    contactPhone: '',
    contactEmail: '',
    address: '',
    notes: '',
    isActive: true
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 工具方法
const getSupplierTypeTagType = (type) => {
  switch (type) {
    case 1: return 'primary'
    case 2: return 'success'
    case 3: return 'warning'
    default: return 'info'
  }
}

const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  fetchSupplierList()
})
</script>

<style scoped>
.supplier-list-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
