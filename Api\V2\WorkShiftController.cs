// File: Api/V2/WorkShiftController.cs
// Description: 班次管理API控制器

using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.Tasks.Services;
using ItAssetsSystem.Application.Features.Tasks.Dtos;

namespace ItAssetsSystem.Api.V2
{
    /// <summary>
    /// 班次管理API控制器
    /// </summary>
    [ApiController]
    [Route("api/v2/work-shifts")]
    [Authorize]
    public class WorkShiftController : ControllerBase
    {
        private readonly WorkShiftService _workShiftService;
        private readonly TaskClaimService _taskClaimService;
        private readonly ILogger<WorkShiftController> _logger;

        public WorkShiftController(
            WorkShiftService workShiftService,
            TaskClaimService taskClaimService,
            ILogger<WorkShiftController> logger)
        {
            _workShiftService = workShiftService;
            _taskClaimService = taskClaimService;
            _logger = logger;
        }

        /// <summary>
        /// 获取当前用户ID
        /// </summary>
        /// <returns>当前用户ID</returns>
        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? User.FindFirst("uid")?.Value;
            if (int.TryParse(userIdClaim, out var userId))
            {
                return userId;
            }
            _logger.LogWarning("Unable to parse UserID from token claims");
            return 1; // 默认返回1用于测试
        }

        /// <summary>
        /// 获取所有班次
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<ApiResponse<List<WorkShiftDto>>>> GetAllShifts()
        {
            var result = await _workShiftService.GetAllShiftsAsync();
            return Ok(result);
        }

        /// <summary>
        /// 创建班次
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<ApiResponse<WorkShiftDto>>> CreateShift([FromBody] CreateWorkShiftDto request)
        {
            var currentUserId = GetCurrentUserId();
            var result = await _workShiftService.CreateShiftAsync(request, currentUserId);
            return Ok(result);
        }

        /// <summary>
        /// 分配用户到班次
        /// </summary>
        [HttpPost("assignments")]
        public async Task<ActionResult<ApiResponse<UserShiftAssignmentDto>>> AssignUserToShift([FromBody] CreateUserShiftAssignmentDto request)
        {
            var currentUserId = GetCurrentUserId();
            var result = await _workShiftService.AssignUserToShiftAsync(request, currentUserId);
            return Ok(result);
        }

        /// <summary>
        /// 获取班次分配的用户列表
        /// </summary>
        [HttpGet("{shiftId}/assignments")]
        public async Task<ActionResult<ApiResponse<List<UserShiftAssignmentDto>>>> GetShiftAssignments(long shiftId)
        {
            var result = await _workShiftService.GetShiftAssignmentsAsync(shiftId);
            return Ok(result);
        }

        /// <summary>
        /// 移除用户班次分配
        /// </summary>
        [HttpDelete("assignments/{assignmentId}")]
        public async Task<ActionResult<ApiResponse<object>>> RemoveUserShiftAssignment(long assignmentId)
        {
            var currentUserId = GetCurrentUserId();
            var result = await _workShiftService.RemoveUserShiftAssignmentAsync(assignmentId, currentUserId);
            return Ok(result);
        }

        /// <summary>
        /// 获取用户当前班次
        /// </summary>
        [HttpGet("current")]
        public async Task<ActionResult<ApiResponse<WorkShiftDto?>>> GetUserCurrentShift()
        {
            var currentUserId = GetCurrentUserId();
            var result = await _workShiftService.GetUserCurrentShiftAsync(currentUserId);
            return Ok(result);
        }

        /// <summary>
        /// 获取指定用户的当前班次
        /// </summary>
        [HttpGet("users/{userId}/current")]
        public async Task<ActionResult<ApiResponse<WorkShiftDto?>>> GetUserCurrentShift(int userId)
        {
            var result = await _workShiftService.GetUserCurrentShiftAsync(userId);
            return Ok(result);
        }

        /// <summary>
        /// 领取任务
        /// </summary>
        [HttpPost("claim-task")]
        public async Task<ActionResult<ApiResponse<TaskClaimDto>>> ClaimTask([FromBody] CreateTaskClaimDto request)
        {
            var currentUserId = GetCurrentUserId();
            var result = await _taskClaimService.ClaimTaskAsync(request, currentUserId);
            return Ok(result);
        }

        /// <summary>
        /// 更新任务领取状态
        /// </summary>
        [HttpPut("claims/{claimId}/status")]
        public async Task<ActionResult<ApiResponse<TaskClaimDto>>> UpdateClaimStatus(long claimId, [FromBody] UpdateTaskClaimStatusDto request)
        {
            var currentUserId = GetCurrentUserId();
            var result = await _taskClaimService.UpdateClaimStatusAsync(claimId, request, currentUserId);
            return Ok(result);
        }

        /// <summary>
        /// 获取用户今日任务领取记录
        /// </summary>
        [HttpGet("claims/today")]
        public async Task<ActionResult<ApiResponse<List<TaskClaimDto>>>> GetUserTodayClaims()
        {
            var currentUserId = GetCurrentUserId();
            var result = await _taskClaimService.GetUserTodayClaimsAsync(currentUserId);
            return Ok(result);
        }

        /// <summary>
        /// 获取指定用户今日任务领取记录
        /// </summary>
        [HttpGet("users/{userId}/claims/today")]
        public async Task<ActionResult<ApiResponse<List<TaskClaimDto>>>> GetUserTodayClaims(int userId)
        {
            var result = await _taskClaimService.GetUserTodayClaimsAsync(userId);
            return Ok(result);
        }

        /// <summary>
        /// 获取今日班次任务统计
        /// </summary>
        [HttpGet("statistics/today")]
        public async Task<ActionResult<ApiResponse<List<TaskClaimStatisticsDto>>>> GetTodayShiftStatistics([FromQuery] ShiftTaskStatisticsQueryDto query)
        {
            var result = await _taskClaimService.GetTodayShiftStatisticsAsync(query);
            return Ok(result);
        }
    }
}
