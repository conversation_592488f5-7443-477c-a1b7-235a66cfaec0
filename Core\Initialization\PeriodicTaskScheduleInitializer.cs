// File: Core/Initialization/PeriodicTaskScheduleInitializer.cs
// Description: 周期性任务计划表初始化和修复工具

using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using MySqlConnector;

namespace ItAssetsSystem.Core.Initialization
{
    /// <summary>
    /// 周期性任务计划表初始化器，用于检查和修复周期性任务计划表结构
    /// </summary>
    public static class PeriodicTaskScheduleInitializer
    {
        /// <summary>
        /// 初始化并修复周期性任务计划表结构
        /// </summary>
        public static async Task InitializePeriodicTaskScheduleTableAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<AppDbContext>>();
            var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            logger.LogInformation("检查周期性任务计划表结构");

            try
            {
                // 检查PeriodicTaskSchedules表是否存在（使用Pascal命名）
                bool tableExists = await CheckTableExistsAsync(dbContext, "PeriodicTaskSchedules");

                if (!tableExists)
                {
                    logger.LogWarning("周期性任务计划表不存在，将创建表结构");
                    await CreatePeriodicTaskScheduleTableAsync(dbContext);
                    logger.LogInformation("成功创建周期性任务计划表");
                }
                else
                {
                    logger.LogInformation("周期性任务计划表已存在，检查表结构");

                    // 检查关键列是否存在
                    bool hasCorrectStructure = await ValidateTableStructureAsync(dbContext);

                    if (!hasCorrectStructure)
                    {
                        logger.LogWarning("周期性任务计划表结构不匹配，将重新创建表");
                        await RecreatePeriodicTaskScheduleTableAsync(dbContext, logger);
                        logger.LogInformation("成功重新创建周期性任务计划表");
                    }
                    else
                    {
                        logger.LogInformation("周期性任务计划表结构正常");
                    }
                }

                // 检查并创建关联表
                await EnsureAssigneeTableExistsAsync(dbContext, logger);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "检查/修复周期性任务计划表结构失败");
            }
        }

        /// <summary>
        /// 检查表是否存在
        /// </summary>
        private static async Task<bool> CheckTableExistsAsync(AppDbContext dbContext, string tableName)
        {
            string sql = @"
                SELECT COUNT(*)
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_SCHEMA = @schema
                  AND TABLE_NAME = @tableName";

            var connectionString = dbContext.Database.GetConnectionString();
            var builder = new MySqlConnectionStringBuilder(connectionString);
            string dbName = builder.Database;

            var parameters = new[]
            {
                new MySqlParameter("@schema", dbName),
                new MySqlParameter("@tableName", tableName)
            };

            var result = await dbContext.Database.ExecuteSqlRawAsync(
                "SELECT @result := (" + sql + ")",
                parameters);

            // 使用更简单的方法检查表是否存在
            try
            {
                await dbContext.Database.ExecuteSqlRawAsync($"SELECT 1 FROM `{tableName}` LIMIT 1");
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证表结构是否正确
        /// </summary>
        private static async Task<bool> ValidateTableStructureAsync(AppDbContext dbContext)
        {
            try
            {
                // 简化验证：直接尝试查询表，如果失败说明结构不正确
                await dbContext.Database.ExecuteSqlRawAsync(
                    "SELECT PeriodicTaskScheduleId, TemplateTaskId, CreatorUserId, Name FROM PeriodicTaskSchedules LIMIT 1");
                return true;
            }
            catch
            {
                // 如果查询失败，说明列名不匹配，需要重新创建表
                return false;
            }
        }

        /// <summary>
        /// 创建周期性任务计划表
        /// </summary>
        private static async Task CreatePeriodicTaskScheduleTableAsync(AppDbContext dbContext)
        {
            string createTableSql = @"
                CREATE TABLE IF NOT EXISTS `PeriodicTaskSchedules` (
                  `PeriodicTaskScheduleId` bigint NOT NULL AUTO_INCREMENT,
                  `TemplateTaskId` bigint NOT NULL,
                  `CreatorUserId` int NOT NULL,
                  `Name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
                  `Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
                  `RecurrenceType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Daily',
                  `RecurrenceInterval` int NOT NULL DEFAULT 1,
                  `DaysOfWeek` json NULL,
                  `DayOfMonth` int NULL DEFAULT NULL,
                  `WeekOfMonth` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
                  `DayOfWeekForMonth` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
                  `MonthOfYear` int NULL DEFAULT NULL,
                  `CronExpression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
                  `StartDate` datetime(6) NOT NULL,
                  `EndConditionType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Never',
                  `EndDate` datetime(6) NULL DEFAULT NULL,
                  `TotalOccurrences` int NULL DEFAULT NULL,
                  `OccurrencesGenerated` int NOT NULL DEFAULT 0,
                  `NextGenerationTime` datetime(6) NOT NULL,
                  `Status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Active',
                  `LastGeneratedTimestamp` datetime(6) NULL DEFAULT NULL,
                  `LastError` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
                  `DefaultPoints` int NOT NULL DEFAULT 0,
                  `CreationTimestamp` datetime(6) NOT NULL,
                  `LastUpdatedTimestamp` datetime(6) NOT NULL,
                  PRIMARY KEY (`PeriodicTaskScheduleId`) USING BTREE,
                  INDEX `IX_PeriodicTaskSchedules_CreatorUserId`(`CreatorUserId`) USING BTREE,
                  INDEX `IX_PeriodicTaskSchedules_TemplateTaskId`(`TemplateTaskId`) USING BTREE
                ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;";

            await dbContext.Database.ExecuteSqlRawAsync(createTableSql);
        }

        /// <summary>
        /// 重新创建周期性任务计划表
        /// </summary>
        private static async Task RecreatePeriodicTaskScheduleTableAsync(AppDbContext dbContext, ILogger logger)
        {
            try
            {
                // 删除关联表
                logger.LogInformation("删除周期性任务计划关联表");
                await dbContext.Database.ExecuteSqlRawAsync("DROP TABLE IF EXISTS `PeriodicTaskScheduleAssignees`");

                // 删除主表
                logger.LogInformation("删除旧的周期性任务计划表");
                await dbContext.Database.ExecuteSqlRawAsync("DROP TABLE IF EXISTS `PeriodicTaskSchedules`");

                // 创建新表
                logger.LogInformation("创建新的周期性任务计划表");
                await CreatePeriodicTaskScheduleTableAsync(dbContext);

                // 重新创建关联表
                logger.LogInformation("重新创建周期性任务计划关联表");
                await EnsureAssigneeTableExistsAsync(dbContext, logger);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "重新创建周期性任务计划表失败");
                throw;
            }
        }

        /// <summary>
        /// 修复表结构
        /// </summary>
        private static async Task FixTableStructureAsync(AppDbContext dbContext)
        {
            // 这里可以添加具体的表结构修复逻辑
            // 比如添加缺失的列、修改列类型等
            // 目前先简单处理
        }

        /// <summary>
        /// 确保负责人关联表存在
        /// </summary>
        private static async Task EnsureAssigneeTableExistsAsync(AppDbContext dbContext, ILogger logger)
        {
            try
            {
                bool assigneeTableExists = await CheckTableExistsAsync(dbContext, "PeriodicTaskScheduleAssignees");

                if (!assigneeTableExists)
                {
                    logger.LogInformation("创建周期性任务计划负责人关联表");

                    string createAssigneeTableSql = @"
                        CREATE TABLE IF NOT EXISTS `PeriodicTaskScheduleAssignees` (
                          `Id` bigint NOT NULL AUTO_INCREMENT,
                          `PeriodicTaskScheduleId` bigint NOT NULL,
                          `UserId` int NOT NULL,
                          `CreatedAt` datetime(6) NOT NULL,
                          PRIMARY KEY (`Id`) USING BTREE,
                          UNIQUE INDEX `uk_schedule_user`(`PeriodicTaskScheduleId`, `UserId`) USING BTREE,
                          INDEX `idx_schedule_id`(`PeriodicTaskScheduleId`) USING BTREE,
                          INDEX `idx_user_id`(`UserId`) USING BTREE
                        ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;";

                    await dbContext.Database.ExecuteSqlRawAsync(createAssigneeTableSql);
                    logger.LogInformation("成功创建周期性任务计划负责人关联表");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "创建周期性任务计划负责人关联表失败");
            }
        }
    }
}
