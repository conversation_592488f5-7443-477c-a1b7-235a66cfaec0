/**
 * ECharts 全局配置和优化 - 最终修复版
 * 
 * 本文件包含了对原始代码的修改，旨在：
 * 1. 彻底解决 non-passive 事件监听器警告，且不破坏 ECharts 的滚轮缩放(dataZoom)功能。
 * 2. 移除冗余和有风险的代码（如“猴子补丁”），使代码更稳定、更易于维护。
 * 3. 保留了原始的 `createOptimizedChart` 函数签名和默认导出结构。
 */

import * as echarts from 'echarts';

// --- [核心修复] 开始：替换了原始的有问题的 addEventListener 重写 ---
// 这个新版本更智能，它只在需要时才添加 passive:true，并且会尊重 ECharts 的原始设置。

// 确保代码只在浏览器环境中执行，避免在 Node.js (SSR) 环境下出错。
if (typeof window !== 'undefined' && typeof EventTarget !== 'undefined') {

  // 保存原始的 addEventListener 方法，以便后续调用。
  const originalAddEventListener = EventTarget.prototype.addEventListener;
  
  // 检测浏览器是否支持 'passive' 选项。
  let passiveSupported = false;
  try {
    const options = {
      get passive() {
        passiveSupported = true;
        return false;
      }
    };
    window.addEventListener('testPassive', null, options);
    window.removeEventListener('testPassive', null, options);
  } catch (err) {
    passiveSupported = false;
  }

  /**
   * 重写全局的 addEventListener 方法。
   */
  EventTarget.prototype.addEventListener = function (type, listener, options) {
    // 场景 1: 如果调用者已经显式地提供了 passive 选项（无论是 true 还是 false），
    // 我们完全尊重原始意图，直接调用原始方法。
    if (options && typeof options === 'object' && typeof options.passive !== 'undefined') {
      return originalAddEventListener.call(this, type, listener, options);
    }

    // 场景 2: 对于会阻塞页面滚动的事件，如果浏览器支持 passive，并且调用者没有指定 passive，
    // 我们默认添加 `passive: true` 来优化性能。
    const scrollBlockingEvents = ['wheel', 'mousewheel', 'touchstart', 'touchmove'];
    if (passiveSupported && scrollBlockingEvents.includes(type)) {
      const newOptions = typeof options === 'boolean' ? { capture: options, passive: true } : (options || {});
      newOptions.passive = true;
      
      return originalAddEventListener.call(this, type, listener, newOptions);
    }

    // 场景 3: 对于所有其他不相关的情况，保持原始行为不变。
    return originalAddEventListener.call(this, type, listener, options);
  };
}
// --- [核心修复] 结束 ---


/**
 * 创建优化的 ECharts 实例 (已简化)
 * 移除了不必要且无效的 setTimeout 和 optimizeChartEvents 调用。
 * @param {HTMLElement} container - 容器元素
 * @param {string} theme - 主题
 * @param {Object} opts - 选项
 * @returns {Object} ECharts 实例
 */
export function createOptimizedChart(container, theme = null, opts = {}) {
  if (!container) {
    console.error('ECharts initialization failed: The provided DOM element is null or undefined.');
    return null;
  }
  
  // 默认选项，保留了原始逻辑
  const defaultOpts = {
    renderer: 'canvas', // 性能较好。如果追求更高清晰度，可以考虑改为 'svg'
    useDirtyRect: false, // ECharts 5 新特性，按需开启
    ...opts
  };
  
  // 创建图表实例，现在函数体非常纯净
  const chart = echarts.init(container, theme, defaultOpts);
  
  return chart;
}

// --- [已移除] ---
// 以下函数 (optimizeChartEvents, addPassiveWheelListener, disableEChartsWheelZoom, 
// restoreOriginalEventListener, initEChartsOptimization) 
// 已被移除，因为它们在新方案中是多余的、无效的或有风险的。
// 全局的 addEventListener 优化在文件被导入时自动生效，不再需要手动初始化。


// 保留原始的默认导出，确保项目其他地方的 `import createOptimizedChart from ...` 能正常工作。
export default createOptimizedChart;