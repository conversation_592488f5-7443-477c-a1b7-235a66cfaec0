<template>
  <div class="debug-container">
    <h1>资产统计API调试页面</h1>
    
    <div class="section">
      <h2>🔐 认证状态</h2>
      <p>Token存在: {{ hasToken ? '是' : '否' }}</p>
      <p>用户信息: {{ userInfo }}</p>
    </div>

    <div class="section">
      <h2>📊 API调用测试</h2>
      <el-button @click="testOverallStats" :loading="loading">测试总体统计</el-button>
      <el-button @click="testTypeStats" :loading="loading">测试类型统计</el-button>
      <el-button @click="testAllApis" :loading="loading">测试所有API</el-button>
    </div>

    <div class="section">
      <h2>📋 调用结果</h2>
      <div class="result-display">
        <h3>总体统计数据：</h3>
        <pre>{{ JSON.stringify(overallStats, null, 2) }}</pre>
        
        <h3>类型统计数据：</h3>
        <pre>{{ JSON.stringify(typeStats, null, 2) }}</pre>
        
        <h3>错误信息：</h3>
        <pre v-if="errors.length">{{ errors.join('\n') }}</pre>
        <p v-else>无错误</p>
      </div>
    </div>

    <div class="section">
      <h2>🌐 网络请求日志</h2>
      <div class="logs">
        <div v-for="(log, index) in requestLogs" :key="index" class="log-item">
          <strong>{{ log.method }} {{ log.url }}</strong>
          <p>状态: {{ log.status }}</p>
          <p>响应时间: {{ log.time }}ms</p>
          <details>
            <summary>详细信息</summary>
            <pre>{{ JSON.stringify(log.data, null, 2) }}</pre>
          </details>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElButton } from 'element-plus'
import assetStatisticsApi from '@/api/assetStatistics'
import { getToken } from '@/utils/auth'
import { useUserStore } from '@/stores/modules/user'

const loading = ref(false)
const overallStats = ref({})
const typeStats = ref([])
const errors = ref([])
const requestLogs = ref([])

const userStore = useUserStore()

const hasToken = computed(() => !!getToken())
const userInfo = computed(() => userStore.userInfo)

// 添加请求日志
const addRequestLog = (method, url, status, time, data) => {
  requestLogs.value.unshift({
    method,
    url, 
    status,
    time,
    data,
    timestamp: new Date().toLocaleTimeString()
  })
  
  // 只保留最近10条日志
  if (requestLogs.value.length > 10) {
    requestLogs.value.pop()
  }
}

// 测试总体统计
const testOverallStats = async () => {
  loading.value = true
  errors.value = []
  
  try {
    console.log('🔍 开始测试总体统计API...')
    const startTime = Date.now()
    
    const data = await assetStatisticsApi.getOverallStatistics()
    const endTime = Date.now()
    
    console.log('✅ 总体统计API响应:', data)
    overallStats.value = data
    
    addRequestLog('GET', '/v2/asset-statistics/overall', 200, endTime - startTime, data)
    ElMessage.success('总体统计获取成功')
    
  } catch (error) {
    console.error('❌ 总体统计API失败:', error)
    errors.value.push(`总体统计失败: ${error.message}`)
    
    addRequestLog('GET', '/v2/asset-statistics/overall', error.response?.status || 'ERROR', 0, {
      error: error.message,
      response: error.response?.data
    })
    
    ElMessage.error('总体统计获取失败')
  } finally {
    loading.value = false
  }
}

// 测试类型统计  
const testTypeStats = async () => {
  loading.value = true
  errors.value = []
  
  try {
    console.log('🔍 开始测试类型统计API...')
    const startTime = Date.now()
    
    const data = await assetStatisticsApi.getStatisticsByType()
    const endTime = Date.now()
    
    console.log('✅ 类型统计API响应:', data)
    typeStats.value = data
    
    addRequestLog('GET', '/v2/asset-statistics/by-type', 200, endTime - startTime, data)
    ElMessage.success('类型统计获取成功')
    
  } catch (error) {
    console.error('❌ 类型统计API失败:', error)
    errors.value.push(`类型统计失败: ${error.message}`)
    
    addRequestLog('GET', '/v2/asset-statistics/by-type', error.response?.status || 'ERROR', 0, {
      error: error.message,
      response: error.response?.data
    })
    
    ElMessage.error('类型统计获取失败')
  } finally {
    loading.value = false
  }
}

// 测试所有API
const testAllApis = async () => {
  loading.value = true
  errors.value = []
  
  const apis = [
    { name: '总体统计', fn: () => assetStatisticsApi.getOverallStatistics() },
    { name: '类型统计', fn: () => assetStatisticsApi.getStatisticsByType() },
    { name: '区域统计', fn: () => assetStatisticsApi.getStatisticsByRegion() },
    { name: '部门统计', fn: () => assetStatisticsApi.getStatisticsByDepartment() },
    { name: '资产类型列表', fn: () => assetStatisticsApi.getAssetTypes() },
    { name: '区域列表', fn: () => assetStatisticsApi.getRegions() },
    { name: '部门列表', fn: () => assetStatisticsApi.getDepartments() }
  ]
  
  let successCount = 0
  
  for (const api of apis) {
    try {
      console.log(`🔍 测试 ${api.name}...`)
      const startTime = Date.now()
      
      const data = await api.fn()
      const endTime = Date.now()
      
      console.log(`✅ ${api.name} 成功:`, data)
      successCount++
      
      addRequestLog('GET', `API-${api.name}`, 200, endTime - startTime, data)
      
    } catch (error) {
      console.error(`❌ ${api.name} 失败:`, error)
      errors.value.push(`${api.name}失败: ${error.message}`)
      
      addRequestLog('GET', `API-${api.name}`, error.response?.status || 'ERROR', 0, {
        error: error.message,
        response: error.response?.data
      })
    }
  }
  
  loading.value = false
  
  if (successCount === apis.length) {
    ElMessage.success(`所有API测试完成，${successCount}/${apis.length} 成功`)
  } else {
    ElMessage.warning(`API测试完成，${successCount}/${apis.length} 成功`)
  }
}

onMounted(() => {
  console.log('🔍 资产统计调试页面已加载')
  console.log('Token状态:', hasToken.value)
  console.log('用户信息:', userInfo.value)
})
</script>

<style scoped>
.debug-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #fff;
}

.result-display {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  max-height: 400px;
  overflow-y: auto;
}

.result-display pre {
  background: #e9ecef;
  padding: 10px;
  border-radius: 3px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.logs {
  max-height: 500px;
  overflow-y: auto;
}

.log-item {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #dee2e6;
  border-radius: 5px;
  background: #f8f9fa;
}

.log-item strong {
  color: #007bff;
}

.log-item details {
  margin-top: 10px;
}

.log-item pre {
  background: #e9ecef;
  padding: 8px;
  border-radius: 3px;
  font-size: 11px;
  max-height: 150px;
  overflow-y: auto;
}
</style>