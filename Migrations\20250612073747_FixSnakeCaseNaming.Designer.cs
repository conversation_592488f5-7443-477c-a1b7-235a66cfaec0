﻿// <auto-generated />
using System;
using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace ItAssetsSystem.Migrations
{
    [DbContext(typeof(AppDbContext))]
    [Migration("20250612073747_FixSnakeCaseNaming")]
    partial class FixSnakeCaseNaming
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.7")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.AssetSnapshot", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("Id");

                    b.Property<string>("AssetCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("AssetCode");

                    b.Property<int>("AssetId")
                        .HasColumnType("int")
                        .HasColumnName("AssetId");

                    b.Property<string>("AssetName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)")
                        .HasColumnName("AssetName");

                    b.Property<int>("AssetTypeId")
                        .HasColumnType("int")
                        .HasColumnName("AssetTypeId");

                    b.Property<int?>("DepartmentId")
                        .HasColumnType("int")
                        .HasColumnName("DepartmentId");

                    b.Property<string>("FinancialCode")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("FinancialCode");

                    b.Property<int?>("LocationId")
                        .HasColumnType("int")
                        .HasColumnName("LocationId");

                    b.Property<decimal?>("Price")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Price");

                    b.Property<DateTime?>("PurchaseDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("PurchaseDate");

                    b.Property<DateTime>("SnapshotDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("SnapshotDate");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("Status");

                    b.HasKey("Id")
                        .HasName("pk_asset_snapshots");

                    b.HasIndex("AssetId")
                        .HasDatabaseName("IX_AssetSnapshots_AssetId");

                    b.HasIndex("SnapshotDate")
                        .HasDatabaseName("IX_AssetSnapshots_SnapshotDate");

                    b.HasIndex("SnapshotDate", "AssetId")
                        .IsUnique()
                        .HasDatabaseName("UK_AssetSnapshots_Date_AssetId");

                    b.ToTable("AssetSnapshots", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Notes.QuickMemo", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(255)")
                        .HasColumnName("id");

                    b.Property<string>("CategoryId")
                        .HasColumnType("varchar(255)")
                        .HasColumnName("category_id");

                    b.Property<string>("Color")
                        .HasMaxLength(7)
                        .HasColumnType("varchar(7)")
                        .HasColumnName("color");

                    b.Property<string>("Content")
                        .HasColumnType("TEXT")
                        .HasColumnName("content");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<bool>("IsPinned")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_pinned");

                    b.Property<int>("PositionXDb")
                        .HasColumnType("int")
                        .HasColumnName("PositionX");

                    b.Property<int>("PositionYDb")
                        .HasColumnType("int")
                        .HasColumnName("PositionY");

                    b.Property<int>("SizeHeight")
                        .HasColumnType("int")
                        .HasColumnName("SizeHeight");

                    b.Property<int>("SizeWidth")
                        .HasColumnType("int")
                        .HasColumnName("SizeWidth");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)")
                        .HasColumnName("title");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.Property<int>("ZIndex")
                        .HasColumnType("int")
                        .HasColumnName("ZIndex");

                    b.HasKey("Id")
                        .HasName("pk_quick_memos");

                    b.HasIndex("CategoryId")
                        .HasDatabaseName("ix_quick_memos_category_id");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_quick_memos_user_id");

                    b.ToTable("quick_memos", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Notes.QuickMemoCategory", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("varchar(255)")
                        .HasColumnName("id");

                    b.Property<string>("Color")
                        .HasMaxLength(7)
                        .HasColumnType("varchar(7)")
                        .HasColumnName("color");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("name");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_quick_memo_categories");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_quick_memo_categories_user_id");

                    b.ToTable("quick_memo_categories", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Notification", b =>
                {
                    b.Property<long>("NotificationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("notification_id");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("content");

                    b.Property<DateTime>("CreationTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("creation_timestamp");

                    b.Property<bool>("IsRead")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_read");

                    b.Property<DateTime?>("ReadTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("read_timestamp");

                    b.Property<long?>("ReferenceId")
                        .HasColumnType("bigint")
                        .HasColumnName("reference_id");

                    b.Property<string>("ReferenceType")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("reference_type");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasColumnName("title");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("type");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.HasKey("NotificationId")
                        .HasName("pk_notifications");

                    b.ToTable("notifications", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.RepairItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("Id");

                    b.Property<int>("AssetId")
                        .HasColumnType("int")
                        .HasColumnName("AssetId");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasColumnName("Description");

                    b.Property<int?>("FaultRecordId")
                        .HasColumnType("int")
                        .HasColumnName("FaultRecordId");

                    b.Property<decimal>("RepairCost")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("RepairCost");

                    b.Property<int>("RepairOrderId")
                        .HasColumnType("int")
                        .HasColumnName("RepairOrderId");

                    b.Property<string>("RepairResult")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasColumnName("RepairResult");

                    b.Property<int>("RepairStatus")
                        .HasColumnType("int")
                        .HasColumnName("RepairStatus");

                    b.HasKey("Id")
                        .HasName("pk_repair_items");

                    b.HasIndex("AssetId")
                        .HasDatabaseName("IX_RepairItems_AssetId");

                    b.HasIndex("FaultRecordId")
                        .HasDatabaseName("IX_RepairItems_FaultRecordId");

                    b.HasIndex("RepairOrderId")
                        .HasDatabaseName("IX_RepairItems_RepairOrderId");

                    b.HasIndex("RepairStatus")
                        .HasDatabaseName("IX_RepairItems_RepairStatus");

                    b.ToTable("RepairItems", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.RepairOrder", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("Id");

                    b.Property<DateTime?>("ActualReturnDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("ActualReturnDate");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreatedAt");

                    b.Property<int>("CreatorId")
                        .HasColumnType("int")
                        .HasColumnName("creator_id");

                    b.Property<DateTime?>("ExpectedReturnDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("ExpectedReturnDate");

                    b.Property<string>("Notes")
                        .HasColumnType("longtext")
                        .HasColumnName("notes");

                    b.Property<string>("OrderCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("OrderCode");

                    b.Property<DateTime?>("SendDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("SendDate");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("Status");

                    b.Property<int>("SupplierId")
                        .HasColumnType("int")
                        .HasColumnName("SupplierId");

                    b.Property<decimal>("TotalCost")
                        .HasColumnType("decimal(65,30)")
                        .HasColumnName("total_cost");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("Id")
                        .HasName("pk_repair_orders");

                    b.HasIndex("CreatorId")
                        .HasDatabaseName("ix_repair_orders_creator_id");

                    b.HasIndex("OrderCode")
                        .IsUnique()
                        .HasDatabaseName("IX_RepairOrders_OrderCode");

                    b.HasIndex("SendDate")
                        .HasDatabaseName("IX_RepairOrders_SendDate");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_RepairOrders_Status");

                    b.HasIndex("SupplierId")
                        .HasDatabaseName("IX_RepairOrders_SupplierId");

                    b.ToTable("RepairOrders", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.SparePart", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("Brand")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("brand");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<long?>("LocationId")
                        .HasColumnType("bigint")
                        .HasColumnName("location_id");

                    b.Property<string>("MaterialNumber")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("material_number");

                    b.Property<int>("MinStock")
                        .HasColumnType("int")
                        .HasColumnName("min_threshold");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("name");

                    b.Property<decimal?>("Price")
                        .HasPrecision(10, 2)
                        .HasColumnType("decimal(10,2)")
                        .HasColumnName("purchase_price");

                    b.Property<string>("Remarks")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasColumnName("notes");

                    b.Property<string>("Specification")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)")
                        .HasColumnName("spec");

                    b.Property<int>("StockQuantity")
                        .HasColumnType("int")
                        .HasColumnName("quantity");

                    b.Property<long>("TypeId")
                        .HasColumnType("bigint")
                        .HasColumnName("type_id");

                    b.Property<string>("Unit")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)")
                        .HasColumnName("unit");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.Property<int>("WarningThreshold")
                        .HasColumnType("int")
                        .HasColumnName("warning_threshold");

                    b.HasKey("Id")
                        .HasName("pk_spare_parts");

                    b.HasIndex("LocationId")
                        .HasDatabaseName("ix_spare_parts_location_id");

                    b.HasIndex("TypeId")
                        .HasDatabaseName("ix_spare_parts_type_id");

                    b.ToTable("spare_parts", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.SparePartLocation", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("Area")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)")
                        .HasColumnName("area");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)")
                        .HasColumnName("description");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_active");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("name");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_spare_part_locations");

                    b.ToTable("spare_part_locations", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.SparePartTransaction", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("BatchNumber")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("batch_number");

                    b.Property<bool>("IsSystemGenerated")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_system_generated");

                    b.Property<long>("LocationId")
                        .HasColumnType("bigint")
                        .HasColumnName("location_id");

                    b.Property<DateTime>("OperationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("transaction_time");

                    b.Property<int>("OperatorUserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.Property<long>("PartId")
                        .HasColumnType("bigint")
                        .HasColumnName("part_id");

                    b.Property<int>("Quantity")
                        .HasColumnType("int")
                        .HasColumnName("quantity");

                    b.Property<string>("Reason")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasColumnName("reason");

                    b.Property<byte>("ReasonType")
                        .HasColumnType("tinyint unsigned")
                        .HasColumnName("reason_type");

                    b.Property<string>("ReferenceNumber")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("reference");

                    b.Property<int?>("RelatedAssetId")
                        .HasColumnType("int")
                        .HasColumnName("related_asset_id");

                    b.Property<int?>("RelatedFaultId")
                        .HasColumnType("int")
                        .HasColumnName("related_fault_id");

                    b.Property<int>("StockAfter")
                        .HasColumnType("int")
                        .HasColumnName("stock_after");

                    b.Property<byte>("Type")
                        .HasColumnType("tinyint unsigned")
                        .HasColumnName("type");

                    b.HasKey("Id")
                        .HasName("pk_spare_part_transactions");

                    b.HasIndex("LocationId")
                        .HasDatabaseName("ix_spare_part_transactions_location_id");

                    b.HasIndex("PartId")
                        .HasDatabaseName("ix_spare_part_transactions_part_id");

                    b.ToTable("spare_part_transactions", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.SparePartType", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)")
                        .HasColumnName("description");

                    b.Property<int>("Level")
                        .HasColumnType("int")
                        .HasColumnName("level");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("name");

                    b.Property<long?>("ParentId")
                        .HasColumnType("bigint")
                        .HasColumnName("parent_id");

                    b.Property<string>("Path")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasColumnName("path");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_spare_part_types");

                    b.HasIndex("ParentId")
                        .HasDatabaseName("ix_spare_part_types_parent_id");

                    b.ToTable("spare_part_types", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.Attachment", b =>
                {
                    b.Property<long>("AttachmentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("attachment_id");

                    b.Property<long?>("CommentId")
                        .HasColumnType("bigint")
                        .HasColumnName("comment_id");

                    b.Property<DateTime>("CreationTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("creation_timestamp");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("file_name");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("file_path");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint")
                        .HasColumnName("file_size");

                    b.Property<string>("FileType")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("file_type");

                    b.Property<bool>("IsPreviewable")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_previewable");

                    b.Property<string>("StorageType")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("storage_type");

                    b.Property<string>("StoredFileName")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("stored_file_name");

                    b.Property<long?>("TaskId")
                        .HasColumnType("bigint")
                        .HasColumnName("task_id");

                    b.Property<int>("UploaderUserId")
                        .HasColumnType("int")
                        .HasColumnName("uploader_user_id");

                    b.HasKey("AttachmentId")
                        .HasName("pk_attachments");

                    b.HasIndex("CommentId")
                        .HasDatabaseName("ix_attachments_comment_id");

                    b.HasIndex("TaskId")
                        .HasDatabaseName("ix_attachments_task_id");

                    b.HasIndex("UploaderUserId")
                        .HasDatabaseName("ix_attachments_uploader_user_id");

                    b.ToTable("attachments", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.Comment", b =>
                {
                    b.Property<long>("CommentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("comment_id");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("content");

                    b.Property<DateTime>("CreationTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("creation_timestamp");

                    b.Property<bool>("IsEdited")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_edited");

                    b.Property<bool>("IsPinned")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_pinned");

                    b.Property<DateTime>("LastUpdatedTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("last_updated_timestamp");

                    b.Property<string>("MentionedUserIds")
                        .HasColumnType("longtext")
                        .HasColumnName("mentioned_user_ids");

                    b.Property<long?>("ParentCommentId")
                        .HasColumnType("bigint")
                        .HasColumnName("parent_comment_id");

                    b.Property<long>("TaskId")
                        .HasColumnType("bigint")
                        .HasColumnName("task_id");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.HasKey("CommentId")
                        .HasName("pk_comments");

                    b.HasIndex("ParentCommentId")
                        .HasDatabaseName("ix_comments_parent_comment_id");

                    b.HasIndex("TaskId")
                        .HasDatabaseName("ix_comments_task_id");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_comments_user_id");

                    b.ToTable("comments", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.PdcaPlan", b =>
                {
                    b.Property<long>("PdcaPlanId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("pdca_plan_id");

                    b.Property<string>("ActAction")
                        .HasColumnType("longtext")
                        .HasColumnName("act_action");

                    b.Property<string>("CheckResult")
                        .HasColumnType("longtext")
                        .HasColumnName("check_result");

                    b.Property<decimal>("CompletionRate")
                        .HasColumnType("decimal(65,30)")
                        .HasColumnName("completion_rate");

                    b.Property<DateTime>("CreationTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("creation_timestamp");

                    b.Property<int>("CreatorUserId")
                        .HasColumnType("int")
                        .HasColumnName("creator_user_id");

                    b.Property<string>("DoRecord")
                        .HasColumnType("longtext")
                        .HasColumnName("do_record");

                    b.Property<string>("Goal")
                        .HasColumnType("longtext")
                        .HasColumnName("goal");

                    b.Property<DateTime>("LastUpdatedTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("last_updated_timestamp");

                    b.Property<string>("Notes")
                        .HasColumnType("longtext")
                        .HasColumnName("notes");

                    b.Property<string>("PlanContent")
                        .HasColumnType("longtext")
                        .HasColumnName("plan_content");

                    b.Property<int>("ResponsiblePersonId")
                        .HasColumnType("int")
                        .HasColumnName("responsible_person_id");

                    b.Property<string>("Stage")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("stage");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status");

                    b.Property<long>("TaskId")
                        .HasColumnType("bigint")
                        .HasColumnName("task_id");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("title");

                    b.HasKey("PdcaPlanId")
                        .HasName("pk_pdca_plans");

                    b.HasIndex("CreatorUserId")
                        .HasDatabaseName("ix_pdca_plans_creator_user_id");

                    b.HasIndex("ResponsiblePersonId")
                        .HasDatabaseName("ix_pdca_plans_responsible_person_id");

                    b.HasIndex("TaskId")
                        .HasDatabaseName("ix_pdca_plans_task_id");

                    b.ToTable("pdca_plans", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.PeriodicTaskSchedule", b =>
                {
                    b.Property<long>("PeriodicTaskScheduleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("periodic_task_schedule_id");

                    b.Property<DateTime>("CreationTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("creation_timestamp");

                    b.Property<int>("CreatorUserId")
                        .HasColumnType("int")
                        .HasColumnName("creator_user_id");

                    b.Property<string>("CronExpression")
                        .HasColumnType("longtext")
                        .HasColumnName("cron_expression");

                    b.Property<int?>("DayOfMonth")
                        .HasColumnType("int")
                        .HasColumnName("day_of_month");

                    b.Property<string>("DayOfWeekForMonth")
                        .HasColumnType("longtext")
                        .HasColumnName("day_of_week_for_month");

                    b.Property<string>("DaysOfWeek")
                        .HasColumnType("longtext")
                        .HasColumnName("days_of_week");

                    b.Property<int>("DefaultPoints")
                        .HasColumnType("int")
                        .HasColumnName("default_points");

                    b.Property<string>("Description")
                        .HasColumnType("longtext")
                        .HasColumnName("description");

                    b.Property<string>("EndConditionType")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("end_condition_type");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("end_date");

                    b.Property<string>("LastError")
                        .HasColumnType("longtext")
                        .HasColumnName("last_error");

                    b.Property<DateTime?>("LastGeneratedTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("last_generated_timestamp");

                    b.Property<DateTime>("LastUpdatedTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("last_updated_timestamp");

                    b.Property<int?>("MonthOfYear")
                        .HasColumnType("int")
                        .HasColumnName("month_of_year");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("name");

                    b.Property<DateTime>("NextGenerationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("next_generation_time");

                    b.Property<int>("OccurrencesGenerated")
                        .HasColumnType("int")
                        .HasColumnName("occurrences_generated");

                    b.Property<int>("RecurrenceInterval")
                        .HasColumnType("int")
                        .HasColumnName("recurrence_interval");

                    b.Property<string>("RecurrenceType")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("recurrence_type");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("start_date");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("status");

                    b.Property<long>("TemplateTaskId")
                        .HasColumnType("bigint")
                        .HasColumnName("template_task_id");

                    b.Property<long>("TemplateTaskTaskId")
                        .HasColumnType("bigint")
                        .HasColumnName("template_task_task_id");

                    b.Property<int?>("TotalOccurrences")
                        .HasColumnType("int")
                        .HasColumnName("total_occurrences");

                    b.Property<string>("WeekOfMonth")
                        .HasColumnType("longtext")
                        .HasColumnName("week_of_month");

                    b.HasKey("PeriodicTaskScheduleId")
                        .HasName("pk_periodic_task_schedules");

                    b.HasIndex("CreatorUserId")
                        .HasDatabaseName("ix_periodic_task_schedules_creator_user_id");

                    b.HasIndex("TemplateTaskTaskId")
                        .HasDatabaseName("ix_periodic_task_schedules_template_task_task_id");

                    b.ToTable("periodic_task_schedules", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.PeriodicTaskScheduleAssignee", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<long>("PeriodicTaskScheduleId")
                        .HasColumnType("bigint")
                        .HasColumnName("periodic_task_schedule_id");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_periodic_task_schedule_assignees");

                    b.HasIndex("PeriodicTaskScheduleId")
                        .HasDatabaseName("ix_periodic_task_schedule_assignees_periodic_task_schedule_id");

                    b.ToTable("periodic_task_schedule_assignees", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.Task", b =>
                {
                    b.Property<long>("TaskId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("task_id");

                    b.Property<DateTime?>("ActualEndDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("actual_end_date");

                    b.Property<DateTime?>("ActualStartDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("actual_start_date");

                    b.Property<int?>("AssetId")
                        .HasColumnType("int")
                        .HasColumnName("asset_id");

                    b.Property<int?>("AssigneeUserId")
                        .HasColumnType("int")
                        .HasColumnName("assignee_user_id");

                    b.Property<DateTime>("CreationTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("creation_timestamp");

                    b.Property<int>("CreatorUserId")
                        .HasColumnType("int")
                        .HasColumnName("creator_user_id");

                    b.Property<string>("Description")
                        .HasColumnType("longtext")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_deleted");

                    b.Property<bool>("IsOverdueAcknowledged")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_overdue_acknowledged");

                    b.Property<DateTime>("LastUpdatedTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("last_updated_timestamp");

                    b.Property<int?>("LocationId")
                        .HasColumnType("int")
                        .HasColumnName("location_id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("name");

                    b.Property<string>("PDCAStage")
                        .HasColumnType("longtext")
                        .HasColumnName("pdca_stage");

                    b.Property<long?>("ParentTaskId")
                        .HasColumnType("bigint")
                        .HasColumnName("parent_task_id");

                    b.Property<long?>("PeriodicTaskScheduleId")
                        .HasColumnType("bigint")
                        .HasColumnName("periodic_task_schedule_id");

                    b.Property<DateTime?>("PlanEndDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("plan_end_date");

                    b.Property<DateTime?>("PlanStartDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("plan_start_date");

                    b.Property<int>("Points")
                        .HasColumnType("int")
                        .HasColumnName("points");

                    b.Property<long?>("PreviousInstanceTaskId")
                        .HasColumnType("bigint")
                        .HasColumnName("previous_instance_task_id");

                    b.Property<string>("Priority")
                        .HasColumnType("longtext")
                        .HasColumnName("priority");

                    b.Property<int>("Progress")
                        .HasColumnType("int")
                        .HasColumnName("progress");

                    b.Property<long?>("ProjectId")
                        .HasColumnType("bigint")
                        .HasColumnName("project_id");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("status");

                    b.Property<string>("TaskType")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("task_type");

                    b.HasKey("TaskId")
                        .HasName("pk_tasks");

                    b.HasIndex("AssetId")
                        .HasDatabaseName("ix_tasks_asset_id");

                    b.HasIndex("AssigneeUserId")
                        .HasDatabaseName("ix_tasks_assignee_user_id");

                    b.HasIndex("CreatorUserId")
                        .HasDatabaseName("ix_tasks_creator_user_id");

                    b.HasIndex("LocationId")
                        .HasDatabaseName("ix_tasks_location_id");

                    b.HasIndex("ParentTaskId")
                        .HasDatabaseName("ix_tasks_parent_task_id");

                    b.ToTable("tasks", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.TaskAssignee", b =>
                {
                    b.Property<long>("TaskAssigneeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("task_assignee_id");

                    b.Property<int>("AssignedByUserId")
                        .HasColumnType("int")
                        .HasColumnName("assigned_by_user_id");

                    b.Property<DateTime>("AssignmentTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("assignment_timestamp");

                    b.Property<string>("AssignmentType")
                        .HasColumnType("longtext")
                        .HasColumnName("assignment_type");

                    b.Property<long>("TaskId")
                        .HasColumnType("bigint")
                        .HasColumnName("task_id");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.HasKey("TaskAssigneeId")
                        .HasName("pk_task_assignees");

                    b.HasIndex("AssignedByUserId")
                        .HasDatabaseName("ix_task_assignees_assigned_by_user_id");

                    b.HasIndex("TaskId")
                        .HasDatabaseName("ix_task_assignees_task_id");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_task_assignees_user_id");

                    b.ToTable("task_assignees", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.TaskHistory", b =>
                {
                    b.Property<long>("TaskHistoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("task_history_id");

                    b.Property<string>("ActionType")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("action_type");

                    b.Property<long?>("AttachmentId")
                        .HasColumnType("bigint")
                        .HasColumnName("attachment_id");

                    b.Property<long?>("CommentId")
                        .HasColumnType("bigint")
                        .HasColumnName("comment_id");

                    b.Property<string>("Description")
                        .HasColumnType("longtext")
                        .HasColumnName("description");

                    b.Property<string>("FieldName")
                        .HasColumnType("longtext")
                        .HasColumnName("field_name");

                    b.Property<string>("NewValue")
                        .HasColumnType("longtext")
                        .HasColumnName("new_value");

                    b.Property<string>("OldValue")
                        .HasColumnType("longtext")
                        .HasColumnName("old_value");

                    b.Property<long>("TaskId")
                        .HasColumnType("bigint")
                        .HasColumnName("task_id");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("timestamp");

                    b.Property<int?>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.HasKey("TaskHistoryId")
                        .HasName("pk_task_histories");

                    b.HasIndex("AttachmentId")
                        .HasDatabaseName("ix_task_histories_attachment_id");

                    b.HasIndex("CommentId")
                        .HasDatabaseName("ix_task_histories_comment_id");

                    b.HasIndex("TaskId")
                        .HasDatabaseName("ix_task_histories_task_id");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_task_histories_user_id");

                    b.ToTable("task_histories", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Asset", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<string>("AssetCode")
                        .HasColumnType("longtext")
                        .HasColumnName("asset_code");

                    b.Property<int>("AssetTypeId")
                        .HasColumnType("int")
                        .HasColumnName("asset_type_id");

                    b.Property<string>("Brand")
                        .HasColumnType("longtext")
                        .HasColumnName("brand");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<int?>("DepartmentId")
                        .HasColumnType("int")
                        .HasColumnName("department_id");

                    b.Property<string>("FinancialCode")
                        .HasColumnType("longtext")
                        .HasColumnName("financial_code");

                    b.Property<int?>("LocationId")
                        .HasColumnType("int")
                        .HasColumnName("location_id");

                    b.Property<string>("Model")
                        .HasColumnType("longtext")
                        .HasColumnName("model");

                    b.Property<string>("Name")
                        .HasColumnType("longtext")
                        .HasColumnName("name");

                    b.Property<string>("Notes")
                        .HasColumnType("longtext")
                        .HasColumnName("notes");

                    b.Property<decimal?>("Price")
                        .HasColumnType("decimal(65,30)")
                        .HasColumnName("price");

                    b.Property<DateTime?>("PurchaseDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("purchase_date");

                    b.Property<string>("SerialNumber")
                        .HasColumnType("longtext")
                        .HasColumnName("serial_number");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.Property<DateTime?>("WarrantyExpireDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("warranty_expire_date");

                    b.HasKey("Id")
                        .HasName("pk_assets");

                    b.HasIndex("AssetTypeId")
                        .HasDatabaseName("ix_assets_asset_type_id");

                    b.HasIndex("DepartmentId")
                        .HasDatabaseName("ix_assets_department_id");

                    b.HasIndex("LocationId")
                        .HasDatabaseName("ix_assets_location_id");

                    b.ToTable("assets", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.AssetHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<int>("AssetId")
                        .HasColumnType("int")
                        .HasColumnName("asset_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasColumnType("longtext")
                        .HasColumnName("description");

                    b.Property<DateTime>("OperationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("operation_time");

                    b.Property<int>("OperationType")
                        .HasColumnType("int")
                        .HasColumnName("operation_type");

                    b.Property<int>("OperatorId")
                        .HasColumnType("int")
                        .HasColumnName("operator_id");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_asset_histories");

                    b.HasIndex("AssetId")
                        .HasDatabaseName("ix_asset_histories_asset_id");

                    b.HasIndex("OperatorId")
                        .HasDatabaseName("ix_asset_histories_operator_id");

                    b.ToTable("asset_histories", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.AssetReceive", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<int>("AssetId")
                        .HasColumnType("int")
                        .HasColumnName("asset_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("int")
                        .HasColumnName("created_by");

                    b.Property<string>("InspectionResult")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasColumnName("inspection_result");

                    b.Property<int>("LocationId")
                        .HasColumnType("int")
                        .HasColumnName("location_id");

                    b.Property<int?>("PurchaseOrderId")
                        .HasColumnType("int")
                        .HasColumnName("purchase_order_id");

                    b.Property<DateTime>("ReceiveDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("receive_date");

                    b.Property<int>("ReceiveType")
                        .HasColumnType("int")
                        .HasColumnName("receive_type");

                    b.Property<int>("ReceiverId")
                        .HasColumnType("int")
                        .HasColumnName("receiver_id");

                    b.Property<string>("Remarks")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasColumnName("remarks");

                    b.Property<int?>("ReturnToFactoryId")
                        .HasColumnType("int")
                        .HasColumnName("return_to_factory_id");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("int")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_asset_receives");

                    b.HasIndex("AssetId")
                        .HasDatabaseName("ix_asset_receives_asset_id");

                    b.HasIndex("LocationId")
                        .HasDatabaseName("ix_asset_receives_location_id");

                    b.HasIndex("PurchaseOrderId")
                        .HasDatabaseName("ix_asset_receives_purchase_order_id");

                    b.HasIndex("ReceiverId")
                        .HasDatabaseName("ix_asset_receives_receiver_id");

                    b.HasIndex("ReturnToFactoryId")
                        .HasDatabaseName("ix_asset_receives_return_to_factory_id");

                    b.ToTable("asset_receives", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.AssetType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .HasColumnType("longtext")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasColumnType("longtext")
                        .HasColumnName("description");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_active");

                    b.Property<string>("Name")
                        .HasColumnType("longtext")
                        .HasColumnName("name");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int")
                        .HasColumnName("parent_id");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_assettypes");

                    b.HasIndex("ParentId")
                        .HasDatabaseName("ix_assettypes_parent_id");

                    b.ToTable("assettypes", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.AuditLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<int>("ActionType")
                        .HasColumnType("int")
                        .HasColumnName("action_type");

                    b.Property<string>("Content")
                        .HasColumnType("longtext")
                        .HasColumnName("content");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<string>("Function")
                        .HasColumnType("longtext")
                        .HasColumnName("function");

                    b.Property<string>("IPAddress")
                        .HasColumnType("longtext")
                        .HasColumnName("ip_address");

                    b.Property<string>("Module")
                        .HasColumnType("longtext")
                        .HasColumnName("module");

                    b.Property<int>("Result")
                        .HasColumnType("int")
                        .HasColumnName("result");

                    b.Property<string>("Target")
                        .HasColumnType("longtext")
                        .HasColumnName("target");

                    b.Property<string>("TargetId")
                        .HasColumnType("longtext")
                        .HasColumnName("target_id");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.Property<string>("Username")
                        .HasColumnType("longtext")
                        .HasColumnName("username");

                    b.HasKey("Id")
                        .HasName("pk_audit_logs");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_audit_logs_user_id");

                    b.ToTable("audit_logs", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Department", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .HasColumnType("longtext")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<int?>("DeputyManagerId")
                        .HasColumnType("int")
                        .HasColumnName("deputy_manager_id");

                    b.Property<string>("Description")
                        .HasColumnType("longtext")
                        .HasColumnName("description");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_active");

                    b.Property<int?>("ManagerId")
                        .HasColumnType("int")
                        .HasColumnName("manager_id");

                    b.Property<string>("Name")
                        .HasColumnType("longtext")
                        .HasColumnName("name");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int")
                        .HasColumnName("parent_id");

                    b.Property<string>("Path")
                        .HasColumnType("longtext")
                        .HasColumnName("path");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_departments");

                    b.HasIndex("ParentId")
                        .HasDatabaseName("ix_departments_parent_id");

                    b.ToTable("departments", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.FaultRecord", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("Id");

                    b.Property<int?>("AssetId")
                        .HasColumnType("int")
                        .HasColumnName("AssetId");

                    b.Property<DateTime?>("AssignTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("AssignTime");

                    b.Property<int?>("AssigneeId")
                        .HasColumnType("int")
                        .HasColumnName("AssigneeId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreatedAt");

                    b.Property<string>("Description")
                        .HasColumnType("longtext")
                        .HasColumnName("Description");

                    b.Property<int>("FaultTypeId")
                        .HasColumnType("int")
                        .HasColumnName("FaultTypeId");

                    b.Property<bool>("IsReturned")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("IsReturned");

                    b.Property<int?>("LocationId")
                        .HasColumnType("int")
                        .HasColumnName("LocationId");

                    b.Property<string>("Notes")
                        .HasColumnType("longtext")
                        .HasColumnName("Notes");

                    b.Property<DateTime>("ReportTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("ReportTime");

                    b.Property<int>("ReporterId")
                        .HasColumnType("int")
                        .HasColumnName("ReporterId");

                    b.Property<string>("Resolution")
                        .HasColumnType("longtext")
                        .HasColumnName("Resolution");

                    b.Property<DateTime?>("ResolutionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("ResolutionTime");

                    b.Property<DateTime?>("ResponseTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("ResponseTime");

                    b.Property<string>("RootCause")
                        .HasColumnType("longtext")
                        .HasColumnName("RootCause");

                    b.Property<int>("Severity")
                        .HasColumnType("int")
                        .HasColumnName("Severity");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("Status");

                    b.Property<string>("Title")
                        .HasColumnType("longtext")
                        .HasColumnName("Title");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("Id")
                        .HasName("pk_faultrecords");

                    b.HasIndex("AssetId")
                        .HasDatabaseName("ix_faultrecords_asset_id");

                    b.HasIndex("AssigneeId")
                        .HasDatabaseName("ix_faultrecords_assignee_id");

                    b.HasIndex("FaultTypeId")
                        .HasDatabaseName("ix_faultrecords_fault_type_id");

                    b.HasIndex("LocationId")
                        .HasDatabaseName("ix_faultrecords_location_id");

                    b.HasIndex("ReporterId")
                        .HasDatabaseName("ix_faultrecords_reporter_id");

                    b.ToTable("faultrecords", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.FaultType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .HasColumnType("longtext")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasColumnType("longtext")
                        .HasColumnName("description");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_active");

                    b.Property<string>("Name")
                        .HasColumnType("longtext")
                        .HasColumnName("name");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int")
                        .HasColumnName("parent_id");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("sort_order");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_fault_types");

                    b.HasIndex("ParentId")
                        .HasDatabaseName("ix_fault_types_parent_id");

                    b.ToTable("fault_types", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Location", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<int?>("DefaultDepartmentId")
                        .HasColumnType("int")
                        .HasColumnName("DefaultDepartmentId");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)")
                        .HasColumnName("description");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_active");

                    b.Property<int?>("ManagerId")
                        .HasColumnType("int")
                        .HasColumnName("DefaultResponsiblePersonId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("name");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int")
                        .HasColumnName("parent_id");

                    b.Property<string>("Path")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)")
                        .HasColumnName("path");

                    b.Property<int>("Type")
                        .HasColumnType("int")
                        .HasColumnName("type");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_locations");

                    b.HasIndex("DefaultDepartmentId")
                        .HasDatabaseName("ix_locations_default_department_id");

                    b.HasIndex("ManagerId")
                        .HasDatabaseName("ix_locations_default_responsible_person_id");

                    b.HasIndex("ParentId")
                        .HasDatabaseName("ix_locations_parent_id");

                    b.ToTable("locations", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.LocationHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("Id");

                    b.Property<int>("AssetId")
                        .HasColumnType("int")
                        .HasColumnName("AssetId");

                    b.Property<DateTime>("ChangeTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("ChangeTime");

                    b.Property<int>("ChangeType")
                        .HasColumnType("int")
                        .HasColumnName("ChangeType");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreatedAt");

                    b.Property<int>("NewLocationId")
                        .HasColumnType("int")
                        .HasColumnName("NewLocationId");

                    b.Property<string>("Notes")
                        .HasColumnType("longtext")
                        .HasColumnName("Notes");

                    b.Property<int?>("OldLocationId")
                        .HasColumnType("int")
                        .HasColumnName("OldLocationId");

                    b.Property<int>("OperatorId")
                        .HasColumnType("int")
                        .HasColumnName("OperatorId");

                    b.HasKey("Id")
                        .HasName("pk_locationhistories");

                    b.HasIndex("AssetId")
                        .HasDatabaseName("ix_locationhistories_asset_id");

                    b.HasIndex("NewLocationId")
                        .HasDatabaseName("ix_locationhistories_new_location_id");

                    b.HasIndex("OldLocationId")
                        .HasDatabaseName("ix_locationhistories_old_location_id");

                    b.HasIndex("OperatorId")
                        .HasDatabaseName("ix_locationhistories_operator_id");

                    b.ToTable("locationhistories", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.LocationUser", b =>
                {
                    b.Property<int>("LocationId")
                        .HasColumnType("int")
                        .HasColumnName("location_id");

                    b.Property<int>("PersonnelId")
                        .HasColumnType("int")
                        .HasColumnName("personnel_id");

                    b.Property<int>("UserType")
                        .HasColumnType("int")
                        .HasColumnName("user_type");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<int?>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.HasKey("LocationId", "PersonnelId", "UserType")
                        .HasName("pk_locationusers");

                    b.HasIndex("PersonnelId")
                        .HasDatabaseName("ix_locationusers_personnel_id");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_locationusers_user_id");

                    b.ToTable("locationusers", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.MaintenanceOrder", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<DateTime?>("ActualCompletionDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("actual_completion_date");

                    b.Property<int>("AssetId")
                        .HasColumnType("int")
                        .HasColumnName("asset_id");

                    b.Property<int>("AssigneeId")
                        .HasColumnType("int")
                        .HasColumnName("assignee_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("int")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasColumnName("description");

                    b.Property<int?>("FaultRecordId")
                        .HasColumnType("int")
                        .HasColumnName("fault_record_id");

                    b.Property<int>("LocationId")
                        .HasColumnType("int")
                        .HasColumnName("location_id");

                    b.Property<decimal?>("MaintenanceCost")
                        .HasColumnType("decimal(65,30)")
                        .HasColumnName("maintenance_cost");

                    b.Property<DateTime>("MaintenanceDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("maintenance_date");

                    b.Property<int>("MaintenanceType")
                        .HasColumnType("int")
                        .HasColumnName("maintenance_type");

                    b.Property<string>("OrderNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("order_number");

                    b.Property<DateTime?>("PlannedCompletionDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("planned_completion_date");

                    b.Property<string>("Remarks")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasColumnName("remarks");

                    b.Property<string>("Result")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasColumnName("result");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("int")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_maintenance_orders");

                    b.HasIndex("AssetId")
                        .HasDatabaseName("ix_maintenance_orders_asset_id");

                    b.HasIndex("AssigneeId")
                        .HasDatabaseName("ix_maintenance_orders_assignee_id");

                    b.HasIndex("FaultRecordId")
                        .HasDatabaseName("ix_maintenance_orders_fault_record_id");

                    b.HasIndex("LocationId")
                        .HasDatabaseName("ix_maintenance_orders_location_id");

                    b.ToTable("maintenance_orders", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Menu", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .HasColumnType("longtext")
                        .HasColumnName("code");

                    b.Property<string>("Component")
                        .HasColumnType("longtext")
                        .HasColumnName("component");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<string>("Icon")
                        .HasColumnType("longtext")
                        .HasColumnName("icon");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_active");

                    b.Property<bool>("IsVisible")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_visible");

                    b.Property<string>("Name")
                        .HasColumnType("longtext")
                        .HasColumnName("name");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int")
                        .HasColumnName("parent_id");

                    b.Property<string>("Path")
                        .HasColumnType("longtext")
                        .HasColumnName("path");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("sort_order");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_menus");

                    b.HasIndex("ParentId")
                        .HasDatabaseName("ix_menus_parent_id");

                    b.ToTable("menus", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Permission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("code");

                    b.Property<string>("Description")
                        .HasColumnType("longtext")
                        .HasColumnName("description");

                    b.Property<bool>("IsSystem")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_system");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("name");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("type");

                    b.HasKey("Id")
                        .HasName("pk_permissions");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("ix_permissions_code");

                    b.ToTable("permissions", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Personnel", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<string>("Contact")
                        .HasColumnType("longtext")
                        .HasColumnName("contact");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<int?>("DepartmentId")
                        .HasColumnType("int")
                        .HasColumnName("department_id");

                    b.Property<string>("EmployeeCode")
                        .HasColumnType("longtext")
                        .HasColumnName("employee_code");

                    b.Property<string>("Name")
                        .HasColumnType("longtext")
                        .HasColumnName("name");

                    b.Property<string>("Position")
                        .HasColumnType("longtext")
                        .HasColumnName("position");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_personnel");

                    b.HasIndex("DepartmentId")
                        .HasDatabaseName("ix_personnel_department_id");

                    b.ToTable("personnel", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.PurchaseItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("Id");

                    b.Property<int?>("AssetTypeId")
                        .HasColumnType("int")
                        .HasColumnName("AssetTypeId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreatedAt");

                    b.Property<string>("ItemCode")
                        .HasColumnType("longtext")
                        .HasColumnName("ItemCode");

                    b.Property<string>("ItemName")
                        .HasColumnType("longtext")
                        .HasColumnName("ItemName");

                    b.Property<string>("Notes")
                        .HasColumnType("longtext")
                        .HasColumnName("Notes");

                    b.Property<int>("PurchaseOrderId")
                        .HasColumnType("int")
                        .HasColumnName("PurchaseOrderId");

                    b.Property<int>("Quantity")
                        .HasColumnType("int")
                        .HasColumnName("Quantity");

                    b.Property<string>("Specification")
                        .HasColumnType("longtext")
                        .HasColumnName("Specification");

                    b.Property<decimal>("TotalPrice")
                        .HasColumnType("decimal(65,30)")
                        .HasColumnName("TotalPrice");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("decimal(65,30)")
                        .HasColumnName("UnitPrice");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("Id")
                        .HasName("pk_purchaseitems");

                    b.HasIndex("AssetTypeId")
                        .HasDatabaseName("ix_purchaseitems_asset_type_id");

                    b.HasIndex("PurchaseOrderId")
                        .HasDatabaseName("ix_purchaseitems_purchase_order_id");

                    b.ToTable("purchaseitems", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.PurchaseOrder", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("Id");

                    b.Property<DateTime?>("ActualDeliveryDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("ActualDeliveryDate");

                    b.Property<DateTime>("ApplicationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("ApplicationTime");

                    b.Property<DateTime?>("ApprovalTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("ApprovalTime");

                    b.Property<int?>("ApproverId")
                        .HasColumnType("int")
                        .HasColumnName("ApproverId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreatedAt");

                    b.Property<string>("Description")
                        .HasColumnType("longtext")
                        .HasColumnName("Description");

                    b.Property<DateTime?>("ExpectedDeliveryDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("EstimatedDeliveryDate");

                    b.Property<string>("Notes")
                        .HasColumnType("longtext")
                        .HasColumnName("Notes");

                    b.Property<string>("OrderNumber")
                        .HasColumnType("longtext")
                        .HasColumnName("OrderCode");

                    b.Property<int>("RequesterId")
                        .HasColumnType("int")
                        .HasColumnName("ApplicantId");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("Status");

                    b.Property<int>("SupplierId")
                        .HasColumnType("int")
                        .HasColumnName("SupplierId");

                    b.Property<string>("Title")
                        .HasColumnType("longtext")
                        .HasColumnName("Title");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(65,30)")
                        .HasColumnName("TotalAmount");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("Id")
                        .HasName("pk_purchaseorders");

                    b.HasIndex("ApproverId")
                        .HasDatabaseName("ix_purchaseorders_approver_id");

                    b.HasIndex("RequesterId")
                        .HasDatabaseName("ix_purchaseorders_requester_id");

                    b.HasIndex("SupplierId")
                        .HasDatabaseName("ix_purchaseorders_supplier_id");

                    b.ToTable("purchaseorders", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.RefreshToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<DateTime>("AddedDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("added_date");

                    b.Property<DateTime>("ExpiryDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("expiry_date");

                    b.Property<bool>("IsRevoked")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_revoked");

                    b.Property<bool>("IsUsed")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_used");

                    b.Property<string>("JwtId")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasColumnName("jwt_id");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasColumnName("token");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_refresh_tokens");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_refresh_tokens_user_id");

                    b.ToTable("refresh_tokens", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.ReturnToFactory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("Id");

                    b.Property<DateTime?>("ActualReturnTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("actual_return_time");

                    b.Property<int>("AssetId")
                        .HasColumnType("int")
                        .HasColumnName("asset_id");

                    b.Property<string>("Code")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("EstimatedReturnTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("estimated_return_time");

                    b.Property<int>("FaultRecordId")
                        .HasColumnType("int")
                        .HasColumnName("fault_record_id");

                    b.Property<bool>("InWarranty")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("in_warranty");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasColumnName("notes");

                    b.Property<decimal?>("RepairCost")
                        .HasColumnType("decimal(65,30)")
                        .HasColumnName("repair_cost");

                    b.Property<string>("RepairResult")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasColumnName("repair_result");

                    b.Property<DateTime?>("SendTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("send_time");

                    b.Property<int>("SenderId")
                        .HasColumnType("int")
                        .HasColumnName("sender_id");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status");

                    b.Property<int>("SupplierId")
                        .HasColumnType("int")
                        .HasColumnName("supplier_id");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_returntofactories");

                    b.HasIndex("AssetId")
                        .HasDatabaseName("ix_returntofactories_asset_id");

                    b.HasIndex("SupplierId")
                        .HasDatabaseName("ix_returntofactories_supplier_id");

                    b.ToTable("returntofactories", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Role", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasColumnType("longtext")
                        .HasColumnName("description");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_active");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("name");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_roles");

                    b.ToTable("roles", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.RoleMenu", b =>
                {
                    b.Property<int>("RoleId")
                        .HasColumnType("int")
                        .HasColumnName("role_id");

                    b.Property<int>("MenuId")
                        .HasColumnType("int")
                        .HasColumnName("menu_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.HasKey("RoleId", "MenuId")
                        .HasName("pk_rolemenus");

                    b.HasIndex("MenuId")
                        .HasDatabaseName("ix_rolemenus_menu_id");

                    b.ToTable("rolemenus", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.RolePermission", b =>
                {
                    b.Property<int>("RoleId")
                        .HasColumnType("int")
                        .HasColumnName("role_id");

                    b.Property<int>("PermissionId")
                        .HasColumnType("int")
                        .HasColumnName("permission_id");

                    b.HasKey("RoleId", "PermissionId")
                        .HasName("pk_rolepermissions");

                    b.HasIndex("PermissionId")
                        .HasDatabaseName("ix_rolepermissions_permission_id");

                    b.ToTable("rolepermissions", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Supplier", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("Id");

                    b.Property<string>("Address")
                        .HasColumnType("longtext")
                        .HasColumnName("address");

                    b.Property<string>("Code")
                        .HasColumnType("longtext")
                        .HasColumnName("code");

                    b.Property<string>("ContactEmail")
                        .HasColumnType("longtext")
                        .HasColumnName("contact_email");

                    b.Property<string>("ContactPerson")
                        .HasColumnType("longtext")
                        .HasColumnName("contact_person");

                    b.Property<string>("ContactPhone")
                        .HasColumnType("longtext")
                        .HasColumnName("contact_phone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_active");

                    b.Property<string>("Name")
                        .HasColumnType("longtext")
                        .HasColumnName("name");

                    b.Property<string>("Notes")
                        .HasColumnType("longtext")
                        .HasColumnName("notes");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_suppliers");

                    b.ToTable("suppliers", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<string>("Avatar")
                        .HasColumnType("longtext")
                        .HasColumnName("Avatar");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreatedAt");

                    b.Property<int?>("DefaultRoleId")
                        .HasColumnType("int")
                        .HasColumnName("DefaultRoleId");

                    b.Property<int?>("DepartmentId")
                        .HasColumnType("int")
                        .HasColumnName("DepartmentId");

                    b.Property<string>("Email")
                        .HasColumnType("varchar(255)")
                        .HasColumnName("Email");

                    b.Property<int>("Gender")
                        .HasColumnType("int")
                        .HasColumnName("Gender");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("IsActive");

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastLoginAt");

                    b.Property<string>("Mobile")
                        .HasColumnType("longtext")
                        .HasColumnName("Mobile");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("Name");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("PasswordHash");

                    b.Property<string>("Position")
                        .HasColumnType("longtext")
                        .HasColumnName("Position");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("longtext")
                        .HasColumnName("SecurityStamp");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("UpdatedAt");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("Username");

                    b.HasKey("Id")
                        .HasName("pk_users");

                    b.HasIndex("DefaultRoleId")
                        .HasDatabaseName("ix_users_default_role_id");

                    b.HasIndex("DepartmentId")
                        .HasDatabaseName("ix_users_department_id");

                    b.HasIndex("Email")
                        .IsUnique()
                        .HasDatabaseName("ix_users_email");

                    b.HasIndex("Username")
                        .IsUnique()
                        .HasDatabaseName("ix_users_username");

                    b.ToTable("users", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.UserRole", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.Property<int>("RoleId")
                        .HasColumnType("int")
                        .HasColumnName("role_id");

                    b.HasKey("UserId", "RoleId")
                        .HasName("pk_userroles");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("ix_userroles_role_id");

                    b.ToTable("userroles", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Notes.QuickMemo", b =>
                {
                    b.HasOne("ItAssetsSystem.Domain.Entities.Notes.QuickMemoCategory", "Category")
                        .WithMany("QuickMemos")
                        .HasForeignKey("CategoryId")
                        .HasConstraintName("fk_quick_memos_quick_memo_categories_category_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_quick_memos_users_user_id");

                    b.Navigation("Category");

                    b.Navigation("User");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Notes.QuickMemoCategory", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_quick_memo_categories_users_user_id");

                    b.Navigation("User");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.RepairItem", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Asset", "Asset")
                        .WithMany()
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_RepairItems_Assets_AssetId");

                    b.HasOne("ItAssetsSystem.Models.Entities.FaultRecord", "FaultRecord")
                        .WithMany()
                        .HasForeignKey("FaultRecordId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_RepairItems_FaultRecords_FaultRecordId");

                    b.HasOne("ItAssetsSystem.Domain.Entities.RepairOrder", "RepairOrder")
                        .WithMany("RepairItems")
                        .HasForeignKey("RepairOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_RepairItems_RepairOrders_RepairOrderId");

                    b.Navigation("Asset");

                    b.Navigation("FaultRecord");

                    b.Navigation("RepairOrder");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.RepairOrder", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.User", "Creator")
                        .WithMany()
                        .HasForeignKey("CreatorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_RepairOrders_Users_CreatorId");

                    b.HasOne("ItAssetsSystem.Models.Entities.Supplier", "Supplier")
                        .WithMany()
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_RepairOrders_Suppliers_SupplierId");

                    b.Navigation("Creator");

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.SparePart", b =>
                {
                    b.HasOne("ItAssetsSystem.Domain.Entities.SparePartLocation", "Location")
                        .WithMany("SpareParts")
                        .HasForeignKey("LocationId")
                        .HasConstraintName("fk_spare_parts_spare_part_locations_location_id");

                    b.HasOne("ItAssetsSystem.Domain.Entities.SparePartType", "Type")
                        .WithMany("SpareParts")
                        .HasForeignKey("TypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_spare_parts_spare_part_types_type_id");

                    b.Navigation("Location");

                    b.Navigation("Type");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.SparePartTransaction", b =>
                {
                    b.HasOne("ItAssetsSystem.Domain.Entities.SparePartLocation", "Location")
                        .WithMany("Transactions")
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_spare_part_transactions_spare_part_locations_location_id");

                    b.HasOne("ItAssetsSystem.Domain.Entities.SparePart", "Part")
                        .WithMany("Transactions")
                        .HasForeignKey("PartId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_spare_part_transactions_spare_parts_part_id");

                    b.Navigation("Location");

                    b.Navigation("Part");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.SparePartType", b =>
                {
                    b.HasOne("ItAssetsSystem.Domain.Entities.SparePartType", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .HasConstraintName("fk_spare_part_types_spare_part_types_parent_id");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.Attachment", b =>
                {
                    b.HasOne("ItAssetsSystem.Domain.Entities.Tasks.Comment", "Comment")
                        .WithMany("Attachments")
                        .HasForeignKey("CommentId")
                        .HasConstraintName("fk_attachments_comments_comment_id");

                    b.HasOne("ItAssetsSystem.Domain.Entities.Tasks.Task", "Task")
                        .WithMany("Attachments")
                        .HasForeignKey("TaskId")
                        .HasConstraintName("fk_attachments_tasks_task_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "UploaderUser")
                        .WithMany()
                        .HasForeignKey("UploaderUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_attachments_users_uploader_user_id");

                    b.Navigation("Comment");

                    b.Navigation("Task");

                    b.Navigation("UploaderUser");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.Comment", b =>
                {
                    b.HasOne("ItAssetsSystem.Domain.Entities.Tasks.Comment", "ParentComment")
                        .WithMany("Replies")
                        .HasForeignKey("ParentCommentId")
                        .HasConstraintName("fk_comments_comments_parent_comment_id");

                    b.HasOne("ItAssetsSystem.Domain.Entities.Tasks.Task", "Task")
                        .WithMany("Comments")
                        .HasForeignKey("TaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_comments_tasks_task_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_comments_users_user_id");

                    b.Navigation("ParentComment");

                    b.Navigation("Task");

                    b.Navigation("User");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.PdcaPlan", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.User", "CreatorUser")
                        .WithMany()
                        .HasForeignKey("CreatorUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_pdca_plans_users_creator_user_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "ResponsiblePerson")
                        .WithMany()
                        .HasForeignKey("ResponsiblePersonId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_pdca_plans_users_responsible_person_id");

                    b.HasOne("ItAssetsSystem.Domain.Entities.Tasks.Task", "Task")
                        .WithMany()
                        .HasForeignKey("TaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_pdca_plans_tasks_task_id");

                    b.Navigation("CreatorUser");

                    b.Navigation("ResponsiblePerson");

                    b.Navigation("Task");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.PeriodicTaskSchedule", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.User", "CreatorUser")
                        .WithMany()
                        .HasForeignKey("CreatorUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_periodic_task_schedules_users_creator_user_id");

                    b.HasOne("ItAssetsSystem.Domain.Entities.Tasks.Task", "TemplateTask")
                        .WithMany()
                        .HasForeignKey("TemplateTaskTaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_periodic_task_schedules_tasks_template_task_task_id");

                    b.Navigation("CreatorUser");

                    b.Navigation("TemplateTask");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.PeriodicTaskScheduleAssignee", b =>
                {
                    b.HasOne("ItAssetsSystem.Domain.Entities.Tasks.PeriodicTaskSchedule", "PeriodicTaskSchedule")
                        .WithMany("Assignees")
                        .HasForeignKey("PeriodicTaskScheduleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_periodic_task_schedule_assignees_periodic_task_schedules_per");

                    b.Navigation("PeriodicTaskSchedule");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.Task", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Asset", "Asset")
                        .WithMany()
                        .HasForeignKey("AssetId")
                        .HasConstraintName("fk_tasks_assets_asset_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "Assignee")
                        .WithMany()
                        .HasForeignKey("AssigneeUserId")
                        .HasConstraintName("fk_tasks_users_assignee_user_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "Creator")
                        .WithMany()
                        .HasForeignKey("CreatorUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_tasks_users_creator_user_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.Location", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .HasConstraintName("fk_tasks_locations_location_id");

                    b.HasOne("ItAssetsSystem.Domain.Entities.Tasks.Task", "ParentTask")
                        .WithMany("SubTasks")
                        .HasForeignKey("ParentTaskId")
                        .HasConstraintName("fk_tasks_tasks_parent_task_id");

                    b.Navigation("Asset");

                    b.Navigation("Assignee");

                    b.Navigation("Creator");

                    b.Navigation("Location");

                    b.Navigation("ParentTask");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.TaskAssignee", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.User", "AssignedByUser")
                        .WithMany()
                        .HasForeignKey("AssignedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_task_assignees_users_assigned_by_user_id");

                    b.HasOne("ItAssetsSystem.Domain.Entities.Tasks.Task", "Task")
                        .WithMany("Assignees")
                        .HasForeignKey("TaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_task_assignees_tasks_task_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_task_assignees_users_user_id");

                    b.Navigation("AssignedByUser");

                    b.Navigation("Task");

                    b.Navigation("User");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.TaskHistory", b =>
                {
                    b.HasOne("ItAssetsSystem.Domain.Entities.Tasks.Attachment", "Attachment")
                        .WithMany()
                        .HasForeignKey("AttachmentId")
                        .HasConstraintName("fk_task_histories_attachments_attachment_id");

                    b.HasOne("ItAssetsSystem.Domain.Entities.Tasks.Comment", "Comment")
                        .WithMany()
                        .HasForeignKey("CommentId")
                        .HasConstraintName("fk_task_histories_comments_comment_id");

                    b.HasOne("ItAssetsSystem.Domain.Entities.Tasks.Task", "Task")
                        .WithMany("History")
                        .HasForeignKey("TaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_task_histories_tasks_task_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .HasConstraintName("fk_task_histories_users_user_id");

                    b.Navigation("Attachment");

                    b.Navigation("Comment");

                    b.Navigation("Task");

                    b.Navigation("User");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Asset", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.AssetType", "AssetType")
                        .WithMany("Assets")
                        .HasForeignKey("AssetTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_assets_asset_types_asset_type_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.Department", "Department")
                        .WithMany("Assets")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_assets_departments_department_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.Location", "Location")
                        .WithMany("Assets")
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_assets_locations_location_id");

                    b.Navigation("AssetType");

                    b.Navigation("Department");

                    b.Navigation("Location");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.AssetHistory", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Asset", "Asset")
                        .WithMany("AssetHistories")
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_asset_histories_assets_asset_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "Operator")
                        .WithMany()
                        .HasForeignKey("OperatorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_asset_histories_users_operator_id");

                    b.Navigation("Asset");

                    b.Navigation("Operator");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.AssetReceive", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Asset", "Asset")
                        .WithMany()
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_asset_receives_assets_asset_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.Location", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_asset_receives_locations_location_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.PurchaseOrder", "PurchaseOrder")
                        .WithMany()
                        .HasForeignKey("PurchaseOrderId")
                        .HasConstraintName("fk_asset_receives_purchase_orders_purchase_order_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "Receiver")
                        .WithMany()
                        .HasForeignKey("ReceiverId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_asset_receives_users_receiver_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.ReturnToFactory", "ReturnToFactory")
                        .WithMany()
                        .HasForeignKey("ReturnToFactoryId")
                        .HasConstraintName("fk_asset_receives_return_to_factories_return_to_factory_id");

                    b.Navigation("Asset");

                    b.Navigation("Location");

                    b.Navigation("PurchaseOrder");

                    b.Navigation("Receiver");

                    b.Navigation("ReturnToFactory");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.AssetType", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.AssetType", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .HasConstraintName("fk_assettypes_assettypes_parent_id");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.AuditLog", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .HasConstraintName("fk_audit_logs_users_user_id");

                    b.Navigation("User");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Department", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Department", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .HasConstraintName("fk_departments_departments_parent_id");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.FaultRecord", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Asset", "Asset")
                        .WithMany("FaultRecords")
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_faultrecords_assets_asset_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "Assignee")
                        .WithMany()
                        .HasForeignKey("AssigneeId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_faultrecords_users_assignee_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.FaultType", "FaultType")
                        .WithMany("FaultRecords")
                        .HasForeignKey("FaultTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_faultrecords_fault_types_fault_type_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.Location", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_faultrecords_locations_location_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "Reporter")
                        .WithMany()
                        .HasForeignKey("ReporterId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_faultrecords_users_reporter_id");

                    b.Navigation("Asset");

                    b.Navigation("Assignee");

                    b.Navigation("FaultType");

                    b.Navigation("Location");

                    b.Navigation("Reporter");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.FaultType", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.FaultType", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .HasConstraintName("fk_fault_types_fault_types_parent_id");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Location", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DefaultDepartmentId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_locations_departments_default_department_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "Manager")
                        .WithMany()
                        .HasForeignKey("ManagerId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_locations_users_default_responsible_person_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.Location", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_locations_locations_parent_id");

                    b.Navigation("Department");

                    b.Navigation("Manager");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.LocationHistory", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Asset", "Asset")
                        .WithMany("LocationHistories")
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_locationhistories_assets_asset_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.Location", "NewLocation")
                        .WithMany()
                        .HasForeignKey("NewLocationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_locationhistories_locations_new_location_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.Location", "OldLocation")
                        .WithMany()
                        .HasForeignKey("OldLocationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_locationhistories_locations_old_location_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "Operator")
                        .WithMany()
                        .HasForeignKey("OperatorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_locationhistories_users_operator_id");

                    b.Navigation("Asset");

                    b.Navigation("NewLocation");

                    b.Navigation("OldLocation");

                    b.Navigation("Operator");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.LocationUser", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Location", "Location")
                        .WithMany("LocationUsers")
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_locationusers_locations_location_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.Personnel", "Personnel")
                        .WithMany("LocationUsers")
                        .HasForeignKey("PersonnelId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_locationusers_personnel_personnel_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.User", null)
                        .WithMany("LocationUsers")
                        .HasForeignKey("UserId")
                        .HasConstraintName("fk_locationusers_users_user_id");

                    b.Navigation("Location");

                    b.Navigation("Personnel");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.MaintenanceOrder", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Asset", "Asset")
                        .WithMany()
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_maintenance_orders_assets_asset_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "Assignee")
                        .WithMany()
                        .HasForeignKey("AssigneeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_maintenance_orders_users_assignee_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.FaultRecord", "FaultRecord")
                        .WithMany()
                        .HasForeignKey("FaultRecordId")
                        .HasConstraintName("fk_maintenance_orders_fault_records_fault_record_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.Location", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_maintenance_orders_locations_location_id");

                    b.Navigation("Asset");

                    b.Navigation("Assignee");

                    b.Navigation("FaultRecord");

                    b.Navigation("Location");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Menu", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Menu", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .HasConstraintName("fk_menus_menus_parent_id");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Personnel", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DepartmentId")
                        .HasConstraintName("fk_personnel_departments_department_id");

                    b.Navigation("Department");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.PurchaseItem", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.AssetType", "AssetType")
                        .WithMany()
                        .HasForeignKey("AssetTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_purchaseitems_assettypes_asset_type_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.PurchaseOrder", "PurchaseOrder")
                        .WithMany("PurchaseItems")
                        .HasForeignKey("PurchaseOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_purchaseitems_purchaseorders_purchase_order_id");

                    b.Navigation("AssetType");

                    b.Navigation("PurchaseOrder");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.PurchaseOrder", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.User", "Approver")
                        .WithMany()
                        .HasForeignKey("ApproverId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_purchaseorders_users_approver_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "Requester")
                        .WithMany()
                        .HasForeignKey("RequesterId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_purchaseorders_users_requester_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.Supplier", "Supplier")
                        .WithMany("PurchaseOrders")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_purchaseorders_suppliers_supplier_id");

                    b.Navigation("Approver");

                    b.Navigation("Requester");

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.RefreshToken", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_refresh_tokens_users_user_id");

                    b.Navigation("User");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.ReturnToFactory", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Asset", "Asset")
                        .WithMany()
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_returntofactories_assets_asset_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.Supplier", "Supplier")
                        .WithMany()
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_returntofactories_suppliers_supplier_id");

                    b.Navigation("Asset");

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.RoleMenu", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Menu", "Menu")
                        .WithMany("RoleMenus")
                        .HasForeignKey("MenuId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_rolemenus_menus_menu_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.Role", "Role")
                        .WithMany("RoleMenus")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_rolemenus_roles_role_id");

                    b.Navigation("Menu");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.RolePermission", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Permission", "Permission")
                        .WithMany("RolePermissions")
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_rolepermissions_permissions_permission_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.Role", "Role")
                        .WithMany("RolePermissions")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_rolepermissions_roles_role_id");

                    b.Navigation("Permission");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.User", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Role", "DefaultRole")
                        .WithMany()
                        .HasForeignKey("DefaultRoleId")
                        .HasConstraintName("fk_users_roles_default_role_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.Department", "Department")
                        .WithMany("Users")
                        .HasForeignKey("DepartmentId")
                        .HasConstraintName("fk_users_departments_department_id");

                    b.Navigation("DefaultRole");

                    b.Navigation("Department");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.UserRole", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Role", "Role")
                        .WithMany("UserRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_userroles_roles_role_id");

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "User")
                        .WithMany("UserRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_userroles_users_user_id");

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Notes.QuickMemoCategory", b =>
                {
                    b.Navigation("QuickMemos");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.RepairOrder", b =>
                {
                    b.Navigation("RepairItems");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.SparePart", b =>
                {
                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.SparePartLocation", b =>
                {
                    b.Navigation("SpareParts");

                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.SparePartType", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("SpareParts");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.Comment", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("Replies");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.PeriodicTaskSchedule", b =>
                {
                    b.Navigation("Assignees");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.Task", b =>
                {
                    b.Navigation("Assignees");

                    b.Navigation("Attachments");

                    b.Navigation("Comments");

                    b.Navigation("History");

                    b.Navigation("SubTasks");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Asset", b =>
                {
                    b.Navigation("AssetHistories");

                    b.Navigation("FaultRecords");

                    b.Navigation("LocationHistories");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.AssetType", b =>
                {
                    b.Navigation("Assets");

                    b.Navigation("Children");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Department", b =>
                {
                    b.Navigation("Assets");

                    b.Navigation("Children");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.FaultType", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("FaultRecords");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Location", b =>
                {
                    b.Navigation("Assets");

                    b.Navigation("Children");

                    b.Navigation("LocationUsers");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Menu", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("RoleMenus");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Permission", b =>
                {
                    b.Navigation("RolePermissions");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Personnel", b =>
                {
                    b.Navigation("LocationUsers");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.PurchaseOrder", b =>
                {
                    b.Navigation("PurchaseItems");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Role", b =>
                {
                    b.Navigation("RoleMenus");

                    b.Navigation("RolePermissions");

                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Supplier", b =>
                {
                    b.Navigation("PurchaseOrders");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.User", b =>
                {
                    b.Navigation("LocationUsers");

                    b.Navigation("UserRoles");
                });
#pragma warning restore 612, 618
        }
    }
}
