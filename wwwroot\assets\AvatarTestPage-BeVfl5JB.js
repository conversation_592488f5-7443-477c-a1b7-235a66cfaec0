import{_ as a,j as e,r as l,p as t,m as s,b as u,d as n,e as o,f as r,w as c,bF as v,A as d,t as i,aO as p,o as m,a9 as g}from"./index-CkwLz8y6.js";const f={class:"avatar-test-page"},_={key:0},y={key:1},A={key:2},I={key:0,src:"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"},S={class:"debug-info"},b=a({__name:"AvatarTestPage",setup(a){const b=e(),k=l(""),U=l(void 0),h=l(document.location.origin),T=()=>{const a=b.computedAvatarUrl;k.value=a};t((()=>b.computedAvatarUrl),(a=>{k.value=a}),{immediate:!0}),s((async()=>{if(!b.isLogin&&b.token)try{await b.getInfo()}catch(a){}T()}));const L=()=>{T()};return(a,e)=>{var l;return m(),u("div",f,[e[11]||(e[11]=n("h1",null,"用户头像测试页面",-1)),e[12]||(e[12]=n("p",null,"此页面专门用于测试用户头像的显示功能。",-1)),r(b).pending?(m(),u("div",_,e[0]||(e[0]=[n("p",null,"正在加载用户信息...",-1)]))):r(b).isLogin?(m(),u("div",A,[e[9]||(e[9]=n("h2",null,"头像预览:",-1)),o(r(v),{size:150,src:k.value,class:"test-avatar"},{default:c((()=>[k.value?g("",!0):(m(),u("img",I))])),_:1},8,["src"]),n("div",S,[e[8]||(e[8]=n("h3",null,"调试信息:",-1)),n("p",null,[e[2]||(e[2]=n("code",null,"userStore.userInfo.value?.avatar",-1)),d(": "+i(null==(l=r(b).userInfo.value)?void 0:l.avatar),1)]),n("p",null,[e[3]||(e[3]=n("code",null,"userStore.avatar.value",-1)),d(": "+i(r(b).avatar.value),1)]),n("p",null,[e[4]||(e[4]=n("code",null,"userStore.computedAvatarUrl",-1)),d(" (直接从 store 获取): "+i(r(b).computedAvatarUrl),1)]),n("p",null,[e[5]||(e[5]=n("code",null,"avatarUrlToDisplay",-1)),d(" (在组件中计算得到): "+i(k.value),1)]),n("p",null,[e[6]||(e[6]=n("code",null,"VITE_STATIC_FILES_BASE_URL",-1)),d(": "+i(U.value),1)]),n("p",null,[e[7]||(e[7]=n("code",null,"document.location.origin",-1)),d(": "+i(h.value),1)])])])):(m(),u("div",y,e[1]||(e[1]=[n("p",null,"用户未登录，无法显示头像。",-1)]))),o(r(p),{onClick:L,style:{"margin-top":"20px"}},{default:c((()=>e[10]||(e[10]=[d("强制刷新Store信息")]))),_:1})])}}},[["__scopeId","data-v-32ea68a0"]]);export{b as default};
